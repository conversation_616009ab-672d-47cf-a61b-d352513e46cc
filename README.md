# 🌈 墨忆 (Ink Memory)

<div align="center">

![墨忆Logo](https://img.shields.io/badge/墨忆-InkMemory-blue?style=for-the-badge&logo=android)
![Android](https://img.shields.io/badge/Android-3DDC84?style=for-the-badge&logo=android&logoColor=white)
![Kotlin](https://img.shields.io/badge/Kotlin-0095D5?style=for-the-badge&logo=kotlin&logoColor=white)
![Jetpack Compose](https://img.shields.io/badge/Jetpack%20Compose-4285F4?style=for-the-badge&logo=jetpackcompose&logoColor=white)

**一款现代化的Android备忘录应用，支持多主题、图片存储和社交化体验**

[📱 功能特性](#-功能特性) • [🎨 主题展示](#-主题展示) • [🚀 快速开始](#-快速开始) • [📖 使用指南](#-使用指南) • [🤝 参与贡献](#-参与贡献)

</div>

---

## 📖 项目介绍

墨忆是一款采用现代化设计理念的Android备忘录应用，基于Jetpack Compose构建，提供丰富的主题选择和优雅的用户体验。应用名称"墨忆"寓意着用文字记录美好回忆，如墨香般持久留存。

### ✨ 核心亮点

- 🌈 **彩虹主题朋友圈风格** - 模仿微信朋友圈的社交化卡片设计
- 🎨 **多样化主题系统** - 5种主题模式，3种卡片样式
- 🖼️ **图片持久化存储** - 深拷贝到应用私有目录，重启不丢失
- 📱 **现代化界面** - Material Design 3 + Jetpack Compose
- 🔄 **MVP架构** - 清晰的代码结构，易于维护和扩展

## 🌟 功能特性

### 📝 备忘录管理
- ✅ 创建、编辑、删除备忘录
- ✅ 富文本编辑器支持（列表、编号）
- ✅ 图片插入（最多9张，九宫格布局）
- ✅ 优先级设置（高、中、低）
- ✅ 分类标签管理
- ✅ 收藏和完成状态
- ✅ 提醒通知功能

### 🎨 主题系统
- 🌈 **彩虹主题** - 朋友圈风格，动态彩虹色彩
- ⚪ **白色简约** - 手账风格，纯净简洁
- 🌙 **深色主题** - 护眼模式，夜间友好
- ☀️ **浅色主题** - 经典设计，清新明亮
- 🔄 **跟随系统** - 自动适配系统主题

### 🎯 卡片样式
- 📋 **标准样式** - 简洁实用的传统卡片
- 💬 **社交样式** - 类似社交媒体的卡片设计
- ✨ **现代样式** - 采用现代设计语言的美观卡片

### 🗂️ 信息管理
- 👤 **身份证信息** - 专门的身份信息存储
- 💳 **银行卡信息** - 安全的银行卡信息管理
- 🔐 **账户信息** - 各类账户密码存储
- 🔒 **密码保护** - 敏感信息访问需要密码验证

### 🔍 搜索与筛选
- 🔎 **全文搜索** - 快速查找备忘录内容
- 🏷️ **标签筛选** - 按分类标签过滤
- 📊 **排序功能** - 多种排序方式
- 📱 **显示模式** - 备忘录模式/全信息模式切换

### 📤 数据管理
- 💾 **数据导出** - 支持JSON格式导出
- 📥 **数据导入** - 从文件恢复数据
- 🗑️ **批量操作** - 批量删除、标记完成
- 🔄 **数据同步** - 本地数据持久化存储

## 🎨 主题展示

### 🌈 彩虹主题 - 朋友圈风格
- **设计理念**: 模仿微信朋友圈发说说的样式
- **视觉特色**: 8秒HSV色相循环动画，扁平化无圆角设计
- **图片布局**: 单图大图显示，多图智能网格
- **用户体验**: 社交化头像、用户名、朋友圈时间格式

### ⚪ 白色简约 - 手账风格
- **设计理念**: 纯净简洁的手账本风格
- **视觉特色**: 手绘风格边框，黑白灰配色
- **布局特点**: 95%卡片宽度，一行一卡
- **适用场景**: 专注内容，减少视觉干扰

### ✨ 现代样式 - Material Design 3
- **设计理念**: 现代化设计语言
- **视觉特色**: 24dp大圆角，12dp深度阴影
- **交互体验**: 按压缩放动画，流畅的状态切换
- **功能完整**: 优先级指示器，智能图片网格

## 🏗️ 技术架构

### 📱 开发技术栈
- **开发语言**: Kotlin 100%
- **UI框架**: Jetpack Compose
- **架构模式**: MVP (Model-View-Presenter)
- **依赖注入**: Hilt
- **数据存储**: SharedPreferences + Room Database
- **图片加载**: Coil
- **动画系统**: Compose Animation

### 🗂️ 项目结构
```
app/src/main/java/com/vere/likes/
├── model/                  # 数据模型层
│   ├── data/              # 数据类定义
│   └── repository/        # 数据仓库
├── view/                  # 视图层
│   ├── compose/           # Compose UI组件
│   └── contract/          # MVP契约接口
├── presenter/             # 表示层
├── manager/               # 管理器类
├── notification/          # 通知系统
└── ui/theme/             # 主题系统
```

### 🔧 核心组件
- **PersistentImageManager** - 图片持久化管理
- **ThemeManager** - 主题状态管理
- **ReminderScheduler** - 提醒通知调度
- **ImportExportManager** - 数据导入导出

## 🚀 快速开始

### 📋 环境要求
- Android Studio Hedgehog | 2023.1.1 或更高版本
- Android SDK API 24 (Android 7.0) 或更高版本
- Kotlin 1.9.0 或更高版本
- Gradle 8.0 或更高版本

### 🛠️ 安装步骤

1. **克隆仓库**
   ```bash
   git clone https://gitee.com/beipiao_boy/ink-memory.git
   cd ink-memory
   ```

2. **打开项目**
   - 使用Android Studio打开项目
   - 等待Gradle同步完成

3. **运行应用**
   - 连接Android设备或启动模拟器
   - 点击运行按钮或使用快捷键 `Shift + F10`

### 📦 构建APK
```bash
# 构建Debug版本
./gradlew assembleDebug

# 构建Release版本
./gradlew assembleRelease
```

## 📖 使用指南

### 🎯 基础操作

1. **创建备忘录**
   - 点击右下角"+"按钮
   - 选择"备忘录"类型
   - 填写标题、内容，选择分类和优先级
   - 可添加图片和设置提醒时间

2. **主题切换**
   - 点击左上角应用标题打开侧边栏
   - 选择"设置" → "外观设置" → "主题设置"
   - 选择喜欢的主题模式

3. **信息管理**
   - 点击"+"按钮选择信息类型
   - 身份证信息、银行卡信息、账户信息
   - 首次进入需设置4位数字密码

### 🔍 高级功能

1. **搜索筛选**
   - 点击右上角搜索图标
   - 输入关键词进行全文搜索
   - 使用标签筛选特定分类

2. **数据管理**
   - 设置 → 数据管理 → 导出数据
   - 选择导出位置，生成JSON文件
   - 导入时选择对应的JSON文件

3. **批量操作**
   - 长按备忘录卡片进入选择模式
   - 选择多个项目进行批量删除或标记

## 🎨 自定义配置

### 🌈 主题配置
应用支持5种主题模式，每种主题都有独特的视觉风格：

- **彩虹主题**: 适合喜欢活泼色彩的用户
- **白色简约**: 适合专注内容的用户
- **深色主题**: 适合夜间使用
- **浅色主题**: 适合日间使用
- **跟随系统**: 自动适配系统设置

### 🎯 卡片样式
在非专用主题下，可以选择不同的卡片样式：

- **标准样式**: 传统的Material Design卡片
- **社交样式**: 类似社交媒体的卡片设计
- **现代样式**: 现代化设计语言的美观卡片

## 🤝 参与贡献

我们欢迎所有形式的贡献！无论是bug报告、功能建议还是代码贡献。

### 🔧 开发贡献

1. **Fork 本仓库**
   ```bash
   git clone https://gitee.com/beipiao_boy/ink-memory.git
   ```

2. **创建功能分支**
   ```bash
   git checkout -b feature/your-feature-name
   ```

3. **提交更改**
   ```bash
   git commit -m "feat: 添加新功能描述"
   ```

4. **推送分支**
   ```bash
   git push origin feature/your-feature-name
   ```

5. **创建 Pull Request**
   - 在Gitee上创建Pull Request
   - 详细描述你的更改
   - 等待代码审查

### 📝 提交规范
请遵循以下提交信息格式：
- `feat:` 新功能
- `fix:` 修复bug
- `docs:` 文档更新
- `style:` 代码格式调整
- `refactor:` 代码重构
- `test:` 测试相关
- `chore:` 构建过程或辅助工具的变动

### 🐛 问题反馈
如果您发现了bug或有功能建议，请：
1. 在Issues中搜索是否已有相关问题
2. 如果没有，请创建新的Issue
3. 详细描述问题或建议
4. 提供复现步骤（如果是bug）

## 📄 开源协议

本项目采用 [MIT License](LICENSE) 开源协议。

## 🙏 致谢

感谢以下开源项目和技术：
- [Jetpack Compose](https://developer.android.com/jetpack/compose) - 现代化UI工具包
- [Material Design 3](https://m3.material.io/) - 设计系统
- [Hilt](https://dagger.dev/hilt/) - 依赖注入框架
- [Coil](https://coil-kt.github.io/coil/) - 图片加载库

## 📞 联系方式

- **项目地址**: https://gitee.com/beipiao_boy/ink-memory
- **问题反馈**: [Issues](https://gitee.com/beipiao_boy/ink-memory/issues)
- **功能建议**: [Issues](https://gitee.com/beipiao_boy/ink-memory/issues)

---

<div align="center">

**如果这个项目对您有帮助，请给个 ⭐ Star 支持一下！**

Made with ❤️ by [beipiao_boy](https://gitee.com/beipiao_boy)

</div>
