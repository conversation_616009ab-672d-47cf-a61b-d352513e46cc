@echo off
echo 正在更新所有密度目录的图标文件...

set SOURCE_DIR=app\src\main\res\mipmap-mdpi
set DENSITIES=hdpi xhdpi xxhdpi xxxhdpi

echo 源目录: %SOURCE_DIR%

for %%d in (%DENSITIES%) do (
    echo 正在更新 mipmap-%%d...
    
    REM 删除旧的JPG文件
    if exist "app\src\main\res\mipmap-%%d\ic_launcher.jpg" del "app\src\main\res\mipmap-%%d\ic_launcher.jpg"
    if exist "app\src\main\res\mipmap-%%d\ic_launcher_round.jpg" del "app\src\main\res\mipmap-%%d\ic_launcher_round.jpg"
    if exist "app\src\main\res\mipmap-%%d\ic_launcher_custom.jpg" del "app\src\main\res\mipmap-%%d\ic_launcher_custom.jpg"
    if exist "app\src\main\res\mipmap-%%d\ic_launcher_foreground.jpg" del "app\src\main\res\mipmap-%%d\ic_launcher_foreground.jpg"
    if exist "app\src\main\res\mipmap-%%d\ic_launcher_background.jpg" del "app\src\main\res\mipmap-%%d\ic_launcher_background.jpg"
    
    REM 复制WebP文件
    copy "%SOURCE_DIR%\ic_launcher.webp" "app\src\main\res\mipmap-%%d\"
    copy "%SOURCE_DIR%\ic_launcher_round.webp" "app\src\main\res\mipmap-%%d\"
    copy "%SOURCE_DIR%\ic_launcher_custom.webp" "app\src\main\res\mipmap-%%d\"
    copy "%SOURCE_DIR%\ic_launcher_foreground.webp" "app\src\main\res\mipmap-%%d\"
    copy "%SOURCE_DIR%\ic_launcher_background.webp" "app\src\main\res\mipmap-%%d\"
    
    echo mipmap-%%d 更新完成
)

echo 清理mdpi目录中的JPG文件...
del "%SOURCE_DIR%\ic_launcher.jpg" 2>nul
del "%SOURCE_DIR%\ic_launcher_round.jpg" 2>nul
del "%SOURCE_DIR%\ic_launcher_foreground.jpg" 2>nul
del "%SOURCE_DIR%\ic_launcher_background.jpg" 2>nul

echo 所有图标文件更新完成！
echo.
echo 当前图标文件结构：
for %%d in (mdpi %DENSITIES%) do (
    echo === mipmap-%%d ===
    dir "app\src\main\res\mipmap-%%d\ic_launcher*.webp" /b 2>nul
)

pause
