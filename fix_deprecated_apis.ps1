# PowerShell脚本：修复过时的API使用
Write-Host "开始修复过时的API..." -ForegroundColor Green

# 定义需要替换的过时API映射
$apiReplacements = @{
    # 图标替换
    'Icons\.Default\.ArrowBack' = 'Icons.AutoMirrored.Filled.ArrowBack'
    'Icons\.Filled\.ArrowBack' = 'Icons.AutoMirrored.Filled.ArrowBack'
    'Icons\.Default\.ArrowForward' = 'Icons.AutoMirrored.Filled.ArrowForward'
    'Icons\.Filled\.ArrowForward' = 'Icons.AutoMirrored.Filled.ArrowForward'
    'Icons\.Default\.KeyboardArrowLeft' = 'Icons.AutoMirrored.Filled.KeyboardArrowLeft'
    'Icons\.Default\.KeyboardArrowRight' = 'Icons.AutoMirrored.Filled.KeyboardArrowRight'
    'Icons\.Default\.TrendingUp' = 'Icons.AutoMirrored.Filled.TrendingUp'
    'Icons\.Default\.Sort' = 'Icons.AutoMirrored.Filled.Sort'
    'Icons\.Default\.Note' = 'Icons.AutoMirrored.Filled.Note'
    'Icons\.Filled\.Note' = 'Icons.AutoMirrored.Filled.Note'
    'Icons\.Default\.Notes' = 'Icons.AutoMirrored.Filled.Notes'
    'Icons\.Default\.NoteAdd' = 'Icons.AutoMirrored.Filled.NoteAdd'
    'Icons\.Default\.Help' = 'Icons.AutoMirrored.Filled.Help'
    'Icons\.Default\.Label' = 'Icons.AutoMirrored.Filled.Label'
    'Icons\.Default\.ViewList' = 'Icons.AutoMirrored.Filled.ViewList'
    'Icons\.Default\.Send' = 'Icons.AutoMirrored.Filled.Send'
    'Icons\.Default\.OpenInNew' = 'Icons.AutoMirrored.Filled.OpenInNew'
    'Icons\.Default\.Assignment' = 'Icons.AutoMirrored.Filled.Assignment'
    'Icons\.Default\.EventNote' = 'Icons.AutoMirrored.Filled.EventNote'
    'Icons\.Default\.FormatListBulleted' = 'Icons.AutoMirrored.Filled.FormatListBulleted'
    'Icons\.Default\.FormatAlignLeft' = 'Icons.AutoMirrored.Filled.FormatAlignLeft'
    'Icons\.Default\.FormatAlignRight' = 'Icons.AutoMirrored.Filled.FormatAlignRight'
    'Icons\.Default\.Undo' = 'Icons.AutoMirrored.Filled.Undo'
    'Icons\.Default\.Redo' = 'Icons.AutoMirrored.Filled.Redo'
    
    # LinearProgressIndicator 替换 - 暂时跳过复杂的正则替换
    # 'LinearProgressIndicator\s*\(\s*progress\s*=\s*([^,\)]+)' = 'LinearProgressIndicator(progress = { $1 }'
    
    # Divider 替换
    'Divider\s*\(' = 'HorizontalDivider('
}

# 获取所有Kotlin文件
$kotlinFiles = Get-ChildItem -Path "app\src" -Filter "*.kt" -Recurse

Write-Host "找到 $($kotlinFiles.Count) 个Kotlin文件" -ForegroundColor Yellow

$totalReplacements = 0
$modifiedFiles = 0

foreach ($file in $kotlinFiles) {
    $content = Get-Content $file.FullName -Raw -Encoding UTF8
    $originalContent = $content
    $fileReplacements = 0
    
    # 应用所有替换
    foreach ($pattern in $apiReplacements.Keys) {
        $replacement = $apiReplacements[$pattern]
        $matches = [regex]::Matches($content, $pattern)
        if ($matches.Count -gt 0) {
            $content = [regex]::Replace($content, $pattern, $replacement)
            $fileReplacements += $matches.Count
            Write-Host "  在 $($file.Name) 中替换了 $($matches.Count) 个 '$pattern'" -ForegroundColor Cyan
        }
    }
    
    # 如果有修改，保存文件
    if ($content -ne $originalContent) {
        Set-Content $file.FullName $content -Encoding UTF8 -NoNewline
        $modifiedFiles++
        $totalReplacements += $fileReplacements
        Write-Host "✅ 修改了 $($file.Name) ($fileReplacements 处替换)" -ForegroundColor Green
    }
}

Write-Host "`n=== 修复完成 ===" -ForegroundColor Green
Write-Host "修改的文件数: $modifiedFiles" -ForegroundColor Yellow
Write-Host "总替换次数: $totalReplacements" -ForegroundColor Yellow

# 检查是否需要添加新的import
Write-Host "`n检查是否需要添加新的import..." -ForegroundColor Yellow

$needsAutoMirroredImport = @()
foreach ($file in $kotlinFiles) {
    $content = Get-Content $file.FullName -Raw -Encoding UTF8
    if ($content -match 'Icons\.AutoMirrored\.Filled\.' -and $content -notmatch 'import androidx\.compose\.material\.icons\.Icons\.AutoMirrored') {
        $needsAutoMirroredImport += $file
    }
}

if ($needsAutoMirroredImport.Count -gt 0) {
    Write-Host "需要添加AutoMirrored import的文件:" -ForegroundColor Red
    foreach ($file in $needsAutoMirroredImport) {
        Write-Host "  - $($file.Name)" -ForegroundColor Red
    }
    
    Write-Host "`n正在添加缺失的import..." -ForegroundColor Yellow
    foreach ($file in $needsAutoMirroredImport) {
        $content = Get-Content $file.FullName -Raw -Encoding UTF8
        
        # 查找现有的Icons import
        if ($content -match 'import androidx\.compose\.material\.icons\.Icons') {
            # 在现有Icons import后添加AutoMirrored import
            $content = $content -replace '(import androidx\.compose\.material\.icons\.Icons[^\r\n]*)', '$1`nimport androidx.compose.material.icons.Icons.AutoMirrored'
        } else {
            # 在其他import后添加
            $content = $content -replace '(import androidx\.compose\.material\.icons\.[^\r\n]*)', '$1`nimport androidx.compose.material.icons.Icons.AutoMirrored'
        }
        
        Set-Content $file.FullName $content -Encoding UTF8 -NoNewline
        Write-Host "✅ 为 $($file.Name) 添加了AutoMirrored import" -ForegroundColor Green
    }
}

Write-Host "`n🎉 所有过时API修复完成！" -ForegroundColor Green
Write-Host "建议运行编译测试以验证修复效果" -ForegroundColor Cyan
