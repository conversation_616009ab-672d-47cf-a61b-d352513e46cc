package com.vere.likes

import androidx.compose.ui.test.*
import androidx.compose.ui.test.junit4.createAndroidComposeRule
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.vere.likes.MainActivity
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith

/**
 * 时间规划模块按钮功能自动化测试
 * 验证修复后的按钮功能是否正常工作
 */
@RunWith(AndroidJUnit4::class)
class ButtonFunctionalityTest {

    @get:Rule
    val composeTestRule = createAndroidComposeRule<MainActivity>()

    @Test
    fun testTimePlanningNavigationAndButtons() {
        // 启动应用并导航到时间规划
        composeTestRule.apply {
            // 等待应用加载完成
            waitForIdle()
            
            // 查找并点击时间规划按钮
            onNodeWithText("时间规划")
                .assertExists("时间规划按钮应该存在")
                .performClick()
            
            // 等待页面加载
            waitForIdle()
            
            // 验证进入时间规划主界面
            onNodeWithText("日计划")
                .assertExists("应该显示日计划标签")
        }
    }

    @Test
    fun testTimeBlockButtons() {
        navigateToTimePlanning()
        
        composeTestRule.apply {
            // 查找时间块卡片
            onAllNodesWithTag("time_block_card")
                .onFirst()
                .assertExists("应该存在时间块卡片")
            
            // 查找开始按钮
            onNodeWithContentDescription("开始")
                .assertExists("应该存在开始按钮")
                .assertIsEnabled()
            
            // 点击开始按钮
            onNodeWithContentDescription("开始")
                .performClick()
            
            // 等待状态更新
            waitForIdle()
            
            // 验证按钮变为完成按钮
            onNodeWithContentDescription("完成")
                .assertExists("点击开始后应该显示完成按钮")
        }
    }

    @Test
    fun testTaskButtons() {
        navigateToTimePlanning()
        
        composeTestRule.apply {
            // 查找任务卡片
            onAllNodesWithTag("task_card")
                .onFirst()
                .assertExists("应该存在任务卡片")
            
            // 查找任务完成复选框
            onNodeWithTag("task_checkbox")
                .assertExists("应该存在任务复选框")
                .assertIsEnabled()
            
            // 点击复选框完成任务
            onNodeWithTag("task_checkbox")
                .performClick()
            
            // 等待状态更新
            waitForIdle()
            
            // 验证任务状态变化
            onNodeWithTag("task_checkbox")
                .assertExists("复选框应该仍然存在")
        }
    }

    @Test
    fun testStatusDisplay() {
        navigateToTimePlanning()
        
        composeTestRule.apply {
            // 验证状态标签存在
            onNodeWithTag("status_badge")
                .assertExists("应该显示状态标签")
            
            // 验证状态文本
            onNodeWithText("计划中")
                .assertExists("应该显示计划中状态")
        }
    }

    @Test
    fun testButtonResponsiveness() {
        navigateToTimePlanning()
        
        composeTestRule.apply {
            // 测试按钮响应速度
            val startTime = System.currentTimeMillis()
            
            onNodeWithContentDescription("开始")
                .performClick()
            
            // 验证在合理时间内有响应
            waitForIdle()
            val responseTime = System.currentTimeMillis() - startTime
            
            assert(responseTime < 1000) { "按钮响应时间应该小于1秒，实际：${responseTime}ms" }
        }
    }

    @Test
    fun testDataPersistence() {
        navigateToTimePlanning()
        
        composeTestRule.apply {
            // 执行状态变更
            onNodeWithContentDescription("开始")
                .performClick()
            
            waitForIdle()
            
            // 切换到其他页面
            onNodeWithText("周计划")
                .performClick()
            
            waitForIdle()
            
            // 切换回日计划
            onNodeWithText("日计划")
                .performClick()
            
            waitForIdle()
            
            // 验证状态保持
            onNodeWithContentDescription("完成")
                .assertExists("状态应该保持为进行中")
        }
    }

    @Test
    fun testErrorHandling() {
        navigateToTimePlanning()
        
        composeTestRule.apply {
            // 测试在异常情况下的错误处理
            // 这里可以模拟网络错误或数据库错误
            
            // 验证错误提示不会导致崩溃
            onNodeWithContentDescription("开始")
                .performClick()
            
            waitForIdle()
            
            // 应用应该仍然可用
            onNodeWithText("日计划")
                .assertExists("应用应该保持稳定")
        }
    }

    @Test
    fun testMultipleOperations() {
        navigateToTimePlanning()
        
        composeTestRule.apply {
            // 测试连续操作
            repeat(3) { index ->
                // 开始时间块
                onNodeWithContentDescription("开始")
                    .performClick()
                
                waitForIdle()
                
                // 完成时间块
                onNodeWithContentDescription("完成")
                    .performClick()
                
                waitForIdle()
                
                // 验证操作成功
                onNodeWithContentDescription("重新开始")
                    .assertExists("第${index + 1}次操作应该成功")
            }
        }
    }

    @Test
    fun testUIConsistency() {
        navigateToTimePlanning()
        
        composeTestRule.apply {
            // 验证UI一致性
            onAllNodesWithTag("time_block_card")
                .assertCountEquals(1)

            onAllNodesWithTag("task_card")
                .assertCountEquals(1)
            
            // 验证按钮样式一致
            onNodeWithContentDescription("开始")
                .assertExists("开始按钮应该存在")
                .assertHasClickAction()
        }
    }

    @Test
    fun testAccessibility() {
        navigateToTimePlanning()
        
        composeTestRule.apply {
            // 验证无障碍功能
            onNodeWithContentDescription("开始")
                .assertExists("按钮应该有内容描述")
            
            onNodeWithContentDescription("完成")
                .assertDoesNotExist()
            
            // 点击后验证内容描述变化
            onNodeWithContentDescription("开始")
                .performClick()
            
            waitForIdle()
            
            onNodeWithContentDescription("完成")
                .assertExists("点击后应该显示完成按钮")
        }
    }

    /**
     * 导航到时间规划页面的辅助方法
     */
    private fun navigateToTimePlanning() {
        composeTestRule.apply {
            waitForIdle()
            
            onNodeWithText("时间规划")
                .performClick()
            
            waitForIdle()
        }
    }
}

/**
 * 扩展的测试工具类
 */
class ButtonFunctionalityExtendedTest {

    @get:Rule
    val composeTestRule = createAndroidComposeRule<MainActivity>()

    @Test
    fun testPerformanceUnderLoad() {
        // 性能压力测试
        composeTestRule.apply {
            navigateToTimePlanning()
            
            val startTime = System.currentTimeMillis()
            
            // 执行大量操作
            repeat(50) {
                onNodeWithContentDescription("开始")
                    .performClick()
                
                onNodeWithContentDescription("完成")
                    .performClick()
            }
            
            val totalTime = System.currentTimeMillis() - startTime
            assert(totalTime < 10000) { "50次操作应该在10秒内完成，实际：${totalTime}ms" }
        }
    }

    @Test
    fun testMemoryLeaks() {
        // 内存泄漏测试
        composeTestRule.apply {
            repeat(10) {
                navigateToTimePlanning()
                
                // 执行一些操作
                onNodeWithContentDescription("开始")
                    .performClick()
                
                waitForIdle()
                
                // 返回主页
                onNodeWithContentDescription("返回")
                    .performClick()
                
                waitForIdle()
            }
            
            // 验证应用仍然响应
            onNodeWithText("时间规划")
                .assertExists("应用应该仍然响应")
        }
    }

    private fun navigateToTimePlanning() {
        composeTestRule.apply {
            waitForIdle()
            onNodeWithText("时间规划").performClick()
            waitForIdle()
        }
    }
}
