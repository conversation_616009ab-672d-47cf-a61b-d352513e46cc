package com.vere.likes.debug

import org.junit.Test
import java.io.File

/**
 * 测试调试按钮是否已被移除
 */
class DebugButtonRemovalTest {

    @Test
    fun `verify debug button is removed from HomeScreen`() {
        println("=== 验证调试按钮已从 HomeScreen 移除 ===")
        
        // 读取 HomeScreen.kt 文件内容
        val homeScreenFile = File("src/main/java/com/vere/likes/view/compose/screen/HomeScreen.kt")
        
        if (!homeScreenFile.exists()) {
            println("❌ HomeScreen.kt 文件不存在")
            return
        }
        
        val content = homeScreenFile.readText()
        
        // 检查是否包含调试按钮相关的文本
        val debugButtonPatterns = listOf(
            "调试：刷新数据",
            "调试按钮",
            "Debug button clicked",
            "debug.*button.*clicked",
            "Button.*调试",
            "Text.*调试.*刷新"
        )
        
        var foundDebugButton = false
        val foundPatterns = mutableListOf<String>()
        
        debugButtonPatterns.forEach { pattern ->
            if (content.contains(pattern, ignoreCase = true)) {
                foundDebugButton = true
                foundPatterns.add(pattern)
            }
        }
        
        if (foundDebugButton) {
            println("❌ 仍然发现调试按钮相关代码:")
            foundPatterns.forEach { pattern ->
                println("  - 发现模式: $pattern")
            }
            
            // 显示包含调试按钮的行
            content.lines().forEachIndexed { index, line ->
                foundPatterns.forEach { pattern ->
                    if (line.contains(pattern, ignoreCase = true)) {
                        println("  第 ${index + 1} 行: ${line.trim()}")
                    }
                }
            }
            
            assert(false) { "调试按钮尚未完全移除" }
        } else {
            println("✅ 调试按钮已成功移除")
            
            // 验证文件仍然包含正常的功能
            val normalFeatures = listOf(
                "SearchBar",
                "FilterSortBar", 
                "CategoryChip",
                "MemoCard",
                "FloatingActionButton"
            )
            
            val missingFeatures = mutableListOf<String>()
            normalFeatures.forEach { feature ->
                if (!content.contains(feature)) {
                    missingFeatures.add(feature)
                }
            }
            
            if (missingFeatures.isNotEmpty()) {
                println("⚠️ 警告：以下正常功能可能被意外移除:")
                missingFeatures.forEach { feature ->
                    println("  - $feature")
                }
            } else {
                println("✅ 所有正常功能都保持完整")
            }
        }
        
        // 统计文件信息
        val lines = content.lines()
        val totalLines = lines.size
        val nonEmptyLines = lines.count { it.trim().isNotEmpty() }
        val commentLines = lines.count { it.trim().startsWith("//") || it.trim().startsWith("*") }
        
        println("\n📊 文件统计信息:")
        println("  总行数: $totalLines")
        println("  非空行数: $nonEmptyLines")
        println("  注释行数: $commentLines")
        println("  代码行数: ${nonEmptyLines - commentLines}")
        
        println("\n✅ 调试按钮移除验证完成")
    }

    @Test
    fun `verify HomeScreen still compiles correctly`() {
        println("=== 验证 HomeScreen 编译正确性 ===")
        
        val homeScreenFile = File("src/main/java/com/vere/likes/view/compose/screen/HomeScreen.kt")
        
        if (!homeScreenFile.exists()) {
            println("❌ HomeScreen.kt 文件不存在")
            return
        }
        
        val content = homeScreenFile.readText()
        
        // 检查基本的 Kotlin 语法结构
        val syntaxChecks = mapOf(
            "包声明" to "package com.vere.likes",
            "Composable 函数" to "@Composable",
            "导入语句" to "import androidx.compose",
            "函数定义" to "fun HomeScreen",
            "大括号匹配" to "{"
        )
        
        syntaxChecks.forEach { (name, pattern) ->
            if (content.contains(pattern)) {
                println("✅ $name: 正常")
            } else {
                println("❌ $name: 缺失")
            }
        }
        
        // 检查是否有明显的语法错误
        val potentialErrors = listOf(
            "未闭合的大括号" to (content.count { it == '{' } != content.count { it == '}' }),
            "未闭合的小括号" to (content.count { it == '(' } != content.count { it == ')' }),
            "未闭合的方括号" to (content.count { it == '[' } != content.count { it == ']' })
        )
        
        var hasErrors = false
        potentialErrors.forEach { (errorType, hasError) ->
            if (hasError) {
                println("❌ 发现 $errorType")
                hasErrors = true
            } else {
                println("✅ $errorType: 正常")
            }
        }
        
        if (!hasErrors) {
            println("✅ 基本语法检查通过")
        } else {
            println("❌ 发现语法问题")
        }
        
        println("\n✅ HomeScreen 编译正确性验证完成")
    }
}
