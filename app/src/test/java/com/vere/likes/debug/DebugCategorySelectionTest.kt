package com.vere.likes.debug

import android.content.Context
import android.content.SharedPreferences
import com.vere.likes.model.data.Category
import com.vere.likes.model.data.Memo
import com.vere.likes.model.data.Priority
import com.vere.likes.model.repository.impl.SharedPrefCategoryRepositoryImpl
import com.vere.likes.model.repository.impl.SharedPrefMemoRepositoryImpl
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Test
import org.mockito.Mock
import org.mockito.Mockito.*
import org.mockito.MockitoAnnotations
import java.time.LocalDateTime

/**
 * 测试分类选择功能
 */
class DebugCategorySelectionTest {

    @Mock
    private lateinit var context: Context

    @Mock
    private lateinit var sharedPreferences: SharedPreferences

    @Mock
    private lateinit var editor: SharedPreferences.Editor

    private lateinit var categoryRepository: SharedPrefCategoryRepositoryImpl
    private lateinit var memoRepository: SharedPrefMemoRepositoryImpl

    @Before
    fun setup() {
        MockitoAnnotations.openMocks(this)
        
        // Mock SharedPreferences behavior
        `when`(context.getSharedPreferences(anyString(), anyInt())).thenReturn(sharedPreferences)
        `when`(sharedPreferences.edit()).thenReturn(editor)
        `when`(editor.putString(anyString(), anyString())).thenReturn(editor)
        `when`(editor.putBoolean(anyString(), anyBoolean())).thenReturn(editor)
        `when`(editor.remove(anyString())).thenReturn(editor)
        `when`(editor.apply()).then { }
        
        categoryRepository = SharedPrefCategoryRepositoryImpl(context)
        memoRepository = SharedPrefMemoRepositoryImpl(context)
    }

    @Test
    fun `debug - test category loading and selection`() = runTest {
        println("=== 测试分类加载和选择 ===")

        // 直接使用默认分类，避免 JSON 序列化问题
        val defaultCategories = Category.getDefaultCategories()
        println("加载的分类数量: ${defaultCategories.size}")

        defaultCategories.forEach { category ->
            println("分类: ${category.name} (ID: ${category.id}, 颜色: ${category.color})")
        }

        // Test creating memo with category
        val selectedCategory = defaultCategories.firstOrNull { it.name == "工作" }
        println("选择的分类: ${selectedCategory?.name ?: "无分类"}")

        val testMemo = Memo(
            title = "测试备忘录",
            content = "这是一个测试备忘录",
            categoryId = selectedCategory?.id,
            priority = Priority.HIGH
        )

        println("创建的备忘录:")
        println("  标题: ${testMemo.title}")
        println("  内容: ${testMemo.content}")
        println("  分类ID: ${testMemo.categoryId}")
        println("  优先级: ${testMemo.priority.displayName}")

        assert(testMemo.categoryId == selectedCategory?.id) { "分类ID应该匹配" }
        assert(testMemo.categoryId == "default_work") { "应该是工作分类" }
        println("✅ 分类选择测试通过")
    }

    @Test
    fun `debug - test memo creation with different categories`() = runTest {
        println("=== 测试不同分类的备忘录创建 ===")
        
        val defaultCategories = Category.getDefaultCategories()
        
        defaultCategories.forEach { category ->
            val memo = Memo(
                title = "${category.name}备忘录",
                content = "这是${category.name}分类的备忘录",
                categoryId = category.id,
                priority = Priority.MEDIUM
            )
            
            println("创建备忘录: ${memo.title}")
            println("  分类: ${category.name}")
            println("  分类ID: ${memo.categoryId}")
            println("  优先级: ${memo.priority.displayName}")
            println()
        }
        
        // Test memo without category
        val noCategory = Memo(
            title = "无分类备忘录",
            content = "这是没有分类的备忘录",
            categoryId = null,
            priority = Priority.LOW
        )
        
        println("创建无分类备忘录: ${noCategory.title}")
        println("  分类ID: ${noCategory.categoryId ?: "null"}")
        println("  优先级: ${noCategory.priority.displayName}")
        
        println("✅ 不同分类备忘录创建测试通过")
    }

    @Test
    fun `debug - test category filtering logic`() = runTest {
        println("=== 测试分类筛选逻辑 ===")
        
        val categories = Category.getDefaultCategories()
        val memos = mutableListOf<Memo>()
        
        // Create memos for each category
        categories.forEach { category ->
            repeat(2) { index ->
                memos.add(
                    Memo(
                        title = "${category.name}备忘录${index + 1}",
                        content = "内容${index + 1}",
                        categoryId = category.id,
                        priority = Priority.MEDIUM
                    )
                )
            }
        }
        
        // Create memo without category
        memos.add(
            Memo(
                title = "无分类备忘录",
                content = "无分类内容",
                categoryId = null,
                priority = Priority.LOW
            )
        )
        
        println("总备忘录数量: ${memos.size}")
        
        // Test filtering by category
        categories.forEach { category ->
            val filteredMemos = memos.filter { it.categoryId == category.id }
            println("${category.name}分类的备忘录数量: ${filteredMemos.size}")
            filteredMemos.forEach { memo ->
                println("  - ${memo.title}")
            }
        }
        
        // Test filtering memos without category
        val noCategoryMemos = memos.filter { it.categoryId == null }
        println("无分类备忘录数量: ${noCategoryMemos.size}")
        noCategoryMemos.forEach { memo ->
            println("  - ${memo.title}")
        }
        
        println("✅ 分类筛选逻辑测试通过")
    }

    @Test
    fun `debug - test category selection UI logic`() = runTest {
        println("=== 测试分类选择UI逻辑 ===")

        val categories = Category.getDefaultCategories()
        var selectedCategoryId: String? = null

        // 模拟用户选择分类的过程
        println("初始状态: selectedCategoryId = $selectedCategoryId")

        // 选择工作分类
        val workCategory = categories.first { it.name == "工作" }
        selectedCategoryId = workCategory.id
        println("选择工作分类: selectedCategoryId = $selectedCategoryId")
        assert(selectedCategoryId == "default_work") { "应该选择工作分类" }

        // 再次点击工作分类（取消选择）
        selectedCategoryId = if (selectedCategoryId == workCategory.id) null else workCategory.id
        println("取消选择工作分类: selectedCategoryId = $selectedCategoryId")
        assert(selectedCategoryId == null) { "应该取消选择" }

        // 选择个人分类
        val personalCategory = categories.first { it.name == "个人" }
        selectedCategoryId = personalCategory.id
        println("选择个人分类: selectedCategoryId = $selectedCategoryId")
        assert(selectedCategoryId == "default_personal") { "应该选择个人分类" }

        // 切换到学习分类
        val studyCategory = categories.first { it.name == "学习" }
        selectedCategoryId = studyCategory.id
        println("切换到学习分类: selectedCategoryId = $selectedCategoryId")
        assert(selectedCategoryId == "default_study") { "应该选择学习分类" }

        // 测试创建备忘录时使用选择的分类
        val memo = Memo(
            title = "学习笔记",
            content = "今天学习了Kotlin协程",
            categoryId = selectedCategoryId,
            priority = Priority.MEDIUM
        )

        println("创建的备忘录:")
        println("  标题: ${memo.title}")
        println("  分类ID: ${memo.categoryId}")
        println("  分类名称: ${categories.find { it.id == memo.categoryId }?.name}")

        assert(memo.categoryId == "default_study") { "备忘录应该属于学习分类" }
        println("✅ 分类选择UI逻辑测试通过")
    }

    @Test
    fun `debug - test memo creation with category selection`() = runTest {
        println("=== 测试带分类选择的备忘录创建 ===")

        val categories = Category.getDefaultCategories()

        // 模拟 MemoAddScreen 的分类选择和保存流程
        fun createMemoWithCategory(title: String, content: String, categoryId: String?): Memo {
            return Memo(
                id = java.util.UUID.randomUUID().toString(),
                title = title.trim(),
                content = content.trim(),
                categoryId = categoryId,
                priority = Priority.MEDIUM,
                isFavorite = false,
                isCompleted = false,
                createdAt = LocalDateTime.now(),
                updatedAt = LocalDateTime.now(),
                reminderAt = null,
                tags = emptyList()
            )
        }

        // 测试不同分类的备忘录创建
        val testCases = listOf(
            Triple("工作任务", "完成项目报告", "default_work"),
            Triple("个人事务", "买生日礼物", "default_personal"),
            Triple("学习计划", "复习数据结构", "default_study"),
            Triple("生活安排", "周末大扫除", "default_life"),
            Triple("随机想法", "这是一个没有分类的想法", null)
        )

        testCases.forEach { (title, content, categoryId) ->
            val memo = createMemoWithCategory(title, content, categoryId)
            val categoryName = categories.find { it.id == categoryId }?.name ?: "无分类"

            println("创建备忘录: $title")
            println("  分类: $categoryName")
            println("  分类ID: ${memo.categoryId}")
            println("  内容: ${memo.content}")
            println()

            assert(memo.categoryId == categoryId) { "分类ID应该匹配" }
            assert(memo.title == title) { "标题应该匹配" }
            assert(memo.content == content) { "内容应该匹配" }
        }

        println("✅ 带分类选择的备忘录创建测试通过")
    }
}
