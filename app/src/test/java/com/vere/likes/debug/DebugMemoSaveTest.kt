package com.vere.likes.debug

import android.content.Context
import android.content.SharedPreferences
import com.vere.likes.model.data.Memo
import com.vere.likes.model.data.Priority
import com.vere.likes.model.repository.impl.SharedPrefMemoRepositoryImpl
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Test
import org.junit.Assert.*
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import org.mockito.kotlin.*
import java.time.LocalDateTime

/**
 * 调试测试：专门用于调试备忘录保存问题
 */
class DebugMemoSaveTest {

    @Mock
    private lateinit var context: Context

    @Mock
    private lateinit var sharedPreferences: SharedPreferences

    @Mock
    private lateinit var editor: SharedPreferences.Editor

    private lateinit var repository: SharedPrefMemoRepositoryImpl

    // 用于捕获实际保存的数据
    private var capturedJsonData: String? = null

    @Before
    fun setup() {
        MockitoAnnotations.openMocks(this)
        
        // Mock SharedPreferences behavior
        whenever(context.getSharedPreferences("memo_prefs", Context.MODE_PRIVATE))
            .thenReturn(sharedPreferences)
        whenever(sharedPreferences.edit()).thenReturn(editor)
        
        // 捕获保存的数据
        whenever(editor.putString(eq("memos"), any())).thenAnswer { invocation ->
            capturedJsonData = invocation.getArgument(1)
            println("=== 保存的JSON数据 ===")
            println(capturedJsonData)
            println("==================")
            editor
        }
        whenever(editor.apply()).then {
            println("=== SharedPreferences.apply() 被调用 ===")
        }

        repository = SharedPrefMemoRepositoryImpl(context)
    }

    @Test
    fun `debug - test initial sample data creation`() = runTest {
        println("=== 测试初始示例数据创建 ===")
        
        // Given: 没有现有数据
        whenever(sharedPreferences.getString("memos", null)).thenReturn(null)

        // When: 调用getAllMemos
        val memos = repository.getAllMemos()

        // Then: 应该创建示例数据
        println("获取到的备忘录数量: ${memos.size}")
        memos.forEach { memo ->
            println("备忘录: ${memo.title} - ${memo.content}")
        }

        assertEquals(3, memos.size)
        verify(editor).putString(eq("memos"), any())
        verify(editor).apply()
        
        assertNotNull("应该保存了JSON数据", capturedJsonData)
        assertTrue("JSON数据应该包含会议记录", capturedJsonData!!.contains("会议记录"))
    }

    @Test
    fun `debug - test adding new memo to existing data`() = runTest {
        println("=== 测试添加新备忘录到现有数据 ===")
        
        // Given: 已有示例数据
        val existingData = """[{"id":"existing-1","title":"现有备忘录","content":"现有内容","categoryId":"default_work","priority":"MEDIUM","isFavorite":false,"isCompleted":false,"createdAt":"2025-06-28T01:00:00","updatedAt":"2025-06-28T01:00:00","reminderAt":"","tags":[]}]"""
        whenever(sharedPreferences.getString("memos", null)).thenReturn(existingData)

        // When: 添加新备忘录
        val newMemo = Memo(
            id = "new-memo-id",
            title = "新测试备忘录",
            content = "这是新的测试内容",
            categoryId = "default_work",
            priority = Priority.HIGH,
            isFavorite = false,
            isCompleted = false,
            createdAt = LocalDateTime.now(),
            updatedAt = LocalDateTime.now(),
            reminderAt = null,
            tags = emptyList()
        )

        println("准备添加备忘录: ${newMemo.title}")
        val result = repository.addMemo(newMemo)

        // Then: 验证结果
        println("添加结果: ${result.title}")
        assertEquals(newMemo.id, result.id)
        assertEquals(newMemo.title, result.title)

        verify(editor).putString(eq("memos"), any())
        verify(editor).apply()

        assertNotNull("应该保存了更新的JSON数据", capturedJsonData)
        assertTrue("JSON数据应该包含新备忘录", capturedJsonData!!.contains("新测试备忘录"))
        assertTrue("JSON数据应该包含现有备忘录", capturedJsonData!!.contains("现有备忘录"))
    }

    @Test
    fun `debug - test complete save and load cycle`() = runTest {
        println("=== 测试完整的保存和加载周期 ===")
        
        // Step 1: 初始状态（无数据）
        whenever(sharedPreferences.getString("memos", null)).thenReturn(null)
        
        val initialMemos = repository.getAllMemos()
        println("初始备忘录数量: ${initialMemos.size}")
        
        // Step 2: 模拟数据已保存，现在从保存的数据中读取
        whenever(sharedPreferences.getString("memos", null)).thenReturn(capturedJsonData)
        
        // Step 3: 添加新备忘录
        val newMemo = Memo(
            id = "cycle-test-id",
            title = "周期测试备忘录",
            content = "测试完整周期",
            categoryId = "default_life",
            priority = Priority.MEDIUM,
            isFavorite = true,
            isCompleted = false,
            createdAt = LocalDateTime.now(),
            updatedAt = LocalDateTime.now(),
            reminderAt = null,
            tags = listOf("测试", "周期")
        )

        println("添加新备忘录: ${newMemo.title}")
        repository.addMemo(newMemo)

        // Step 4: 更新mock以返回最新保存的数据
        whenever(sharedPreferences.getString("memos", null)).thenReturn(capturedJsonData)

        // Step 5: 重新加载所有备忘录
        val finalMemos = repository.getAllMemos()
        println("最终备忘录数量: ${finalMemos.size}")
        
        finalMemos.forEach { memo ->
            println("最终备忘录: ${memo.title}")
        }

        // 验证
        assertEquals(4, finalMemos.size) // 3个示例 + 1个新增
        assertTrue("应该包含新添加的备忘录", finalMemos.any { it.title == "周期测试备忘录" })
        assertTrue("应该包含示例备忘录", finalMemos.any { it.title == "会议记录" })
    }

    @Test
    fun `debug - verify JSON serialization format`() = runTest {
        println("=== 验证JSON序列化格式 ===")
        
        // Given
        whenever(sharedPreferences.getString("memos", null)).thenReturn(null)
        
        // When: 创建示例数据
        repository.getAllMemos()
        
        // Then: 检查JSON格式
        assertNotNull("应该有JSON数据", capturedJsonData)
        println("JSON数据长度: ${capturedJsonData!!.length}")
        
        // 验证JSON包含必要字段
        assertTrue("应该包含id字段", capturedJsonData!!.contains("\"id\""))
        assertTrue("应该包含title字段", capturedJsonData!!.contains("\"title\""))
        assertTrue("应该包含content字段", capturedJsonData!!.contains("\"content\""))
        assertTrue("应该包含categoryId字段", capturedJsonData!!.contains("\"categoryId\""))
        assertTrue("应该包含priority字段", capturedJsonData!!.contains("\"priority\""))
        assertTrue("应该包含createdAt字段", capturedJsonData!!.contains("\"createdAt\""))
        assertTrue("应该包含updatedAt字段", capturedJsonData!!.contains("\"updatedAt\""))
        
        // 验证是有效的JSON数组
        assertTrue("应该以[开始", capturedJsonData!!.startsWith("["))
        assertTrue("应该以]结束", capturedJsonData!!.endsWith("]"))
    }
}
