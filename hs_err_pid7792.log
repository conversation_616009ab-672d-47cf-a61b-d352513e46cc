#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 1428816 bytes for Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   The process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (allocation.cpp:389), pid=7792, tid=0x0000000000005f18
#
# JRE version: Java(TM) SE Runtime Environment (8.0_441) (build 1.8.0_441-b07)
# Java VM: Java HotSpot(TM) 64-Bit Server VM (25.441-b07 mixed mode windows-amd64 compressed oops)
# Failed to write core dump. Minidumps are not enabled by default on client versions of Windows
#

---------------  T H R E A D  ---------------

Current thread (0x000001fce2dbf800):  JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=24344, stack(0x0000009f71300000,0x0000009f71400000)]

Stack: [0x0000009f71300000,0x0000009f71400000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x34ba99]
V  [jvm.dll+0x288e12]
V  [jvm.dll+0x2899e0]
V  [jvm.dll+0x27dc65]
V  [jvm.dll+0xd9d8c]
V  [jvm.dll+0xda71c]
V  [jvm.dll+0x49527d]
V  [jvm.dll+0x458865]
V  [jvm.dll+0x461b1f]
V  [jvm.dll+0x460f12]
V  [jvm.dll+0x44abbc]
V  [jvm.dll+0xaf385]
V  [jvm.dll+0xada9f]
V  [jvm.dll+0x24e539]
V  [jvm.dll+0x2a595c]
C  [ucrtbase.dll+0x37b0]
C  [KERNEL32.DLL+0x2e8d7]
C  [ntdll.dll+0x3c34c]


Current CompileTask:
C2:  36754 21184       4       org.jetbrains.kotlin.serialization.deserialization.descriptors.DeserializedMemberScope$OptimizedImplementation::<init> (887 bytes)


---------------  P R O C E S S  ---------------

Java Threads: ( => current thread )
  0x000001fceb5b0800 JavaThread "jar transforms Thread 20" [_thread_blocked, id=3148, stack(0x0000009f7ee00000,0x0000009f7ef00000)]
  0x000001fceb5b2000 JavaThread "jar transforms Thread 19" [_thread_blocked, id=29428, stack(0x0000009f7ed00000,0x0000009f7ee00000)]
  0x000001fceb5ae800 JavaThread "jar transforms Thread 18" [_thread_blocked, id=9868, stack(0x0000009f7ec00000,0x0000009f7ed00000)]
  0x000001fceb5b1800 JavaThread "jar transforms Thread 17" [_thread_blocked, id=23628, stack(0x0000009f7eb00000,0x0000009f7ec00000)]
  0x000001fceb5af000 JavaThread "jar transforms Thread 16" [_thread_blocked, id=4068, stack(0x0000009f7ea00000,0x0000009f7eb00000)]
  0x000001fceb5ab800 JavaThread "jar transforms Thread 15" [_thread_blocked, id=17524, stack(0x0000009f7e900000,0x0000009f7ea00000)]
  0x000001fceb5ad800 JavaThread "jar transforms Thread 14" [_thread_blocked, id=29216, stack(0x0000009f7e800000,0x0000009f7e900000)]
  0x000001fceb5ac000 JavaThread "jar transforms Thread 13" [_thread_blocked, id=19172, stack(0x0000009f7e700000,0x0000009f7e800000)]
  0x000001fceb5ad000 JavaThread "jar transforms Thread 12" [_thread_blocked, id=26216, stack(0x0000009f7e600000,0x0000009f7e700000)]
  0x000001fceb5a7800 JavaThread "jar transforms Thread 11" [_thread_blocked, id=2252, stack(0x0000009f7e500000,0x0000009f7e600000)]
  0x000001fceb5aa800 JavaThread "jar transforms Thread 10" [_thread_blocked, id=21796, stack(0x0000009f7e400000,0x0000009f7e500000)]
  0x000001fceb5aa000 JavaThread "jar transforms Thread 9" [_thread_blocked, id=19068, stack(0x0000009f7e300000,0x0000009f7e400000)]
  0x000001fceb5a8800 JavaThread "jar transforms Thread 8" [_thread_blocked, id=5048, stack(0x0000009f7e200000,0x0000009f7e300000)]
  0x000001fceb5a6000 JavaThread "jar transforms Thread 7" [_thread_blocked, id=24092, stack(0x0000009f7e100000,0x0000009f7e200000)]
  0x000001fceb5a9000 JavaThread "jar transforms Thread 6" [_thread_blocked, id=7772, stack(0x0000009f7e000000,0x0000009f7e100000)]
  0x000001fceb5a7000 JavaThread "jar transforms Thread 5" [_thread_blocked, id=29356, stack(0x0000009f7df00000,0x0000009f7e000000)]
  0x000001fceb5a2800 JavaThread "jar transforms Thread 4" [_thread_blocked, id=15824, stack(0x0000009f7de00000,0x0000009f7df00000)]
  0x000001fceb5a4800 JavaThread "Unconstrained build operations Thread 161" [_thread_blocked, id=3696, stack(0x0000009f7dd00000,0x0000009f7de00000)]
  0x000001fceb5a5800 JavaThread "Unconstrained build operations Thread 160" [_thread_blocked, id=8208, stack(0x0000009f7dc00000,0x0000009f7dd00000)]
  0x000001fceb5a3000 JavaThread "Unconstrained build operations Thread 159" [_thread_blocked, id=27484, stack(0x0000009f7db00000,0x0000009f7dc00000)]
  0x000001fceb5a4000 JavaThread "Unconstrained build operations Thread 158" [_thread_blocked, id=1740, stack(0x0000009f7da00000,0x0000009f7db00000)]
  0x000001fcecd36000 JavaThread "Unconstrained build operations Thread 157" [_thread_blocked, id=28924, stack(0x0000009f7d900000,0x0000009f7da00000)]
  0x000001fcecd38800 JavaThread "Unconstrained build operations Thread 156" [_thread_blocked, id=20996, stack(0x0000009f7d800000,0x0000009f7d900000)]
  0x000001fcecd37800 JavaThread "Unconstrained build operations Thread 155" [_thread_blocked, id=20256, stack(0x0000009f7d700000,0x0000009f7d800000)]
  0x000001fcecd39000 JavaThread "Unconstrained build operations Thread 154" [_thread_blocked, id=27968, stack(0x0000009f7d600000,0x0000009f7d700000)]
  0x000001fcecd34800 JavaThread "Unconstrained build operations Thread 153" [_thread_blocked, id=14196, stack(0x0000009f7d500000,0x0000009f7d600000)]
  0x000001fcecd37000 JavaThread "Unconstrained build operations Thread 152" [_thread_blocked, id=27196, stack(0x0000009f7d400000,0x0000009f7d500000)]
  0x000001fcecd35800 JavaThread "Unconstrained build operations Thread 151" [_thread_blocked, id=29632, stack(0x0000009f7d300000,0x0000009f7d400000)]
  0x000001fcecd30800 JavaThread "Unconstrained build operations Thread 150" [_thread_blocked, id=24516, stack(0x0000009f7d200000,0x0000009f7d300000)]
  0x000001fcecd34000 JavaThread "Unconstrained build operations Thread 149" [_thread_blocked, id=14176, stack(0x0000009f7d100000,0x0000009f7d200000)]
  0x000001fcecd33000 JavaThread "Unconstrained build operations Thread 148" [_thread_blocked, id=8884, stack(0x0000009f7d000000,0x0000009f7d100000)]
  0x000001fcecd32800 JavaThread "Unconstrained build operations Thread 147" [_thread_blocked, id=29376, stack(0x0000009f7cf00000,0x0000009f7d000000)]
  0x000001fcecd30000 JavaThread "Unconstrained build operations Thread 146" [_thread_blocked, id=7416, stack(0x0000009f7ce00000,0x0000009f7cf00000)]
  0x000001fcecd31800 JavaThread "Unconstrained build operations Thread 145" [_thread_blocked, id=21928, stack(0x0000009f7cd00000,0x0000009f7ce00000)]
  0x000001fcecd2c000 JavaThread "Unconstrained build operations Thread 144" [_thread_blocked, id=22392, stack(0x0000009f7cc00000,0x0000009f7cd00000)]
  0x000001fcecd2f000 JavaThread "Unconstrained build operations Thread 143" [_thread_blocked, id=16664, stack(0x0000009f7cb00000,0x0000009f7cc00000)]
  0x000001fcecd2e800 JavaThread "Unconstrained build operations Thread 142" [_thread_blocked, id=27808, stack(0x0000009f7ca00000,0x0000009f7cb00000)]
  0x000001fcecd2b800 JavaThread "Unconstrained build operations Thread 141" [_thread_blocked, id=15428, stack(0x0000009f7c900000,0x0000009f7ca00000)]
  0x000001fcecd2d800 JavaThread "Unconstrained build operations Thread 140" [_thread_blocked, id=15040, stack(0x0000009f7c800000,0x0000009f7c900000)]
  0x000001fcecd2d000 JavaThread "Unconstrained build operations Thread 139" [_thread_blocked, id=2260, stack(0x0000009f7c700000,0x0000009f7c800000)]
  0x000001fcecd2a800 JavaThread "Unconstrained build operations Thread 138" [_thread_blocked, id=18420, stack(0x0000009f7c600000,0x0000009f7c700000)]
  0x000001fcecd2a000 JavaThread "Unconstrained build operations Thread 137" [_thread_blocked, id=29232, stack(0x0000009f7c500000,0x0000009f7c600000)]
  0x000001fcecd27800 JavaThread "Unconstrained build operations Thread 136" [_thread_blocked, id=4416, stack(0x0000009f7c400000,0x0000009f7c500000)]
  0x000001fcecd29000 JavaThread "Unconstrained build operations Thread 135" [_thread_blocked, id=29020, stack(0x0000009f7c300000,0x0000009f7c400000)]
  0x000001fcecd28800 JavaThread "Unconstrained build operations Thread 134" [_thread_blocked, id=29464, stack(0x0000009f7c200000,0x0000009f7c300000)]
  0x000001fcecd27000 JavaThread "Unconstrained build operations Thread 133" [_thread_blocked, id=21200, stack(0x0000009f7c100000,0x0000009f7c200000)]
  0x000001fcecd25800 JavaThread "Unconstrained build operations Thread 132" [_thread_blocked, id=3172, stack(0x0000009f7c000000,0x0000009f7c100000)]
  0x000001fcecd26000 JavaThread "Unconstrained build operations Thread 131" [_thread_blocked, id=5756, stack(0x0000009f7bf00000,0x0000009f7c000000)]
  0x000001fcecd21800 JavaThread "Unconstrained build operations Thread 130" [_thread_blocked, id=1088, stack(0x0000009f7be00000,0x0000009f7bf00000)]
  0x000001fcecd24800 JavaThread "Unconstrained build operations Thread 129" [_thread_blocked, id=25344, stack(0x0000009f7bd00000,0x0000009f7be00000)]
  0x000001fcecd23000 JavaThread "Unconstrained build operations Thread 128" [_thread_blocked, id=25648, stack(0x0000009f7bc00000,0x0000009f7bd00000)]
  0x000001fcecd21000 JavaThread "Unconstrained build operations Thread 127" [_thread_blocked, id=12240, stack(0x0000009f7bb00000,0x0000009f7bc00000)]
  0x000001fcecd24000 JavaThread "Unconstrained build operations Thread 126" [_thread_blocked, id=20888, stack(0x0000009f7ba00000,0x0000009f7bb00000)]
  0x000001fcecd22800 JavaThread "Unconstrained build operations Thread 125" [_thread_blocked, id=16228, stack(0x0000009f7b900000,0x0000009f7ba00000)]
  0x000001fcecd1e800 JavaThread "Unconstrained build operations Thread 124" [_thread_blocked, id=8, stack(0x0000009f7b800000,0x0000009f7b900000)]
  0x000001fcecd1d000 JavaThread "Unconstrained build operations Thread 123" [_thread_blocked, id=4768, stack(0x0000009f7b700000,0x0000009f7b800000)]
  0x000001fcecd20000 JavaThread "Unconstrained build operations Thread 122" [_thread_blocked, id=28900, stack(0x0000009f7b600000,0x0000009f7b700000)]
  0x000001fcecd1f800 JavaThread "Unconstrained build operations Thread 121" [_thread_blocked, id=13936, stack(0x0000009f7b500000,0x0000009f7b600000)]
  0x000001fcecd1e000 JavaThread "Unconstrained build operations Thread 120" [_thread_blocked, id=17828, stack(0x0000009f7b400000,0x0000009f7b500000)]
  0x000001fcecd1a000 JavaThread "Unconstrained build operations Thread 119" [_thread_blocked, id=23932, stack(0x0000009f7b300000,0x0000009f7b400000)]
  0x000001fcecd1b800 JavaThread "Unconstrained build operations Thread 118" [_thread_blocked, id=28636, stack(0x0000009f7b200000,0x0000009f7b300000)]
  0x000001fcecd1c800 JavaThread "Unconstrained build operations Thread 117" [_thread_blocked, id=25716, stack(0x0000009f7b100000,0x0000009f7b200000)]
  0x000001fcecd1b000 JavaThread "Unconstrained build operations Thread 116" [_thread_blocked, id=12084, stack(0x0000009f7b000000,0x0000009f7b100000)]
  0x000001fce4bc5000 JavaThread "Unconstrained build operations Thread 115" [_thread_blocked, id=19848, stack(0x0000009f7af00000,0x0000009f7b000000)]
  0x000001fce4bc1800 JavaThread "Unconstrained build operations Thread 114" [_thread_blocked, id=28228, stack(0x0000009f7ae00000,0x0000009f7af00000)]
  0x000001fce4bc4800 JavaThread "Unconstrained build operations Thread 113" [_thread_blocked, id=2736, stack(0x0000009f7ad00000,0x0000009f7ae00000)]
  0x000001fce4bc3800 JavaThread "Unconstrained build operations Thread 112" [_thread_blocked, id=10564, stack(0x0000009f7ac00000,0x0000009f7ad00000)]
  0x000001fce4bc0000 JavaThread "Unconstrained build operations Thread 111" [_thread_blocked, id=28588, stack(0x0000009f7ab00000,0x0000009f7ac00000)]
  0x000001fce4bc3000 JavaThread "Unconstrained build operations Thread 110" [_thread_blocked, id=18272, stack(0x0000009f7aa00000,0x0000009f7ab00000)]
  0x000001fce4bc2000 JavaThread "Unconstrained build operations Thread 109" [_thread_blocked, id=29404, stack(0x0000009f7a900000,0x0000009f7aa00000)]
  0x000001fce4bc0800 JavaThread "Unconstrained build operations Thread 108" [_thread_blocked, id=22688, stack(0x0000009f7a800000,0x0000009f7a900000)]
  0x000001fce4bbc000 JavaThread "Unconstrained build operations Thread 107" [_thread_blocked, id=25408, stack(0x0000009f7a700000,0x0000009f7a800000)]
  0x000001fce4bbf000 JavaThread "Unconstrained build operations Thread 106" [_thread_blocked, id=12412, stack(0x0000009f7a600000,0x0000009f7a700000)]
  0x000001fce4bbd800 JavaThread "Unconstrained build operations Thread 105" [_thread_blocked, id=8360, stack(0x0000009f7a500000,0x0000009f7a600000)]
  0x000001fce4bbb800 JavaThread "Unconstrained build operations Thread 104" [_thread_blocked, id=18036, stack(0x0000009f7a400000,0x0000009f7a500000)]
  0x000001fce4bbe800 JavaThread "Unconstrained build operations Thread 103" [_thread_blocked, id=29348, stack(0x0000009f7a300000,0x0000009f7a400000)]
  0x000001fce4bba000 JavaThread "Unconstrained build operations Thread 102" [_thread_blocked, id=24216, stack(0x0000009f7a200000,0x0000009f7a300000)]
  0x000001fce4bba800 JavaThread "Unconstrained build operations Thread 101" [_thread_blocked, id=28272, stack(0x0000009f7a100000,0x0000009f7a200000)]
  0x000001fce4bbd000 JavaThread "Unconstrained build operations Thread 100" [_thread_blocked, id=23260, stack(0x0000009f7a000000,0x0000009f7a100000)]
  0x000001fce4bb9000 JavaThread "Unconstrained build operations Thread 99" [_thread_blocked, id=29024, stack(0x0000009f79f00000,0x0000009f7a000000)]
  0x000001fce4bb7800 JavaThread "Unconstrained build operations Thread 98" [_thread_blocked, id=22668, stack(0x0000009f79e00000,0x0000009f79f00000)]
  0x000001fce4bb7000 JavaThread "Unconstrained build operations Thread 97" [_thread_blocked, id=16400, stack(0x0000009f79d00000,0x0000009f79e00000)]
  0x000001fce4bb8800 JavaThread "Unconstrained build operations Thread 96" [_thread_blocked, id=28860, stack(0x0000009f79c00000,0x0000009f79d00000)]
  0x000001fce4bb6000 JavaThread "Unconstrained build operations Thread 95" [_thread_blocked, id=29148, stack(0x0000009f79b00000,0x0000009f79c00000)]
  0x000001fce65a9800 JavaThread "Unconstrained build operations Thread 94" [_thread_blocked, id=16140, stack(0x0000009f79a00000,0x0000009f79b00000)]
  0x000001fce65a8000 JavaThread "Unconstrained build operations Thread 93" [_thread_blocked, id=13664, stack(0x0000009f79900000,0x0000009f79a00000)]
  0x000001fce65a8800 JavaThread "Unconstrained build operations Thread 92" [_thread_blocked, id=27836, stack(0x0000009f79800000,0x0000009f79900000)]
  0x000001fce65a4000 JavaThread "Unconstrained build operations Thread 91" [_thread_blocked, id=7904, stack(0x0000009f79700000,0x0000009f79800000)]
  0x000001fce65a7000 JavaThread "Unconstrained build operations Thread 90" [_thread_blocked, id=23336, stack(0x0000009f79600000,0x0000009f79700000)]
  0x000001fce65a6800 JavaThread "Unconstrained build operations Thread 89" [_thread_blocked, id=22360, stack(0x0000009f79500000,0x0000009f79600000)]
  0x000001fce65a3800 JavaThread "Unconstrained build operations Thread 88" [_thread_blocked, id=20868, stack(0x0000009f79400000,0x0000009f79500000)]
  0x000001fce65a5800 JavaThread "Unconstrained build operations Thread 87" [_thread_blocked, id=6604, stack(0x0000009f79300000,0x0000009f79400000)]
  0x000001fce65a2000 JavaThread "Unconstrained build operations Thread 86" [_thread_blocked, id=26672, stack(0x0000009f79200000,0x0000009f79300000)]
  0x000001fce65a2800 JavaThread "Unconstrained build operations Thread 85" [_thread_blocked, id=2104, stack(0x0000009f79100000,0x0000009f79200000)]
  0x000001fce65a5000 JavaThread "Unconstrained build operations Thread 84" [_thread_blocked, id=12188, stack(0x0000009f79000000,0x0000009f79100000)]
  0x000001fce659e000 JavaThread "Unconstrained build operations Thread 83" [_thread_blocked, id=10824, stack(0x0000009f78f00000,0x0000009f79000000)]
  0x000001fce65a0800 JavaThread "Unconstrained build operations Thread 82" [_thread_blocked, id=22372, stack(0x0000009f78e00000,0x0000009f78f00000)]
  0x000001fce659f000 JavaThread "Unconstrained build operations Thread 81" [_thread_blocked, id=21696, stack(0x0000009f78d00000,0x0000009f78e00000)]
  0x000001fce65a1000 JavaThread "Unconstrained build operations Thread 80" [_thread_blocked, id=5968, stack(0x0000009f78c00000,0x0000009f78d00000)]
  0x000001fce659f800 JavaThread "Unconstrained build operations Thread 79" [_thread_blocked, id=19644, stack(0x0000009f78b00000,0x0000009f78c00000)]
  0x000001fce659a800 JavaThread "Unconstrained build operations Thread 78" [_thread_blocked, id=7908, stack(0x0000009f78a00000,0x0000009f78b00000)]
  0x000001fce659b000 JavaThread "Unconstrained build operations Thread 77" [_thread_blocked, id=27368, stack(0x0000009f78900000,0x0000009f78a00000)]
  0x000001fce659d800 JavaThread "Unconstrained build operations Thread 76" [_thread_blocked, id=25084, stack(0x0000009f78800000,0x0000009f78900000)]
  0x000001fce659c800 JavaThread "Unconstrained build operations Thread 75" [_thread_blocked, id=29108, stack(0x0000009f78700000,0x0000009f78800000)]
  0x000001fce659c000 JavaThread "Unconstrained build operations Thread 74" [_thread_blocked, id=28716, stack(0x0000009f78600000,0x0000009f78700000)]
  0x000001fcec133000 JavaThread "Unconstrained build operations Thread 73" [_thread_blocked, id=22344, stack(0x0000009f78500000,0x0000009f78600000)]
  0x000001fcec133800 JavaThread "Unconstrained build operations Thread 72" [_thread_blocked, id=23432, stack(0x0000009f78400000,0x0000009f78500000)]
  0x000001fcec12f000 JavaThread "Unconstrained build operations Thread 71" [_thread_blocked, id=25588, stack(0x0000009f78300000,0x0000009f78400000)]
  0x000001fcec132000 JavaThread "Unconstrained build operations Thread 70" [_thread_blocked, id=17312, stack(0x0000009f78200000,0x0000009f78300000)]
  0x000001fcec130000 JavaThread "Unconstrained build operations Thread 69" [_thread_blocked, id=24020, stack(0x0000009f78100000,0x0000009f78200000)]
  0x000001fcec12e800 JavaThread "Unconstrained build operations Thread 68" [_thread_blocked, id=14620, stack(0x0000009f78000000,0x0000009f78100000)]
  0x000001fcec131800 JavaThread "Unconstrained build operations Thread 67" [_thread_blocked, id=27892, stack(0x0000009f77f00000,0x0000009f78000000)]
  0x000001fcec12d800 JavaThread "Unconstrained build operations Thread 66" [_thread_blocked, id=10656, stack(0x0000009f77e00000,0x0000009f77f00000)]
  0x000001fcec130800 JavaThread "Unconstrained build operations Thread 65" [_thread_blocked, id=27916, stack(0x0000009f77d00000,0x0000009f77e00000)]
  0x000001fcec12a000 JavaThread "Unconstrained build operations Thread 64" [_thread_blocked, id=24852, stack(0x0000009f77c00000,0x0000009f77d00000)]
  0x000001fcec12d000 JavaThread "Unconstrained build operations Thread 63" [_thread_blocked, id=29496, stack(0x0000009f77b00000,0x0000009f77c00000)]
  0x000001fcec129000 JavaThread "Unconstrained build operations Thread 62" [_thread_blocked, id=6600, stack(0x0000009f77a00000,0x0000009f77b00000)]
  0x000001fcec12a800 JavaThread "Unconstrained build operations Thread 61" [_thread_blocked, id=14008, stack(0x0000009f77900000,0x0000009f77a00000)]
  0x000001fcec12b800 JavaThread "Unconstrained build operations Thread 60" [_thread_blocked, id=17928, stack(0x0000009f77800000,0x0000009f77900000)]
  0x000001fcec12c000 JavaThread "Unconstrained build operations Thread 59" [_thread_blocked, id=27664, stack(0x0000009f77700000,0x0000009f77800000)]
  0x000001fcec125800 JavaThread "Unconstrained build operations Thread 58" [_thread_blocked, id=18080, stack(0x0000009f77600000,0x0000009f77700000)]
  0x000001fcec128800 JavaThread "Unconstrained build operations Thread 57" [_thread_blocked, id=26176, stack(0x0000009f77500000,0x0000009f77600000)]
  0x000001fcec127800 JavaThread "Unconstrained build operations Thread 56" [_thread_blocked, id=12516, stack(0x0000009f77400000,0x0000009f77500000)]
  0x000001fcec127000 JavaThread "Unconstrained build operations Thread 55" [_thread_blocked, id=22512, stack(0x0000009f77300000,0x0000009f77400000)]
  0x000001fcec126000 JavaThread "Unconstrained build operations Thread 54" [_thread_blocked, id=13800, stack(0x0000009f77200000,0x0000009f77300000)]
  0x000001fcec124800 JavaThread "Unconstrained build operations Thread 53" [_thread_blocked, id=23956, stack(0x0000009f77100000,0x0000009f77200000)]
  0x000001fcef635800 JavaThread "Unconstrained build operations Thread 52" [_thread_blocked, id=26460, stack(0x0000009f77000000,0x0000009f77100000)]
  0x000001fcef633800 JavaThread "Unconstrained build operations Thread 51" [_thread_blocked, id=6660, stack(0x0000009f76f00000,0x0000009f77000000)]
  0x000001fcef632000 JavaThread "Unconstrained build operations Thread 50" [_thread_blocked, id=26560, stack(0x0000009f76e00000,0x0000009f76f00000)]
  0x000001fcef635000 JavaThread "Unconstrained build operations Thread 49" [_thread_blocked, id=2348, stack(0x0000009f76d00000,0x0000009f76e00000)]
  0x000001fcef631000 JavaThread "Unconstrained build operations Thread 48" [_thread_blocked, id=10716, stack(0x0000009f76c00000,0x0000009f76d00000)]
  0x000001fcef634000 JavaThread "Unconstrained build operations Thread 47" [_thread_blocked, id=23596, stack(0x0000009f76b00000,0x0000009f76c00000)]
  0x000001fcef62f800 JavaThread "Unconstrained build operations Thread 46" [_thread_blocked, id=20076, stack(0x0000009f76a00000,0x0000009f76b00000)]
  0x000001fcef632800 JavaThread "Unconstrained build operations Thread 45" [_thread_blocked, id=27684, stack(0x0000009f76900000,0x0000009f76a00000)]
  0x000001fcef630800 JavaThread "Unconstrained build operations Thread 44" [_thread_blocked, id=21724, stack(0x0000009f76800000,0x0000009f76900000)]
  0x000001fcef62f000 JavaThread "Unconstrained build operations Thread 43" [_thread_blocked, id=7688, stack(0x0000009f76700000,0x0000009f76800000)]
  0x000001fcef62c000 JavaThread "Unconstrained build operations Thread 42" [_thread_blocked, id=12460, stack(0x0000009f76600000,0x0000009f76700000)]
  0x000001fcef62b000 JavaThread "Unconstrained build operations Thread 41" [_thread_blocked, id=23188, stack(0x0000009f76500000,0x0000009f76600000)]
  0x000001fcef62d800 JavaThread "Unconstrained build operations Thread 40" [_thread_blocked, id=25984, stack(0x0000009f76400000,0x0000009f76500000)]
  0x000001fcef62c800 JavaThread "Unconstrained build operations Thread 39" [_thread_blocked, id=6596, stack(0x0000009f76300000,0x0000009f76400000)]
  0x000001fcef62e000 JavaThread "Unconstrained build operations Thread 38" [_thread_blocked, id=11716, stack(0x0000009f76200000,0x0000009f76300000)]
  0x000001fcef62a800 JavaThread "Unconstrained build operations Thread 37" [_thread_blocked, id=16392, stack(0x0000009f76100000,0x0000009f76200000)]
  0x000001fcef626800 JavaThread "Unconstrained build operations Thread 36" [_thread_blocked, id=4604, stack(0x0000009f76000000,0x0000009f76100000)]
  0x000001fcef629800 JavaThread "Unconstrained build operations Thread 35" [_thread_blocked, id=24524, stack(0x0000009f75f00000,0x0000009f76000000)]
  0x000001fcef629000 JavaThread "Unconstrained build operations Thread 34" [_thread_blocked, id=25776, stack(0x0000009f75e00000,0x0000009f75f00000)]
  0x000001fcef628000 JavaThread "Unconstrained build operations Thread 33" [_thread_blocked, id=4340, stack(0x0000009f75d00000,0x0000009f75e00000)]
  0x000001fcef627800 JavaThread "Unconstrained build operations Thread 32" [_thread_blocked, id=28116, stack(0x0000009f75c00000,0x0000009f75d00000)]
  0x000001fce7c90000 JavaThread "Unconstrained build operations Thread 31" [_thread_blocked, id=29268, stack(0x0000009f75b00000,0x0000009f75c00000)]
  0x000001fce7c8d000 JavaThread "Unconstrained build operations Thread 30" [_thread_blocked, id=28332, stack(0x0000009f75a00000,0x0000009f75b00000)]
  0x000001fce7c8d800 JavaThread "Unconstrained build operations Thread 29" [_thread_blocked, id=25096, stack(0x0000009f75900000,0x0000009f75a00000)]
  0x000001fce7c8c000 JavaThread "Unconstrained build operations Thread 28" [_thread_blocked, id=3956, stack(0x0000009f75800000,0x0000009f75900000)]
  0x000001fce7c8f000 JavaThread "Unconstrained build operations Thread 27" [_thread_blocked, id=26144, stack(0x0000009f75700000,0x0000009f75800000)]
  0x000001fce7c8e800 JavaThread "Unconstrained build operations Thread 26" [_thread_blocked, id=28912, stack(0x0000009f75600000,0x0000009f75700000)]
  0x000001fce7c88800 JavaThread "Unconstrained build operations Thread 25" [_thread_blocked, id=5824, stack(0x0000009f75500000,0x0000009f75600000)]
  0x000001fce7c89000 JavaThread "Unconstrained build operations Thread 24" [_thread_blocked, id=26900, stack(0x0000009f75400000,0x0000009f75500000)]
  0x000001fce7c8b800 JavaThread "Unconstrained build operations Thread 23" [_thread_blocked, id=18288, stack(0x0000009f75300000,0x0000009f75400000)]
  0x000001fce7c87800 JavaThread "Unconstrained build operations Thread 22" [_thread_blocked, id=27544, stack(0x0000009f75200000,0x0000009f75300000)]
  0x000001fce7c8a000 JavaThread "Unconstrained build operations Thread 21" [_thread_blocked, id=29544, stack(0x0000009f75100000,0x0000009f75200000)]
  0x000001fce7c8a800 JavaThread "Unconstrained build operations Thread 20" [_thread_blocked, id=15256, stack(0x0000009f75000000,0x0000009f75100000)]
  0x000001fce7c84000 JavaThread "Unconstrained build operations Thread 19" [_thread_blocked, id=24692, stack(0x0000009f74f00000,0x0000009f75000000)]
  0x000001fce7c87000 JavaThread "Unconstrained build operations Thread 18" [_thread_blocked, id=29588, stack(0x0000009f74e00000,0x0000009f74f00000)]
  0x000001fce7c86000 JavaThread "Unconstrained build operations Thread 17" [_thread_blocked, id=23124, stack(0x0000009f74d00000,0x0000009f74e00000)]
  0x000001fce7c85800 JavaThread "Unconstrained build operations Thread 16" [_thread_blocked, id=5860, stack(0x0000009f74c00000,0x0000009f74d00000)]
  0x000001fce7c82800 JavaThread "Unconstrained build operations Thread 15" [_thread_blocked, id=23256, stack(0x0000009f74b00000,0x0000009f74c00000)]
  0x000001fce7c81800 JavaThread "Unconstrained build operations Thread 14" [_thread_blocked, id=9628, stack(0x0000009f74a00000,0x0000009f74b00000)]
  0x000001fce7c84800 JavaThread "Unconstrained build operations Thread 13" [_thread_blocked, id=8440, stack(0x0000009f74900000,0x0000009f74a00000)]
  0x000001fce7c83000 JavaThread "Unconstrained build operations Thread 12" [_thread_blocked, id=23524, stack(0x0000009f74800000,0x0000009f74900000)]
  0x000001fce7c81000 JavaThread "Unconstrained build operations Thread 11" [_thread_blocked, id=27500, stack(0x0000009f74700000,0x0000009f74800000)]
  0x000001fce2e4a000 JavaThread "Unconstrained build operations Thread 10" [_thread_blocked, id=23804, stack(0x0000009f74600000,0x0000009f74700000)]
  0x000001fce44df800 JavaThread "Unconstrained build operations Thread 9" [_thread_blocked, id=29408, stack(0x0000009f74500000,0x0000009f74600000)]
  0x000001fce57ce000 JavaThread "Unconstrained build operations Thread 8" [_thread_blocked, id=23820, stack(0x0000009f74400000,0x0000009f74500000)]
  0x000001fce57c9800 JavaThread "Unconstrained build operations Thread 7" [_thread_blocked, id=5664, stack(0x0000009f74300000,0x0000009f74400000)]
  0x000001fce57c7000 JavaThread "Unconstrained build operations Thread 6" [_thread_blocked, id=28488, stack(0x0000009f74200000,0x0000009f74300000)]
  0x000001fce57c8800 JavaThread "Unconstrained build operations Thread 5" [_thread_blocked, id=29144, stack(0x0000009f74100000,0x0000009f74200000)]
  0x000001fce788e800 JavaThread "Unconstrained build operations Thread 4" [_thread_blocked, id=7880, stack(0x0000009f74000000,0x0000009f74100000)]
  0x000001fce788f800 JavaThread "Unconstrained build operations Thread 3" [_thread_blocked, id=4104, stack(0x0000009f73f00000,0x0000009f74000000)]
  0x000001fce7887000 JavaThread "Unconstrained build operations Thread 2" [_thread_blocked, id=27512, stack(0x0000009f73e00000,0x0000009f73f00000)]
  0x000001fce7886800 JavaThread "Unconstrained build operations" [_thread_blocked, id=26572, stack(0x0000009f73d00000,0x0000009f73e00000)]
  0x000001fce788e000 JavaThread "jar transforms Thread 3" [_thread_blocked, id=19972, stack(0x0000009f73b00000,0x0000009f73c00000)]
  0x000001fce788d000 JavaThread "Kotlin DSL Writer" [_thread_blocked, id=26344, stack(0x0000009f73a00000,0x0000009f73b00000)]
  0x000001fce788c800 JavaThread "Cache worker for Build Output Cleanup Cache (D:\code\Android\Likes\.gradle\buildOutputCleanup)" [_thread_blocked, id=5560, stack(0x0000009f73900000,0x0000009f73a00000)]
  0x000001fce44de800 JavaThread "Cache worker for dependencies-accessors (D:\code\Android\Likes\.gradle\7.2\dependencies-accessors)" [_thread_blocked, id=20588, stack(0x0000009f73800000,0x0000009f73900000)]
  0x000001fce44db000 JavaThread "jar transforms Thread 2" [_thread_blocked, id=24560, stack(0x0000009f73700000,0x0000009f73800000)]
  0x000001fce44dd000 JavaThread "jar transforms" [_thread_blocked, id=11012, stack(0x0000009f73600000,0x0000009f73700000)]
  0x000001fce7883800 JavaThread "Cache worker for kotlin-dsl (C:\Users\<USER>\.gradle\caches\7.2\kotlin-dsl)" [_thread_blocked, id=29044, stack(0x0000009f73500000,0x0000009f73600000)]
  0x000001fce7882800 JavaThread "Cache worker for execution history cache (C:\Users\<USER>\.gradle\caches\7.2\executionHistory)" [_thread_blocked, id=27904, stack(0x0000009f73400000,0x0000009f73500000)]
  0x000001fce7882000 JavaThread "Cache worker for cache directory md-rule (C:\Users\<USER>\.gradle\caches\7.2\md-rule)" [_thread_blocked, id=25772, stack(0x0000009f73300000,0x0000009f73400000)]
  0x000001fce7881000 JavaThread "Cache worker for cache directory md-supplier (C:\Users\<USER>\.gradle\caches\7.2\md-supplier)" [_thread_blocked, id=18864, stack(0x0000009f73200000,0x0000009f73300000)]
  0x000001fce2e50800 JavaThread "Cache worker for checksums cache (D:\code\Android\Likes\.gradle\checksums)" [_thread_blocked, id=13964, stack(0x0000009f73100000,0x0000009f73200000)]
  0x000001fce2e50000 JavaThread "File watcher consumer" daemon [_thread_blocked, id=18756, stack(0x0000009f73000000,0x0000009f73100000)]
  0x000001fce2e4f000 JavaThread "File watcher server" daemon [_thread_in_native, id=21676, stack(0x0000009f72f00000,0x0000009f73000000)]
  0x000001fce2e4b800 JavaThread "Cache worker for file hash cache (D:\code\Android\Likes\.gradle\7.2\fileHashes)" [_thread_blocked, id=29128, stack(0x0000009f72e00000,0x0000009f72f00000)]
  0x000001fce2e4c000 JavaThread "Cache worker for file hash cache (C:\Users\<USER>\.gradle\caches\7.2\fileHashes)" [_thread_blocked, id=9120, stack(0x0000009f72d00000,0x0000009f72e00000)]
  0x000001fce2e4d800 JavaThread "File lock request listener" [_thread_in_native, id=20512, stack(0x0000009f72c00000,0x0000009f72d00000)]
  0x000001fce2e4e800 JavaThread "Cache worker for journal cache (C:\Users\<USER>\.gradle\caches\journal-1)" [_thread_blocked, id=5060, stack(0x0000009f72b00000,0x0000009f72c00000)]
  0x000001fce5ef2000 JavaThread "Daemon client event forwarder" [_thread_blocked, id=26516, stack(0x0000009f72a00000,0x0000009f72b00000)]
  0x000001fce5de2800 JavaThread "Stdin handler" [_thread_blocked, id=22080, stack(0x0000009f72900000,0x0000009f72a00000)]
  0x000001fce5ddd800 JavaThread "Asynchronous log dispatcher for DefaultDaemonConnection: socket connection from /127.0.0.1:62383 to /127.0.0.1:62384" [_thread_blocked, id=28024, stack(0x0000009f72800000,0x0000009f72900000)]
  0x000001fce5e81000 JavaThread "Daemon worker" [_thread_in_Java, id=27720, stack(0x0000009f72700000,0x0000009f72800000)]
  0x000001fce5e7e000 JavaThread "Cancel handler" [_thread_blocked, id=23300, stack(0x0000009f72600000,0x0000009f72700000)]
  0x000001fce5e9b000 JavaThread "Handler for socket connection from /127.0.0.1:62383 to /127.0.0.1:62384" [_thread_in_native, id=29368, stack(0x0000009f72500000,0x0000009f72600000)]
  0x000001fce5e9a000 JavaThread "Daemon" [_thread_blocked, id=28340, stack(0x0000009f72400000,0x0000009f72500000)]
  0x000001fce5e42800 JavaThread "Daemon periodic checks" [_thread_blocked, id=17548, stack(0x0000009f72300000,0x0000009f72400000)]
  0x000001fce5e39000 JavaThread "Incoming local TCP Connector on port 62383" [_thread_in_native, id=13584, stack(0x0000009f72200000,0x0000009f72300000)]
  0x000001fce56bf800 JavaThread "Daemon health stats" [_thread_blocked, id=25184, stack(0x0000009f72100000,0x0000009f72200000)]
  0x000001fce2f1d800 JavaThread "Service Thread" daemon [_thread_blocked, id=8560, stack(0x0000009f71f00000,0x0000009f72000000)]
  0x000001fce2e4d000 JavaThread "C1 CompilerThread11" daemon [_thread_blocked, id=20108, stack(0x0000009f71e00000,0x0000009f71f00000)]
  0x000001fce2e49000 JavaThread "C1 CompilerThread10" daemon [_thread_in_native, id=20488, stack(0x0000009f71d00000,0x0000009f71e00000)]
  0x000001fce2e40800 JavaThread "C1 CompilerThread9" daemon [_thread_blocked, id=19728, stack(0x0000009f71c00000,0x0000009f71d00000)]
  0x000001fce2e3d800 JavaThread "C1 CompilerThread8" daemon [_thread_blocked, id=29204, stack(0x0000009f71b00000,0x0000009f71c00000)]
  0x000001fce2e3d000 JavaThread "C2 CompilerThread7" daemon [_thread_blocked, id=4028, stack(0x0000009f71a00000,0x0000009f71b00000)]
  0x000001fce2e36000 JavaThread "C2 CompilerThread6" daemon [_thread_blocked, id=14440, stack(0x0000009f71900000,0x0000009f71a00000)]
  0x000001fce2e34800 JavaThread "C2 CompilerThread5" daemon [_thread_in_native, id=21444, stack(0x0000009f71800000,0x0000009f71900000)]
  0x000001fce2e2f800 JavaThread "C2 CompilerThread4" daemon [_thread_blocked, id=17356, stack(0x0000009f71700000,0x0000009f71800000)]
  0x000001fce0c2c000 JavaThread "C2 CompilerThread3" daemon [_thread_in_native, id=21532, stack(0x0000009f71600000,0x0000009f71700000)]
  0x000001fce0c2a000 JavaThread "C2 CompilerThread2" daemon [_thread_blocked, id=18484, stack(0x0000009f71500000,0x0000009f71600000)]
  0x000001fce2e17000 JavaThread "C2 CompilerThread1" daemon [_thread_blocked, id=17700, stack(0x0000009f71400000,0x0000009f71500000)]
=>0x000001fce2dbf800 JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=24344, stack(0x0000009f71300000,0x0000009f71400000)]
  0x000001fce2dbe800 JavaThread "Attach Listener" daemon [_thread_blocked, id=25124, stack(0x0000009f71200000,0x0000009f71300000)]
  0x000001fce2e2a000 JavaThread "Signal Dispatcher" daemon [_thread_blocked, id=8912, stack(0x0000009f71100000,0x0000009f71200000)]
  0x000001fce0c19000 JavaThread "Finalizer" daemon [_thread_blocked, id=16584, stack(0x0000009f71000000,0x0000009f71100000)]
  0x000001fce0c0c000 JavaThread "Reference Handler" daemon [_thread_blocked, id=12508, stack(0x0000009f70f00000,0x0000009f71000000)]
  0x000001fcc127a000 JavaThread "main" [_thread_blocked, id=20112, stack(0x0000009f6fe00000,0x0000009f6ff00000)]

Other Threads:
  0x000001fce0c04800 VMThread [stack: 0x0000009f70e00000,0x0000009f70f00000] [id=20672]
  0x000001fce2f23000 WatcherThread [stack: 0x0000009f72000000,0x0000009f72100000] [id=196]

VM state:not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x000001fcc1279020] Compile_lock - owner thread: 0x000001fce2e3d800
[0x000001fcc12783a0] MethodCompileQueue_lock - owner thread: 0x000001fce2e3d800

heap address: 0x0000000640800000, size: 6144 MB, Compressed Oops mode: Zero based, Oop shift amount: 3
Narrow klass base: 0x0000000000000000, Narrow klass shift: 3
Compressed class space size: 1065353216 Address: 0x00000007c0800000

Heap:
 PSYoungGen      total 2027520K, used 195899K [0x0000000740800000, 0x00000007c0800000, 0x00000007c0800000)
  eden space 1956864K, 9% used [0x0000000740800000,0x000000074b79e840,0x00000007b7f00000)
  from space 70656K, 22% used [0x00000007bc180000,0x00000007bd130418,0x00000007c0680000)
  to   space 68096K, 0% used [0x00000007b7f00000,0x00000007b7f00000,0x00000007bc180000)
 ParOldGen       total 2502144K, used 87605K [0x0000000640800000, 0x00000006d9380000, 0x0000000740800000)
  object space 2502144K, 3% used [0x0000000640800000,0x0000000645d8d740,0x00000006d9380000)
 Metaspace       used 102548K, capacity 105760K, committed 105984K, reserved 1134592K
  class space    used 12707K, capacity 13311K, committed 13312K, reserved 1040384K

Card table byte_map: [0x000001fcd2120000,0x000001fcd2d30000] byte_map_base: 0x000001fccef1c000

Marking Bits: (ParMarkBitMap*) 0x00007ffe77390200
 Begin Bits: [0x000001fcd3530000, 0x000001fcd9530000)
 End Bits:   [0x000001fcd9530000, 0x000001fcdf530000)

Polling page: 0x000001fcc1200000

CodeCache: size=245760Kb used=74625Kb max_used=74640Kb free=171134Kb
 bounds [0x000001fcc2d60000, 0x000001fcc76a0000, 0x000001fcd1d60000]
 total_blobs=18240 nmethods=17511 adapters=635
 compilation: enabled

Compilation events (10 events):
Event: 36.724 Thread 0x000001fce2e3d000 nmethod 21208 0x000001fcc766c490 code [0x000001fcc766c9a0, 0x000001fcc7670388]
Event: 36.729 Thread 0x000001fce2e17000 21218       4       sun.nio.fs.WindowsFileSystem::getPath (101 bytes)
Event: 36.729 Thread 0x000001fce2e40800 21219       3       org.jetbrains.kotlin.load.java.lazy.descriptors.LazyJavaScope$functionNamesLazy$2::invoke (5 bytes)
Event: 36.730 Thread 0x000001fce2e49000 21220       3       org.jetbrains.kotlin.load.java.lazy.descriptors.LazyJavaScope$functionNamesLazy$2::invoke (12 bytes)
Event: 36.730 Thread 0x000001fce2e40800 nmethod 21219 0x000001fcc7657090 code [0x000001fcc7657200, 0x000001fcc7657510]
Event: 36.730 Thread 0x000001fce2e49000 nmethod 21220 0x000001fcc7662c10 code [0x000001fcc7662d80, 0x000001fcc7662fd0]
Event: 36.730 Thread 0x000001fce2e4d000 21221       3       org.jetbrains.kotlin.metadata.jvm.JvmProtoBuf$JvmPropertySignature$Builder::mergeSyntheticMethod (58 bytes)
Event: 36.730 Thread 0x000001fce2e4d000 nmethod 21221 0x000001fcc7661cd0 code [0x000001fcc7661ec0, 0x000001fcc7662770]
Event: 36.732 Thread 0x000001fce2e36000 nmethod 21160 0x000001fcc767cf10 code [0x000001fcc767d900, 0x000001fcc7686300]
Event: 36.732 Thread 0x000001fce2e17000 nmethod 21218 0x000001fcc7660f90 code [0x000001fcc7661140, 0x000001fcc76616a0]

GC Heap History (10 events):
Event: 25.041 GC heap before
{Heap before GC invocations=20 (full 3):
 PSYoungGen      total 2025984K, used 633219K [0x0000000740800000, 0x00000007c0800000, 0x00000007c0800000)
  eden space 1968128K, 29% used [0x0000000740800000,0x00000007644d2fa8,0x00000007b8a00000)
  from space 57856K, 80% used [0x00000007bcf80000,0x00000007bfd0dfa8,0x00000007c0800000)
  to   space 64512K, 0% used [0x00000007b8a00000,0x00000007b8a00000,0x00000007bc900000)
 ParOldGen       total 1824768K, used 67214K [0x0000000640800000, 0x00000006afe00000, 0x0000000740800000)
  object space 1824768K, 3% used [0x0000000640800000,0x00000006449a3970,0x00000006afe00000)
 Metaspace       used 95615K, capacity 98388K, committed 98496K, reserved 1126400K
  class space    used 11921K, capacity 12495K, committed 12544K, reserved 1040384K
Event: 25.063 GC heap after
Heap after GC invocations=20 (full 3):
 PSYoungGen      total 2025472K, used 56869K [0x0000000740800000, 0x00000007c0800000, 0x00000007c0800000)
  eden space 1968128K, 0% used [0x0000000740800000,0x0000000740800000,0x00000007b8a00000)
  from space 57344K, 99% used [0x00000007b8a00000,0x00000007bc189798,0x00000007bc200000)
  to   space 71680K, 0% used [0x00000007bc200000,0x00000007bc200000,0x00000007c0800000)
 ParOldGen       total 1824768K, used 67222K [0x0000000640800000, 0x00000006afe00000, 0x0000000740800000)
  object space 1824768K, 3% used [0x0000000640800000,0x00000006449a5970,0x00000006afe00000)
 Metaspace       used 95615K, capacity 98388K, committed 98496K, reserved 1126400K
  class space    used 11921K, capacity 12495K, committed 12544K, reserved 1040384K
}
Event: 25.063 GC heap before
{Heap before GC invocations=21 (full 4):
 PSYoungGen      total 2025472K, used 56869K [0x0000000740800000, 0x00000007c0800000, 0x00000007c0800000)
  eden space 1968128K, 0% used [0x0000000740800000,0x0000000740800000,0x00000007b8a00000)
  from space 57344K, 99% used [0x00000007b8a00000,0x00000007bc189798,0x00000007bc200000)
  to   space 71680K, 0% used [0x00000007bc200000,0x00000007bc200000,0x00000007c0800000)
 ParOldGen       total 1824768K, used 67222K [0x0000000640800000, 0x00000006afe00000, 0x0000000740800000)
  object space 1824768K, 3% used [0x0000000640800000,0x00000006449a5970,0x00000006afe00000)
 Metaspace       used 95615K, capacity 98388K, committed 98496K, reserved 1126400K
  class space    used 11921K, capacity 12495K, committed 12544K, reserved 1040384K
Event: 25.248 GC heap after
Heap after GC invocations=21 (full 4):
 PSYoungGen      total 2025472K, used 0K [0x0000000740800000, 0x00000007c0800000, 0x00000007c0800000)
  eden space 1968128K, 0% used [0x0000000740800000,0x0000000740800000,0x00000007b8a00000)
  from space 57344K, 0% used [0x00000007b8a00000,0x00000007b8a00000,0x00000007bc200000)
  to   space 71680K, 0% used [0x00000007bc200000,0x00000007bc200000,0x00000007c0800000)
 ParOldGen       total 2502144K, used 87581K [0x0000000640800000, 0x00000006d9380000, 0x0000000740800000)
  object space 2502144K, 3% used [0x0000000640800000,0x0000000645d87740,0x00000006d9380000)
 Metaspace       used 95615K, capacity 98388K, committed 98496K, reserved 1126400K
  class space    used 11921K, capacity 12495K, committed 12544K, reserved 1040384K
}
Event: 30.572 GC heap before
{Heap before GC invocations=22 (full 4):
 PSYoungGen      total 2025472K, used 1968128K [0x0000000740800000, 0x00000007c0800000, 0x00000007c0800000)
  eden space 1968128K, 100% used [0x0000000740800000,0x00000007b8a00000,0x00000007b8a00000)
  from space 57344K, 0% used [0x00000007b8a00000,0x00000007b8a00000,0x00000007bc200000)
  to   space 71680K, 0% used [0x00000007bc200000,0x00000007bc200000,0x00000007c0800000)
 ParOldGen       total 2502144K, used 87581K [0x0000000640800000, 0x00000006d9380000, 0x0000000740800000)
  object space 2502144K, 3% used [0x0000000640800000,0x0000000645d87740,0x00000006d9380000)
 Metaspace       used 102145K, capacity 105376K, committed 105472K, reserved 1132544K
  class space    used 12698K, capacity 13311K, committed 13312K, reserved 1040384K
Event: 30.577 GC heap after
Heap after GC invocations=22 (full 4):
 PSYoungGen      total 2025984K, used 8693K [0x0000000740800000, 0x00000007c0800000, 0x00000007c0800000)
  eden space 1954304K, 0% used [0x0000000740800000,0x0000000740800000,0x00000007b7c80000)
  from space 71680K, 12% used [0x00000007bc200000,0x00000007bca7d400,0x00000007c0800000)
  to   space 71168K, 0% used [0x00000007b7c80000,0x00000007b7c80000,0x00000007bc200000)
 ParOldGen       total 2502144K, used 87589K [0x0000000640800000, 0x00000006d9380000, 0x0000000740800000)
  object space 2502144K, 3% used [0x0000000640800000,0x0000000645d89740,0x00000006d9380000)
 Metaspace       used 102145K, capacity 105376K, committed 105472K, reserved 1132544K
  class space    used 12698K, capacity 13311K, committed 13312K, reserved 1040384K
}
Event: 34.026 GC heap before
{Heap before GC invocations=23 (full 4):
 PSYoungGen      total 2025984K, used 1962997K [0x0000000740800000, 0x00000007c0800000, 0x00000007c0800000)
  eden space 1954304K, 100% used [0x0000000740800000,0x00000007b7c80000,0x00000007b7c80000)
  from space 71680K, 12% used [0x00000007bc200000,0x00000007bca7d400,0x00000007c0800000)
  to   space 71168K, 0% used [0x00000007b7c80000,0x00000007b7c80000,0x00000007bc200000)
 ParOldGen       total 2502144K, used 87589K [0x0000000640800000, 0x00000006d9380000, 0x0000000740800000)
  object space 2502144K, 3% used [0x0000000640800000,0x0000000645d89740,0x00000006d9380000)
 Metaspace       used 102464K, capacity 105696K, committed 105728K, reserved 1134592K
  class space    used 12707K, capacity 13311K, committed 13312K, reserved 1040384K
Event: 34.032 GC heap after
Heap after GC invocations=23 (full 4):
 PSYoungGen      total 1961472K, used 6876K [0x0000000740800000, 0x00000007c0680000, 0x00000007c0800000)
  eden space 1954304K, 0% used [0x0000000740800000,0x0000000740800000,0x00000007b7c80000)
  from space 7168K, 95% used [0x00000007b7c80000,0x00000007b8337150,0x00000007b8380000)
  to   space 70656K, 0% used [0x00000007bc180000,0x00000007bc180000,0x00000007c0680000)
 ParOldGen       total 2502144K, used 87597K [0x0000000640800000, 0x00000006d9380000, 0x0000000740800000)
  object space 2502144K, 3% used [0x0000000640800000,0x0000000645d8b740,0x00000006d9380000)
 Metaspace       used 102464K, capacity 105696K, committed 105728K, reserved 1134592K
  class space    used 12707K, capacity 13311K, committed 13312K, reserved 1040384K
}
Event: 36.312 GC heap before
{Heap before GC invocations=24 (full 4):
 PSYoungGen      total 1961472K, used 1961180K [0x0000000740800000, 0x00000007c0680000, 0x00000007c0800000)
  eden space 1954304K, 100% used [0x0000000740800000,0x00000007b7c80000,0x00000007b7c80000)
  from space 7168K, 95% used [0x00000007b7c80000,0x00000007b8337150,0x00000007b8380000)
  to   space 70656K, 0% used [0x00000007bc180000,0x00000007bc180000,0x00000007c0680000)
 ParOldGen       total 2502144K, used 87597K [0x0000000640800000, 0x00000006d9380000, 0x0000000740800000)
  object space 2502144K, 3% used [0x0000000640800000,0x0000000645d8b740,0x00000006d9380000)
 Metaspace       used 102497K, capacity 105696K, committed 105728K, reserved 1134592K
  class space    used 12707K, capacity 13311K, committed 13312K, reserved 1040384K
Event: 36.318 GC heap after
Heap after GC invocations=24 (full 4):
 PSYoungGen      total 2027520K, used 16065K [0x0000000740800000, 0x00000007c0800000, 0x00000007c0800000)
  eden space 1956864K, 0% used [0x0000000740800000,0x0000000740800000,0x00000007b7f00000)
  from space 70656K, 22% used [0x00000007bc180000,0x00000007bd130418,0x00000007c0680000)
  to   space 68096K, 0% used [0x00000007b7f00000,0x00000007b7f00000,0x00000007bc180000)
 ParOldGen       total 2502144K, used 87605K [0x0000000640800000, 0x00000006d9380000, 0x0000000740800000)
  object space 2502144K, 3% used [0x0000000640800000,0x0000000645d8d740,0x00000006d9380000)
 Metaspace       used 102497K, capacity 105696K, committed 105728K, reserved 1134592K
  class space    used 12707K, capacity 13311K, committed 13312K, reserved 1040384K
}

Deoptimization events (10 events):
Event: 36.185 Thread 0x000001fce5e81000 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000001fcc5bdaf08 method=org.jetbrains.kotlin.com.intellij.util.text.ImmutableText.subSequence(II)Ljava/lang/CharSequence; @ 1
Event: 36.185 Thread 0x000001fce5e81000 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000001fcc5bb08b4 method=org.jetbrains.kotlin.com.intellij.util.text.ImmutableText.subSequence(II)Ljava/lang/CharSequence; @ 1
Event: 36.185 Thread 0x000001fce5e81000 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000001fcc42adcc0 method=org.jetbrains.kotlin.com.intellij.util.text.ImmutableText.subSequence(II)Ljava/lang/CharSequence; @ 1
Event: 36.185 Thread 0x000001fce5e81000 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000001fcc5e1fcd8 method=org.jetbrains.kotlin.com.intellij.psi.impl.source.CharTableImpl.subSequenceHashCode(Ljava/lang/CharSequence;II)I @ 15
Event: 36.630 Thread 0x000001fce5e81000 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000001fcc5d0aba0 method=kotlin.collections.AbstractMutableList.size()I @ 1
Event: 36.633 Thread 0x000001fce5e81000 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000001fcc6ce3870 method=org.jetbrains.kotlin.serialization.deserialization.descriptors.DeserializedMemberScope$OptimizedImplementation.<init>(Lorg/jetbrains/kotlin/serialization/deserialization
Event: 36.642 Thread 0x000001fce5e81000 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000001fcc6ce3870 method=org.jetbrains.kotlin.serialization.deserialization.descriptors.DeserializedMemberScope$OptimizedImplementation.<init>(Lorg/jetbrains/kotlin/serialization/deserialization
Event: 36.642 Thread 0x000001fce5e81000 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000001fcc6ce3870 method=org.jetbrains.kotlin.serialization.deserialization.descriptors.DeserializedMemberScope$OptimizedImplementation.<init>(Lorg/jetbrains/kotlin/serialization/deserialization
Event: 36.643 Thread 0x000001fce5e81000 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000001fcc6ce3870 method=org.jetbrains.kotlin.serialization.deserialization.descriptors.DeserializedMemberScope$OptimizedImplementation.<init>(Lorg/jetbrains/kotlin/serialization/deserialization
Event: 36.730 Thread 0x000001fce5e81000 Uncommon trap: reason=speculate_class_check action=maybe_recompile pc=0x000001fcc6cb0bec method=org.jetbrains.kotlin.com.intellij.openapi.vfs.VirtualFile.getNameWithoutExtension()Ljava/lang/String; @ 1

Classes redefined (0 events):
No events

Internal exceptions (10 events):
Event: 27.825 Thread 0x000001fceb5ae800 Exception <a 'sun/nio/fs/WindowsException'> (0x000000077e0088d8) thrown at [C:\jenkins\workspace\8-2-build-windows-x64-cygwin-sans-NAS\jdk8u441\1521\hotspot\src\share\vm\prims\jni.cpp, line 710]
Event: 27.826 Thread 0x000001fceb5ae800 Exception <a 'sun/nio/fs/WindowsException'> (0x000000077e008c58) thrown at [C:\jenkins\workspace\8-2-build-windows-x64-cygwin-sans-NAS\jdk8u441\1521\hotspot\src\share\vm\prims\jni.cpp, line 710]
Event: 27.952 Thread 0x000001fceb5a2800 Exception <a 'sun/nio/fs/WindowsException'> (0x0000000793e2eea8) thrown at [C:\jenkins\workspace\8-2-build-windows-x64-cygwin-sans-NAS\jdk8u441\1521\hotspot\src\share\vm\prims\jni.cpp, line 710]
Event: 27.953 Thread 0x000001fceb5a2800 Exception <a 'sun/nio/fs/WindowsException'> (0x0000000793e2f230) thrown at [C:\jenkins\workspace\8-2-build-windows-x64-cygwin-sans-NAS\jdk8u441\1521\hotspot\src\share\vm\prims\jni.cpp, line 710]
Event: 28.065 Thread 0x000001fceb5a7800 Implicit null exception at 0x000001fcc70841f4 to 0x000001fcc70843cd
Event: 28.067 Thread 0x000001fceb5a7800 Exception <a 'sun/nio/fs/WindowsException'> (0x000000079fc3d100) thrown at [C:\jenkins\workspace\8-2-build-windows-x64-cygwin-sans-NAS\jdk8u441\1521\hotspot\src\share\vm\prims\jni.cpp, line 710]
Event: 28.067 Thread 0x000001fceb5a7800 Exception <a 'sun/nio/fs/WindowsException'> (0x000000079fc3d488) thrown at [C:\jenkins\workspace\8-2-build-windows-x64-cygwin-sans-NAS\jdk8u441\1521\hotspot\src\share\vm\prims\jni.cpp, line 710]
Event: 28.073 Thread 0x000001fce5e81000 Implicit null exception at 0x000001fcc56b4cb8 to 0x000001fcc56b4d39
Event: 29.384 Thread 0x000001fce5e81000 Exception <a 'java/lang/NoSuchMethodError': java.lang.Object.lambda$comparingByKey$bbdbfea9$1(Ljava/util/Map$Entry;Ljava/util/Map$Entry;)I> (0x0000000745961688) thrown at [C:\jenkins\workspace\8-2-build-windows-x64-cygwin-sans-NAS\jdk8u441\1521\hotspot\sr
Event: 33.461 Thread 0x000001fce5e81000 Implicit null exception at 0x000001fcc7427523 to 0x000001fcc74286bd

Events (10 events):
Event: 36.643 Thread 0x000001fce5e81000 DEOPT UNPACKING pc=0x000001fcc2da58c3 sp=0x0000009f727f45f0 mode 2
Event: 36.674 Thread 0x000001fce5e81000 DEOPT PACKING pc=0x000001fcc3a9ae68 sp=0x0000009f727f04d0
Event: 36.674 Thread 0x000001fce5e81000 DEOPT UNPACKING pc=0x000001fcc2da7918 sp=0x0000009f727f0238 mode 0
Event: 36.674 Thread 0x000001fce5e81000 DEOPT PACKING pc=0x000001fcc66b079f sp=0x0000009f727f1050
Event: 36.674 Thread 0x000001fce5e81000 DEOPT UNPACKING pc=0x000001fcc2da7918 sp=0x0000009f727f0d70 mode 0
Event: 36.676 Thread 0x000001fce5e81000 DEOPT PACKING pc=0x000001fcc3a9ae68 sp=0x0000009f727f0e80
Event: 36.676 Thread 0x000001fce5e81000 DEOPT UNPACKING pc=0x000001fcc2da7918 sp=0x0000009f727f0be8 mode 0
Event: 36.730 Thread 0x000001fce5e81000 Uncommon trap: trap_request=0xffffff76 fr.pc=0x000001fcc6cb0bec
Event: 36.730 Thread 0x000001fce5e81000 DEOPT PACKING pc=0x000001fcc6cb0bec sp=0x0000009f727f02f0
Event: 36.730 Thread 0x000001fce5e81000 DEOPT UNPACKING pc=0x000001fcc2da58c3 sp=0x0000009f727f0260 mode 2


Dynamic libraries:
0x00007ff76b8f0000 - 0x00007ff76b93f000 	C:\Program Files\Java\jdk-1.8\bin\java.exe
0x00007fff116a0000 - 0x00007fff11908000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007fff0f940000 - 0x00007fff0fa09000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007fff0ea70000 - 0x00007fff0ee5d000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007fff104b0000 - 0x00007fff10564000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007fff11380000 - 0x00007fff11429000 	C:\WINDOWS\System32\msvcrt.dll
0x00007fff10ff0000 - 0x00007fff11096000 	C:\WINDOWS\System32\sechost.dll
0x00007fff0f600000 - 0x00007fff0f718000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007fff10dc0000 - 0x00007fff10f8c000 	C:\WINDOWS\System32\USER32.dll
0x00007fff0f410000 - 0x00007fff0f437000 	C:\WINDOWS\System32\win32u.dll
0x00007fff110a0000 - 0x00007fff110cb000 	C:\WINDOWS\System32\GDI32.dll
0x00007fff0e8a0000 - 0x00007fff0e9d7000 	C:\WINDOWS\System32\gdi32full.dll
0x00007fff0e7f0000 - 0x00007fff0e893000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007fff0ee60000 - 0x00007fff0efab000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ffef1810000 - 0x00007ffef1aaa000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.4484_none_3e0e6d4ce32ef3b3\COMCTL32.dll
0x00007fff11620000 - 0x00007fff1164f000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ffed59a0000 - 0x00007ffed59bb000 	C:\Program Files\Java\jdk-1.8\jre\bin\vcruntime140.dll
0x00007ffefc6f0000 - 0x00007ffefc6fc000 	C:\Program Files\Java\jdk-1.8\jre\bin\vcruntime140_1.dll
0x00007ffec2510000 - 0x00007ffec259e000 	C:\Program Files\Java\jdk-1.8\jre\bin\msvcp140.dll
0x00007ffe76b60000 - 0x00007ffe7740a000 	C:\Program Files\Java\jdk-1.8\jre\bin\server\jvm.dll
0x00007fff10660000 - 0x00007fff10668000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007ffedb6e0000 - 0x00007ffedb6ea000 	C:\WINDOWS\SYSTEM32\WSOCK32.dll
0x00007fff02a60000 - 0x00007fff02a95000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007fff02a10000 - 0x00007fff02a1b000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007fff0f720000 - 0x00007fff0f794000 	C:\WINDOWS\System32\WS2_32.dll
0x00007fff0d560000 - 0x00007fff0d57b000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ffee1960000 - 0x00007ffee1970000 	C:\Program Files\Java\jdk-1.8\jre\bin\verify.dll
0x00007ffec4220000 - 0x00007ffec424b000 	C:\Program Files\Java\jdk-1.8\jre\bin\java.dll
0x00007ffed5980000 - 0x00007ffed5998000 	C:\Program Files\Java\jdk-1.8\jre\bin\zip.dll
0x00007fff10670000 - 0x00007fff10dba000 	C:\WINDOWS\System32\SHELL32.dll
0x00007fff0f1f0000 - 0x00007fff0f364000 	C:\WINDOWS\System32\wintypes.dll
0x00007fff100d0000 - 0x00007fff10456000 	C:\WINDOWS\System32\combase.dll
0x00007fff0c3f0000 - 0x00007fff0cc4b000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007fff0ffd0000 - 0x00007fff100c5000 	C:\WINDOWS\System32\SHCORE.dll
0x00007fff0f440000 - 0x00007fff0f4aa000 	C:\WINDOWS\System32\shlwapi.dll
0x00007fff0e700000 - 0x00007fff0e72f000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007ffec4200000 - 0x00007ffec421c000 	C:\Program Files\Java\jdk-1.8\jre\bin\net.dll
0x00007fff0db00000 - 0x00007fff0db6a000 	C:\WINDOWS\system32\mswsock.dll
0x00007ffec3ca0000 - 0x00007ffec3cb3000 	C:\Program Files\Java\jdk-1.8\jre\bin\nio.dll
0x00007ffec89e0000 - 0x00007ffec8a07000 	C:\Users\<USER>\.gradle\native\e1d6ef7f7dcc3fd88c89a11ec53ec762bb8ba0a96d01ffa2cd45eb1d1d8dd5c5\windows-amd64\native-platform.dll
0x00007ffec2240000 - 0x00007ffec2384000 	C:\Users\<USER>\.gradle\native\5664cfc778a61ccfe75a443a1ab52a65af34e5dc3c78e0209fed803814484fcb\windows-amd64\native-platform-file-events.dll
0x00007ffed08d0000 - 0x00007ffed08dd000 	C:\Program Files\Java\jdk-1.8\jre\bin\management.dll
0x00007fff0ddd0000 - 0x00007fff0ddeb000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007fff0d4c0000 - 0x00007fff0d4fb000 	C:\WINDOWS\system32\rsaenh.dll
0x00007fff0dba0000 - 0x00007fff0dbcb000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007fff0e6d0000 - 0x00007fff0e6f6000 	C:\WINDOWS\SYSTEM32\bcrypt.dll
0x00007fff0f370000 - 0x00007fff0f409000 	C:\WINDOWS\System32\bcryptprimitives.dll
0x00007fff0ddf0000 - 0x00007fff0ddfc000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007fff0d0b0000 - 0x00007fff0d0e3000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007fff11610000 - 0x00007fff1161a000 	C:\WINDOWS\System32\NSI.dll
0x00007fff07bc0000 - 0x00007fff07bdf000 	C:\WINDOWS\SYSTEM32\dhcpcsvc6.DLL
0x00007fff07e90000 - 0x00007fff07eb5000 	C:\WINDOWS\SYSTEM32\dhcpcsvc.DLL
0x00007fff0d0f0000 - 0x00007fff0d216000 	C:\WINDOWS\SYSTEM32\DNSAPI.dll
0x00007ffed10a0000 - 0x00007ffed10c5000 	C:\Program Files\Java\jdk-1.8\jre\bin\sunec.dll
0x00007fff07b20000 - 0x00007fff07b2b000 	C:\Windows\System32\rasadhlp.dll
0x00007fff084b0000 - 0x00007fff08536000 	C:\WINDOWS\System32\fwpuclnt.dll
0x00007ffefc660000 - 0x00007ffefc66e000 	C:\Program Files\Java\jdk-1.8\jre\bin\sunmscapi.dll
0x00007fff0f070000 - 0x00007fff0f1e7000 	C:\WINDOWS\System32\CRYPT32.dll
0x00007fff0dff0000 - 0x00007fff0e020000 	C:\WINDOWS\SYSTEM32\ncrypt.dll
0x00007fff0dfa0000 - 0x00007fff0dfdf000 	C:\WINDOWS\SYSTEM32\NTASN1.dll
0x00007fff08190000 - 0x00007fff083d1000 	C:\WINDOWS\SYSTEM32\dbghelp.dll
0x00007fff10570000 - 0x00007fff10650000 	C:\WINDOWS\System32\OLEAUT32.dll

VM Arguments:
jvm_args: -XX:MaxMetaspaceSize=1024m -XX:+HeapDumpOnOutOfMemoryError -Xms2048m -Xmx6144m -Dfile.encoding=UTF-8 -Duser.country=CN -Duser.language=zh -Duser.variant 
java_command: org.gradle.launcher.daemon.bootstrap.GradleDaemon 7.2
java_class_path (initial): C:\Users\<USER>\.gradle\wrapper\dists\gradle-7.2-bin\2dnblmf4td7x66yl1d74lt32g\gradle-7.2\lib\gradle-launcher-7.2.jar
Launcher Type: SUN_STANDARD

Environment Variables:
JAVA_HOME=C:\Program Files\Java\jdk-1.8
CLASSPATH=D:\code\Android\Likes\\gradle\wrapper\gradle-wrapper.jar
PATH=C:\Program Files\Java\jdk-1.8\bin;C:\Program Files (x86)\VMware\VMware Workstation\bin\;C:\Program Files\Python313\Scripts;C:\Program Files\Python313;C:\Windows;C:\Windows\System32\wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\platform-tools;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\CursorModifier;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;c:\Users\<USER>\AppData\Local\Prog;C:\ProgramData\chocolatey\bin;C:\Windows\System32;C:\Program Files\nodejs\;C:\Program Files\Dock;C:\Program Files\dotnet\;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit\;D:\Program Files\CMake\bin;C:\Program Files\Git\cmd;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Links;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\.dotnet\tools
USERNAME=��ʤ�
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 186 Stepping 2, GenuineIntel



---------------  S Y S T E M  ---------------

OS: Windows 11 , 64 bit Build 26100 (10.0.26100.4484)

CPU:total 20 (initial active 20) (10 cores per cpu, 2 threads per core) family 6 model 186 stepping 2, cmov, cx8, fxsr, mmx, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, avx, avx2, aes, clmul, erms, 3dnowpref, lzcnt, ht, tsc, tscinvbit, bmi1, bmi2, adx

Memory: 4k page, physical 33272696k(4603380k free), swap 50445220k(27612k free)

vm_info: Java HotSpot(TM) 64-Bit Server VM (25.441-b07) for windows-amd64 JRE (1.8.0_441-b07), built on Dec  4 2024 08:12:36 by "java_re" with MS VC++ 17.6 (VS2022)

time: Fri Jul 11 01:47:42 2025
timezone: Intel64 Family 6 Model 186 Stepping 2, GenuineIntel
elapsed time: 36.770858 seconds (0d 0h 0m 36s)

