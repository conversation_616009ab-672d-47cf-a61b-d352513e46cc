# 修复AutoMirrored import问题
Write-Host "开始修复AutoMirrored import问题..." -ForegroundColor Green

$filesToFix = @(
    "app\src\main\java\com\vere\likes\view\compose\screen\BankCardInfoDetailScreen.kt",
    "app\src\main\java\com\vere\likes\view\compose\screen\BankCardInfoEditScreen.kt",
    "app\src\main\java\com\vere\likes\view\compose\screen\CalendarScreen.kt",
    "app\src\main\java\com\vere\likes\view\compose\screen\GoalManagementScreen.kt",
    "app\src\main\java\com\vere\likes\view\compose\screen\HierarchicalPlanningScreen.kt",
    "app\src\main\java\com\vere\likes\view\compose\screen\HierarchicalTimelineScreen.kt",
    "app\src\main\java\com\vere\likes\view\compose\screen\MemoAddScreen.kt",
    "app\src\main\java\com\vere\likes\view\compose\screen\MemoDetailScreen.kt",
    "app\src\main\java\com\vere\likes\view\compose\screen\MemoEditScreen.kt",
    "app\src\main\java\com\vere\likes\view\compose\screen\PlanningSettingsScreen.kt",
    "app\src\main\java\com\vere\likes\view\compose\screen\ReminderSettingsScreen.kt",
    "app\src\main\java\com\vere\likes\view\compose\screen\TaskManagementScreen.kt"
)

$fixedCount = 0

foreach ($file in $filesToFix) {
    if (Test-Path $file) {
        Write-Host "修复文件: $file" -ForegroundColor Yellow
        
        $content = Get-Content $file -Raw -Encoding UTF8
        
        # 检查是否已经有AutoMirrored import
        if ($content -notmatch "import androidx\.compose\.material\.icons\.Icons\.AutoMirrored") {
            # 在Icons import后添加AutoMirrored import
            if ($content -match "import androidx\.compose\.material\.icons\.Icons\s*\n") {
                $content = $content -replace "(import androidx\.compose\.material\.icons\.Icons\s*\n)", "`$1import androidx.compose.material.icons.Icons.AutoMirrored`n"
                
                Set-Content $file $content -Encoding UTF8 -NoNewline
                Write-Host "✅ 已为 $file 添加AutoMirrored import" -ForegroundColor Green
                $fixedCount++
            } else {
                Write-Host "⚠️ 在 $file 中未找到Icons import" -ForegroundColor Yellow
            }
        } else {
            Write-Host "ℹ️ $file 已经有AutoMirrored import" -ForegroundColor Cyan
        }
    } else {
        Write-Host "❌ 文件不存在: $file" -ForegroundColor Red
    }
}

Write-Host "`n=== 修复完成 ===" -ForegroundColor Green
Write-Host "修复的文件数: $fixedCount" -ForegroundColor Yellow
Write-Host "AutoMirrored import问题修复完成！" -ForegroundColor Green
