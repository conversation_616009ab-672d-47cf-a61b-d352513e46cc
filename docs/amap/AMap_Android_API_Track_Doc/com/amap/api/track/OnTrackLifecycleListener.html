<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>OnTrackLifecycleListener</title>
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="OnTrackLifecycleListener";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6};
var tabs = {65535:["t0","所有方法"],2:["t2","实例方法"],4:["t3","抽象方法"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/OnTrackLifecycleListener.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/amap/api/track/OnCustomAttributeListener.html" title="com.amap.api.track中的接口"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../com/amap/api/track/TrackParam.html" title="com.amap.api.track中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/amap/api/track/OnTrackLifecycleListener.html" target="_top">框架</a></li>
<li><a href="OnTrackLifecycleListener.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.amap.api.track</div>
<h2 title="接口 OnTrackLifecycleListener" class="title">接口 OnTrackLifecycleListener</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public interface <span class="typeNameLabel">OnTrackLifecycleListener</span></pre>
<div class="block">用于监听服务的绑定和启停，以及采集的启停事件的监听器</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>1.0</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>所有方法</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">实例方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">抽象方法</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/track/OnTrackLifecycleListener.html#onBindServiceCallback-int-java.lang.String-">onBindServiceCallback</a></span>(int&nbsp;status,
                     java.lang.String&nbsp;message)</code>
<div class="block">绑定服务回调接口</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/track/OnTrackLifecycleListener.html#onStartGatherCallback-int-java.lang.String-">onStartGatherCallback</a></span>(int&nbsp;status,
                     java.lang.String&nbsp;message)</code>
<div class="block">开启采集回调接口</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/track/OnTrackLifecycleListener.html#onStartTrackCallback-int-java.lang.String-">onStartTrackCallback</a></span>(int&nbsp;status,
                    java.lang.String&nbsp;message)</code>
<div class="block">开启服务回调接口</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/track/OnTrackLifecycleListener.html#onStopGatherCallback-int-java.lang.String-">onStopGatherCallback</a></span>(int&nbsp;status,
                    java.lang.String&nbsp;message)</code>
<div class="block">停止采集回调接口</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/track/OnTrackLifecycleListener.html#onStopTrackCallback-int-java.lang.String-">onStopTrackCallback</a></span>(int&nbsp;status,
                   java.lang.String&nbsp;message)</code>
<div class="block">停止服务回调接口</div>
</td>
</tr>
</table>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="onBindServiceCallback-int-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onBindServiceCallback</h4>
<pre>void&nbsp;onBindServiceCallback(int&nbsp;status,
                           java.lang.String&nbsp;message)</pre>
<div class="block">绑定服务回调接口</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>status</code> - 结果错误码</dd>
<dd><code>message</code> - 结果描述</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>1.0</dd>
</dl>
</li>
</ul>
<a name="onStartGatherCallback-int-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onStartGatherCallback</h4>
<pre>void&nbsp;onStartGatherCallback(int&nbsp;status,
                           java.lang.String&nbsp;message)</pre>
<div class="block">开启采集回调接口</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>status</code> - 结果错误码</dd>
<dd><code>message</code> - 结果描述</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>1.0</dd>
</dl>
</li>
</ul>
<a name="onStartTrackCallback-int-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onStartTrackCallback</h4>
<pre>void&nbsp;onStartTrackCallback(int&nbsp;status,
                          java.lang.String&nbsp;message)</pre>
<div class="block">开启服务回调接口</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>status</code> - 结果错误码</dd>
<dd><code>message</code> - 结果描述</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>1.0</dd>
</dl>
</li>
</ul>
<a name="onStopGatherCallback-int-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onStopGatherCallback</h4>
<pre>void&nbsp;onStopGatherCallback(int&nbsp;status,
                          java.lang.String&nbsp;message)</pre>
<div class="block">停止采集回调接口</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>status</code> - 结果错误码</dd>
<dd><code>message</code> - 结果描述</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>1.0</dd>
</dl>
</li>
</ul>
<a name="onStopTrackCallback-int-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>onStopTrackCallback</h4>
<pre>void&nbsp;onStopTrackCallback(int&nbsp;status,
                         java.lang.String&nbsp;message)</pre>
<div class="block">停止服务回调接口</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>status</code> - 结果错误码</dd>
<dd><code>message</code> - 结果描述</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>1.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/OnTrackLifecycleListener.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/amap/api/track/OnCustomAttributeListener.html" title="com.amap.api.track中的接口"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../com/amap/api/track/TrackParam.html" title="com.amap.api.track中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/amap/api/track/OnTrackLifecycleListener.html" target="_top">框架</a></li>
<li><a href="OnTrackLifecycleListener.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
