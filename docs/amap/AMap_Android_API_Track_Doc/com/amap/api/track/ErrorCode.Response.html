<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>ErrorCode.Response</title>
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ErrorCode.Response";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/ErrorCode.Response.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/amap/api/track/ErrorCode.html" title="com.amap.api.track中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../com/amap/api/track/ErrorCode.TrackListen.html" title="com.amap.api.track中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/amap/api/track/ErrorCode.Response.html" target="_top">框架</a></li>
<li><a href="ErrorCode.Response.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li><a href="#field.summary">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#methods.inherited.from.class.java.lang.Object">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li><a href="#field.detail">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li>方法</li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.amap.api.track</div>
<h2 title="类 ErrorCode.Response" class="title">类 ErrorCode.Response</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.amap.api.track.ErrorCode.Response</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>封闭类:</dt>
<dd><a href="../../../../com/amap/api/track/ErrorCode.html" title="com.amap.api.track中的类">ErrorCode</a></dd>
</dl>
<hr>
<br>
<pre>public static class <span class="typeNameLabel">ErrorCode.Response</span>
extends java.lang.Object</pre>
<div class="block">定义查询请求相关的错误码</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>1.0</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>字段概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="字段概要表, 列表字段和解释">
<caption><span>字段</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">字段和说明</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/track/ErrorCode.Response.html#BAD_NETWORK">BAD_NETWORK</a></span></code>
<div class="block">请求失败，网络未链接</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/track/ErrorCode.Response.html#BEYOND_LIMIT">BEYOND_LIMIT</a></span></code>
<div class="block">超出限制的个数</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/track/ErrorCode.Response.html#NET_RES_FAIL">NET_RES_FAIL</a></span></code>
<div class="block">请求失败，网络请求失败</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/track/ErrorCode.Response.html#PARAM_ERROR_CODE">PARAM_ERROR_CODE</a></span></code>
<div class="block">请求失败，参数错误</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/track/ErrorCode.Response.html#REQ_NOT_VALID">REQ_NOT_VALID</a></span></code>
<div class="block">请求失败，请求参数为空</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/track/ErrorCode.Response.html#SERVICE_NON_EXIST">SERVICE_NON_EXIST</a></span></code>
<div class="block">Service 不存在</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/track/ErrorCode.Response.html#SUCCESS">SUCCESS</a></span></code>
<div class="block">成功</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/track/ErrorCode.Response.html#TERMINAL_ALREADY_EXIST">TERMINAL_ALREADY_EXIST</a></span></code>
<div class="block">填入的terminal已经存在</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/track/ErrorCode.Response.html#TERMINAL_CREATE_FAIL">TERMINAL_CREATE_FAIL</a></span></code>
<div class="block">terminal 创建失败</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/track/ErrorCode.Response.html#TERMINAL_NON_EXIST">TERMINAL_NON_EXIST</a></span></code>
<div class="block">Terminal 不存在</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/track/ErrorCode.Response.html#TRID_NON_EXIST">TRID_NON_EXIST</a></span></code>
<div class="block">轨迹id不存在</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/amap/api/track/ErrorCode.Response.html#Response--">Response</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>字段详细资料</h3>
<a name="PARAM_ERROR_CODE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PARAM_ERROR_CODE</h4>
<pre>public static final&nbsp;int PARAM_ERROR_CODE</pre>
<div class="block">请求失败，参数错误</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>1.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.track.ErrorCode.Response.PARAM_ERROR_CODE">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="BAD_NETWORK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BAD_NETWORK</h4>
<pre>public static final&nbsp;int BAD_NETWORK</pre>
<div class="block">请求失败，网络未链接</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>1.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.track.ErrorCode.Response.BAD_NETWORK">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="NET_RES_FAIL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NET_RES_FAIL</h4>
<pre>public static final&nbsp;int NET_RES_FAIL</pre>
<div class="block">请求失败，网络请求失败</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>1.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.track.ErrorCode.Response.NET_RES_FAIL">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="REQ_NOT_VALID">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>REQ_NOT_VALID</h4>
<pre>public static final&nbsp;int REQ_NOT_VALID</pre>
<div class="block">请求失败，请求参数为空</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>1.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.track.ErrorCode.Response.REQ_NOT_VALID">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="SUCCESS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SUCCESS</h4>
<pre>public static final&nbsp;int SUCCESS</pre>
<div class="block">成功</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>1.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.track.ErrorCode.Response.SUCCESS">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="TERMINAL_ALREADY_EXIST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TERMINAL_ALREADY_EXIST</h4>
<pre>public static final&nbsp;int TERMINAL_ALREADY_EXIST</pre>
<div class="block">填入的terminal已经存在</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>1.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.track.ErrorCode.Response.TERMINAL_ALREADY_EXIST">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="SERVICE_NON_EXIST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SERVICE_NON_EXIST</h4>
<pre>public static final&nbsp;int SERVICE_NON_EXIST</pre>
<div class="block">Service 不存在</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>1.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.track.ErrorCode.Response.SERVICE_NON_EXIST">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="TERMINAL_NON_EXIST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TERMINAL_NON_EXIST</h4>
<pre>public static final&nbsp;int TERMINAL_NON_EXIST</pre>
<div class="block">Terminal 不存在</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>1.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.track.ErrorCode.Response.TERMINAL_NON_EXIST">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="TRID_NON_EXIST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TRID_NON_EXIST</h4>
<pre>public static final&nbsp;int TRID_NON_EXIST</pre>
<div class="block">轨迹id不存在</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>1.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.track.ErrorCode.Response.TRID_NON_EXIST">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="TERMINAL_CREATE_FAIL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TERMINAL_CREATE_FAIL</h4>
<pre>public static final&nbsp;int TERMINAL_CREATE_FAIL</pre>
<div class="block">terminal 创建失败</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>1.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.track.ErrorCode.Response.TERMINAL_CREATE_FAIL">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="BEYOND_LIMIT">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>BEYOND_LIMIT</h4>
<pre>public static final&nbsp;int BEYOND_LIMIT</pre>
<div class="block">超出限制的个数</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>1.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.track.ErrorCode.Response.BEYOND_LIMIT">常量字段值</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="Response--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>Response</h4>
<pre>public&nbsp;Response()</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/ErrorCode.Response.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/amap/api/track/ErrorCode.html" title="com.amap.api.track中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../com/amap/api/track/ErrorCode.TrackListen.html" title="com.amap.api.track中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/amap/api/track/ErrorCode.Response.html" target="_top">框架</a></li>
<li><a href="ErrorCode.Response.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li><a href="#field.summary">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#methods.inherited.from.class.java.lang.Object">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li><a href="#field.detail">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li>方法</li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
