<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>com.amap.api.track.query.model</title>
<link rel="stylesheet" type="text/css" href="../../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="com.amap.api.track.query.model";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../../overview-summary.html">概览</a></li>
<li class="navBarCell1Rev">程序包</li>
<li>类</li>
<li><a href="package-use.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../../com/amap/api/track/query/entity/package-summary.html">上一个程序包</a></li>
<li>下一个程序包</li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?com/amap/api/track/query/model/package-summary.html" target="_top">框架</a></li>
<li><a href="package-summary.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 title="程序包" class="title">程序包&nbsp;com.amap.api.track.query.model</h1>
<div class="docSummary">
<div class="block">定义接口请求和响应</div>
</div>
<p>请参阅:&nbsp;<a href="#package.description">说明</a></p>
</div>
<div class="contentContainer">
<ul class="blockList">
<li class="blockList">
<table class="typeSummary" border="0" cellpadding="3" cellspacing="0" summary="接口概要表, 列表接口和解释">
<caption><span>接口概要</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">接口</th>
<th class="colLast" scope="col">说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../../com/amap/api/track/query/model/OnTrackListener.html" title="com.amap.api.track.query.model中的接口">OnTrackListener</a></td>
<td class="colLast">
<div class="block">监听轨迹相关的查询、创建请求结果的监听器</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="typeSummary" border="0" cellpadding="3" cellspacing="0" summary="类概要表, 列表类和解释">
<caption><span>类概要</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">类</th>
<th class="colLast" scope="col">说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../../com/amap/api/track/query/model/AddTerminalRequest.html" title="com.amap.api.track.query.model中的类">AddTerminalRequest</a></td>
<td class="colLast">
<div class="block">创建新终端</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../../com/amap/api/track/query/model/AddTerminalResponse.html" title="com.amap.api.track.query.model中的类">AddTerminalResponse</a></td>
<td class="colLast">
<div class="block">创建终端请求返回结果</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../../com/amap/api/track/query/model/AddTrackRequest.html" title="com.amap.api.track.query.model中的类">AddTrackRequest</a></td>
<td class="colLast">
<div class="block">创建轨迹请求</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../../com/amap/api/track/query/model/AddTrackResponse.html" title="com.amap.api.track.query.model中的类">AddTrackResponse</a></td>
<td class="colLast">
<div class="block">创建轨迹请求的返回结果</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../../com/amap/api/track/query/model/BaseResponse.html" title="com.amap.api.track.query.model中的类">BaseResponse</a></td>
<td class="colLast">
<div class="block">所有请求返回的基类</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../../com/amap/api/track/query/model/DistanceRequest.html" title="com.amap.api.track.query.model中的类">DistanceRequest</a></td>
<td class="colLast">
<div class="block">查询终端某时间段内的行驶里程</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../../com/amap/api/track/query/model/DistanceResponse.html" title="com.amap.api.track.query.model中的类">DistanceResponse</a></td>
<td class="colLast">
<div class="block">查询终端行驶里程的返回结果</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../../com/amap/api/track/query/model/HistoryTrackRequest.html" title="com.amap.api.track.query.model中的类">HistoryTrackRequest</a></td>
<td class="colLast">
<div class="block">查询某个终端某段时间内的行驶轨迹及里程</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../../com/amap/api/track/query/model/HistoryTrackResponse.html" title="com.amap.api.track.query.model中的类">HistoryTrackResponse</a></td>
<td class="colLast">
<div class="block">查询终端行驶里程轨迹及里程的请求结果</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../../com/amap/api/track/query/model/LatestPointRequest.html" title="com.amap.api.track.query.model中的类">LatestPointRequest</a></td>
<td class="colLast">
<div class="block">实时查询某终端位置

 通过指定 service 和 terminal，返回该 terminal 最后定位的经纬度</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../../com/amap/api/track/query/model/LatestPointResponse.html" title="com.amap.api.track.query.model中的类">LatestPointResponse</a></td>
<td class="colLast">
<div class="block">实时查询终端位置返回结果</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../../com/amap/api/track/query/model/ParamErrorResponse.html" title="com.amap.api.track.query.model中的类">ParamErrorResponse</a></td>
<td class="colLast">
<div class="block">调用轨迹相关的查询请求或创建请求时，参数校验失败时返回的表示调用错误的结果</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../../com/amap/api/track/query/model/QueryTerminalRequest.html" title="com.amap.api.track.query.model中的类">QueryTerminalRequest</a></td>
<td class="colLast">
<div class="block">查询终端请求</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../../com/amap/api/track/query/model/QueryTerminalResponse.html" title="com.amap.api.track.query.model中的类">QueryTerminalResponse</a></td>
<td class="colLast">
<div class="block">查询终端请求的返回结果</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../../com/amap/api/track/query/model/QueryTrackRequest.html" title="com.amap.api.track.query.model中的类">QueryTrackRequest</a></td>
<td class="colLast">
<div class="block">查询某个终端的某几条轨迹信息
 <br>
 能够查询某个终端的某条轨迹，提供多种自定义参数查询方式。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../../com/amap/api/track/query/model/QueryTrackResponse.html" title="com.amap.api.track.query.model中的类">QueryTrackResponse</a></td>
<td class="colLast">
<div class="block">查询轨迹信息请求的返回结果</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
<a name="package.description">
<!--   -->
</a>
<h2 title="程序包com.amap.api.track.query.model的说明">程序包com.amap.api.track.query.model的说明</h2>
<div class="block">定义接口请求和响应</div>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../../overview-summary.html">概览</a></li>
<li class="navBarCell1Rev">程序包</li>
<li>类</li>
<li><a href="package-use.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../../com/amap/api/track/query/entity/package-summary.html">上一个程序包</a></li>
<li>下一个程序包</li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?com/amap/api/track/query/model/package-summary.html" target="_top">框架</a></li>
<li><a href="package-summary.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
