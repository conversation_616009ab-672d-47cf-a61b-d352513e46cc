<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>OnTrackListener</title>
<link rel="stylesheet" type="text/css" href="../../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="OnTrackListener";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6};
var tabs = {65535:["t0","所有方法"],2:["t2","实例方法"],4:["t3","抽象方法"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/OnTrackListener.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../../com/amap/api/track/query/model/LatestPointResponse.html" title="com.amap.api.track.query.model中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../../com/amap/api/track/query/model/ParamErrorResponse.html" title="com.amap.api.track.query.model中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?com/amap/api/track/query/model/OnTrackListener.html" target="_top">框架</a></li>
<li><a href="OnTrackListener.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.amap.api.track.query.model</div>
<h2 title="接口 OnTrackListener" class="title">接口 OnTrackListener</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public interface <span class="typeNameLabel">OnTrackListener</span></pre>
<div class="block">监听轨迹相关的查询、创建请求结果的监听器</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>1.0</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>所有方法</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">实例方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">抽象方法</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/track/query/model/OnTrackListener.html#onAddTrackCallback-com.amap.api.track.query.model.AddTrackResponse-">onAddTrackCallback</a></span>(<a href="../../../../../../com/amap/api/track/query/model/AddTrackResponse.html" title="com.amap.api.track.query.model中的类">AddTrackResponse</a>&nbsp;response)</code>
<div class="block">增加轨迹请求的回调</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/track/query/model/OnTrackListener.html#onCreateTerminalCallback-com.amap.api.track.query.model.AddTerminalResponse-">onCreateTerminalCallback</a></span>(<a href="../../../../../../com/amap/api/track/query/model/AddTerminalResponse.html" title="com.amap.api.track.query.model中的类">AddTerminalResponse</a>&nbsp;response)</code>
<div class="block">创建terminal的回调</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/track/query/model/OnTrackListener.html#onDistanceCallback-com.amap.api.track.query.model.DistanceResponse-">onDistanceCallback</a></span>(<a href="../../../../../../com/amap/api/track/query/model/DistanceResponse.html" title="com.amap.api.track.query.model中的类">DistanceResponse</a>&nbsp;response)</code>
<div class="block">查询里程回调</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/track/query/model/OnTrackListener.html#onHistoryTrackCallback-com.amap.api.track.query.model.HistoryTrackResponse-">onHistoryTrackCallback</a></span>(<a href="../../../../../../com/amap/api/track/query/model/HistoryTrackResponse.html" title="com.amap.api.track.query.model中的类">HistoryTrackResponse</a>&nbsp;response)</code>
<div class="block">查询历史轨迹的的回调</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/track/query/model/OnTrackListener.html#onLatestPointCallback-com.amap.api.track.query.model.LatestPointResponse-">onLatestPointCallback</a></span>(<a href="../../../../../../com/amap/api/track/query/model/LatestPointResponse.html" title="com.amap.api.track.query.model中的类">LatestPointResponse</a>&nbsp;response)</code>
<div class="block">查询终端最新轨迹点的回调</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/track/query/model/OnTrackListener.html#onParamErrorCallback-com.amap.api.track.query.model.ParamErrorResponse-">onParamErrorCallback</a></span>(<a href="../../../../../../com/amap/api/track/query/model/ParamErrorResponse.html" title="com.amap.api.track.query.model中的类">ParamErrorResponse</a>&nbsp;response)</code>
<div class="block">参数错误回调</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/track/query/model/OnTrackListener.html#onQueryTerminalCallback-com.amap.api.track.query.model.QueryTerminalResponse-">onQueryTerminalCallback</a></span>(<a href="../../../../../../com/amap/api/track/query/model/QueryTerminalResponse.html" title="com.amap.api.track.query.model中的类">QueryTerminalResponse</a>&nbsp;response)</code>
<div class="block">查询terminal的详细信息的回调</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/track/query/model/OnTrackListener.html#onQueryTrackCallback-com.amap.api.track.query.model.QueryTrackResponse-">onQueryTrackCallback</a></span>(<a href="../../../../../../com/amap/api/track/query/model/QueryTrackResponse.html" title="com.amap.api.track.query.model中的类">QueryTrackResponse</a>&nbsp;response)</code>
<div class="block">查询终端轨迹信息的回调</div>
</td>
</tr>
</table>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="onQueryTerminalCallback-com.amap.api.track.query.model.QueryTerminalResponse-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onQueryTerminalCallback</h4>
<pre>void&nbsp;onQueryTerminalCallback(<a href="../../../../../../com/amap/api/track/query/model/QueryTerminalResponse.html" title="com.amap.api.track.query.model中的类">QueryTerminalResponse</a>&nbsp;response)</pre>
<div class="block">查询terminal的详细信息的回调</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>response</code> - 查询到的terminal信息</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>1.0</dd>
</dl>
</li>
</ul>
<a name="onCreateTerminalCallback-com.amap.api.track.query.model.AddTerminalResponse-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onCreateTerminalCallback</h4>
<pre>void&nbsp;onCreateTerminalCallback(<a href="../../../../../../com/amap/api/track/query/model/AddTerminalResponse.html" title="com.amap.api.track.query.model中的类">AddTerminalResponse</a>&nbsp;response)</pre>
<div class="block">创建terminal的回调</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>response</code> - 成功创建的terminal信息</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>1.0</dd>
</dl>
</li>
</ul>
<a name="onDistanceCallback-com.amap.api.track.query.model.DistanceResponse-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onDistanceCallback</h4>
<pre>void&nbsp;onDistanceCallback(<a href="../../../../../../com/amap/api/track/query/model/DistanceResponse.html" title="com.amap.api.track.query.model中的类">DistanceResponse</a>&nbsp;response)</pre>
<div class="block">查询里程回调</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>response</code> - 查询到的里程信息</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>1.0</dd>
</dl>
</li>
</ul>
<a name="onLatestPointCallback-com.amap.api.track.query.model.LatestPointResponse-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onLatestPointCallback</h4>
<pre>void&nbsp;onLatestPointCallback(<a href="../../../../../../com/amap/api/track/query/model/LatestPointResponse.html" title="com.amap.api.track.query.model中的类">LatestPointResponse</a>&nbsp;response)</pre>
<div class="block">查询终端最新轨迹点的回调</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>response</code> - 查询到的终端最新轨迹点信息</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>1.0</dd>
</dl>
</li>
</ul>
<a name="onHistoryTrackCallback-com.amap.api.track.query.model.HistoryTrackResponse-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onHistoryTrackCallback</h4>
<pre>void&nbsp;onHistoryTrackCallback(<a href="../../../../../../com/amap/api/track/query/model/HistoryTrackResponse.html" title="com.amap.api.track.query.model中的类">HistoryTrackResponse</a>&nbsp;response)</pre>
<div class="block">查询历史轨迹的的回调</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>response</code> - 查询到的历史轨迹</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>1.0</dd>
</dl>
</li>
</ul>
<a name="onQueryTrackCallback-com.amap.api.track.query.model.QueryTrackResponse-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onQueryTrackCallback</h4>
<pre>void&nbsp;onQueryTrackCallback(<a href="../../../../../../com/amap/api/track/query/model/QueryTrackResponse.html" title="com.amap.api.track.query.model中的类">QueryTrackResponse</a>&nbsp;response)</pre>
<div class="block">查询终端轨迹信息的回调</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>response</code> - 查询到的轨迹信息</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>1.0</dd>
</dl>
</li>
</ul>
<a name="onAddTrackCallback-com.amap.api.track.query.model.AddTrackResponse-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onAddTrackCallback</h4>
<pre>void&nbsp;onAddTrackCallback(<a href="../../../../../../com/amap/api/track/query/model/AddTrackResponse.html" title="com.amap.api.track.query.model中的类">AddTrackResponse</a>&nbsp;response)</pre>
<div class="block">增加轨迹请求的回调</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>response</code> - 添加的轨迹信息</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>1.0</dd>
</dl>
</li>
</ul>
<a name="onParamErrorCallback-com.amap.api.track.query.model.ParamErrorResponse-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>onParamErrorCallback</h4>
<pre>void&nbsp;onParamErrorCallback(<a href="../../../../../../com/amap/api/track/query/model/ParamErrorResponse.html" title="com.amap.api.track.query.model中的类">ParamErrorResponse</a>&nbsp;response)</pre>
<div class="block">参数错误回调</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>response</code> - 错误详情</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>1.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/OnTrackListener.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../../com/amap/api/track/query/model/LatestPointResponse.html" title="com.amap.api.track.query.model中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../../com/amap/api/track/query/model/ParamErrorResponse.html" title="com.amap.api.track.query.model中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?com/amap/api/track/query/model/OnTrackListener.html" target="_top">框架</a></li>
<li><a href="OnTrackListener.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
