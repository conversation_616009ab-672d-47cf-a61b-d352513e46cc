<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>LatestPointRequest</title>
<link rel="stylesheet" type="text/css" href="../../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="LatestPointRequest";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/LatestPointRequest.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../../com/amap/api/track/query/model/HistoryTrackResponse.html" title="com.amap.api.track.query.model中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../../com/amap/api/track/query/model/LatestPointResponse.html" title="com.amap.api.track.query.model中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?com/amap/api/track/query/model/LatestPointRequest.html" target="_top">框架</a></li>
<li><a href="LatestPointRequest.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#methods.inherited.from.class.java.lang.Object">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li>方法</li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.amap.api.track.query.model</div>
<h2 title="类 LatestPointRequest" class="title">类 LatestPointRequest</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>BaseRequest</li>
<li>
<ul class="inheritance">
<li>com.amap.api.track.query.model.LatestPointRequest</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public final class <span class="typeNameLabel">LatestPointRequest</span>
extends BaseRequest</pre>
<div class="block"><p>实时查询某终端位置</p>

 <p>通过指定 service 和 terminal，返回该 terminal 最后定位的经纬度</p></div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>1.0</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/track/query/model/LatestPointRequest.html#LatestPointRequest-long-long-">LatestPointRequest</a></span>(long&nbsp;sid,
                  long&nbsp;tid)</code>
<div class="block">构造请求
 <br>
 查询终端最后一次定位的经纬度</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/track/query/model/LatestPointRequest.html#LatestPointRequest-long-long-long-">LatestPointRequest</a></span>(long&nbsp;sid,
                  long&nbsp;tid,
                  long&nbsp;trid)</code>
<div class="block">构造请求
 <br>
 查询终端的某个特定轨迹下最后上报的轨迹点，若将轨迹id传-1可查询所有轨迹中最后一次上报的轨迹点</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/track/query/model/LatestPointRequest.html#LatestPointRequest-long-long-long-int-java.lang.String-">LatestPointRequest</a></span>(long&nbsp;sid,
                  long&nbsp;tid,
                  long&nbsp;trid,
                  int&nbsp;correction,
                  java.lang.String&nbsp;accuracy)</code>
<div class="block">构造请求
 <br>
 查询终端的某个特定轨迹下最后上报的轨迹点，若将轨迹id传-1可查询所有轨迹中最后一次上报的轨迹点</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="LatestPointRequest-long-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LatestPointRequest</h4>
<pre>public&nbsp;LatestPointRequest(long&nbsp;sid,
                          long&nbsp;tid)</pre>
<div class="block">构造请求
 <br>
 查询终端最后一次定位的经纬度</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>sid</code> - 服务id</dd>
<dd><code>tid</code> - 终端id</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>1.0</dd>
</dl>
</li>
</ul>
<a name="LatestPointRequest-long-long-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LatestPointRequest</h4>
<pre>public&nbsp;LatestPointRequest(long&nbsp;sid,
                          long&nbsp;tid,
                          long&nbsp;trid)</pre>
<div class="block">构造请求
 <br>
 查询终端的某个特定轨迹下最后上报的轨迹点，若将轨迹id传-1可查询所有轨迹中最后一次上报的轨迹点</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>sid</code> - 服务id</dd>
<dd><code>tid</code> - 终端id</dd>
<dd><code>trid</code> - 轨迹id</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>1.0</dd>
</dl>
</li>
</ul>
<a name="LatestPointRequest-long-long-long-int-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>LatestPointRequest</h4>
<pre>public&nbsp;LatestPointRequest(long&nbsp;sid,
                          long&nbsp;tid,
                          long&nbsp;trid,
                          int&nbsp;correction,
                          java.lang.String&nbsp;accuracy)</pre>
<div class="block">构造请求
 <br>
 查询终端的某个特定轨迹下最后上报的轨迹点，若将轨迹id传-1可查询所有轨迹中最后一次上报的轨迹点</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>sid</code> - 服务id</dd>
<dd><code>tid</code> - 终端id</dd>
<dd><code>trid</code> - 轨迹id</dd>
<dd><code>correction</code> - 是否纠偏，取值参考<a href="../../../../../../com/amap/api/track/query/entity/CorrectMode.html" title="com.amap.api.track.query.entity中的类"><code>CorrectMode</code></a></dd>
<dd><code>accuracy</code> - 定位精度过滤，该请求暂未支持该参数，可传null</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>1.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/LatestPointRequest.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../../com/amap/api/track/query/model/HistoryTrackResponse.html" title="com.amap.api.track.query.model中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../../com/amap/api/track/query/model/LatestPointResponse.html" title="com.amap.api.track.query.model中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?com/amap/api/track/query/model/LatestPointRequest.html" target="_top">框架</a></li>
<li><a href="LatestPointRequest.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#methods.inherited.from.class.java.lang.Object">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li>方法</li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
