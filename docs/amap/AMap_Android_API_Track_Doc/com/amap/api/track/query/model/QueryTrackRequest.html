<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>QueryTrackRequest</title>
<link rel="stylesheet" type="text/css" href="../../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="QueryTrackRequest";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/QueryTrackRequest.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../../com/amap/api/track/query/model/QueryTerminalResponse.html" title="com.amap.api.track.query.model中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../../com/amap/api/track/query/model/QueryTrackResponse.html" title="com.amap.api.track.query.model中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?com/amap/api/track/query/model/QueryTrackRequest.html" target="_top">框架</a></li>
<li><a href="QueryTrackRequest.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#methods.inherited.from.class.java.lang.Object">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li>方法</li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.amap.api.track.query.model</div>
<h2 title="类 QueryTrackRequest" class="title">类 QueryTrackRequest</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>BaseRequest</li>
<li>
<ul class="inheritance">
<li>com.amap.api.track.query.model.QueryTrackRequest</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public final class <span class="typeNameLabel">QueryTrackRequest</span>
extends BaseRequest</pre>
<div class="block">查询某个终端的某几条轨迹信息
 <br>
 能够查询某个终端的某条轨迹，提供多种自定义参数查询方式。
 <br>
 1、 id 查询轨迹信息:以轨迹 id 查询轨迹信息，最多支持查询 1 条轨迹;
 <br>
 2、 时间段查询轨迹信息:传递设备 id，查询在一定时间内的(最大时间检索跨度为 24h)开始记录的所有设备轨迹;
 <br>
 3、 分段查询轨迹信息:可以分段查询指定的一条轨迹(通过 trid 指定轨迹)，设置查询的时间间隔(通过 starttime 和endtime)
 用于轨迹的分段。在分段查询中，trid、 starttime、endtime 均为必填信息。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>1.0</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/track/query/model/QueryTrackRequest.html#QueryTrackRequest-long-long-long-long-">QueryTrackRequest</a></span>(long&nbsp;sid,
                 long&nbsp;tid,
                 long&nbsp;startTime,
                 long&nbsp;endTime)</code>
<div class="block">构造查询请求</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/track/query/model/QueryTrackRequest.html#QueryTrackRequest-long-long-long-long-long-">QueryTrackRequest</a></span>(long&nbsp;sid,
                 long&nbsp;tid,
                 long&nbsp;trid,
                 long&nbsp;startTime,
                 long&nbsp;endTime)</code>
<div class="block">构造查询请求</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/track/query/model/QueryTrackRequest.html#QueryTrackRequest-long-long-long-long-long-int-int-int-int-int-int-int-int-int-">QueryTrackRequest</a></span>(long&nbsp;sid,
                 long&nbsp;tid,
                 long&nbsp;trid,
                 long&nbsp;startTime,
                 long&nbsp;endTime,
                 int&nbsp;denoise,
                 int&nbsp;mapmatch,
                 int&nbsp;threshold,
                 int&nbsp;drivemode,
                 int&nbsp;recoup,
                 int&nbsp;gap,
                 int&nbsp;ispoints,
                 int&nbsp;page,
                 int&nbsp;pageSize)</code>
<div class="block">构造查询请求</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="QueryTrackRequest-long-long-long-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>QueryTrackRequest</h4>
<pre>public&nbsp;QueryTrackRequest(long&nbsp;sid,
                         long&nbsp;tid,
                         long&nbsp;startTime,
                         long&nbsp;endTime)</pre>
<div class="block">构造查询请求</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>sid</code> - 服务id</dd>
<dd><code>tid</code> - 终端id</dd>
<dd><code>startTime</code> - 开始时间，unix时间戳，单位为毫秒</dd>
<dd><code>endTime</code> - 结束时间，unix时间戳，单位为毫秒，注意，结束时间不能大于当前时间，且距离开始时间不能超过24小时</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>1.0</dd>
</dl>
</li>
</ul>
<a name="QueryTrackRequest-long-long-long-long-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>QueryTrackRequest</h4>
<pre>public&nbsp;QueryTrackRequest(long&nbsp;sid,
                         long&nbsp;tid,
                         long&nbsp;trid,
                         long&nbsp;startTime,
                         long&nbsp;endTime)</pre>
<div class="block">构造查询请求</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>sid</code> - 服务id</dd>
<dd><code>tid</code> - 终端id</dd>
<dd><code>trid</code> - 轨迹id</dd>
<dd><code>startTime</code> - 开始时间，unix时间戳，单位为毫秒</dd>
<dd><code>endTime</code> - 结束时间，unix时间戳，单位为毫秒，注意，结束时间不能大于当前时间，且距离开始时间不能超过24小时</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>1.0</dd>
</dl>
</li>
</ul>
<a name="QueryTrackRequest-long-long-long-long-long-int-int-int-int-int-int-int-int-int-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>QueryTrackRequest</h4>
<pre>public&nbsp;QueryTrackRequest(long&nbsp;sid,
                         long&nbsp;tid,
                         long&nbsp;trid,
                         long&nbsp;startTime,
                         long&nbsp;endTime,
                         int&nbsp;denoise,
                         int&nbsp;mapmatch,
                         int&nbsp;threshold,
                         int&nbsp;drivemode,
                         int&nbsp;recoup,
                         int&nbsp;gap,
                         int&nbsp;ispoints,
                         int&nbsp;page,
                         int&nbsp;pageSize)</pre>
<div class="block">构造查询请求</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>sid</code> - 服务id</dd>
<dd><code>tid</code> - 终端id</dd>
<dd><code>trid</code> - 轨迹id</dd>
<dd><code>startTime</code> - 开始时间，unix时间戳，单位为毫秒</dd>
<dd><code>endTime</code> - 结束时间，unix时间戳，单位为毫秒，注意，结束时间不能大于当前时间，且距离开始时间不能超过24小时</dd>
<dd><code>denoise</code> - 是否去噪，取值参考<a href="../../../../../../com/amap/api/track/query/entity/DenoiseMode.html" title="com.amap.api.track.query.entity中的类"><code>DenoiseMode</code></a></dd>
<dd><code>mapmatch</code> - 是否绑路，取值参考<a href="../../../../../../com/amap/api/track/query/entity/MapMatchMode.html" title="com.amap.api.track.query.entity中的类"><code>MapMatchMode</code></a></dd>
<dd><code>threshold</code> - 是否进行定位精度过滤，过滤掉定位精度较差的轨迹点，取值参考<a href="../../../../../../com/amap/api/track/query/entity/ThresholdMode.html" title="com.amap.api.track.query.entity中的类"><code>ThresholdMode</code></a></dd>
<dd><code>drivemode</code> - 交通方式，该参数决定纠偏策略，目前仅支持驾车模式，取值参考<a href="../../../../../../com/amap/api/track/query/entity/DriveMode.html" title="com.amap.api.track.query.entity中的类"><code>DriveMode</code></a></dd>
<dd><code>recoup</code> - 是否进行距离补偿，可取值参考<a href="../../../../../../com/amap/api/track/query/entity/RecoupMode.html" title="com.amap.api.track.query.entity中的类"><code>RecoupMode</code></a></dd>
<dd><code>gap</code> - 距离补偿生效的点间距，单位为米，范围必须在50m~10km，当两点间距离超过该值时，将启用距离补偿计算两点
            间距离</dd>
<dd><code>ispoints</code> - 是否返回轨迹包含的轨迹点内容，1表示返回，0表示不返回。分页当前仅对查询单条轨迹生效</dd>
<dd><code>page</code> - 第几页数据</dd>
<dd><code>pageSize</code> - 每页返回点的个数，该值必须小于1000</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>1.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/QueryTrackRequest.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../../com/amap/api/track/query/model/QueryTerminalResponse.html" title="com.amap.api.track.query.model中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../../com/amap/api/track/query/model/QueryTrackResponse.html" title="com.amap.api.track.query.model中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?com/amap/api/track/query/model/QueryTrackRequest.html" target="_top">框架</a></li>
<li><a href="QueryTrackRequest.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#methods.inherited.from.class.java.lang.Object">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li>方法</li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
