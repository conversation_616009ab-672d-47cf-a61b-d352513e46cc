<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>com.amap.api.track.query.entity 类分层结构</title>
<link rel="stylesheet" type="text/css" href="../../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="com.amap.api.track.query.entity \u7C7B\u5206\u5C42\u7ED3\u6784";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li>类</li>
<li>使用</li>
<li class="navBarCell1Rev">树</li>
<li><a href="../../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../../com/amap/api/track/package-tree.html">上一个</a></li>
<li><a href="../../../../../../com/amap/api/track/query/model/package-tree.html">下一个</a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?com/amap/api/track/query/entity/package-tree.html" target="_top">框架</a></li>
<li><a href="package-tree.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 class="title">程序包com.amap.api.track.query.entity的分层结构</h1>
<span class="packageHierarchyLabel">程序包分层结构:</span>
<ul class="horizontal">
<li><a href="../../../../../../overview-tree.html">所有程序包</a></li>
</ul>
</div>
<div class="contentContainer">
<h2 title="类分层结构">类分层结构</h2>
<ul>
<li type="circle">java.lang.Object
<ul>
<li type="circle">com.amap.api.track.query.entity.<a href="../../../../../../com/amap/api/track/query/entity/AccuracyMode.html" title="com.amap.api.track.query.entity中的类"><span class="typeNameLink">AccuracyMode</span></a></li>
<li type="circle">com.amap.api.track.query.entity.<a href="../../../../../../com/amap/api/track/query/entity/CorrectMode.html" title="com.amap.api.track.query.entity中的类"><span class="typeNameLink">CorrectMode</span></a></li>
<li type="circle">com.amap.api.track.query.entity.<a href="../../../../../../com/amap/api/track/query/entity/DenoiseMode.html" title="com.amap.api.track.query.entity中的类"><span class="typeNameLink">DenoiseMode</span></a></li>
<li type="circle">com.amap.api.track.query.entity.<a href="../../../../../../com/amap/api/track/query/entity/DriveMode.html" title="com.amap.api.track.query.entity中的类"><span class="typeNameLink">DriveMode</span></a></li>
<li type="circle">com.amap.api.track.query.entity.<a href="../../../../../../com/amap/api/track/query/entity/HistoryTrack.html" title="com.amap.api.track.query.entity中的类"><span class="typeNameLink">HistoryTrack</span></a></li>
<li type="circle">com.amap.api.track.query.entity.<a href="../../../../../../com/amap/api/track/query/entity/LatestPoint.html" title="com.amap.api.track.query.entity中的类"><span class="typeNameLink">LatestPoint</span></a></li>
<li type="circle">com.amap.api.track.query.entity.<a href="../../../../../../com/amap/api/track/query/entity/LocationMode.html" title="com.amap.api.track.query.entity中的类"><span class="typeNameLink">LocationMode</span></a></li>
<li type="circle">com.amap.api.track.query.entity.<a href="../../../../../../com/amap/api/track/query/entity/MapMatchMode.html" title="com.amap.api.track.query.entity中的类"><span class="typeNameLink">MapMatchMode</span></a></li>
<li type="circle">com.amap.api.track.query.entity.<a href="../../../../../../com/amap/api/track/query/entity/OrderMode.html" title="com.amap.api.track.query.entity中的类"><span class="typeNameLink">OrderMode</span></a></li>
<li type="circle">com.amap.api.track.query.entity.<a href="../../../../../../com/amap/api/track/query/entity/Point.html" title="com.amap.api.track.query.entity中的类"><span class="typeNameLink">Point</span></a></li>
<li type="circle">com.amap.api.track.query.entity.<a href="../../../../../../com/amap/api/track/query/entity/ProtocolType.html" title="com.amap.api.track.query.entity中的类"><span class="typeNameLink">ProtocolType</span></a></li>
<li type="circle">com.amap.api.track.query.entity.<a href="../../../../../../com/amap/api/track/query/entity/RecoupMode.html" title="com.amap.api.track.query.entity中的类"><span class="typeNameLink">RecoupMode</span></a></li>
<li type="circle">com.amap.api.track.query.entity.<a href="../../../../../../com/amap/api/track/query/entity/ThresholdMode.html" title="com.amap.api.track.query.entity中的类"><span class="typeNameLink">ThresholdMode</span></a></li>
<li type="circle">com.amap.api.track.query.entity.<a href="../../../../../../com/amap/api/track/query/entity/Track.html" title="com.amap.api.track.query.entity中的类"><span class="typeNameLink">Track</span></a></li>
<li type="circle">com.amap.api.track.query.entity.<a href="../../../../../../com/amap/api/track/query/entity/TrackPoint.html" title="com.amap.api.track.query.entity中的类"><span class="typeNameLink">TrackPoint</span></a></li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li>类</li>
<li>使用</li>
<li class="navBarCell1Rev">树</li>
<li><a href="../../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../../com/amap/api/track/package-tree.html">上一个</a></li>
<li><a href="../../../../../../com/amap/api/track/query/model/package-tree.html">下一个</a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?com/amap/api/track/query/entity/package-tree.html" target="_top">框架</a></li>
<li><a href="package-tree.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
