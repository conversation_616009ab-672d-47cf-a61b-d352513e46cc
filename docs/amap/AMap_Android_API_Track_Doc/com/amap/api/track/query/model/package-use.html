<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>程序包 com.amap.api.track.query.model的使用</title>
<link rel="stylesheet" type="text/css" href="../../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="\u7A0B\u5E8F\u5305 com.amap.api.track.query.model\u7684\u4F7F\u7528";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li>类</li>
<li class="navBarCell1Rev">使用</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个</li>
<li>下一个</li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?com/amap/api/track/query/model/package-use.html" target="_top">框架</a></li>
<li><a href="package-use.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 title="程序包的使用 com.amap.api.track.query.model" class="title">程序包的使用<br>com.amap.api.track.query.model</h1>
</div>
<div class="contentContainer">
<ul class="blockList">
<li class="blockList">
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表程序包和解释">
<caption><span>使用<a href="../../../../../../com/amap/api/track/query/model/package-summary.html">com.amap.api.track.query.model</a>的程序包</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">程序包</th>
<th class="colLast" scope="col">说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="#com.amap.api.track">com.amap.api.track</a></td>
<td class="colLast">
<div class="block">猎鹰sdk功能入口</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.amap.api.track.query.model">com.amap.api.track.query.model</a></td>
<td class="colLast">
<div class="block">定义接口请求和响应</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.amap.api.track">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表类和解释">
<caption><span><a href="../../../../../../com/amap/api/track/package-summary.html">com.amap.api.track</a>使用的<a href="../../../../../../com/amap/api/track/query/model/package-summary.html">com.amap.api.track.query.model</a>中的类</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">类和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../../../com/amap/api/track/query/model/class-use/AddTerminalRequest.html#com.amap.api.track">AddTerminalRequest</a>
<div class="block">创建新终端</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../../com/amap/api/track/query/model/class-use/AddTrackRequest.html#com.amap.api.track">AddTrackRequest</a>
<div class="block">创建轨迹请求</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../../com/amap/api/track/query/model/class-use/DistanceRequest.html#com.amap.api.track">DistanceRequest</a>
<div class="block">查询终端某时间段内的行驶里程</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../../com/amap/api/track/query/model/class-use/HistoryTrackRequest.html#com.amap.api.track">HistoryTrackRequest</a>
<div class="block">查询某个终端某段时间内的行驶轨迹及里程</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../../com/amap/api/track/query/model/class-use/LatestPointRequest.html#com.amap.api.track">LatestPointRequest</a>
<div class="block">实时查询某终端位置

 通过指定 service 和 terminal，返回该 terminal 最后定位的经纬度</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../../com/amap/api/track/query/model/class-use/OnTrackListener.html#com.amap.api.track">OnTrackListener</a>
<div class="block">监听轨迹相关的查询、创建请求结果的监听器</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../../com/amap/api/track/query/model/class-use/QueryTerminalRequest.html#com.amap.api.track">QueryTerminalRequest</a>
<div class="block">查询终端请求</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../../com/amap/api/track/query/model/class-use/QueryTrackRequest.html#com.amap.api.track">QueryTrackRequest</a>
<div class="block">查询某个终端的某几条轨迹信息
 <br>
 能够查询某个终端的某条轨迹，提供多种自定义参数查询方式。</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.amap.api.track.query.model">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表类和解释">
<caption><span><a href="../../../../../../com/amap/api/track/query/model/package-summary.html">com.amap.api.track.query.model</a>使用的<a href="../../../../../../com/amap/api/track/query/model/package-summary.html">com.amap.api.track.query.model</a>中的类</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">类和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../../../com/amap/api/track/query/model/class-use/AddTerminalResponse.html#com.amap.api.track.query.model">AddTerminalResponse</a>
<div class="block">创建终端请求返回结果</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../../com/amap/api/track/query/model/class-use/AddTrackResponse.html#com.amap.api.track.query.model">AddTrackResponse</a>
<div class="block">创建轨迹请求的返回结果</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../../com/amap/api/track/query/model/class-use/BaseResponse.html#com.amap.api.track.query.model">BaseResponse</a>
<div class="block">所有请求返回的基类</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../../com/amap/api/track/query/model/class-use/DistanceResponse.html#com.amap.api.track.query.model">DistanceResponse</a>
<div class="block">查询终端行驶里程的返回结果</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../../com/amap/api/track/query/model/class-use/HistoryTrackResponse.html#com.amap.api.track.query.model">HistoryTrackResponse</a>
<div class="block">查询终端行驶里程轨迹及里程的请求结果</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../../com/amap/api/track/query/model/class-use/LatestPointResponse.html#com.amap.api.track.query.model">LatestPointResponse</a>
<div class="block">实时查询终端位置返回结果</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../../com/amap/api/track/query/model/class-use/ParamErrorResponse.html#com.amap.api.track.query.model">ParamErrorResponse</a>
<div class="block">调用轨迹相关的查询请求或创建请求时，参数校验失败时返回的表示调用错误的结果</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../../com/amap/api/track/query/model/class-use/QueryTerminalResponse.html#com.amap.api.track.query.model">QueryTerminalResponse</a>
<div class="block">查询终端请求的返回结果</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../../com/amap/api/track/query/model/class-use/QueryTrackResponse.html#com.amap.api.track.query.model">QueryTrackResponse</a>
<div class="block">查询轨迹信息请求的返回结果</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li>类</li>
<li class="navBarCell1Rev">使用</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个</li>
<li>下一个</li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?com/amap/api/track/query/model/package-use.html" target="_top">框架</a></li>
<li><a href="package-use.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
