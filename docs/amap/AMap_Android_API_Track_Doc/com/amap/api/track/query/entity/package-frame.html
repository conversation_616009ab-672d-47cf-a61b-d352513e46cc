<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>com.amap.api.track.query.entity</title>
<link rel="stylesheet" type="text/css" href="../../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../../script.js"></script>
</head>
<body>
<h1 class="bar"><a href="../../../../../../com/amap/api/track/query/entity/package-summary.html" target="classFrame">com.amap.api.track.query.entity</a></h1>
<div class="indexContainer">
<h2 title="类">类</h2>
<ul title="类">
<li><a href="AccuracyMode.html" title="com.amap.api.track.query.entity中的类" target="classFrame">AccuracyMode</a></li>
<li><a href="CorrectMode.html" title="com.amap.api.track.query.entity中的类" target="classFrame">CorrectMode</a></li>
<li><a href="DenoiseMode.html" title="com.amap.api.track.query.entity中的类" target="classFrame">DenoiseMode</a></li>
<li><a href="DriveMode.html" title="com.amap.api.track.query.entity中的类" target="classFrame">DriveMode</a></li>
<li><a href="HistoryTrack.html" title="com.amap.api.track.query.entity中的类" target="classFrame">HistoryTrack</a></li>
<li><a href="LatestPoint.html" title="com.amap.api.track.query.entity中的类" target="classFrame">LatestPoint</a></li>
<li><a href="LocationMode.html" title="com.amap.api.track.query.entity中的类" target="classFrame">LocationMode</a></li>
<li><a href="MapMatchMode.html" title="com.amap.api.track.query.entity中的类" target="classFrame">MapMatchMode</a></li>
<li><a href="OrderMode.html" title="com.amap.api.track.query.entity中的类" target="classFrame">OrderMode</a></li>
<li><a href="Point.html" title="com.amap.api.track.query.entity中的类" target="classFrame">Point</a></li>
<li><a href="ProtocolType.html" title="com.amap.api.track.query.entity中的类" target="classFrame">ProtocolType</a></li>
<li><a href="RecoupMode.html" title="com.amap.api.track.query.entity中的类" target="classFrame">RecoupMode</a></li>
<li><a href="ThresholdMode.html" title="com.amap.api.track.query.entity中的类" target="classFrame">ThresholdMode</a></li>
<li><a href="Track.html" title="com.amap.api.track.query.entity中的类" target="classFrame">Track</a></li>
<li><a href="TrackPoint.html" title="com.amap.api.track.query.entity中的类" target="classFrame">TrackPoint</a></li>
</ul>
</div>
</body>
</html>
