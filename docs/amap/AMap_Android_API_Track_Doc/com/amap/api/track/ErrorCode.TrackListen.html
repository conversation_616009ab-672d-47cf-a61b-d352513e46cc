<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>ErrorCode.TrackListen</title>
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ErrorCode.TrackListen";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/ErrorCode.TrackListen.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/amap/api/track/ErrorCode.Response.html" title="com.amap.api.track中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../com/amap/api/track/OnCustomAttributeListener.html" title="com.amap.api.track中的接口"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/amap/api/track/ErrorCode.TrackListen.html" target="_top">框架</a></li>
<li><a href="ErrorCode.TrackListen.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li><a href="#field.summary">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#methods.inherited.from.class.java.lang.Object">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li><a href="#field.detail">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li>方法</li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.amap.api.track</div>
<h2 title="类 ErrorCode.TrackListen" class="title">类 ErrorCode.TrackListen</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.amap.api.track.ErrorCode.TrackListen</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>封闭类:</dt>
<dd><a href="../../../../com/amap/api/track/ErrorCode.html" title="com.amap.api.track中的类">ErrorCode</a></dd>
</dl>
<hr>
<br>
<pre>public static class <span class="typeNameLabel">ErrorCode.TrackListen</span>
extends java.lang.Object</pre>
<div class="block">定义轨迹上报和定位采集启停相关的错误码</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>1.0</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>字段概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="字段概要表, 列表字段和解释">
<caption><span>字段</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">字段和说明</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/track/ErrorCode.TrackListen.html#BIND_SUCCESS">BIND_SUCCESS</a></span></code>
<div class="block">绑定寻迹服务成功</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/track/ErrorCode.TrackListen.html#REMOTE_EX">REMOTE_EX</a></span></code>
<div class="block">寻迹服务异常</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/track/ErrorCode.TrackListen.html#SERVICE_NOT_STARTED">SERVICE_NOT_STARTED</a></span></code>
<div class="block">寻迹服务未启动，请先启动</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/track/ErrorCode.TrackListen.html#START_GATHER_ALREADY_STARTED">START_GATHER_ALREADY_STARTED</a></span></code>
<div class="block">定位采集 已经启动</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/track/ErrorCode.TrackListen.html#START_GATHER_SUCEE">START_GATHER_SUCEE</a></span></code>
<div class="block">定位采集 启动成功</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/track/ErrorCode.TrackListen.html#START_GATHER_TRACK_NOT_STARTED">START_GATHER_TRACK_NOT_STARTED</a></span></code>
<div class="block">轨迹同步 未启动</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/track/ErrorCode.TrackListen.html#START_SERVICE_EX">START_SERVICE_EX</a></span></code>
<div class="block">启动寻迹服务失败，寻迹服务启动出现异常</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/track/ErrorCode.TrackListen.html#START_TRACK_ALREADY_STARTED">START_TRACK_ALREADY_STARTED</a></span></code>
<div class="block">轨迹同步已经启动</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/track/ErrorCode.TrackListen.html#START_TRACK_AUTH_CHECK_FAIL">START_TRACK_AUTH_CHECK_FAIL</a></span></code>
<div class="block">鉴权失败</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/track/ErrorCode.TrackListen.html#START_TRACK_CREATE_TERMINAL_FAIL">START_TRACK_CREATE_TERMINAL_FAIL</a></span></code>
<div class="block">创建terminal非法</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/track/ErrorCode.TrackListen.html#START_TRACK_CUSTOM_ATTRIBUTE_INVALID">START_TRACK_CUSTOM_ATTRIBUTE_INVALID</a></span></code>
<div class="block">获取自定义参数时，出现异常</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/track/ErrorCode.TrackListen.html#START_TRACK_CUSTOM_ATTRIBUTE_INVALID_MSG">START_TRACK_CUSTOM_ATTRIBUTE_INVALID_MSG</a></span></code>
<div class="block">获取自定义参数时，出现异常</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/track/ErrorCode.TrackListen.html#START_TRACK_NET_CONNECTED">START_TRACK_NET_CONNECTED</a></span></code>
<div class="block">网络未连接</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/track/ErrorCode.TrackListen.html#START_TRACK_SERVICE_IS_INVALID">START_TRACK_SERVICE_IS_INVALID</a></span></code>
<div class="block">serviceid 非法</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/track/ErrorCode.TrackListen.html#START_TRACK_SUCEE">START_TRACK_SUCEE</a></span></code>
<div class="block">启动轨迹同步成功</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/track/ErrorCode.TrackListen.html#START_TRACK_SUCEE_NO_NETWORK">START_TRACK_SUCEE_NO_NETWORK</a></span></code>
<div class="block">启动轨迹同步成功，但是网络未连接</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/track/ErrorCode.TrackListen.html#START_TRACK_TERMINAL_IS_INVALID">START_TRACK_TERMINAL_IS_INVALID</a></span></code>
<div class="block">terminal 非法</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/track/ErrorCode.TrackListen.html#START_TRACK_TRACK_IS_INVALID">START_TRACK_TRACK_IS_INVALID</a></span></code>
<div class="block">Track不能为null</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/track/ErrorCode.TrackListen.html#STOP_GATHER_GATHER_NOT_STARTED">STOP_GATHER_GATHER_NOT_STARTED</a></span></code>
<div class="block">定位采集 未启动</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/track/ErrorCode.TrackListen.html#STOP_GATHER_SUCCE">STOP_GATHER_SUCCE</a></span></code>
<div class="block">定位采集 停止成功</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/track/ErrorCode.TrackListen.html#STOP_GATHER_TRACK_NOT_STARTED">STOP_GATHER_TRACK_NOT_STARTED</a></span></code>
<div class="block">停止采集失败，轨迹同步未启动</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/track/ErrorCode.TrackListen.html#STOP_TRACK_SUCCE">STOP_TRACK_SUCCE</a></span></code>
<div class="block">轨迹同步 停止成功</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/amap/api/track/ErrorCode.TrackListen.html#TrackListen--">TrackListen</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>字段详细资料</h3>
<a name="BIND_SUCCESS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BIND_SUCCESS</h4>
<pre>public static final&nbsp;int BIND_SUCCESS</pre>
<div class="block">绑定寻迹服务成功</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>1.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.track.ErrorCode.TrackListen.BIND_SUCCESS">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="START_SERVICE_EX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>START_SERVICE_EX</h4>
<pre>public static final&nbsp;int START_SERVICE_EX</pre>
<div class="block">启动寻迹服务失败，寻迹服务启动出现异常</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>1.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.track.ErrorCode.TrackListen.START_SERVICE_EX">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="SERVICE_NOT_STARTED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SERVICE_NOT_STARTED</h4>
<pre>public static final&nbsp;int SERVICE_NOT_STARTED</pre>
<div class="block">寻迹服务未启动，请先启动</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>1.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.track.ErrorCode.TrackListen.SERVICE_NOT_STARTED">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="REMOTE_EX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>REMOTE_EX</h4>
<pre>public static final&nbsp;int REMOTE_EX</pre>
<div class="block">寻迹服务异常</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>1.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.track.ErrorCode.TrackListen.REMOTE_EX">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="START_TRACK_SUCEE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>START_TRACK_SUCEE</h4>
<pre>public static final&nbsp;int START_TRACK_SUCEE</pre>
<div class="block">启动轨迹同步成功</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>1.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.track.ErrorCode.TrackListen.START_TRACK_SUCEE">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="START_TRACK_SUCEE_NO_NETWORK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>START_TRACK_SUCEE_NO_NETWORK</h4>
<pre>public static final&nbsp;int START_TRACK_SUCEE_NO_NETWORK</pre>
<div class="block">启动轨迹同步成功，但是网络未连接</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>1.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.track.ErrorCode.TrackListen.START_TRACK_SUCEE_NO_NETWORK">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="START_TRACK_ALREADY_STARTED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>START_TRACK_ALREADY_STARTED</h4>
<pre>public static final&nbsp;int START_TRACK_ALREADY_STARTED</pre>
<div class="block">轨迹同步已经启动</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>1.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.track.ErrorCode.TrackListen.START_TRACK_ALREADY_STARTED">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="START_GATHER_TRACK_NOT_STARTED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>START_GATHER_TRACK_NOT_STARTED</h4>
<pre>public static final&nbsp;int START_GATHER_TRACK_NOT_STARTED</pre>
<div class="block">轨迹同步 未启动</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>1.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.track.ErrorCode.TrackListen.START_GATHER_TRACK_NOT_STARTED">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="START_GATHER_ALREADY_STARTED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>START_GATHER_ALREADY_STARTED</h4>
<pre>public static final&nbsp;int START_GATHER_ALREADY_STARTED</pre>
<div class="block">定位采集 已经启动</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>1.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.track.ErrorCode.TrackListen.START_GATHER_ALREADY_STARTED">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="START_GATHER_SUCEE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>START_GATHER_SUCEE</h4>
<pre>public static final&nbsp;int START_GATHER_SUCEE</pre>
<div class="block">定位采集 启动成功</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>1.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.track.ErrorCode.TrackListen.START_GATHER_SUCEE">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="STOP_GATHER_TRACK_NOT_STARTED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>STOP_GATHER_TRACK_NOT_STARTED</h4>
<pre>public static final&nbsp;int STOP_GATHER_TRACK_NOT_STARTED</pre>
<div class="block">停止采集失败，轨迹同步未启动</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>1.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.track.ErrorCode.TrackListen.STOP_GATHER_TRACK_NOT_STARTED">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="STOP_GATHER_GATHER_NOT_STARTED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>STOP_GATHER_GATHER_NOT_STARTED</h4>
<pre>public static final&nbsp;int STOP_GATHER_GATHER_NOT_STARTED</pre>
<div class="block">定位采集 未启动</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>1.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.track.ErrorCode.TrackListen.STOP_GATHER_GATHER_NOT_STARTED">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="STOP_GATHER_SUCCE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>STOP_GATHER_SUCCE</h4>
<pre>public static final&nbsp;int STOP_GATHER_SUCCE</pre>
<div class="block">定位采集 停止成功</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>1.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.track.ErrorCode.TrackListen.STOP_GATHER_SUCCE">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="STOP_TRACK_SUCCE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>STOP_TRACK_SUCCE</h4>
<pre>public static final&nbsp;int STOP_TRACK_SUCCE</pre>
<div class="block">轨迹同步 停止成功</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>1.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.track.ErrorCode.TrackListen.STOP_TRACK_SUCCE">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="START_TRACK_NET_CONNECTED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>START_TRACK_NET_CONNECTED</h4>
<pre>public static final&nbsp;int START_TRACK_NET_CONNECTED</pre>
<div class="block">网络未连接</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>1.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.track.ErrorCode.TrackListen.START_TRACK_NET_CONNECTED">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="START_TRACK_AUTH_CHECK_FAIL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>START_TRACK_AUTH_CHECK_FAIL</h4>
<pre>public static final&nbsp;int START_TRACK_AUTH_CHECK_FAIL</pre>
<div class="block">鉴权失败</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>1.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.track.ErrorCode.TrackListen.START_TRACK_AUTH_CHECK_FAIL">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="START_TRACK_TRACK_IS_INVALID">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>START_TRACK_TRACK_IS_INVALID</h4>
<pre>public static final&nbsp;int START_TRACK_TRACK_IS_INVALID</pre>
<div class="block">Track不能为null</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>1.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.track.ErrorCode.TrackListen.START_TRACK_TRACK_IS_INVALID">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="START_TRACK_SERVICE_IS_INVALID">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>START_TRACK_SERVICE_IS_INVALID</h4>
<pre>public static final&nbsp;int START_TRACK_SERVICE_IS_INVALID</pre>
<div class="block">serviceid 非法</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>1.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.track.ErrorCode.TrackListen.START_TRACK_SERVICE_IS_INVALID">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="START_TRACK_TERMINAL_IS_INVALID">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>START_TRACK_TERMINAL_IS_INVALID</h4>
<pre>public static final&nbsp;int START_TRACK_TERMINAL_IS_INVALID</pre>
<div class="block">terminal 非法</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>1.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.track.ErrorCode.TrackListen.START_TRACK_TERMINAL_IS_INVALID">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="START_TRACK_CREATE_TERMINAL_FAIL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>START_TRACK_CREATE_TERMINAL_FAIL</h4>
<pre>public static final&nbsp;int START_TRACK_CREATE_TERMINAL_FAIL</pre>
<div class="block">创建terminal非法</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>1.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.track.ErrorCode.TrackListen.START_TRACK_CREATE_TERMINAL_FAIL">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="START_TRACK_CUSTOM_ATTRIBUTE_INVALID">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>START_TRACK_CUSTOM_ATTRIBUTE_INVALID</h4>
<pre>public static final&nbsp;int START_TRACK_CUSTOM_ATTRIBUTE_INVALID</pre>
<div class="block">获取自定义参数时，出现异常</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>1.1.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.track.ErrorCode.TrackListen.START_TRACK_CUSTOM_ATTRIBUTE_INVALID">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="START_TRACK_CUSTOM_ATTRIBUTE_INVALID_MSG">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>START_TRACK_CUSTOM_ATTRIBUTE_INVALID_MSG</h4>
<pre>public static final&nbsp;java.lang.String START_TRACK_CUSTOM_ATTRIBUTE_INVALID_MSG</pre>
<div class="block">获取自定义参数时，出现异常</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>1.1.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.track.ErrorCode.TrackListen.START_TRACK_CUSTOM_ATTRIBUTE_INVALID_MSG">常量字段值</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="TrackListen--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>TrackListen</h4>
<pre>public&nbsp;TrackListen()</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/ErrorCode.TrackListen.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/amap/api/track/ErrorCode.Response.html" title="com.amap.api.track中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../com/amap/api/track/OnCustomAttributeListener.html" title="com.amap.api.track中的接口"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/amap/api/track/ErrorCode.TrackListen.html" target="_top">框架</a></li>
<li><a href="ErrorCode.TrackListen.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li><a href="#field.summary">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#methods.inherited.from.class.java.lang.Object">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li><a href="#field.detail">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li>方法</li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
