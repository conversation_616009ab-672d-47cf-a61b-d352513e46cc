<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>AMapTrackClient</title>
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="AMapTrackClient";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":9,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10};
var tabs = {65535:["t0","所有方法"],1:["t1","静态方法"],2:["t2","实例方法"],8:["t4","具体方法"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/AMapTrackClient.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个类</li>
<li><a href="../../../../com/amap/api/track/ErrorCode.html" title="com.amap.api.track中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/amap/api/track/AMapTrackClient.html" target="_top">框架</a></li>
<li><a href="AMapTrackClient.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.amap.api.track</div>
<h2 title="类 AMapTrackClient" class="title">类 AMapTrackClient</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.amap.api.track.AMapTrackClient</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public final class <span class="typeNameLabel">AMapTrackClient</span>
extends java.lang.Object</pre>
<div class="block">猎鹰sdk服务类，提供轨迹上报控制、查询上报轨迹信息功能。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>1.0</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/amap/api/track/AMapTrackClient.html#AMapTrackClient-android.content.Context-">AMapTrackClient</a></span>(android.content.Context&nbsp;context)</code>
<div class="block">构造函数
 应该将Application作为Context传入</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>所有方法</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">静态方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">实例方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">具体方法</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/track/AMapTrackClient.html#addTerminal-com.amap.api.track.query.model.AddTerminalRequest-com.amap.api.track.query.model.OnTrackListener-">addTerminal</a></span>(<a href="../../../../com/amap/api/track/query/model/AddTerminalRequest.html" title="com.amap.api.track.query.model中的类">AddTerminalRequest</a>&nbsp;addTerminalRequest,
           <a href="../../../../com/amap/api/track/query/model/OnTrackListener.html" title="com.amap.api.track.query.model中的接口">OnTrackListener</a>&nbsp;listener)</code>
<div class="block">创建terminal</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/track/AMapTrackClient.html#addTrack-com.amap.api.track.query.model.AddTrackRequest-com.amap.api.track.query.model.OnTrackListener-">addTrack</a></span>(<a href="../../../../com/amap/api/track/query/model/AddTrackRequest.html" title="com.amap.api.track.query.model中的类">AddTrackRequest</a>&nbsp;addTrackRequest,
        <a href="../../../../com/amap/api/track/query/model/OnTrackListener.html" title="com.amap.api.track.query.model中的接口">OnTrackListener</a>&nbsp;listener)</code>
<div class="block">增加轨迹</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/track/AMapTrackClient.html#getTrackId--">getTrackId</a></span>()</code>
<div class="block">获得当前设置的轨迹id
 该方法只有在已经启动轨迹服务后才会生效</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/track/AMapTrackClient.html#getVersion--">getVersion</a></span>()</code>
<div class="block">获得版本号</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/track/AMapTrackClient.html#queryDistance-com.amap.api.track.query.model.DistanceRequest-com.amap.api.track.query.model.OnTrackListener-">queryDistance</a></span>(<a href="../../../../com/amap/api/track/query/model/DistanceRequest.html" title="com.amap.api.track.query.model中的类">DistanceRequest</a>&nbsp;request,
             <a href="../../../../com/amap/api/track/query/model/OnTrackListener.html" title="com.amap.api.track.query.model中的接口">OnTrackListener</a>&nbsp;listener)</code>
<div class="block">查询里程</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/track/AMapTrackClient.html#queryHistoryTrack-com.amap.api.track.query.model.HistoryTrackRequest-com.amap.api.track.query.model.OnTrackListener-">queryHistoryTrack</a></span>(<a href="../../../../com/amap/api/track/query/model/HistoryTrackRequest.html" title="com.amap.api.track.query.model中的类">HistoryTrackRequest</a>&nbsp;request,
                 <a href="../../../../com/amap/api/track/query/model/OnTrackListener.html" title="com.amap.api.track.query.model中的接口">OnTrackListener</a>&nbsp;listener)</code>
<div class="block">查询历史轨迹</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/track/AMapTrackClient.html#queryLatestPoint-com.amap.api.track.query.model.LatestPointRequest-com.amap.api.track.query.model.OnTrackListener-">queryLatestPoint</a></span>(<a href="../../../../com/amap/api/track/query/model/LatestPointRequest.html" title="com.amap.api.track.query.model中的类">LatestPointRequest</a>&nbsp;request,
                <a href="../../../../com/amap/api/track/query/model/OnTrackListener.html" title="com.amap.api.track.query.model中的接口">OnTrackListener</a>&nbsp;listener)</code>
<div class="block">查询最新轨迹点</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/track/AMapTrackClient.html#queryTerminal-com.amap.api.track.query.model.QueryTerminalRequest-com.amap.api.track.query.model.OnTrackListener-">queryTerminal</a></span>(<a href="../../../../com/amap/api/track/query/model/QueryTerminalRequest.html" title="com.amap.api.track.query.model中的类">QueryTerminalRequest</a>&nbsp;queryTerminalRequest,
             <a href="../../../../com/amap/api/track/query/model/OnTrackListener.html" title="com.amap.api.track.query.model中的接口">OnTrackListener</a>&nbsp;listener)</code>
<div class="block">查询terminal</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/track/AMapTrackClient.html#queryTerminalTrack-com.amap.api.track.query.model.QueryTrackRequest-com.amap.api.track.query.model.OnTrackListener-">queryTerminalTrack</a></span>(<a href="../../../../com/amap/api/track/query/model/QueryTrackRequest.html" title="com.amap.api.track.query.model中的类">QueryTrackRequest</a>&nbsp;request,
                  <a href="../../../../com/amap/api/track/query/model/OnTrackListener.html" title="com.amap.api.track.query.model中的接口">OnTrackListener</a>&nbsp;listener)</code>
<div class="block">查询终端下的轨迹</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/track/AMapTrackClient.html#setCacheSize-int-">setCacheSize</a></span>(int&nbsp;cacheSize)</code>
<div class="block">设置缓存大小, 单位MB。</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/track/AMapTrackClient.html#setInterval-int-int-">setInterval</a></span>(int&nbsp;gatherInterval,
           int&nbsp;packInterval)</code>
<div class="block">设置采集和打包位置数据的时间间隔
 该接口既可以在开启轨迹服务前调用，也可以在开启轨迹服务后调用。</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/track/AMapTrackClient.html#setLocationMode-int-">setLocationMode</a></span>(int&nbsp;locationMode)</code>
<div class="block">设置定位模式。</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/track/AMapTrackClient.html#setOnCustomAttributeListener-com.amap.api.track.OnCustomAttributeListener-">setOnCustomAttributeListener</a></span>(<a href="../../../../com/amap/api/track/OnCustomAttributeListener.html" title="com.amap.api.track中的接口">OnCustomAttributeListener</a>&nbsp;customAttributeListener)</code>
<div class="block">设置自定义属性监听器
 该接口既可以在开启轨迹服务前调用，也可以在开启轨迹服务后调用。</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/track/AMapTrackClient.html#setOnTrackListener-com.amap.api.track.OnTrackLifecycleListener-">setOnTrackListener</a></span>(<a href="../../../../com/amap/api/track/OnTrackLifecycleListener.html" title="com.amap.api.track中的接口">OnTrackLifecycleListener</a>&nbsp;trackListener)</code>
<div class="block">设置轨迹服务监听器
 该接口必须在开启轨迹服务后调用才会生效</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/track/AMapTrackClient.html#setProtocolType-int-">setProtocolType</a></span>(int&nbsp;protocolType)</code>
<div class="block">设置协议类型（http、https），默认为https
 该接口既可以在开启轨迹服务前调用，也可以在开启轨迹服务后调用。</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/track/AMapTrackClient.html#setTrackId-long-">setTrackId</a></span>(long&nbsp;trackId)</code>
<div class="block">设置轨迹id。</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/track/AMapTrackClient.html#startGather-com.amap.api.track.OnTrackLifecycleListener-">startGather</a></span>(<a href="../../../../com/amap/api/track/OnTrackLifecycleListener.html" title="com.amap.api.track中的接口">OnTrackLifecycleListener</a>&nbsp;trackListener)</code>
<div class="block">开启轨迹采集</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/track/AMapTrackClient.html#startTrack-com.amap.api.track.TrackParam-com.amap.api.track.OnTrackLifecycleListener-">startTrack</a></span>(<a href="../../../../com/amap/api/track/TrackParam.html" title="com.amap.api.track中的类">TrackParam</a>&nbsp;mTrackParam,
          <a href="../../../../com/amap/api/track/OnTrackLifecycleListener.html" title="com.amap.api.track中的接口">OnTrackLifecycleListener</a>&nbsp;trackListener)</code>
<div class="block">开启轨迹服务
 在开启轨迹服务前，需要初始化Track，并在AndroidManifest.xml文件中配置API_KEY(AK)。</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/track/AMapTrackClient.html#stopGather-com.amap.api.track.OnTrackLifecycleListener-">stopGather</a></span>(<a href="../../../../com/amap/api/track/OnTrackLifecycleListener.html" title="com.amap.api.track中的接口">OnTrackLifecycleListener</a>&nbsp;trackListener)</code>
<div class="block">停止轨迹采集</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/track/AMapTrackClient.html#stopTrack-com.amap.api.track.TrackParam-com.amap.api.track.OnTrackLifecycleListener-">stopTrack</a></span>(<a href="../../../../com/amap/api/track/TrackParam.html" title="com.amap.api.track中的类">TrackParam</a>&nbsp;mTrackParam,
         <a href="../../../../com/amap/api/track/OnTrackLifecycleListener.html" title="com.amap.api.track中的接口">OnTrackLifecycleListener</a>&nbsp;trackListener)</code>
<div class="block">停止轨迹服务</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="AMapTrackClient-android.content.Context-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>AMapTrackClient</h4>
<pre>public&nbsp;AMapTrackClient(android.content.Context&nbsp;context)</pre>
<div class="block">构造函数
 应该将Application作为Context传入</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>context</code> - 该参数应该传入Application Context</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>1.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="getVersion--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVersion</h4>
<pre>public static&nbsp;java.lang.String&nbsp;getVersion()</pre>
<div class="block">获得版本号</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>版本号</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>1.0</dd>
</dl>
</li>
</ul>
<a name="setCacheSize-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCacheSize</h4>
<pre>public&nbsp;void&nbsp;setCacheSize(int&nbsp;cacheSize)</pre>
<div class="block">设置缓存大小, 单位MB。默认为50MB
 该接口既可以在开启轨迹服务前调用，也可以在开启轨迹服务后调用。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>cacheSize</code> - 缓存大小，单位为MB</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>1.0</dd>
</dl>
</li>
</ul>
<a name="setInterval-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setInterval</h4>
<pre>public&nbsp;void&nbsp;setInterval(int&nbsp;gatherInterval,
                        int&nbsp;packInterval)</pre>
<div class="block">设置采集和打包位置数据的时间间隔
 该接口既可以在开启轨迹服务前调用，也可以在开启轨迹服务后调用。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>gatherInterval</code> - 采集时间间隔 , 单位为s, 范围为1s~60s , 在定位周期大于15s时，SDK会将定位周期设置为5的倍数</dd>
<dd><code>packInterval</code> - 打包时间间隔 , 单位为s, 范围为采集时间*5~采集时间*50，为采集时间的整数倍</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>1.0</dd>
</dl>
</li>
</ul>
<a name="setLocationMode-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLocationMode</h4>
<pre>public&nbsp;void&nbsp;setLocationMode(int&nbsp;locationMode)</pre>
<div class="block">设置定位模式。默认为高精度定位模式
 高精度定位模式：在这种定位模式下，将同时使用高德网络定位和GPS定位,优先返回精度高的定位
 低功耗定位模式：在这种模式下，将只使用高德网络定位
 仅设备定位模式：在这种模式下，将只使用GPS定位。
 该接口既可以在开启轨迹服务前调用，也可以在开启轨迹服务后调用。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>locationMode</code> - 定位模式，取值参考<a href="../../../../com/amap/api/track/query/entity/LocationMode.html" title="com.amap.api.track.query.entity中的类"><code>LocationMode</code></a></dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>1.0</dd>
</dl>
</li>
</ul>
<a name="setOnCustomAttributeListener-com.amap.api.track.OnCustomAttributeListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setOnCustomAttributeListener</h4>
<pre>public&nbsp;void&nbsp;setOnCustomAttributeListener(<a href="../../../../com/amap/api/track/OnCustomAttributeListener.html" title="com.amap.api.track中的接口">OnCustomAttributeListener</a>&nbsp;customAttributeListener)</pre>
<div class="block">设置自定义属性监听器
 该接口既可以在开启轨迹服务前调用，也可以在开启轨迹服务后调用。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>customAttributeListener</code> - 自定义属性监听器，每个轨迹点都会使用由该listener返回的自定义属性进行上报</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>1.1.0</dd>
</dl>
</li>
</ul>
<a name="setOnTrackListener-com.amap.api.track.OnTrackLifecycleListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setOnTrackListener</h4>
<pre>public&nbsp;void&nbsp;setOnTrackListener(<a href="../../../../com/amap/api/track/OnTrackLifecycleListener.html" title="com.amap.api.track中的接口">OnTrackLifecycleListener</a>&nbsp;trackListener)</pre>
<div class="block">设置轨迹服务监听器
 该接口必须在开启轨迹服务后调用才会生效</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>trackListener</code> - 轨迹服务监听器，监听服务的绑定和启停、采集启停事件</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>1.0</dd>
</dl>
</li>
</ul>
<a name="setProtocolType-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setProtocolType</h4>
<pre>public&nbsp;void&nbsp;setProtocolType(int&nbsp;protocolType)</pre>
<div class="block">设置协议类型（http、https），默认为https
 该接口既可以在开启轨迹服务前调用，也可以在开启轨迹服务后调用。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>protocolType</code> - 协议类型，取值参考<a href="../../../../com/amap/api/track/query/entity/ProtocolType.html" title="com.amap.api.track.query.entity中的类"><code>ProtocolType</code></a></dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>1.0</dd>
</dl>
</li>
</ul>
<a name="startGather-com.amap.api.track.OnTrackLifecycleListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>startGather</h4>
<pre>public&nbsp;void&nbsp;startGather(<a href="../../../../com/amap/api/track/OnTrackLifecycleListener.html" title="com.amap.api.track中的接口">OnTrackLifecycleListener</a>&nbsp;trackListener)</pre>
<div class="block">开启轨迹采集</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>trackListener</code> - 开启轨迹采集的结果监听listener</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>1.0</dd>
</dl>
</li>
</ul>
<a name="startTrack-com.amap.api.track.TrackParam-com.amap.api.track.OnTrackLifecycleListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>startTrack</h4>
<pre>public&nbsp;void&nbsp;startTrack(<a href="../../../../com/amap/api/track/TrackParam.html" title="com.amap.api.track中的类">TrackParam</a>&nbsp;mTrackParam,
                       <a href="../../../../com/amap/api/track/OnTrackLifecycleListener.html" title="com.amap.api.track中的接口">OnTrackLifecycleListener</a>&nbsp;trackListener)</pre>
<div class="block">开启轨迹服务
 在开启轨迹服务前，需要初始化Track，并在AndroidManifest.xml文件中配置API_KEY(AK)。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>mTrackParam</code> - 轨迹采集的配置信息</dd>
<dd><code>trackListener</code> - 开启轨迹服务的结果监听listener</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>1.0</dd>
</dl>
</li>
</ul>
<a name="setTrackId-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTrackId</h4>
<pre>public&nbsp;void&nbsp;setTrackId(long&nbsp;trackId)</pre>
<div class="block">设置轨迹id。如果要上报散点，则将trackId置为-1
 该方法只有在已经启动轨迹服务后才会生效</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>trackId</code> - 轨迹id</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>1.0</dd>
</dl>
</li>
</ul>
<a name="getTrackId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTrackId</h4>
<pre>public&nbsp;long&nbsp;getTrackId()</pre>
<div class="block">获得当前设置的轨迹id
 该方法只有在已经启动轨迹服务后才会生效</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>1.0</dd>
</dl>
</li>
</ul>
<a name="stopGather-com.amap.api.track.OnTrackLifecycleListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>stopGather</h4>
<pre>public&nbsp;void&nbsp;stopGather(<a href="../../../../com/amap/api/track/OnTrackLifecycleListener.html" title="com.amap.api.track中的接口">OnTrackLifecycleListener</a>&nbsp;trackListener)</pre>
<div class="block">停止轨迹采集</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>trackListener</code> - 停止结果监听器</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>1.0</dd>
</dl>
</li>
</ul>
<a name="stopTrack-com.amap.api.track.TrackParam-com.amap.api.track.OnTrackLifecycleListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>stopTrack</h4>
<pre>public&nbsp;void&nbsp;stopTrack(<a href="../../../../com/amap/api/track/TrackParam.html" title="com.amap.api.track中的类">TrackParam</a>&nbsp;mTrackParam,
                      <a href="../../../../com/amap/api/track/OnTrackLifecycleListener.html" title="com.amap.api.track中的接口">OnTrackLifecycleListener</a>&nbsp;trackListener)</pre>
<div class="block">停止轨迹服务</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>mTrackParam</code> - 和开启时传入的track参数一致</dd>
<dd><code>trackListener</code> - 停止结果监听器</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>1.0</dd>
</dl>
</li>
</ul>
<a name="queryTerminal-com.amap.api.track.query.model.QueryTerminalRequest-com.amap.api.track.query.model.OnTrackListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>queryTerminal</h4>
<pre>public&nbsp;void&nbsp;queryTerminal(<a href="../../../../com/amap/api/track/query/model/QueryTerminalRequest.html" title="com.amap.api.track.query.model中的类">QueryTerminalRequest</a>&nbsp;queryTerminalRequest,
                          <a href="../../../../com/amap/api/track/query/model/OnTrackListener.html" title="com.amap.api.track.query.model中的接口">OnTrackListener</a>&nbsp;listener)</pre>
<div class="block">查询terminal</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>queryTerminalRequest</code> - 查询请求</dd>
<dd><code>listener</code> - 查询结果监听器</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>1.0</dd>
</dl>
</li>
</ul>
<a name="addTerminal-com.amap.api.track.query.model.AddTerminalRequest-com.amap.api.track.query.model.OnTrackListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addTerminal</h4>
<pre>public&nbsp;void&nbsp;addTerminal(<a href="../../../../com/amap/api/track/query/model/AddTerminalRequest.html" title="com.amap.api.track.query.model中的类">AddTerminalRequest</a>&nbsp;addTerminalRequest,
                        <a href="../../../../com/amap/api/track/query/model/OnTrackListener.html" title="com.amap.api.track.query.model中的接口">OnTrackListener</a>&nbsp;listener)</pre>
<div class="block">创建terminal</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>addTerminalRequest</code> - 创建请求</dd>
<dd><code>listener</code> - 请求结果监听器</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>1.0</dd>
</dl>
</li>
</ul>
<a name="queryDistance-com.amap.api.track.query.model.DistanceRequest-com.amap.api.track.query.model.OnTrackListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>queryDistance</h4>
<pre>public&nbsp;void&nbsp;queryDistance(<a href="../../../../com/amap/api/track/query/model/DistanceRequest.html" title="com.amap.api.track.query.model中的类">DistanceRequest</a>&nbsp;request,
                          <a href="../../../../com/amap/api/track/query/model/OnTrackListener.html" title="com.amap.api.track.query.model中的接口">OnTrackListener</a>&nbsp;listener)</pre>
<div class="block">查询里程</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>request</code> - 查询请求</dd>
<dd><code>listener</code> - 请求结果监听器</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>1.0</dd>
</dl>
</li>
</ul>
<a name="queryLatestPoint-com.amap.api.track.query.model.LatestPointRequest-com.amap.api.track.query.model.OnTrackListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>queryLatestPoint</h4>
<pre>public&nbsp;void&nbsp;queryLatestPoint(<a href="../../../../com/amap/api/track/query/model/LatestPointRequest.html" title="com.amap.api.track.query.model中的类">LatestPointRequest</a>&nbsp;request,
                             <a href="../../../../com/amap/api/track/query/model/OnTrackListener.html" title="com.amap.api.track.query.model中的接口">OnTrackListener</a>&nbsp;listener)</pre>
<div class="block">查询最新轨迹点</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>request</code> - 查询请求</dd>
<dd><code>listener</code> - 请求结果监听器</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>1.0</dd>
</dl>
</li>
</ul>
<a name="queryHistoryTrack-com.amap.api.track.query.model.HistoryTrackRequest-com.amap.api.track.query.model.OnTrackListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>queryHistoryTrack</h4>
<pre>public&nbsp;void&nbsp;queryHistoryTrack(<a href="../../../../com/amap/api/track/query/model/HistoryTrackRequest.html" title="com.amap.api.track.query.model中的类">HistoryTrackRequest</a>&nbsp;request,
                              <a href="../../../../com/amap/api/track/query/model/OnTrackListener.html" title="com.amap.api.track.query.model中的接口">OnTrackListener</a>&nbsp;listener)</pre>
<div class="block">查询历史轨迹</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>request</code> - 查询请求</dd>
<dd><code>listener</code> - 请求结果监听器</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>1.0</dd>
</dl>
</li>
</ul>
<a name="queryTerminalTrack-com.amap.api.track.query.model.QueryTrackRequest-com.amap.api.track.query.model.OnTrackListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>queryTerminalTrack</h4>
<pre>public&nbsp;void&nbsp;queryTerminalTrack(<a href="../../../../com/amap/api/track/query/model/QueryTrackRequest.html" title="com.amap.api.track.query.model中的类">QueryTrackRequest</a>&nbsp;request,
                               <a href="../../../../com/amap/api/track/query/model/OnTrackListener.html" title="com.amap.api.track.query.model中的接口">OnTrackListener</a>&nbsp;listener)</pre>
<div class="block">查询终端下的轨迹</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>request</code> - 查询请求</dd>
<dd><code>listener</code> - 请求结果监听器</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>1.0</dd>
</dl>
</li>
</ul>
<a name="addTrack-com.amap.api.track.query.model.AddTrackRequest-com.amap.api.track.query.model.OnTrackListener-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>addTrack</h4>
<pre>public&nbsp;void&nbsp;addTrack(<a href="../../../../com/amap/api/track/query/model/AddTrackRequest.html" title="com.amap.api.track.query.model中的类">AddTrackRequest</a>&nbsp;addTrackRequest,
                     <a href="../../../../com/amap/api/track/query/model/OnTrackListener.html" title="com.amap.api.track.query.model中的接口">OnTrackListener</a>&nbsp;listener)</pre>
<div class="block">增加轨迹</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>addTrackRequest</code> - 增加轨迹请求</dd>
<dd><code>listener</code> - 请求结果监听器</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>1.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/AMapTrackClient.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个类</li>
<li><a href="../../../../com/amap/api/track/ErrorCode.html" title="com.amap.api.track中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/amap/api/track/AMapTrackClient.html" target="_top">框架</a></li>
<li><a href="AMapTrackClient.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
