<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>A - 索引</title>
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="A - \u7D22\u5F15";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个字母</li>
<li><a href="index-2.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-1.html" target="_top">框架</a></li>
<li><a href="index-1.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="contentContainer"><a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">G</a>&nbsp;<a href="index-7.html">H</a>&nbsp;<a href="index-8.html">I</a>&nbsp;<a href="index-9.html">L</a>&nbsp;<a href="index-10.html">M</a>&nbsp;<a href="index-11.html">N</a>&nbsp;<a href="index-12.html">O</a>&nbsp;<a href="index-13.html">P</a>&nbsp;<a href="index-14.html">Q</a>&nbsp;<a href="index-15.html">R</a>&nbsp;<a href="index-16.html">S</a>&nbsp;<a href="index-17.html">T</a>&nbsp;<a href="index-18.html">W</a>&nbsp;<a name="I:A">
<!--   -->
</a>
<h2 class="title">A</h2>
<dl>
<dt><a href="../com/amap/api/track/query/entity/AccuracyMode.html" title="com.amap.api.track.query.entity中的类"><span class="typeNameLink">AccuracyMode</span></a> - <a href="../com/amap/api/track/query/entity/package-summary.html">com.amap.api.track.query.entity</a>中的类</dt>
<dd>
<div class="block">轨迹点精度过滤</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/query/entity/AccuracyMode.html#AccuracyMode--">AccuracyMode()</a></span> - 类 的构造器com.amap.api.track.query.entity.<a href="../com/amap/api/track/query/entity/AccuracyMode.html" title="com.amap.api.track.query.entity中的类">AccuracyMode</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/AMapTrackClient.html#addTerminal-com.amap.api.track.query.model.AddTerminalRequest-com.amap.api.track.query.model.OnTrackListener-">addTerminal(AddTerminalRequest, OnTrackListener)</a></span> - 类 中的方法com.amap.api.track.<a href="../com/amap/api/track/AMapTrackClient.html" title="com.amap.api.track中的类">AMapTrackClient</a></dt>
<dd>
<div class="block">创建terminal</div>
</dd>
<dt><a href="../com/amap/api/track/query/model/AddTerminalRequest.html" title="com.amap.api.track.query.model中的类"><span class="typeNameLink">AddTerminalRequest</span></a> - <a href="../com/amap/api/track/query/model/package-summary.html">com.amap.api.track.query.model</a>中的类</dt>
<dd>
<div class="block">创建新终端</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/query/model/AddTerminalRequest.html#AddTerminalRequest-java.lang.String-long-">AddTerminalRequest(String, long)</a></span> - 类 的构造器com.amap.api.track.query.model.<a href="../com/amap/api/track/query/model/AddTerminalRequest.html" title="com.amap.api.track.query.model中的类">AddTerminalRequest</a></dt>
<dd>
<div class="block">构造创建新终端请求</div>
</dd>
<dt><a href="../com/amap/api/track/query/model/AddTerminalResponse.html" title="com.amap.api.track.query.model中的类"><span class="typeNameLink">AddTerminalResponse</span></a> - <a href="../com/amap/api/track/query/model/package-summary.html">com.amap.api.track.query.model</a>中的类</dt>
<dd>
<div class="block">创建终端请求返回结果</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/AMapTrackClient.html#addTrack-com.amap.api.track.query.model.AddTrackRequest-com.amap.api.track.query.model.OnTrackListener-">addTrack(AddTrackRequest, OnTrackListener)</a></span> - 类 中的方法com.amap.api.track.<a href="../com/amap/api/track/AMapTrackClient.html" title="com.amap.api.track中的类">AMapTrackClient</a></dt>
<dd>
<div class="block">增加轨迹</div>
</dd>
<dt><a href="../com/amap/api/track/query/model/AddTrackRequest.html" title="com.amap.api.track.query.model中的类"><span class="typeNameLink">AddTrackRequest</span></a> - <a href="../com/amap/api/track/query/model/package-summary.html">com.amap.api.track.query.model</a>中的类</dt>
<dd>
<div class="block">创建轨迹请求</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/query/model/AddTrackRequest.html#AddTrackRequest-long-long-">AddTrackRequest(long, long)</a></span> - 类 的构造器com.amap.api.track.query.model.<a href="../com/amap/api/track/query/model/AddTrackRequest.html" title="com.amap.api.track.query.model中的类">AddTrackRequest</a></dt>
<dd>
<div class="block">构造创建新轨迹请求</div>
</dd>
<dt><a href="../com/amap/api/track/query/model/AddTrackResponse.html" title="com.amap.api.track.query.model中的类"><span class="typeNameLink">AddTrackResponse</span></a> - <a href="../com/amap/api/track/query/model/package-summary.html">com.amap.api.track.query.model</a>中的类</dt>
<dd>
<div class="block">创建轨迹请求的返回结果</div>
</dd>
<dt><a href="../com/amap/api/track/AMapTrackClient.html" title="com.amap.api.track中的类"><span class="typeNameLink">AMapTrackClient</span></a> - <a href="../com/amap/api/track/package-summary.html">com.amap.api.track</a>中的类</dt>
<dd>
<div class="block">猎鹰sdk服务类，提供轨迹上报控制、查询上报轨迹信息功能。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/AMapTrackClient.html#AMapTrackClient-android.content.Context-">AMapTrackClient(Context)</a></span> - 类 的构造器com.amap.api.track.<a href="../com/amap/api/track/AMapTrackClient.html" title="com.amap.api.track中的类">AMapTrackClient</a></dt>
<dd>
<div class="block">构造函数
 应该将Application作为Context传入</div>
</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">G</a>&nbsp;<a href="index-7.html">H</a>&nbsp;<a href="index-8.html">I</a>&nbsp;<a href="index-9.html">L</a>&nbsp;<a href="index-10.html">M</a>&nbsp;<a href="index-11.html">N</a>&nbsp;<a href="index-12.html">O</a>&nbsp;<a href="index-13.html">P</a>&nbsp;<a href="index-14.html">Q</a>&nbsp;<a href="index-15.html">R</a>&nbsp;<a href="index-16.html">S</a>&nbsp;<a href="index-17.html">T</a>&nbsp;<a href="index-18.html">W</a>&nbsp;</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个字母</li>
<li><a href="index-2.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-1.html" target="_top">框架</a></li>
<li><a href="index-1.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
