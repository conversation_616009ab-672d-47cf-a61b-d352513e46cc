<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>O - 索引</title>
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="O - \u7D22\u5F15";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-11.html">上一个字母</a></li>
<li><a href="index-13.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-12.html" target="_top">框架</a></li>
<li><a href="index-12.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="contentContainer"><a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">G</a>&nbsp;<a href="index-7.html">H</a>&nbsp;<a href="index-8.html">I</a>&nbsp;<a href="index-9.html">L</a>&nbsp;<a href="index-10.html">M</a>&nbsp;<a href="index-11.html">N</a>&nbsp;<a href="index-12.html">O</a>&nbsp;<a href="index-13.html">P</a>&nbsp;<a href="index-14.html">Q</a>&nbsp;<a href="index-15.html">R</a>&nbsp;<a href="index-16.html">S</a>&nbsp;<a href="index-17.html">T</a>&nbsp;<a href="index-18.html">W</a>&nbsp;<a name="I:O">
<!--   -->
</a>
<h2 class="title">O</h2>
<dl>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/query/entity/OrderMode.html#OLD_FIRST">OLD_FIRST</a></span> - 类 中的静态变量com.amap.api.track.query.entity.<a href="../com/amap/api/track/query/entity/OrderMode.html" title="com.amap.api.track.query.entity中的类">OrderMode</a></dt>
<dd>
<div class="block">按照定位时间，由旧>新排序</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/query/model/OnTrackListener.html#onAddTrackCallback-com.amap.api.track.query.model.AddTrackResponse-">onAddTrackCallback(AddTrackResponse)</a></span> - 接口 中的方法com.amap.api.track.query.model.<a href="../com/amap/api/track/query/model/OnTrackListener.html" title="com.amap.api.track.query.model中的接口">OnTrackListener</a></dt>
<dd>
<div class="block">增加轨迹请求的回调</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/OnTrackLifecycleListener.html#onBindServiceCallback-int-java.lang.String-">onBindServiceCallback(int, String)</a></span> - 接口 中的方法com.amap.api.track.<a href="../com/amap/api/track/OnTrackLifecycleListener.html" title="com.amap.api.track中的接口">OnTrackLifecycleListener</a></dt>
<dd>
<div class="block">绑定服务回调接口</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/query/model/OnTrackListener.html#onCreateTerminalCallback-com.amap.api.track.query.model.AddTerminalResponse-">onCreateTerminalCallback(AddTerminalResponse)</a></span> - 接口 中的方法com.amap.api.track.query.model.<a href="../com/amap/api/track/query/model/OnTrackListener.html" title="com.amap.api.track.query.model中的接口">OnTrackListener</a></dt>
<dd>
<div class="block">创建terminal的回调</div>
</dd>
<dt><a href="../com/amap/api/track/OnCustomAttributeListener.html" title="com.amap.api.track中的接口"><span class="typeNameLink">OnCustomAttributeListener</span></a> - <a href="../com/amap/api/track/package-summary.html">com.amap.api.track</a>中的接口</dt>
<dd>
<div class="block">轨迹属性回调接口，决定每个上报的轨迹点的自定义属性</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/query/model/OnTrackListener.html#onDistanceCallback-com.amap.api.track.query.model.DistanceResponse-">onDistanceCallback(DistanceResponse)</a></span> - 接口 中的方法com.amap.api.track.query.model.<a href="../com/amap/api/track/query/model/OnTrackListener.html" title="com.amap.api.track.query.model中的接口">OnTrackListener</a></dt>
<dd>
<div class="block">查询里程回调</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/query/model/OnTrackListener.html#onHistoryTrackCallback-com.amap.api.track.query.model.HistoryTrackResponse-">onHistoryTrackCallback(HistoryTrackResponse)</a></span> - 接口 中的方法com.amap.api.track.query.model.<a href="../com/amap/api/track/query/model/OnTrackListener.html" title="com.amap.api.track.query.model中的接口">OnTrackListener</a></dt>
<dd>
<div class="block">查询历史轨迹的的回调</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/query/model/OnTrackListener.html#onLatestPointCallback-com.amap.api.track.query.model.LatestPointResponse-">onLatestPointCallback(LatestPointResponse)</a></span> - 接口 中的方法com.amap.api.track.query.model.<a href="../com/amap/api/track/query/model/OnTrackListener.html" title="com.amap.api.track.query.model中的接口">OnTrackListener</a></dt>
<dd>
<div class="block">查询终端最新轨迹点的回调</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/query/model/OnTrackListener.html#onParamErrorCallback-com.amap.api.track.query.model.ParamErrorResponse-">onParamErrorCallback(ParamErrorResponse)</a></span> - 接口 中的方法com.amap.api.track.query.model.<a href="../com/amap/api/track/query/model/OnTrackListener.html" title="com.amap.api.track.query.model中的接口">OnTrackListener</a></dt>
<dd>
<div class="block">参数错误回调</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/query/model/OnTrackListener.html#onQueryTerminalCallback-com.amap.api.track.query.model.QueryTerminalResponse-">onQueryTerminalCallback(QueryTerminalResponse)</a></span> - 接口 中的方法com.amap.api.track.query.model.<a href="../com/amap/api/track/query/model/OnTrackListener.html" title="com.amap.api.track.query.model中的接口">OnTrackListener</a></dt>
<dd>
<div class="block">查询terminal的详细信息的回调</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/query/model/OnTrackListener.html#onQueryTrackCallback-com.amap.api.track.query.model.QueryTrackResponse-">onQueryTrackCallback(QueryTrackResponse)</a></span> - 接口 中的方法com.amap.api.track.query.model.<a href="../com/amap/api/track/query/model/OnTrackListener.html" title="com.amap.api.track.query.model中的接口">OnTrackListener</a></dt>
<dd>
<div class="block">查询终端轨迹信息的回调</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/OnTrackLifecycleListener.html#onStartGatherCallback-int-java.lang.String-">onStartGatherCallback(int, String)</a></span> - 接口 中的方法com.amap.api.track.<a href="../com/amap/api/track/OnTrackLifecycleListener.html" title="com.amap.api.track中的接口">OnTrackLifecycleListener</a></dt>
<dd>
<div class="block">开启采集回调接口</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/OnTrackLifecycleListener.html#onStartTrackCallback-int-java.lang.String-">onStartTrackCallback(int, String)</a></span> - 接口 中的方法com.amap.api.track.<a href="../com/amap/api/track/OnTrackLifecycleListener.html" title="com.amap.api.track中的接口">OnTrackLifecycleListener</a></dt>
<dd>
<div class="block">开启服务回调接口</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/OnTrackLifecycleListener.html#onStopGatherCallback-int-java.lang.String-">onStopGatherCallback(int, String)</a></span> - 接口 中的方法com.amap.api.track.<a href="../com/amap/api/track/OnTrackLifecycleListener.html" title="com.amap.api.track中的接口">OnTrackLifecycleListener</a></dt>
<dd>
<div class="block">停止采集回调接口</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/OnTrackLifecycleListener.html#onStopTrackCallback-int-java.lang.String-">onStopTrackCallback(int, String)</a></span> - 接口 中的方法com.amap.api.track.<a href="../com/amap/api/track/OnTrackLifecycleListener.html" title="com.amap.api.track中的接口">OnTrackLifecycleListener</a></dt>
<dd>
<div class="block">停止服务回调接口</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/OnCustomAttributeListener.html#onTrackAttributeCallback--">onTrackAttributeCallback()</a></span> - 接口 中的方法com.amap.api.track.<a href="../com/amap/api/track/OnCustomAttributeListener.html" title="com.amap.api.track中的接口">OnCustomAttributeListener</a></dt>
<dd>
<div class="block">轨迹属性回调接口。</div>
</dd>
<dt><a href="../com/amap/api/track/OnTrackLifecycleListener.html" title="com.amap.api.track中的接口"><span class="typeNameLink">OnTrackLifecycleListener</span></a> - <a href="../com/amap/api/track/package-summary.html">com.amap.api.track</a>中的接口</dt>
<dd>
<div class="block">用于监听服务的绑定和启停，以及采集的启停事件的监听器</div>
</dd>
<dt><a href="../com/amap/api/track/query/model/OnTrackListener.html" title="com.amap.api.track.query.model中的接口"><span class="typeNameLink">OnTrackListener</span></a> - <a href="../com/amap/api/track/query/model/package-summary.html">com.amap.api.track.query.model</a>中的接口</dt>
<dd>
<div class="block">监听轨迹相关的查询、创建请求结果的监听器</div>
</dd>
<dt><a href="../com/amap/api/track/query/entity/OrderMode.html" title="com.amap.api.track.query.entity中的类"><span class="typeNameLink">OrderMode</span></a> - <a href="../com/amap/api/track/query/entity/package-summary.html">com.amap.api.track.query.entity</a>中的类</dt>
<dd>
<div class="block">排序模式。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/query/entity/OrderMode.html#OrderMode--">OrderMode()</a></span> - 类 的构造器com.amap.api.track.query.entity.<a href="../com/amap/api/track/query/entity/OrderMode.html" title="com.amap.api.track.query.entity中的类">OrderMode</a></dt>
<dd>&nbsp;</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">G</a>&nbsp;<a href="index-7.html">H</a>&nbsp;<a href="index-8.html">I</a>&nbsp;<a href="index-9.html">L</a>&nbsp;<a href="index-10.html">M</a>&nbsp;<a href="index-11.html">N</a>&nbsp;<a href="index-12.html">O</a>&nbsp;<a href="index-13.html">P</a>&nbsp;<a href="index-14.html">Q</a>&nbsp;<a href="index-15.html">R</a>&nbsp;<a href="index-16.html">S</a>&nbsp;<a href="index-17.html">T</a>&nbsp;<a href="index-18.html">W</a>&nbsp;</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-11.html">上一个字母</a></li>
<li><a href="index-13.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-12.html" target="_top">框架</a></li>
<li><a href="index-12.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
