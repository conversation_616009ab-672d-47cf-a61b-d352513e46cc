<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>S - 索引</title>
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="S - \u7D22\u5F15";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-15.html">上一个字母</a></li>
<li><a href="index-17.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-16.html" target="_top">框架</a></li>
<li><a href="index-16.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="contentContainer"><a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">G</a>&nbsp;<a href="index-7.html">H</a>&nbsp;<a href="index-8.html">I</a>&nbsp;<a href="index-9.html">L</a>&nbsp;<a href="index-10.html">M</a>&nbsp;<a href="index-11.html">N</a>&nbsp;<a href="index-12.html">O</a>&nbsp;<a href="index-13.html">P</a>&nbsp;<a href="index-14.html">Q</a>&nbsp;<a href="index-15.html">R</a>&nbsp;<a href="index-16.html">S</a>&nbsp;<a href="index-17.html">T</a>&nbsp;<a href="index-18.html">W</a>&nbsp;<a name="I:S">
<!--   -->
</a>
<h2 class="title">S</h2>
<dl>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/ErrorCode.Response.html#SERVICE_NON_EXIST">SERVICE_NON_EXIST</a></span> - 类 中的静态变量com.amap.api.track.<a href="../com/amap/api/track/ErrorCode.Response.html" title="com.amap.api.track中的类">ErrorCode.Response</a></dt>
<dd>
<div class="block">Service 不存在</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/ErrorCode.TrackListen.html#SERVICE_NOT_STARTED">SERVICE_NOT_STARTED</a></span> - 类 中的静态变量com.amap.api.track.<a href="../com/amap/api/track/ErrorCode.TrackListen.html" title="com.amap.api.track中的类">ErrorCode.TrackListen</a></dt>
<dd>
<div class="block">寻迹服务未启动，请先启动</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/AMapTrackClient.html#setCacheSize-int-">setCacheSize(int)</a></span> - 类 中的方法com.amap.api.track.<a href="../com/amap/api/track/AMapTrackClient.html" title="com.amap.api.track中的类">AMapTrackClient</a></dt>
<dd>
<div class="block">设置缓存大小, 单位MB。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/AMapTrackClient.html#setInterval-int-int-">setInterval(int, int)</a></span> - 类 中的方法com.amap.api.track.<a href="../com/amap/api/track/AMapTrackClient.html" title="com.amap.api.track中的类">AMapTrackClient</a></dt>
<dd>
<div class="block">设置采集和打包位置数据的时间间隔
 该接口既可以在开启轨迹服务前调用，也可以在开启轨迹服务后调用。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/AMapTrackClient.html#setLocationMode-int-">setLocationMode(int)</a></span> - 类 中的方法com.amap.api.track.<a href="../com/amap/api/track/AMapTrackClient.html" title="com.amap.api.track中的类">AMapTrackClient</a></dt>
<dd>
<div class="block">设置定位模式。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/TrackParam.html#setNotification-android.app.Notification-">setNotification(Notification)</a></span> - 类 中的方法com.amap.api.track.<a href="../com/amap/api/track/TrackParam.html" title="com.amap.api.track中的类">TrackParam</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/AMapTrackClient.html#setOnCustomAttributeListener-com.amap.api.track.OnCustomAttributeListener-">setOnCustomAttributeListener(OnCustomAttributeListener)</a></span> - 类 中的方法com.amap.api.track.<a href="../com/amap/api/track/AMapTrackClient.html" title="com.amap.api.track中的类">AMapTrackClient</a></dt>
<dd>
<div class="block">设置自定义属性监听器
 该接口既可以在开启轨迹服务前调用，也可以在开启轨迹服务后调用。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/AMapTrackClient.html#setOnTrackListener-com.amap.api.track.OnTrackLifecycleListener-">setOnTrackListener(OnTrackLifecycleListener)</a></span> - 类 中的方法com.amap.api.track.<a href="../com/amap/api/track/AMapTrackClient.html" title="com.amap.api.track中的类">AMapTrackClient</a></dt>
<dd>
<div class="block">设置轨迹服务监听器
 该接口必须在开启轨迹服务后调用才会生效</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/AMapTrackClient.html#setProtocolType-int-">setProtocolType(int)</a></span> - 类 中的方法com.amap.api.track.<a href="../com/amap/api/track/AMapTrackClient.html" title="com.amap.api.track中的类">AMapTrackClient</a></dt>
<dd>
<div class="block">设置协议类型（http、https），默认为https
 该接口既可以在开启轨迹服务前调用，也可以在开启轨迹服务后调用。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/AMapTrackClient.html#setTrackId-long-">setTrackId(long)</a></span> - 类 中的方法com.amap.api.track.<a href="../com/amap/api/track/AMapTrackClient.html" title="com.amap.api.track中的类">AMapTrackClient</a></dt>
<dd>
<div class="block">设置轨迹id。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/TrackParam.html#setTrackId-long-">setTrackId(long)</a></span> - 类 中的方法com.amap.api.track.<a href="../com/amap/api/track/TrackParam.html" title="com.amap.api.track中的类">TrackParam</a></dt>
<dd>
<div class="block">设置上报轨迹id</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/ErrorCode.TrackListen.html#START_GATHER_ALREADY_STARTED">START_GATHER_ALREADY_STARTED</a></span> - 类 中的静态变量com.amap.api.track.<a href="../com/amap/api/track/ErrorCode.TrackListen.html" title="com.amap.api.track中的类">ErrorCode.TrackListen</a></dt>
<dd>
<div class="block">定位采集 已经启动</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/ErrorCode.TrackListen.html#START_GATHER_SUCEE">START_GATHER_SUCEE</a></span> - 类 中的静态变量com.amap.api.track.<a href="../com/amap/api/track/ErrorCode.TrackListen.html" title="com.amap.api.track中的类">ErrorCode.TrackListen</a></dt>
<dd>
<div class="block">定位采集 启动成功</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/ErrorCode.TrackListen.html#START_GATHER_TRACK_NOT_STARTED">START_GATHER_TRACK_NOT_STARTED</a></span> - 类 中的静态变量com.amap.api.track.<a href="../com/amap/api/track/ErrorCode.TrackListen.html" title="com.amap.api.track中的类">ErrorCode.TrackListen</a></dt>
<dd>
<div class="block">轨迹同步 未启动</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/ErrorCode.TrackListen.html#START_SERVICE_EX">START_SERVICE_EX</a></span> - 类 中的静态变量com.amap.api.track.<a href="../com/amap/api/track/ErrorCode.TrackListen.html" title="com.amap.api.track中的类">ErrorCode.TrackListen</a></dt>
<dd>
<div class="block">启动寻迹服务失败，寻迹服务启动出现异常</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/ErrorCode.TrackListen.html#START_TRACK_ALREADY_STARTED">START_TRACK_ALREADY_STARTED</a></span> - 类 中的静态变量com.amap.api.track.<a href="../com/amap/api/track/ErrorCode.TrackListen.html" title="com.amap.api.track中的类">ErrorCode.TrackListen</a></dt>
<dd>
<div class="block">轨迹同步已经启动</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/ErrorCode.TrackListen.html#START_TRACK_AUTH_CHECK_FAIL">START_TRACK_AUTH_CHECK_FAIL</a></span> - 类 中的静态变量com.amap.api.track.<a href="../com/amap/api/track/ErrorCode.TrackListen.html" title="com.amap.api.track中的类">ErrorCode.TrackListen</a></dt>
<dd>
<div class="block">鉴权失败</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/ErrorCode.TrackListen.html#START_TRACK_CREATE_TERMINAL_FAIL">START_TRACK_CREATE_TERMINAL_FAIL</a></span> - 类 中的静态变量com.amap.api.track.<a href="../com/amap/api/track/ErrorCode.TrackListen.html" title="com.amap.api.track中的类">ErrorCode.TrackListen</a></dt>
<dd>
<div class="block">创建terminal非法</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/ErrorCode.TrackListen.html#START_TRACK_CUSTOM_ATTRIBUTE_INVALID">START_TRACK_CUSTOM_ATTRIBUTE_INVALID</a></span> - 类 中的静态变量com.amap.api.track.<a href="../com/amap/api/track/ErrorCode.TrackListen.html" title="com.amap.api.track中的类">ErrorCode.TrackListen</a></dt>
<dd>
<div class="block">获取自定义参数时，出现异常</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/ErrorCode.TrackListen.html#START_TRACK_CUSTOM_ATTRIBUTE_INVALID_MSG">START_TRACK_CUSTOM_ATTRIBUTE_INVALID_MSG</a></span> - 类 中的静态变量com.amap.api.track.<a href="../com/amap/api/track/ErrorCode.TrackListen.html" title="com.amap.api.track中的类">ErrorCode.TrackListen</a></dt>
<dd>
<div class="block">获取自定义参数时，出现异常</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/ErrorCode.TrackListen.html#START_TRACK_NET_CONNECTED">START_TRACK_NET_CONNECTED</a></span> - 类 中的静态变量com.amap.api.track.<a href="../com/amap/api/track/ErrorCode.TrackListen.html" title="com.amap.api.track中的类">ErrorCode.TrackListen</a></dt>
<dd>
<div class="block">网络未连接</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/ErrorCode.TrackListen.html#START_TRACK_SERVICE_IS_INVALID">START_TRACK_SERVICE_IS_INVALID</a></span> - 类 中的静态变量com.amap.api.track.<a href="../com/amap/api/track/ErrorCode.TrackListen.html" title="com.amap.api.track中的类">ErrorCode.TrackListen</a></dt>
<dd>
<div class="block">serviceid 非法</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/ErrorCode.TrackListen.html#START_TRACK_SUCEE">START_TRACK_SUCEE</a></span> - 类 中的静态变量com.amap.api.track.<a href="../com/amap/api/track/ErrorCode.TrackListen.html" title="com.amap.api.track中的类">ErrorCode.TrackListen</a></dt>
<dd>
<div class="block">启动轨迹同步成功</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/ErrorCode.TrackListen.html#START_TRACK_SUCEE_NO_NETWORK">START_TRACK_SUCEE_NO_NETWORK</a></span> - 类 中的静态变量com.amap.api.track.<a href="../com/amap/api/track/ErrorCode.TrackListen.html" title="com.amap.api.track中的类">ErrorCode.TrackListen</a></dt>
<dd>
<div class="block">启动轨迹同步成功，但是网络未连接</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/ErrorCode.TrackListen.html#START_TRACK_TERMINAL_IS_INVALID">START_TRACK_TERMINAL_IS_INVALID</a></span> - 类 中的静态变量com.amap.api.track.<a href="../com/amap/api/track/ErrorCode.TrackListen.html" title="com.amap.api.track中的类">ErrorCode.TrackListen</a></dt>
<dd>
<div class="block">terminal 非法</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/ErrorCode.TrackListen.html#START_TRACK_TRACK_IS_INVALID">START_TRACK_TRACK_IS_INVALID</a></span> - 类 中的静态变量com.amap.api.track.<a href="../com/amap/api/track/ErrorCode.TrackListen.html" title="com.amap.api.track中的类">ErrorCode.TrackListen</a></dt>
<dd>
<div class="block">Track不能为null</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/AMapTrackClient.html#startGather-com.amap.api.track.OnTrackLifecycleListener-">startGather(OnTrackLifecycleListener)</a></span> - 类 中的方法com.amap.api.track.<a href="../com/amap/api/track/AMapTrackClient.html" title="com.amap.api.track中的类">AMapTrackClient</a></dt>
<dd>
<div class="block">开启轨迹采集</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/AMapTrackClient.html#startTrack-com.amap.api.track.TrackParam-com.amap.api.track.OnTrackLifecycleListener-">startTrack(TrackParam, OnTrackLifecycleListener)</a></span> - 类 中的方法com.amap.api.track.<a href="../com/amap/api/track/AMapTrackClient.html" title="com.amap.api.track中的类">AMapTrackClient</a></dt>
<dd>
<div class="block">开启轨迹服务
 在开启轨迹服务前，需要初始化Track，并在AndroidManifest.xml文件中配置API_KEY(AK)。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/ErrorCode.TrackListen.html#STOP_GATHER_GATHER_NOT_STARTED">STOP_GATHER_GATHER_NOT_STARTED</a></span> - 类 中的静态变量com.amap.api.track.<a href="../com/amap/api/track/ErrorCode.TrackListen.html" title="com.amap.api.track中的类">ErrorCode.TrackListen</a></dt>
<dd>
<div class="block">定位采集 未启动</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/ErrorCode.TrackListen.html#STOP_GATHER_SUCCE">STOP_GATHER_SUCCE</a></span> - 类 中的静态变量com.amap.api.track.<a href="../com/amap/api/track/ErrorCode.TrackListen.html" title="com.amap.api.track中的类">ErrorCode.TrackListen</a></dt>
<dd>
<div class="block">定位采集 停止成功</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/ErrorCode.TrackListen.html#STOP_GATHER_TRACK_NOT_STARTED">STOP_GATHER_TRACK_NOT_STARTED</a></span> - 类 中的静态变量com.amap.api.track.<a href="../com/amap/api/track/ErrorCode.TrackListen.html" title="com.amap.api.track中的类">ErrorCode.TrackListen</a></dt>
<dd>
<div class="block">停止采集失败，轨迹同步未启动</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/ErrorCode.TrackListen.html#STOP_TRACK_SUCCE">STOP_TRACK_SUCCE</a></span> - 类 中的静态变量com.amap.api.track.<a href="../com/amap/api/track/ErrorCode.TrackListen.html" title="com.amap.api.track中的类">ErrorCode.TrackListen</a></dt>
<dd>
<div class="block">轨迹同步 停止成功</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/AMapTrackClient.html#stopGather-com.amap.api.track.OnTrackLifecycleListener-">stopGather(OnTrackLifecycleListener)</a></span> - 类 中的方法com.amap.api.track.<a href="../com/amap/api/track/AMapTrackClient.html" title="com.amap.api.track中的类">AMapTrackClient</a></dt>
<dd>
<div class="block">停止轨迹采集</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/AMapTrackClient.html#stopTrack-com.amap.api.track.TrackParam-com.amap.api.track.OnTrackLifecycleListener-">stopTrack(TrackParam, OnTrackLifecycleListener)</a></span> - 类 中的方法com.amap.api.track.<a href="../com/amap/api/track/AMapTrackClient.html" title="com.amap.api.track中的类">AMapTrackClient</a></dt>
<dd>
<div class="block">停止轨迹服务</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/query/entity/RecoupMode.html#STRAIGHT_LINE">STRAIGHT_LINE</a></span> - 类 中的静态变量com.amap.api.track.query.entity.<a href="../com/amap/api/track/query/entity/RecoupMode.html" title="com.amap.api.track.query.entity中的类">RecoupMode</a></dt>
<dd>
<div class="block">代表用直线距离进行补点计算</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/ErrorCode.Response.html#SUCCESS">SUCCESS</a></span> - 类 中的静态变量com.amap.api.track.<a href="../com/amap/api/track/ErrorCode.Response.html" title="com.amap.api.track中的类">ErrorCode.Response</a></dt>
<dd>
<div class="block">成功</div>
</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">G</a>&nbsp;<a href="index-7.html">H</a>&nbsp;<a href="index-8.html">I</a>&nbsp;<a href="index-9.html">L</a>&nbsp;<a href="index-10.html">M</a>&nbsp;<a href="index-11.html">N</a>&nbsp;<a href="index-12.html">O</a>&nbsp;<a href="index-13.html">P</a>&nbsp;<a href="index-14.html">Q</a>&nbsp;<a href="index-15.html">R</a>&nbsp;<a href="index-16.html">S</a>&nbsp;<a href="index-17.html">T</a>&nbsp;<a href="index-18.html">W</a>&nbsp;</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-15.html">上一个字母</a></li>
<li><a href="index-17.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-16.html" target="_top">框架</a></li>
<li><a href="index-16.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
