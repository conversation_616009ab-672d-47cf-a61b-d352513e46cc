<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>L - 索引</title>
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="L - \u7D22\u5F15";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-8.html">上一个字母</a></li>
<li><a href="index-10.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-9.html" target="_top">框架</a></li>
<li><a href="index-9.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="contentContainer"><a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">G</a>&nbsp;<a href="index-7.html">H</a>&nbsp;<a href="index-8.html">I</a>&nbsp;<a href="index-9.html">L</a>&nbsp;<a href="index-10.html">M</a>&nbsp;<a href="index-11.html">N</a>&nbsp;<a href="index-12.html">O</a>&nbsp;<a href="index-13.html">P</a>&nbsp;<a href="index-14.html">Q</a>&nbsp;<a href="index-15.html">R</a>&nbsp;<a href="index-16.html">S</a>&nbsp;<a href="index-17.html">T</a>&nbsp;<a href="index-18.html">W</a>&nbsp;<a name="I:L">
<!--   -->
</a>
<h2 class="title">L</h2>
<dl>
<dt><a href="../com/amap/api/track/query/entity/LatestPoint.html" title="com.amap.api.track.query.entity中的类"><span class="typeNameLink">LatestPoint</span></a> - <a href="../com/amap/api/track/query/entity/package-summary.html">com.amap.api.track.query.entity</a>中的类</dt>
<dd>
<div class="block">终端位置</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/query/entity/LatestPoint.html#LatestPoint--">LatestPoint()</a></span> - 类 的构造器com.amap.api.track.query.entity.<a href="../com/amap/api/track/query/entity/LatestPoint.html" title="com.amap.api.track.query.entity中的类">LatestPoint</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/amap/api/track/query/model/LatestPointRequest.html" title="com.amap.api.track.query.model中的类"><span class="typeNameLink">LatestPointRequest</span></a> - <a href="../com/amap/api/track/query/model/package-summary.html">com.amap.api.track.query.model</a>中的类</dt>
<dd>
<div class="block">实时查询某终端位置

 通过指定 service 和 terminal，返回该 terminal 最后定位的经纬度</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/query/model/LatestPointRequest.html#LatestPointRequest-long-long-">LatestPointRequest(long, long)</a></span> - 类 的构造器com.amap.api.track.query.model.<a href="../com/amap/api/track/query/model/LatestPointRequest.html" title="com.amap.api.track.query.model中的类">LatestPointRequest</a></dt>
<dd>
<div class="block">构造请求
 <br>
 查询终端最后一次定位的经纬度</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/query/model/LatestPointRequest.html#LatestPointRequest-long-long-long-">LatestPointRequest(long, long, long)</a></span> - 类 的构造器com.amap.api.track.query.model.<a href="../com/amap/api/track/query/model/LatestPointRequest.html" title="com.amap.api.track.query.model中的类">LatestPointRequest</a></dt>
<dd>
<div class="block">构造请求
 <br>
 查询终端的某个特定轨迹下最后上报的轨迹点，若将轨迹id传-1可查询所有轨迹中最后一次上报的轨迹点</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/query/model/LatestPointRequest.html#LatestPointRequest-long-long-long-int-java.lang.String-">LatestPointRequest(long, long, long, int, String)</a></span> - 类 的构造器com.amap.api.track.query.model.<a href="../com/amap/api/track/query/model/LatestPointRequest.html" title="com.amap.api.track.query.model中的类">LatestPointRequest</a></dt>
<dd>
<div class="block">构造请求
 <br>
 查询终端的某个特定轨迹下最后上报的轨迹点，若将轨迹id传-1可查询所有轨迹中最后一次上报的轨迹点</div>
</dd>
<dt><a href="../com/amap/api/track/query/model/LatestPointResponse.html" title="com.amap.api.track.query.model中的类"><span class="typeNameLink">LatestPointResponse</span></a> - <a href="../com/amap/api/track/query/model/package-summary.html">com.amap.api.track.query.model</a>中的类</dt>
<dd>
<div class="block">实时查询终端位置返回结果</div>
</dd>
<dt><a href="../com/amap/api/track/query/entity/LocationMode.html" title="com.amap.api.track.query.entity中的类"><span class="typeNameLink">LocationMode</span></a> - <a href="../com/amap/api/track/query/entity/package-summary.html">com.amap.api.track.query.entity</a>中的类</dt>
<dd>
<div class="block">定位模式</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/query/entity/LocationMode.html#LocationMode--">LocationMode()</a></span> - 类 的构造器com.amap.api.track.query.entity.<a href="../com/amap/api/track/query/entity/LocationMode.html" title="com.amap.api.track.query.entity中的类">LocationMode</a></dt>
<dd>&nbsp;</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">G</a>&nbsp;<a href="index-7.html">H</a>&nbsp;<a href="index-8.html">I</a>&nbsp;<a href="index-9.html">L</a>&nbsp;<a href="index-10.html">M</a>&nbsp;<a href="index-11.html">N</a>&nbsp;<a href="index-12.html">O</a>&nbsp;<a href="index-13.html">P</a>&nbsp;<a href="index-14.html">Q</a>&nbsp;<a href="index-15.html">R</a>&nbsp;<a href="index-16.html">S</a>&nbsp;<a href="index-17.html">T</a>&nbsp;<a href="index-18.html">W</a>&nbsp;</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-8.html">上一个字母</a></li>
<li><a href="index-10.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-9.html" target="_top">框架</a></li>
<li><a href="index-9.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
