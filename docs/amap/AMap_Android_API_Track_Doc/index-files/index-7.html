<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>H - 索引</title>
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="H - \u7D22\u5F15";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-6.html">上一个字母</a></li>
<li><a href="index-8.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-7.html" target="_top">框架</a></li>
<li><a href="index-7.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="contentContainer"><a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">G</a>&nbsp;<a href="index-7.html">H</a>&nbsp;<a href="index-8.html">I</a>&nbsp;<a href="index-9.html">L</a>&nbsp;<a href="index-10.html">M</a>&nbsp;<a href="index-11.html">N</a>&nbsp;<a href="index-12.html">O</a>&nbsp;<a href="index-13.html">P</a>&nbsp;<a href="index-14.html">Q</a>&nbsp;<a href="index-15.html">R</a>&nbsp;<a href="index-16.html">S</a>&nbsp;<a href="index-17.html">T</a>&nbsp;<a href="index-18.html">W</a>&nbsp;<a name="I:H">
<!--   -->
</a>
<h2 class="title">H</h2>
<dl>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/query/entity/LocationMode.html#HIGHT_ACCURACY">HIGHT_ACCURACY</a></span> - 类 中的静态变量com.amap.api.track.query.entity.<a href="../com/amap/api/track/query/entity/LocationMode.html" title="com.amap.api.track.query.entity中的类">LocationMode</a></dt>
<dd>
<div class="block">高精度定位模式：在这种定位模式下，将同时使用高德网络定位和GPS定位,优先返回精度高的定位</div>
</dd>
<dt><a href="../com/amap/api/track/query/entity/HistoryTrack.html" title="com.amap.api.track.query.entity中的类"><span class="typeNameLink">HistoryTrack</span></a> - <a href="../com/amap/api/track/query/entity/package-summary.html">com.amap.api.track.query.entity</a>中的类</dt>
<dd>
<div class="block">查询终端行驶轨迹请求的返回结果</div>
</dd>
<dt><a href="../com/amap/api/track/query/model/HistoryTrackRequest.html" title="com.amap.api.track.query.model中的类"><span class="typeNameLink">HistoryTrackRequest</span></a> - <a href="../com/amap/api/track/query/model/package-summary.html">com.amap.api.track.query.model</a>中的类</dt>
<dd>
<div class="block">查询某个终端某段时间内的行驶轨迹及里程</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/query/model/HistoryTrackRequest.html#HistoryTrackRequest-long-long-long-long-">HistoryTrackRequest(long, long, long, long)</a></span> - 类 的构造器com.amap.api.track.query.model.<a href="../com/amap/api/track/query/model/HistoryTrackRequest.html" title="com.amap.api.track.query.model中的类">HistoryTrackRequest</a></dt>
<dd>
<div class="block">创建请求</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/query/model/HistoryTrackRequest.html#HistoryTrackRequest-long-long-long-long-int-int-int-int-int-int-java.lang.String-">HistoryTrackRequest(long, long, long, long, int, int, int, int, int, int, String)</a></span> - 类 的构造器com.amap.api.track.query.model.<a href="../com/amap/api/track/query/model/HistoryTrackRequest.html" title="com.amap.api.track.query.model中的类">HistoryTrackRequest</a></dt>
<dd>
<div class="block">创建请求</div>
</dd>
<dt><a href="../com/amap/api/track/query/model/HistoryTrackResponse.html" title="com.amap.api.track.query.model中的类"><span class="typeNameLink">HistoryTrackResponse</span></a> - <a href="../com/amap/api/track/query/model/package-summary.html">com.amap.api.track.query.model</a>中的类</dt>
<dd>
<div class="block">查询终端行驶里程轨迹及里程的请求结果</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/query/entity/ProtocolType.html#HTTP">HTTP</a></span> - 类 中的静态变量com.amap.api.track.query.entity.<a href="../com/amap/api/track/query/entity/ProtocolType.html" title="com.amap.api.track.query.entity中的类">ProtocolType</a></dt>
<dd>
<div class="block">使用Http进行请求</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/query/entity/ProtocolType.html#HTTPS">HTTPS</a></span> - 类 中的静态变量com.amap.api.track.query.entity.<a href="../com/amap/api/track/query/entity/ProtocolType.html" title="com.amap.api.track.query.entity中的类">ProtocolType</a></dt>
<dd>
<div class="block">使用Https进行请求</div>
</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">G</a>&nbsp;<a href="index-7.html">H</a>&nbsp;<a href="index-8.html">I</a>&nbsp;<a href="index-9.html">L</a>&nbsp;<a href="index-10.html">M</a>&nbsp;<a href="index-11.html">N</a>&nbsp;<a href="index-12.html">O</a>&nbsp;<a href="index-13.html">P</a>&nbsp;<a href="index-14.html">Q</a>&nbsp;<a href="index-15.html">R</a>&nbsp;<a href="index-16.html">S</a>&nbsp;<a href="index-17.html">T</a>&nbsp;<a href="index-18.html">W</a>&nbsp;</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-6.html">上一个字母</a></li>
<li><a href="index-8.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-7.html" target="_top">框架</a></li>
<li><a href="index-7.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
