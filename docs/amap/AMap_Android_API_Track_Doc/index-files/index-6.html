<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>G - 索引</title>
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="G - \u7D22\u5F15";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-5.html">上一个字母</a></li>
<li><a href="index-7.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-6.html" target="_top">框架</a></li>
<li><a href="index-6.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="contentContainer"><a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">G</a>&nbsp;<a href="index-7.html">H</a>&nbsp;<a href="index-8.html">I</a>&nbsp;<a href="index-9.html">L</a>&nbsp;<a href="index-10.html">M</a>&nbsp;<a href="index-11.html">N</a>&nbsp;<a href="index-12.html">O</a>&nbsp;<a href="index-13.html">P</a>&nbsp;<a href="index-14.html">Q</a>&nbsp;<a href="index-15.html">R</a>&nbsp;<a href="index-16.html">S</a>&nbsp;<a href="index-17.html">T</a>&nbsp;<a href="index-18.html">W</a>&nbsp;<a name="I:G">
<!--   -->
</a>
<h2 class="title">G</h2>
<dl>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/query/entity/Point.html#getAccuracy--">getAccuracy()</a></span> - 类 中的方法com.amap.api.track.query.entity.<a href="../com/amap/api/track/query/entity/Point.html" title="com.amap.api.track.query.entity中的类">Point</a></dt>
<dd>
<div class="block">获取定位精度，纠偏后的点没有该信息</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/query/entity/HistoryTrack.html#getCount--">getCount()</a></span> - 类 中的方法com.amap.api.track.query.entity.<a href="../com/amap/api/track/query/entity/HistoryTrack.html" title="com.amap.api.track.query.entity中的类">HistoryTrack</a></dt>
<dd>
<div class="block">获取符合要求点的个数</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/query/entity/Track.html#getCount--">getCount()</a></span> - 类 中的方法com.amap.api.track.query.entity.<a href="../com/amap/api/track/query/entity/Track.html" title="com.amap.api.track.query.entity中的类">Track</a></dt>
<dd>
<div class="block">获取轨迹点的数量</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/query/model/QueryTrackResponse.html#getCount--">getCount()</a></span> - 类 中的方法com.amap.api.track.query.model.<a href="../com/amap/api/track/query/model/QueryTrackResponse.html" title="com.amap.api.track.query.model中的类">QueryTrackResponse</a></dt>
<dd>
<div class="block">获取返回的轨迹条数</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/query/entity/Point.html#getDirection--">getDirection()</a></span> - 类 中的方法com.amap.api.track.query.entity.<a href="../com/amap/api/track/query/entity/Point.html" title="com.amap.api.track.query.entity中的类">Point</a></dt>
<dd>
<div class="block">获取方向，取值范围0～359，0代表正北，顺时针方向，纠偏后的点没有该信息</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/query/entity/HistoryTrack.html#getDistance--">getDistance()</a></span> - 类 中的方法com.amap.api.track.query.entity.<a href="../com/amap/api/track/query/entity/HistoryTrack.html" title="com.amap.api.track.query.entity中的类">HistoryTrack</a></dt>
<dd>
<div class="block">获取这段轨迹的总里程</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/query/entity/Track.html#getDistance--">getDistance()</a></span> - 类 中的方法com.amap.api.track.query.entity.<a href="../com/amap/api/track/query/entity/Track.html" title="com.amap.api.track.query.entity中的类">Track</a></dt>
<dd>
<div class="block">获取轨迹距离长度，单位是米</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/query/model/DistanceResponse.html#getDistance--">getDistance()</a></span> - 类 中的方法com.amap.api.track.query.model.<a href="../com/amap/api/track/query/model/DistanceResponse.html" title="com.amap.api.track.query.model中的类">DistanceResponse</a></dt>
<dd>
<div class="block">获取查询到的终端行驶里程</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/query/entity/HistoryTrack.html#getEndPoint--">getEndPoint()</a></span> - 类 中的方法com.amap.api.track.query.entity.<a href="../com/amap/api/track/query/entity/HistoryTrack.html" title="com.amap.api.track.query.entity中的类">HistoryTrack</a></dt>
<dd>
<div class="block">获取终点相关信息</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/query/entity/Track.html#getEndPoint--">getEndPoint()</a></span> - 类 中的方法com.amap.api.track.query.entity.<a href="../com/amap/api/track/query/entity/Track.html" title="com.amap.api.track.query.entity中的类">Track</a></dt>
<dd>
<div class="block">获取终点相关信息</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/query/model/BaseResponse.html#getErrorCode--">getErrorCode()</a></span> - 类 中的方法com.amap.api.track.query.model.<a href="../com/amap/api/track/query/model/BaseResponse.html" title="com.amap.api.track.query.model中的类">BaseResponse</a></dt>
<dd>
<div class="block">获取错误码</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/query/model/BaseResponse.html#getErrorMsg--">getErrorMsg()</a></span> - 类 中的方法com.amap.api.track.query.model.<a href="../com/amap/api/track/query/model/BaseResponse.html" title="com.amap.api.track.query.model中的类">BaseResponse</a></dt>
<dd>
<div class="block">获取错误信息提示</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/query/entity/Point.html#getHeight--">getHeight()</a></span> - 类 中的方法com.amap.api.track.query.entity.<a href="../com/amap/api/track/query/entity/Point.html" title="com.amap.api.track.query.entity中的类">Point</a></dt>
<dd>
<div class="block">获取高度，单位为米，纠偏后的点没有该信息</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/query/model/HistoryTrackResponse.html#getHistoryTrack--">getHistoryTrack()</a></span> - 类 中的方法com.amap.api.track.query.model.<a href="../com/amap/api/track/query/model/HistoryTrackResponse.html" title="com.amap.api.track.query.model中的类">HistoryTrackResponse</a></dt>
<dd>
<div class="block">获取行驶轨迹信息</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/query/model/BaseResponse.html#getInfoMsg-long-long-long-">getInfoMsg(long, long, long)</a></span> - 类 中的方法com.amap.api.track.query.model.<a href="../com/amap/api/track/query/model/BaseResponse.html" title="com.amap.api.track.query.model中的类">BaseResponse</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/query/entity/Point.html#getLat--">getLat()</a></span> - 类 中的方法com.amap.api.track.query.entity.<a href="../com/amap/api/track/query/entity/Point.html" title="com.amap.api.track.query.entity中的类">Point</a></dt>
<dd>
<div class="block">获取轨迹点纬度</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/query/model/LatestPointResponse.html#getLatestPoint--">getLatestPoint()</a></span> - 类 中的方法com.amap.api.track.query.model.<a href="../com/amap/api/track/query/model/LatestPointResponse.html" title="com.amap.api.track.query.model中的类">LatestPointResponse</a></dt>
<dd>
<div class="block">获取查询到的终端位置</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/query/entity/Point.html#getLng--">getLng()</a></span> - 类 中的方法com.amap.api.track.query.entity.<a href="../com/amap/api/track/query/entity/Point.html" title="com.amap.api.track.query.entity中的类">Point</a></dt>
<dd>
<div class="block">获取轨迹点经度</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/query/entity/TrackPoint.html#getLocatetime--">getLocatetime()</a></span> - 类 中的方法com.amap.api.track.query.entity.<a href="../com/amap/api/track/query/entity/TrackPoint.html" title="com.amap.api.track.query.entity中的类">TrackPoint</a></dt>
<dd>
<div class="block">获取轨迹点定位时间，单位是ms</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/query/entity/TrackPoint.html#getLocation--">getLocation()</a></span> - 类 中的方法com.amap.api.track.query.entity.<a href="../com/amap/api/track/query/entity/TrackPoint.html" title="com.amap.api.track.query.entity中的类">TrackPoint</a></dt>
<dd>
<div class="block">获取该轨迹点经纬度</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/TrackParam.html#getNotification--">getNotification()</a></span> - 类 中的方法com.amap.api.track.<a href="../com/amap/api/track/TrackParam.html" title="com.amap.api.track中的类">TrackParam</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/query/model/BaseResponse.html#getOuterErrorMsg-long-long-long-">getOuterErrorMsg(long, long, long)</a></span> - 类 中的方法com.amap.api.track.query.model.<a href="../com/amap/api/track/query/model/BaseResponse.html" title="com.amap.api.track.query.model中的类">BaseResponse</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/query/entity/LatestPoint.html#getPoint--">getPoint()</a></span> - 类 中的方法com.amap.api.track.query.entity.<a href="../com/amap/api/track/query/entity/LatestPoint.html" title="com.amap.api.track.query.entity中的类">LatestPoint</a></dt>
<dd>
<div class="block">获取位置信息</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/query/entity/HistoryTrack.html#getPoints--">getPoints()</a></span> - 类 中的方法com.amap.api.track.query.entity.<a href="../com/amap/api/track/query/entity/HistoryTrack.html" title="com.amap.api.track.query.entity中的类">HistoryTrack</a></dt>
<dd>
<div class="block">获取这段轨迹的轨迹点列表</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/query/entity/Track.html#getPoints--">getPoints()</a></span> - 类 中的方法com.amap.api.track.query.entity.<a href="../com/amap/api/track/query/entity/Track.html" title="com.amap.api.track.query.entity中的类">Track</a></dt>
<dd>
<div class="block">获取轨迹点数组</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/query/entity/Point.html#getProps--">getProps()</a></span> - 类 中的方法com.amap.api.track.query.entity.<a href="../com/amap/api/track/query/entity/Point.html" title="com.amap.api.track.query.entity中的类">Point</a></dt>
<dd>
<div class="block">获取用户自定义属性</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/TrackParam.html#getSid--">getSid()</a></span> - 类 中的方法com.amap.api.track.<a href="../com/amap/api/track/TrackParam.html" title="com.amap.api.track中的类">TrackParam</a></dt>
<dd>
<div class="block">获取轨迹服务ID</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/query/entity/HistoryTrack.html#getStartPoint--">getStartPoint()</a></span> - 类 中的方法com.amap.api.track.query.entity.<a href="../com/amap/api/track/query/entity/HistoryTrack.html" title="com.amap.api.track.query.entity中的类">HistoryTrack</a></dt>
<dd>
<div class="block">获取起点相关信息</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/query/entity/Track.html#getStartPoint--">getStartPoint()</a></span> - 类 中的方法com.amap.api.track.query.entity.<a href="../com/amap/api/track/query/entity/Track.html" title="com.amap.api.track.query.entity中的类">Track</a></dt>
<dd>
<div class="block">获取起点相关信息</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/query/model/LatestPointResponse.html#getTermainl--">getTermainl()</a></span> - 类 中的方法com.amap.api.track.query.model.<a href="../com/amap/api/track/query/model/LatestPointResponse.html" title="com.amap.api.track.query.model中的类">LatestPointResponse</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/query/model/AddTerminalResponse.html#getTid--">getTid()</a></span> - 类 中的方法com.amap.api.track.query.model.<a href="../com/amap/api/track/query/model/AddTerminalResponse.html" title="com.amap.api.track.query.model中的类">AddTerminalResponse</a></dt>
<dd>
<div class="block">获取创建的终端id</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/query/model/QueryTerminalResponse.html#getTid--">getTid()</a></span> - 类 中的方法com.amap.api.track.query.model.<a href="../com/amap/api/track/query/model/QueryTerminalResponse.html" title="com.amap.api.track.query.model中的类">QueryTerminalResponse</a></dt>
<dd>
<div class="block">获取终端id</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/TrackParam.html#getTid--">getTid()</a></span> - 类 中的方法com.amap.api.track.<a href="../com/amap/api/track/TrackParam.html" title="com.amap.api.track中的类">TrackParam</a></dt>
<dd>
<div class="block">获取entity标识</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/query/entity/Point.html#getTime--">getTime()</a></span> - 类 中的方法com.amap.api.track.query.entity.<a href="../com/amap/api/track/query/entity/Point.html" title="com.amap.api.track.query.entity中的类">Point</a></dt>
<dd>
<div class="block">获取轨迹点上传时间</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/query/entity/Track.html#getTime--">getTime()</a></span> - 类 中的方法com.amap.api.track.query.entity.<a href="../com/amap/api/track/query/entity/Track.html" title="com.amap.api.track.query.entity中的类">Track</a></dt>
<dd>
<div class="block">获取轨迹持续时间</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/AMapTrackClient.html#getTrackId--">getTrackId()</a></span> - 类 中的方法com.amap.api.track.<a href="../com/amap/api/track/AMapTrackClient.html" title="com.amap.api.track中的类">AMapTrackClient</a></dt>
<dd>
<div class="block">获得当前设置的轨迹id
 该方法只有在已经启动轨迹服务后才会生效</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/TrackParam.html#getTrackId--">getTrackId()</a></span> - 类 中的方法com.amap.api.track.<a href="../com/amap/api/track/TrackParam.html" title="com.amap.api.track中的类">TrackParam</a></dt>
<dd>
<div class="block">获取轨迹id</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/query/model/QueryTrackResponse.html#getTracks--">getTracks()</a></span> - 类 中的方法com.amap.api.track.query.model.<a href="../com/amap/api/track/query/model/QueryTrackResponse.html" title="com.amap.api.track.query.model中的类">QueryTrackResponse</a></dt>
<dd>
<div class="block">获取轨迹数组</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/query/entity/Track.html#getTrid--">getTrid()</a></span> - 类 中的方法com.amap.api.track.query.entity.<a href="../com/amap/api/track/query/entity/Track.html" title="com.amap.api.track.query.entity中的类">Track</a></dt>
<dd>
<div class="block">获取轨迹id</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/query/model/AddTrackResponse.html#getTrid--">getTrid()</a></span> - 类 中的方法com.amap.api.track.query.model.<a href="../com/amap/api/track/query/model/AddTrackResponse.html" title="com.amap.api.track.query.model中的类">AddTrackResponse</a></dt>
<dd>
<div class="block">获取新创建轨迹的轨迹id</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/AMapTrackClient.html#getVersion--">getVersion()</a></span> - 类 中的静态方法com.amap.api.track.<a href="../com/amap/api/track/AMapTrackClient.html" title="com.amap.api.track中的类">AMapTrackClient</a></dt>
<dd>
<div class="block">获得版本号</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/query/entity/ThresholdMode.html#GPS_THRESHOLD">GPS_THRESHOLD</a></span> - 类 中的静态变量com.amap.api.track.query.entity.<a href="../com/amap/api/track/query/entity/ThresholdMode.html" title="com.amap.api.track.query.entity中的类">ThresholdMode</a></dt>
<dd>
<div class="block">若只需保留 GPS 定位点,则建议设为：20；</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/track/query/entity/ThresholdMode.html#GPS_WIFI_THRESHOLD">GPS_WIFI_THRESHOLD</a></span> - 类 中的静态变量com.amap.api.track.query.entity.<a href="../com/amap/api/track/query/entity/ThresholdMode.html" title="com.amap.api.track.query.entity中的类">ThresholdMode</a></dt>
<dd>
<div class="block">若需保留 GPS 和 Wi-Fi 定位点，去除基站定位点，则建议设为：100</div>
</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">G</a>&nbsp;<a href="index-7.html">H</a>&nbsp;<a href="index-8.html">I</a>&nbsp;<a href="index-9.html">L</a>&nbsp;<a href="index-10.html">M</a>&nbsp;<a href="index-11.html">N</a>&nbsp;<a href="index-12.html">O</a>&nbsp;<a href="index-13.html">P</a>&nbsp;<a href="index-14.html">Q</a>&nbsp;<a href="index-15.html">R</a>&nbsp;<a href="index-16.html">S</a>&nbsp;<a href="index-17.html">T</a>&nbsp;<a href="index-18.html">W</a>&nbsp;</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-5.html">上一个字母</a></li>
<li><a href="index-7.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-6.html" target="_top">框架</a></li>
<li><a href="index-6.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
