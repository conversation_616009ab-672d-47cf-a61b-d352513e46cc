<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>常量字段值</title>
<link rel="stylesheet" type="text/css" href="stylesheet.css" title="Style">
<script type="text/javascript" src="script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="\u5E38\u91CF\u5B57\u6BB5\u503C";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="overview-tree.html">树</a></li>
<li><a href="deprecated-list.html">已过时</a></li>
<li><a href="index-files/index-1.html">索引</a></li>
<li><a href="help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个</li>
<li>下一个</li>
</ul>
<ul class="navList">
<li><a href="index.html?constant-values.html" target="_top">框架</a></li>
<li><a href="constant-values.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 title="常量字段值" class="title">常量字段值</h1>
<h2 title="目录">目录</h2>
<ul>
<li><a href="#com.amap">com.amap.*</a></li>
</ul>
</div>
<div class="constantValuesContainer"><a name="com.amap">
<!--   -->
</a>
<h2 title="com.amap">com.amap.*</h2>
<ul class="blockList">
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="常量字段值表, 列表常量字段和值">
<caption><span>com.amap.api.track.<a href="com/amap/api/track/ErrorCode.Response.html" title="com.amap.api.track中的类">ErrorCode.Response</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th scope="col">常量字段</th>
<th class="colLast" scope="col">值</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.track.ErrorCode.Response.BAD_NETWORK">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/track/ErrorCode.Response.html#BAD_NETWORK">BAD_NETWORK</a></code></td>
<td class="colLast"><code>3002</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.track.ErrorCode.Response.BEYOND_LIMIT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/track/ErrorCode.Response.html#BEYOND_LIMIT">BEYOND_LIMIT</a></code></td>
<td class="colLast"><code>20150</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.track.ErrorCode.Response.NET_RES_FAIL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/track/ErrorCode.Response.html#NET_RES_FAIL">NET_RES_FAIL</a></code></td>
<td class="colLast"><code>3003</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.track.ErrorCode.Response.PARAM_ERROR_CODE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/track/ErrorCode.Response.html#PARAM_ERROR_CODE">PARAM_ERROR_CODE</a></code></td>
<td class="colLast"><code>3001</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.track.ErrorCode.Response.REQ_NOT_VALID">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/track/ErrorCode.Response.html#REQ_NOT_VALID">REQ_NOT_VALID</a></code></td>
<td class="colLast"><code>3004</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.track.ErrorCode.Response.SERVICE_NON_EXIST">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/track/ErrorCode.Response.html#SERVICE_NON_EXIST">SERVICE_NON_EXIST</a></code></td>
<td class="colLast"><code>20050</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.track.ErrorCode.Response.SUCCESS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/track/ErrorCode.Response.html#SUCCESS">SUCCESS</a></code></td>
<td class="colLast"><code>10000</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.track.ErrorCode.Response.TERMINAL_ALREADY_EXIST">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/track/ErrorCode.Response.html#TERMINAL_ALREADY_EXIST">TERMINAL_ALREADY_EXIST</a></code></td>
<td class="colLast"><code>20009</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.track.ErrorCode.Response.TERMINAL_CREATE_FAIL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/track/ErrorCode.Response.html#TERMINAL_CREATE_FAIL">TERMINAL_CREATE_FAIL</a></code></td>
<td class="colLast"><code>20052</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.track.ErrorCode.Response.TERMINAL_NON_EXIST">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/track/ErrorCode.Response.html#TERMINAL_NON_EXIST">TERMINAL_NON_EXIST</a></code></td>
<td class="colLast"><code>20051</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.track.ErrorCode.Response.TRID_NON_EXIST">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/track/ErrorCode.Response.html#TRID_NON_EXIST">TRID_NON_EXIST</a></code></td>
<td class="colLast"><code>20010</code></td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="常量字段值表, 列表常量字段和值">
<caption><span>com.amap.api.track.<a href="com/amap/api/track/ErrorCode.TrackListen.html" title="com.amap.api.track中的类">ErrorCode.TrackListen</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th scope="col">常量字段</th>
<th class="colLast" scope="col">值</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.track.ErrorCode.TrackListen.BIND_SUCCESS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/track/ErrorCode.TrackListen.html#BIND_SUCCESS">BIND_SUCCESS</a></code></td>
<td class="colLast"><code>2001</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.track.ErrorCode.TrackListen.REMOTE_EX">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/track/ErrorCode.TrackListen.html#REMOTE_EX">REMOTE_EX</a></code></td>
<td class="colLast"><code>2004</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.track.ErrorCode.TrackListen.SERVICE_NOT_STARTED">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/track/ErrorCode.TrackListen.html#SERVICE_NOT_STARTED">SERVICE_NOT_STARTED</a></code></td>
<td class="colLast"><code>2003</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.track.ErrorCode.TrackListen.START_GATHER_ALREADY_STARTED">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/track/ErrorCode.TrackListen.html#START_GATHER_ALREADY_STARTED">START_GATHER_ALREADY_STARTED</a></code></td>
<td class="colLast"><code>2009</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.track.ErrorCode.TrackListen.START_GATHER_SUCEE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/track/ErrorCode.TrackListen.html#START_GATHER_SUCEE">START_GATHER_SUCEE</a></code></td>
<td class="colLast"><code>2010</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.track.ErrorCode.TrackListen.START_GATHER_TRACK_NOT_STARTED">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/track/ErrorCode.TrackListen.html#START_GATHER_TRACK_NOT_STARTED">START_GATHER_TRACK_NOT_STARTED</a></code></td>
<td class="colLast"><code>2008</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.track.ErrorCode.TrackListen.START_SERVICE_EX">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/track/ErrorCode.TrackListen.html#START_SERVICE_EX">START_SERVICE_EX</a></code></td>
<td class="colLast"><code>2002</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.track.ErrorCode.TrackListen.START_TRACK_ALREADY_STARTED">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/track/ErrorCode.TrackListen.html#START_TRACK_ALREADY_STARTED">START_TRACK_ALREADY_STARTED</a></code></td>
<td class="colLast"><code>2007</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.track.ErrorCode.TrackListen.START_TRACK_AUTH_CHECK_FAIL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/track/ErrorCode.TrackListen.html#START_TRACK_AUTH_CHECK_FAIL">START_TRACK_AUTH_CHECK_FAIL</a></code></td>
<td class="colLast"><code>2017</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.track.ErrorCode.TrackListen.START_TRACK_CREATE_TERMINAL_FAIL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/track/ErrorCode.TrackListen.html#START_TRACK_CREATE_TERMINAL_FAIL">START_TRACK_CREATE_TERMINAL_FAIL</a></code></td>
<td class="colLast"><code>2021</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.track.ErrorCode.TrackListen.START_TRACK_CUSTOM_ATTRIBUTE_INVALID">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/track/ErrorCode.TrackListen.html#START_TRACK_CUSTOM_ATTRIBUTE_INVALID">START_TRACK_CUSTOM_ATTRIBUTE_INVALID</a></code></td>
<td class="colLast"><code>2031</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.track.ErrorCode.TrackListen.START_TRACK_CUSTOM_ATTRIBUTE_INVALID_MSG">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/track/ErrorCode.TrackListen.html#START_TRACK_CUSTOM_ATTRIBUTE_INVALID_MSG">START_TRACK_CUSTOM_ATTRIBUTE_INVALID_MSG</a></code></td>
<td class="colLast"><code>"\u81ea\u5b9a\u4e49\u53c2\u6570\u83b7\u53d6\u5f02\u5e38"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.track.ErrorCode.TrackListen.START_TRACK_NET_CONNECTED">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/track/ErrorCode.TrackListen.html#START_TRACK_NET_CONNECTED">START_TRACK_NET_CONNECTED</a></code></td>
<td class="colLast"><code>2016</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.track.ErrorCode.TrackListen.START_TRACK_SERVICE_IS_INVALID">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/track/ErrorCode.TrackListen.html#START_TRACK_SERVICE_IS_INVALID">START_TRACK_SERVICE_IS_INVALID</a></code></td>
<td class="colLast"><code>2019</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.track.ErrorCode.TrackListen.START_TRACK_SUCEE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/track/ErrorCode.TrackListen.html#START_TRACK_SUCEE">START_TRACK_SUCEE</a></code></td>
<td class="colLast"><code>2005</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.track.ErrorCode.TrackListen.START_TRACK_SUCEE_NO_NETWORK">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/track/ErrorCode.TrackListen.html#START_TRACK_SUCEE_NO_NETWORK">START_TRACK_SUCEE_NO_NETWORK</a></code></td>
<td class="colLast"><code>2006</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.track.ErrorCode.TrackListen.START_TRACK_TERMINAL_IS_INVALID">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/track/ErrorCode.TrackListen.html#START_TRACK_TERMINAL_IS_INVALID">START_TRACK_TERMINAL_IS_INVALID</a></code></td>
<td class="colLast"><code>2020</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.track.ErrorCode.TrackListen.START_TRACK_TRACK_IS_INVALID">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/track/ErrorCode.TrackListen.html#START_TRACK_TRACK_IS_INVALID">START_TRACK_TRACK_IS_INVALID</a></code></td>
<td class="colLast"><code>2018</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.track.ErrorCode.TrackListen.STOP_GATHER_GATHER_NOT_STARTED">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/track/ErrorCode.TrackListen.html#STOP_GATHER_GATHER_NOT_STARTED">STOP_GATHER_GATHER_NOT_STARTED</a></code></td>
<td class="colLast"><code>2012</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.track.ErrorCode.TrackListen.STOP_GATHER_SUCCE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/track/ErrorCode.TrackListen.html#STOP_GATHER_SUCCE">STOP_GATHER_SUCCE</a></code></td>
<td class="colLast"><code>2013</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.track.ErrorCode.TrackListen.STOP_GATHER_TRACK_NOT_STARTED">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/track/ErrorCode.TrackListen.html#STOP_GATHER_TRACK_NOT_STARTED">STOP_GATHER_TRACK_NOT_STARTED</a></code></td>
<td class="colLast"><code>2011</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.track.ErrorCode.TrackListen.STOP_TRACK_SUCCE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/track/ErrorCode.TrackListen.html#STOP_TRACK_SUCCE">STOP_TRACK_SUCCE</a></code></td>
<td class="colLast"><code>2014</code></td>
</tr>
</tbody>
</table>
</li>
</ul>
<ul class="blockList">
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="常量字段值表, 列表常量字段和值">
<caption><span>com.amap.api.track.query.entity.<a href="com/amap/api/track/query/entity/CorrectMode.html" title="com.amap.api.track.query.entity中的类">CorrectMode</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th scope="col">常量字段</th>
<th class="colLast" scope="col">值</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.track.query.entity.CorrectMode.DRIVING">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/track/query/entity/CorrectMode.html#DRIVING">DRIVING</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.track.query.entity.CorrectMode.NONE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/track/query/entity/CorrectMode.html#NONE">NONE</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="常量字段值表, 列表常量字段和值">
<caption><span>com.amap.api.track.query.entity.<a href="com/amap/api/track/query/entity/DenoiseMode.html" title="com.amap.api.track.query.entity中的类">DenoiseMode</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th scope="col">常量字段</th>
<th class="colLast" scope="col">值</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.track.query.entity.DenoiseMode.DENOSE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/track/query/entity/DenoiseMode.html#DENOSE">DENOSE</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.track.query.entity.DenoiseMode.NON_DENOSE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/track/query/entity/DenoiseMode.html#NON_DENOSE">NON_DENOSE</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="常量字段值表, 列表常量字段和值">
<caption><span>com.amap.api.track.query.entity.<a href="com/amap/api/track/query/entity/DriveMode.html" title="com.amap.api.track.query.entity中的类">DriveMode</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th scope="col">常量字段</th>
<th class="colLast" scope="col">值</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.track.query.entity.DriveMode.DRIVING">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/track/query/entity/DriveMode.html#DRIVING">DRIVING</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.track.query.entity.DriveMode.RIDING">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/track/query/entity/DriveMode.html#RIDING">RIDING</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.track.query.entity.DriveMode.WALKING">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/track/query/entity/DriveMode.html#WALKING">WALKING</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="常量字段值表, 列表常量字段和值">
<caption><span>com.amap.api.track.query.entity.<a href="com/amap/api/track/query/entity/LocationMode.html" title="com.amap.api.track.query.entity中的类">LocationMode</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th scope="col">常量字段</th>
<th class="colLast" scope="col">值</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.track.query.entity.LocationMode.BATTERY_SAVING">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/track/query/entity/LocationMode.html#BATTERY_SAVING">BATTERY_SAVING</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.track.query.entity.LocationMode.DEVICE_SENSORS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/track/query/entity/LocationMode.html#DEVICE_SENSORS">DEVICE_SENSORS</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.track.query.entity.LocationMode.HIGHT_ACCURACY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/track/query/entity/LocationMode.html#HIGHT_ACCURACY">HIGHT_ACCURACY</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="常量字段值表, 列表常量字段和值">
<caption><span>com.amap.api.track.query.entity.<a href="com/amap/api/track/query/entity/MapMatchMode.html" title="com.amap.api.track.query.entity中的类">MapMatchMode</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th scope="col">常量字段</th>
<th class="colLast" scope="col">值</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.track.query.entity.MapMatchMode.MAPMATCH">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/track/query/entity/MapMatchMode.html#MAPMATCH">MAPMATCH</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.track.query.entity.MapMatchMode.NON_MAPMATCH">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/track/query/entity/MapMatchMode.html#NON_MAPMATCH">NON_MAPMATCH</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="常量字段值表, 列表常量字段和值">
<caption><span>com.amap.api.track.query.entity.<a href="com/amap/api/track/query/entity/OrderMode.html" title="com.amap.api.track.query.entity中的类">OrderMode</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th scope="col">常量字段</th>
<th class="colLast" scope="col">值</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.track.query.entity.OrderMode.NEW_FIRST">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/track/query/entity/OrderMode.html#NEW_FIRST">NEW_FIRST</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.track.query.entity.OrderMode.OLD_FIRST">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/track/query/entity/OrderMode.html#OLD_FIRST">OLD_FIRST</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="常量字段值表, 列表常量字段和值">
<caption><span>com.amap.api.track.query.entity.<a href="com/amap/api/track/query/entity/ProtocolType.html" title="com.amap.api.track.query.entity中的类">ProtocolType</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th scope="col">常量字段</th>
<th class="colLast" scope="col">值</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.track.query.entity.ProtocolType.HTTP">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/track/query/entity/ProtocolType.html#HTTP">HTTP</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.track.query.entity.ProtocolType.HTTPS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/track/query/entity/ProtocolType.html#HTTPS">HTTPS</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="常量字段值表, 列表常量字段和值">
<caption><span>com.amap.api.track.query.entity.<a href="com/amap/api/track/query/entity/RecoupMode.html" title="com.amap.api.track.query.entity中的类">RecoupMode</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th scope="col">常量字段</th>
<th class="colLast" scope="col">值</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.track.query.entity.RecoupMode.DRIVING">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/track/query/entity/RecoupMode.html#DRIVING">DRIVING</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.track.query.entity.RecoupMode.STRAIGHT_LINE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/track/query/entity/RecoupMode.html#STRAIGHT_LINE">STRAIGHT_LINE</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="常量字段值表, 列表常量字段和值">
<caption><span>com.amap.api.track.query.entity.<a href="com/amap/api/track/query/entity/ThresholdMode.html" title="com.amap.api.track.query.entity中的类">ThresholdMode</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th scope="col">常量字段</th>
<th class="colLast" scope="col">值</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.track.query.entity.ThresholdMode.GPS_THRESHOLD">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/track/query/entity/ThresholdMode.html#GPS_THRESHOLD">GPS_THRESHOLD</a></code></td>
<td class="colLast"><code>20</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.track.query.entity.ThresholdMode.GPS_WIFI_THRESHOLD">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/track/query/entity/ThresholdMode.html#GPS_WIFI_THRESHOLD">GPS_WIFI_THRESHOLD</a></code></td>
<td class="colLast"><code>100</code></td>
</tr>
</tbody>
</table>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="overview-tree.html">树</a></li>
<li><a href="deprecated-list.html">已过时</a></li>
<li><a href="index-files/index-1.html">索引</a></li>
<li><a href="help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个</li>
<li>下一个</li>
</ul>
<ul class="navList">
<li><a href="index.html?constant-values.html" target="_top">框架</a></li>
<li><a href="constant-values.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
