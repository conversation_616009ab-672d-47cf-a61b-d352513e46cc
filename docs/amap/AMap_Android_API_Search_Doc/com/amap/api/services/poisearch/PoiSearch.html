<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>PoiSearch</title>
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="PoiSearch";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":42,"i1":42,"i2":42,"i3":42,"i4":42,"i5":42,"i6":42,"i7":42,"i8":42,"i9":42,"i10":42};
var tabs = {65535:["t0","所有方法"],2:["t2","实例方法"],8:["t4","具体方法"],32:["t6","已过时的方法"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/PoiSearch.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/poisearch/PoiResultV2.html" title="com.amap.api.services.poisearch中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/amap/api/services/poisearch/PoiSearch.OnPoiSearchListener.html" title="com.amap.api.services.poisearch中的接口"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/poisearch/PoiSearch.html" target="_top">框架</a></li>
<li><a href="PoiSearch.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li><a href="#nested.class.summary">嵌套</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li><a href="#field.detail">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.amap.api.services.poisearch</div>
<h2 title="类 PoiSearch" class="title">类 PoiSearch</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.amap.api.services.poisearch.PoiSearch</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;
<div class="block"><span class="deprecationComment">自v9.4.0废弃,推荐使用 <a href="../../../../../com/amap/api/services/poisearch/PoiSearchV2.html" title="com.amap.api.services.poisearch中的类"><code>PoiSearchV2</code></a></span></div>
</div>
<br>
<pre>public class <span class="typeNameLabel">PoiSearch</span>
extends java.lang.Object</pre>
<div class="block">本类为POI（Point Of Interest，兴趣点）搜索的“入口”类，定义此类，开始搜索。在类PoiSearch 中，还定义了两个内部类，Query 与SearchBound，请用这两个内部类设定搜索参数。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.0</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>嵌套类概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="嵌套类概要表, 列表嵌套类和解释">
<caption><span>嵌套类</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">类和说明</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static interface&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/poisearch/PoiSearch.OnPoiSearchListener.html" title="com.amap.api.services.poisearch中的接口">PoiSearch.OnPoiSearchListener</a></span></code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;</div>
<div class="block">本类为POI（Point Of Interest，兴趣点）搜索结果的异步处理回调接口。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/poisearch/PoiSearch.Query.html" title="com.amap.api.services.poisearch中的类">PoiSearch.Query</a></span></code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;</div>
<div class="block">此类定义了搜索的关键字，类别及城市。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/poisearch/PoiSearch.SearchBound.html" title="com.amap.api.services.poisearch中的类">PoiSearch.SearchBound</a></span></code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;</div>
<div class="block">此类定义了查询圆形和查询矩形，查询返回的POI的位置在此圆形或矩形内。</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>字段概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="字段概要表, 列表字段和解释">
<caption><span>字段</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">字段和说明</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/poisearch/PoiSearch.html#CHINESE">CHINESE</a></span></code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;</div>
<div class="block">语言设置常量。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/poisearch/PoiSearch.html#ENGLISH">ENGLISH</a></span></code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;</div>
<div class="block">语言设置常量。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/poisearch/PoiSearch.html#EXTENSIONS_ALL">EXTENSIONS_ALL</a></span></code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;</div>
<div class="block">扩展字段all，会返回完整参数</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/poisearch/PoiSearch.html#EXTENSIONS_BASE">EXTENSIONS_BASE</a></span></code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;</div>
<div class="block">扩展字段base，会返回部分参数</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/poisearch/PoiSearch.html#PoiSearch-Context-com.amap.api.services.poisearch.PoiSearch.Query-">PoiSearch</a></span>(Context&nbsp;context,
         <a href="../../../../../com/amap/api/services/poisearch/PoiSearch.Query.html" title="com.amap.api.services.poisearch中的类">PoiSearch.Query</a>&nbsp;query)</code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;</div>
<div class="block">根据给定的参数构造一个PoiSearch 的新对象。</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>所有方法</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">实例方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">具体方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t6" class="tableTab"><span><a href="javascript:show(32);">已过时的方法</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/services/poisearch/PoiSearch.SearchBound.html" title="com.amap.api.services.poisearch中的类">PoiSearch.SearchBound</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/poisearch/PoiSearch.html#getBound--">getBound</a></span>()</code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;</div>
<div class="block">返回查询矩形。</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/poisearch/PoiSearch.html#getLanguage--">getLanguage</a></span>()</code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;
<div class="block"><span class="deprecationComment">自V2.5.0后废弃，请参考 <a href="../../../../../com/amap/api/services/core/ServiceSettings.html#getLanguage--"><code>ServiceSettings.getLanguage()</code></a></span></div>
</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/services/poisearch/PoiSearch.Query.html" title="com.amap.api.services.poisearch中的类">PoiSearch.Query</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/poisearch/PoiSearch.html#getQuery--">getQuery</a></span>()</code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;</div>
<div class="block">返回查询条件。</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/services/poisearch/PoiResult.html" title="com.amap.api.services.poisearch中的类">PoiResult</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/poisearch/PoiSearch.html#searchPOI--">searchPOI</a></span>()</code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;</div>
<div class="block">查询POI。</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/poisearch/PoiSearch.html#searchPOIAsyn--">searchPOIAsyn</a></span>()</code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;</div>
<div class="block">查询POI异步接口。</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/services/core/PoiItem.html" title="com.amap.api.services.core中的类">PoiItem</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/poisearch/PoiSearch.html#searchPOIId-java.lang.String-">searchPOIId</a></span>(java.lang.String&nbsp;poiID)</code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;</div>
<div class="block">已知poiid信息（点击地图底图），搜索POI的详细信息，同步</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/poisearch/PoiSearch.html#searchPOIIdAsyn-java.lang.String-">searchPOIIdAsyn</a></span>(java.lang.String&nbsp;poiID)</code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;</div>
<div class="block">已知poiid信息（点击地图底图），搜索POI的详细信息，异步</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/poisearch/PoiSearch.html#setBound-com.amap.api.services.poisearch.PoiSearch.SearchBound-">setBound</a></span>(<a href="../../../../../com/amap/api/services/poisearch/PoiSearch.SearchBound.html" title="com.amap.api.services.poisearch中的类">PoiSearch.SearchBound</a>&nbsp;bnd)</code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;</div>
<div class="block">设置查询矩形。</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/poisearch/PoiSearch.html#setLanguage-java.lang.String-">setLanguage</a></span>(java.lang.String&nbsp;language)</code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;
<div class="block"><span class="deprecationComment">自V2.5.0后废弃，请参考 <a href="../../../../../com/amap/api/services/core/ServiceSettings.html#setLanguage-java.lang.String-"><code>ServiceSettings.setLanguage(String)</code></a></span></div>
</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/poisearch/PoiSearch.html#setOnPoiSearchListener-com.amap.api.services.poisearch.PoiSearch.OnPoiSearchListener-">setOnPoiSearchListener</a></span>(<a href="../../../../../com/amap/api/services/poisearch/PoiSearch.OnPoiSearchListener.html" title="com.amap.api.services.poisearch中的接口">PoiSearch.OnPoiSearchListener</a>&nbsp;listener)</code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;</div>
<div class="block">设置查询监听接口。</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/poisearch/PoiSearch.html#setQuery-com.amap.api.services.poisearch.PoiSearch.Query-">setQuery</a></span>(<a href="../../../../../com/amap/api/services/poisearch/PoiSearch.Query.html" title="com.amap.api.services.poisearch中的类">PoiSearch.Query</a>&nbsp;query)</code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;</div>
<div class="block">设置查询条件。</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>字段详细资料</h3>
<a name="ENGLISH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENGLISH</h4>
<pre>public static final&nbsp;java.lang.String ENGLISH</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;</div>
<div class="block">语言设置常量。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.3.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.poisearch.PoiSearch.ENGLISH">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="CHINESE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CHINESE</h4>
<pre>public static final&nbsp;java.lang.String CHINESE</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;</div>
<div class="block">语言设置常量。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.3.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.poisearch.PoiSearch.CHINESE">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="EXTENSIONS_ALL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>EXTENSIONS_ALL</h4>
<pre>public static final&nbsp;java.lang.String EXTENSIONS_ALL</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;</div>
<div class="block">扩展字段all，会返回完整参数</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>7.6.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.poisearch.PoiSearch.EXTENSIONS_ALL">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="EXTENSIONS_BASE">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>EXTENSIONS_BASE</h4>
<pre>public static final&nbsp;java.lang.String EXTENSIONS_BASE</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;</div>
<div class="block">扩展字段base，会返回部分参数</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>7.6.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.poisearch.PoiSearch.EXTENSIONS_BASE">常量字段值</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="PoiSearch-Context-com.amap.api.services.poisearch.PoiSearch.Query-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>PoiSearch</h4>
<pre>public&nbsp;PoiSearch(Context&nbsp;context,
                 <a href="../../../../../com/amap/api/services/poisearch/PoiSearch.Query.html" title="com.amap.api.services.poisearch中的类">PoiSearch.Query</a>&nbsp;query)
          throws <a href="../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;</div>
<div class="block">根据给定的参数构造一个PoiSearch 的新对象。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>context</code> - 对应的Context。</dd>
<dd><code>query</code> - 查询条件。</dd>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code><a href="../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></code></dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="setOnPoiSearchListener-com.amap.api.services.poisearch.PoiSearch.OnPoiSearchListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setOnPoiSearchListener</h4>
<pre>public&nbsp;void&nbsp;setOnPoiSearchListener(<a href="../../../../../com/amap/api/services/poisearch/PoiSearch.OnPoiSearchListener.html" title="com.amap.api.services.poisearch中的接口">PoiSearch.OnPoiSearchListener</a>&nbsp;listener)</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;</div>
<div class="block">设置查询监听接口。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>listener</code> - 查询监听接口。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="setLanguage-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLanguage</h4>
<pre>public&nbsp;void&nbsp;setLanguage(java.lang.String&nbsp;language)</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;<span class="deprecationComment">自V2.5.0后废弃，请参考 <a href="../../../../../com/amap/api/services/core/ServiceSettings.html#setLanguage-java.lang.String-"><code>ServiceSettings.setLanguage(String)</code></a></span></div>
<div class="block">设置POI搜索返回结果语言。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>language</code> - 支持ENGLISH和CHINESE。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.3.1</dd>
</dl>
</li>
</ul>
<a name="getLanguage--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLanguage</h4>
<pre>public&nbsp;java.lang.String&nbsp;getLanguage()</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;<span class="deprecationComment">自V2.5.0后废弃，请参考 <a href="../../../../../com/amap/api/services/core/ServiceSettings.html#getLanguage--"><code>ServiceSettings.getLanguage()</code></a></span></div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.3.1</dd>
</dl>
</li>
</ul>
<a name="searchPOI--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>searchPOI</h4>
<pre>public&nbsp;<a href="../../../../../com/amap/api/services/poisearch/PoiResult.html" title="com.amap.api.services.poisearch中的类">PoiResult</a>&nbsp;searchPOI()
                    throws <a href="../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;</div>
<div class="block">查询POI。
 <p>
 如果此时矩形查询（SearchBound）已定义，则搜索范围为该矩形内的符合条件的POI，否则如果此时查询条件（Query）中的行政区划代码已定义，则搜索该城市（地区）内的所有符合条件的POI,如上述两个条件均未定义，则搜索范围为全国。为了性能及结果的清晰，强烈建议定义一个范围：矩形或行政区划代码均可。整个查询的语义为查找符合查询关键字，
 符合查询类型编码组合及在指定范围内的所有POI。
 </p>
 <p>
 此接口在网络连接出现问题的情况下，会抛出AMapException。<br>

 </p></div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>查询POI 的结果，结果是分页的，每页最多30 个。通过类PoiPagedResult 的实例可以检索每一页。</dd>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code>AMapException。</code></dd>
<dd><code><a href="../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></code></dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.0</dd>
</dl>
</li>
</ul>
<a name="searchPOIAsyn--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>searchPOIAsyn</h4>
<pre>public&nbsp;void&nbsp;searchPOIAsyn()</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;</div>
<div class="block">查询POI异步接口。 如果此时矩形查询（SearchBound）已定义，则搜索范围为该矩形内的符合条件的POI，否则如果此时查询条件（Query）中的行政区划代码已定义，则搜索该城市（地区）内的所有符合条件的POI,如上述两个条件均未定义，则搜索范围为全国。建议定义一个范围：矩形或行政区划代码均可。整个查询的语义为查找符合查询关键字，
 符合查询类型编码组合及在指定范围内的指定POI。查询POI 的结果，结果是分页的，每页最多30 个。 此方法为异步处理。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="searchPOIId-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>searchPOIId</h4>
<pre>public&nbsp;<a href="../../../../../com/amap/api/services/core/PoiItem.html" title="com.amap.api.services.core中的类">PoiItem</a>&nbsp;searchPOIId(java.lang.String&nbsp;poiID)
                    throws <a href="../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;</div>
<div class="block">已知poiid信息（点击地图底图），搜索POI的详细信息，同步</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>poiID</code> - poi的ID</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>poi的信息，如POI名称、类型、城市编码等。</dd>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code><a href="../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></code></dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.8.0</dd>
</dl>
</li>
</ul>
<a name="searchPOIIdAsyn-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>searchPOIIdAsyn</h4>
<pre>public&nbsp;void&nbsp;searchPOIIdAsyn(java.lang.String&nbsp;poiID)</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;</div>
<div class="block">已知poiid信息（点击地图底图），搜索POI的详细信息，异步</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>poiID</code> - poi的ID</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.8.0</dd>
</dl>
</li>
</ul>
<a name="setQuery-com.amap.api.services.poisearch.PoiSearch.Query-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setQuery</h4>
<pre>public&nbsp;void&nbsp;setQuery(<a href="../../../../../com/amap/api/services/poisearch/PoiSearch.Query.html" title="com.amap.api.services.poisearch中的类">PoiSearch.Query</a>&nbsp;query)</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;</div>
<div class="block">设置查询条件。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>query</code> - 新的查询条件。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.0</dd>
</dl>
</li>
</ul>
<a name="setBound-com.amap.api.services.poisearch.PoiSearch.SearchBound-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBound</h4>
<pre>public&nbsp;void&nbsp;setBound(<a href="../../../../../com/amap/api/services/poisearch/PoiSearch.SearchBound.html" title="com.amap.api.services.poisearch中的类">PoiSearch.SearchBound</a>&nbsp;bnd)</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;</div>
<div class="block">设置查询矩形。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>bnd</code> - 新的查询矩形。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.0</dd>
</dl>
</li>
</ul>
<a name="getQuery--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getQuery</h4>
<pre>public&nbsp;<a href="../../../../../com/amap/api/services/poisearch/PoiSearch.Query.html" title="com.amap.api.services.poisearch中的类">PoiSearch.Query</a>&nbsp;getQuery()</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;</div>
<div class="block">返回查询条件。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>查询条件。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.0</dd>
</dl>
</li>
</ul>
<a name="getBound--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getBound</h4>
<pre>public&nbsp;<a href="../../../../../com/amap/api/services/poisearch/PoiSearch.SearchBound.html" title="com.amap.api.services.poisearch中的类">PoiSearch.SearchBound</a>&nbsp;getBound()</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;</div>
<div class="block">返回查询矩形。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>查询矩形。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/PoiSearch.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/poisearch/PoiResultV2.html" title="com.amap.api.services.poisearch中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/amap/api/services/poisearch/PoiSearch.OnPoiSearchListener.html" title="com.amap.api.services.poisearch中的接口"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/poisearch/PoiSearch.html" target="_top">框架</a></li>
<li><a href="PoiSearch.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li><a href="#nested.class.summary">嵌套</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li><a href="#field.detail">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
