<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>PoiSearchV2.SearchBound</title>
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="PoiSearchV2.SearchBound";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10};
var tabs = {65535:["t0","所有方法"],2:["t2","实例方法"],8:["t4","具体方法"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/PoiSearchV2.SearchBound.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/poisearch/PoiSearchV2.Query.html" title="com.amap.api.services.poisearch中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/amap/api/services/poisearch/PoiSearchV2.ShowFields.html" title="com.amap.api.services.poisearch中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/poisearch/PoiSearchV2.SearchBound.html" target="_top">框架</a></li>
<li><a href="PoiSearchV2.SearchBound.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li><a href="#field.summary">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li><a href="#field.detail">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.amap.api.services.poisearch</div>
<h2 title="类 PoiSearchV2.SearchBound" class="title">类 PoiSearchV2.SearchBound</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.amap.api.services.poisearch.PoiSearchV2.SearchBound</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>所有已实现的接口:</dt>
<dd>java.lang.Cloneable</dd>
</dl>
<dl>
<dt>封闭类:</dt>
<dd><a href="../../../../../com/amap/api/services/poisearch/PoiSearchV2.html" title="com.amap.api.services.poisearch中的类">PoiSearchV2</a></dd>
</dl>
<hr>
<br>
<pre>public static class <span class="typeNameLabel">PoiSearchV2.SearchBound</span>
extends java.lang.Object
implements java.lang.Cloneable</pre>
<div class="block">此类定义了查询圆形和查询矩形，查询返回的POI的位置在此圆形或矩形内。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.4.0</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>字段概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="字段概要表, 列表字段和解释">
<caption><span>字段</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">字段和说明</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/poisearch/PoiSearchV2.SearchBound.html#BOUND_SHAPE">BOUND_SHAPE</a></span></code>
<div class="block">圆形区域</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/poisearch/PoiSearchV2.SearchBound.html#ELLIPSE_SHAPE">ELLIPSE_SHAPE</a></span></code>
<div class="block">椭圆区域</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/poisearch/PoiSearchV2.SearchBound.html#POLYGON_SHAPE">POLYGON_SHAPE</a></span></code>
<div class="block">多边形区域</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/poisearch/PoiSearchV2.SearchBound.html#RECTANGLE_SHAPE">RECTANGLE_SHAPE</a></span></code>
<div class="block">矩形区域</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/poisearch/PoiSearchV2.SearchBound.html#SearchBound-com.amap.api.services.core.LatLonPoint-int-">SearchBound</a></span>(<a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&nbsp;center,
           int&nbsp;radiusInMeters)</code>
<div class="block">根据给定的参数来构造PoiSearchV2.SearchBound 的新对象，默认由近到远排序。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/poisearch/PoiSearchV2.SearchBound.html#SearchBound-com.amap.api.services.core.LatLonPoint-int-boolean-">SearchBound</a></span>(<a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&nbsp;center,
           int&nbsp;radiusInMeters,
           boolean&nbsp;isDistanceSort)</code>
<div class="block">根据给定的参数来构造PoiSearchV2.SearchBound 的新对象，默认由近到远排序。</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/poisearch/PoiSearchV2.SearchBound.html#SearchBound-com.amap.api.services.core.LatLonPoint-com.amap.api.services.core.LatLonPoint-">SearchBound</a></span>(<a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&nbsp;lowerLeft,
           <a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&nbsp;upperRight)</code>
<div class="block">根据给定的参数来构造PoiSearchV2.SearchBound 的新对象。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/poisearch/PoiSearchV2.SearchBound.html#SearchBound-java.util.List-">SearchBound</a></span>(java.util.List&lt;<a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&gt;&nbsp;list)</code>
<div class="block">根据给定的参数来构造PoiSearchV2.SearchBound 的新对象。</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>所有方法</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">实例方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">具体方法</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/poisearch/PoiSearchV2.SearchBound.html#getCenter--">getCenter</a></span>()</code>
<div class="block">返回矩形中心点坐标。</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/poisearch/PoiSearchV2.SearchBound.html#getLowerLeft--">getLowerLeft</a></span>()</code>
<div class="block">返回矩形左下角坐标。</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/poisearch/PoiSearchV2.SearchBound.html#getPolyGonList--">getPolyGonList</a></span>()</code>
<div class="block">返回首尾相接的几何点，可以组成多边形。</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/poisearch/PoiSearchV2.SearchBound.html#getRange--">getRange</a></span>()</code>
<div class="block">返回查询范围半径。</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/poisearch/PoiSearchV2.SearchBound.html#getShape--">getShape</a></span>()</code>
<div class="block">返回查询范围形状。</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/poisearch/PoiSearchV2.SearchBound.html#getUpperRight--">getUpperRight</a></span>()</code>
<div class="block">返回矩形右上角坐标。</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/poisearch/PoiSearchV2.SearchBound.html#isDistanceSort--">isDistanceSort</a></span>()</code>
<div class="block">返回是否按照距离排序。</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>字段详细资料</h3>
<a name="BOUND_SHAPE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BOUND_SHAPE</h4>
<pre>public static final&nbsp;java.lang.String BOUND_SHAPE</pre>
<div class="block">圆形区域</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.4.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.poisearch.PoiSearchV2.SearchBound.BOUND_SHAPE">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="POLYGON_SHAPE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>POLYGON_SHAPE</h4>
<pre>public static final&nbsp;java.lang.String POLYGON_SHAPE</pre>
<div class="block">多边形区域</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.4.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.poisearch.PoiSearchV2.SearchBound.POLYGON_SHAPE">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="RECTANGLE_SHAPE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RECTANGLE_SHAPE</h4>
<pre>public static final&nbsp;java.lang.String RECTANGLE_SHAPE</pre>
<div class="block">矩形区域</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.4.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.poisearch.PoiSearchV2.SearchBound.RECTANGLE_SHAPE">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="ELLIPSE_SHAPE">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>ELLIPSE_SHAPE</h4>
<pre>public static final&nbsp;java.lang.String ELLIPSE_SHAPE</pre>
<div class="block">椭圆区域</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.4.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.poisearch.PoiSearchV2.SearchBound.ELLIPSE_SHAPE">常量字段值</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="SearchBound-com.amap.api.services.core.LatLonPoint-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SearchBound</h4>
<pre>public&nbsp;SearchBound(<a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&nbsp;center,
                   int&nbsp;radiusInMeters)</pre>
<div class="block">根据给定的参数来构造PoiSearchV2.SearchBound 的新对象，默认由近到远排序。
 <p>
 如果超出中国边界或者radiusInMeters<=0，则抛出IllegalArgumentException 异常。
 </p></div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>center</code> - 该范围的中心点。</dd>
<dd><code>radiusInMeters</code> - 半径，单位：米。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.4.0</dd>
</dl>
</li>
</ul>
<a name="SearchBound-com.amap.api.services.core.LatLonPoint-int-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SearchBound</h4>
<pre>public&nbsp;SearchBound(<a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&nbsp;center,
                   int&nbsp;radiusInMeters,
                   boolean&nbsp;isDistanceSort)</pre>
<div class="block">根据给定的参数来构造PoiSearchV2.SearchBound 的新对象，默认由近到远排序。 如果超出中国边界或者radiusInMeters<=0，则抛出IllegalArgumentException 异常。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>center</code> - 该范围的中心点。</dd>
<dd><code>radiusInMeters</code> - 半径，单位：米。</dd>
<dd><code>isDistanceSort</code> - 是否按照距离排序。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.4.0</dd>
</dl>
</li>
</ul>
<a name="SearchBound-com.amap.api.services.core.LatLonPoint-com.amap.api.services.core.LatLonPoint-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SearchBound</h4>
<pre>public&nbsp;SearchBound(<a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&nbsp;lowerLeft,
                   <a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&nbsp;upperRight)</pre>
<div class="block">根据给定的参数来构造PoiSearchV2.SearchBound 的新对象。
 <p>
 如果超出中国边界或者lowerLeft>=upperRight，则抛出IllegalArgumentException 异常。
 </p></div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>lowerLeft</code> - 矩形的左下角。</dd>
<dd><code>upperRight</code> - 矩形的右上角。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.4.0</dd>
</dl>
</li>
</ul>
<a name="SearchBound-java.util.List-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>SearchBound</h4>
<pre>public&nbsp;SearchBound(java.util.List&lt;<a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&gt;&nbsp;list)</pre>
<div class="block">根据给定的参数来构造PoiSearchV2.SearchBound 的新对象。 如果超出中国边界或者list不是首尾相接的几何点，并且不为多边形，则抛出IllegalArgumentException 异常。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>list</code> - 首尾相接的几何点，可以组成多边形。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.4.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="getLowerLeft--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLowerLeft</h4>
<pre>public&nbsp;<a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&nbsp;getLowerLeft()</pre>
<div class="block">返回矩形左下角坐标。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>矩形左下角坐标。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.4.0</dd>
</dl>
</li>
</ul>
<a name="getUpperRight--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getUpperRight</h4>
<pre>public&nbsp;<a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&nbsp;getUpperRight()</pre>
<div class="block">返回矩形右上角坐标。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>矩形右上角坐标。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.4.0</dd>
</dl>
</li>
</ul>
<a name="getCenter--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCenter</h4>
<pre>public&nbsp;<a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&nbsp;getCenter()</pre>
<div class="block">返回矩形中心点坐标。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>矩形中心点坐标。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.4.0</dd>
</dl>
</li>
</ul>
<a name="getRange--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRange</h4>
<pre>public&nbsp;int&nbsp;getRange()</pre>
<div class="block">返回查询范围半径。默认 3000</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>查询范围半径。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.4.0</dd>
</dl>
</li>
</ul>
<a name="getShape--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getShape</h4>
<pre>public&nbsp;java.lang.String&nbsp;getShape()</pre>
<div class="block">返回查询范围形状。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>查询范围形状。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.4.0</dd>
</dl>
</li>
</ul>
<a name="isDistanceSort--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isDistanceSort</h4>
<pre>public&nbsp;boolean&nbsp;isDistanceSort()</pre>
<div class="block">返回是否按照距离排序。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>true 按照距离排序，false不按照距离排序。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.4.0</dd>
</dl>
</li>
</ul>
<a name="getPolyGonList--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getPolyGonList</h4>
<pre>public&nbsp;java.util.List&lt;<a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&gt;&nbsp;getPolyGonList()</pre>
<div class="block">返回首尾相接的几何点，可以组成多边形。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>组成多边形的点列表</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.4.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/PoiSearchV2.SearchBound.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/poisearch/PoiSearchV2.Query.html" title="com.amap.api.services.poisearch中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/amap/api/services/poisearch/PoiSearchV2.ShowFields.html" title="com.amap.api.services.poisearch中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/poisearch/PoiSearchV2.SearchBound.html" target="_top">框架</a></li>
<li><a href="PoiSearchV2.SearchBound.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li><a href="#field.summary">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li><a href="#field.detail">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
