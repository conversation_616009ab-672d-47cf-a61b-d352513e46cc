<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>com.amap.api.services.poisearch</title>
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="com.amap.api.services.poisearch";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li class="navBarCell1Rev">程序包</li>
<li>类</li>
<li><a href="package-use.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/nearby/package-summary.html">上一个程序包</a></li>
<li><a href="../../../../../com/amap/api/services/road/package-summary.html">下一个程序包</a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/poisearch/package-summary.html" target="_top">框架</a></li>
<li><a href="package-summary.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 title="程序包" class="title">程序包&nbsp;com.amap.api.services.poisearch</h1>
<div class="docSummary">
<div class="block">
POI查询包，包含了兴趣点的详细信息。</div>
</div>
<p>请参阅:&nbsp;<a href="#package.description">说明</a></p>
</div>
<div class="contentContainer">
<ul class="blockList">
<li class="blockList">
<table class="typeSummary" border="0" cellpadding="3" cellspacing="0" summary="接口概要表, 列表接口和解释">
<caption><span>接口概要</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">接口</th>
<th class="colLast" scope="col">说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/poisearch/PoiSearch.OnPoiSearchListener.html" title="com.amap.api.services.poisearch中的接口">PoiSearch.OnPoiSearchListener</a></td>
<td class="colLast">
<div class="block">本类为POI（Point Of Interest，兴趣点）搜索结果的异步处理回调接口。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/poisearch/PoiSearchV2.OnPoiSearchListener.html" title="com.amap.api.services.poisearch中的接口">PoiSearchV2.OnPoiSearchListener</a></td>
<td class="colLast">
<div class="block">本类为POI（Point Of Interest，兴趣点）搜索结果的异步处理回调接口。</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="typeSummary" border="0" cellpadding="3" cellspacing="0" summary="类概要表, 列表类和解释">
<caption><span>类概要</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">类</th>
<th class="colLast" scope="col">说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/poisearch/Business.html" title="com.amap.api.services.poisearch中的类">Business</a></td>
<td class="colLast">
<div class="block">定义了一个POI商业信息类。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/poisearch/IndoorData.html" title="com.amap.api.services.poisearch中的类">IndoorData</a></td>
<td class="colLast">
<div class="block">室内地图相关数据</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/poisearch/IndoorDataV2.html" title="com.amap.api.services.poisearch中的类">IndoorDataV2</a></td>
<td class="colLast">
<div class="block">室内地图相关数据</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/poisearch/Photo.html" title="com.amap.api.services.poisearch中的类">Photo</a></td>
<td class="colLast">
<div class="block">POI图片类</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/poisearch/PoiItemExtension.html" title="com.amap.api.services.poisearch中的类">PoiItemExtension</a></td>
<td class="colLast">
<div class="block">定义了一个POI深度信息类。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/poisearch/PoiNavi.html" title="com.amap.api.services.poisearch中的类">PoiNavi</a></td>
<td class="colLast">
<div class="block">POI导航类</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/poisearch/PoiResult.html" title="com.amap.api.services.poisearch中的类">PoiResult</a></td>
<td class="colLast">
<div class="block">POI（Point Of Interest，兴趣点）搜索结果分页显示。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/poisearch/PoiResultV2.html" title="com.amap.api.services.poisearch中的类">PoiResultV2</a></td>
<td class="colLast">
<div class="block">POI（Point Of Interest，兴趣点）搜索结果分页显示。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/poisearch/PoiSearch.html" title="com.amap.api.services.poisearch中的类">PoiSearch</a></td>
<td class="colLast">已过时
<div class="block"><span class="deprecationComment">自v9.4.0废弃,推荐使用 <a href="../../../../../com/amap/api/services/poisearch/PoiSearchV2.html" title="com.amap.api.services.poisearch中的类"><code>PoiSearchV2</code></a></span></div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/poisearch/PoiSearch.Query.html" title="com.amap.api.services.poisearch中的类">PoiSearch.Query</a></td>
<td class="colLast">
<div class="block">此类定义了搜索的关键字，类别及城市。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/poisearch/PoiSearch.SearchBound.html" title="com.amap.api.services.poisearch中的类">PoiSearch.SearchBound</a></td>
<td class="colLast">
<div class="block">此类定义了查询圆形和查询矩形，查询返回的POI的位置在此圆形或矩形内。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/poisearch/PoiSearchV2.html" title="com.amap.api.services.poisearch中的类">PoiSearchV2</a></td>
<td class="colLast">
<div class="block">本类为POI（Point Of Interest，兴趣点）搜索的“入口”类，定义此类，开始搜索。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/poisearch/PoiSearchV2.Query.html" title="com.amap.api.services.poisearch中的类">PoiSearchV2.Query</a></td>
<td class="colLast">
<div class="block">此类定义了搜索的关键字，类别及城市。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/poisearch/PoiSearchV2.SearchBound.html" title="com.amap.api.services.poisearch中的类">PoiSearchV2.SearchBound</a></td>
<td class="colLast">
<div class="block">此类定义了查询圆形和查询矩形，查询返回的POI的位置在此圆形或矩形内。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/poisearch/PoiSearchV2.ShowFields.html" title="com.amap.api.services.poisearch中的类">PoiSearchV2.ShowFields</a></td>
<td class="colLast">
<div class="block">扩展字段</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/poisearch/SubPoiItem.html" title="com.amap.api.services.poisearch中的类">SubPoiItem</a></td>
<td class="colLast">
<div class="block">定义了一个子POI类</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/poisearch/SubPoiItemV2.html" title="com.amap.api.services.poisearch中的类">SubPoiItemV2</a></td>
<td class="colLast">
<div class="block">定义了一个子POI类</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
<a name="package.description">
<!--   -->
</a>
<h2 title="程序包com.amap.api.services.poisearch的说明">程序包com.amap.api.services.poisearch的说明</h2>
<div class="block"><p>
POI查询包，包含了兴趣点的详细信息。
</p></div>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li class="navBarCell1Rev">程序包</li>
<li>类</li>
<li><a href="package-use.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/nearby/package-summary.html">上一个程序包</a></li>
<li><a href="../../../../../com/amap/api/services/road/package-summary.html">下一个程序包</a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/poisearch/package-summary.html" target="_top">框架</a></li>
<li><a href="package-summary.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
