<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>PoiSearchV2.Query</title>
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="PoiSearchV2.Query";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":42,"i21":10,"i22":10};
var tabs = {65535:["t0","所有方法"],2:["t2","实例方法"],8:["t4","具体方法"],32:["t6","已过时的方法"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/PoiSearchV2.Query.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/poisearch/PoiSearchV2.OnPoiSearchListener.html" title="com.amap.api.services.poisearch中的接口"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/amap/api/services/poisearch/PoiSearchV2.SearchBound.html" title="com.amap.api.services.poisearch中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/poisearch/PoiSearchV2.Query.html" target="_top">框架</a></li>
<li><a href="PoiSearchV2.Query.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.amap.api.services.poisearch</div>
<h2 title="类 PoiSearchV2.Query" class="title">类 PoiSearchV2.Query</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.amap.api.services.poisearch.PoiSearchV2.Query</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>所有已实现的接口:</dt>
<dd>java.lang.Cloneable</dd>
</dl>
<dl>
<dt>封闭类:</dt>
<dd><a href="../../../../../com/amap/api/services/poisearch/PoiSearchV2.html" title="com.amap.api.services.poisearch中的类">PoiSearchV2</a></dd>
</dl>
<hr>
<br>
<pre>public static class <span class="typeNameLabel">PoiSearchV2.Query</span>
extends java.lang.Object
implements java.lang.Cloneable</pre>
<div class="block">此类定义了搜索的关键字，类别及城市。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.4.0</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/poisearch/PoiSearchV2.Query.html#Query-java.lang.String-java.lang.String-">Query</a></span>(java.lang.String&nbsp;query,
     java.lang.String&nbsp;ctgr)</code>
<div class="block">Query构造函数
 
 参数 query 及 ctgr 至少需要定义一个。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/poisearch/PoiSearchV2.Query.html#Query-java.lang.String-java.lang.String-java.lang.String-">Query</a></span>(java.lang.String&nbsp;query,
     java.lang.String&nbsp;ctgr,
     java.lang.String&nbsp;city)</code>
<div class="block">根据给定的参数来构造一个 PoiSearchV2.Query 的新对象。</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>所有方法</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">实例方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">具体方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t6" class="tableTab"><span><a href="javascript:show(32);">已过时的方法</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/poisearch/PoiSearchV2.Query.html#equals-java.lang.Object-">equals</a></span>(java.lang.Object&nbsp;obj)</code>
<div class="block">比较两个查询条件是否相同（包括查询第几页）。</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/poisearch/PoiSearchV2.Query.html#getBuilding--">getBuilding</a></span>()</code>
<div class="block">返回待查询建筑物的标识</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/poisearch/PoiSearchV2.Query.html#getCategory--">getCategory</a></span>()</code>
<div class="block">返回待查分类组合。</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/poisearch/PoiSearchV2.Query.html#getCity--">getCity</a></span>()</code>
<div class="block">返回待查城市（地区）的电话区号。</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/poisearch/PoiSearchV2.Query.html#getCityLimit--">getCityLimit</a></span>()</code>
<div class="block">返回是否严格按照设定城市搜索</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/poisearch/PoiSearchV2.Query.html#getLocation--">getLocation</a></span>()</code>
<div class="block">获取设置的经纬度</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/poisearch/PoiSearchV2.Query.html#getPageNum--">getPageNum</a></span>()</code>
<div class="block">获取设置查询的是第几页，从0开始。</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/poisearch/PoiSearchV2.Query.html#getPageSize--">getPageSize</a></span>()</code>
<div class="block">获取设置的查询页面的结果数目。</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>protected java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/poisearch/PoiSearchV2.Query.html#getQueryLanguage--">getQueryLanguage</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/poisearch/PoiSearchV2.Query.html#getQueryString--">getQueryString</a></span>()</code>
<div class="block">返回待查字符串。</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/services/poisearch/PoiSearchV2.ShowFields.html" title="com.amap.api.services.poisearch中的类">PoiSearchV2.ShowFields</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/poisearch/PoiSearchV2.Query.html#getShowFields--">getShowFields</a></span>()</code>
<div class="block">扩展字段 base表示只返回基础数据，all表示所有数据 ，默认 base</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/poisearch/PoiSearchV2.Query.html#isDistanceSort--">isDistanceSort</a></span>()</code>
<div class="block">返回是否按照距离排序。</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/poisearch/PoiSearchV2.Query.html#isSpecial--">isSpecial</a></span>()</code>
<div class="block">是否对结果进行人工干预，如火车站，原因为POI较为特殊，结果存在人工干预，干预结果优先，所以距离优先的排序未生效
 仅周边搜索支持</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/poisearch/PoiSearchV2.Query.html#queryEquals-com.amap.api.services.poisearch.PoiSearchV2.Query-">queryEquals</a></span>(<a href="../../../../../com/amap/api/services/poisearch/PoiSearchV2.Query.html" title="com.amap.api.services.poisearch中的类">PoiSearchV2.Query</a>&nbsp;query)</code>
<div class="block">比较两个查询条件是否相同（不包括查询第几页）。</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/poisearch/PoiSearchV2.Query.html#setBuilding-java.lang.String-">setBuilding</a></span>(java.lang.String&nbsp;mBuilding)</code>
<div class="block">如果要进行室内搜索，需要添加该字段</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/poisearch/PoiSearchV2.Query.html#setCityLimit-boolean-">setCityLimit</a></span>(boolean&nbsp;isLimit)</code>
<div class="block">仅在通过关键字搜索时进行限制严格按照设置城市搜索</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/poisearch/PoiSearchV2.Query.html#setDistanceSort-boolean-">setDistanceSort</a></span>(boolean&nbsp;distanceSort)</code>
<div class="block">设置是否按距离排序</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/poisearch/PoiSearchV2.Query.html#setLocation-com.amap.api.services.core.LatLonPoint-">setLocation</a></span>(<a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&nbsp;location)</code>
<div class="block">设置经纬度</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/poisearch/PoiSearchV2.Query.html#setPageNum-int-">setPageNum</a></span>(int&nbsp;pageNum)</code>
<div class="block">设置查询第几页的结果数目。</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/poisearch/PoiSearchV2.Query.html#setPageSize-int-">setPageSize</a></span>(int&nbsp;size)</code>
<div class="block">设置查询每页的结果数目。</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/poisearch/PoiSearchV2.Query.html#setQueryLanguage-java.lang.String-">setQueryLanguage</a></span>(java.lang.String&nbsp;language)</code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;
<div class="block"><span class="deprecationComment">请参考 <a href="../../../../../com/amap/api/services/core/ServiceSettings.html#setLanguage-java.lang.String-"><code>ServiceSettings.setLanguage(String)</code></a></span></div>
</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/poisearch/PoiSearchV2.Query.html#setShowFields-com.amap.api.services.poisearch.PoiSearchV2.ShowFields-">setShowFields</a></span>(<a href="../../../../../com/amap/api/services/poisearch/PoiSearchV2.ShowFields.html" title="com.amap.api.services.poisearch中的类">PoiSearchV2.ShowFields</a>&nbsp;showFields)</code>
<div class="block">扩展字段 base表示只返回基础数据，all表示所有数据 ，默认 base</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/poisearch/PoiSearchV2.Query.html#setSpecial-boolean-">setSpecial</a></span>(boolean&nbsp;special)</code>
<div class="block">是否对结果进行人工干预，如火车站，原因为POI较为特殊，结果存在人工干预，干预结果优先，所以距离优先的排序未生效
 仅周边搜索支持
 默认为 true</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="Query-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Query</h4>
<pre>public&nbsp;Query(java.lang.String&nbsp;query,
             java.lang.String&nbsp;ctgr)</pre>
<div class="block">Query构造函数
 <p>
 参数 query 及 ctgr 至少需要定义一个。
 </p></div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>query</code> - 查询字符串，多个关键字用“|”分割 。</dd>
<dd><code>ctgr</code> - POI 类型的组合，比如定义如下组合：餐馆|电影院|景点 （POI类型请在网站“相关下载”处获取）。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.4.0</dd>
</dl>
</li>
</ul>
<a name="Query-java.lang.String-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>Query</h4>
<pre>public&nbsp;Query(java.lang.String&nbsp;query,
             java.lang.String&nbsp;ctgr,
             java.lang.String&nbsp;city)</pre>
<div class="block">根据给定的参数来构造一个 PoiSearchV2.Query 的新对象。
 <p>
 参数 query 及 ctgr 至少需要定义一个，并且参数 city 必须定义，不能为空。
 </p></div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>query</code> - 查询字符串，多个关键字用“|”分割 。</dd>
<dd><code>ctgr</code> - POI 类型的组合，比如定义如下组合：餐馆|电影院|景点 （POI类型请在网站“相关下载”处获取）。</dd>
<dd><code>city</code> - 待查询城市（地区）的城市编码 citycode、城市名称（中文或中文全拼）、行政区划代码adcode。必设参数。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.4.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="getBuilding--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBuilding</h4>
<pre>public&nbsp;java.lang.String&nbsp;getBuilding()</pre>
<div class="block">返回待查询建筑物的标识</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>待查询建筑物的标识</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.4.0</dd>
</dl>
</li>
</ul>
<a name="setBuilding-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBuilding</h4>
<pre>public&nbsp;void&nbsp;setBuilding(java.lang.String&nbsp;mBuilding)</pre>
<div class="block">如果要进行室内搜索，需要添加该字段</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>mBuilding</code> - 待查询建筑物的标识</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.4.0</dd>
</dl>
</li>
</ul>
<a name="getQueryString--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getQueryString</h4>
<pre>public&nbsp;java.lang.String&nbsp;getQueryString()</pre>
<div class="block">返回待查字符串。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>待查字符串。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.4.0</dd>
</dl>
</li>
</ul>
<a name="setQueryLanguage-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setQueryLanguage</h4>
<pre>public&nbsp;void&nbsp;setQueryLanguage(java.lang.String&nbsp;language)</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;<span class="deprecationComment">请参考 <a href="../../../../../com/amap/api/services/core/ServiceSettings.html#setLanguage-java.lang.String-"><code>ServiceSettings.setLanguage(String)</code></a></span></div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.4.0</dd>
</dl>
</li>
</ul>
<a name="getQueryLanguage--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getQueryLanguage</h4>
<pre>protected&nbsp;java.lang.String&nbsp;getQueryLanguage()</pre>
</li>
</ul>
<a name="getCategory--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCategory</h4>
<pre>public&nbsp;java.lang.String&nbsp;getCategory()</pre>
<div class="block">返回待查分类组合。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>待查分类组合。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.4.0</dd>
</dl>
</li>
</ul>
<a name="getCity--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCity</h4>
<pre>public&nbsp;java.lang.String&nbsp;getCity()</pre>
<div class="block">返回待查城市（地区）的电话区号。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>待查城市（地区）的电话区号。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.4.0</dd>
</dl>
</li>
</ul>
<a name="getPageNum--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPageNum</h4>
<pre>public&nbsp;int&nbsp;getPageNum()</pre>
<div class="block">获取设置查询的是第几页，从0开始。<font color="red">自 5.2.1后修改成从1开始，和iOS保持一致。</font></div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>查询的是第几页。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.4.0</dd>
</dl>
</li>
</ul>
<a name="setPageNum-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPageNum</h4>
<pre>public&nbsp;void&nbsp;setPageNum(int&nbsp;pageNum)</pre>
<div class="block">设置查询第几页的结果数目。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>pageNum</code> - 查询第几页的结果，从0开始。<font color="red">自 5.2.1后修改成从1开始，和iOS保持一致。</font></dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.4.0</dd>
</dl>
</li>
</ul>
<a name="setPageSize-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPageSize</h4>
<pre>public&nbsp;void&nbsp;setPageSize(int&nbsp;size)</pre>
<div class="block">设置查询每页的结果数目。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>size</code> - 新的查询条件， 默认值是20 条,取值范围在1-30 条。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.4.0</dd>
</dl>
</li>
</ul>
<a name="getPageSize--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPageSize</h4>
<pre>public&nbsp;int&nbsp;getPageSize()</pre>
<div class="block">获取设置的查询页面的结果数目。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>查询页面的结果数目。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.4.0</dd>
</dl>
</li>
</ul>
<a name="setCityLimit-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCityLimit</h4>
<pre>public&nbsp;void&nbsp;setCityLimit(boolean&nbsp;isLimit)</pre>
<div class="block">仅在通过关键字搜索时进行限制严格按照设置城市搜索</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>isLimit</code> - true，城市限制；false，输入文字是权重词汇时，全国搜索。默认为false。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.4.0</dd>
</dl>
</li>
</ul>
<a name="getCityLimit--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCityLimit</h4>
<pre>public&nbsp;boolean&nbsp;getCityLimit()</pre>
<div class="block">返回是否严格按照设定城市搜索</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>true，城市限制；false，关键字是权重词汇时，全国搜索</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.4.0</dd>
</dl>
</li>
</ul>
<a name="isDistanceSort--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isDistanceSort</h4>
<pre>public&nbsp;boolean&nbsp;isDistanceSort()</pre>
<div class="block">返回是否按照距离排序。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>true 按照距离排序，false不按照距离排序。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.4.0</dd>
</dl>
</li>
</ul>
<a name="setDistanceSort-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDistanceSort</h4>
<pre>public&nbsp;void&nbsp;setDistanceSort(boolean&nbsp;distanceSort)</pre>
<div class="block">设置是否按距离排序</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>distanceSort</code> - 是否按照距离排序</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.4.0</dd>
</dl>
</li>
</ul>
<a name="getLocation--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLocation</h4>
<pre>public&nbsp;<a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&nbsp;getLocation()</pre>
<div class="block">获取设置的经纬度</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>经纬度</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.4.0</dd>
</dl>
</li>
</ul>
<a name="setLocation-com.amap.api.services.core.LatLonPoint-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLocation</h4>
<pre>public&nbsp;void&nbsp;setLocation(<a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&nbsp;location)</pre>
<div class="block">设置经纬度</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>location</code> - </dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.4.0</dd>
</dl>
</li>
</ul>
<a name="isSpecial--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isSpecial</h4>
<pre>public&nbsp;boolean&nbsp;isSpecial()</pre>
<div class="block">是否对结果进行人工干预，如火车站，原因为POI较为特殊，结果存在人工干预，干预结果优先，所以距离优先的排序未生效
 仅周边搜索支持</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>是否对结果进行人工干预</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.4.0</dd>
</dl>
</li>
</ul>
<a name="setSpecial-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSpecial</h4>
<pre>public&nbsp;void&nbsp;setSpecial(boolean&nbsp;special)</pre>
<div class="block">是否对结果进行人工干预，如火车站，原因为POI较为特殊，结果存在人工干预，干预结果优先，所以距离优先的排序未生效
 仅周边搜索支持
 默认为 true</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.4.0</dd>
</dl>
</li>
</ul>
<a name="getShowFields--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getShowFields</h4>
<pre>public&nbsp;<a href="../../../../../com/amap/api/services/poisearch/PoiSearchV2.ShowFields.html" title="com.amap.api.services.poisearch中的类">PoiSearchV2.ShowFields</a>&nbsp;getShowFields()</pre>
<div class="block">扩展字段 base表示只返回基础数据，all表示所有数据 ，默认 base</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.4.0</dd>
</dl>
</li>
</ul>
<a name="setShowFields-com.amap.api.services.poisearch.PoiSearchV2.ShowFields-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setShowFields</h4>
<pre>public&nbsp;void&nbsp;setShowFields(<a href="../../../../../com/amap/api/services/poisearch/PoiSearchV2.ShowFields.html" title="com.amap.api.services.poisearch中的类">PoiSearchV2.ShowFields</a>&nbsp;showFields)</pre>
<div class="block">扩展字段 base表示只返回基础数据，all表示所有数据 ，默认 base</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.4.0</dd>
</dl>
</li>
</ul>
<a name="queryEquals-com.amap.api.services.poisearch.PoiSearchV2.Query-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>queryEquals</h4>
<pre>public&nbsp;boolean&nbsp;queryEquals(<a href="../../../../../com/amap/api/services/poisearch/PoiSearchV2.Query.html" title="com.amap.api.services.poisearch中的类">PoiSearchV2.Query</a>&nbsp;query)</pre>
<div class="block">比较两个查询条件是否相同（不包括查询第几页）。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>query</code> - 待比较的对象</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>查询条件是否相同</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.4.0</dd>
</dl>
</li>
</ul>
<a name="equals-java.lang.Object-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>equals</h4>
<pre>public&nbsp;boolean&nbsp;equals(java.lang.Object&nbsp;obj)</pre>
<div class="block">比较两个查询条件是否相同（包括查询第几页）。</div>
<dl>
<dt><span class="overrideSpecifyLabel">覆盖:</span></dt>
<dd><code>equals</code>&nbsp;在类中&nbsp;<code>java.lang.Object</code></dd>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>obj</code> - 查询条件。</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>查询条件是否相同。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.4.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/PoiSearchV2.Query.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/poisearch/PoiSearchV2.OnPoiSearchListener.html" title="com.amap.api.services.poisearch中的接口"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/amap/api/services/poisearch/PoiSearchV2.SearchBound.html" title="com.amap.api.services.poisearch中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/poisearch/PoiSearchV2.Query.html" target="_top">框架</a></li>
<li><a href="PoiSearchV2.Query.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
