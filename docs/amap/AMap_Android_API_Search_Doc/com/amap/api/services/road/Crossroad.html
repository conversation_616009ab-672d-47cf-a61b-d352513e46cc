<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Crossroad</title>
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Crossroad";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10};
var tabs = {65535:["t0","所有方法"],2:["t2","实例方法"],8:["t4","具体方法"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/Crossroad.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个类</li>
<li><a href="../../../../../com/amap/api/services/road/Road.html" title="com.amap.api.services.road中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/road/Crossroad.html" target="_top">框架</a></li>
<li><a href="Crossroad.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.amap.api.services.road</div>
<h2 title="类 Crossroad" class="title">类 Crossroad</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../../../com/amap/api/services/road/Road.html" title="com.amap.api.services.road中的类">com.amap.api.services.road.Road</a></li>
<li>
<ul class="inheritance">
<li>com.amap.api.services.road.Crossroad</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public final class <span class="typeNameLabel">Crossroad</span>
extends <a href="../../../../../com/amap/api/services/road/Road.html" title="com.amap.api.services.road中的类">Road</a></pre>
<div class="block">逆地理编码返回的结果的交叉路口对象。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>所有方法</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">实例方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">具体方法</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/road/Crossroad.html#getDirection--">getDirection</a></span>()</code>
<div class="block">返回交叉路口相对逆地理坐标点的方向。</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/road/Crossroad.html#getDistance--">getDistance</a></span>()</code>
<div class="block">返回逆地理坐标点与交叉路口的垂直距离，单位米。</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/road/Crossroad.html#getFirstRoadId--">getFirstRoadId</a></span>()</code>
<div class="block">返回交叉路口的第一条道路ID。</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/road/Crossroad.html#getFirstRoadName--">getFirstRoadName</a></span>()</code>
<div class="block">返回交叉路口的第一条道路名称。</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/road/Crossroad.html#getSecondRoadId--">getSecondRoadId</a></span>()</code>
<div class="block">返回交叉路口的第二条道路ID。</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/road/Crossroad.html#getSecondRoadName--">getSecondRoadName</a></span>()</code>
<div class="block">返回交叉路口的第二条道路名称。</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/road/Crossroad.html#setDirection-java.lang.String-">setDirection</a></span>(java.lang.String&nbsp;direction)</code>
<div class="block">设置交叉路口相对逆地理坐标点的方向。</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/road/Crossroad.html#setDistance-float-">setDistance</a></span>(float&nbsp;distance)</code>
<div class="block">设置逆地理坐标点与交叉路口的垂直距离，单位米。</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/road/Crossroad.html#setFirstRoadId-java.lang.String-">setFirstRoadId</a></span>(java.lang.String&nbsp;firstRoadId)</code>
<div class="block">设置交叉路口的第一条道路ID。</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/road/Crossroad.html#setFirstRoadName-java.lang.String-">setFirstRoadName</a></span>(java.lang.String&nbsp;firstRoadName)</code>
<div class="block">设置交叉路口的第一条道路名称。</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/road/Crossroad.html#setSecondRoadId-java.lang.String-">setSecondRoadId</a></span>(java.lang.String&nbsp;secondRoadId)</code>
<div class="block">设置交叉路口的第二条道路ID。</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/road/Crossroad.html#setSecondRoadName-java.lang.String-">setSecondRoadName</a></span>(java.lang.String&nbsp;secondRoadName)</code>
<div class="block">设置交叉路口的第二条道路名称。</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.amap.api.services.road.Road">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;com.amap.api.services.road.<a href="../../../../../com/amap/api/services/road/Road.html" title="com.amap.api.services.road中的类">Road</a></h3>
<code><a href="../../../../../com/amap/api/services/road/Road.html#getCenterPoint--">getCenterPoint</a>, <a href="../../../../../com/amap/api/services/road/Road.html#getCityCode--">getCityCode</a>, <a href="../../../../../com/amap/api/services/road/Road.html#getId--">getId</a>, <a href="../../../../../com/amap/api/services/road/Road.html#getName--">getName</a>, <a href="../../../../../com/amap/api/services/road/Road.html#getRoadWidth--">getRoadWidth</a>, <a href="../../../../../com/amap/api/services/road/Road.html#getType--">getType</a>, <a href="../../../../../com/amap/api/services/road/Road.html#setCenterPoint-com.amap.api.services.core.LatLonPoint-">setCenterPoint</a>, <a href="../../../../../com/amap/api/services/road/Road.html#setCityCode-java.lang.String-">setCityCode</a>, <a href="../../../../../com/amap/api/services/road/Road.html#setId-java.lang.String-">setId</a>, <a href="../../../../../com/amap/api/services/road/Road.html#setName-java.lang.String-">setName</a>, <a href="../../../../../com/amap/api/services/road/Road.html#setRoadWidth-float-">setRoadWidth</a>, <a href="../../../../../com/amap/api/services/road/Road.html#setType-java.lang.String-">setType</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="getDistance--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDistance</h4>
<pre>public&nbsp;float&nbsp;getDistance()</pre>
<div class="block">返回逆地理坐标点与交叉路口的垂直距离，单位米。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>逆地理坐标点与交叉路口的垂直距离。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="setDistance-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDistance</h4>
<pre>public&nbsp;void&nbsp;setDistance(float&nbsp;distance)</pre>
<div class="block">设置逆地理坐标点与交叉路口的垂直距离，单位米。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>distance</code> - 逆地理坐标点与交叉路口的垂直距离。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="getDirection--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDirection</h4>
<pre>public&nbsp;java.lang.String&nbsp;getDirection()</pre>
<div class="block">返回交叉路口相对逆地理坐标点的方向。方向显示为中文名称，如南、 东北。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>交叉路口相对逆地理坐标点的方向。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="setDirection-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDirection</h4>
<pre>public&nbsp;void&nbsp;setDirection(java.lang.String&nbsp;direction)</pre>
<div class="block">设置交叉路口相对逆地理坐标点的方向。方向显示为中文名称，如南、 东北。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>direction</code> - 交叉路口相对逆地理坐标点的方向。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="getFirstRoadId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFirstRoadId</h4>
<pre>public&nbsp;java.lang.String&nbsp;getFirstRoadId()</pre>
<div class="block">返回交叉路口的第一条道路ID。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>交叉路口的第一条道路ID。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="setFirstRoadId-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFirstRoadId</h4>
<pre>public&nbsp;void&nbsp;setFirstRoadId(java.lang.String&nbsp;firstRoadId)</pre>
<div class="block">设置交叉路口的第一条道路ID。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>firstRoadId</code> - 交叉路口的第一条道路ID。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="getFirstRoadName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFirstRoadName</h4>
<pre>public&nbsp;java.lang.String&nbsp;getFirstRoadName()</pre>
<div class="block">返回交叉路口的第一条道路名称。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>返回交叉路口的第一条道路名称。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="setFirstRoadName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFirstRoadName</h4>
<pre>public&nbsp;void&nbsp;setFirstRoadName(java.lang.String&nbsp;firstRoadName)</pre>
<div class="block">设置交叉路口的第一条道路名称。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>firstRoadName</code> - 返回交叉路口的第一条道路名称。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="getSecondRoadId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSecondRoadId</h4>
<pre>public&nbsp;java.lang.String&nbsp;getSecondRoadId()</pre>
<div class="block">返回交叉路口的第二条道路ID。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>交叉路口的第二条道路ID。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="setSecondRoadId-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSecondRoadId</h4>
<pre>public&nbsp;void&nbsp;setSecondRoadId(java.lang.String&nbsp;secondRoadId)</pre>
<div class="block">设置交叉路口的第二条道路ID。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>secondRoadId</code> - 交叉路口的第二条道路ID。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="getSecondRoadName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSecondRoadName</h4>
<pre>public&nbsp;java.lang.String&nbsp;getSecondRoadName()</pre>
<div class="block">返回交叉路口的第二条道路名称。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>返回交叉路口的第二条道路名称。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="setSecondRoadName-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setSecondRoadName</h4>
<pre>public&nbsp;void&nbsp;setSecondRoadName(java.lang.String&nbsp;secondRoadName)</pre>
<div class="block">设置交叉路口的第二条道路名称。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>secondRoadName</code> - 返回交叉路口的第二条道路名称。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/Crossroad.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个类</li>
<li><a href="../../../../../com/amap/api/services/road/Road.html" title="com.amap.api.services.road中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/road/Crossroad.html" target="_top">框架</a></li>
<li><a href="Crossroad.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
