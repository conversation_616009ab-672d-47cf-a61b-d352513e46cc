<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>ChargeStationInfo</title>
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ChargeStationInfo";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10};
var tabs = {65535:["t0","所有方法"],2:["t2","实例方法"],8:["t4","具体方法"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/ChargeStationInfo.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/route/BusStep.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/amap/api/services/route/Cost.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/route/ChargeStationInfo.html" target="_top">框架</a></li>
<li><a href="ChargeStationInfo.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.amap.api.services.route</div>
<h2 title="类 ChargeStationInfo" class="title">类 ChargeStationInfo</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.amap.api.services.route.ChargeStationInfo</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">ChargeStationInfo</span>
extends java.lang.Object</pre>
<div class="block">新能源充电桩信息

 //* @exclude javadoc中不显示</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/ChargeStationInfo.html#ChargeStationInfo--">ChargeStationInfo</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>所有方法</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">实例方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">具体方法</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/ChargeStationInfo.html#getAmperage--">getAmperage</a></span>()</code>
<div class="block">获取充电桩的电流，单位：安培</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/ChargeStationInfo.html#getBrandName--">getBrandName</a></span>()</code>
<div class="block">获取充电站品牌名称</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/ChargeStationInfo.html#getChargePercent--">getChargePercent</a></span>()</code>
<div class="block">获取建议充电百分比，百分比*100后的整数，如60</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/ChargeStationInfo.html#getChargeTime--">getChargeTime</a></span>()</code>
<div class="block">获取预计充电时长，单位秒，注意：当电量不可达时，这个值不可用</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/ChargeStationInfo.html#getMaxPower--">getMaxPower</a></span>()</code>
<div class="block">获取快充桩最大功率，单位：千瓦</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/ChargeStationInfo.html#getName--">getName</a></span>()</code>
<div class="block">获取充电站名称</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/ChargeStationInfo.html#getPoiId--">getPoiId</a></span>()</code>
<div class="block">获取POIID</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/ChargeStationInfo.html#getProjectivePoint--">getProjectivePoint</a></span>()</code>
<div class="block">获取显示坐标的投影点坐标，规则： lon，lat（经度，纬度）， “,”分割，如117.500244, 40.417801 经纬度小数点不超过6位</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/ChargeStationInfo.html#getRemainingCapacity--">getRemainingCapacity</a></span>()</code>
<div class="block">获取预计到达充电桩剩余电量，单位：百分之一瓦时，注意：当这个值小于0时，表示该站电量不可达</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/ChargeStationInfo.html#getShowPoint--">getShowPoint</a></span>()</code>
<div class="block">获取显示坐标，规则： lon，lat（经度，纬度）， “,”分割，如117.500244, 40.417801 经纬度小数点不超过6位</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/ChargeStationInfo.html#getStepIndex--">getStepIndex</a></span>()</code>
<div class="block">获取导航段steps索引</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/ChargeStationInfo.html#getVoltage--">getVoltage</a></span>()</code>
<div class="block">获取充电桩的电压</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/ChargeStationInfo.html#setAmperage-int-">setAmperage</a></span>(int&nbsp;amperage)</code>
<div class="block">设置充电桩的电流，单位：安培</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/ChargeStationInfo.html#setBrandName-java.lang.String-">setBrandName</a></span>(java.lang.String&nbsp;brandName)</code>
<div class="block">设置充电站品牌名称</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/ChargeStationInfo.html#setChargePercent-int-">setChargePercent</a></span>(int&nbsp;chargePercent)</code>
<div class="block">设置建议充电百分比，百分比*100后的整数，如60</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/ChargeStationInfo.html#setChargeTime-int-">setChargeTime</a></span>(int&nbsp;chargeTime)</code>
<div class="block">设置预计充电时长，单位秒，注意：当电量不可达时，这个值不可用</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/ChargeStationInfo.html#setMaxPower-int-">setMaxPower</a></span>(int&nbsp;maxPower)</code>
<div class="block">设置快充桩最大功率，单位：千瓦</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/ChargeStationInfo.html#setName-java.lang.String-">setName</a></span>(java.lang.String&nbsp;name)</code>
<div class="block">设置充电站名称</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/ChargeStationInfo.html#setPoiId-java.lang.String-">setPoiId</a></span>(java.lang.String&nbsp;poiId)</code>
<div class="block">设置POIID</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/ChargeStationInfo.html#setProjectivePoint-com.amap.api.services.core.LatLonPoint-">setProjectivePoint</a></span>(<a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&nbsp;projectivePoint)</code>
<div class="block">设置显示坐标的投影点坐标，规则： lon，lat（经度，纬度）， “,”分割，如117.500244, 40.417801 经纬度小数点不超过6位</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/ChargeStationInfo.html#setRemainingCapacity-int-">setRemainingCapacity</a></span>(int&nbsp;remainingCapacity)</code>
<div class="block">设置预计到达充电桩剩余电量，单位：百分之一瓦时，注意：当这个值小于0时，表示该站电量不可达</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/ChargeStationInfo.html#setShowPoint-com.amap.api.services.core.LatLonPoint-">setShowPoint</a></span>(<a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&nbsp;showPoint)</code>
<div class="block">设置显示坐标，规则： lon，lat（经度，纬度）， “,”分割，如117.500244, 40.417801 经纬度小数点不超过6位</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/ChargeStationInfo.html#setStepIndex-int-">setStepIndex</a></span>(int&nbsp;stepIndex)</code>
<div class="block">设置导航段steps索引</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/ChargeStationInfo.html#setVoltage-int-">setVoltage</a></span>(int&nbsp;voltage)</code>
<div class="block">设置充电桩的电压</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="ChargeStationInfo--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>ChargeStationInfo</h4>
<pre>public&nbsp;ChargeStationInfo()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="getStepIndex--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStepIndex</h4>
<pre>public&nbsp;int&nbsp;getStepIndex()</pre>
<div class="block">获取导航段steps索引</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>导航段steps索引</dd>
</dl>
</li>
</ul>
<a name="setStepIndex-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setStepIndex</h4>
<pre>public&nbsp;void&nbsp;setStepIndex(int&nbsp;stepIndex)</pre>
<div class="block">设置导航段steps索引</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>stepIndex</code> - 导航段steps索引</dd>
</dl>
</li>
</ul>
<a name="getShowPoint--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getShowPoint</h4>
<pre>public&nbsp;<a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&nbsp;getShowPoint()</pre>
<div class="block">获取显示坐标，规则： lon，lat（经度，纬度）， “,”分割，如117.500244, 40.417801 经纬度小数点不超过6位</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>显示坐标，规则： lon，lat（经度，纬度）， “,”分割，如117.500244, 40.417801 经纬度小数点不超过6位</dd>
</dl>
</li>
</ul>
<a name="setShowPoint-com.amap.api.services.core.LatLonPoint-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setShowPoint</h4>
<pre>public&nbsp;void&nbsp;setShowPoint(<a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&nbsp;showPoint)</pre>
<div class="block">设置显示坐标，规则： lon，lat（经度，纬度）， “,”分割，如117.500244, 40.417801 经纬度小数点不超过6位</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>showPoint</code> - 显示坐标，规则： lon，lat（经度，纬度）， “,”分割，如117.500244, 40.417801 经纬度小数点不超过6位</dd>
</dl>
</li>
</ul>
<a name="getProjectivePoint--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProjectivePoint</h4>
<pre>public&nbsp;<a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&nbsp;getProjectivePoint()</pre>
<div class="block">获取显示坐标的投影点坐标，规则： lon，lat（经度，纬度）， “,”分割，如117.500244, 40.417801 经纬度小数点不超过6位</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>显示坐标的投影点坐标，规则： lon，lat（经度，纬度）， “,”分割，如117.500244, 40.417801 经纬度小数点不超过6位</dd>
</dl>
</li>
</ul>
<a name="setProjectivePoint-com.amap.api.services.core.LatLonPoint-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setProjectivePoint</h4>
<pre>public&nbsp;void&nbsp;setProjectivePoint(<a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&nbsp;projectivePoint)</pre>
<div class="block">设置显示坐标的投影点坐标，规则： lon，lat（经度，纬度）， “,”分割，如117.500244, 40.417801 经纬度小数点不超过6位</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>projectivePoint</code> - 显示坐标的投影点坐标，规则： lon，lat（经度，纬度）， “,”分割，如117.500244, 40.417801 经纬度小数点不超过6位</dd>
</dl>
</li>
</ul>
<a name="getPoiId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPoiId</h4>
<pre>public&nbsp;java.lang.String&nbsp;getPoiId()</pre>
<div class="block">获取POIID</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>POIID</dd>
</dl>
</li>
</ul>
<a name="setPoiId-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPoiId</h4>
<pre>public&nbsp;void&nbsp;setPoiId(java.lang.String&nbsp;poiId)</pre>
<div class="block">设置POIID</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>poiId</code> - </dd>
</dl>
</li>
</ul>
<a name="getName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getName</h4>
<pre>public&nbsp;java.lang.String&nbsp;getName()</pre>
<div class="block">获取充电站名称</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>充电站名称</dd>
</dl>
</li>
</ul>
<a name="setName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setName</h4>
<pre>public&nbsp;void&nbsp;setName(java.lang.String&nbsp;name)</pre>
<div class="block">设置充电站名称</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>name</code> - 充电站名称</dd>
</dl>
</li>
</ul>
<a name="getBrandName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBrandName</h4>
<pre>public&nbsp;java.lang.String&nbsp;getBrandName()</pre>
<div class="block">获取充电站品牌名称</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>充电站品牌名称</dd>
</dl>
</li>
</ul>
<a name="setBrandName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBrandName</h4>
<pre>public&nbsp;void&nbsp;setBrandName(java.lang.String&nbsp;brandName)</pre>
<div class="block">设置充电站品牌名称</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>brandName</code> - 充电站品牌名称</dd>
</dl>
</li>
</ul>
<a name="getMaxPower--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMaxPower</h4>
<pre>public&nbsp;int&nbsp;getMaxPower()</pre>
<div class="block">获取快充桩最大功率，单位：千瓦</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>快充桩最大功率，单位：千瓦</dd>
</dl>
</li>
</ul>
<a name="setMaxPower-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMaxPower</h4>
<pre>public&nbsp;void&nbsp;setMaxPower(int&nbsp;maxPower)</pre>
<div class="block">设置快充桩最大功率，单位：千瓦</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>maxPower</code> - 快充桩最大功率，单位：千瓦</dd>
</dl>
</li>
</ul>
<a name="getChargePercent--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getChargePercent</h4>
<pre>public&nbsp;int&nbsp;getChargePercent()</pre>
<div class="block">获取建议充电百分比，百分比*100后的整数，如60</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>建议充电百分比，百分比*100后的整数，如60</dd>
</dl>
</li>
</ul>
<a name="setChargePercent-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setChargePercent</h4>
<pre>public&nbsp;void&nbsp;setChargePercent(int&nbsp;chargePercent)</pre>
<div class="block">设置建议充电百分比，百分比*100后的整数，如60</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>chargePercent</code> - 建议充电百分比，百分比*100后的整数，如60</dd>
</dl>
</li>
</ul>
<a name="getChargeTime--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getChargeTime</h4>
<pre>public&nbsp;int&nbsp;getChargeTime()</pre>
<div class="block">获取预计充电时长，单位秒，注意：当电量不可达时，这个值不可用</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>预计充电时长，单位秒，注意：当电量不可达时，这个值不可用</dd>
</dl>
</li>
</ul>
<a name="setChargeTime-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setChargeTime</h4>
<pre>public&nbsp;void&nbsp;setChargeTime(int&nbsp;chargeTime)</pre>
<div class="block">设置预计充电时长，单位秒，注意：当电量不可达时，这个值不可用</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>chargeTime</code> - 预计充电时长，单位秒，注意：当电量不可达时，这个值不可用</dd>
</dl>
</li>
</ul>
<a name="getRemainingCapacity--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRemainingCapacity</h4>
<pre>public&nbsp;int&nbsp;getRemainingCapacity()</pre>
<div class="block">获取预计到达充电桩剩余电量，单位：百分之一瓦时，注意：当这个值小于0时，表示该站电量不可达</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>预计到达充电桩剩余电量，单位：百分之一瓦时，注意：当这个值小于0时，表示该站电量不可达</dd>
</dl>
</li>
</ul>
<a name="setRemainingCapacity-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRemainingCapacity</h4>
<pre>public&nbsp;void&nbsp;setRemainingCapacity(int&nbsp;remainingCapacity)</pre>
<div class="block">设置预计到达充电桩剩余电量，单位：百分之一瓦时，注意：当这个值小于0时，表示该站电量不可达</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>remainingCapacity</code> - 预计到达充电桩剩余电量，单位：百分之一瓦时，注意：当这个值小于0时，表示该站电量不可达</dd>
</dl>
</li>
</ul>
<a name="getVoltage--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVoltage</h4>
<pre>public&nbsp;int&nbsp;getVoltage()</pre>
<div class="block">获取充电桩的电压</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>充电桩的电压</dd>
</dl>
</li>
</ul>
<a name="setVoltage-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setVoltage</h4>
<pre>public&nbsp;void&nbsp;setVoltage(int&nbsp;voltage)</pre>
<div class="block">设置充电桩的电压</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>voltage</code> - 充电桩的电压</dd>
</dl>
</li>
</ul>
<a name="getAmperage--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAmperage</h4>
<pre>public&nbsp;int&nbsp;getAmperage()</pre>
<div class="block">获取充电桩的电流，单位：安培</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>充电桩的电流，单位：安培</dd>
</dl>
</li>
</ul>
<a name="setAmperage-int-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setAmperage</h4>
<pre>public&nbsp;void&nbsp;setAmperage(int&nbsp;amperage)</pre>
<div class="block">设置充电桩的电流，单位：安培</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>amperage</code> - 充电桩的电流，单位：安培</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/ChargeStationInfo.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/route/BusStep.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/amap/api/services/route/Cost.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/route/ChargeStationInfo.html" target="_top">框架</a></li>
<li><a href="ChargeStationInfo.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
