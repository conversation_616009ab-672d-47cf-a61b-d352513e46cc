<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>BusStepV2</title>
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="BusStepV2";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":42,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10};
var tabs = {65535:["t0","所有方法"],2:["t2","实例方法"],8:["t4","具体方法"],32:["t6","已过时的方法"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/BusStepV2.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/route/BusStep.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/amap/api/services/route/Cost.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/route/BusStepV2.html" target="_top">框架</a></li>
<li><a href="BusStepV2.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.amap.api.services.route</div>
<h2 title="类 BusStepV2" class="title">类 BusStepV2</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.amap.api.services.route.BusStepV2</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">BusStepV2</span>
extends java.lang.Object</pre>
<div class="block">定义了公交路径规划的一个路段。
 路段最多包含一段步行信息和公交导航信息。可能出现路段中没有步行信息的情况。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>所有方法</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">实例方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">具体方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t6" class="tableTab"><span><a href="javascript:show(32);">已过时的方法</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/services/route/RouteBusLineItem.html" title="com.amap.api.services.route中的类">RouteBusLineItem</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/BusStepV2.html#getBusLine--">getBusLine</a></span>()</code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;
<div class="block"><span class="deprecationComment">此接口废弃。</span></div>
</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../../com/amap/api/services/route/RouteBusLineItem.html" title="com.amap.api.services.route中的类">RouteBusLineItem</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/BusStepV2.html#getBusLines--">getBusLines</a></span>()</code>
<div class="block">返回此路段的公交导航信息。</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/services/route/Doorway.html" title="com.amap.api.services.route中的类">Doorway</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/BusStepV2.html#getEntrance--">getEntrance</a></span>()</code>
<div class="block">返回此路段的入口信息。</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/services/route/Doorway.html" title="com.amap.api.services.route中的类">Doorway</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/BusStepV2.html#getExit--">getExit</a></span>()</code>
<div class="block">返回此路段的出口信息。</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/services/route/RouteRailwayItem.html" title="com.amap.api.services.route中的类">RouteRailwayItem</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/BusStepV2.html#getRailway--">getRailway</a></span>()</code>
<div class="block">返回此路段的火车乘坐信息。</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/services/route/TaxiItemV2.html" title="com.amap.api.services.route中的类">TaxiItemV2</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/BusStepV2.html#getTaxi--">getTaxi</a></span>()</code>
<div class="block">返回此路段的出租车信息。</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/services/route/RouteBusWalkItem.html" title="com.amap.api.services.route中的类">RouteBusWalkItem</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/BusStepV2.html#getWalk--">getWalk</a></span>()</code>
<div class="block">返回此路段的步行信息。</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="getWalk--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWalk</h4>
<pre>public&nbsp;<a href="../../../../../com/amap/api/services/route/RouteBusWalkItem.html" title="com.amap.api.services.route中的类">RouteBusWalkItem</a>&nbsp;getWalk()</pre>
<div class="block">返回此路段的步行信息。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>此路段的步行信息。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="getBusLine--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBusLine</h4>
<pre>@Deprecated
public&nbsp;<a href="../../../../../com/amap/api/services/route/RouteBusLineItem.html" title="com.amap.api.services.route中的类">RouteBusLineItem</a>&nbsp;getBusLine()</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;<span class="deprecationComment">此接口废弃。</span></div>
<div class="block">返回此路段的公交导航信息。目前只返回一条公交线路信息。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>此路段的公交导航信息。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="getBusLines--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBusLines</h4>
<pre>public&nbsp;java.util.List&lt;<a href="../../../../../com/amap/api/services/route/RouteBusLineItem.html" title="com.amap.api.services.route中的类">RouteBusLineItem</a>&gt;&nbsp;getBusLines()</pre>
<div class="block">返回此路段的公交导航信息。目前返回多条公交线路信息。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>此路段的公交导航信息。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
<a name="getEntrance--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEntrance</h4>
<pre>public&nbsp;<a href="../../../../../com/amap/api/services/route/Doorway.html" title="com.amap.api.services.route中的类">Doorway</a>&nbsp;getEntrance()</pre>
<div class="block">返回此路段的入口信息。
 入口信息指换乘地铁时的进站口。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>此路段的入口信息。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="getExit--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getExit</h4>
<pre>public&nbsp;<a href="../../../../../com/amap/api/services/route/Doorway.html" title="com.amap.api.services.route中的类">Doorway</a>&nbsp;getExit()</pre>
<div class="block">返回此路段的出口信息。
 出口信息指乘地铁之后的出站口信息。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>此路段的出口信息。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="getRailway--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRailway</h4>
<pre>public&nbsp;<a href="../../../../../com/amap/api/services/route/RouteRailwayItem.html" title="com.amap.api.services.route中的类">RouteRailwayItem</a>&nbsp;getRailway()</pre>
<div class="block">返回此路段的火车乘坐信息。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>此路段的火车乘坐信息。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.4.0</dd>
</dl>
</li>
</ul>
<a name="getTaxi--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getTaxi</h4>
<pre>public&nbsp;<a href="../../../../../com/amap/api/services/route/TaxiItemV2.html" title="com.amap.api.services.route中的类">TaxiItemV2</a>&nbsp;getTaxi()</pre>
<div class="block">返回此路段的出租车信息。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>此路段的出租车信息。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.4.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/BusStepV2.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/route/BusStep.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/amap/api/services/route/Cost.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/route/BusStepV2.html" target="_top">框架</a></li>
<li><a href="BusStepV2.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
