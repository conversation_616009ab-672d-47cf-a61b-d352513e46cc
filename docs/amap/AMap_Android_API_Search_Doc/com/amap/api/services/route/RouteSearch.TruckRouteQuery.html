<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>RouteSearch.TruckRouteQuery</title>
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="RouteSearch.TruckRouteQuery";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10};
var tabs = {65535:["t0","所有方法"],2:["t2","实例方法"],8:["t4","具体方法"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/RouteSearch.TruckRouteQuery.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/route/RouteSearch.RideRouteQuery.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/amap/api/services/route/RouteSearch.WalkRouteQuery.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/route/RouteSearch.TruckRouteQuery.html" target="_top">框架</a></li>
<li><a href="RouteSearch.TruckRouteQuery.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.amap.api.services.route</div>
<h2 title="类 RouteSearch.TruckRouteQuery" class="title">类 RouteSearch.TruckRouteQuery</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.amap.api.services.route.RouteSearch.TruckRouteQuery</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>所有已实现的接口:</dt>
<dd>java.lang.Cloneable</dd>
</dl>
<dl>
<dt>封闭类:</dt>
<dd><a href="../../../../../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dd>
</dl>
<hr>
<br>
<pre>public static class <span class="typeNameLabel">RouteSearch.TruckRouteQuery</span>
extends java.lang.Object
implements java.lang.Cloneable</pre>
<div class="block">此类定义了货车路径的起终点和计算路径的模式。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.1.0</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.TruckRouteQuery.html#TruckRouteQuery-com.amap.api.services.route.RouteSearch.FromAndTo-int-java.util.List-int-">TruckRouteQuery</a></span>(<a href="../../../../../com/amap/api/services/route/RouteSearch.FromAndTo.html" title="com.amap.api.services.route中的类">RouteSearch.FromAndTo</a>&nbsp;fromAndTo,
               int&nbsp;mode,
               java.util.List&lt;<a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&gt;&nbsp;passedByPoints,
               int&nbsp;truckSize)</code>
<div class="block">货车导航请求参数构造</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>所有方法</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">实例方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">具体方法</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.TruckRouteQuery.html#getExtensions--">getExtensions</a></span>()</code>
<div class="block">扩展字段 base表示只返回基础数据，all表示所有数据 ，默认 base</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/services/route/RouteSearch.FromAndTo.html" title="com.amap.api.services.route中的类">RouteSearch.FromAndTo</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.TruckRouteQuery.html#getFromAndTo--">getFromAndTo</a></span>()</code>
<div class="block">获取路径的起点终点</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.TruckRouteQuery.html#getMode--">getMode</a></span>()</code>
<div class="block">计算路径的模式</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.TruckRouteQuery.html#getPassedPointStr--">getPassedPointStr</a></span>()</code>
<div class="block">将途径点位置坐标转换为字符串输出。</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.TruckRouteQuery.html#getTruckAxis--">getTruckAxis</a></span>()</code>
<div class="block">获取设置的车辆轴数</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.TruckRouteQuery.html#getTruckHeight--">getTruckHeight</a></span>()</code>
<div class="block">获取设置的车辆高度</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.TruckRouteQuery.html#getTruckLoad--">getTruckLoad</a></span>()</code>
<div class="block">获取设置的车辆总重</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.TruckRouteQuery.html#getTruckSize--">getTruckSize</a></span>()</code>
<div class="block">获取设置的车辆大小</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.TruckRouteQuery.html#getTruckWeight--">getTruckWeight</a></span>()</code>
<div class="block">获取设置的车辆核定载重</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.TruckRouteQuery.html#getTruckWidth--">getTruckWidth</a></span>()</code>
<div class="block">获取设置的车辆高度</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.TruckRouteQuery.html#setExtensions-java.lang.String-">setExtensions</a></span>(java.lang.String&nbsp;extensions)</code>
<div class="block">扩展字段 base表示只返回基础数据，all表示所有数据 ，默认 base</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.TruckRouteQuery.html#setMode-int-">setMode</a></span>(int&nbsp;mode)</code>
<div class="block">货车导航计算路径的模式设置</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.TruckRouteQuery.html#setTruckAxis-float-">setTruckAxis</a></span>(float&nbsp;truckAxis)</code>
<div class="block">车辆轴数</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.TruckRouteQuery.html#setTruckHeight-float-">setTruckHeight</a></span>(float&nbsp;truckHeight)</code>
<div class="block">车辆高度</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.TruckRouteQuery.html#setTruckLoad-float-">setTruckLoad</a></span>(float&nbsp;truckLoad)</code>
<div class="block">车辆总重</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.TruckRouteQuery.html#setTruckSize-int-">setTruckSize</a></span>(int&nbsp;truckSize)</code>
<div class="block">高德此分类依据国标
 1：微型车，2：轻型车（默认值），3：中型车，4：重型车<br>
 根据新的汽车分类国家标准(GB9417-89)就可方便地区分车型。</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.TruckRouteQuery.html#setTruckWeight-float-">setTruckWeight</a></span>(float&nbsp;truckWeight)</code>
<div class="block">货车核定载重</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.TruckRouteQuery.html#setTruckWidth-float-">setTruckWidth</a></span>(float&nbsp;truckWidth)</code>
<div class="block">车辆宽度</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="TruckRouteQuery-com.amap.api.services.route.RouteSearch.FromAndTo-int-java.util.List-int-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>TruckRouteQuery</h4>
<pre>public&nbsp;TruckRouteQuery(<a href="../../../../../com/amap/api/services/route/RouteSearch.FromAndTo.html" title="com.amap.api.services.route中的类">RouteSearch.FromAndTo</a>&nbsp;fromAndTo,
                       int&nbsp;mode,
                       java.util.List&lt;<a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&gt;&nbsp;passedByPoints,
                       int&nbsp;truckSize)</pre>
<div class="block">货车导航请求参数构造</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>fromAndTo</code> - 必填 路径的起点终点。</dd>
<dd><code>mode</code> - 计算路径的模式。可选，默认为躲避拥堵 <a href="../../../../../com/amap/api/services/route/RouteSearch.html#TRUCK_AVOID_CONGESTION"><code>RouteSearch.TRUCK_AVOID_CONGESTION</code></a>。</dd>
<dd><code>passedByPoints</code> - 途经点，可选。最多支持6个途经点。</dd>
<dd><code>truckSize</code> - 必填 货车大小 默认 <a href="../../../../../com/amap/api/services/route/RouteSearch.html#TRUCK_SIZE_LIGHT"><code>RouteSearch.TRUCK_SIZE_LIGHT</code></a>
                       高德此分类依据国标
                       1：微型车，2：轻型车（默认值），3：中型车，4：重型车<br>
                       根据新的汽车分类国家标准(GB9417-89)就可方便地区分车型。<br>
                       载货汽车： 依公路运行时厂定最大总质量（GA）划分为：<br>
                       微型货车（GA≤1．8吨）  <a href="../../../../../com/amap/api/services/route/RouteSearch.html#TRUCK_SIZE_MINI"><code>RouteSearch.TRUCK_SIZE_MINI</code></a><br>
                       轻型货车（1．8吨＜GA≤6吨）  <a href="../../../../../com/amap/api/services/route/RouteSearch.html#TRUCK_SIZE_LIGHT"><code>RouteSearch.TRUCK_SIZE_LIGHT</code></a><br>
                       中型货车（6．0吨＜GA≤14吨）  <a href="../../../../../com/amap/api/services/route/RouteSearch.html#TRUCK_SIZE_MEDIUM"><code>RouteSearch.TRUCK_SIZE_MEDIUM</code></a><br>
                       重型货车（GA＞14吨）  <a href="../../../../../com/amap/api/services/route/RouteSearch.html#TRUCK_SIZE_HEAVY"><code>RouteSearch.TRUCK_SIZE_HEAVY</code></a><br></dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.1.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="setMode-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMode</h4>
<pre>public&nbsp;void&nbsp;setMode(int&nbsp;mode)</pre>
<div class="block">货车导航计算路径的模式设置</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>mode</code> - 计算路径的模式, 默认为躲避拥堵 <a href="../../../../../com/amap/api/services/route/RouteSearch.html#TRUCK_AVOID_CONGESTION"><code>RouteSearch.TRUCK_AVOID_CONGESTION</code></a>。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.1.0</dd>
</dl>
</li>
</ul>
<a name="setTruckSize-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTruckSize</h4>
<pre>public&nbsp;void&nbsp;setTruckSize(int&nbsp;truckSize)</pre>
<div class="block">高德此分类依据国标
 1：微型车，2：轻型车（默认值），3：中型车，4：重型车<br>
 根据新的汽车分类国家标准(GB9417-89)就可方便地区分车型。<br>
 载货汽车： 依公路运行时厂定最大总质量（GA）划分为：<br>
 微型货车（GA≤1．8吨）  <a href="../../../../../com/amap/api/services/route/RouteSearch.html#TRUCK_SIZE_MINI"><code>RouteSearch.TRUCK_SIZE_MINI</code></a><br>
 轻型货车（1．8吨＜GA≤6吨）  <a href="../../../../../com/amap/api/services/route/RouteSearch.html#TRUCK_SIZE_LIGHT"><code>RouteSearch.TRUCK_SIZE_LIGHT</code></a><br>
 中型货车（6．0吨＜GA≤14吨）  <a href="../../../../../com/amap/api/services/route/RouteSearch.html#TRUCK_SIZE_MEDIUM"><code>RouteSearch.TRUCK_SIZE_MEDIUM</code></a><br>
 重型货车（GA＞14吨）  <a href="../../../../../com/amap/api/services/route/RouteSearch.html#TRUCK_SIZE_HEAVY"><code>RouteSearch.TRUCK_SIZE_HEAVY</code></a><br></div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>truckSize</code> - 货车大小 <a href="../../../../../com/amap/api/services/route/RouteSearch.html#TRUCK_SIZE_LIGHT"><code>RouteSearch.TRUCK_SIZE_LIGHT</code></a>。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.1.0</dd>
</dl>
</li>
</ul>
<a name="setTruckHeight-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTruckHeight</h4>
<pre>public&nbsp;void&nbsp;setTruckHeight(float&nbsp;truckHeight)</pre>
<div class="block">车辆高度</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>truckHeight</code> - 单位米，取值[0 – 25.5]米，默认 1.6 米，会严格按照填写数字进行限行规避，请按照车辆真实信息合理填写。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.1.0</dd>
</dl>
</li>
</ul>
<a name="setTruckWidth-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTruckWidth</h4>
<pre>public&nbsp;void&nbsp;setTruckWidth(float&nbsp;truckWidth)</pre>
<div class="block">车辆宽度</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>truckWidth</code> - 单位米，取值[0 – 25.5]米，默认 2.5 米，会严格按照填写数字进行限行规避，请按照车辆真实信息合理填写。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.1.0</dd>
</dl>
</li>
</ul>
<a name="setTruckLoad-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTruckLoad</h4>
<pre>public&nbsp;void&nbsp;setTruckLoad(float&nbsp;truckLoad)</pre>
<div class="block">车辆总重</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>truckLoad</code> - 单位吨，取值[0 – 6553.5]吨，默认 0.9 吨，会严格按照填写数字进行限行规避，请按照车辆真实信息合理填写。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.1.0</dd>
</dl>
</li>
</ul>
<a name="setTruckWeight-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTruckWeight</h4>
<pre>public&nbsp;void&nbsp;setTruckWeight(float&nbsp;truckWeight)</pre>
<div class="block">货车核定载重</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>truckWeight</code> - 单位吨，取值[0 – 6553.5]吨，默认 10 吨，会严格按照填写数字进行限行规避，请按照车辆真实信息合理填写。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.1.0</dd>
</dl>
</li>
</ul>
<a name="setTruckAxis-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTruckAxis</h4>
<pre>public&nbsp;void&nbsp;setTruckAxis(float&nbsp;truckAxis)</pre>
<div class="block">车辆轴数</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>truckAxis</code> - 单位个，取值[0 –255]个，默认 2个轴，会严格按照填写数字进行限行规避，请按照车辆真实信息合理填写。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.1.0</dd>
</dl>
</li>
</ul>
<a name="getFromAndTo--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFromAndTo</h4>
<pre>public&nbsp;<a href="../../../../../com/amap/api/services/route/RouteSearch.FromAndTo.html" title="com.amap.api.services.route中的类">RouteSearch.FromAndTo</a>&nbsp;getFromAndTo()</pre>
<div class="block">获取路径的起点终点</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>获取路径的起点和终点</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.1.0</dd>
</dl>
</li>
</ul>
<a name="getMode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMode</h4>
<pre>public&nbsp;int&nbsp;getMode()</pre>
<div class="block">计算路径的模式</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>计算路径的模式</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.1.0</dd>
</dl>
</li>
</ul>
<a name="getExtensions--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getExtensions</h4>
<pre>public&nbsp;java.lang.String&nbsp;getExtensions()</pre>
<div class="block">扩展字段 base表示只返回基础数据，all表示所有数据 ，默认 base</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>7.6.0</dd>
</dl>
</li>
</ul>
<a name="setExtensions-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setExtensions</h4>
<pre>public&nbsp;void&nbsp;setExtensions(java.lang.String&nbsp;extensions)</pre>
<div class="block">扩展字段 base表示只返回基础数据，all表示所有数据 ，默认 base</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>7.6.0</dd>
</dl>
</li>
</ul>
<a name="getPassedPointStr--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPassedPointStr</h4>
<pre>public&nbsp;java.lang.String&nbsp;getPassedPointStr()</pre>
<div class="block">将途径点位置坐标转换为字符串输出。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>途径点位置坐标转换为字符串</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.1.0</dd>
</dl>
</li>
</ul>
<a name="getTruckSize--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTruckSize</h4>
<pre>public&nbsp;int&nbsp;getTruckSize()</pre>
<div class="block">获取设置的车辆大小</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>车辆大小</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.1.0</dd>
</dl>
</li>
</ul>
<a name="getTruckHeight--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTruckHeight</h4>
<pre>public&nbsp;float&nbsp;getTruckHeight()</pre>
<div class="block">获取设置的车辆高度</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>车辆高度</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.1.0</dd>
</dl>
</li>
</ul>
<a name="getTruckWidth--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTruckWidth</h4>
<pre>public&nbsp;float&nbsp;getTruckWidth()</pre>
<div class="block">获取设置的车辆高度</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>车辆宽度</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.1.0</dd>
</dl>
</li>
</ul>
<a name="getTruckLoad--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTruckLoad</h4>
<pre>public&nbsp;float&nbsp;getTruckLoad()</pre>
<div class="block">获取设置的车辆总重</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>车辆总重</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.1.0</dd>
</dl>
</li>
</ul>
<a name="getTruckWeight--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTruckWeight</h4>
<pre>public&nbsp;float&nbsp;getTruckWeight()</pre>
<div class="block">获取设置的车辆核定载重</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>车辆核定载重</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.1.0</dd>
</dl>
</li>
</ul>
<a name="getTruckAxis--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getTruckAxis</h4>
<pre>public&nbsp;float&nbsp;getTruckAxis()</pre>
<div class="block">获取设置的车辆轴数</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>车辆轴数</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.1.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/RouteSearch.TruckRouteQuery.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/route/RouteSearch.RideRouteQuery.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/amap/api/services/route/RouteSearch.WalkRouteQuery.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/route/RouteSearch.TruckRouteQuery.html" target="_top">框架</a></li>
<li><a href="RouteSearch.TruckRouteQuery.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
