<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>com.amap.api.services.route</title>
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<h1 class="bar"><a href="../../../../../com/amap/api/services/route/package-summary.html" target="classFrame">com.amap.api.services.route</a></h1>
<div class="indexContainer">
<h2 title="接口">接口</h2>
<ul title="接口">
<li><a href="DistanceSearch.OnDistanceSearchListener.html" title="com.amap.api.services.route中的接口" target="classFrame"><span class="interfaceName">DistanceSearch.OnDistanceSearchListener</span></a></li>
<li><a href="RouteSearch.OnRoutePlanSearchListener.html" title="com.amap.api.services.route中的接口" target="classFrame"><span class="interfaceName">RouteSearch.OnRoutePlanSearchListener</span></a></li>
<li><a href="RouteSearch.OnRouteSearchListener.html" title="com.amap.api.services.route中的接口" target="classFrame"><span class="interfaceName">RouteSearch.OnRouteSearchListener</span></a></li>
<li><a href="RouteSearch.OnTruckRouteSearchListener.html" title="com.amap.api.services.route中的接口" target="classFrame"><span class="interfaceName">RouteSearch.OnTruckRouteSearchListener</span></a></li>
<li><a href="RouteSearchV2.OnRoutePlanSearchListener.html" title="com.amap.api.services.route中的接口" target="classFrame"><span class="interfaceName">RouteSearchV2.OnRoutePlanSearchListener</span></a></li>
<li><a href="RouteSearchV2.OnRouteSearchListener.html" title="com.amap.api.services.route中的接口" target="classFrame"><span class="interfaceName">RouteSearchV2.OnRouteSearchListener</span></a></li>
<li><a href="RouteSearchV2.OnTruckRouteSearchListener.html" title="com.amap.api.services.route中的接口" target="classFrame"><span class="interfaceName">RouteSearchV2.OnTruckRouteSearchListener</span></a></li>
</ul>
<h2 title="类">类</h2>
<ul title="类">
<li><a href="BusPath.html" title="com.amap.api.services.route中的类" target="classFrame">BusPath</a></li>
<li><a href="BusPathV2.html" title="com.amap.api.services.route中的类" target="classFrame">BusPathV2</a></li>
<li><a href="BusRouteResult.html" title="com.amap.api.services.route中的类" target="classFrame">BusRouteResult</a></li>
<li><a href="BusRouteResultV2.html" title="com.amap.api.services.route中的类" target="classFrame">BusRouteResultV2</a></li>
<li><a href="BusStep.html" title="com.amap.api.services.route中的类" target="classFrame">BusStep</a></li>
<li><a href="BusStepV2.html" title="com.amap.api.services.route中的类" target="classFrame">BusStepV2</a></li>
<li><a href="Cost.html" title="com.amap.api.services.route中的类" target="classFrame">Cost</a></li>
<li><a href="DistanceItem.html" title="com.amap.api.services.route中的类" target="classFrame">DistanceItem</a></li>
<li><a href="DistanceResult.html" title="com.amap.api.services.route中的类" target="classFrame">DistanceResult</a></li>
<li><a href="DistanceSearch.html" title="com.amap.api.services.route中的类" target="classFrame">DistanceSearch</a></li>
<li><a href="DistanceSearch.DistanceQuery.html" title="com.amap.api.services.route中的类" target="classFrame">DistanceSearch.DistanceQuery</a></li>
<li><a href="District.html" title="com.amap.api.services.route中的类" target="classFrame">District</a></li>
<li><a href="Doorway.html" title="com.amap.api.services.route中的类" target="classFrame">Doorway</a></li>
<li><a href="DrivePath.html" title="com.amap.api.services.route中的类" target="classFrame">DrivePath</a></li>
<li><a href="DrivePathV2.html" title="com.amap.api.services.route中的类" target="classFrame">DrivePathV2</a></li>
<li><a href="DrivePlanPath.html" title="com.amap.api.services.route中的类" target="classFrame">DrivePlanPath</a></li>
<li><a href="DrivePlanStep.html" title="com.amap.api.services.route中的类" target="classFrame">DrivePlanStep</a></li>
<li><a href="DriveRoutePlanResult.html" title="com.amap.api.services.route中的类" target="classFrame">DriveRoutePlanResult</a></li>
<li><a href="DriveRouteResult.html" title="com.amap.api.services.route中的类" target="classFrame">DriveRouteResult</a></li>
<li><a href="DriveRouteResultV2.html" title="com.amap.api.services.route中的类" target="classFrame">DriveRouteResultV2</a></li>
<li><a href="DriveStep.html" title="com.amap.api.services.route中的类" target="classFrame">DriveStep</a></li>
<li><a href="DriveStepV2.html" title="com.amap.api.services.route中的类" target="classFrame">DriveStepV2</a></li>
<li><a href="Navi.html" title="com.amap.api.services.route中的类" target="classFrame">Navi</a></li>
<li><a href="NaviWalkType.html" title="com.amap.api.services.route中的类" target="classFrame">NaviWalkType</a></li>
<li><a href="Path.html" title="com.amap.api.services.route中的类" target="classFrame">Path</a></li>
<li><a href="Railway.html" title="com.amap.api.services.route中的类" target="classFrame">Railway</a></li>
<li><a href="RailwaySpace.html" title="com.amap.api.services.route中的类" target="classFrame">RailwaySpace</a></li>
<li><a href="RailwayStationItem.html" title="com.amap.api.services.route中的类" target="classFrame">RailwayStationItem</a></li>
<li><a href="RidePath.html" title="com.amap.api.services.route中的类" target="classFrame">RidePath</a></li>
<li><a href="RideRouteResult.html" title="com.amap.api.services.route中的类" target="classFrame">RideRouteResult</a></li>
<li><a href="RideRouteResultV2.html" title="com.amap.api.services.route中的类" target="classFrame">RideRouteResultV2</a></li>
<li><a href="RideStep.html" title="com.amap.api.services.route中的类" target="classFrame">RideStep</a></li>
<li><a href="RouteBusLineItem.html" title="com.amap.api.services.route中的类" target="classFrame">RouteBusLineItem</a></li>
<li><a href="RouteBusWalkItem.html" title="com.amap.api.services.route中的类" target="classFrame">RouteBusWalkItem</a></li>
<li><a href="RoutePlanResult.html" title="com.amap.api.services.route中的类" target="classFrame">RoutePlanResult</a></li>
<li><a href="RouteRailwayItem.html" title="com.amap.api.services.route中的类" target="classFrame">RouteRailwayItem</a></li>
<li><a href="RouteResult.html" title="com.amap.api.services.route中的类" target="classFrame">RouteResult</a></li>
<li><a href="RouteSearch.html" title="com.amap.api.services.route中的类" target="classFrame">RouteSearch</a></li>
<li><a href="RouteSearch.BusRouteQuery.html" title="com.amap.api.services.route中的类" target="classFrame">RouteSearch.BusRouteQuery</a></li>
<li><a href="RouteSearch.DrivePlanQuery.html" title="com.amap.api.services.route中的类" target="classFrame">RouteSearch.DrivePlanQuery</a></li>
<li><a href="RouteSearch.DriveRouteQuery.html" title="com.amap.api.services.route中的类" target="classFrame">RouteSearch.DriveRouteQuery</a></li>
<li><a href="RouteSearch.FromAndTo.html" title="com.amap.api.services.route中的类" target="classFrame">RouteSearch.FromAndTo</a></li>
<li><a href="RouteSearch.RideRouteQuery.html" title="com.amap.api.services.route中的类" target="classFrame">RouteSearch.RideRouteQuery</a></li>
<li><a href="RouteSearch.TruckRouteQuery.html" title="com.amap.api.services.route中的类" target="classFrame">RouteSearch.TruckRouteQuery</a></li>
<li><a href="RouteSearch.WalkRouteQuery.html" title="com.amap.api.services.route中的类" target="classFrame">RouteSearch.WalkRouteQuery</a></li>
<li><a href="RouteSearchCity.html" title="com.amap.api.services.route中的类" target="classFrame">RouteSearchCity</a></li>
<li><a href="RouteSearchV2.html" title="com.amap.api.services.route中的类" target="classFrame">RouteSearchV2</a></li>
<li><a href="RouteSearchV2.BusMode.html" title="com.amap.api.services.route中的类" target="classFrame">RouteSearchV2.BusMode</a></li>
<li><a href="RouteSearchV2.BusRouteQuery.html" title="com.amap.api.services.route中的类" target="classFrame">RouteSearchV2.BusRouteQuery</a></li>
<li><a href="RouteSearchV2.DriveRouteQuery.html" title="com.amap.api.services.route中的类" target="classFrame">RouteSearchV2.DriveRouteQuery</a></li>
<li><a href="RouteSearchV2.FromAndTo.html" title="com.amap.api.services.route中的类" target="classFrame">RouteSearchV2.FromAndTo</a></li>
<li><a href="RouteSearchV2.RideRouteQuery.html" title="com.amap.api.services.route中的类" target="classFrame">RouteSearchV2.RideRouteQuery</a></li>
<li><a href="RouteSearchV2.ShowFields.html" title="com.amap.api.services.route中的类" target="classFrame">RouteSearchV2.ShowFields</a></li>
<li><a href="RouteSearchV2.WalkRouteQuery.html" title="com.amap.api.services.route中的类" target="classFrame">RouteSearchV2.WalkRouteQuery</a></li>
<li><a href="SearchCity.html" title="com.amap.api.services.route中的类" target="classFrame">SearchCity</a></li>
<li><a href="TaxiItem.html" title="com.amap.api.services.route中的类" target="classFrame">TaxiItem</a></li>
<li><a href="TaxiItemV2.html" title="com.amap.api.services.route中的类" target="classFrame">TaxiItemV2</a></li>
<li><a href="TimeInfo.html" title="com.amap.api.services.route中的类" target="classFrame">TimeInfo</a></li>
<li><a href="TimeInfosElement.html" title="com.amap.api.services.route中的类" target="classFrame">TimeInfosElement</a></li>
<li><a href="TMC.html" title="com.amap.api.services.route中的类" target="classFrame">TMC</a></li>
<li><a href="TruckPath.html" title="com.amap.api.services.route中的类" target="classFrame">TruckPath</a></li>
<li><a href="TruckRouteRestult.html" title="com.amap.api.services.route中的类" target="classFrame">TruckRouteRestult</a></li>
<li><a href="TruckStep.html" title="com.amap.api.services.route中的类" target="classFrame">TruckStep</a></li>
<li><a href="WalkPath.html" title="com.amap.api.services.route中的类" target="classFrame">WalkPath</a></li>
<li><a href="WalkRouteResult.html" title="com.amap.api.services.route中的类" target="classFrame">WalkRouteResult</a></li>
<li><a href="WalkRouteResultV2.html" title="com.amap.api.services.route中的类" target="classFrame">WalkRouteResultV2</a></li>
<li><a href="WalkStep.html" title="com.amap.api.services.route中的类" target="classFrame">WalkStep</a></li>
</ul>
<h2 title="枚举">枚举</h2>
<ul title="枚举">
<li><a href="RouteSearchV2.DrivingStrategy.html" title="com.amap.api.services.route中的枚举" target="classFrame">RouteSearchV2.DrivingStrategy</a></li>
</ul>
</div>
</body>
</html>
