<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>RouteSearchV2.SlopeCost</title>
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="RouteSearchV2.SlopeCost";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10};
var tabs = {65535:["t0","所有方法"],2:["t2","实例方法"],8:["t4","具体方法"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/RouteSearchV2.SlopeCost.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/route/RouteSearchV2.ShowFields.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/amap/api/services/route/RouteSearchV2.SpeedCost.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/route/RouteSearchV2.SlopeCost.html" target="_top">框架</a></li>
<li><a href="RouteSearchV2.SlopeCost.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.amap.api.services.route</div>
<h2 title="类 RouteSearchV2.SlopeCost" class="title">类 RouteSearchV2.SlopeCost</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.amap.api.services.route.RouteSearchV2.SlopeCost</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>封闭类:</dt>
<dd><a href="../../../../../com/amap/api/services/route/RouteSearchV2.html" title="com.amap.api.services.route中的类">RouteSearchV2</a></dd>
</dl>
<hr>
<br>
<pre>public static class <span class="typeNameLabel">RouteSearchV2.SlopeCost</span>
extends java.lang.Object</pre>
<div class="block">坡度消耗权值

     //* @exclude javadoc中不包含</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.SlopeCost.html#SlopeCost--">SlopeCost</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>所有方法</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">实例方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">具体方法</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.SlopeCost.html#getDown--">getDown</a></span>()</code>
<div class="block">获取坡度下降的回收系数，坡度下降导致的势能减少与车辆回收电量间的比例关系，势能减量/SLOPE_DOWN=回收电量，单位J/KWH，取值范围暂无限制</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.SlopeCost.html#getUp--">getUp</a></span>()</code>
<div class="block">获取坡度上升的消耗系数，坡度上升导致的势能提升与车辆耗电量间的比例关系，势能增量/SLOPE_UP=耗电量，单位J/KWH，取值范围[0-3600000]</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.SlopeCost.html#setDown-float-">setDown</a></span>(float&nbsp;down)</code>
<div class="block">设置坡度下降的回收系数，坡度下降导致的势能减少与车辆回收电量间的比例关系，势能减量/SLOPE_DOWN=回收电量，单位J/KWH，取值范围暂无限制</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.SlopeCost.html#setUp-float-">setUp</a></span>(float&nbsp;up)</code>
<div class="block">设置坡度上升的消耗系数，坡度上升导致的势能提升与车辆耗电量间的比例关系，势能增量/SLOPE_UP=耗电量，单位J/KWH，取值范围[0-3600000]</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="SlopeCost--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>SlopeCost</h4>
<pre>public&nbsp;SlopeCost()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="getUp--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getUp</h4>
<pre>public&nbsp;float&nbsp;getUp()</pre>
<div class="block">获取坡度上升的消耗系数，坡度上升导致的势能提升与车辆耗电量间的比例关系，势能增量/SLOPE_UP=耗电量，单位J/KWH，取值范围[0-3600000]</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>坡度上升的消耗系数，坡度上升导致的势能提升与车辆耗电量间的比例关系，势能增量/SLOPE_UP=耗电量，单位J/KWH，取值范围[0-3600000]</dd>
</dl>
</li>
</ul>
<a name="setUp-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setUp</h4>
<pre>public&nbsp;void&nbsp;setUp(float&nbsp;up)</pre>
<div class="block">设置坡度上升的消耗系数，坡度上升导致的势能提升与车辆耗电量间的比例关系，势能增量/SLOPE_UP=耗电量，单位J/KWH，取值范围[0-3600000]</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>up</code> - 坡度上升的消耗系数，坡度上升导致的势能提升与车辆耗电量间的比例关系，势能增量/SLOPE_UP=耗电量，单位J/KWH，取值范围[0-3600000]</dd>
</dl>
</li>
</ul>
<a name="getDown--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDown</h4>
<pre>public&nbsp;float&nbsp;getDown()</pre>
<div class="block">获取坡度下降的回收系数，坡度下降导致的势能减少与车辆回收电量间的比例关系，势能减量/SLOPE_DOWN=回收电量，单位J/KWH，取值范围暂无限制</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>坡度下降的回收系数，坡度下降导致的势能减少与车辆回收电量间的比例关系，势能减量/SLOPE_DOWN=回收电量，单位J/KWH，取值范围暂无限制</dd>
</dl>
</li>
</ul>
<a name="setDown-float-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setDown</h4>
<pre>public&nbsp;void&nbsp;setDown(float&nbsp;down)</pre>
<div class="block">设置坡度下降的回收系数，坡度下降导致的势能减少与车辆回收电量间的比例关系，势能减量/SLOPE_DOWN=回收电量，单位J/KWH，取值范围暂无限制</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>down</code> - 坡度下降的回收系数，坡度下降导致的势能减少与车辆回收电量间的比例关系，势能减量/SLOPE_DOWN=回收电量，单位J/KWH，取值范围暂无限制</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/RouteSearchV2.SlopeCost.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/route/RouteSearchV2.ShowFields.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/amap/api/services/route/RouteSearchV2.SpeedCost.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/route/RouteSearchV2.SlopeCost.html" target="_top">框架</a></li>
<li><a href="RouteSearchV2.SlopeCost.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
