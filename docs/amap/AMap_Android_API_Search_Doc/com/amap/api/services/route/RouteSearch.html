<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>RouteSearch</title>
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="RouteSearch";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":42,"i1":42,"i2":10,"i3":10,"i4":42,"i5":42,"i6":42,"i7":42,"i8":10,"i9":10,"i10":42,"i11":42,"i12":10,"i13":10,"i14":10};
var tabs = {65535:["t0","所有方法"],2:["t2","实例方法"],8:["t4","具体方法"],32:["t6","已过时的方法"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/RouteSearch.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/route/RouteResult.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/amap/api/services/route/RouteSearch.BusRouteQuery.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/route/RouteSearch.html" target="_top">框架</a></li>
<li><a href="RouteSearch.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li><a href="#nested.class.summary">嵌套</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li><a href="#field.detail">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.amap.api.services.route</div>
<h2 title="类 RouteSearch" class="title">类 RouteSearch</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.amap.api.services.route.RouteSearch</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">RouteSearch</span>
extends java.lang.Object</pre>
<div class="block">该类路径规划搜索的入口，定义此类开始路径规划搜索</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>嵌套类概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="嵌套类概要表, 列表嵌套类和解释">
<caption><span>嵌套类</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">类和说明</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.BusRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearch.BusRouteQuery</a></span></code>
<div class="block">此类定义了公交路径规划查询路径的起终点、计算路径的模式、城市和是否计算夜班车。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.DrivePlanQuery.html" title="com.amap.api.services.route中的类">RouteSearch.DrivePlanQuery</a></span></code>
<div class="block">此类定义了驾车未来路径查询规划,最大支持未来七天的规划。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.DriveRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearch.DriveRouteQuery</a></span></code>
<div class="block">此类定义了驾车路径查询规划。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.FromAndTo.html" title="com.amap.api.services.route中的类">RouteSearch.FromAndTo</a></span></code>
<div class="block">构造路径规划的起点和终点坐标。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static interface&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.OnRoutePlanSearchListener.html" title="com.amap.api.services.route中的接口">RouteSearch.OnRoutePlanSearchListener</a></span></code>
<div class="block">未来路径规划回调方法</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static interface&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.OnRouteSearchListener.html" title="com.amap.api.services.route中的接口">RouteSearch.OnRouteSearchListener</a></span></code>
<div class="block">本类为路径搜索结果的异步处理回调接口。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static interface&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.OnTruckRouteSearchListener.html" title="com.amap.api.services.route中的接口">RouteSearch.OnTruckRouteSearchListener</a></span></code>
<div class="block">货车路径规划回调方法</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.RideRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearch.RideRouteQuery</a></span></code>
<div class="block">此类定义了骑行路径的起终点和计算路径的模式。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.TruckRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearch.TruckRouteQuery</a></span></code>
<div class="block">此类定义了货车路径的起终点和计算路径的模式。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.WalkRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearch.WalkRouteQuery</a></span></code>
<div class="block">此类定义了步行路径的起终点和计算路径的模式。</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>字段概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="字段概要表, 列表字段和解释">
<caption><span>字段</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">字段和说明</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#BUS_COMFORTABLE">BUS_COMFORTABLE</a></span></code>
<div class="block">最舒适。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#BUS_DEFAULT">BUS_DEFAULT</a></span></code>
<div class="block">最快捷模式。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#BUS_LEASE_CHANGE">BUS_LEASE_CHANGE</a></span></code>
<div class="block">最少换乘。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#BUS_LEASE_WALK">BUS_LEASE_WALK</a></span></code>
<div class="block">最少步行。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#BUS_NO_SUBWAY">BUS_NO_SUBWAY</a></span></code>
<div class="block">不乘地铁。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#BUS_SAVE_MONEY">BUS_SAVE_MONEY</a></span></code>
<div class="block">最经济模式。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#BusComfortable">BusComfortable</a></span></code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;
<div class="block"><span class="deprecationComment">自V4.0.0以后，请参考 <a href="../../../../../com/amap/api/services/route/RouteSearch.html#BUS_COMFORTABLE"><code>RouteSearch.BUS_COMFORTABLE</code></a></span></div>
</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#BusDefault">BusDefault</a></span></code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;
<div class="block"><span class="deprecationComment">自V4.0.0以后，请参考 <a href="../../../../../com/amap/api/services/route/RouteSearch.html#BUS_DEFAULT"><code>RouteSearch.BUS_DEFAULT</code></a></span></div>
</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#BusLeaseChange">BusLeaseChange</a></span></code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;
<div class="block"><span class="deprecationComment">自V4.0.0以后，请参考 <a href="../../../../../com/amap/api/services/route/RouteSearch.html#BUS_LEASE_CHANGE"><code>RouteSearch.BUS_LEASE_CHANGE</code></a></span></div>
</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#BusLeaseWalk">BusLeaseWalk</a></span></code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;
<div class="block"><span class="deprecationComment">自V4.0.0以后，请参考 <a href="../../../../../com/amap/api/services/route/RouteSearch.html#BUS_LEASE_WALK"><code>RouteSearch.BUS_LEASE_WALK</code></a></span></div>
</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#BusNoSubway">BusNoSubway</a></span></code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;
<div class="block"><span class="deprecationComment">自V4.0.0以后，请参考 <a href="../../../../../com/amap/api/services/route/RouteSearch.html#BUS_NO_SUBWAY"><code>RouteSearch.BUS_NO_SUBWAY</code></a></span></div>
</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#BusSaveMoney">BusSaveMoney</a></span></code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;
<div class="block"><span class="deprecationComment">自V4.0.0以后，请参考 <a href="../../../../../com/amap/api/services/route/RouteSearch.html#BUS_SAVE_MONEY"><code>RouteSearch.BUS_SAVE_MONEY</code></a></span></div>
</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#DRIVEING_PLAN_AVOID_CONGESTION_CHOICE_HIGHWAY">DRIVEING_PLAN_AVOID_CONGESTION_CHOICE_HIGHWAY</a></span></code>
<div class="block">返回的结果会优先考虑高速路，并且会考虑路况躲避拥堵，与高德地图的“躲避拥堵&高速优先”策略一致</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#DRIVEING_PLAN_AVOID_CONGESTION_FASTEST_SAVE_MONEY">DRIVEING_PLAN_AVOID_CONGESTION_FASTEST_SAVE_MONEY</a></span></code>
<div class="block">避让拥堵&速度优先&避免收费</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#DRIVEING_PLAN_AVOID_CONGESTION_NO_HIGHWAY">DRIVEING_PLAN_AVOID_CONGESTION_NO_HIGHWAY</a></span></code>
<div class="block">返回的结果考虑路况，尽量躲避拥堵而规划路径，并且不走高速，与高德地图的“躲避拥堵&不走高速”策略一致</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#DRIVEING_PLAN_AVOID_CONGESTION_SAVE_MONEY">DRIVEING_PLAN_AVOID_CONGESTION_SAVE_MONEY</a></span></code>
<div class="block">返回路径规划结果会尽量的躲避拥堵，并且规划收费较低甚至免费的路径结果，与高德地图的“躲避拥堵&避免收费”策略一致</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#DRIVEING_PLAN_AVOID_CONGESTION_SAVE_MONEY_NO_HIGHWAY">DRIVEING_PLAN_AVOID_CONGESTION_SAVE_MONEY_NO_HIGHWAY</a></span></code>
<div class="block">返回的结果尽量躲避拥堵，规划收费较低甚至免费的路径结果，并且尽量不走高速路，与高德地图的“避免拥堵&避免收费&不走高速”策略一致</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#DRIVEING_PLAN_CHOICE_HIGHWAY">DRIVEING_PLAN_CHOICE_HIGHWAY</a></span></code>
<div class="block">返回的结果会优先选择高速路，与高德地图的“高速优先”策略一致</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#DRIVEING_PLAN_DEFAULT">DRIVEING_PLAN_DEFAULT</a></span></code>
<div class="block">返回的结果考虑路况，尽量躲避拥堵而规划路径，与高德地图的“躲避拥堵”策略一致</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#DRIVEING_PLAN_FASTEST_SHORTEST">DRIVEING_PLAN_FASTEST_SHORTEST</a></span></code>
<div class="block">不考虑路况，返回速度最优、耗时最短的路</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#DRIVEING_PLAN_NO_HIGHWAY">DRIVEING_PLAN_NO_HIGHWAY</a></span></code>
<div class="block">返回的结果不走高速，与高德地图“不走高速”策略一致</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#DRIVEING_PLAN_SAVE_MONEY">DRIVEING_PLAN_SAVE_MONEY</a></span></code>
<div class="block">返回的结果尽可能规划收费较低甚至免费的路径，与高德地图“避免收费”策略一致</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#DRIVEING_PLAN_SAVE_MONEY_NO_HIGHWAY">DRIVEING_PLAN_SAVE_MONEY_NO_HIGHWAY</a></span></code>
<div class="block">返回的结果尽量不走高速，并且尽量规划收费较低甚至免费的路径结果，与高德地图的“避免收费&不走高速”策略一致</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#DRIVING_EXCLUDE_FERRY">DRIVING_EXCLUDE_FERRY</a></span></code>
<div class="block">海外生效 规避道路类型-渡船</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#DRIVING_EXCLUDE_MOTORWAY">DRIVING_EXCLUDE_MOTORWAY</a></span></code>
<div class="block">海外生效 规避道路类型-高速路</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#DRIVING_EXCLUDE_TOLL">DRIVING_EXCLUDE_TOLL</a></span></code>
<div class="block">海外生效 规避道路类型-收费道路</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#DRIVING_MULTI_CHOICE_AVOID_CONGESTION">DRIVING_MULTI_CHOICE_AVOID_CONGESTION</a></span></code>
<div class="block">多备选，躲避拥堵（考虑路况）</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#DRIVING_MULTI_CHOICE_AVOID_CONGESTION_NO_HIGHWAY">DRIVING_MULTI_CHOICE_AVOID_CONGESTION_NO_HIGHWAY</a></span></code>
<div class="block">多备选，躲避拥堵，不走高速（考虑路况）</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#DRIVING_MULTI_CHOICE_AVOID_CONGESTION_NO_HIGHWAY_SAVE_MONEY">DRIVING_MULTI_CHOICE_AVOID_CONGESTION_NO_HIGHWAY_SAVE_MONEY</a></span></code>
<div class="block">多备选，躲避拥堵，不走高速，费用优先（考虑路况）</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#DRIVING_MULTI_CHOICE_AVOID_CONGESTION_SAVE_MONEY">DRIVING_MULTI_CHOICE_AVOID_CONGESTION_SAVE_MONEY</a></span></code>
<div class="block">多备选，躲避拥堵，费用优先（考虑路况）</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#DRIVING_MULTI_CHOICE_HIGHWAY">DRIVING_MULTI_CHOICE_HIGHWAY</a></span></code>
<div class="block">多备选，高速优先</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#DRIVING_MULTI_CHOICE_HIGHWAY_AVOID_CONGESTION">DRIVING_MULTI_CHOICE_HIGHWAY_AVOID_CONGESTION</a></span></code>
<div class="block">多备选，高速优先，躲避拥堵（考虑路况）</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#DRIVING_MULTI_CHOICE_NO_HIGHWAY">DRIVING_MULTI_CHOICE_NO_HIGHWAY</a></span></code>
<div class="block">多备选，不走高速</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#DRIVING_MULTI_CHOICE_SAVE_MONEY">DRIVING_MULTI_CHOICE_SAVE_MONEY</a></span></code>
<div class="block">多备选，费用优先</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#DRIVING_MULTI_CHOICE_SAVE_MONEY_NO_HIGHWAY">DRIVING_MULTI_CHOICE_SAVE_MONEY_NO_HIGHWAY</a></span></code>
<div class="block">多备选，费用有限，不走高速</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#DRIVING_MULTI_STRATEGY_FASTEST_SAVE_MONEY_SHORTEST">DRIVING_MULTI_STRATEGY_FASTEST_SAVE_MONEY_SHORTEST</a></span></code>
<div class="block">同时使用速度优先、费用优先、距离优先三个策略计算路径。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#DRIVING_MULTI_STRATEGY_FASTEST_SHORTEST">DRIVING_MULTI_STRATEGY_FASTEST_SHORTEST</a></span></code>
<div class="block">多备选，时间最短，距离最短</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#DRIVING_MULTI_STRATEGY_FASTEST_SHORTEST_AVOID_CONGESTION">DRIVING_MULTI_STRATEGY_FASTEST_SHORTEST_AVOID_CONGESTION</a></span></code>
<div class="block">多备选，时间最短，距离最短，躲避拥堵（考虑路况）</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#DRIVING_NORMAL_CAR">DRIVING_NORMAL_CAR</a></span></code>
<div class="block">普通汽车</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#DRIVING_PLUGIN_HYBRID_CAR">DRIVING_PLUGIN_HYBRID_CAR</a></span></code>
<div class="block">插电混动车</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#DRIVING_PURE_ELECTRIC_VEHICLE">DRIVING_PURE_ELECTRIC_VEHICLE</a></span></code>
<div class="block">纯电动车</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#DRIVING_SINGLE_AVOID_CONGESTION">DRIVING_SINGLE_AVOID_CONGESTION</a></span></code>
<div class="block">避免拥堵。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#DRIVING_SINGLE_DEFAULT">DRIVING_SINGLE_DEFAULT</a></span></code>
<div class="block">速度优先</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#DRIVING_SINGLE_NO_EXPRESSWAYS">DRIVING_SINGLE_NO_EXPRESSWAYS</a></span></code>
<div class="block">不走快速路。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#DRIVING_SINGLE_NO_HIGHWAY">DRIVING_SINGLE_NO_HIGHWAY</a></span></code>
<div class="block">不走高速。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#DRIVING_SINGLE_NO_HIGHWAY_SAVE_MONEY">DRIVING_SINGLE_NO_HIGHWAY_SAVE_MONEY</a></span></code>
<div class="block">不走高速且避免收费。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#DRIVING_SINGLE_NO_HIGHWAY_SAVE_MONEY_AVOID_CONGESTION">DRIVING_SINGLE_NO_HIGHWAY_SAVE_MONEY_AVOID_CONGESTION</a></span></code>
<div class="block">不走高速且躲避收费和拥堵。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#DRIVING_SINGLE_SAVE_MONEY">DRIVING_SINGLE_SAVE_MONEY</a></span></code>
<div class="block">费用优先（不走收费路的最快道路）。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#DRIVING_SINGLE_SAVE_MONEY_AVOID_CONGESTION">DRIVING_SINGLE_SAVE_MONEY_AVOID_CONGESTION</a></span></code>
<div class="block">避免收费与拥堵。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#DRIVING_SINGLE_SHORTEST">DRIVING_SINGLE_SHORTEST</a></span></code>
<div class="block">距离优先。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#DrivingAvoidCongestion">DrivingAvoidCongestion</a></span></code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;
<div class="block"><span class="deprecationComment">自V4.0.0以后，请参考 <a href="../../../../../com/amap/api/services/route/RouteSearch.html#DRIVING_SINGLE_AVOID_CONGESTION"><code>RouteSearch.DRIVING_SINGLE_AVOID_CONGESTION</code></a></span></div>
</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#DrivingDefault">DrivingDefault</a></span></code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;
<div class="block"><span class="deprecationComment">自V4.0.0以后，请参考 <a href="../../../../../com/amap/api/services/route/RouteSearch.html#DRIVING_SINGLE_DEFAULT"><code>RouteSearch.DRIVING_SINGLE_DEFAULT</code></a></span></div>
</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#DrivingMultiStrategy">DrivingMultiStrategy</a></span></code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;
<div class="block"><span class="deprecationComment">自V4.0.0以后，请参考 <a href="../../../../../com/amap/api/services/route/RouteSearch.html#DRIVING_MULTI_STRATEGY_FASTEST_SAVE_MONEY_SHORTEST"><code>RouteSearch.DRIVING_MULTI_STRATEGY_FASTEST_SAVE_MONEY_SHORTEST</code></a></span></div>
</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#DrivingNoExpressways">DrivingNoExpressways</a></span></code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;
<div class="block"><span class="deprecationComment">自V4.0.0以后，请参考 <a href="../../../../../com/amap/api/services/route/RouteSearch.html#DRIVING_SINGLE_NO_EXPRESSWAYS"><code>RouteSearch.DRIVING_SINGLE_NO_EXPRESSWAYS</code></a></span></div>
</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#DrivingNoHighAvoidCongestionSaveMoney">DrivingNoHighAvoidCongestionSaveMoney</a></span></code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;
<div class="block"><span class="deprecationComment">自V4.0.0以后，请参考 <a href="../../../../../com/amap/api/services/route/RouteSearch.html#DRIVING_SINGLE_NO_HIGHWAY_SAVE_MONEY_AVOID_CONGESTION"><code>RouteSearch.DRIVING_SINGLE_NO_HIGHWAY_SAVE_MONEY_AVOID_CONGESTION</code></a></span></div>
</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#DrivingNoHighWay">DrivingNoHighWay</a></span></code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;
<div class="block"><span class="deprecationComment">自V4.0.0以后，请参考 <a href="../../../../../com/amap/api/services/route/RouteSearch.html#DRIVING_SINGLE_NO_HIGHWAY"><code>RouteSearch.DRIVING_SINGLE_NO_HIGHWAY</code></a></span></div>
</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#DrivingNoHighWaySaveMoney">DrivingNoHighWaySaveMoney</a></span></code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;
<div class="block"><span class="deprecationComment">自V4.0.0以后，请参考 <a href="../../../../../com/amap/api/services/route/RouteSearch.html#DRIVING_SINGLE_NO_HIGHWAY_SAVE_MONEY"><code>RouteSearch.DRIVING_SINGLE_NO_HIGHWAY_SAVE_MONEY</code></a></span></div>
</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#DrivingSaveMoney">DrivingSaveMoney</a></span></code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;
<div class="block"><span class="deprecationComment">自V4.0.0以后，请参考 <a href="../../../../../com/amap/api/services/route/RouteSearch.html#DRIVING_SINGLE_SAVE_MONEY"><code>RouteSearch.DRIVING_SINGLE_SAVE_MONEY</code></a></span></div>
</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#DrivingSaveMoneyAvoidCongestion">DrivingSaveMoneyAvoidCongestion</a></span></code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;
<div class="block"><span class="deprecationComment">自V4.0.0以后，请参考 <a href="../../../../../com/amap/api/services/route/RouteSearch.html#DRIVING_SINGLE_SAVE_MONEY_AVOID_CONGESTION"><code>RouteSearch.DRIVING_SINGLE_SAVE_MONEY_AVOID_CONGESTION</code></a></span></div>
</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#DrivingShortDistance">DrivingShortDistance</a></span></code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;
<div class="block"><span class="deprecationComment">自V4.0.0以后，请参考 <a href="../../../../../com/amap/api/services/route/RouteSearch.html#DRIVING_SINGLE_SHORTEST"><code>RouteSearch.DRIVING_SINGLE_SHORTEST</code></a></span></div>
</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#EXTENSIONS_ALL">EXTENSIONS_ALL</a></span></code>
<div class="block">扩展字段all，会返回完整参数</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#EXTENSIONS_BASE">EXTENSIONS_BASE</a></span></code>
<div class="block">扩展字段base，会返回部分参数</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#RIDING_DEFAULT">RIDING_DEFAULT</a></span></code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;
<div class="block"><span class="deprecationComment">骑行不再提供模式相关设置</span></div>
</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#RIDING_FAST">RIDING_FAST</a></span></code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;
<div class="block"><span class="deprecationComment">骑行不再提供模式相关设置</span></div>
</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#RIDING_RECOMMEND">RIDING_RECOMMEND</a></span></code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;
<div class="block"><span class="deprecationComment">骑行不再提供骑行相关设置</span></div>
</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#RidingDefault">RidingDefault</a></span></code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;
<div class="block"><span class="deprecationComment">自V4.0.0以后，请参考 <a href="../../../../../com/amap/api/services/route/RouteSearch.html#RIDING_DEFAULT"><code>RouteSearch.RIDING_DEFAULT</code></a></span></div>
</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#RidingFast">RidingFast</a></span></code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;
<div class="block"><span class="deprecationComment">自V4.0.0以后，请参考 <a href="../../../../../com/amap/api/services/route/RouteSearch.html#RIDING_FAST"><code>RouteSearch.RIDING_FAST</code></a></span></div>
</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#RidingRecommend">RidingRecommend</a></span></code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;
<div class="block"><span class="deprecationComment">自V4.0.0以后，请参考 <a href="../../../../../com/amap/api/services/route/RouteSearch.html#RIDING_RECOMMEND"><code>RouteSearch.RIDING_RECOMMEND</code></a></span></div>
</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#TRUCK_AVOID_CONGESTION">TRUCK_AVOID_CONGESTION</a></span></code>
<div class="block">结果考虑路况，尽量躲避拥堵而规划路径，与高德地图的“躲避拥堵”策略一致</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#TRUCK_AVOID_CONGESTION__SAVE_MONEY">TRUCK_AVOID_CONGESTION__SAVE_MONEY</a></span></code>
<div class="block">结果会尽量的躲避拥堵，并且规划收费较低甚至免费的路径结果，与高德地图的“躲避拥堵&避免收费”策略一致</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#TRUCK_AVOID_CONGESTION__SAVE_MONEY_NO_HIGHWAY">TRUCK_AVOID_CONGESTION__SAVE_MONEY_NO_HIGHWAY</a></span></code>
<div class="block">结果尽量躲避拥堵，规划收费较低甚至免费的路径结果，并且尽量不走高速路，与高德地图的“避免拥堵&避免收费&不走高速”策略一致</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#TRUCK_AVOID_CONGESTION_CHOICE_HIGHWAY">TRUCK_AVOID_CONGESTION_CHOICE_HIGHWAY</a></span></code>
<div class="block">结果会优先考虑高速路，并且会考虑路况躲避拥堵，与高德地图的“躲避拥堵&高速优先”策略一致</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#TRUCK_AVOID_CONGESTION_NO_HIGHWAY">TRUCK_AVOID_CONGESTION_NO_HIGHWAY</a></span></code>
<div class="block">结果考虑路况，尽量躲避拥堵而规划路径，并且不走高速，与高德地图的“躲避拥堵&不走高速”策略一致</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#TRUCK_CHOICE_HIGHWAY">TRUCK_CHOICE_HIGHWAY</a></span></code>
<div class="block">结果会优先选择高速路，与高德地图的“高速优先”策略一致</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#TRUCK_NO_HIGHWAY">TRUCK_NO_HIGHWAY</a></span></code>
<div class="block">结果不走高速，与高德地图“不走高速”策略一致</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#TRUCK_SAVE_MONEY">TRUCK_SAVE_MONEY</a></span></code>
<div class="block">结果尽可能规划收费较低甚至免费的路径，与高德地图“避免收费”策略一致</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#TRUCK_SAVE_MONEY_NO_HIGHWAY">TRUCK_SAVE_MONEY_NO_HIGHWAY</a></span></code>
<div class="block">结果尽量不走高速，并且尽量规划收费较低甚至免费的路径结果，与高德地图的“避免收费&不走高速”策略一致</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#TRUCK_SIZE_HEAVY">TRUCK_SIZE_HEAVY</a></span></code>
<div class="block">重型车</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#TRUCK_SIZE_LIGHT">TRUCK_SIZE_LIGHT</a></span></code>
<div class="block">轻型车</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#TRUCK_SIZE_MEDIUM">TRUCK_SIZE_MEDIUM</a></span></code>
<div class="block">中型车</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#TRUCK_SIZE_MINI">TRUCK_SIZE_MINI</a></span></code>
<div class="block">微型车</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#WALK_DEFAULT">WALK_DEFAULT</a></span></code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;
<div class="block"><span class="deprecationComment">步行不再提供模式相关设置</span></div>
</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#WALK_MULTI_PATH">WALK_MULTI_PATH</a></span></code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;
<div class="block"><span class="deprecationComment">步行不再提供模式相关设置</span></div>
</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#WalkDefault">WalkDefault</a></span></code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;
<div class="block"><span class="deprecationComment">自V4.0.0以后，请参考 <a href="../../../../../com/amap/api/services/route/RouteSearch.html#WALK_DEFAULT"><code>RouteSearch.WALK_DEFAULT</code></a></span></div>
</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#WalkMultipath">WalkMultipath</a></span></code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;
<div class="block"><span class="deprecationComment">自V4.0.0以后，请参考 <a href="../../../../../com/amap/api/services/route/RouteSearch.html#WALK_MULTI_PATH"><code>RouteSearch.WALK_MULTI_PATH</code></a></span></div>
</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#RouteSearch-Context-">RouteSearch</a></span>(Context&nbsp;context)</code>
<div class="block">根据给定的参数来构造一个RouteSearch的新对象。</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>所有方法</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">实例方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">具体方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t6" class="tableTab"><span><a href="javascript:show(32);">已过时的方法</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/services/route/BusRouteResult.html" title="com.amap.api.services.route中的类">BusRouteResult</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#calculateBusRoute-com.amap.api.services.route.RouteSearch.BusRouteQuery-">calculateBusRoute</a></span>(<a href="../../../../../com/amap/api/services/route/RouteSearch.BusRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearch.BusRouteQuery</a>&nbsp;busQuery)</code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;
<div class="block"><span class="deprecationComment">自9.4.0废弃 <a href="../../../../../com/amap/api/services/route/RouteSearchV2.html#calculateBusRoute-com.amap.api.services.route.RouteSearchV2.BusRouteQuery-"><code>RouteSearchV2.calculateBusRoute(com.amap.api.services.route.RouteSearchV2.BusRouteQuery)</code></a></span></div>
</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#calculateBusRouteAsyn-com.amap.api.services.route.RouteSearch.BusRouteQuery-">calculateBusRouteAsyn</a></span>(<a href="../../../../../com/amap/api/services/route/RouteSearch.BusRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearch.BusRouteQuery</a>&nbsp;busQuery)</code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;
<div class="block"><span class="deprecationComment">自9.4.0废弃 <a href="../../../../../com/amap/api/services/route/RouteSearchV2.html#calculateBusRouteAsyn-com.amap.api.services.route.RouteSearchV2.BusRouteQuery-"><code>RouteSearchV2.calculateBusRouteAsyn(com.amap.api.services.route.RouteSearchV2.BusRouteQuery)</code></a></span></div>
</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/services/route/DriveRoutePlanResult.html" title="com.amap.api.services.route中的类">DriveRoutePlanResult</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#calculateDrivePlan-com.amap.api.services.route.RouteSearch.DrivePlanQuery-">calculateDrivePlan</a></span>(<a href="../../../../../com/amap/api/services/route/RouteSearch.DrivePlanQuery.html" title="com.amap.api.services.route中的类">RouteSearch.DrivePlanQuery</a>&nbsp;driveQuery)</code>
<div class="block">根据指定的参数来计算驾车路径。</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#calculateDrivePlanAsyn-com.amap.api.services.route.RouteSearch.DrivePlanQuery-">calculateDrivePlanAsyn</a></span>(<a href="../../../../../com/amap/api/services/route/RouteSearch.DrivePlanQuery.html" title="com.amap.api.services.route中的类">RouteSearch.DrivePlanQuery</a>&nbsp;driveQuery)</code>
<div class="block">异步处理。</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/services/route/DriveRouteResult.html" title="com.amap.api.services.route中的类">DriveRouteResult</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#calculateDriveRoute-com.amap.api.services.route.RouteSearch.DriveRouteQuery-">calculateDriveRoute</a></span>(<a href="../../../../../com/amap/api/services/route/RouteSearch.DriveRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearch.DriveRouteQuery</a>&nbsp;driveQuery)</code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;
<div class="block"><span class="deprecationComment">自9.4.0废弃 <a href="../../../../../com/amap/api/services/route/RouteSearchV2.html#calculateDriveRoute-com.amap.api.services.route.RouteSearchV2.DriveRouteQuery-"><code>RouteSearchV2.calculateDriveRoute(com.amap.api.services.route.RouteSearchV2.DriveRouteQuery)</code></a></span></div>
</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#calculateDriveRouteAsyn-com.amap.api.services.route.RouteSearch.DriveRouteQuery-">calculateDriveRouteAsyn</a></span>(<a href="../../../../../com/amap/api/services/route/RouteSearch.DriveRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearch.DriveRouteQuery</a>&nbsp;driveQuery)</code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;
<div class="block"><span class="deprecationComment">自9.4.0废弃 <a href="../../../../../com/amap/api/services/route/RouteSearchV2.html#calculateDriveRouteAsyn-com.amap.api.services.route.RouteSearchV2.DriveRouteQuery-"><code>RouteSearchV2.calculateDriveRouteAsyn(com.amap.api.services.route.RouteSearchV2.DriveRouteQuery)</code></a></span></div>
</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/services/route/RideRouteResult.html" title="com.amap.api.services.route中的类">RideRouteResult</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#calculateRideRoute-com.amap.api.services.route.RouteSearch.RideRouteQuery-">calculateRideRoute</a></span>(<a href="../../../../../com/amap/api/services/route/RouteSearch.RideRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearch.RideRouteQuery</a>&nbsp;rideQuery)</code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;
<div class="block"><span class="deprecationComment">自9.4.0废弃 <a href="../../../../../com/amap/api/services/route/RouteSearchV2.html#calculateRideRoute-com.amap.api.services.route.RouteSearchV2.RideRouteQuery-"><code>RouteSearchV2.calculateRideRoute(com.amap.api.services.route.RouteSearchV2.RideRouteQuery)</code></a></span></div>
</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#calculateRideRouteAsyn-com.amap.api.services.route.RouteSearch.RideRouteQuery-">calculateRideRouteAsyn</a></span>(<a href="../../../../../com/amap/api/services/route/RouteSearch.RideRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearch.RideRouteQuery</a>&nbsp;rideQuery)</code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;
<div class="block"><span class="deprecationComment">自9.4.0废弃 <a href="../../../../../com/amap/api/services/route/RouteSearchV2.html#calculateRideRouteAsyn-com.amap.api.services.route.RouteSearchV2.RideRouteQuery-"><code>RouteSearchV2.calculateRideRouteAsyn(com.amap.api.services.route.RouteSearchV2.RideRouteQuery)</code></a></span></div>
</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/services/route/TruckRouteRestult.html" title="com.amap.api.services.route中的类">TruckRouteRestult</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#calculateTruckRoute-com.amap.api.services.route.RouteSearch.TruckRouteQuery-">calculateTruckRoute</a></span>(<a href="../../../../../com/amap/api/services/route/RouteSearch.TruckRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearch.TruckRouteQuery</a>&nbsp;truckQuery)</code>
<div class="block">根据指定的参数来计算货车路径。</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#calculateTruckRouteAsyn-com.amap.api.services.route.RouteSearch.TruckRouteQuery-">calculateTruckRouteAsyn</a></span>(<a href="../../../../../com/amap/api/services/route/RouteSearch.TruckRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearch.TruckRouteQuery</a>&nbsp;truckQuery)</code>
<div class="block">异步处理。</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/services/route/WalkRouteResult.html" title="com.amap.api.services.route中的类">WalkRouteResult</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#calculateWalkRoute-com.amap.api.services.route.RouteSearch.WalkRouteQuery-">calculateWalkRoute</a></span>(<a href="../../../../../com/amap/api/services/route/RouteSearch.WalkRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearch.WalkRouteQuery</a>&nbsp;walkQuery)</code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;
<div class="block"><span class="deprecationComment">自9.4.0废弃 <a href="../../../../../com/amap/api/services/route/RouteSearchV2.html#calculateWalkRoute-com.amap.api.services.route.RouteSearchV2.WalkRouteQuery-"><code>RouteSearchV2.calculateWalkRoute(com.amap.api.services.route.RouteSearchV2.WalkRouteQuery)</code></a></span></div>
</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#calculateWalkRouteAsyn-com.amap.api.services.route.RouteSearch.WalkRouteQuery-">calculateWalkRouteAsyn</a></span>(<a href="../../../../../com/amap/api/services/route/RouteSearch.WalkRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearch.WalkRouteQuery</a>&nbsp;walkQuery)</code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;
<div class="block"><span class="deprecationComment">自废弃9.4.0废弃 <a href="../../../../../com/amap/api/services/route/RouteSearchV2.html#calculateWalkRouteAsyn-com.amap.api.services.route.RouteSearchV2.WalkRouteQuery-"><code>RouteSearchV2.calculateWalkRouteAsyn(com.amap.api.services.route.RouteSearchV2.WalkRouteQuery)</code></a></span></div>
</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#setOnRoutePlanSearchListener-com.amap.api.services.route.RouteSearch.OnRoutePlanSearchListener-">setOnRoutePlanSearchListener</a></span>(<a href="../../../../../com/amap/api/services/route/RouteSearch.OnRoutePlanSearchListener.html" title="com.amap.api.services.route中的接口">RouteSearch.OnRoutePlanSearchListener</a>&nbsp;onRoutePlanSearchListener)</code>
<div class="block">未来路径规划搜索结果监听接口设置。</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#setOnTruckRouteSearchListener-com.amap.api.services.route.RouteSearch.OnTruckRouteSearchListener-">setOnTruckRouteSearchListener</a></span>(<a href="../../../../../com/amap/api/services/route/RouteSearch.OnTruckRouteSearchListener.html" title="com.amap.api.services.route中的接口">RouteSearch.OnTruckRouteSearchListener</a>&nbsp;onTruckRouteSearchListener)</code>
<div class="block">路径货车搜索结果监听接口设置。</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearch.html#setRouteSearchListener-com.amap.api.services.route.RouteSearch.OnRouteSearchListener-">setRouteSearchListener</a></span>(<a href="../../../../../com/amap/api/services/route/RouteSearch.OnRouteSearchListener.html" title="com.amap.api.services.route中的接口">RouteSearch.OnRouteSearchListener</a>&nbsp;listener)</code>
<div class="block">路径搜索结果监听接口设置。</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>字段详细资料</h3>
<a name="BusDefault">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BusDefault</h4>
<pre>public static final&nbsp;int BusDefault</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;<span class="deprecationComment">自V4.0.0以后，请参考 <a href="../../../../../com/amap/api/services/route/RouteSearch.html#BUS_DEFAULT"><code>RouteSearch.BUS_DEFAULT</code></a></span></div>
<div class="block">最快捷模式。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.route.RouteSearch.BusDefault">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="BusSaveMoney">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BusSaveMoney</h4>
<pre>public static final&nbsp;int BusSaveMoney</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;<span class="deprecationComment">自V4.0.0以后，请参考 <a href="../../../../../com/amap/api/services/route/RouteSearch.html#BUS_SAVE_MONEY"><code>RouteSearch.BUS_SAVE_MONEY</code></a></span></div>
<div class="block">最经济模式。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.route.RouteSearch.BusSaveMoney">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="BusLeaseChange">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BusLeaseChange</h4>
<pre>public static final&nbsp;int BusLeaseChange</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;<span class="deprecationComment">自V4.0.0以后，请参考 <a href="../../../../../com/amap/api/services/route/RouteSearch.html#BUS_LEASE_CHANGE"><code>RouteSearch.BUS_LEASE_CHANGE</code></a></span></div>
<div class="block">最少换乘。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.route.RouteSearch.BusLeaseChange">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="BusLeaseWalk">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BusLeaseWalk</h4>
<pre>public static final&nbsp;int BusLeaseWalk</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;<span class="deprecationComment">自V4.0.0以后，请参考 <a href="../../../../../com/amap/api/services/route/RouteSearch.html#BUS_LEASE_WALK"><code>RouteSearch.BUS_LEASE_WALK</code></a></span></div>
<div class="block">最少步行。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.route.RouteSearch.BusLeaseWalk">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="BusComfortable">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BusComfortable</h4>
<pre>public static final&nbsp;int BusComfortable</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;<span class="deprecationComment">自V4.0.0以后，请参考 <a href="../../../../../com/amap/api/services/route/RouteSearch.html#BUS_COMFORTABLE"><code>RouteSearch.BUS_COMFORTABLE</code></a></span></div>
<div class="block">最舒适。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.route.RouteSearch.BusComfortable">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="BusNoSubway">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BusNoSubway</h4>
<pre>public static final&nbsp;int BusNoSubway</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;<span class="deprecationComment">自V4.0.0以后，请参考 <a href="../../../../../com/amap/api/services/route/RouteSearch.html#BUS_NO_SUBWAY"><code>RouteSearch.BUS_NO_SUBWAY</code></a></span></div>
<div class="block">不乘地铁。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.route.RouteSearch.BusNoSubway">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="DrivingDefault">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DrivingDefault</h4>
<pre>public static final&nbsp;int DrivingDefault</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;<span class="deprecationComment">自V4.0.0以后，请参考 <a href="../../../../../com/amap/api/services/route/RouteSearch.html#DRIVING_SINGLE_DEFAULT"><code>RouteSearch.DRIVING_SINGLE_DEFAULT</code></a></span></div>
<div class="block">速度优先。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.route.RouteSearch.DrivingDefault">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="DrivingSaveMoney">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DrivingSaveMoney</h4>
<pre>public static final&nbsp;int DrivingSaveMoney</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;<span class="deprecationComment">自V4.0.0以后，请参考 <a href="../../../../../com/amap/api/services/route/RouteSearch.html#DRIVING_SINGLE_SAVE_MONEY"><code>RouteSearch.DRIVING_SINGLE_SAVE_MONEY</code></a></span></div>
<div class="block">费用优先（不走收费路的最快道路）。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.route.RouteSearch.DrivingSaveMoney">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="DrivingShortDistance">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DrivingShortDistance</h4>
<pre>public static final&nbsp;int DrivingShortDistance</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;<span class="deprecationComment">自V4.0.0以后，请参考 <a href="../../../../../com/amap/api/services/route/RouteSearch.html#DRIVING_SINGLE_SHORTEST"><code>RouteSearch.DRIVING_SINGLE_SHORTEST</code></a></span></div>
<div class="block">距离优先。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.route.RouteSearch.DrivingShortDistance">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="DrivingNoExpressways">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DrivingNoExpressways</h4>
<pre>public static final&nbsp;int DrivingNoExpressways</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;<span class="deprecationComment">自V4.0.0以后，请参考 <a href="../../../../../com/amap/api/services/route/RouteSearch.html#DRIVING_SINGLE_NO_EXPRESSWAYS"><code>RouteSearch.DRIVING_SINGLE_NO_EXPRESSWAYS</code></a></span></div>
<div class="block">不走快速路。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.route.RouteSearch.DrivingNoExpressways">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="DrivingAvoidCongestion">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DrivingAvoidCongestion</h4>
<pre>public static final&nbsp;int DrivingAvoidCongestion</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;<span class="deprecationComment">自V4.0.0以后，请参考 <a href="../../../../../com/amap/api/services/route/RouteSearch.html#DRIVING_SINGLE_AVOID_CONGESTION"><code>RouteSearch.DRIVING_SINGLE_AVOID_CONGESTION</code></a></span></div>
<div class="block">避免拥堵。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.route.RouteSearch.DrivingAvoidCongestion">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="DrivingMultiStrategy">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DrivingMultiStrategy</h4>
<pre>public static final&nbsp;int DrivingMultiStrategy</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;<span class="deprecationComment">自V4.0.0以后，请参考 <a href="../../../../../com/amap/api/services/route/RouteSearch.html#DRIVING_MULTI_STRATEGY_FASTEST_SAVE_MONEY_SHORTEST"><code>RouteSearch.DRIVING_MULTI_STRATEGY_FASTEST_SAVE_MONEY_SHORTEST</code></a></span></div>
<div class="block">同时使用速度优先、费用优先、距离优先三个策略计算路径。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.route.RouteSearch.DrivingMultiStrategy">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="DrivingNoHighWay">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DrivingNoHighWay</h4>
<pre>public static final&nbsp;int DrivingNoHighWay</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;<span class="deprecationComment">自V4.0.0以后，请参考 <a href="../../../../../com/amap/api/services/route/RouteSearch.html#DRIVING_SINGLE_NO_HIGHWAY"><code>RouteSearch.DRIVING_SINGLE_NO_HIGHWAY</code></a></span></div>
<div class="block">不走高速。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.route.RouteSearch.DrivingNoHighWay">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="DrivingNoHighWaySaveMoney">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DrivingNoHighWaySaveMoney</h4>
<pre>public static final&nbsp;int DrivingNoHighWaySaveMoney</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;<span class="deprecationComment">自V4.0.0以后，请参考 <a href="../../../../../com/amap/api/services/route/RouteSearch.html#DRIVING_SINGLE_NO_HIGHWAY_SAVE_MONEY"><code>RouteSearch.DRIVING_SINGLE_NO_HIGHWAY_SAVE_MONEY</code></a></span></div>
<div class="block">不走高速且避免收费。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.route.RouteSearch.DrivingNoHighWaySaveMoney">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="DrivingSaveMoneyAvoidCongestion">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DrivingSaveMoneyAvoidCongestion</h4>
<pre>public static final&nbsp;int DrivingSaveMoneyAvoidCongestion</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;<span class="deprecationComment">自V4.0.0以后，请参考 <a href="../../../../../com/amap/api/services/route/RouteSearch.html#DRIVING_SINGLE_SAVE_MONEY_AVOID_CONGESTION"><code>RouteSearch.DRIVING_SINGLE_SAVE_MONEY_AVOID_CONGESTION</code></a></span></div>
<div class="block">避免收费与拥堵。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.route.RouteSearch.DrivingSaveMoneyAvoidCongestion">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="DrivingNoHighAvoidCongestionSaveMoney">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DrivingNoHighAvoidCongestionSaveMoney</h4>
<pre>public static final&nbsp;int DrivingNoHighAvoidCongestionSaveMoney</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;<span class="deprecationComment">自V4.0.0以后，请参考 <a href="../../../../../com/amap/api/services/route/RouteSearch.html#DRIVING_SINGLE_NO_HIGHWAY_SAVE_MONEY_AVOID_CONGESTION"><code>RouteSearch.DRIVING_SINGLE_NO_HIGHWAY_SAVE_MONEY_AVOID_CONGESTION</code></a></span></div>
<div class="block">不走高速且躲避收费和拥堵。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.route.RouteSearch.DrivingNoHighAvoidCongestionSaveMoney">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="WalkDefault">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>WalkDefault</h4>
<pre>public static final&nbsp;int WalkDefault</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;<span class="deprecationComment">自V4.0.0以后，请参考 <a href="../../../../../com/amap/api/services/route/RouteSearch.html#WALK_DEFAULT"><code>RouteSearch.WALK_DEFAULT</code></a></span></div>
<div class="block">路径为步行模式。只提供一条步行方案。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.route.RouteSearch.WalkDefault">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="WalkMultipath">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>WalkMultipath</h4>
<pre>public static final&nbsp;int WalkMultipath</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;<span class="deprecationComment">自V4.0.0以后，请参考 <a href="../../../../../com/amap/api/services/route/RouteSearch.html#WALK_MULTI_PATH"><code>RouteSearch.WALK_MULTI_PATH</code></a></span></div>
<div class="block">提供备选步行方案（有可能无备选方案）。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.route.RouteSearch.WalkMultipath">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="RidingDefault">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RidingDefault</h4>
<pre>public static final&nbsp;int RidingDefault</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;<span class="deprecationComment">自V4.0.0以后，请参考 <a href="../../../../../com/amap/api/services/route/RouteSearch.html#RIDING_DEFAULT"><code>RouteSearch.RIDING_DEFAULT</code></a></span></div>
<div class="block">骑行推荐路线及最快路线综合。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.5.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.route.RouteSearch.RidingDefault">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="RidingRecommend">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RidingRecommend</h4>
<pre>public static final&nbsp;int RidingRecommend</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;<span class="deprecationComment">自V4.0.0以后，请参考 <a href="../../../../../com/amap/api/services/route/RouteSearch.html#RIDING_RECOMMEND"><code>RouteSearch.RIDING_RECOMMEND</code></a></span></div>
<div class="block">骑行推荐路线。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.5.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.route.RouteSearch.RidingRecommend">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="RidingFast">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RidingFast</h4>
<pre>public static final&nbsp;int RidingFast</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;<span class="deprecationComment">自V4.0.0以后，请参考 <a href="../../../../../com/amap/api/services/route/RouteSearch.html#RIDING_FAST"><code>RouteSearch.RIDING_FAST</code></a></span></div>
<div class="block">骑行最快路线。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.5.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.route.RouteSearch.RidingFast">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="BUS_DEFAULT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BUS_DEFAULT</h4>
<pre>public static final&nbsp;int BUS_DEFAULT</pre>
<div class="block">最快捷模式。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>4.0.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.route.RouteSearch.BUS_DEFAULT">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="BUS_SAVE_MONEY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BUS_SAVE_MONEY</h4>
<pre>public static final&nbsp;int BUS_SAVE_MONEY</pre>
<div class="block">最经济模式。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>4.0.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.route.RouteSearch.BUS_SAVE_MONEY">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="BUS_LEASE_CHANGE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BUS_LEASE_CHANGE</h4>
<pre>public static final&nbsp;int BUS_LEASE_CHANGE</pre>
<div class="block">最少换乘。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>4.0.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.route.RouteSearch.BUS_LEASE_CHANGE">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="BUS_LEASE_WALK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BUS_LEASE_WALK</h4>
<pre>public static final&nbsp;int BUS_LEASE_WALK</pre>
<div class="block">最少步行。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>4.0.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.route.RouteSearch.BUS_LEASE_WALK">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="BUS_COMFORTABLE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BUS_COMFORTABLE</h4>
<pre>public static final&nbsp;int BUS_COMFORTABLE</pre>
<div class="block">最舒适。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>4.0.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.route.RouteSearch.BUS_COMFORTABLE">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="BUS_NO_SUBWAY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BUS_NO_SUBWAY</h4>
<pre>public static final&nbsp;int BUS_NO_SUBWAY</pre>
<div class="block">不乘地铁。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>4.0.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.route.RouteSearch.BUS_NO_SUBWAY">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="WALK_DEFAULT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>WALK_DEFAULT</h4>
<pre>public static final&nbsp;int WALK_DEFAULT</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;<span class="deprecationComment">步行不再提供模式相关设置</span></div>
<div class="block">路径为步行模式。只提供一条步行方案。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>4.0.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.route.RouteSearch.WALK_DEFAULT">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="WALK_MULTI_PATH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>WALK_MULTI_PATH</h4>
<pre>public static final&nbsp;int WALK_MULTI_PATH</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;<span class="deprecationComment">步行不再提供模式相关设置</span></div>
<div class="block">提供备选步行方案（有可能无备选方案）。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>4.0.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.route.RouteSearch.WALK_MULTI_PATH">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="RIDING_DEFAULT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RIDING_DEFAULT</h4>
<pre>public static final&nbsp;int RIDING_DEFAULT</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;<span class="deprecationComment">骑行不再提供模式相关设置</span></div>
<div class="block">骑行推荐路线及最快路线综合。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>4.0.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.route.RouteSearch.RIDING_DEFAULT">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="RIDING_RECOMMEND">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RIDING_RECOMMEND</h4>
<pre>public static final&nbsp;int RIDING_RECOMMEND</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;<span class="deprecationComment">骑行不再提供骑行相关设置</span></div>
<div class="block">骑行推荐路线。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>4.0.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.route.RouteSearch.RIDING_RECOMMEND">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="RIDING_FAST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RIDING_FAST</h4>
<pre>public static final&nbsp;int RIDING_FAST</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;<span class="deprecationComment">骑行不再提供模式相关设置</span></div>
<div class="block">骑行最快路线。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>4.0.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.route.RouteSearch.RIDING_FAST">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="DRIVING_SINGLE_DEFAULT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DRIVING_SINGLE_DEFAULT</h4>
<pre>public static final&nbsp;int DRIVING_SINGLE_DEFAULT</pre>
<div class="block">速度优先</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>4.0.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.route.RouteSearch.DRIVING_SINGLE_DEFAULT">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="DRIVING_SINGLE_SAVE_MONEY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DRIVING_SINGLE_SAVE_MONEY</h4>
<pre>public static final&nbsp;int DRIVING_SINGLE_SAVE_MONEY</pre>
<div class="block">费用优先（不走收费路的最快道路）。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>4.0.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.route.RouteSearch.DRIVING_SINGLE_SAVE_MONEY">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="DRIVING_SINGLE_SHORTEST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DRIVING_SINGLE_SHORTEST</h4>
<pre>public static final&nbsp;int DRIVING_SINGLE_SHORTEST</pre>
<div class="block">距离优先。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>4.0.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.route.RouteSearch.DRIVING_SINGLE_SHORTEST">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="DRIVING_SINGLE_NO_EXPRESSWAYS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DRIVING_SINGLE_NO_EXPRESSWAYS</h4>
<pre>public static final&nbsp;int DRIVING_SINGLE_NO_EXPRESSWAYS</pre>
<div class="block">不走快速路。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>4.0.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.route.RouteSearch.DRIVING_SINGLE_NO_EXPRESSWAYS">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="DRIVING_SINGLE_AVOID_CONGESTION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DRIVING_SINGLE_AVOID_CONGESTION</h4>
<pre>public static final&nbsp;int DRIVING_SINGLE_AVOID_CONGESTION</pre>
<div class="block">避免拥堵。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>4.0.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.route.RouteSearch.DRIVING_SINGLE_AVOID_CONGESTION">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="DRIVING_MULTI_STRATEGY_FASTEST_SAVE_MONEY_SHORTEST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DRIVING_MULTI_STRATEGY_FASTEST_SAVE_MONEY_SHORTEST</h4>
<pre>public static final&nbsp;int DRIVING_MULTI_STRATEGY_FASTEST_SAVE_MONEY_SHORTEST</pre>
<div class="block">同时使用速度优先、费用优先、距离优先三个策略计算路径。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>4.0.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.route.RouteSearch.DRIVING_MULTI_STRATEGY_FASTEST_SAVE_MONEY_SHORTEST">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="DRIVING_SINGLE_NO_HIGHWAY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DRIVING_SINGLE_NO_HIGHWAY</h4>
<pre>public static final&nbsp;int DRIVING_SINGLE_NO_HIGHWAY</pre>
<div class="block">不走高速。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>4.0.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.route.RouteSearch.DRIVING_SINGLE_NO_HIGHWAY">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="DRIVING_SINGLE_NO_HIGHWAY_SAVE_MONEY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DRIVING_SINGLE_NO_HIGHWAY_SAVE_MONEY</h4>
<pre>public static final&nbsp;int DRIVING_SINGLE_NO_HIGHWAY_SAVE_MONEY</pre>
<div class="block">不走高速且避免收费。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>4.0.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.route.RouteSearch.DRIVING_SINGLE_NO_HIGHWAY_SAVE_MONEY">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="DRIVING_SINGLE_SAVE_MONEY_AVOID_CONGESTION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DRIVING_SINGLE_SAVE_MONEY_AVOID_CONGESTION</h4>
<pre>public static final&nbsp;int DRIVING_SINGLE_SAVE_MONEY_AVOID_CONGESTION</pre>
<div class="block">避免收费与拥堵。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>4.0.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.route.RouteSearch.DRIVING_SINGLE_SAVE_MONEY_AVOID_CONGESTION">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="DRIVING_SINGLE_NO_HIGHWAY_SAVE_MONEY_AVOID_CONGESTION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DRIVING_SINGLE_NO_HIGHWAY_SAVE_MONEY_AVOID_CONGESTION</h4>
<pre>public static final&nbsp;int DRIVING_SINGLE_NO_HIGHWAY_SAVE_MONEY_AVOID_CONGESTION</pre>
<div class="block">不走高速且躲避收费和拥堵。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>4.0.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.route.RouteSearch.DRIVING_SINGLE_NO_HIGHWAY_SAVE_MONEY_AVOID_CONGESTION">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="DRIVING_MULTI_STRATEGY_FASTEST_SHORTEST_AVOID_CONGESTION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DRIVING_MULTI_STRATEGY_FASTEST_SHORTEST_AVOID_CONGESTION</h4>
<pre>public static final&nbsp;int DRIVING_MULTI_STRATEGY_FASTEST_SHORTEST_AVOID_CONGESTION</pre>
<div class="block">多备选，时间最短，距离最短，躲避拥堵（考虑路况）</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>4.0.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.route.RouteSearch.DRIVING_MULTI_STRATEGY_FASTEST_SHORTEST_AVOID_CONGESTION">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="DRIVING_MULTI_STRATEGY_FASTEST_SHORTEST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DRIVING_MULTI_STRATEGY_FASTEST_SHORTEST</h4>
<pre>public static final&nbsp;int DRIVING_MULTI_STRATEGY_FASTEST_SHORTEST</pre>
<div class="block">多备选，时间最短，距离最短</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>4.0.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.route.RouteSearch.DRIVING_MULTI_STRATEGY_FASTEST_SHORTEST">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="DRIVING_MULTI_CHOICE_AVOID_CONGESTION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DRIVING_MULTI_CHOICE_AVOID_CONGESTION</h4>
<pre>public static final&nbsp;int DRIVING_MULTI_CHOICE_AVOID_CONGESTION</pre>
<div class="block">多备选，躲避拥堵（考虑路况）</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>4.0.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.route.RouteSearch.DRIVING_MULTI_CHOICE_AVOID_CONGESTION">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="DRIVING_MULTI_CHOICE_NO_HIGHWAY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DRIVING_MULTI_CHOICE_NO_HIGHWAY</h4>
<pre>public static final&nbsp;int DRIVING_MULTI_CHOICE_NO_HIGHWAY</pre>
<div class="block">多备选，不走高速</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>4.0.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.route.RouteSearch.DRIVING_MULTI_CHOICE_NO_HIGHWAY">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="DRIVING_MULTI_CHOICE_SAVE_MONEY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DRIVING_MULTI_CHOICE_SAVE_MONEY</h4>
<pre>public static final&nbsp;int DRIVING_MULTI_CHOICE_SAVE_MONEY</pre>
<div class="block">多备选，费用优先</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>4.0.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.route.RouteSearch.DRIVING_MULTI_CHOICE_SAVE_MONEY">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="DRIVING_MULTI_CHOICE_AVOID_CONGESTION_NO_HIGHWAY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DRIVING_MULTI_CHOICE_AVOID_CONGESTION_NO_HIGHWAY</h4>
<pre>public static final&nbsp;int DRIVING_MULTI_CHOICE_AVOID_CONGESTION_NO_HIGHWAY</pre>
<div class="block">多备选，躲避拥堵，不走高速（考虑路况）</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>4.0.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.route.RouteSearch.DRIVING_MULTI_CHOICE_AVOID_CONGESTION_NO_HIGHWAY">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="DRIVING_MULTI_CHOICE_SAVE_MONEY_NO_HIGHWAY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DRIVING_MULTI_CHOICE_SAVE_MONEY_NO_HIGHWAY</h4>
<pre>public static final&nbsp;int DRIVING_MULTI_CHOICE_SAVE_MONEY_NO_HIGHWAY</pre>
<div class="block">多备选，费用有限，不走高速</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>4.0.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.route.RouteSearch.DRIVING_MULTI_CHOICE_SAVE_MONEY_NO_HIGHWAY">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="DRIVING_MULTI_CHOICE_AVOID_CONGESTION_SAVE_MONEY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DRIVING_MULTI_CHOICE_AVOID_CONGESTION_SAVE_MONEY</h4>
<pre>public static final&nbsp;int DRIVING_MULTI_CHOICE_AVOID_CONGESTION_SAVE_MONEY</pre>
<div class="block">多备选，躲避拥堵，费用优先（考虑路况）</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>4.0.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.route.RouteSearch.DRIVING_MULTI_CHOICE_AVOID_CONGESTION_SAVE_MONEY">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="DRIVING_MULTI_CHOICE_AVOID_CONGESTION_NO_HIGHWAY_SAVE_MONEY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DRIVING_MULTI_CHOICE_AVOID_CONGESTION_NO_HIGHWAY_SAVE_MONEY</h4>
<pre>public static final&nbsp;int DRIVING_MULTI_CHOICE_AVOID_CONGESTION_NO_HIGHWAY_SAVE_MONEY</pre>
<div class="block">多备选，躲避拥堵，不走高速，费用优先（考虑路况）</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>4.0.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.route.RouteSearch.DRIVING_MULTI_CHOICE_AVOID_CONGESTION_NO_HIGHWAY_SAVE_MONEY">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="DRIVING_MULTI_CHOICE_HIGHWAY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DRIVING_MULTI_CHOICE_HIGHWAY</h4>
<pre>public static final&nbsp;int DRIVING_MULTI_CHOICE_HIGHWAY</pre>
<div class="block">多备选，高速优先</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>4.0.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.route.RouteSearch.DRIVING_MULTI_CHOICE_HIGHWAY">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="DRIVING_MULTI_CHOICE_HIGHWAY_AVOID_CONGESTION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DRIVING_MULTI_CHOICE_HIGHWAY_AVOID_CONGESTION</h4>
<pre>public static final&nbsp;int DRIVING_MULTI_CHOICE_HIGHWAY_AVOID_CONGESTION</pre>
<div class="block">多备选，高速优先，躲避拥堵（考虑路况）</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>4.0.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.route.RouteSearch.DRIVING_MULTI_CHOICE_HIGHWAY_AVOID_CONGESTION">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="DRIVING_NORMAL_CAR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DRIVING_NORMAL_CAR</h4>
<pre>public static final&nbsp;int DRIVING_NORMAL_CAR</pre>
<div class="block">普通汽车</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.9.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.route.RouteSearch.DRIVING_NORMAL_CAR">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="DRIVING_PURE_ELECTRIC_VEHICLE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DRIVING_PURE_ELECTRIC_VEHICLE</h4>
<pre>public static final&nbsp;int DRIVING_PURE_ELECTRIC_VEHICLE</pre>
<div class="block">纯电动车</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.9.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.route.RouteSearch.DRIVING_PURE_ELECTRIC_VEHICLE">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="DRIVING_PLUGIN_HYBRID_CAR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DRIVING_PLUGIN_HYBRID_CAR</h4>
<pre>public static final&nbsp;int DRIVING_PLUGIN_HYBRID_CAR</pre>
<div class="block">插电混动车</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.9.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.route.RouteSearch.DRIVING_PLUGIN_HYBRID_CAR">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="TRUCK_AVOID_CONGESTION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TRUCK_AVOID_CONGESTION</h4>
<pre>public static final&nbsp;int TRUCK_AVOID_CONGESTION</pre>
<div class="block">结果考虑路况，尽量躲避拥堵而规划路径，与高德地图的“躲避拥堵”策略一致</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.1.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.route.RouteSearch.TRUCK_AVOID_CONGESTION">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="TRUCK_NO_HIGHWAY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TRUCK_NO_HIGHWAY</h4>
<pre>public static final&nbsp;int TRUCK_NO_HIGHWAY</pre>
<div class="block">结果不走高速，与高德地图“不走高速”策略一致</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.1.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.route.RouteSearch.TRUCK_NO_HIGHWAY">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="TRUCK_SAVE_MONEY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TRUCK_SAVE_MONEY</h4>
<pre>public static final&nbsp;int TRUCK_SAVE_MONEY</pre>
<div class="block">结果尽可能规划收费较低甚至免费的路径，与高德地图“避免收费”策略一致</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.1.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.route.RouteSearch.TRUCK_SAVE_MONEY">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="TRUCK_AVOID_CONGESTION_NO_HIGHWAY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TRUCK_AVOID_CONGESTION_NO_HIGHWAY</h4>
<pre>public static final&nbsp;int TRUCK_AVOID_CONGESTION_NO_HIGHWAY</pre>
<div class="block">结果考虑路况，尽量躲避拥堵而规划路径，并且不走高速，与高德地图的“躲避拥堵&不走高速”策略一致</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.1.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.route.RouteSearch.TRUCK_AVOID_CONGESTION_NO_HIGHWAY">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="TRUCK_SAVE_MONEY_NO_HIGHWAY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TRUCK_SAVE_MONEY_NO_HIGHWAY</h4>
<pre>public static final&nbsp;int TRUCK_SAVE_MONEY_NO_HIGHWAY</pre>
<div class="block">结果尽量不走高速，并且尽量规划收费较低甚至免费的路径结果，与高德地图的“避免收费&不走高速”策略一致</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.1.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.route.RouteSearch.TRUCK_SAVE_MONEY_NO_HIGHWAY">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="TRUCK_AVOID_CONGESTION__SAVE_MONEY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TRUCK_AVOID_CONGESTION__SAVE_MONEY</h4>
<pre>public static final&nbsp;int TRUCK_AVOID_CONGESTION__SAVE_MONEY</pre>
<div class="block">结果会尽量的躲避拥堵，并且规划收费较低甚至免费的路径结果，与高德地图的“躲避拥堵&避免收费”策略一致</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.1.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.route.RouteSearch.TRUCK_AVOID_CONGESTION__SAVE_MONEY">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="TRUCK_AVOID_CONGESTION__SAVE_MONEY_NO_HIGHWAY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TRUCK_AVOID_CONGESTION__SAVE_MONEY_NO_HIGHWAY</h4>
<pre>public static final&nbsp;int TRUCK_AVOID_CONGESTION__SAVE_MONEY_NO_HIGHWAY</pre>
<div class="block">结果尽量躲避拥堵，规划收费较低甚至免费的路径结果，并且尽量不走高速路，与高德地图的“避免拥堵&避免收费&不走高速”策略一致</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.1.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.route.RouteSearch.TRUCK_AVOID_CONGESTION__SAVE_MONEY_NO_HIGHWAY">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="TRUCK_CHOICE_HIGHWAY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TRUCK_CHOICE_HIGHWAY</h4>
<pre>public static final&nbsp;int TRUCK_CHOICE_HIGHWAY</pre>
<div class="block">结果会优先选择高速路，与高德地图的“高速优先”策略一致</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.1.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.route.RouteSearch.TRUCK_CHOICE_HIGHWAY">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="TRUCK_AVOID_CONGESTION_CHOICE_HIGHWAY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TRUCK_AVOID_CONGESTION_CHOICE_HIGHWAY</h4>
<pre>public static final&nbsp;int TRUCK_AVOID_CONGESTION_CHOICE_HIGHWAY</pre>
<div class="block">结果会优先考虑高速路，并且会考虑路况躲避拥堵，与高德地图的“躲避拥堵&高速优先”策略一致</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.1.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.route.RouteSearch.TRUCK_AVOID_CONGESTION_CHOICE_HIGHWAY">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="TRUCK_SIZE_MINI">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TRUCK_SIZE_MINI</h4>
<pre>public static final&nbsp;int TRUCK_SIZE_MINI</pre>
<div class="block">微型车</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.1.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.route.RouteSearch.TRUCK_SIZE_MINI">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="TRUCK_SIZE_LIGHT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TRUCK_SIZE_LIGHT</h4>
<pre>public static final&nbsp;int TRUCK_SIZE_LIGHT</pre>
<div class="block">轻型车</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.1.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.route.RouteSearch.TRUCK_SIZE_LIGHT">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="TRUCK_SIZE_MEDIUM">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TRUCK_SIZE_MEDIUM</h4>
<pre>public static final&nbsp;int TRUCK_SIZE_MEDIUM</pre>
<div class="block">中型车</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.1.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.route.RouteSearch.TRUCK_SIZE_MEDIUM">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="TRUCK_SIZE_HEAVY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TRUCK_SIZE_HEAVY</h4>
<pre>public static final&nbsp;int TRUCK_SIZE_HEAVY</pre>
<div class="block">重型车</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.1.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.route.RouteSearch.TRUCK_SIZE_HEAVY">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="DRIVEING_PLAN_DEFAULT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DRIVEING_PLAN_DEFAULT</h4>
<pre>public static final&nbsp;int DRIVEING_PLAN_DEFAULT</pre>
<div class="block">返回的结果考虑路况，尽量躲避拥堵而规划路径，与高德地图的“躲避拥堵”策略一致</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.9.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.route.RouteSearch.DRIVEING_PLAN_DEFAULT">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="DRIVEING_PLAN_NO_HIGHWAY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DRIVEING_PLAN_NO_HIGHWAY</h4>
<pre>public static final&nbsp;int DRIVEING_PLAN_NO_HIGHWAY</pre>
<div class="block">返回的结果不走高速，与高德地图“不走高速”策略一致</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.9.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.route.RouteSearch.DRIVEING_PLAN_NO_HIGHWAY">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="DRIVEING_PLAN_SAVE_MONEY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DRIVEING_PLAN_SAVE_MONEY</h4>
<pre>public static final&nbsp;int DRIVEING_PLAN_SAVE_MONEY</pre>
<div class="block">返回的结果尽可能规划收费较低甚至免费的路径，与高德地图“避免收费”策略一致</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.9.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.route.RouteSearch.DRIVEING_PLAN_SAVE_MONEY">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="DRIVEING_PLAN_AVOID_CONGESTION_NO_HIGHWAY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DRIVEING_PLAN_AVOID_CONGESTION_NO_HIGHWAY</h4>
<pre>public static final&nbsp;int DRIVEING_PLAN_AVOID_CONGESTION_NO_HIGHWAY</pre>
<div class="block">返回的结果考虑路况，尽量躲避拥堵而规划路径，并且不走高速，与高德地图的“躲避拥堵&不走高速”策略一致</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.9.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.route.RouteSearch.DRIVEING_PLAN_AVOID_CONGESTION_NO_HIGHWAY">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="DRIVEING_PLAN_SAVE_MONEY_NO_HIGHWAY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DRIVEING_PLAN_SAVE_MONEY_NO_HIGHWAY</h4>
<pre>public static final&nbsp;int DRIVEING_PLAN_SAVE_MONEY_NO_HIGHWAY</pre>
<div class="block">返回的结果尽量不走高速，并且尽量规划收费较低甚至免费的路径结果，与高德地图的“避免收费&不走高速”策略一致</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.9.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.route.RouteSearch.DRIVEING_PLAN_SAVE_MONEY_NO_HIGHWAY">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="DRIVEING_PLAN_AVOID_CONGESTION_SAVE_MONEY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DRIVEING_PLAN_AVOID_CONGESTION_SAVE_MONEY</h4>
<pre>public static final&nbsp;int DRIVEING_PLAN_AVOID_CONGESTION_SAVE_MONEY</pre>
<div class="block">返回路径规划结果会尽量的躲避拥堵，并且规划收费较低甚至免费的路径结果，与高德地图的“躲避拥堵&避免收费”策略一致</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.9.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.route.RouteSearch.DRIVEING_PLAN_AVOID_CONGESTION_SAVE_MONEY">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="DRIVEING_PLAN_AVOID_CONGESTION_SAVE_MONEY_NO_HIGHWAY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DRIVEING_PLAN_AVOID_CONGESTION_SAVE_MONEY_NO_HIGHWAY</h4>
<pre>public static final&nbsp;int DRIVEING_PLAN_AVOID_CONGESTION_SAVE_MONEY_NO_HIGHWAY</pre>
<div class="block">返回的结果尽量躲避拥堵，规划收费较低甚至免费的路径结果，并且尽量不走高速路，与高德地图的“避免拥堵&避免收费&不走高速”策略一致</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.9.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.route.RouteSearch.DRIVEING_PLAN_AVOID_CONGESTION_SAVE_MONEY_NO_HIGHWAY">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="DRIVEING_PLAN_CHOICE_HIGHWAY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DRIVEING_PLAN_CHOICE_HIGHWAY</h4>
<pre>public static final&nbsp;int DRIVEING_PLAN_CHOICE_HIGHWAY</pre>
<div class="block">返回的结果会优先选择高速路，与高德地图的“高速优先”策略一致</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.9.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.route.RouteSearch.DRIVEING_PLAN_CHOICE_HIGHWAY">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="DRIVEING_PLAN_AVOID_CONGESTION_CHOICE_HIGHWAY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DRIVEING_PLAN_AVOID_CONGESTION_CHOICE_HIGHWAY</h4>
<pre>public static final&nbsp;int DRIVEING_PLAN_AVOID_CONGESTION_CHOICE_HIGHWAY</pre>
<div class="block">返回的结果会优先考虑高速路，并且会考虑路况躲避拥堵，与高德地图的“躲避拥堵&高速优先”策略一致</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.9.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.route.RouteSearch.DRIVEING_PLAN_AVOID_CONGESTION_CHOICE_HIGHWAY">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="DRIVEING_PLAN_FASTEST_SHORTEST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DRIVEING_PLAN_FASTEST_SHORTEST</h4>
<pre>public static final&nbsp;int DRIVEING_PLAN_FASTEST_SHORTEST</pre>
<div class="block">不考虑路况，返回速度最优、耗时最短的路</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.9.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.route.RouteSearch.DRIVEING_PLAN_FASTEST_SHORTEST">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="DRIVEING_PLAN_AVOID_CONGESTION_FASTEST_SAVE_MONEY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DRIVEING_PLAN_AVOID_CONGESTION_FASTEST_SAVE_MONEY</h4>
<pre>public static final&nbsp;int DRIVEING_PLAN_AVOID_CONGESTION_FASTEST_SAVE_MONEY</pre>
<div class="block">避让拥堵&速度优先&避免收费</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.9.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.route.RouteSearch.DRIVEING_PLAN_AVOID_CONGESTION_FASTEST_SAVE_MONEY">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="DRIVING_EXCLUDE_TOLL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DRIVING_EXCLUDE_TOLL</h4>
<pre>public static final&nbsp;java.lang.String DRIVING_EXCLUDE_TOLL</pre>
<div class="block">海外生效 规避道路类型-收费道路</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>7.4.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.route.RouteSearch.DRIVING_EXCLUDE_TOLL">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="DRIVING_EXCLUDE_MOTORWAY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DRIVING_EXCLUDE_MOTORWAY</h4>
<pre>public static final&nbsp;java.lang.String DRIVING_EXCLUDE_MOTORWAY</pre>
<div class="block">海外生效 规避道路类型-高速路</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>7.4.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.route.RouteSearch.DRIVING_EXCLUDE_MOTORWAY">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="DRIVING_EXCLUDE_FERRY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DRIVING_EXCLUDE_FERRY</h4>
<pre>public static final&nbsp;java.lang.String DRIVING_EXCLUDE_FERRY</pre>
<div class="block">海外生效 规避道路类型-渡船</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>7.4.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.route.RouteSearch.DRIVING_EXCLUDE_FERRY">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="EXTENSIONS_ALL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>EXTENSIONS_ALL</h4>
<pre>public static final&nbsp;java.lang.String EXTENSIONS_ALL</pre>
<div class="block">扩展字段all，会返回完整参数</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>7.6.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.route.RouteSearch.EXTENSIONS_ALL">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="EXTENSIONS_BASE">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>EXTENSIONS_BASE</h4>
<pre>public static final&nbsp;java.lang.String EXTENSIONS_BASE</pre>
<div class="block">扩展字段base，会返回部分参数</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>7.6.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.route.RouteSearch.EXTENSIONS_BASE">常量字段值</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="RouteSearch-Context-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>RouteSearch</h4>
<pre>public&nbsp;RouteSearch(Context&nbsp;context)
            throws <a href="../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></pre>
<div class="block">根据给定的参数来构造一个RouteSearch的新对象。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>context</code> - 对应的Context。</dd>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code><a href="../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></code></dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="setRouteSearchListener-com.amap.api.services.route.RouteSearch.OnRouteSearchListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRouteSearchListener</h4>
<pre>public&nbsp;void&nbsp;setRouteSearchListener(<a href="../../../../../com/amap/api/services/route/RouteSearch.OnRouteSearchListener.html" title="com.amap.api.services.route中的接口">RouteSearch.OnRouteSearchListener</a>&nbsp;listener)</pre>
<div class="block">路径搜索结果监听接口设置。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>listener</code> - 路径搜索结果监听接口。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="setOnTruckRouteSearchListener-com.amap.api.services.route.RouteSearch.OnTruckRouteSearchListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setOnTruckRouteSearchListener</h4>
<pre>public&nbsp;void&nbsp;setOnTruckRouteSearchListener(<a href="../../../../../com/amap/api/services/route/RouteSearch.OnTruckRouteSearchListener.html" title="com.amap.api.services.route中的接口">RouteSearch.OnTruckRouteSearchListener</a>&nbsp;onTruckRouteSearchListener)</pre>
<div class="block">路径货车搜索结果监听接口设置。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>onTruckRouteSearchListener</code> - 货车路径搜索结果监听接口。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.1.0</dd>
</dl>
</li>
</ul>
<a name="setOnRoutePlanSearchListener-com.amap.api.services.route.RouteSearch.OnRoutePlanSearchListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setOnRoutePlanSearchListener</h4>
<pre>public&nbsp;void&nbsp;setOnRoutePlanSearchListener(<a href="../../../../../com/amap/api/services/route/RouteSearch.OnRoutePlanSearchListener.html" title="com.amap.api.services.route中的接口">RouteSearch.OnRoutePlanSearchListener</a>&nbsp;onRoutePlanSearchListener)</pre>
<div class="block">未来路径规划搜索结果监听接口设置。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>onRoutePlanSearchListener</code> - 货车路径搜索结果监听接口。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.9.0</dd>
</dl>
</li>
</ul>
<a name="calculateWalkRoute-com.amap.api.services.route.RouteSearch.WalkRouteQuery-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>calculateWalkRoute</h4>
<pre>public&nbsp;<a href="../../../../../com/amap/api/services/route/WalkRouteResult.html" title="com.amap.api.services.route中的类">WalkRouteResult</a>&nbsp;calculateWalkRoute(<a href="../../../../../com/amap/api/services/route/RouteSearch.WalkRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearch.WalkRouteQuery</a>&nbsp;walkQuery)
                                   throws <a href="../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;<span class="deprecationComment">自9.4.0废弃 <a href="../../../../../com/amap/api/services/route/RouteSearchV2.html#calculateWalkRoute-com.amap.api.services.route.RouteSearchV2.WalkRouteQuery-"><code>RouteSearchV2.calculateWalkRoute(com.amap.api.services.route.RouteSearchV2.WalkRouteQuery)</code></a></span></div>
<div class="block">根据指定的参数来计算步行路径。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>walkQuery</code> - 查询参数。</dd>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code><a href="../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></code></dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="calculateWalkRouteAsyn-com.amap.api.services.route.RouteSearch.WalkRouteQuery-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>calculateWalkRouteAsyn</h4>
<pre>public&nbsp;void&nbsp;calculateWalkRouteAsyn(<a href="../../../../../com/amap/api/services/route/RouteSearch.WalkRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearch.WalkRouteQuery</a>&nbsp;walkQuery)</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;<span class="deprecationComment">自废弃9.4.0废弃 <a href="../../../../../com/amap/api/services/route/RouteSearchV2.html#calculateWalkRouteAsyn-com.amap.api.services.route.RouteSearchV2.WalkRouteQuery-"><code>RouteSearchV2.calculateWalkRouteAsyn(com.amap.api.services.route.RouteSearchV2.WalkRouteQuery)</code></a></span></div>
<div class="block">异步处理。根据指定的参数来计算步行路径的异步处理。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>walkQuery</code> - 查询参数。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="calculateBusRoute-com.amap.api.services.route.RouteSearch.BusRouteQuery-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>calculateBusRoute</h4>
<pre>public&nbsp;<a href="../../../../../com/amap/api/services/route/BusRouteResult.html" title="com.amap.api.services.route中的类">BusRouteResult</a>&nbsp;calculateBusRoute(<a href="../../../../../com/amap/api/services/route/RouteSearch.BusRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearch.BusRouteQuery</a>&nbsp;busQuery)
                                 throws <a href="../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;<span class="deprecationComment">自9.4.0废弃 <a href="../../../../../com/amap/api/services/route/RouteSearchV2.html#calculateBusRoute-com.amap.api.services.route.RouteSearchV2.BusRouteQuery-"><code>RouteSearchV2.calculateBusRoute(com.amap.api.services.route.RouteSearchV2.BusRouteQuery)</code></a></span></div>
<div class="block">根据指定的参数来计算公交路径。只支持市内公交换乘。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>busQuery</code> - 查询参数。</dd>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code><a href="../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></code></dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="calculateBusRouteAsyn-com.amap.api.services.route.RouteSearch.BusRouteQuery-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>calculateBusRouteAsyn</h4>
<pre>public&nbsp;void&nbsp;calculateBusRouteAsyn(<a href="../../../../../com/amap/api/services/route/RouteSearch.BusRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearch.BusRouteQuery</a>&nbsp;busQuery)</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;<span class="deprecationComment">自9.4.0废弃 <a href="../../../../../com/amap/api/services/route/RouteSearchV2.html#calculateBusRouteAsyn-com.amap.api.services.route.RouteSearchV2.BusRouteQuery-"><code>RouteSearchV2.calculateBusRouteAsyn(com.amap.api.services.route.RouteSearchV2.BusRouteQuery)</code></a></span></div>
<div class="block">异步处理。根据指定的参数来计算公交换乘路径的异步处理。只支持市内公交换乘。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>busQuery</code> - 查询参数。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="calculateDriveRoute-com.amap.api.services.route.RouteSearch.DriveRouteQuery-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>calculateDriveRoute</h4>
<pre>public&nbsp;<a href="../../../../../com/amap/api/services/route/DriveRouteResult.html" title="com.amap.api.services.route中的类">DriveRouteResult</a>&nbsp;calculateDriveRoute(<a href="../../../../../com/amap/api/services/route/RouteSearch.DriveRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearch.DriveRouteQuery</a>&nbsp;driveQuery)
                                     throws <a href="../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;<span class="deprecationComment">自9.4.0废弃 <a href="../../../../../com/amap/api/services/route/RouteSearchV2.html#calculateDriveRoute-com.amap.api.services.route.RouteSearchV2.DriveRouteQuery-"><code>RouteSearchV2.calculateDriveRoute(com.amap.api.services.route.RouteSearchV2.DriveRouteQuery)</code></a></span></div>
<div class="block">根据指定的参数来计算驾车路径。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>driveQuery</code> - 查询参数。</dd>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code><a href="../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></code> - 途经点太多或避让区域太大会返回非法参数</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="calculateDriveRouteAsyn-com.amap.api.services.route.RouteSearch.DriveRouteQuery-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>calculateDriveRouteAsyn</h4>
<pre>public&nbsp;void&nbsp;calculateDriveRouteAsyn(<a href="../../../../../com/amap/api/services/route/RouteSearch.DriveRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearch.DriveRouteQuery</a>&nbsp;driveQuery)</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;<span class="deprecationComment">自9.4.0废弃 <a href="../../../../../com/amap/api/services/route/RouteSearchV2.html#calculateDriveRouteAsyn-com.amap.api.services.route.RouteSearchV2.DriveRouteQuery-"><code>RouteSearchV2.calculateDriveRouteAsyn(com.amap.api.services.route.RouteSearchV2.DriveRouteQuery)</code></a></span></div>
<div class="block">异步处理。根据指定的参数来计算驾车路径的异步处理。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>driveQuery</code> - 查询参数。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="calculateRideRouteAsyn-com.amap.api.services.route.RouteSearch.RideRouteQuery-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>calculateRideRouteAsyn</h4>
<pre>public&nbsp;void&nbsp;calculateRideRouteAsyn(<a href="../../../../../com/amap/api/services/route/RouteSearch.RideRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearch.RideRouteQuery</a>&nbsp;rideQuery)</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;<span class="deprecationComment">自9.4.0废弃 <a href="../../../../../com/amap/api/services/route/RouteSearchV2.html#calculateRideRouteAsyn-com.amap.api.services.route.RouteSearchV2.RideRouteQuery-"><code>RouteSearchV2.calculateRideRouteAsyn(com.amap.api.services.route.RouteSearchV2.RideRouteQuery)</code></a></span></div>
<div class="block">异步处理。根据指定的参数来计算骑行路径的异步处理。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>rideQuery</code> - 查询参数。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.5.0</dd>
</dl>
</li>
</ul>
<a name="calculateRideRoute-com.amap.api.services.route.RouteSearch.RideRouteQuery-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>calculateRideRoute</h4>
<pre>public&nbsp;<a href="../../../../../com/amap/api/services/route/RideRouteResult.html" title="com.amap.api.services.route中的类">RideRouteResult</a>&nbsp;calculateRideRoute(<a href="../../../../../com/amap/api/services/route/RouteSearch.RideRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearch.RideRouteQuery</a>&nbsp;rideQuery)
                                   throws <a href="../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;<span class="deprecationComment">自9.4.0废弃 <a href="../../../../../com/amap/api/services/route/RouteSearchV2.html#calculateRideRoute-com.amap.api.services.route.RouteSearchV2.RideRouteQuery-"><code>RouteSearchV2.calculateRideRoute(com.amap.api.services.route.RouteSearchV2.RideRouteQuery)</code></a></span></div>
<div class="block">根据指定的参数来计算骑行路径，同步接口。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>rideQuery</code> - 查询参数。</dd>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code><a href="../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></code></dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.5.0</dd>
</dl>
</li>
</ul>
<a name="calculateTruckRoute-com.amap.api.services.route.RouteSearch.TruckRouteQuery-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>calculateTruckRoute</h4>
<pre>public&nbsp;<a href="../../../../../com/amap/api/services/route/TruckRouteRestult.html" title="com.amap.api.services.route中的类">TruckRouteRestult</a>&nbsp;calculateTruckRoute(<a href="../../../../../com/amap/api/services/route/RouteSearch.TruckRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearch.TruckRouteQuery</a>&nbsp;truckQuery)
                                      throws <a href="../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></pre>
<div class="block">根据指定的参数来计算货车路径。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>truckQuery</code> - 查询参数。</dd>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code><a href="../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></code> - 途经点太多会返回非法参数</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.1.0</dd>
</dl>
</li>
</ul>
<a name="calculateTruckRouteAsyn-com.amap.api.services.route.RouteSearch.TruckRouteQuery-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>calculateTruckRouteAsyn</h4>
<pre>public&nbsp;void&nbsp;calculateTruckRouteAsyn(<a href="../../../../../com/amap/api/services/route/RouteSearch.TruckRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearch.TruckRouteQuery</a>&nbsp;truckQuery)</pre>
<div class="block">异步处理。根据指定的参数来计算货车路径的异步处理。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>truckQuery</code> - 查询参数。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.1.0</dd>
</dl>
</li>
</ul>
<a name="calculateDrivePlan-com.amap.api.services.route.RouteSearch.DrivePlanQuery-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>calculateDrivePlan</h4>
<pre>public&nbsp;<a href="../../../../../com/amap/api/services/route/DriveRoutePlanResult.html" title="com.amap.api.services.route中的类">DriveRoutePlanResult</a>&nbsp;calculateDrivePlan(<a href="../../../../../com/amap/api/services/route/RouteSearch.DrivePlanQuery.html" title="com.amap.api.services.route中的类">RouteSearch.DrivePlanQuery</a>&nbsp;driveQuery)
                                        throws <a href="../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></pre>
<div class="block">根据指定的参数来计算驾车路径。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>driveQuery</code> - 查询参数。</dd>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code><a href="../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></code></dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.9.0</dd>
</dl>
</li>
</ul>
<a name="calculateDrivePlanAsyn-com.amap.api.services.route.RouteSearch.DrivePlanQuery-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>calculateDrivePlanAsyn</h4>
<pre>public&nbsp;void&nbsp;calculateDrivePlanAsyn(<a href="../../../../../com/amap/api/services/route/RouteSearch.DrivePlanQuery.html" title="com.amap.api.services.route中的类">RouteSearch.DrivePlanQuery</a>&nbsp;driveQuery)</pre>
<div class="block">异步处理。根据指定的参数来计算驾车路径的异步处理。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>driveQuery</code> - 查询参数。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.9.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/RouteSearch.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/route/RouteResult.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/amap/api/services/route/RouteSearch.BusRouteQuery.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/route/RouteSearch.html" target="_top">框架</a></li>
<li><a href="RouteSearch.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li><a href="#nested.class.summary">嵌套</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li><a href="#field.detail">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
