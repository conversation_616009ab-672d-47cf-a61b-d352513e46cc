<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>DistanceItem</title>
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="DistanceItem";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10};
var tabs = {65535:["t0","所有方法"],2:["t2","实例方法"],8:["t4","具体方法"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/DistanceItem.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/route/Cost.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/amap/api/services/route/DistanceResult.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/route/DistanceItem.html" target="_top">框架</a></li>
<li><a href="DistanceItem.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li><a href="#field.summary">字段</a>&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li><a href="#field.detail">字段</a>&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.amap.api.services.route</div>
<h2 title="类 DistanceItem" class="title">类 DistanceItem</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.amap.api.services.route.DistanceItem</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">DistanceItem</span>
extends java.lang.Object</pre>
<div class="block">距离测量返回结果中某一个结果的具体信息</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.1.0</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>字段概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="字段概要表, 列表字段和解释">
<caption><span>字段</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">字段和说明</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/DistanceItem.html#ERROR_CODE_NO_DRIVE">ERROR_CODE_NO_DRIVE</a></span></code>
<div class="block">指定地点之间没有可以行车的道路</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/DistanceItem.html#ERROR_CODE_NOT_IN_CHINA">ERROR_CODE_NOT_IN_CHINA</a></span></code>
<div class="block">起点/终点不在中国境内</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/DistanceItem.html#ERROR_CODE_TOO_FAR">ERROR_CODE_TOO_FAR</a></span></code>
<div class="block">起点/终点 距离所有道路均距离过远（例如在海洋/矿业）</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>所有方法</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">实例方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">具体方法</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/DistanceItem.html#getDestId--">getDestId</a></span>()</code>
<div class="block">终点坐标，终点坐标序列号（从１开始）<br>
     虽然终点仅支持一个坐标，但为了和起点统一也按照坐标序列号方式返回</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/DistanceItem.html#getDistance--">getDistance</a></span>()</code>
<div class="block">路径距离，单位：米</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/DistanceItem.html#getDuration--">getDuration</a></span>()</code>
<div class="block">预计行驶时间，单位：秒</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/DistanceItem.html#getErrorCode--">getErrorCode</a></span>()</code>
<div class="block">仅在出错的时候显示此字段。</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/DistanceItem.html#getErrorInfo--">getErrorInfo</a></span>()</code>
<div class="block">仅在出错的时候显示该字段。</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/DistanceItem.html#getOriginId--">getOriginId</a></span>()</code>
<div class="block">起点坐标，起点坐标序列号（从１开始）<br>
     如传入4个起点，originId为1则表示第一个点对应的结果</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>字段详细资料</h3>
<a name="ERROR_CODE_NO_DRIVE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ERROR_CODE_NO_DRIVE</h4>
<pre>public final&nbsp;int ERROR_CODE_NO_DRIVE</pre>
<div class="block">指定地点之间没有可以行车的道路</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.1.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.route.DistanceItem.ERROR_CODE_NO_DRIVE">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="ERROR_CODE_TOO_FAR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ERROR_CODE_TOO_FAR</h4>
<pre>public final&nbsp;int ERROR_CODE_TOO_FAR</pre>
<div class="block">起点/终点 距离所有道路均距离过远（例如在海洋/矿业）</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.1.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.route.DistanceItem.ERROR_CODE_TOO_FAR">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="ERROR_CODE_NOT_IN_CHINA">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>ERROR_CODE_NOT_IN_CHINA</h4>
<pre>public final&nbsp;int ERROR_CODE_NOT_IN_CHINA</pre>
<div class="block">起点/终点不在中国境内</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.1.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.route.DistanceItem.ERROR_CODE_NOT_IN_CHINA">常量字段值</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="getOriginId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getOriginId</h4>
<pre>public&nbsp;int&nbsp;getOriginId()</pre>
<div class="block">起点坐标，起点坐标序列号（从１开始）<br>
     如传入4个起点，originId为1则表示第一个点对应的结果</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.1.0</dd>
</dl>
</li>
</ul>
<a name="getDestId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDestId</h4>
<pre>public&nbsp;int&nbsp;getDestId()</pre>
<div class="block">终点坐标，终点坐标序列号（从１开始）<br>
     虽然终点仅支持一个坐标，但为了和起点统一也按照坐标序列号方式返回</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.1.0</dd>
</dl>
</li>
</ul>
<a name="getDistance--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDistance</h4>
<pre>public&nbsp;float&nbsp;getDistance()</pre>
<div class="block">路径距离，单位：米</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.1.0</dd>
</dl>
</li>
</ul>
<a name="getDuration--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDuration</h4>
<pre>public&nbsp;float&nbsp;getDuration()</pre>
<div class="block">预计行驶时间，单位：秒</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.1.0</dd>
</dl>
</li>
</ul>
<a name="getErrorInfo--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getErrorInfo</h4>
<pre>public&nbsp;java.lang.String&nbsp;getErrorInfo()</pre>
<div class="block">仅在出错的时候显示该字段。大部分显示“未知错误”<br>
 由于此接口支持批量请求，建议不论批量与否用此字段判断请求是否成功</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.1.0</dd>
</dl>
</li>
</ul>
<a name="getErrorCode--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getErrorCode</h4>
<pre>public&nbsp;int&nbsp;getErrorCode()</pre>
<div class="block">仅在出错的时候显示此字段。
 错误码，在驾车模式下：<br>
 <a href="../../../../../com/amap/api/services/route/DistanceItem.html#ERROR_CODE_NO_DRIVE"><code>DistanceItem.ERROR_CODE_NO_DRIVE</code></a> 1，指定地点之间没有可以行车的道<br>
 <a href="../../../../../com/amap/api/services/route/DistanceItem.html#ERROR_CODE_TOO_FAR"><code>DistanceItem.ERROR_CODE_TOO_FAR</code></a> 2，起点/终点 距离所有道路均距离过远（例如在海洋/矿业）<br>
 <a href="../../../../../com/amap/api/services/route/DistanceItem.html#ERROR_CODE_NOT_IN_CHINA"><code>DistanceItem.ERROR_CODE_NOT_IN_CHINA</code></a> 3，起点/终点不在中国境内<br></div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.1.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/DistanceItem.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/route/Cost.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/amap/api/services/route/DistanceResult.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/route/DistanceItem.html" target="_top">框架</a></li>
<li><a href="DistanceItem.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li><a href="#field.summary">字段</a>&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li><a href="#field.detail">字段</a>&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
