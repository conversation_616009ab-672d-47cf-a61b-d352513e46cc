<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>RouteSearchV2.OnRouteSearchListener</title>
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="RouteSearchV2.OnRouteSearchListener";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":6,"i1":6,"i2":6,"i3":6};
var tabs = {65535:["t0","所有方法"],2:["t2","实例方法"],4:["t3","抽象方法"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/RouteSearchV2.OnRouteSearchListener.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/route/RouteSearchV2.OnRoutePlanSearchListener.html" title="com.amap.api.services.route中的接口"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/amap/api/services/route/RouteSearchV2.OnTruckRouteSearchListener.html" title="com.amap.api.services.route中的接口"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/route/RouteSearchV2.OnRouteSearchListener.html" target="_top">框架</a></li>
<li><a href="RouteSearchV2.OnRouteSearchListener.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.amap.api.services.route</div>
<h2 title="接口 RouteSearchV2.OnRouteSearchListener" class="title">接口 RouteSearchV2.OnRouteSearchListener</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>封闭类:</dt>
<dd><a href="../../../../../com/amap/api/services/route/RouteSearchV2.html" title="com.amap.api.services.route中的类">RouteSearchV2</a></dd>
</dl>
<hr>
<br>
<pre>public static interface <span class="typeNameLabel">RouteSearchV2.OnRouteSearchListener</span></pre>
<div class="block">本类为路径搜索结果的异步处理回调接口。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.2.0</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>所有方法</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">实例方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">抽象方法</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.OnRouteSearchListener.html#onBusRouteSearched-com.amap.api.services.route.BusRouteResultV2-int-">onBusRouteSearched</a></span>(<a href="../../../../../com/amap/api/services/route/BusRouteResultV2.html" title="com.amap.api.services.route中的类">BusRouteResultV2</a>&nbsp;busRouteResult,
                  int&nbsp;errorCode)</code>
<div class="block">公交换乘路径规划结果的回调方法。</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.OnRouteSearchListener.html#onDriveRouteSearched-com.amap.api.services.route.DriveRouteResultV2-int-">onDriveRouteSearched</a></span>(<a href="../../../../../com/amap/api/services/route/DriveRouteResultV2.html" title="com.amap.api.services.route中的类">DriveRouteResultV2</a>&nbsp;driveRouteResult,
                    int&nbsp;errorCode)</code>
<div class="block">驾车路径规划结果的回调方法。</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.OnRouteSearchListener.html#onRideRouteSearched-com.amap.api.services.route.RideRouteResultV2-int-">onRideRouteSearched</a></span>(<a href="../../../../../com/amap/api/services/route/RideRouteResultV2.html" title="com.amap.api.services.route中的类">RideRouteResultV2</a>&nbsp;rideRouteResult,
                   int&nbsp;errorCode)</code>
<div class="block">骑行路径规划结果的回调方法。</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.OnRouteSearchListener.html#onWalkRouteSearched-com.amap.api.services.route.WalkRouteResultV2-int-">onWalkRouteSearched</a></span>(<a href="../../../../../com/amap/api/services/route/WalkRouteResultV2.html" title="com.amap.api.services.route中的类">WalkRouteResultV2</a>&nbsp;walkRouteResult,
                   int&nbsp;errorCode)</code>
<div class="block">步行路径规划结果的回调方法。</div>
</td>
</tr>
</table>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="onDriveRouteSearched-com.amap.api.services.route.DriveRouteResultV2-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onDriveRouteSearched</h4>
<pre>void&nbsp;onDriveRouteSearched(<a href="../../../../../com/amap/api/services/route/DriveRouteResultV2.html" title="com.amap.api.services.route中的类">DriveRouteResultV2</a>&nbsp;driveRouteResult,
                          int&nbsp;errorCode)</pre>
<div class="block">驾车路径规划结果的回调方法。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>driveRouteResult</code> - 驾车路径规划的结果集。</dd>
<dd><code>errorCode</code> - 返回结果成功或者失败的响应码。1000为成功(V3.2.1之后搜索成功响应码为1000，之前版本为0)，其他为失败（详细信息参见网站开发指南-错误码对照表）。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.2.0</dd>
</dl>
</li>
</ul>
<a name="onBusRouteSearched-com.amap.api.services.route.BusRouteResultV2-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onBusRouteSearched</h4>
<pre>void&nbsp;onBusRouteSearched(<a href="../../../../../com/amap/api/services/route/BusRouteResultV2.html" title="com.amap.api.services.route中的类">BusRouteResultV2</a>&nbsp;busRouteResult,
                        int&nbsp;errorCode)</pre>
<div class="block">公交换乘路径规划结果的回调方法。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>busRouteResult</code> - 公交路径规划的结果集。</dd>
<dd><code>errorCode</code> - 返回结果成功或者失败的响应码。1000为成功(V3.2.1之后搜索成功响应码为1000，之前版本为0)，其他为失败（详细信息参见网站开发指南-错误码对照表）。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.4.0</dd>
</dl>
</li>
</ul>
<a name="onWalkRouteSearched-com.amap.api.services.route.WalkRouteResultV2-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onWalkRouteSearched</h4>
<pre>void&nbsp;onWalkRouteSearched(<a href="../../../../../com/amap/api/services/route/WalkRouteResultV2.html" title="com.amap.api.services.route中的类">WalkRouteResultV2</a>&nbsp;walkRouteResult,
                         int&nbsp;errorCode)</pre>
<div class="block">步行路径规划结果的回调方法。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>walkRouteResult</code> - 步行路径规划的结果集。</dd>
<dd><code>errorCode</code> - 返回结果成功或者失败的响应码。1000为成功(V3.2.1之后搜索成功响应码为1000，之前版本为0)，其他为失败（详细信息参见网站开发指南-错误码对照表）。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.4.0</dd>
</dl>
</li>
</ul>
<a name="onRideRouteSearched-com.amap.api.services.route.RideRouteResultV2-int-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>onRideRouteSearched</h4>
<pre>void&nbsp;onRideRouteSearched(<a href="../../../../../com/amap/api/services/route/RideRouteResultV2.html" title="com.amap.api.services.route中的类">RideRouteResultV2</a>&nbsp;rideRouteResult,
                         int&nbsp;errorCode)</pre>
<div class="block">骑行路径规划结果的回调方法。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>rideRouteResult</code> - 骑行路径规划的结果集。</dd>
<dd><code>errorCode</code> - 返回结果成功或者失败的响应码。1000为成功，其他为失败（详细信息参见网站开发指南-错误码对照表）。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.4.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/RouteSearchV2.OnRouteSearchListener.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/route/RouteSearchV2.OnRoutePlanSearchListener.html" title="com.amap.api.services.route中的接口"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/amap/api/services/route/RouteSearchV2.OnTruckRouteSearchListener.html" title="com.amap.api.services.route中的接口"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/route/RouteSearchV2.OnRouteSearchListener.html" target="_top">框架</a></li>
<li><a href="RouteSearchV2.OnRouteSearchListener.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
