<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>RouteSearchV2.CustomCostMode</title>
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="RouteSearchV2.CustomCostMode";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10};
var tabs = {65535:["t0","所有方法"],2:["t2","实例方法"],8:["t4","具体方法"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/RouteSearchV2.CustomCostMode.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/route/RouteSearchV2.CurveCost.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/amap/api/services/route/RouteSearchV2.DriveRouteQuery.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/route/RouteSearchV2.CustomCostMode.html" target="_top">框架</a></li>
<li><a href="RouteSearchV2.CustomCostMode.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.amap.api.services.route</div>
<h2 title="类 RouteSearchV2.CustomCostMode" class="title">类 RouteSearchV2.CustomCostMode</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.amap.api.services.route.RouteSearchV2.CustomCostMode</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>封闭类:</dt>
<dd><a href="../../../../../com/amap/api/services/route/RouteSearchV2.html" title="com.amap.api.services.route中的类">RouteSearchV2</a></dd>
</dl>
<hr>
<br>
<pre>public static class <span class="typeNameLabel">RouteSearchV2.CustomCostMode</span>
extends java.lang.Object</pre>
<div class="block">能耗模型

     //* @exclude javadoc中不显示</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.CustomCostMode.html#CustomCostMode--">CustomCostMode</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>所有方法</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">实例方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">具体方法</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.CustomCostMode.html#getAuxCost--">getAuxCost</a></span>()</code>
<div class="block">获取辅助消耗系数，与时间成比例的能量消耗，含车辆启动后的基础能耗、附加设备的能耗（如空调、座椅加热等），随车辆驾驶模式变化，单位wh/s</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/services/route/RouteSearchV2.CurveCost.html" title="com.amap.api.services.route中的类">RouteSearchV2.CurveCost</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.CustomCostMode.html#getCurveCost--">getCurveCost</a></span>()</code>
<div class="block">获取弯道消耗权值</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.CustomCostMode.html#getFerryCost--">getFerryCost</a></span>()</code>
<div class="block">获取轮渡消耗（车辆静置场景下的消耗）默认0</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/services/route/RouteSearchV2.PowerTrainLoss.html" title="com.amap.api.services.route中的类">RouteSearchV2.PowerTrainLoss</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.CustomCostMode.html#getPowerTrainLosses--">getPowerTrainLosses</a></span>()</code>
<div class="block">获取能量消耗与动力总成消耗数组。</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/services/route/RouteSearchV2.SlopeCost.html" title="com.amap.api.services.route中的类">RouteSearchV2.SlopeCost</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.CustomCostMode.html#getSlopeCost--">getSlopeCost</a></span>()</code>
<div class="block">获取坡度消耗权值</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../../com/amap/api/services/route/RouteSearchV2.SpeedCost.html" title="com.amap.api.services.route中的类">RouteSearchV2.SpeedCost</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.CustomCostMode.html#getSpeedCosts--">getSpeedCosts</a></span>()</code>
<div class="block">获取速度相关权值，可以重复传多组，含多组SPEED和VALUE，每组代表一个特定速度下每行驶一公里的能耗值（不含坡度消耗），介于两个速度之间的可用线性插值计算</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/services/route/RouteSearchV2.TransCost.html" title="com.amap.api.services.route中的类">RouteSearchV2.TransCost</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.CustomCostMode.html#getTransCost--">getTransCost</a></span>()</code>
<div class="block">获取转向消耗权值数组</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.CustomCostMode.html#setAuxCost-float-">setAuxCost</a></span>(float&nbsp;auxCost)</code>
<div class="block">设置辅助消耗系数，与时间成比例的能量消耗，含车辆启动后的基础能耗、附加设备的能耗（如空调、座椅加热等），随车辆驾驶模式变化，单位wh/s</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.CustomCostMode.html#setCurveCost-com.amap.api.services.route.RouteSearchV2.CurveCost-">setCurveCost</a></span>(<a href="../../../../../com/amap/api/services/route/RouteSearchV2.CurveCost.html" title="com.amap.api.services.route中的类">RouteSearchV2.CurveCost</a>&nbsp;curveCost)</code>
<div class="block">设置弯道消耗权值</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.CustomCostMode.html#setFerryCost-float-">setFerryCost</a></span>(float&nbsp;ferryCost)</code>
<div class="block">设置轮渡消耗（车辆静置场景下的消耗）默认0</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.CustomCostMode.html#setPowerTrainLosses-com.amap.api.services.route.RouteSearchV2.PowerTrainLoss-">setPowerTrainLosses</a></span>(<a href="../../../../../com/amap/api/services/route/RouteSearchV2.PowerTrainLoss.html" title="com.amap.api.services.route中的类">RouteSearchV2.PowerTrainLoss</a>&nbsp;powerTrainLosses)</code>
<div class="block">设置能量消耗与动力总成消耗数组。</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.CustomCostMode.html#setSlopeCost-com.amap.api.services.route.RouteSearchV2.SlopeCost-">setSlopeCost</a></span>(<a href="../../../../../com/amap/api/services/route/RouteSearchV2.SlopeCost.html" title="com.amap.api.services.route中的类">RouteSearchV2.SlopeCost</a>&nbsp;slopeCost)</code>
<div class="block">设置坡度消耗权值</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.CustomCostMode.html#setSpeedCosts-java.util.List-">setSpeedCosts</a></span>(java.util.List&lt;<a href="../../../../../com/amap/api/services/route/RouteSearchV2.SpeedCost.html" title="com.amap.api.services.route中的类">RouteSearchV2.SpeedCost</a>&gt;&nbsp;speedCosts)</code>
<div class="block">设置速度相关权值，可以重复传多组，含多组SPEED和VALUE，每组代表一个特定速度下每行驶一公里的能耗值（不含坡度消耗），介于两个速度之间的可用线性插值计算</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.CustomCostMode.html#setTransCost-com.amap.api.services.route.RouteSearchV2.TransCost-">setTransCost</a></span>(<a href="../../../../../com/amap/api/services/route/RouteSearchV2.TransCost.html" title="com.amap.api.services.route中的类">RouteSearchV2.TransCost</a>&nbsp;transCost)</code>
<div class="block">设置转向消耗权值数组</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.CustomCostMode.html#toJson--">toJson</a></span>()</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="CustomCostMode--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>CustomCostMode</h4>
<pre>public&nbsp;CustomCostMode()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="getSpeedCosts--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSpeedCosts</h4>
<pre>public&nbsp;java.util.List&lt;<a href="../../../../../com/amap/api/services/route/RouteSearchV2.SpeedCost.html" title="com.amap.api.services.route中的类">RouteSearchV2.SpeedCost</a>&gt;&nbsp;getSpeedCosts()</pre>
<div class="block">获取速度相关权值，可以重复传多组，含多组SPEED和VALUE，每组代表一个特定速度下每行驶一公里的能耗值（不含坡度消耗），介于两个速度之间的可用线性插值计算</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>速度相关权值，可以重复传多组，含多组SPEED和VALUE，每组代表一个特定速度下每行驶一公里的能耗值（不含坡度消耗），介于两个速度之间的可用线性插值计算</dd>
</dl>
</li>
</ul>
<a name="setSpeedCosts-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSpeedCosts</h4>
<pre>public&nbsp;void&nbsp;setSpeedCosts(java.util.List&lt;<a href="../../../../../com/amap/api/services/route/RouteSearchV2.SpeedCost.html" title="com.amap.api.services.route中的类">RouteSearchV2.SpeedCost</a>&gt;&nbsp;speedCosts)</pre>
<div class="block">设置速度相关权值，可以重复传多组，含多组SPEED和VALUE，每组代表一个特定速度下每行驶一公里的能耗值（不含坡度消耗），介于两个速度之间的可用线性插值计算</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>speedCosts</code> - 速度相关权值，可以重复传多组，含多组SPEED和VALUE，每组代表一个特定速度下每行驶一公里的能耗值（不含坡度消耗），介于两个速度之间的可用线性插值计算</dd>
</dl>
</li>
</ul>
<a name="getCurveCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCurveCost</h4>
<pre>public&nbsp;<a href="../../../../../com/amap/api/services/route/RouteSearchV2.CurveCost.html" title="com.amap.api.services.route中的类">RouteSearchV2.CurveCost</a>&nbsp;getCurveCost()</pre>
<div class="block">获取弯道消耗权值</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>弯道消耗权值</dd>
</dl>
</li>
</ul>
<a name="setCurveCost-com.amap.api.services.route.RouteSearchV2.CurveCost-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCurveCost</h4>
<pre>public&nbsp;void&nbsp;setCurveCost(<a href="../../../../../com/amap/api/services/route/RouteSearchV2.CurveCost.html" title="com.amap.api.services.route中的类">RouteSearchV2.CurveCost</a>&nbsp;curveCost)</pre>
<div class="block">设置弯道消耗权值</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>curveCost</code> - 弯道消耗权值</dd>
</dl>
</li>
</ul>
<a name="getSlopeCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSlopeCost</h4>
<pre>public&nbsp;<a href="../../../../../com/amap/api/services/route/RouteSearchV2.SlopeCost.html" title="com.amap.api.services.route中的类">RouteSearchV2.SlopeCost</a>&nbsp;getSlopeCost()</pre>
<div class="block">获取坡度消耗权值</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>坡度消耗权值</dd>
</dl>
</li>
</ul>
<a name="setSlopeCost-com.amap.api.services.route.RouteSearchV2.SlopeCost-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSlopeCost</h4>
<pre>public&nbsp;void&nbsp;setSlopeCost(<a href="../../../../../com/amap/api/services/route/RouteSearchV2.SlopeCost.html" title="com.amap.api.services.route中的类">RouteSearchV2.SlopeCost</a>&nbsp;slopeCost)</pre>
<div class="block">设置坡度消耗权值</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>slopeCost</code> - 坡度消耗权值</dd>
</dl>
</li>
</ul>
<a name="getAuxCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAuxCost</h4>
<pre>public&nbsp;float&nbsp;getAuxCost()</pre>
<div class="block">获取辅助消耗系数，与时间成比例的能量消耗，含车辆启动后的基础能耗、附加设备的能耗（如空调、座椅加热等），随车辆驾驶模式变化，单位wh/s</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>辅助消耗系数，与时间成比例的能量消耗，含车辆启动后的基础能耗、附加设备的能耗（如空调、座椅加热等），随车辆驾驶模式变化，单位wh/s</dd>
</dl>
</li>
</ul>
<a name="setAuxCost-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAuxCost</h4>
<pre>public&nbsp;void&nbsp;setAuxCost(float&nbsp;auxCost)</pre>
<div class="block">设置辅助消耗系数，与时间成比例的能量消耗，含车辆启动后的基础能耗、附加设备的能耗（如空调、座椅加热等），随车辆驾驶模式变化，单位wh/s</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>auxCost</code> - 辅助消耗系数，与时间成比例的能量消耗，含车辆启动后的基础能耗、附加设备的能耗（如空调、座椅加热等），随车辆驾驶模式变化，单位wh/s</dd>
</dl>
</li>
</ul>
<a name="getTransCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTransCost</h4>
<pre>public&nbsp;<a href="../../../../../com/amap/api/services/route/RouteSearchV2.TransCost.html" title="com.amap.api.services.route中的类">RouteSearchV2.TransCost</a>&nbsp;getTransCost()</pre>
<div class="block">获取转向消耗权值数组</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>转向消耗权值数组</dd>
</dl>
</li>
</ul>
<a name="setTransCost-com.amap.api.services.route.RouteSearchV2.TransCost-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTransCost</h4>
<pre>public&nbsp;void&nbsp;setTransCost(<a href="../../../../../com/amap/api/services/route/RouteSearchV2.TransCost.html" title="com.amap.api.services.route中的类">RouteSearchV2.TransCost</a>&nbsp;transCost)</pre>
<div class="block">设置转向消耗权值数组</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>transCost</code> - 转向消耗权值数组</dd>
</dl>
</li>
</ul>
<a name="getFerryCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFerryCost</h4>
<pre>public&nbsp;float&nbsp;getFerryCost()</pre>
<div class="block">获取轮渡消耗（车辆静置场景下的消耗）默认0</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>轮渡消耗（车辆静置场景下的消耗）默认0</dd>
</dl>
</li>
</ul>
<a name="setFerryCost-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFerryCost</h4>
<pre>public&nbsp;void&nbsp;setFerryCost(float&nbsp;ferryCost)</pre>
<div class="block">设置轮渡消耗（车辆静置场景下的消耗）默认0</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>ferryCost</code> - 轮渡消耗（车辆静置场景下的消耗）默认0</dd>
</dl>
</li>
</ul>
<a name="getPowerTrainLosses--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPowerTrainLosses</h4>
<pre>public&nbsp;<a href="../../../../../com/amap/api/services/route/RouteSearchV2.PowerTrainLoss.html" title="com.amap.api.services.route中的类">RouteSearchV2.PowerTrainLoss</a>&nbsp;getPowerTrainLosses()</pre>
<div class="block">获取能量消耗与动力总成消耗数组。主要指在动力传动系统其他额外的损耗，比如电池耗电8kwh，前几项能耗只有7.5kwh，有一部分可能损耗在传动系统或其他地方，可以通过这个参数补充修正值</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>* 能量消耗与动力总成消耗数组。主要指在动力传动系统其他额外的损耗，比如电池耗电8kwh，前几项能耗只有7.5kwh，有一部分可能损耗在传动系统或其他地方，可以通过这个参数补充修正值</dd>
</dl>
</li>
</ul>
<a name="setPowerTrainLosses-com.amap.api.services.route.RouteSearchV2.PowerTrainLoss-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPowerTrainLosses</h4>
<pre>public&nbsp;void&nbsp;setPowerTrainLosses(<a href="../../../../../com/amap/api/services/route/RouteSearchV2.PowerTrainLoss.html" title="com.amap.api.services.route中的类">RouteSearchV2.PowerTrainLoss</a>&nbsp;powerTrainLosses)</pre>
<div class="block">设置能量消耗与动力总成消耗数组。主要指在动力传动系统其他额外的损耗，比如电池耗电8kwh，前几项能耗只有7.5kwh，有一部分可能损耗在传动系统或其他地方，可以通过这个参数补充修正值</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>powerTrainLosses</code> - 能量消耗与动力总成消耗数组。主要指在动力传动系统其他额外的损耗，比如电池耗电8kwh，前几项能耗只有7.5kwh，有一部分可能损耗在传动系统或其他地方，可以通过这个参数补充修正值</dd>
</dl>
</li>
</ul>
<a name="toJson--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>toJson</h4>
<pre>public&nbsp;java.lang.String&nbsp;toJson()</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/RouteSearchV2.CustomCostMode.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/route/RouteSearchV2.CurveCost.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/amap/api/services/route/RouteSearchV2.DriveRouteQuery.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/route/RouteSearchV2.CustomCostMode.html" target="_top">框架</a></li>
<li><a href="RouteSearchV2.CustomCostMode.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
