<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>DistanceSearch.DistanceQuery</title>
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="DistanceSearch.DistanceQuery";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10};
var tabs = {65535:["t0","所有方法"],2:["t2","实例方法"],8:["t4","具体方法"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/DistanceSearch.DistanceQuery.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/route/DistanceSearch.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/amap/api/services/route/DistanceSearch.OnDistanceSearchListener.html" title="com.amap.api.services.route中的接口"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/route/DistanceSearch.DistanceQuery.html" target="_top">框架</a></li>
<li><a href="DistanceSearch.DistanceQuery.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.amap.api.services.route</div>
<h2 title="类 DistanceSearch.DistanceQuery" class="title">类 DistanceSearch.DistanceQuery</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.amap.api.services.route.DistanceSearch.DistanceQuery</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>所有已实现的接口:</dt>
<dd>java.lang.Cloneable</dd>
</dl>
<dl>
<dt>封闭类:</dt>
<dd><a href="../../../../../com/amap/api/services/route/DistanceSearch.html" title="com.amap.api.services.route中的类">DistanceSearch</a></dd>
</dl>
<hr>
<br>
<pre>public static class <span class="typeNameLabel">DistanceSearch.DistanceQuery</span>
extends java.lang.Object
implements java.lang.Cloneable</pre>
<div class="block">距离测量属性设置类，其中包含起终点以及测量类型的设置。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.1.0</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/DistanceSearch.DistanceQuery.html#DistanceQuery--">DistanceQuery</a></span>()</code>
<div class="block">距离测量属性设置构造函数。</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>所有方法</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">实例方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">具体方法</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/DistanceSearch.DistanceQuery.html#addOrigins-com.amap.api.services.core.LatLonPoint...-">addOrigins</a></span>(<a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>...&nbsp;origins)</code>
<div class="block">设置距离测量起点数据集合,建议不超过s100个坐标
 和<a href="../../../../../com/amap/api/services/route/DistanceSearch.DistanceQuery.html#setOrigins-java.util.List-"><code>DistanceSearch.DistanceQuery.setOrigins(List)</code></a> 不同，不需要自行创建集合对象
 如果已经调用了<a href="../../../../../com/amap/api/services/route/DistanceSearch.DistanceQuery.html#setOrigins-java.util.List-"><code>DistanceSearch.DistanceQuery.setOrigins(List)</code></a>，再次调用此方法，之前的内容也会被保留</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/DistanceSearch.DistanceQuery.html#getDestination--">getDestination</a></span>()</code>
<div class="block">获取距离测量终点</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/DistanceSearch.DistanceQuery.html#getExtensions--">getExtensions</a></span>()</code>
<div class="block">扩展字段 base表示只返回基础数据，all表示所有数据 ，默认 base</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/DistanceSearch.DistanceQuery.html#getMode--">getMode</a></span>()</code>
<div class="block">驾车距离测量模式， 默认是4, 参加驾车路径规划策略模式</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/DistanceSearch.DistanceQuery.html#getOrigins--">getOrigins</a></span>()</code>
<div class="block">获取距离测量起点集合</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/DistanceSearch.DistanceQuery.html#getType--">getType</a></span>()</code>
<div class="block">获取距离测量方式</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/DistanceSearch.DistanceQuery.html#setDestination-com.amap.api.services.core.LatLonPoint-">setDestination</a></span>(<a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&nbsp;destination)</code>
<div class="block">设置距离测量终点，和起点不同终点仅支持一个坐标</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/DistanceSearch.DistanceQuery.html#setExtensions-java.lang.String-">setExtensions</a></span>(java.lang.String&nbsp;extensions)</code>
<div class="block">扩展字段 base表示只返回基础数据，all表示所有数据 ，默认 base</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/DistanceSearch.DistanceQuery.html#setMode-int-">setMode</a></span>(int&nbsp;mMode)</code>
<div class="block">驾车距离测量模式， 默认是4， 参加驾车路径规划策略模式</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/DistanceSearch.DistanceQuery.html#setOrigins-java.util.List-">setOrigins</a></span>(java.util.List&lt;<a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&gt;&nbsp;origins)</code>
<div class="block">设置距离测量起点数据集合,建议不超过100个坐标</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/DistanceSearch.DistanceQuery.html#setType-int-">setType</a></span>(int&nbsp;type)</code>
<div class="block">设置距离测量方式</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="DistanceQuery--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>DistanceQuery</h4>
<pre>public&nbsp;DistanceQuery()</pre>
<div class="block">距离测量属性设置构造函数。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.1.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="getType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getType</h4>
<pre>public&nbsp;int&nbsp;getType()</pre>
<div class="block">获取距离测量方式</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>方式包含<a href="../../../../../com/amap/api/services/route/DistanceSearch.html#TYPE_DISTANCE"><code>DistanceSearch.TYPE_DISTANCE</code></a> <a href="../../../../../com/amap/api/services/route/DistanceSearch.html#TYPE_DRIVING_DISTANCE"><code>DistanceSearch.TYPE_DRIVING_DISTANCE</code></a></dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.1.0</dd>
</dl>
</li>
</ul>
<a name="getOrigins--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getOrigins</h4>
<pre>public&nbsp;java.util.List&lt;<a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&gt;&nbsp;getOrigins()</pre>
<div class="block">获取距离测量起点集合</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>起点集合是一个数据，默认数据为空</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.1.0</dd>
</dl>
</li>
</ul>
<a name="getDestination--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDestination</h4>
<pre>public&nbsp;<a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&nbsp;getDestination()</pre>
<div class="block">获取距离测量终点</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>终点坐标，默认为null</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.1.0</dd>
</dl>
</li>
</ul>
<a name="setType-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setType</h4>
<pre>public&nbsp;void&nbsp;setType(int&nbsp;type)</pre>
<div class="block">设置距离测量方式</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>type</code> - 参考 <a href="../../../../../com/amap/api/services/route/DistanceSearch.html#TYPE_DISTANCE"><code>DistanceSearch.TYPE_DISTANCE</code></a> <a href="../../../../../com/amap/api/services/route/DistanceSearch.html#TYPE_DRIVING_DISTANCE"><code>DistanceSearch.TYPE_DRIVING_DISTANCE</code></a></dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.1.0</dd>
</dl>
</li>
</ul>
<a name="setOrigins-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setOrigins</h4>
<pre>public&nbsp;void&nbsp;setOrigins(java.util.List&lt;<a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&gt;&nbsp;origins)</pre>
<div class="block">设置距离测量起点数据集合,建议不超过100个坐标</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>origins</code> - 起点数据集合，优先级高于<a href="../../../../../com/amap/api/services/route/DistanceSearch.DistanceQuery.html#addOrigins-com.amap.api.services.core.LatLonPoint...-"><code>DistanceSearch.DistanceQuery.addOrigins(LatLonPoint...)</code></a>
                调用此接口会覆盖 <a href="../../../../../com/amap/api/services/route/DistanceSearch.DistanceQuery.html#addOrigins-com.amap.api.services.core.LatLonPoint...-"><code>DistanceSearch.DistanceQuery.addOrigins(LatLonPoint...)</code></a>添加的内容</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.1.0</dd>
</dl>
</li>
</ul>
<a name="addOrigins-com.amap.api.services.core.LatLonPoint...-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addOrigins</h4>
<pre>public&nbsp;void&nbsp;addOrigins(<a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>...&nbsp;origins)</pre>
<div class="block">设置距离测量起点数据集合,建议不超过s100个坐标
 和<a href="../../../../../com/amap/api/services/route/DistanceSearch.DistanceQuery.html#setOrigins-java.util.List-"><code>DistanceSearch.DistanceQuery.setOrigins(List)</code></a> 不同，不需要自行创建集合对象
 如果已经调用了<a href="../../../../../com/amap/api/services/route/DistanceSearch.DistanceQuery.html#setOrigins-java.util.List-"><code>DistanceSearch.DistanceQuery.setOrigins(List)</code></a>，再次调用此方法，之前的内容也会被保留</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>origins</code> - 起点数据</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.1.0</dd>
</dl>
</li>
</ul>
<a name="setDestination-com.amap.api.services.core.LatLonPoint-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDestination</h4>
<pre>public&nbsp;void&nbsp;setDestination(<a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&nbsp;destination)</pre>
<div class="block">设置距离测量终点，和起点不同终点仅支持一个坐标</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>destination</code> - 终点坐标</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.1.0</dd>
</dl>
</li>
</ul>
<a name="getExtensions--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getExtensions</h4>
<pre>public&nbsp;java.lang.String&nbsp;getExtensions()</pre>
<div class="block">扩展字段 base表示只返回基础数据，all表示所有数据 ，默认 base</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>7.6.0</dd>
</dl>
</li>
</ul>
<a name="setExtensions-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setExtensions</h4>
<pre>public&nbsp;void&nbsp;setExtensions(java.lang.String&nbsp;extensions)</pre>
<div class="block">扩展字段 base表示只返回基础数据，all表示所有数据 ，默认 base</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>7.6.0</dd>
</dl>
</li>
</ul>
<a name="getMode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMode</h4>
<pre>public&nbsp;int&nbsp;getMode()</pre>
<div class="block">驾车距离测量模式， 默认是4, 参加驾车路径规划策略模式</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>7.7.0</dd>
</dl>
</li>
</ul>
<a name="setMode-int-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setMode</h4>
<pre>public&nbsp;void&nbsp;setMode(int&nbsp;mMode)</pre>
<div class="block">驾车距离测量模式， 默认是4， 参加驾车路径规划策略模式</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>mMode</code> - 驾车模式</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>7.7.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/DistanceSearch.DistanceQuery.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/route/DistanceSearch.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/amap/api/services/route/DistanceSearch.OnDistanceSearchListener.html" title="com.amap.api.services.route中的接口"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/route/DistanceSearch.DistanceQuery.html" target="_top">框架</a></li>
<li><a href="DistanceSearch.DistanceQuery.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
