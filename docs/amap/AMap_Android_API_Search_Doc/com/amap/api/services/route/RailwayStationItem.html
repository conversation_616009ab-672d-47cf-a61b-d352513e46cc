<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>RailwayStationItem</title>
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="RailwayStationItem";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10};
var tabs = {65535:["t0","所有方法"],2:["t2","实例方法"],8:["t4","具体方法"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/RailwayStationItem.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/route/RailwaySpace.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/amap/api/services/route/RidePath.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/route/RailwayStationItem.html" target="_top">框架</a></li>
<li><a href="RailwayStationItem.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.amap.api.services.route</div>
<h2 title="类 RailwayStationItem" class="title">类 RailwayStationItem</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.amap.api.services.route.RailwayStationItem</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">RailwayStationItem</span>
extends java.lang.Object</pre>
<div class="block">定义了火车站点信息</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.4.0</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RailwayStationItem.html#RailwayStationItem--">RailwayStationItem</a></span>()</code>
<div class="block">RailwayStationItem构造方法。</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>所有方法</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">实例方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">具体方法</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RailwayStationItem.html#getAdcode--">getAdcode</a></span>()</code>
<div class="block">返回站点所在城市adcode。</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RailwayStationItem.html#getID--">getID</a></span>()</code>
<div class="block">返回站点ID。</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RailwayStationItem.html#getLocation--">getLocation</a></span>()</code>
<div class="block">返回站点经纬度。</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RailwayStationItem.html#getName--">getName</a></span>()</code>
<div class="block">返回站点名称。</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RailwayStationItem.html#getTime--">getTime</a></span>()</code>
<div class="block">返回进站时间。</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RailwayStationItem.html#getWait--">getWait</a></span>()</code>
<div class="block">返回途经站的站点停靠时间，单位，秒（途径站才有这个值）。</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RailwayStationItem.html#isEnd--">isEnd</a></span>()</code>
<div class="block">返回是否为终点站。</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RailwayStationItem.html#isStart--">isStart</a></span>()</code>
<div class="block">返回是否为始发站。</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="RailwayStationItem--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>RailwayStationItem</h4>
<pre>public&nbsp;RailwayStationItem()</pre>
<div class="block">RailwayStationItem构造方法。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.4.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="getID--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getID</h4>
<pre>public&nbsp;java.lang.String&nbsp;getID()</pre>
<div class="block">返回站点ID。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>站点ID。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.4.0</dd>
</dl>
</li>
</ul>
<a name="getName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getName</h4>
<pre>public&nbsp;java.lang.String&nbsp;getName()</pre>
<div class="block">返回站点名称。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>站点名称。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.4.0</dd>
</dl>
</li>
</ul>
<a name="getLocation--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLocation</h4>
<pre>public&nbsp;<a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&nbsp;getLocation()</pre>
<div class="block">返回站点经纬度。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>站点经纬度。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.4.0</dd>
</dl>
</li>
</ul>
<a name="getAdcode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAdcode</h4>
<pre>public&nbsp;java.lang.String&nbsp;getAdcode()</pre>
<div class="block">返回站点所在城市adcode。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>站点所在城市adcode。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.4.0</dd>
</dl>
</li>
</ul>
<a name="getTime--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTime</h4>
<pre>public&nbsp;java.lang.String&nbsp;getTime()</pre>
<div class="block">返回进站时间。发车站即为，发车时间。下车站即为到站时间。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>火车进站时间。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.4.0</dd>
</dl>
</li>
</ul>
<a name="isStart--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isStart</h4>
<pre>public&nbsp;boolean&nbsp;isStart()</pre>
<div class="block">返回是否为始发站。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>true表示始发站，false表示为非始发站。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.4.0</dd>
</dl>
</li>
</ul>
<a name="isEnd--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isEnd</h4>
<pre>public&nbsp;boolean&nbsp;isEnd()</pre>
<div class="block">返回是否为终点站。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>true表示终点站，false表示非终点站。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.4.0</dd>
</dl>
</li>
</ul>
<a name="getWait--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getWait</h4>
<pre>public&nbsp;float&nbsp;getWait()</pre>
<div class="block">返回途经站的站点停靠时间，单位，秒（途径站才有这个值）。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>途经站的站点停靠时间。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.4.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/RailwayStationItem.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/route/RailwaySpace.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/amap/api/services/route/RidePath.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/route/RailwayStationItem.html" target="_top">框架</a></li>
<li><a href="RailwayStationItem.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
