<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>com.amap.api.services.route 类分层结构</title>
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="com.amap.api.services.route \u7C7B\u5206\u5C42\u7ED3\u6784";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li>类</li>
<li>使用</li>
<li class="navBarCell1Rev">树</li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/road/package-tree.html">上一个</a></li>
<li><a href="../../../../../com/amap/api/services/routepoisearch/package-tree.html">下一个</a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/route/package-tree.html" target="_top">框架</a></li>
<li><a href="package-tree.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 class="title">程序包com.amap.api.services.route的分层结构</h1>
<span class="packageHierarchyLabel">程序包分层结构:</span>
<ul class="horizontal">
<li><a href="../../../../../overview-tree.html">所有程序包</a></li>
</ul>
</div>
<div class="contentContainer">
<h2 title="类分层结构">类分层结构</h2>
<ul>
<li type="circle">java.lang.Object
<ul>
<li type="circle">com.amap.api.services.busline.<a href="../../../../../com/amap/api/services/busline/BusLineItem.html" title="com.amap.api.services.busline中的类"><span class="typeNameLink">BusLineItem</span></a>
<ul>
<li type="circle">com.amap.api.services.route.<a href="../../../../../com/amap/api/services/route/RouteBusLineItem.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">RouteBusLineItem</span></a></li>
</ul>
</li>
<li type="circle">com.amap.api.services.route.<a href="../../../../../com/amap/api/services/route/BusStep.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">BusStep</span></a></li>
<li type="circle">com.amap.api.services.route.<a href="../../../../../com/amap/api/services/route/BusStepV2.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">BusStepV2</span></a></li>
<li type="circle">com.amap.api.services.route.<a href="../../../../../com/amap/api/services/route/Cost.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">Cost</span></a></li>
<li type="circle">com.amap.api.services.route.<a href="../../../../../com/amap/api/services/route/DistanceItem.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">DistanceItem</span></a></li>
<li type="circle">com.amap.api.services.route.<a href="../../../../../com/amap/api/services/route/DistanceResult.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">DistanceResult</span></a></li>
<li type="circle">com.amap.api.services.route.<a href="../../../../../com/amap/api/services/route/DistanceSearch.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">DistanceSearch</span></a></li>
<li type="circle">com.amap.api.services.route.<a href="../../../../../com/amap/api/services/route/DistanceSearch.DistanceQuery.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">DistanceSearch.DistanceQuery</span></a> (implements java.lang.Cloneable)</li>
<li type="circle">com.amap.api.services.route.<a href="../../../../../com/amap/api/services/route/District.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">District</span></a></li>
<li type="circle">com.amap.api.services.route.<a href="../../../../../com/amap/api/services/route/Doorway.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">Doorway</span></a></li>
<li type="circle">com.amap.api.services.route.<a href="../../../../../com/amap/api/services/route/DrivePlanPath.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">DrivePlanPath</span></a></li>
<li type="circle">com.amap.api.services.route.<a href="../../../../../com/amap/api/services/route/DrivePlanStep.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">DrivePlanStep</span></a></li>
<li type="circle">com.amap.api.services.route.<a href="../../../../../com/amap/api/services/route/DriveStep.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">DriveStep</span></a></li>
<li type="circle">com.amap.api.services.route.<a href="../../../../../com/amap/api/services/route/DriveStepV2.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">DriveStepV2</span></a></li>
<li type="circle">com.amap.api.services.route.<a href="../../../../../com/amap/api/services/route/Navi.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">Navi</span></a>
<ul>
<li type="circle">com.amap.api.services.route.<a href="../../../../../com/amap/api/services/route/NaviWalkType.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">NaviWalkType</span></a></li>
</ul>
</li>
<li type="circle">com.amap.api.services.route.<a href="../../../../../com/amap/api/services/route/Path.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">Path</span></a>
<ul>
<li type="circle">com.amap.api.services.route.<a href="../../../../../com/amap/api/services/route/BusPath.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">BusPath</span></a></li>
<li type="circle">com.amap.api.services.route.<a href="../../../../../com/amap/api/services/route/BusPathV2.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">BusPathV2</span></a></li>
<li type="circle">com.amap.api.services.route.<a href="../../../../../com/amap/api/services/route/DrivePath.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">DrivePath</span></a></li>
<li type="circle">com.amap.api.services.route.<a href="../../../../../com/amap/api/services/route/DrivePathV2.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">DrivePathV2</span></a></li>
<li type="circle">com.amap.api.services.route.<a href="../../../../../com/amap/api/services/route/RidePath.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">RidePath</span></a></li>
<li type="circle">com.amap.api.services.route.<a href="../../../../../com/amap/api/services/route/WalkPath.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">WalkPath</span></a>
<ul>
<li type="circle">com.amap.api.services.route.<a href="../../../../../com/amap/api/services/route/RouteBusWalkItem.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">RouteBusWalkItem</span></a></li>
</ul>
</li>
</ul>
</li>
<li type="circle">com.amap.api.services.route.<a href="../../../../../com/amap/api/services/route/Railway.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">Railway</span></a>
<ul>
<li type="circle">com.amap.api.services.route.<a href="../../../../../com/amap/api/services/route/RouteRailwayItem.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">RouteRailwayItem</span></a></li>
</ul>
</li>
<li type="circle">com.amap.api.services.route.<a href="../../../../../com/amap/api/services/route/RailwaySpace.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">RailwaySpace</span></a></li>
<li type="circle">com.amap.api.services.route.<a href="../../../../../com/amap/api/services/route/RailwayStationItem.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">RailwayStationItem</span></a></li>
<li type="circle">com.amap.api.services.route.<a href="../../../../../com/amap/api/services/route/RideStep.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">RideStep</span></a></li>
<li type="circle">com.amap.api.services.route.<a href="../../../../../com/amap/api/services/route/RoutePlanResult.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">RoutePlanResult</span></a>
<ul>
<li type="circle">com.amap.api.services.route.<a href="../../../../../com/amap/api/services/route/DriveRoutePlanResult.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">DriveRoutePlanResult</span></a></li>
</ul>
</li>
<li type="circle">com.amap.api.services.route.<a href="../../../../../com/amap/api/services/route/RouteResult.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">RouteResult</span></a>
<ul>
<li type="circle">com.amap.api.services.route.<a href="../../../../../com/amap/api/services/route/BusRouteResult.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">BusRouteResult</span></a></li>
<li type="circle">com.amap.api.services.route.<a href="../../../../../com/amap/api/services/route/BusRouteResultV2.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">BusRouteResultV2</span></a></li>
<li type="circle">com.amap.api.services.route.<a href="../../../../../com/amap/api/services/route/DriveRouteResult.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">DriveRouteResult</span></a></li>
<li type="circle">com.amap.api.services.route.<a href="../../../../../com/amap/api/services/route/DriveRouteResultV2.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">DriveRouteResultV2</span></a></li>
<li type="circle">com.amap.api.services.route.<a href="../../../../../com/amap/api/services/route/RideRouteResult.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">RideRouteResult</span></a></li>
<li type="circle">com.amap.api.services.route.<a href="../../../../../com/amap/api/services/route/RideRouteResultV2.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">RideRouteResultV2</span></a></li>
<li type="circle">com.amap.api.services.route.<a href="../../../../../com/amap/api/services/route/WalkRouteResult.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">WalkRouteResult</span></a></li>
<li type="circle">com.amap.api.services.route.<a href="../../../../../com/amap/api/services/route/WalkRouteResultV2.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">WalkRouteResultV2</span></a></li>
</ul>
</li>
<li type="circle">com.amap.api.services.route.<a href="../../../../../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">RouteSearch</span></a></li>
<li type="circle">com.amap.api.services.route.<a href="../../../../../com/amap/api/services/route/RouteSearch.BusRouteQuery.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">RouteSearch.BusRouteQuery</span></a> (implements java.lang.Cloneable)</li>
<li type="circle">com.amap.api.services.route.<a href="../../../../../com/amap/api/services/route/RouteSearch.DrivePlanQuery.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">RouteSearch.DrivePlanQuery</span></a> (implements java.lang.Cloneable)</li>
<li type="circle">com.amap.api.services.route.<a href="../../../../../com/amap/api/services/route/RouteSearch.DriveRouteQuery.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">RouteSearch.DriveRouteQuery</span></a> (implements java.lang.Cloneable)</li>
<li type="circle">com.amap.api.services.route.<a href="../../../../../com/amap/api/services/route/RouteSearch.FromAndTo.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">RouteSearch.FromAndTo</span></a> (implements java.lang.Cloneable)</li>
<li type="circle">com.amap.api.services.route.<a href="../../../../../com/amap/api/services/route/RouteSearch.RideRouteQuery.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">RouteSearch.RideRouteQuery</span></a> (implements java.lang.Cloneable)</li>
<li type="circle">com.amap.api.services.route.<a href="../../../../../com/amap/api/services/route/RouteSearch.TruckRouteQuery.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">RouteSearch.TruckRouteQuery</span></a> (implements java.lang.Cloneable)</li>
<li type="circle">com.amap.api.services.route.<a href="../../../../../com/amap/api/services/route/RouteSearch.WalkRouteQuery.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">RouteSearch.WalkRouteQuery</span></a> (implements java.lang.Cloneable)</li>
<li type="circle">com.amap.api.services.route.<a href="../../../../../com/amap/api/services/route/RouteSearchV2.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">RouteSearchV2</span></a></li>
<li type="circle">com.amap.api.services.route.<a href="../../../../../com/amap/api/services/route/RouteSearchV2.AlternativeRoute.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">RouteSearchV2.AlternativeRoute</span></a></li>
<li type="circle">com.amap.api.services.route.<a href="../../../../../com/amap/api/services/route/RouteSearchV2.BusMode.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">RouteSearchV2.BusMode</span></a></li>
<li type="circle">com.amap.api.services.route.<a href="../../../../../com/amap/api/services/route/RouteSearchV2.BusRouteQuery.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">RouteSearchV2.BusRouteQuery</span></a> (implements java.lang.Cloneable)</li>
<li type="circle">com.amap.api.services.route.<a href="../../../../../com/amap/api/services/route/RouteSearchV2.DriveRouteQuery.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">RouteSearchV2.DriveRouteQuery</span></a> (implements java.lang.Cloneable)</li>
<li type="circle">com.amap.api.services.route.<a href="../../../../../com/amap/api/services/route/RouteSearchV2.FromAndTo.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">RouteSearchV2.FromAndTo</span></a> (implements java.lang.Cloneable)</li>
<li type="circle">com.amap.api.services.route.<a href="../../../../../com/amap/api/services/route/RouteSearchV2.RideRouteQuery.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">RouteSearchV2.RideRouteQuery</span></a> (implements java.lang.Cloneable)</li>
<li type="circle">com.amap.api.services.route.<a href="../../../../../com/amap/api/services/route/RouteSearchV2.ShowFields.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">RouteSearchV2.ShowFields</span></a></li>
<li type="circle">com.amap.api.services.route.<a href="../../../../../com/amap/api/services/route/RouteSearchV2.WalkRouteQuery.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">RouteSearchV2.WalkRouteQuery</span></a> (implements java.lang.Cloneable)</li>
<li type="circle">com.amap.api.services.route.<a href="../../../../../com/amap/api/services/route/SearchCity.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">SearchCity</span></a>
<ul>
<li type="circle">com.amap.api.services.route.<a href="../../../../../com/amap/api/services/route/RouteSearchCity.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">RouteSearchCity</span></a></li>
</ul>
</li>
<li type="circle">com.amap.api.services.route.<a href="../../../../../com/amap/api/services/route/TaxiItem.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">TaxiItem</span></a></li>
<li type="circle">com.amap.api.services.route.<a href="../../../../../com/amap/api/services/route/TaxiItemV2.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">TaxiItemV2</span></a></li>
<li type="circle">com.amap.api.services.route.<a href="../../../../../com/amap/api/services/route/TimeInfo.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">TimeInfo</span></a></li>
<li type="circle">com.amap.api.services.route.<a href="../../../../../com/amap/api/services/route/TimeInfosElement.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">TimeInfosElement</span></a></li>
<li type="circle">com.amap.api.services.route.<a href="../../../../../com/amap/api/services/route/TMC.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">TMC</span></a></li>
<li type="circle">com.amap.api.services.route.<a href="../../../../../com/amap/api/services/route/TruckPath.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">TruckPath</span></a></li>
<li type="circle">com.amap.api.services.route.<a href="../../../../../com/amap/api/services/route/TruckRouteRestult.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">TruckRouteRestult</span></a></li>
<li type="circle">com.amap.api.services.route.<a href="../../../../../com/amap/api/services/route/TruckStep.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">TruckStep</span></a></li>
<li type="circle">com.amap.api.services.route.<a href="../../../../../com/amap/api/services/route/WalkStep.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">WalkStep</span></a></li>
</ul>
</li>
</ul>
<h2 title="接口分层结构">接口分层结构</h2>
<ul>
<li type="circle">com.amap.api.services.route.<a href="../../../../../com/amap/api/services/route/DistanceSearch.OnDistanceSearchListener.html" title="com.amap.api.services.route中的接口"><span class="typeNameLink">DistanceSearch.OnDistanceSearchListener</span></a></li>
<li type="circle">com.amap.api.services.route.<a href="../../../../../com/amap/api/services/route/RouteSearch.OnRoutePlanSearchListener.html" title="com.amap.api.services.route中的接口"><span class="typeNameLink">RouteSearch.OnRoutePlanSearchListener</span></a></li>
<li type="circle">com.amap.api.services.route.<a href="../../../../../com/amap/api/services/route/RouteSearch.OnRouteSearchListener.html" title="com.amap.api.services.route中的接口"><span class="typeNameLink">RouteSearch.OnRouteSearchListener</span></a></li>
<li type="circle">com.amap.api.services.route.<a href="../../../../../com/amap/api/services/route/RouteSearch.OnTruckRouteSearchListener.html" title="com.amap.api.services.route中的接口"><span class="typeNameLink">RouteSearch.OnTruckRouteSearchListener</span></a></li>
<li type="circle">com.amap.api.services.route.<a href="../../../../../com/amap/api/services/route/RouteSearchV2.OnRoutePlanSearchListener.html" title="com.amap.api.services.route中的接口"><span class="typeNameLink">RouteSearchV2.OnRoutePlanSearchListener</span></a></li>
<li type="circle">com.amap.api.services.route.<a href="../../../../../com/amap/api/services/route/RouteSearchV2.OnRouteSearchListener.html" title="com.amap.api.services.route中的接口"><span class="typeNameLink">RouteSearchV2.OnRouteSearchListener</span></a></li>
<li type="circle">com.amap.api.services.route.<a href="../../../../../com/amap/api/services/route/RouteSearchV2.OnTruckRouteSearchListener.html" title="com.amap.api.services.route中的接口"><span class="typeNameLink">RouteSearchV2.OnTruckRouteSearchListener</span></a></li>
</ul>
<h2 title="枚举分层结构">枚举分层结构</h2>
<ul>
<li type="circle">java.lang.Object
<ul>
<li type="circle">java.lang.Enum&lt;E&gt; (implements java.lang.Comparable&lt;T&gt;, java.io.Serializable)
<ul>
<li type="circle">com.amap.api.services.route.<a href="../../../../../com/amap/api/services/route/RouteSearchV2.DrivingStrategy.html" title="com.amap.api.services.route中的枚举"><span class="typeNameLink">RouteSearchV2.DrivingStrategy</span></a></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li>类</li>
<li>使用</li>
<li class="navBarCell1Rev">树</li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/road/package-tree.html">上一个</a></li>
<li><a href="../../../../../com/amap/api/services/routepoisearch/package-tree.html">下一个</a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/route/package-tree.html" target="_top">框架</a></li>
<li><a href="package-tree.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
