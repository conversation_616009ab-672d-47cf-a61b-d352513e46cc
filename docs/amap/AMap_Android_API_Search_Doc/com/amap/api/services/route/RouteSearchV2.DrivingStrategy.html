<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>RouteSearchV2.DrivingStrategy</title>
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="RouteSearchV2.DrivingStrategy";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":10,"i2":9,"i3":9};
var tabs = {65535:["t0","所有方法"],1:["t1","静态方法"],2:["t2","实例方法"],8:["t4","具体方法"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/RouteSearchV2.DrivingStrategy.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/route/RouteSearchV2.DriveRouteQuery.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/amap/api/services/route/RouteSearchV2.FromAndTo.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/route/RouteSearchV2.DrivingStrategy.html" target="_top">框架</a></li>
<li><a href="RouteSearchV2.DrivingStrategy.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li><a href="#enum.constant.summary">枚举常量</a>&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li><a href="#enum.constant.detail">枚举常量</a>&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.amap.api.services.route</div>
<h2 title="枚举 RouteSearchV2.DrivingStrategy" class="title">枚举 RouteSearchV2.DrivingStrategy</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>java.lang.Enum&lt;<a href="../../../../../com/amap/api/services/route/RouteSearchV2.DrivingStrategy.html" title="com.amap.api.services.route中的枚举">RouteSearchV2.DrivingStrategy</a>&gt;</li>
<li>
<ul class="inheritance">
<li>com.amap.api.services.route.RouteSearchV2.DrivingStrategy</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>所有已实现的接口:</dt>
<dd>java.io.Serializable, java.lang.Comparable&lt;<a href="../../../../../com/amap/api/services/route/RouteSearchV2.DrivingStrategy.html" title="com.amap.api.services.route中的枚举">RouteSearchV2.DrivingStrategy</a>&gt;</dd>
</dl>
<dl>
<dt>封闭类:</dt>
<dd><a href="../../../../../com/amap/api/services/route/RouteSearchV2.html" title="com.amap.api.services.route中的类">RouteSearchV2</a></dd>
</dl>
<hr>
<br>
<pre>public static enum <span class="typeNameLabel">RouteSearchV2.DrivingStrategy</span>
extends java.lang.Enum&lt;<a href="../../../../../com/amap/api/services/route/RouteSearchV2.DrivingStrategy.html" title="com.amap.api.services.route中的枚举">RouteSearchV2.DrivingStrategy</a>&gt;</pre>
<div class="block">驾车策略</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== ENUM CONSTANT SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="enum.constant.summary">
<!--   -->
</a>
<h3>枚举常量概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="枚举常量概要表, 列表枚举常量和解释">
<caption><span>枚举常量</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">枚举常量和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.DrivingStrategy.html#AVOID_CONGESTION">AVOID_CONGESTION</a></span></code>
<div class="block">躲避拥堵</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.DrivingStrategy.html#AVOID_CONGESTION_AVOID_HIGHWAY">AVOID_CONGESTION_AVOID_HIGHWAY</a></span></code>
<div class="block">躲避拥堵＋不走高速</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.DrivingStrategy.html#AVOID_CONGESTION_HIGHWAY_PRIORITY">AVOID_CONGESTION_HIGHWAY_PRIORITY</a></span></code>
<div class="block">躲避拥堵＋高速优先</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.DrivingStrategy.html#AVOID_CONGESTION_LESS_CHARGE">AVOID_CONGESTION_LESS_CHARGE</a></span></code>
<div class="block">躲避拥堵＋少收费</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.DrivingStrategy.html#AVOID_CONGESTION_LESS_CHARGE_AVOID_HIGHWAY">AVOID_CONGESTION_LESS_CHARGE_AVOID_HIGHWAY</a></span></code>
<div class="block">躲避拥堵＋少收费＋不走高速</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.DrivingStrategy.html#AVOID_CONGESTION_ROAD_PRIORITY">AVOID_CONGESTION_ROAD_PRIORITY</a></span></code>
<div class="block">躲避拥堵＋大路优先</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.DrivingStrategy.html#AVOID_CONGESTION_SPEED_PRIORITY">AVOID_CONGESTION_SPEED_PRIORITY</a></span></code>
<div class="block">躲避拥堵＋速度最快</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.DrivingStrategy.html#AVOID_HIGHWAY">AVOID_HIGHWAY</a></span></code>
<div class="block">不走高速</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.DrivingStrategy.html#DEFAULT">DEFAULT</a></span></code>
<div class="block">默认，高德推荐，同高德地图APP默认</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.DrivingStrategy.html#HIGHWAY_PRIORITY">HIGHWAY_PRIORITY</a></span></code>
<div class="block">高速优先</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.DrivingStrategy.html#LESS_CHARGE">LESS_CHARGE</a></span></code>
<div class="block">少收费</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.DrivingStrategy.html#LESS_CHARGE_AVOID_HIGHWAY">LESS_CHARGE_AVOID_HIGHWAY</a></span></code>
<div class="block">少收费＋不走高速</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.DrivingStrategy.html#ROAD_PRIORITY">ROAD_PRIORITY</a></span></code>
<div class="block">大路优先</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.DrivingStrategy.html#SPEED_PRIORITY">SPEED_PRIORITY</a></span></code>
<div class="block">速度最快</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>所有方法</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">静态方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">实例方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">具体方法</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static <a href="../../../../../com/amap/api/services/route/RouteSearchV2.DrivingStrategy.html" title="com.amap.api.services.route中的枚举">RouteSearchV2.DrivingStrategy</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.DrivingStrategy.html#fromValue-int-">fromValue</a></span>(int&nbsp;value)</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.DrivingStrategy.html#getValue--">getValue</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static <a href="../../../../../com/amap/api/services/route/RouteSearchV2.DrivingStrategy.html" title="com.amap.api.services.route中的枚举">RouteSearchV2.DrivingStrategy</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.DrivingStrategy.html#valueOf-java.lang.String-">valueOf</a></span>(java.lang.String&nbsp;name)</code>
<div class="block">返回带有指定名称的该类型的枚举常量。</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>static <a href="../../../../../com/amap/api/services/route/RouteSearchV2.DrivingStrategy.html" title="com.amap.api.services.route中的枚举">RouteSearchV2.DrivingStrategy</a>[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.DrivingStrategy.html#values--">values</a></span>()</code>
<div class="block">按照声明该枚举类型的常量的顺序, 返回
包含这些常量的数组。</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Enum">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Enum</h3>
<code>clone, compareTo, equals, finalize, getDeclaringClass, hashCode, name, ordinal, toString, valueOf</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>getClass, notify, notifyAll, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ ENUM CONSTANT DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="enum.constant.detail">
<!--   -->
</a>
<h3>枚举常量详细资料</h3>
<a name="DEFAULT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DEFAULT</h4>
<pre>public static final&nbsp;<a href="../../../../../com/amap/api/services/route/RouteSearchV2.DrivingStrategy.html" title="com.amap.api.services.route中的枚举">RouteSearchV2.DrivingStrategy</a> DEFAULT</pre>
<div class="block">默认，高德推荐，同高德地图APP默认</div>
</li>
</ul>
<a name="AVOID_CONGESTION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>AVOID_CONGESTION</h4>
<pre>public static final&nbsp;<a href="../../../../../com/amap/api/services/route/RouteSearchV2.DrivingStrategy.html" title="com.amap.api.services.route中的枚举">RouteSearchV2.DrivingStrategy</a> AVOID_CONGESTION</pre>
<div class="block">躲避拥堵</div>
</li>
</ul>
<a name="HIGHWAY_PRIORITY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HIGHWAY_PRIORITY</h4>
<pre>public static final&nbsp;<a href="../../../../../com/amap/api/services/route/RouteSearchV2.DrivingStrategy.html" title="com.amap.api.services.route中的枚举">RouteSearchV2.DrivingStrategy</a> HIGHWAY_PRIORITY</pre>
<div class="block">高速优先</div>
</li>
</ul>
<a name="AVOID_HIGHWAY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>AVOID_HIGHWAY</h4>
<pre>public static final&nbsp;<a href="../../../../../com/amap/api/services/route/RouteSearchV2.DrivingStrategy.html" title="com.amap.api.services.route中的枚举">RouteSearchV2.DrivingStrategy</a> AVOID_HIGHWAY</pre>
<div class="block">不走高速</div>
</li>
</ul>
<a name="LESS_CHARGE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LESS_CHARGE</h4>
<pre>public static final&nbsp;<a href="../../../../../com/amap/api/services/route/RouteSearchV2.DrivingStrategy.html" title="com.amap.api.services.route中的枚举">RouteSearchV2.DrivingStrategy</a> LESS_CHARGE</pre>
<div class="block">少收费</div>
</li>
</ul>
<a name="ROAD_PRIORITY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ROAD_PRIORITY</h4>
<pre>public static final&nbsp;<a href="../../../../../com/amap/api/services/route/RouteSearchV2.DrivingStrategy.html" title="com.amap.api.services.route中的枚举">RouteSearchV2.DrivingStrategy</a> ROAD_PRIORITY</pre>
<div class="block">大路优先</div>
</li>
</ul>
<a name="SPEED_PRIORITY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SPEED_PRIORITY</h4>
<pre>public static final&nbsp;<a href="../../../../../com/amap/api/services/route/RouteSearchV2.DrivingStrategy.html" title="com.amap.api.services.route中的枚举">RouteSearchV2.DrivingStrategy</a> SPEED_PRIORITY</pre>
<div class="block">速度最快</div>
</li>
</ul>
<a name="AVOID_CONGESTION_HIGHWAY_PRIORITY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>AVOID_CONGESTION_HIGHWAY_PRIORITY</h4>
<pre>public static final&nbsp;<a href="../../../../../com/amap/api/services/route/RouteSearchV2.DrivingStrategy.html" title="com.amap.api.services.route中的枚举">RouteSearchV2.DrivingStrategy</a> AVOID_CONGESTION_HIGHWAY_PRIORITY</pre>
<div class="block">躲避拥堵＋高速优先</div>
</li>
</ul>
<a name="AVOID_CONGESTION_AVOID_HIGHWAY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>AVOID_CONGESTION_AVOID_HIGHWAY</h4>
<pre>public static final&nbsp;<a href="../../../../../com/amap/api/services/route/RouteSearchV2.DrivingStrategy.html" title="com.amap.api.services.route中的枚举">RouteSearchV2.DrivingStrategy</a> AVOID_CONGESTION_AVOID_HIGHWAY</pre>
<div class="block">躲避拥堵＋不走高速</div>
</li>
</ul>
<a name="AVOID_CONGESTION_LESS_CHARGE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>AVOID_CONGESTION_LESS_CHARGE</h4>
<pre>public static final&nbsp;<a href="../../../../../com/amap/api/services/route/RouteSearchV2.DrivingStrategy.html" title="com.amap.api.services.route中的枚举">RouteSearchV2.DrivingStrategy</a> AVOID_CONGESTION_LESS_CHARGE</pre>
<div class="block">躲避拥堵＋少收费</div>
</li>
</ul>
<a name="LESS_CHARGE_AVOID_HIGHWAY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LESS_CHARGE_AVOID_HIGHWAY</h4>
<pre>public static final&nbsp;<a href="../../../../../com/amap/api/services/route/RouteSearchV2.DrivingStrategy.html" title="com.amap.api.services.route中的枚举">RouteSearchV2.DrivingStrategy</a> LESS_CHARGE_AVOID_HIGHWAY</pre>
<div class="block">少收费＋不走高速</div>
</li>
</ul>
<a name="AVOID_CONGESTION_LESS_CHARGE_AVOID_HIGHWAY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>AVOID_CONGESTION_LESS_CHARGE_AVOID_HIGHWAY</h4>
<pre>public static final&nbsp;<a href="../../../../../com/amap/api/services/route/RouteSearchV2.DrivingStrategy.html" title="com.amap.api.services.route中的枚举">RouteSearchV2.DrivingStrategy</a> AVOID_CONGESTION_LESS_CHARGE_AVOID_HIGHWAY</pre>
<div class="block">躲避拥堵＋少收费＋不走高速</div>
</li>
</ul>
<a name="AVOID_CONGESTION_ROAD_PRIORITY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>AVOID_CONGESTION_ROAD_PRIORITY</h4>
<pre>public static final&nbsp;<a href="../../../../../com/amap/api/services/route/RouteSearchV2.DrivingStrategy.html" title="com.amap.api.services.route中的枚举">RouteSearchV2.DrivingStrategy</a> AVOID_CONGESTION_ROAD_PRIORITY</pre>
<div class="block">躲避拥堵＋大路优先</div>
</li>
</ul>
<a name="AVOID_CONGESTION_SPEED_PRIORITY">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>AVOID_CONGESTION_SPEED_PRIORITY</h4>
<pre>public static final&nbsp;<a href="../../../../../com/amap/api/services/route/RouteSearchV2.DrivingStrategy.html" title="com.amap.api.services.route中的枚举">RouteSearchV2.DrivingStrategy</a> AVOID_CONGESTION_SPEED_PRIORITY</pre>
<div class="block">躲避拥堵＋速度最快</div>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="values--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>values</h4>
<pre>public static&nbsp;<a href="../../../../../com/amap/api/services/route/RouteSearchV2.DrivingStrategy.html" title="com.amap.api.services.route中的枚举">RouteSearchV2.DrivingStrategy</a>[]&nbsp;values()</pre>
<div class="block">按照声明该枚举类型的常量的顺序, 返回
包含这些常量的数组。该方法可用于迭代
常量, 如下所示:
<pre>
for (RouteSearchV2.DrivingStrategy c : RouteSearchV2.DrivingStrategy.values())
&nbsp;   System.out.println(c);
</pre></div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>按照声明该枚举类型的常量的顺序返回的包含这些常量的数组</dd>
</dl>
</li>
</ul>
<a name="valueOf-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>valueOf</h4>
<pre>public static&nbsp;<a href="../../../../../com/amap/api/services/route/RouteSearchV2.DrivingStrategy.html" title="com.amap.api.services.route中的枚举">RouteSearchV2.DrivingStrategy</a>&nbsp;valueOf(java.lang.String&nbsp;name)</pre>
<div class="block">返回带有指定名称的该类型的枚举常量。
字符串必须与用于声明该类型的枚举常量的
标识符<i>完全</i>匹配。(不允许有多余
的空格字符。)</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>name</code> - 要返回的枚举常量的名称。</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>返回带有指定名称的枚举常量</dd>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code>java.lang.IllegalArgumentException</code> - 如果该枚举类型没有带有指定名称的常量</dd>
<dd><code>java.lang.NullPointerException</code> - 如果参数为空值</dd>
</dl>
</li>
</ul>
<a name="getValue--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getValue</h4>
<pre>public&nbsp;int&nbsp;getValue()</pre>
</li>
</ul>
<a name="fromValue-int-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>fromValue</h4>
<pre>public static&nbsp;<a href="../../../../../com/amap/api/services/route/RouteSearchV2.DrivingStrategy.html" title="com.amap.api.services.route中的枚举">RouteSearchV2.DrivingStrategy</a>&nbsp;fromValue(int&nbsp;value)</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/RouteSearchV2.DrivingStrategy.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/route/RouteSearchV2.DriveRouteQuery.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/amap/api/services/route/RouteSearchV2.FromAndTo.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/route/RouteSearchV2.DrivingStrategy.html" target="_top">框架</a></li>
<li><a href="RouteSearchV2.DrivingStrategy.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li><a href="#enum.constant.summary">枚举常量</a>&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li><a href="#enum.constant.detail">枚举常量</a>&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
