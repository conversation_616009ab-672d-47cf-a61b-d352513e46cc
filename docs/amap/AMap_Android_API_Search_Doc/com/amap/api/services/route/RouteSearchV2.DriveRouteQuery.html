<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>RouteSearchV2.DriveRouteQuery</title>
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="RouteSearchV2.DriveRouteQuery";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10};
var tabs = {65535:["t0","所有方法"],2:["t2","实例方法"],8:["t4","具体方法"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/RouteSearchV2.DriveRouteQuery.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/route/RouteSearchV2.BusRouteQuery.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/amap/api/services/route/RouteSearchV2.DrivingStrategy.html" title="com.amap.api.services.route中的枚举"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/route/RouteSearchV2.DriveRouteQuery.html" target="_top">框架</a></li>
<li><a href="RouteSearchV2.DriveRouteQuery.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.amap.api.services.route</div>
<h2 title="类 RouteSearchV2.DriveRouteQuery" class="title">类 RouteSearchV2.DriveRouteQuery</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.amap.api.services.route.RouteSearchV2.DriveRouteQuery</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>所有已实现的接口:</dt>
<dd>java.lang.Cloneable</dd>
</dl>
<dl>
<dt>封闭类:</dt>
<dd><a href="../../../../../com/amap/api/services/route/RouteSearchV2.html" title="com.amap.api.services.route中的类">RouteSearchV2</a></dd>
</dl>
<hr>
<br>
<pre>public static class <span class="typeNameLabel">RouteSearchV2.DriveRouteQuery</span>
extends java.lang.Object
implements java.lang.Cloneable</pre>
<div class="block">此类定义了驾车路径查询规划。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.2.0</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.DriveRouteQuery.html#DriveRouteQuery--">DriveRouteQuery</a></span>()</code>
<div class="block">DriveRouteQuery构造函数</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.DriveRouteQuery.html#DriveRouteQuery-Parcel-">DriveRouteQuery</a></span>(Parcel&nbsp;source)</code>
<div class="block">序列化实现</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.DriveRouteQuery.html#DriveRouteQuery-com.amap.api.services.route.RouteSearchV2.FromAndTo-com.amap.api.services.route.RouteSearchV2.DrivingStrategy-java.util.List-java.util.List-java.lang.String-">DriveRouteQuery</a></span>(<a href="../../../../../com/amap/api/services/route/RouteSearchV2.FromAndTo.html" title="com.amap.api.services.route中的类">RouteSearchV2.FromAndTo</a>&nbsp;fromAndTo,
               <a href="../../../../../com/amap/api/services/route/RouteSearchV2.DrivingStrategy.html" title="com.amap.api.services.route中的枚举">RouteSearchV2.DrivingStrategy</a>&nbsp;mode,
               java.util.List&lt;<a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&gt;&nbsp;passedByPoints,
               java.util.List&lt;java.util.List&lt;<a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&gt;&gt;&nbsp;avoidpolygons,
               java.lang.String&nbsp;avoidRoad)</code>
<div class="block">DriveRouteQuery构造函数。</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>所有方法</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">实例方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">具体方法</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.DriveRouteQuery.html#equals-java.lang.Object-">equals</a></span>(java.lang.Object&nbsp;obj)</code>
<div class="block">比较两个查询条件是否相同。</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>java.util.List&lt;java.util.List&lt;<a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&gt;&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.DriveRouteQuery.html#getAvoidpolygons--">getAvoidpolygons</a></span>()</code>
<div class="block">返回设定查询的避让区域。</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.DriveRouteQuery.html#getAvoidpolygonsStr--">getAvoidpolygonsStr</a></span>()</code>
<div class="block">将避让区域转换为字符串输出。</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.DriveRouteQuery.html#getAvoidRoad--">getAvoidRoad</a></span>()</code>
<div class="block">返回设定查询的避让道路。</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.DriveRouteQuery.html#getCarType--">getCarType</a></span>()</code>
<div class="block">返回车辆类型。</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.DriveRouteQuery.html#getExclude--">getExclude</a></span>()</code>
<div class="block">海外生效
 规避道路类型，可选值：toll-收费道路；motorway-高速路；ferry-渡船，默认不规避</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/services/route/RouteSearchV2.FromAndTo.html" title="com.amap.api.services.route中的类">RouteSearchV2.FromAndTo</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.DriveRouteQuery.html#getFromAndTo--">getFromAndTo</a></span>()</code>
<div class="block">返回查询路径的起终点。</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/services/route/RouteSearchV2.DrivingStrategy.html" title="com.amap.api.services.route中的枚举">RouteSearchV2.DrivingStrategy</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.DriveRouteQuery.html#getMode--">getMode</a></span>()</code>
<div class="block">返回计算路径的模式。</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/services/route/RouteSearchV2.NewEnergy.html" title="com.amap.api.services.route中的类">RouteSearchV2.NewEnergy</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.DriveRouteQuery.html#getNewEnergy--">getNewEnergy</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.DriveRouteQuery.html#getPassedByPoints--">getPassedByPoints</a></span>()</code>
<div class="block">返回设定查询的途经点。</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.DriveRouteQuery.html#getPassedPointStr--">getPassedPointStr</a></span>()</code>
<div class="block">将途径点位置坐标转换为字符串输出。</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.DriveRouteQuery.html#getShowFields--">getShowFields</a></span>()</code>
<div class="block">扩展字段</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.DriveRouteQuery.html#isUseFerry--">isUseFerry</a></span>()</code>
<div class="block">是否使用轮渡，默认使用</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.DriveRouteQuery.html#setCarType-int-">setCarType</a></span>(int&nbsp;carType)</code>
<div class="block">设置车辆类型，默认为普通汽车</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.DriveRouteQuery.html#setExclude-java.lang.String-">setExclude</a></span>(java.lang.String&nbsp;exclude)</code>
<div class="block">海外生效
 规避道路类型，默认不规避
 可选值：
 <code>#DRIVING_EXCLUDE_TOLL</code> toll-收费道路； <br>
 <code>#DRIVING_EXCLUDE_MOTORWAY</code> motorway-高速路；<br>
 <code>#DRIVING_EXCLUDE_FERRY</code> ferry-渡船;<br></div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.DriveRouteQuery.html#setNewEnergy-com.amap.api.services.route.RouteSearchV2.NewEnergy-">setNewEnergy</a></span>(<a href="../../../../../com/amap/api/services/route/RouteSearchV2.NewEnergy.html" title="com.amap.api.services.route中的类">RouteSearchV2.NewEnergy</a>&nbsp;newEnergy)</code>&nbsp;</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.DriveRouteQuery.html#setShowFields-int-">setShowFields</a></span>(int&nbsp;showFields)</code>
<div class="block">扩展字段 <a href="../../../../../com/amap/api/services/route/RouteSearchV2.ShowFields.html" title="com.amap.api.services.route中的类"><code>RouteSearchV2.ShowFields</code></a> 说明</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.DriveRouteQuery.html#setUseFerry-boolean-">setUseFerry</a></span>(boolean&nbsp;useFerry)</code>
<div class="block">是否使用轮渡，默认使用</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="DriveRouteQuery-com.amap.api.services.route.RouteSearchV2.FromAndTo-com.amap.api.services.route.RouteSearchV2.DrivingStrategy-java.util.List-java.util.List-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DriveRouteQuery</h4>
<pre>public&nbsp;DriveRouteQuery(<a href="../../../../../com/amap/api/services/route/RouteSearchV2.FromAndTo.html" title="com.amap.api.services.route中的类">RouteSearchV2.FromAndTo</a>&nbsp;fromAndTo,
                       <a href="../../../../../com/amap/api/services/route/RouteSearchV2.DrivingStrategy.html" title="com.amap.api.services.route中的枚举">RouteSearchV2.DrivingStrategy</a>&nbsp;mode,
                       java.util.List&lt;<a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&gt;&nbsp;passedByPoints,
                       java.util.List&lt;java.util.List&lt;<a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&gt;&gt;&nbsp;avoidpolygons,
                       java.lang.String&nbsp;avoidRoad)</pre>
<div class="block">DriveRouteQuery构造函数。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>fromAndTo</code> - 路径的起点终点。</dd>
<dd><code>mode</code> - 计算路径的模式。可选，默认为速度优先 <a href="../../../../../com/amap/api/services/route/RouteSearchV2.DrivingStrategy.html" title="com.amap.api.services.route中的枚举"><code>RouteSearchV2.DrivingStrategy</code></a>。</dd>
<dd><code>passedByPoints</code> - 途经点，可选。最多支持6个途经点。</dd>
<dd><code>avoidpolygons</code> - 避让区域，可选。区域避让，支持32个避让区域，每个区域最多可有16个顶点。如果是四边形则有4个坐标点，如果是五边形则有5个坐标点。同时传入避让区域及避让道路，仅支持避让道路。</dd>
<dd><code>avoidRoad</code> - 避让道路名称，可选。目前只支持一条避让道路。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.2.0</dd>
</dl>
</li>
</ul>
<a name="DriveRouteQuery-Parcel-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DriveRouteQuery</h4>
<pre>public&nbsp;DriveRouteQuery(Parcel&nbsp;source)</pre>
<div class="block">序列化实现</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.2.0</dd>
</dl>
</li>
</ul>
<a name="DriveRouteQuery--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>DriveRouteQuery</h4>
<pre>public&nbsp;DriveRouteQuery()</pre>
<div class="block">DriveRouteQuery构造函数</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.2.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="getNewEnergy--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNewEnergy</h4>
<pre>public&nbsp;<a href="../../../../../com/amap/api/services/route/RouteSearchV2.NewEnergy.html" title="com.amap.api.services.route中的类">RouteSearchV2.NewEnergy</a>&nbsp;getNewEnergy()</pre>
</li>
</ul>
<a name="setNewEnergy-com.amap.api.services.route.RouteSearchV2.NewEnergy-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setNewEnergy</h4>
<pre>public&nbsp;void&nbsp;setNewEnergy(<a href="../../../../../com/amap/api/services/route/RouteSearchV2.NewEnergy.html" title="com.amap.api.services.route中的类">RouteSearchV2.NewEnergy</a>&nbsp;newEnergy)</pre>
</li>
</ul>
<a name="getFromAndTo--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFromAndTo</h4>
<pre>public&nbsp;<a href="../../../../../com/amap/api/services/route/RouteSearchV2.FromAndTo.html" title="com.amap.api.services.route中的类">RouteSearchV2.FromAndTo</a>&nbsp;getFromAndTo()</pre>
<div class="block">返回查询路径的起终点。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>查询路径的起终点。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.2.0</dd>
</dl>
</li>
</ul>
<a name="getMode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMode</h4>
<pre>public&nbsp;<a href="../../../../../com/amap/api/services/route/RouteSearchV2.DrivingStrategy.html" title="com.amap.api.services.route中的枚举">RouteSearchV2.DrivingStrategy</a>&nbsp;getMode()</pre>
<div class="block">返回计算路径的模式。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>计算路径的模式。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.2.0</dd>
</dl>
</li>
</ul>
<a name="getCarType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCarType</h4>
<pre>public&nbsp;int&nbsp;getCarType()</pre>
<div class="block">返回车辆类型。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>车辆类型。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.9.0</dd>
</dl>
</li>
</ul>
<a name="getPassedByPoints--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPassedByPoints</h4>
<pre>public&nbsp;java.util.List&lt;<a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&gt;&nbsp;getPassedByPoints()</pre>
<div class="block">返回设定查询的途经点。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>设定查询的途经点。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.2.0</dd>
</dl>
</li>
</ul>
<a name="getAvoidpolygons--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAvoidpolygons</h4>
<pre>public&nbsp;java.util.List&lt;java.util.List&lt;<a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&gt;&gt;&nbsp;getAvoidpolygons()</pre>
<div class="block">返回设定查询的避让区域。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>设定查询的避让区域。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.2.0</dd>
</dl>
</li>
</ul>
<a name="getAvoidRoad--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAvoidRoad</h4>
<pre>public&nbsp;java.lang.String&nbsp;getAvoidRoad()</pre>
<div class="block">返回设定查询的避让道路。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>设定查询的避让道路。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.2.0</dd>
</dl>
</li>
</ul>
<a name="getPassedPointStr--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPassedPointStr</h4>
<pre>public&nbsp;java.lang.String&nbsp;getPassedPointStr()</pre>
<div class="block">将途径点位置坐标转换为字符串输出。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>途径点坐标的字符串</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.2.0</dd>
</dl>
</li>
</ul>
<a name="getAvoidpolygonsStr--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAvoidpolygonsStr</h4>
<pre>public&nbsp;java.lang.String&nbsp;getAvoidpolygonsStr()</pre>
<div class="block">将避让区域转换为字符串输出。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>避让区域坐标的字符串。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.2.0</dd>
</dl>
</li>
</ul>
<a name="getExclude--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getExclude</h4>
<pre>public&nbsp;java.lang.String&nbsp;getExclude()</pre>
<div class="block">海外生效
 规避道路类型，可选值：toll-收费道路；motorway-高速路；ferry-渡船，默认不规避</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>7.4.0</dd>
</dl>
</li>
</ul>
<a name="setExclude-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setExclude</h4>
<pre>public&nbsp;void&nbsp;setExclude(java.lang.String&nbsp;exclude)</pre>
<div class="block">海外生效
 规避道路类型，默认不规避
 可选值：
 <code>#DRIVING_EXCLUDE_TOLL</code> toll-收费道路； <br>
 <code>#DRIVING_EXCLUDE_MOTORWAY</code> motorway-高速路；<br>
 <code>#DRIVING_EXCLUDE_FERRY</code> ferry-渡船;<br></div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>7.4.0</dd>
</dl>
</li>
</ul>
<a name="getShowFields--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getShowFields</h4>
<pre>public&nbsp;int&nbsp;getShowFields()</pre>
<div class="block">扩展字段</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
</dl>
</li>
</ul>
<a name="setShowFields-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setShowFields</h4>
<pre>public&nbsp;void&nbsp;setShowFields(int&nbsp;showFields)</pre>
<div class="block">扩展字段 <a href="../../../../../com/amap/api/services/route/RouteSearchV2.ShowFields.html" title="com.amap.api.services.route中的类"><code>RouteSearchV2.ShowFields</code></a> 说明</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>showFields</code> - </dd>
</dl>
</li>
</ul>
<a name="equals-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>equals</h4>
<pre>public&nbsp;boolean&nbsp;equals(java.lang.Object&nbsp;obj)</pre>
<div class="block">比较两个查询条件是否相同。</div>
<dl>
<dt><span class="overrideSpecifyLabel">覆盖:</span></dt>
<dd><code>equals</code>&nbsp;在类中&nbsp;<code>java.lang.Object</code></dd>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>obj</code> - 查询条件。</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>查询条件是否相同。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.2.0</dd>
</dl>
</li>
</ul>
<a name="isUseFerry--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isUseFerry</h4>
<pre>public&nbsp;boolean&nbsp;isUseFerry()</pre>
<div class="block">是否使用轮渡，默认使用</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.5.0</dd>
</dl>
</li>
</ul>
<a name="setUseFerry-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setUseFerry</h4>
<pre>public&nbsp;void&nbsp;setUseFerry(boolean&nbsp;useFerry)</pre>
<div class="block">是否使用轮渡，默认使用</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>useFerry</code> - 使用轮渡，true</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.5.0</dd>
</dl>
</li>
</ul>
<a name="setCarType-int-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setCarType</h4>
<pre>public&nbsp;void&nbsp;setCarType(int&nbsp;carType)</pre>
<div class="block">设置车辆类型，默认为普通汽车</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>carType</code> - 车辆类型,支持0：普通汽车，1：纯电动车，2：插电混动车</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.9.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/RouteSearchV2.DriveRouteQuery.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/route/RouteSearchV2.BusRouteQuery.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/amap/api/services/route/RouteSearchV2.DrivingStrategy.html" title="com.amap.api.services.route中的枚举"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/route/RouteSearchV2.DriveRouteQuery.html" target="_top">框架</a></li>
<li><a href="RouteSearchV2.DriveRouteQuery.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
