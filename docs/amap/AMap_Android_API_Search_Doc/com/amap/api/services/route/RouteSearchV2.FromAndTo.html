<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>RouteSearchV2.FromAndTo</title>
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="RouteSearchV2.FromAndTo";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10};
var tabs = {65535:["t0","所有方法"],2:["t2","实例方法"],8:["t4","具体方法"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/RouteSearchV2.FromAndTo.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/route/RouteSearchV2.DrivingStrategy.html" title="com.amap.api.services.route中的枚举"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/amap/api/services/route/RouteSearchV2.OnRoutePlanSearchListener.html" title="com.amap.api.services.route中的接口"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/route/RouteSearchV2.FromAndTo.html" target="_top">框架</a></li>
<li><a href="RouteSearchV2.FromAndTo.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.amap.api.services.route</div>
<h2 title="类 RouteSearchV2.FromAndTo" class="title">类 RouteSearchV2.FromAndTo</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.amap.api.services.route.RouteSearchV2.FromAndTo</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>所有已实现的接口:</dt>
<dd>java.lang.Cloneable</dd>
</dl>
<dl>
<dt>封闭类:</dt>
<dd><a href="../../../../../com/amap/api/services/route/RouteSearchV2.html" title="com.amap.api.services.route中的类">RouteSearchV2</a></dd>
</dl>
<hr>
<br>
<pre>public static class <span class="typeNameLabel">RouteSearchV2.FromAndTo</span>
extends java.lang.Object
implements java.lang.Cloneable</pre>
<div class="block">构造路径规划的起点和终点坐标。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.2.0</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.FromAndTo.html#FromAndTo-com.amap.api.services.core.LatLonPoint-com.amap.api.services.core.LatLonPoint-">FromAndTo</a></span>(<a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&nbsp;from,
         <a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&nbsp;to)</code>
<div class="block">FromAndTo的构造函数。</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>所有方法</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">实例方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">具体方法</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.FromAndTo.html#equals-java.lang.Object-">equals</a></span>(java.lang.Object&nbsp;obj)</code>
<div class="block">比较路径规划的起终点是否相同。</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.FromAndTo.html#getDestinationPoiID--">getDestinationPoiID</a></span>()</code>
<div class="block">返回路径规划目的地POI的ID。</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.FromAndTo.html#getDestinationType--">getDestinationType</a></span>()</code>
<div class="block">返回路径规划目的地类型。</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.FromAndTo.html#getFrom--">getFrom</a></span>()</code>
<div class="block">返回路径规划的起点坐标。</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.FromAndTo.html#getOriginType--">getOriginType</a></span>()</code>
<div class="block">返回路径规划起点的类型。</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.FromAndTo.html#getPlateNumber--">getPlateNumber</a></span>()</code>
<div class="block">获取车牌号</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.FromAndTo.html#getStartPoiID--">getStartPoiID</a></span>()</code>
<div class="block">返回路径规划起点POI的ID。</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.FromAndTo.html#getTo--">getTo</a></span>()</code>
<div class="block">返回路径规划的终点坐标。</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.FromAndTo.html#setDestinationPoiID-java.lang.String-">setDestinationPoiID</a></span>(java.lang.String&nbsp;mDestinationPoiID)</code>
<div class="block">设置路径规划目的地POI的ID。</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.FromAndTo.html#setDestinationType-java.lang.String-">setDestinationType</a></span>(java.lang.String&nbsp;destinationtype)</code>
<div class="block">设置路径规划目的地类型。</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.FromAndTo.html#setOriginType-java.lang.String-">setOriginType</a></span>(java.lang.String&nbsp;origintype)</code>
<div class="block">设置路径规划起点类型。</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.FromAndTo.html#setPlateNumber-java.lang.String-">setPlateNumber</a></span>(java.lang.String&nbsp;plateNumber)</code>
<div class="block">设置车牌号</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.FromAndTo.html#setStartPoiID-java.lang.String-">setStartPoiID</a></span>(java.lang.String&nbsp;mStartPoiID)</code>
<div class="block">设置路径规划起点POI的ID。</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="FromAndTo-com.amap.api.services.core.LatLonPoint-com.amap.api.services.core.LatLonPoint-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>FromAndTo</h4>
<pre>public&nbsp;FromAndTo(<a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&nbsp;from,
                 <a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&nbsp;to)</pre>
<div class="block">FromAndTo的构造函数。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.2.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="getFrom--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFrom</h4>
<pre>public&nbsp;<a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&nbsp;getFrom()</pre>
<div class="block">返回路径规划的起点坐标。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>路径规划的起点坐标。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.2.0</dd>
</dl>
</li>
</ul>
<a name="getTo--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTo</h4>
<pre>public&nbsp;<a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&nbsp;getTo()</pre>
<div class="block">返回路径规划的终点坐标。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>路径规划的终点坐标。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.2.0</dd>
</dl>
</li>
</ul>
<a name="getStartPoiID--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStartPoiID</h4>
<pre>public&nbsp;java.lang.String&nbsp;getStartPoiID()</pre>
<div class="block">返回路径规划起点POI的ID。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>路径规划的起点POI的ID。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.2.0</dd>
</dl>
</li>
</ul>
<a name="setStartPoiID-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setStartPoiID</h4>
<pre>public&nbsp;void&nbsp;setStartPoiID(java.lang.String&nbsp;mStartPoiID)</pre>
<div class="block">设置路径规划起点POI的ID。<br>
 仅驾车、货车支持<br>
 填充此值以后，会影响路径规划的结果，举例来说，当起点的经纬度在高架桥上面，<br>
 若填充了此值我们会以此POI的经纬度作为更高优的处理。<br></div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>mStartPoiID</code> - 路径规划的起点POI的ID。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.2.0</dd>
</dl>
</li>
</ul>
<a name="getDestinationPoiID--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDestinationPoiID</h4>
<pre>public&nbsp;java.lang.String&nbsp;getDestinationPoiID()</pre>
<div class="block">返回路径规划目的地POI的ID。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>路径规划的目的地POI的ID。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.2.0</dd>
</dl>
</li>
</ul>
<a name="setDestinationPoiID-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDestinationPoiID</h4>
<pre>public&nbsp;void&nbsp;setDestinationPoiID(java.lang.String&nbsp;mDestinationPoiID)</pre>
<div class="block">设置路径规划目的地POI的ID。<br>
 仅驾车、货车支持<br>
 填充此值以后，会影响路径规划的结果，举例来说，当终点的经纬度在高架桥上面，<br>
 若填充了此值我们会以此POI的经纬度作为更高优的处理。<br></div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>mDestinationPoiID</code> - 路径规划的目的地POI的ID。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.2.0</dd>
</dl>
</li>
</ul>
<a name="getOriginType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getOriginType</h4>
<pre>public&nbsp;java.lang.String&nbsp;getOriginType()</pre>
<div class="block">返回路径规划起点的类型。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>路径规划起点类型。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.2.0</dd>
</dl>
</li>
</ul>
<a name="setOriginType-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setOriginType</h4>
<pre>public&nbsp;void&nbsp;setOriginType(java.lang.String&nbsp;origintype)</pre>
<div class="block">设置路径规划起点类型。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>origintype</code> - 路径规划起点类型。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.2.0</dd>
</dl>
</li>
</ul>
<a name="getDestinationType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDestinationType</h4>
<pre>public&nbsp;java.lang.String&nbsp;getDestinationType()</pre>
<div class="block">返回路径规划目的地类型。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>路径规划目的地类型。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.2.0</dd>
</dl>
</li>
</ul>
<a name="setDestinationType-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDestinationType</h4>
<pre>public&nbsp;void&nbsp;setDestinationType(java.lang.String&nbsp;destinationtype)</pre>
<div class="block">设置路径规划目的地类型。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>destinationtype</code> - 路径规划目的地类型。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.2.0</dd>
</dl>
</li>
</ul>
<a name="getPlateNumber--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPlateNumber</h4>
<pre>public&nbsp;java.lang.String&nbsp;getPlateNumber()</pre>
<div class="block">获取车牌号</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>车牌号</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>5.1.0</dd>
</dl>
</li>
</ul>
<a name="setPlateNumber-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPlateNumber</h4>
<pre>public&nbsp;void&nbsp;setPlateNumber(java.lang.String&nbsp;plateNumber)</pre>
<div class="block">设置车牌号</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>plateNumber</code> - 车牌详情 填入除省份及标点之外的字母和数字（需大写）。用于判断限行相关。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>5.1.0</dd>
</dl>
</li>
</ul>
<a name="equals-java.lang.Object-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>equals</h4>
<pre>public&nbsp;boolean&nbsp;equals(java.lang.Object&nbsp;obj)</pre>
<div class="block">比较路径规划的起终点是否相同。</div>
<dl>
<dt><span class="overrideSpecifyLabel">覆盖:</span></dt>
<dd><code>equals</code>&nbsp;在类中&nbsp;<code>java.lang.Object</code></dd>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>obj</code> - 待比较对象。</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>路径规划起终点是否相同。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.2.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/RouteSearchV2.FromAndTo.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/route/RouteSearchV2.DrivingStrategy.html" title="com.amap.api.services.route中的枚举"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/amap/api/services/route/RouteSearchV2.OnRoutePlanSearchListener.html" title="com.amap.api.services.route中的接口"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/route/RouteSearchV2.FromAndTo.html" target="_top">框架</a></li>
<li><a href="RouteSearchV2.FromAndTo.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
