<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>ElecConsumeInfo</title>
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ElecConsumeInfo";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10};
var tabs = {65535:["t0","所有方法"],2:["t2","实例方法"],8:["t4","具体方法"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/ElecConsumeInfo.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/route/DriveStepV2.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/amap/api/services/route/Navi.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/route/ElecConsumeInfo.html" target="_top">框架</a></li>
<li><a href="ElecConsumeInfo.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.amap.api.services.route</div>
<h2 title="类 ElecConsumeInfo" class="title">类 ElecConsumeInfo</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.amap.api.services.route.ElecConsumeInfo</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">ElecConsumeInfo</span>
extends java.lang.Object</pre>
<div class="block">新能源电量消耗信息

 //* @exclude javadoc中不显示</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/ElecConsumeInfo.html#ElecConsumeInfo--">ElecConsumeInfo</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>所有方法</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">实例方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">具体方法</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/ElecConsumeInfo.html#getConsumeEnergy--">getConsumeEnergy</a></span>()</code>
<div class="block">获取路线消耗总能量，单位：瓦时</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>java.util.List&lt;java.lang.Integer&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/ElecConsumeInfo.html#getLeftEnergy--">getLeftEnergy</a></span>()</code>
<div class="block">获取途经点及终点的剩余能量，有符号，按顺序，最后一个是终点，单位：百分之一瓦时</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/ElecConsumeInfo.html#getRunOutPoint--">getRunOutPoint</a></span>()</code>
<div class="block">获取能量耗尽点坐标</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/ElecConsumeInfo.html#getRunOutStepIndex--">getRunOutStepIndex</a></span>()</code>
<div class="block">获取能量耗尽点的导航段steps索引</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/ElecConsumeInfo.html#setConsumeEnergy-int-">setConsumeEnergy</a></span>(int&nbsp;consumeEnergy)</code>
<div class="block">设置路线消耗总能量，单位：瓦时</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/ElecConsumeInfo.html#setLeftEnergy-java.util.List-">setLeftEnergy</a></span>(java.util.List&lt;java.lang.Integer&gt;&nbsp;leftEnergy)</code>
<div class="block">设置途经点及终点的剩余能量，有符号，按顺序，最后一个是终点，单位：百分之一瓦时</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/ElecConsumeInfo.html#setRunOutPoint-com.amap.api.services.core.LatLonPoint-">setRunOutPoint</a></span>(<a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&nbsp;runOutPoint)</code>
<div class="block">设置能量耗尽点坐标</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/ElecConsumeInfo.html#setRunOutStepIndex-int-">setRunOutStepIndex</a></span>(int&nbsp;runOutStepIndex)</code>
<div class="block">设置能量耗尽点的导航段steps索引</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="ElecConsumeInfo--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>ElecConsumeInfo</h4>
<pre>public&nbsp;ElecConsumeInfo()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="getConsumeEnergy--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getConsumeEnergy</h4>
<pre>public&nbsp;int&nbsp;getConsumeEnergy()</pre>
<div class="block">获取路线消耗总能量，单位：瓦时</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>路线消耗总能量，单位：瓦时</dd>
</dl>
</li>
</ul>
<a name="setConsumeEnergy-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setConsumeEnergy</h4>
<pre>public&nbsp;void&nbsp;setConsumeEnergy(int&nbsp;consumeEnergy)</pre>
<div class="block">设置路线消耗总能量，单位：瓦时</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>consumeEnergy</code> - 路线消耗总能量，单位：瓦时</dd>
</dl>
</li>
</ul>
<a name="getRunOutStepIndex--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRunOutStepIndex</h4>
<pre>public&nbsp;int&nbsp;getRunOutStepIndex()</pre>
<div class="block">获取能量耗尽点的导航段steps索引</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>能量耗尽点的导航段steps索引</dd>
</dl>
</li>
</ul>
<a name="setRunOutStepIndex-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRunOutStepIndex</h4>
<pre>public&nbsp;void&nbsp;setRunOutStepIndex(int&nbsp;runOutStepIndex)</pre>
<div class="block">设置能量耗尽点的导航段steps索引</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>runOutStepIndex</code> - 能量耗尽点的导航段steps索引</dd>
</dl>
</li>
</ul>
<a name="getRunOutPoint--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRunOutPoint</h4>
<pre>public&nbsp;<a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&nbsp;getRunOutPoint()</pre>
<div class="block">获取能量耗尽点坐标</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>能量耗尽点坐标</dd>
</dl>
</li>
</ul>
<a name="setRunOutPoint-com.amap.api.services.core.LatLonPoint-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRunOutPoint</h4>
<pre>public&nbsp;void&nbsp;setRunOutPoint(<a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&nbsp;runOutPoint)</pre>
<div class="block">设置能量耗尽点坐标</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>runOutPoint</code> - 能量耗尽点坐标</dd>
</dl>
</li>
</ul>
<a name="getLeftEnergy--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLeftEnergy</h4>
<pre>public&nbsp;java.util.List&lt;java.lang.Integer&gt;&nbsp;getLeftEnergy()</pre>
<div class="block">获取途经点及终点的剩余能量，有符号，按顺序，最后一个是终点，单位：百分之一瓦时</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>途经点及终点的剩余能量，有符号，按顺序，最后一个是终点，单位：百分之一瓦时</dd>
</dl>
</li>
</ul>
<a name="setLeftEnergy-java.util.List-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setLeftEnergy</h4>
<pre>public&nbsp;void&nbsp;setLeftEnergy(java.util.List&lt;java.lang.Integer&gt;&nbsp;leftEnergy)</pre>
<div class="block">设置途经点及终点的剩余能量，有符号，按顺序，最后一个是终点，单位：百分之一瓦时</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>leftEnergy</code> - 途经点及终点的剩余能量，有符号，按顺序，最后一个是终点，单位：百分之一瓦时</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/ElecConsumeInfo.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/route/DriveStepV2.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/amap/api/services/route/Navi.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/route/ElecConsumeInfo.html" target="_top">框架</a></li>
<li><a href="ElecConsumeInfo.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
