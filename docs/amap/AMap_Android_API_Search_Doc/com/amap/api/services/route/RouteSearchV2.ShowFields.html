<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>RouteSearchV2.ShowFields</title>
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="RouteSearchV2.ShowFields";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/RouteSearchV2.ShowFields.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/route/RouteSearchV2.RideRouteQuery.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/amap/api/services/route/RouteSearchV2.WalkRouteQuery.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/route/RouteSearchV2.ShowFields.html" target="_top">框架</a></li>
<li><a href="RouteSearchV2.ShowFields.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li><a href="#field.summary">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#methods.inherited.from.class.java.lang.Object">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li><a href="#field.detail">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li>方法</li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.amap.api.services.route</div>
<h2 title="类 RouteSearchV2.ShowFields" class="title">类 RouteSearchV2.ShowFields</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.amap.api.services.route.RouteSearchV2.ShowFields</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>封闭类:</dt>
<dd><a href="../../../../../com/amap/api/services/route/RouteSearchV2.html" title="com.amap.api.services.route中的类">RouteSearchV2</a></dd>
</dl>
<hr>
<br>
<pre>public static class <span class="typeNameLabel">RouteSearchV2.ShowFields</span>
extends java.lang.Object</pre>
<div class="block">扩展字段</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>字段概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="字段概要表, 列表字段和解释">
<caption><span>字段</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">字段和说明</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.ShowFields.html#ALL">ALL</a></span></code>
<div class="block">设置后添加所有字段</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.ShowFields.html#CITIES">CITIES</a></span></code>
<div class="block">设置后可返回分段途径城市信息</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.ShowFields.html#COST">COST</a></span></code>
<div class="block">设置后可返回方案所需时间及费用成本</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.ShowFields.html#NAVI">NAVI</a></span></code>
<div class="block">设置后可返回详细导航动作指令</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.ShowFields.html#POLINE">POLINE</a></span></code>
<div class="block">设置后可返回分路段坐标点串，两点间用“,”分隔</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.ShowFields.html#TMCS">TMCS</a></span></code>
<div class="block">设置后可返回分段路况详情</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.ShowFields.html#ShowFields--">ShowFields</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>字段详细资料</h3>
<a name="COST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>COST</h4>
<pre>public static final&nbsp;int COST</pre>
<div class="block">设置后可返回方案所需时间及费用成本</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.route.RouteSearchV2.ShowFields.COST">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="TMCS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TMCS</h4>
<pre>public static final&nbsp;int TMCS</pre>
<div class="block">设置后可返回分段路况详情</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.route.RouteSearchV2.ShowFields.TMCS">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="NAVI">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NAVI</h4>
<pre>public static final&nbsp;int NAVI</pre>
<div class="block">设置后可返回详细导航动作指令</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.route.RouteSearchV2.ShowFields.NAVI">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="CITIES">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CITIES</h4>
<pre>public static final&nbsp;int CITIES</pre>
<div class="block">设置后可返回分段途径城市信息</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.route.RouteSearchV2.ShowFields.CITIES">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="POLINE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>POLINE</h4>
<pre>public static final&nbsp;int POLINE</pre>
<div class="block">设置后可返回分路段坐标点串，两点间用“,”分隔</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.route.RouteSearchV2.ShowFields.POLINE">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="ALL">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>ALL</h4>
<pre>public static final&nbsp;int ALL</pre>
<div class="block">设置后添加所有字段</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.route.RouteSearchV2.ShowFields.ALL">常量字段值</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="ShowFields--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>ShowFields</h4>
<pre>public&nbsp;ShowFields()</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/RouteSearchV2.ShowFields.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/route/RouteSearchV2.RideRouteQuery.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/amap/api/services/route/RouteSearchV2.WalkRouteQuery.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/route/RouteSearchV2.ShowFields.html" target="_top">框架</a></li>
<li><a href="RouteSearchV2.ShowFields.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li><a href="#field.summary">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#methods.inherited.from.class.java.lang.Object">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li><a href="#field.detail">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li>方法</li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
