<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>RouteSearchV2.BusRouteQuery</title>
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="RouteSearchV2.BusRouteQuery";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10};
var tabs = {65535:["t0","所有方法"],2:["t2","实例方法"],8:["t4","具体方法"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/RouteSearchV2.BusRouteQuery.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/route/RouteSearchV2.BusMode.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/amap/api/services/route/RouteSearchV2.DriveRouteQuery.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/route/RouteSearchV2.BusRouteQuery.html" target="_top">框架</a></li>
<li><a href="RouteSearchV2.BusRouteQuery.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.amap.api.services.route</div>
<h2 title="类 RouteSearchV2.BusRouteQuery" class="title">类 RouteSearchV2.BusRouteQuery</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.amap.api.services.route.RouteSearchV2.BusRouteQuery</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>所有已实现的接口:</dt>
<dd>java.lang.Cloneable</dd>
</dl>
<dl>
<dt>封闭类:</dt>
<dd><a href="../../../../../com/amap/api/services/route/RouteSearchV2.html" title="com.amap.api.services.route中的类">RouteSearchV2</a></dd>
</dl>
<hr>
<br>
<pre>public static class <span class="typeNameLabel">RouteSearchV2.BusRouteQuery</span>
extends java.lang.Object
implements java.lang.Cloneable</pre>
<div class="block">此类定义了公交路径规划查询路径的起终点、计算路径的模式、城市和是否计算夜班车。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.4.0</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.BusRouteQuery.html#BusRouteQuery-com.amap.api.services.route.RouteSearchV2.FromAndTo-int-java.lang.String-int-">BusRouteQuery</a></span>(<a href="../../../../../com/amap/api/services/route/RouteSearchV2.FromAndTo.html" title="com.amap.api.services.route中的类">RouteSearchV2.FromAndTo</a>&nbsp;fromAndTo,
             int&nbsp;mode,
             java.lang.String&nbsp;city,
             int&nbsp;nightFlag)</code>
<div class="block">BusRouteQuery构造函数。</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>所有方法</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">实例方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">具体方法</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.BusRouteQuery.html#equals-java.lang.Object-">equals</a></span>(java.lang.Object&nbsp;o)</code>
<div class="block">比较两个查询条件是否相同。</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.BusRouteQuery.html#getAd1--">getAd1</a></span>()</code>
<div class="block">起点所在行政区域编码

 仅支持adcode，参考行政区域编码表</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.BusRouteQuery.html#getAd2--">getAd2</a></span>()</code>
<div class="block">终点所在行政区域编码

 仅支持adcode，参考行政区域编码表</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.BusRouteQuery.html#getAlternativeRoute--">getAlternativeRoute</a></span>()</code>
<div class="block">返回方案条数

 可传入1-10的阿拉伯数字，代表返回的不同条数。</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.BusRouteQuery.html#getCity--">getCity</a></span>()</code>
<div class="block">返回查询的城市。</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.BusRouteQuery.html#getCityd--">getCityd</a></span>()</code>
<div class="block">返回目的地的城市（跨城公交路径规划）。</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.BusRouteQuery.html#getDate--">getDate</a></span>()</code>
<div class="block">请求日期

 例如:2013-10-28</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.BusRouteQuery.html#getDestinationPoiId--">getDestinationPoiId</a></span>()</code>
<div class="block">目的地POI ID

 1、目的地POI ID与目的地经纬度均填写时，服务使用目的地 POI ID；

 2、该字段必须和起点 POI ID 成组使用。</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/services/route/RouteSearchV2.FromAndTo.html" title="com.amap.api.services.route中的类">RouteSearchV2.FromAndTo</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.BusRouteQuery.html#getFromAndTo--">getFromAndTo</a></span>()</code>
<div class="block">返回查询路径的起终点。</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.BusRouteQuery.html#getMaxTrans--">getMaxTrans</a></span>()</code>
<div class="block">最大换乘次数

 0：直达

 1：最多换乘1次

 2：最多换乘2次

 3：最多换乘3次

 4：最多换乘4次</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.BusRouteQuery.html#getMode--">getMode</a></span>()</code>
<div class="block">返回计算路径的模式。</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.BusRouteQuery.html#getMultiExport--">getMultiExport</a></span>()</code>
<div class="block">地铁出入口数量

 0：只返回一个地铁出入口

 1：返回全部地铁出入口</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.BusRouteQuery.html#getNightFlag--">getNightFlag</a></span>()</code>
<div class="block">返回是否计算夜班车。</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.BusRouteQuery.html#getOriginPoiId--">getOriginPoiId</a></span>()</code>
<div class="block">起点POI ID

 1、起点POI ID与起点经纬度均填写时，服务使用起点 POI ID；

 2、该字段必须和目的地 POI ID 成组使用。</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.BusRouteQuery.html#getShowFields--">getShowFields</a></span>()</code>
<div class="block">扩展字段，all表示所有数据 ，默认 cost <a href="../../../../../com/amap/api/services/route/RouteSearchV2.ShowFields.html" title="com.amap.api.services.route中的类"><code>RouteSearchV2.ShowFields</code></a></div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.BusRouteQuery.html#getTime--">getTime</a></span>()</code>
<div class="block">请求时间

 例如:9-54</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.BusRouteQuery.html#setAd1-java.lang.String-">setAd1</a></span>(java.lang.String&nbsp;mAd1)</code>
<div class="block">起点所在行政区域编码

 仅支持adcode，参考行政区域编码表</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.BusRouteQuery.html#setAd2-java.lang.String-">setAd2</a></span>(java.lang.String&nbsp;mAd2)</code>
<div class="block">终点所在行政区域编码

 仅支持adcode，参考行政区域编码表</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.BusRouteQuery.html#setAlternativeRoute-int-">setAlternativeRoute</a></span>(int&nbsp;mAlternativeRoute)</code>
<div class="block">返回方案条数

 可传入1-10的阿拉伯数字，代表返回的不同条数。</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.BusRouteQuery.html#setCityd-java.lang.String-">setCityd</a></span>(java.lang.String&nbsp;cityd)</code>
<div class="block">设置跨城公交规划的目的地城市。</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.BusRouteQuery.html#setDate-java.lang.String-">setDate</a></span>(java.lang.String&nbsp;mDate)</code>
<div class="block">请求日期

 例如:2013-10-28</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.BusRouteQuery.html#setDestinationPoiId-java.lang.String-">setDestinationPoiId</a></span>(java.lang.String&nbsp;mDestinationPoiId)</code>
<div class="block">目的地POI ID

 1、目的地POI ID与目的地经纬度均填写时，服务使用目的地 POI ID；

 2、该字段必须和起点 POI ID 成组使用。</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.BusRouteQuery.html#setMaxTrans-int-">setMaxTrans</a></span>(int&nbsp;mMaxTrans)</code>
<div class="block">最大换乘次数

 0：直达

 1：最多换乘1次

 2：最多换乘2次

 3：最多换乘3次

 4：最多换乘4次</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.BusRouteQuery.html#setMultiExport-int-">setMultiExport</a></span>(int&nbsp;mMultiExport)</code>
<div class="block">地铁出入口数量

 0：只返回一个地铁出入口

 1：返回全部地铁出入口</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.BusRouteQuery.html#setOriginPoiId-java.lang.String-">setOriginPoiId</a></span>(java.lang.String&nbsp;mOriginPoiId)</code>
<div class="block">起点POI ID

 1、起点POI ID与起点经纬度均填写时，服务使用起点 POI ID；

 2、该字段必须和目的地 POI ID 成组使用。</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.BusRouteQuery.html#setShowFields-int-">setShowFields</a></span>(int&nbsp;showFields)</code>
<div class="block">扩展字段，all表示所有数据 ，默认 cost <a href="../../../../../com/amap/api/services/route/RouteSearchV2.ShowFields.html" title="com.amap.api.services.route中的类"><code>RouteSearchV2.ShowFields</code></a></div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.BusRouteQuery.html#setTime-java.lang.String-">setTime</a></span>(java.lang.String&nbsp;mTime)</code>
<div class="block">请求时间

 例如:9-54</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="BusRouteQuery-com.amap.api.services.route.RouteSearchV2.FromAndTo-int-java.lang.String-int-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>BusRouteQuery</h4>
<pre>public&nbsp;BusRouteQuery(<a href="../../../../../com/amap/api/services/route/RouteSearchV2.FromAndTo.html" title="com.amap.api.services.route中的类">RouteSearchV2.FromAndTo</a>&nbsp;fromAndTo,
                     int&nbsp;mode,
                     java.lang.String&nbsp;city,
                     int&nbsp;nightFlag)</pre>
<div class="block">BusRouteQuery构造函数。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>fromAndTo</code> - 路径的起终点。</dd>
<dd><code>mode</code> - 计算路径的模式。可选，默认为最快捷 <a href="../../../../../com/amap/api/services/route/RouteSearchV2.BusMode.html" title="com.amap.api.services.route中的类"><code>RouteSearchV2.BusMode</code></a>。</dd>
<dd><code>city</code> - 城市区号/电话区号。此项不能为空。</dd>
<dd><code>nightFlag</code> - 是否计算夜班车，默认为不计算。0：不计算，1：计算。可选。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="getFromAndTo--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFromAndTo</h4>
<pre>public&nbsp;<a href="../../../../../com/amap/api/services/route/RouteSearchV2.FromAndTo.html" title="com.amap.api.services.route中的类">RouteSearchV2.FromAndTo</a>&nbsp;getFromAndTo()</pre>
<div class="block">返回查询路径的起终点。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>查询路径的起终点。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.4.0</dd>
</dl>
</li>
</ul>
<a name="getMode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMode</h4>
<pre>public&nbsp;int&nbsp;getMode()</pre>
<div class="block">返回计算路径的模式。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>计算路径的模式。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.4.0</dd>
</dl>
</li>
</ul>
<a name="getCity--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCity</h4>
<pre>public&nbsp;java.lang.String&nbsp;getCity()</pre>
<div class="block">返回查询的城市。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>查询的城市。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.4.0</dd>
</dl>
</li>
</ul>
<a name="getNightFlag--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNightFlag</h4>
<pre>public&nbsp;int&nbsp;getNightFlag()</pre>
<div class="block">返回是否计算夜班车。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>是否计算夜班车，默认为不计算。0：不计算，1：计算。可选。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.4.0</dd>
</dl>
</li>
</ul>
<a name="getCityd--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCityd</h4>
<pre>public&nbsp;java.lang.String&nbsp;getCityd()</pre>
<div class="block">返回目的地的城市（跨城公交路径规划）。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>目的地的城市。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.4.0</dd>
</dl>
</li>
</ul>
<a name="setCityd-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCityd</h4>
<pre>public&nbsp;void&nbsp;setCityd(java.lang.String&nbsp;cityd)</pre>
<div class="block">设置跨城公交规划的目的地城市。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>cityd</code> - 目的地城市。跨城公交规划必填参数。可选值：城市名称/citycode</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.4.0</dd>
</dl>
</li>
</ul>
<a name="getShowFields--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getShowFields</h4>
<pre>public&nbsp;int&nbsp;getShowFields()</pre>
<div class="block">扩展字段，all表示所有数据 ，默认 cost <a href="../../../../../com/amap/api/services/route/RouteSearchV2.ShowFields.html" title="com.amap.api.services.route中的类"><code>RouteSearchV2.ShowFields</code></a></div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.4.0</dd>
</dl>
</li>
</ul>
<a name="setShowFields-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setShowFields</h4>
<pre>public&nbsp;void&nbsp;setShowFields(int&nbsp;showFields)</pre>
<div class="block">扩展字段，all表示所有数据 ，默认 cost <a href="../../../../../com/amap/api/services/route/RouteSearchV2.ShowFields.html" title="com.amap.api.services.route中的类"><code>RouteSearchV2.ShowFields</code></a></div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.4.0</dd>
</dl>
</li>
</ul>
<a name="getDate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDate</h4>
<pre>public&nbsp;java.lang.String&nbsp;getDate()</pre>
<div class="block">请求日期

 例如:2013-10-28</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
</dl>
</li>
</ul>
<a name="setDate-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDate</h4>
<pre>public&nbsp;void&nbsp;setDate(java.lang.String&nbsp;mDate)</pre>
<div class="block">请求日期

 例如:2013-10-28</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>mDate</code> - </dd>
</dl>
</li>
</ul>
<a name="getTime--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTime</h4>
<pre>public&nbsp;java.lang.String&nbsp;getTime()</pre>
<div class="block">请求时间

 例如:9-54</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
</dl>
</li>
</ul>
<a name="setTime-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTime</h4>
<pre>public&nbsp;void&nbsp;setTime(java.lang.String&nbsp;mTime)</pre>
<div class="block">请求时间

 例如:9-54</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>mTime</code> - </dd>
</dl>
</li>
</ul>
<a name="getOriginPoiId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getOriginPoiId</h4>
<pre>public&nbsp;java.lang.String&nbsp;getOriginPoiId()</pre>
<div class="block">起点POI ID

 1、起点POI ID与起点经纬度均填写时，服务使用起点 POI ID；

 2、该字段必须和目的地 POI ID 成组使用。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
</dl>
</li>
</ul>
<a name="setOriginPoiId-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setOriginPoiId</h4>
<pre>public&nbsp;void&nbsp;setOriginPoiId(java.lang.String&nbsp;mOriginPoiId)</pre>
<div class="block">起点POI ID

 1、起点POI ID与起点经纬度均填写时，服务使用起点 POI ID；

 2、该字段必须和目的地 POI ID 成组使用。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>mOriginPoiId</code> - </dd>
</dl>
</li>
</ul>
<a name="getDestinationPoiId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDestinationPoiId</h4>
<pre>public&nbsp;java.lang.String&nbsp;getDestinationPoiId()</pre>
<div class="block">目的地POI ID

 1、目的地POI ID与目的地经纬度均填写时，服务使用目的地 POI ID；

 2、该字段必须和起点 POI ID 成组使用。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
</dl>
</li>
</ul>
<a name="setDestinationPoiId-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDestinationPoiId</h4>
<pre>public&nbsp;void&nbsp;setDestinationPoiId(java.lang.String&nbsp;mDestinationPoiId)</pre>
<div class="block">目的地POI ID

 1、目的地POI ID与目的地经纬度均填写时，服务使用目的地 POI ID；

 2、该字段必须和起点 POI ID 成组使用。</div>
</li>
</ul>
<a name="getAd1--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAd1</h4>
<pre>public&nbsp;java.lang.String&nbsp;getAd1()</pre>
<div class="block">起点所在行政区域编码

 仅支持adcode，参考行政区域编码表</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
</dl>
</li>
</ul>
<a name="setAd1-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAd1</h4>
<pre>public&nbsp;void&nbsp;setAd1(java.lang.String&nbsp;mAd1)</pre>
<div class="block">起点所在行政区域编码

 仅支持adcode，参考行政区域编码表</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>mAd1</code> - </dd>
</dl>
</li>
</ul>
<a name="getAd2--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAd2</h4>
<pre>public&nbsp;java.lang.String&nbsp;getAd2()</pre>
<div class="block">终点所在行政区域编码

 仅支持adcode，参考行政区域编码表</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
</dl>
</li>
</ul>
<a name="setAd2-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAd2</h4>
<pre>public&nbsp;void&nbsp;setAd2(java.lang.String&nbsp;mAd2)</pre>
<div class="block">终点所在行政区域编码

 仅支持adcode，参考行政区域编码表</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>mAd2</code> - </dd>
</dl>
</li>
</ul>
<a name="getAlternativeRoute--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAlternativeRoute</h4>
<pre>public&nbsp;int&nbsp;getAlternativeRoute()</pre>
<div class="block">返回方案条数

 可传入1-10的阿拉伯数字，代表返回的不同条数。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
</dl>
</li>
</ul>
<a name="setAlternativeRoute-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAlternativeRoute</h4>
<pre>public&nbsp;void&nbsp;setAlternativeRoute(int&nbsp;mAlternativeRoute)</pre>
<div class="block">返回方案条数

 可传入1-10的阿拉伯数字，代表返回的不同条数。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>mAlternativeRoute</code> - </dd>
</dl>
</li>
</ul>
<a name="getMultiExport--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMultiExport</h4>
<pre>public&nbsp;int&nbsp;getMultiExport()</pre>
<div class="block">地铁出入口数量

 0：只返回一个地铁出入口

 1：返回全部地铁出入口</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
</dl>
</li>
</ul>
<a name="setMultiExport-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMultiExport</h4>
<pre>public&nbsp;void&nbsp;setMultiExport(int&nbsp;mMultiExport)</pre>
<div class="block">地铁出入口数量

 0：只返回一个地铁出入口

 1：返回全部地铁出入口</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>mMultiExport</code> - </dd>
</dl>
</li>
</ul>
<a name="getMaxTrans--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMaxTrans</h4>
<pre>public&nbsp;int&nbsp;getMaxTrans()</pre>
<div class="block">最大换乘次数

 0：直达

 1：最多换乘1次

 2：最多换乘2次

 3：最多换乘3次

 4：最多换乘4次</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
</dl>
</li>
</ul>
<a name="setMaxTrans-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMaxTrans</h4>
<pre>public&nbsp;void&nbsp;setMaxTrans(int&nbsp;mMaxTrans)</pre>
<div class="block">最大换乘次数

 0：直达

 1：最多换乘1次

 2：最多换乘2次

 3：最多换乘3次

 4：最多换乘4次</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>mMaxTrans</code> - </dd>
</dl>
</li>
</ul>
<a name="equals-java.lang.Object-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>equals</h4>
<pre>public&nbsp;boolean&nbsp;equals(java.lang.Object&nbsp;o)</pre>
<div class="block">比较两个查询条件是否相同。</div>
<dl>
<dt><span class="overrideSpecifyLabel">覆盖:</span></dt>
<dd><code>equals</code>&nbsp;在类中&nbsp;<code>java.lang.Object</code></dd>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>obj</code> - 查询条件。</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>查询条件是否相同。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/RouteSearchV2.BusRouteQuery.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/route/RouteSearchV2.BusMode.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/amap/api/services/route/RouteSearchV2.DriveRouteQuery.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/route/RouteSearchV2.BusRouteQuery.html" target="_top">框架</a></li>
<li><a href="RouteSearchV2.BusRouteQuery.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
