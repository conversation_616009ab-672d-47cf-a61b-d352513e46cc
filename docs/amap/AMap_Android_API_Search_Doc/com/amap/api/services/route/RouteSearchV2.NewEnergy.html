<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>RouteSearchV2.NewEnergy</title>
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="RouteSearchV2.NewEnergy";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10};
var tabs = {65535:["t0","所有方法"],2:["t2","实例方法"],8:["t4","具体方法"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/RouteSearchV2.NewEnergy.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/route/RouteSearchV2.FromAndTo.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/amap/api/services/route/RouteSearchV2.OnRoutePlanSearchListener.html" title="com.amap.api.services.route中的接口"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/route/RouteSearchV2.NewEnergy.html" target="_top">框架</a></li>
<li><a href="RouteSearchV2.NewEnergy.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.amap.api.services.route</div>
<h2 title="类 RouteSearchV2.NewEnergy" class="title">类 RouteSearchV2.NewEnergy</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.amap.api.services.route.RouteSearchV2.NewEnergy</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>封闭类:</dt>
<dd><a href="../../../../../com/amap/api/services/route/RouteSearchV2.html" title="com.amap.api.services.route中的类">RouteSearchV2</a></dd>
</dl>
<hr>
<br>
<pre>public static class <span class="typeNameLabel">RouteSearchV2.NewEnergy</span>
extends java.lang.Object</pre>
<div class="block">新能源参数

     //* @exclude javadoc中不显示</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.NewEnergy.html#NewEnergy--">NewEnergy</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>所有方法</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">实例方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">具体方法</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.NewEnergy.html#buildParam--">buildParam</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.NewEnergy.html#getArrivingPercent--">getArrivingPercent</a></span>()</code>
<div class="block">获取到达充电站(或终点)时允许的最小电量</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/services/route/RouteSearchV2.CustomCostMode.html" title="com.amap.api.services.route中的类">RouteSearchV2.CustomCostMode</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.NewEnergy.html#getCustomCostMode--">getCustomCostMode</a></span>()</code>
<div class="block">获取costmodelswitch+相关参数</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.NewEnergy.html#getKey--">getKey</a></span>()</code>
<div class="block">获取标识厂商信息</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.NewEnergy.html#getLeavingPercent--">getLeavingPercent</a></span>()</code>
<div class="block">获取离开充电站前允许的剩余电量</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.NewEnergy.html#getLoad--">getLoad</a></span>()</code>
<div class="block">获取车重（单位kg）</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.NewEnergy.html#getMaxVehicleCharge--">getMaxVehicleCharge</a></span>()</code>
<div class="block">设置电动汽车的最大电量负荷（浮点型）</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.NewEnergy.html#getVehicleCharge--">getVehicleCharge</a></span>()</code>
<div class="block">获取当前电量（浮点型）</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.NewEnergy.html#setArrivingPercent-float-">setArrivingPercent</a></span>(float&nbsp;arrivingPercent)</code>
<div class="block">设置到达充电站(或终点)时允许的最小电量</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.NewEnergy.html#setCustomCostMode-com.amap.api.services.route.RouteSearchV2.CustomCostMode-">setCustomCostMode</a></span>(<a href="../../../../../com/amap/api/services/route/RouteSearchV2.CustomCostMode.html" title="com.amap.api.services.route中的类">RouteSearchV2.CustomCostMode</a>&nbsp;customCostMode)</code>
<div class="block">设置costmodelswitch+相关参数</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.NewEnergy.html#setKey-java.lang.String-">setKey</a></span>(java.lang.String&nbsp;key)</code>
<div class="block">设置标识厂商信息</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.NewEnergy.html#setLeavingPercent-float-">setLeavingPercent</a></span>(float&nbsp;leavingPercent)</code>
<div class="block">设置离开充电站前允许的剩余电量</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.NewEnergy.html#setLoad-float-">setLoad</a></span>(float&nbsp;load)</code>
<div class="block">设置车重（单位kg）</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.NewEnergy.html#setMaxVehicleCharge-float-">setMaxVehicleCharge</a></span>(float&nbsp;maxVehicleCharge)</code>
<div class="block">设置电动汽车的最大电量负荷（浮点型）</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.NewEnergy.html#setVehicleCharge-float-">setVehicleCharge</a></span>(float&nbsp;vehicleCharge)</code>
<div class="block">设置当前电量（浮点型）</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="NewEnergy--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>NewEnergy</h4>
<pre>public&nbsp;NewEnergy()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="getKey--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getKey</h4>
<pre>public&nbsp;java.lang.String&nbsp;getKey()</pre>
<div class="block">获取标识厂商信息</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>标识厂商信息</dd>
</dl>
</li>
</ul>
<a name="setKey-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setKey</h4>
<pre>public&nbsp;void&nbsp;setKey(java.lang.String&nbsp;key)</pre>
<div class="block">设置标识厂商信息</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>key</code> - 标识厂商信息</dd>
</dl>
</li>
</ul>
<a name="getCustomCostMode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCustomCostMode</h4>
<pre>public&nbsp;<a href="../../../../../com/amap/api/services/route/RouteSearchV2.CustomCostMode.html" title="com.amap.api.services.route中的类">RouteSearchV2.CustomCostMode</a>&nbsp;getCustomCostMode()</pre>
<div class="block">获取costmodelswitch+相关参数</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>costmodelswitch+相关参数</dd>
</dl>
</li>
</ul>
<a name="setCustomCostMode-com.amap.api.services.route.RouteSearchV2.CustomCostMode-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCustomCostMode</h4>
<pre>public&nbsp;void&nbsp;setCustomCostMode(<a href="../../../../../com/amap/api/services/route/RouteSearchV2.CustomCostMode.html" title="com.amap.api.services.route中的类">RouteSearchV2.CustomCostMode</a>&nbsp;customCostMode)</pre>
<div class="block">设置costmodelswitch+相关参数</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>customCostMode</code> - costmodelswitch+相关参数</dd>
</dl>
</li>
</ul>
<a name="getMaxVehicleCharge--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMaxVehicleCharge</h4>
<pre>public&nbsp;float&nbsp;getMaxVehicleCharge()</pre>
<div class="block">设置电动汽车的最大电量负荷（浮点型）</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>电动汽车的最大电量负荷（浮点型）</dd>
</dl>
</li>
</ul>
<a name="setMaxVehicleCharge-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMaxVehicleCharge</h4>
<pre>public&nbsp;void&nbsp;setMaxVehicleCharge(float&nbsp;maxVehicleCharge)</pre>
<div class="block">设置电动汽车的最大电量负荷（浮点型）</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>maxVehicleCharge</code> - 电动汽车的最大电量负荷（浮点型）</dd>
</dl>
</li>
</ul>
<a name="getVehicleCharge--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVehicleCharge</h4>
<pre>public&nbsp;float&nbsp;getVehicleCharge()</pre>
<div class="block">获取当前电量（浮点型）</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>当前电量（浮点型）</dd>
</dl>
</li>
</ul>
<a name="setVehicleCharge-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setVehicleCharge</h4>
<pre>public&nbsp;void&nbsp;setVehicleCharge(float&nbsp;vehicleCharge)</pre>
<div class="block">设置当前电量（浮点型）</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>vehicleCharge</code> - 当前电量（浮点型）</dd>
</dl>
</li>
</ul>
<a name="getLoad--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLoad</h4>
<pre>public&nbsp;float&nbsp;getLoad()</pre>
<div class="block">获取车重（单位kg）</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>车重（单位kg）</dd>
</dl>
</li>
</ul>
<a name="setLoad-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLoad</h4>
<pre>public&nbsp;void&nbsp;setLoad(float&nbsp;load)</pre>
<div class="block">设置车重（单位kg）</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>load</code> - 车重（单位kg）</dd>
</dl>
</li>
</ul>
<a name="getLeavingPercent--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLeavingPercent</h4>
<pre>public&nbsp;float&nbsp;getLeavingPercent()</pre>
<div class="block">获取离开充电站前允许的剩余电量</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>离开充电站前允许的剩余电量</dd>
</dl>
</li>
</ul>
<a name="setLeavingPercent-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLeavingPercent</h4>
<pre>public&nbsp;void&nbsp;setLeavingPercent(float&nbsp;leavingPercent)</pre>
<div class="block">设置离开充电站前允许的剩余电量</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>leavingPercent</code> - 离开充电站前允许的剩余电量</dd>
</dl>
</li>
</ul>
<a name="getArrivingPercent--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getArrivingPercent</h4>
<pre>public&nbsp;float&nbsp;getArrivingPercent()</pre>
<div class="block">获取到达充电站(或终点)时允许的最小电量</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>到达充电站(或终点)时允许的最小电量</dd>
</dl>
</li>
</ul>
<a name="setArrivingPercent-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setArrivingPercent</h4>
<pre>public&nbsp;void&nbsp;setArrivingPercent(float&nbsp;arrivingPercent)</pre>
<div class="block">设置到达充电站(或终点)时允许的最小电量</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>arrivingPercent</code> - </dd>
</dl>
</li>
</ul>
<a name="buildParam--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>buildParam</h4>
<pre>public&nbsp;java.lang.String&nbsp;buildParam()</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/RouteSearchV2.NewEnergy.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/route/RouteSearchV2.FromAndTo.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/amap/api/services/route/RouteSearchV2.OnRoutePlanSearchListener.html" title="com.amap.api.services.route中的接口"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/route/RouteSearchV2.NewEnergy.html" target="_top">框架</a></li>
<li><a href="RouteSearchV2.NewEnergy.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
