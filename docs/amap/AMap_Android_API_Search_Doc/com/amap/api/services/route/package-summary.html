<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>com.amap.api.services.route</title>
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="com.amap.api.services.route";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li class="navBarCell1Rev">程序包</li>
<li>类</li>
<li><a href="package-use.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/road/package-summary.html">上一个程序包</a></li>
<li><a href="../../../../../com/amap/api/services/routepoisearch/package-summary.html">下一个程序包</a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/route/package-summary.html" target="_top">框架</a></li>
<li><a href="package-summary.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 title="程序包" class="title">程序包&nbsp;com.amap.api.services.route</h1>
<div class="docSummary">
<div class="block">
路径查询包，包含了在公交、自驾或步行路线规划中的详细信息。</div>
</div>
<p>请参阅:&nbsp;<a href="#package.description">说明</a></p>
</div>
<div class="contentContainer">
<ul class="blockList">
<li class="blockList">
<table class="typeSummary" border="0" cellpadding="3" cellspacing="0" summary="接口概要表, 列表接口和解释">
<caption><span>接口概要</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">接口</th>
<th class="colLast" scope="col">说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/route/DistanceSearch.OnDistanceSearchListener.html" title="com.amap.api.services.route中的接口">DistanceSearch.OnDistanceSearchListener</a></td>
<td class="colLast">
<div class="block">距离测量异步请求回调。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/route/RouteSearch.OnRoutePlanSearchListener.html" title="com.amap.api.services.route中的接口">RouteSearch.OnRoutePlanSearchListener</a></td>
<td class="colLast">
<div class="block">未来路径规划回调方法</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/route/RouteSearch.OnRouteSearchListener.html" title="com.amap.api.services.route中的接口">RouteSearch.OnRouteSearchListener</a></td>
<td class="colLast">
<div class="block">本类为路径搜索结果的异步处理回调接口。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/route/RouteSearch.OnTruckRouteSearchListener.html" title="com.amap.api.services.route中的接口">RouteSearch.OnTruckRouteSearchListener</a></td>
<td class="colLast">
<div class="block">货车路径规划回调方法</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.OnRoutePlanSearchListener.html" title="com.amap.api.services.route中的接口">RouteSearchV2.OnRoutePlanSearchListener</a></td>
<td class="colLast">
<div class="block">未来路径规划回调方法</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.OnRouteSearchListener.html" title="com.amap.api.services.route中的接口">RouteSearchV2.OnRouteSearchListener</a></td>
<td class="colLast">
<div class="block">本类为路径搜索结果的异步处理回调接口。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.OnTruckRouteSearchListener.html" title="com.amap.api.services.route中的接口">RouteSearchV2.OnTruckRouteSearchListener</a></td>
<td class="colLast">
<div class="block">货车路径规划回调方法</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="typeSummary" border="0" cellpadding="3" cellspacing="0" summary="类概要表, 列表类和解释">
<caption><span>类概要</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">类</th>
<th class="colLast" scope="col">说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/route/BusPath.html" title="com.amap.api.services.route中的类">BusPath</a></td>
<td class="colLast">
<div class="block">定义了公交换乘路径规划的一个方案。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/route/BusPathV2.html" title="com.amap.api.services.route中的类">BusPathV2</a></td>
<td class="colLast">
<div class="block">定义了公交换乘路径规划的一个方案。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/route/BusRouteResult.html" title="com.amap.api.services.route中的类">BusRouteResult</a></td>
<td class="colLast">
<div class="block">定义了公交路径规划的结果集。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/route/BusRouteResultV2.html" title="com.amap.api.services.route中的类">BusRouteResultV2</a></td>
<td class="colLast">
<div class="block">定义了公交路径规划的结果集。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/route/BusStep.html" title="com.amap.api.services.route中的类">BusStep</a></td>
<td class="colLast">
<div class="block">定义了公交路径规划的一个路段。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/route/BusStepV2.html" title="com.amap.api.services.route中的类">BusStepV2</a></td>
<td class="colLast">
<div class="block">定义了公交路径规划的一个路段。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/route/Cost.html" title="com.amap.api.services.route中的类">Cost</a></td>
<td class="colLast">
<div class="block">路线耗时等信息</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/route/DistanceItem.html" title="com.amap.api.services.route中的类">DistanceItem</a></td>
<td class="colLast">
<div class="block">距离测量返回结果中某一个结果的具体信息</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/route/DistanceResult.html" title="com.amap.api.services.route中的类">DistanceResult</a></td>
<td class="colLast">
<div class="block">距离测量返回结果</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/route/DistanceSearch.html" title="com.amap.api.services.route中的类">DistanceSearch</a></td>
<td class="colLast">
<div class="block">距离测量，通过两点计算直线距离或者驾车导航距离，相比路径规划返回内容较少，适合频繁调用场景。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/route/DistanceSearch.DistanceQuery.html" title="com.amap.api.services.route中的类">DistanceSearch.DistanceQuery</a></td>
<td class="colLast">
<div class="block">距离测量属性设置类，其中包含起终点以及测量类型的设置。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/route/District.html" title="com.amap.api.services.route中的类">District</a></td>
<td class="colLast">
<div class="block">此类定义搜索返回行政区的名称和编码。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/route/Doorway.html" title="com.amap.api.services.route中的类">Doorway</a></td>
<td class="colLast">
<div class="block">定义了公交换乘路径规划的一个换乘点的出入口信息。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/route/DrivePath.html" title="com.amap.api.services.route中的类">DrivePath</a></td>
<td class="colLast">
<div class="block">定义了驾车路径规划的一个方案。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/route/DrivePathV2.html" title="com.amap.api.services.route中的类">DrivePathV2</a></td>
<td class="colLast">
<div class="block">定义了驾车路径规划的一个方案。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/route/DrivePlanPath.html" title="com.amap.api.services.route中的类">DrivePlanPath</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/route/DrivePlanStep.html" title="com.amap.api.services.route中的类">DrivePlanStep</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/route/DriveRoutePlanResult.html" title="com.amap.api.services.route中的类">DriveRoutePlanResult</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/route/DriveRouteResult.html" title="com.amap.api.services.route中的类">DriveRouteResult</a></td>
<td class="colLast">
<div class="block">定义了驾车路径规划的结果集。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/route/DriveRouteResultV2.html" title="com.amap.api.services.route中的类">DriveRouteResultV2</a></td>
<td class="colLast">
<div class="block">定义了驾车路径规划的结果集。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/route/DriveStep.html" title="com.amap.api.services.route中的类">DriveStep</a></td>
<td class="colLast">
<div class="block">定义了驾车路径规划的一个路段。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/route/DriveStepV2.html" title="com.amap.api.services.route中的类">DriveStepV2</a></td>
<td class="colLast">
<div class="block">定义了驾车路径规划的一个路段。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/route/Navi.html" title="com.amap.api.services.route中的类">Navi</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/route/NaviWalkType.html" title="com.amap.api.services.route中的类">NaviWalkType</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/route/Path.html" title="com.amap.api.services.route中的类">Path</a></td>
<td class="colLast">
<div class="block">定义了路径规划的一个方案。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/route/Railway.html" title="com.amap.api.services.route中的类">Railway</a></td>
<td class="colLast">
<div class="block">列车信息</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/route/RailwaySpace.html" title="com.amap.api.services.route中的类">RailwaySpace</a></td>
<td class="colLast">
<div class="block">列车的舱位及价格信息
 舱位：动车分一般为一等软座和二等软座</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/route/RailwayStationItem.html" title="com.amap.api.services.route中的类">RailwayStationItem</a></td>
<td class="colLast">
<div class="block">定义了火车站点信息</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/route/RidePath.html" title="com.amap.api.services.route中的类">RidePath</a></td>
<td class="colLast">
<div class="block">定义了骑行路径规划的一个方案。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/route/RideRouteResult.html" title="com.amap.api.services.route中的类">RideRouteResult</a></td>
<td class="colLast">
<div class="block">定义了骑行路径规划的结果集。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/route/RideRouteResultV2.html" title="com.amap.api.services.route中的类">RideRouteResultV2</a></td>
<td class="colLast">
<div class="block">定义了骑行路径规划的结果集。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/route/RideStep.html" title="com.amap.api.services.route中的类">RideStep</a></td>
<td class="colLast">
<div class="block">定义了骑行路径规划的一个路段。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/route/RouteBusLineItem.html" title="com.amap.api.services.route中的类">RouteBusLineItem</a></td>
<td class="colLast">
<div class="block">定义了公交换乘路径规划的一个换乘段的公交信息。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/route/RouteBusWalkItem.html" title="com.amap.api.services.route中的类">RouteBusWalkItem</a></td>
<td class="colLast">
<div class="block">定义了公交换乘路径规划的一个换乘段的步行信息。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/route/RoutePlanResult.html" title="com.amap.api.services.route中的类">RoutePlanResult</a></td>
<td class="colLast">
<div class="block">定义了路径规划的结果集。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/route/RouteRailwayItem.html" title="com.amap.api.services.route中的类">RouteRailwayItem</a></td>
<td class="colLast">
<div class="block">火车乘坐信息</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/route/RouteResult.html" title="com.amap.api.services.route中的类">RouteResult</a></td>
<td class="colLast">
<div class="block">定义了路径规划的结果集。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></td>
<td class="colLast">
<div class="block">该类路径规划搜索的入口，定义此类开始路径规划搜索</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/route/RouteSearch.BusRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearch.BusRouteQuery</a></td>
<td class="colLast">
<div class="block">此类定义了公交路径规划查询路径的起终点、计算路径的模式、城市和是否计算夜班车。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/route/RouteSearch.DrivePlanQuery.html" title="com.amap.api.services.route中的类">RouteSearch.DrivePlanQuery</a></td>
<td class="colLast">
<div class="block">此类定义了驾车未来路径查询规划,最大支持未来七天的规划。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/route/RouteSearch.DriveRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearch.DriveRouteQuery</a></td>
<td class="colLast">
<div class="block">此类定义了驾车路径查询规划。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/route/RouteSearch.FromAndTo.html" title="com.amap.api.services.route中的类">RouteSearch.FromAndTo</a></td>
<td class="colLast">
<div class="block">构造路径规划的起点和终点坐标。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/route/RouteSearch.RideRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearch.RideRouteQuery</a></td>
<td class="colLast">
<div class="block">此类定义了骑行路径的起终点和计算路径的模式。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/route/RouteSearch.TruckRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearch.TruckRouteQuery</a></td>
<td class="colLast">
<div class="block">此类定义了货车路径的起终点和计算路径的模式。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/route/RouteSearch.WalkRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearch.WalkRouteQuery</a></td>
<td class="colLast">
<div class="block">此类定义了步行路径的起终点和计算路径的模式。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/route/RouteSearchCity.html" title="com.amap.api.services.route中的类">RouteSearchCity</a></td>
<td class="colLast">
<div class="block">此类定义路径规划返回的城市名称、编码和行政区的名称和编码。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.html" title="com.amap.api.services.route中的类">RouteSearchV2</a></td>
<td class="colLast">
<div class="block">该类路径规划搜索的入口，定义此类开始路径规划搜索</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.BusMode.html" title="com.amap.api.services.route中的类">RouteSearchV2.BusMode</a></td>
<td class="colLast">
<div class="block">公交线路模式</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.BusRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearchV2.BusRouteQuery</a></td>
<td class="colLast">
<div class="block">此类定义了公交路径规划查询路径的起终点、计算路径的模式、城市和是否计算夜班车。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.DriveRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearchV2.DriveRouteQuery</a></td>
<td class="colLast">
<div class="block">此类定义了驾车路径查询规划。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.FromAndTo.html" title="com.amap.api.services.route中的类">RouteSearchV2.FromAndTo</a></td>
<td class="colLast">
<div class="block">构造路径规划的起点和终点坐标。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.RideRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearchV2.RideRouteQuery</a></td>
<td class="colLast">
<div class="block">此类定义了骑行路径的起终点和计算路径的模式。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.ShowFields.html" title="com.amap.api.services.route中的类">RouteSearchV2.ShowFields</a></td>
<td class="colLast">
<div class="block">扩展字段</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.WalkRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearchV2.WalkRouteQuery</a></td>
<td class="colLast">
<div class="block">此类定义了步行路径的起终点和计算路径的模式。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/route/SearchCity.html" title="com.amap.api.services.route中的类">SearchCity</a></td>
<td class="colLast">
<div class="block">此类定义搜索返回城市的名称和编码。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/route/TaxiItem.html" title="com.amap.api.services.route中的类">TaxiItem</a></td>
<td class="colLast">
<div class="block">此类定义了打车路段信息</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/route/TaxiItemV2.html" title="com.amap.api.services.route中的类">TaxiItemV2</a></td>
<td class="colLast">
<div class="block">此类定义了打车路段信息</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/route/TimeInfo.html" title="com.amap.api.services.route中的类">TimeInfo</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/route/TimeInfosElement.html" title="com.amap.api.services.route中的类">TimeInfosElement</a></td>
<td class="colLast">
<div class="block">对应的路线</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/route/TMC.html" title="com.amap.api.services.route中的类">TMC</a></td>
<td class="colLast">
<div class="block">交通信息类</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/route/TruckPath.html" title="com.amap.api.services.route中的类">TruckPath</a></td>
<td class="colLast">
<div class="block">定义了货车路径规划的一个方案。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/route/TruckRouteRestult.html" title="com.amap.api.services.route中的类">TruckRouteRestult</a></td>
<td class="colLast">
<div class="block">定义了货车路径规划的结果集。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/route/TruckStep.html" title="com.amap.api.services.route中的类">TruckStep</a></td>
<td class="colLast">
<div class="block">定义了货车路径规划的一个路段。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/route/WalkPath.html" title="com.amap.api.services.route中的类">WalkPath</a></td>
<td class="colLast">
<div class="block">定义了步行路径规划的一个方案。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/route/WalkRouteResult.html" title="com.amap.api.services.route中的类">WalkRouteResult</a></td>
<td class="colLast">
<div class="block">定义了步行路径规划的结果集。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/route/WalkRouteResultV2.html" title="com.amap.api.services.route中的类">WalkRouteResultV2</a></td>
<td class="colLast">
<div class="block">定义了步行路径规划的结果集。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/route/WalkStep.html" title="com.amap.api.services.route中的类">WalkStep</a></td>
<td class="colLast">
<div class="block">定义了步行路径规划的一个路段。</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="typeSummary" border="0" cellpadding="3" cellspacing="0" summary="枚举概要表, 列表枚举和解释">
<caption><span>枚举概要</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">枚举</th>
<th class="colLast" scope="col">说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.DrivingStrategy.html" title="com.amap.api.services.route中的枚举">RouteSearchV2.DrivingStrategy</a></td>
<td class="colLast">
<div class="block">驾车策略</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
<a name="package.description">
<!--   -->
</a>
<h2 title="程序包com.amap.api.services.route的说明">程序包com.amap.api.services.route的说明</h2>
<div class="block"><p>
路径查询包，包含了在公交、自驾或步行路线规划中的详细信息。
</p></div>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li class="navBarCell1Rev">程序包</li>
<li>类</li>
<li><a href="package-use.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/road/package-summary.html">上一个程序包</a></li>
<li><a href="../../../../../com/amap/api/services/routepoisearch/package-summary.html">下一个程序包</a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/route/package-summary.html" target="_top">框架</a></li>
<li><a href="package-summary.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
