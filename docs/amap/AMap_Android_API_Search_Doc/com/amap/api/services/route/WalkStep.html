<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>WalkStep</title>
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="WalkStep";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10};
var tabs = {65535:["t0","所有方法"],2:["t2","实例方法"],8:["t4","具体方法"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/WalkStep.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/route/WalkRouteResultV2.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">上一个类</span></a></li>
<li>下一个类</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/route/WalkStep.html" target="_top">框架</a></li>
<li><a href="WalkStep.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.amap.api.services.route</div>
<h2 title="类 WalkStep" class="title">类 WalkStep</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.amap.api.services.route.WalkStep</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">WalkStep</span>
extends java.lang.Object</pre>
<div class="block">定义了步行路径规划的一个路段。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>所有方法</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">实例方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">具体方法</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/WalkStep.html#getAction--">getAction</a></span>()</code>
<div class="block">返回步行路段的导航主要操作。</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/WalkStep.html#getAssistantAction--">getAssistantAction</a></span>()</code>
<div class="block">返回步行路段的导航辅助操作。</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/WalkStep.html#getDistance--">getDistance</a></span>()</code>
<div class="block">返回步行路段的距离，单位米。</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/WalkStep.html#getDuration--">getDuration</a></span>()</code>
<div class="block">返回步行路段的预计时间，单位为秒。</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/WalkStep.html#getInstruction--">getInstruction</a></span>()</code>
<div class="block">返回步行路段的行进指示。</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/WalkStep.html#getOrientation--">getOrientation</a></span>()</code>
<div class="block">返回步行路段的行进方向，方向为中文名称，如东、西南等。</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/WalkStep.html#getPolyline--">getPolyline</a></span>()</code>
<div class="block">返回步行路段的坐标点集合。</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/WalkStep.html#getRoad--">getRoad</a></span>()</code>
<div class="block">返回步行路段的道路名称。</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/WalkStep.html#getRoadType--">getRoadType</a></span>()</code>
<div class="block">0，普通道路 1，人行横道 3，地下通道 4，过街天桥

 5，地铁通道 6，公园 7，广场 8，扶梯 9，直梯

 10，索道 11，空中通道 12，建筑物穿越通道

 13，行人通道 14，游船路线 15，观光车路线 16，滑道

 18，扩路 19，道路附属连接线 20，阶梯 21，斜坡

 22，桥 23，隧道 30，轮渡</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/WalkStep.html#setRoadType-int-">setRoadType</a></span>(int&nbsp;mRoadType)</code>
<div class="block">0，普通道路 1，人行横道 3，地下通道 4，过街天桥

 5，地铁通道 6，公园 7，广场 8，扶梯 9，直梯

 10，索道 11，空中通道 12，建筑物穿越通道

 13，行人通道 14，游船路线 15，观光车路线 16，滑道

 18，扩路 19，道路附属连接线 20，阶梯 21，斜坡

 22，桥 23，隧道 30，轮渡</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="getInstruction--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getInstruction</h4>
<pre>public&nbsp;java.lang.String&nbsp;getInstruction()</pre>
<div class="block">返回步行路段的行进指示。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>步行路段的行进指示。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="getOrientation--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getOrientation</h4>
<pre>public&nbsp;java.lang.String&nbsp;getOrientation()</pre>
<div class="block">返回步行路段的行进方向，方向为中文名称，如东、西南等。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>步行路段的行进方向。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="getRoad--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRoad</h4>
<pre>public&nbsp;java.lang.String&nbsp;getRoad()</pre>
<div class="block">返回步行路段的道路名称。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>步行路段的道路名称。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="getDistance--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDistance</h4>
<pre>public&nbsp;float&nbsp;getDistance()</pre>
<div class="block">返回步行路段的距离，单位米。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>步行路段的距离。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="getDuration--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDuration</h4>
<pre>public&nbsp;float&nbsp;getDuration()</pre>
<div class="block">返回步行路段的预计时间，单位为秒。时间根据当时路况估算。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>步行路段的预计时间。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="getPolyline--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPolyline</h4>
<pre>public&nbsp;java.util.List&lt;<a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&gt;&nbsp;getPolyline()</pre>
<div class="block">返回步行路段的坐标点集合。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>步行路段的坐标点集合。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="getAction--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAction</h4>
<pre>public&nbsp;java.lang.String&nbsp;getAction()</pre>
<div class="block">返回步行路段的导航主要操作。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>步行路段的导航主要操作。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="getAssistantAction--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAssistantAction</h4>
<pre>public&nbsp;java.lang.String&nbsp;getAssistantAction()</pre>
<div class="block">返回步行路段的导航辅助操作。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>步行路段的导航辅助操作。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="getRoadType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRoadType</h4>
<pre>public&nbsp;int&nbsp;getRoadType()</pre>
<div class="block">0，普通道路 1，人行横道 3，地下通道 4，过街天桥

 5，地铁通道 6，公园 7，广场 8，扶梯 9，直梯

 10，索道 11，空中通道 12，建筑物穿越通道

 13，行人通道 14，游船路线 15，观光车路线 16，滑道

 18，扩路 19，道路附属连接线 20，阶梯 21，斜坡

 22，桥 23，隧道 30，轮渡</div>
</li>
</ul>
<a name="setRoadType-int-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setRoadType</h4>
<pre>public&nbsp;void&nbsp;setRoadType(int&nbsp;mRoadType)</pre>
<div class="block">0，普通道路 1，人行横道 3，地下通道 4，过街天桥

 5，地铁通道 6，公园 7，广场 8，扶梯 9，直梯

 10，索道 11，空中通道 12，建筑物穿越通道

 13，行人通道 14，游船路线 15，观光车路线 16，滑道

 18，扩路 19，道路附属连接线 20，阶梯 21，斜坡

 22，桥 23，隧道 30，轮渡</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/WalkStep.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/route/WalkRouteResultV2.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">上一个类</span></a></li>
<li>下一个类</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/route/WalkStep.html" target="_top">框架</a></li>
<li><a href="WalkStep.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
