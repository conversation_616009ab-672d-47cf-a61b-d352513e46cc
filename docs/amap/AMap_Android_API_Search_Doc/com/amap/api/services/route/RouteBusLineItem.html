<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>RouteBusLineItem</title>
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="RouteBusLineItem";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10};
var tabs = {65535:["t0","所有方法"],2:["t2","实例方法"],8:["t4","具体方法"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/RouteBusLineItem.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/route/RideStep.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/amap/api/services/route/RouteBusWalkItem.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/route/RouteBusLineItem.html" target="_top">框架</a></li>
<li><a href="RouteBusLineItem.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.amap.api.services.route</div>
<h2 title="类 RouteBusLineItem" class="title">类 RouteBusLineItem</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../../../com/amap/api/services/busline/BusLineItem.html" title="com.amap.api.services.busline中的类">com.amap.api.services.busline.BusLineItem</a></li>
<li>
<ul class="inheritance">
<li>com.amap.api.services.route.RouteBusLineItem</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">RouteBusLineItem</span>
extends <a href="../../../../../com/amap/api/services/busline/BusLineItem.html" title="com.amap.api.services.busline中的类">BusLineItem</a></pre>
<div class="block">定义了公交换乘路径规划的一个换乘段的公交信息。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>所有方法</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">实例方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">具体方法</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteBusLineItem.html#equals-java.lang.Object-">equals</a></span>(java.lang.Object&nbsp;obj)</code>
<div class="block">比较两个公交换乘规划是否相同。</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/services/busline/BusStationItem.html" title="com.amap.api.services.busline中的类">BusStationItem</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteBusLineItem.html#getArrivalBusStation--">getArrivalBusStation</a></span>()</code>
<div class="block">返回此公交换乘路段的到达站。</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/services/busline/BusStationItem.html" title="com.amap.api.services.busline中的类">BusStationItem</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteBusLineItem.html#getDepartureBusStation--">getDepartureBusStation</a></span>()</code>
<div class="block">返回此公交换乘路段的出发站。</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteBusLineItem.html#getDuration--">getDuration</a></span>()</code>
<div class="block">返回此公交换乘路段公交预计行驶时间 。</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteBusLineItem.html#getPassStationNum--">getPassStationNum</a></span>()</code>
<div class="block">返回此公交换乘路段经过的站点数目（除出发站、到达站）。</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../../com/amap/api/services/busline/BusStationItem.html" title="com.amap.api.services.busline中的类">BusStationItem</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteBusLineItem.html#getPassStations--">getPassStations</a></span>()</code>
<div class="block">返回此公交换乘路段经过的站点名称 。</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteBusLineItem.html#getPolyline--">getPolyline</a></span>()</code>
<div class="block">返回此公交换乘路段（出发站-到达站）的坐标点集合。</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.amap.api.services.busline.BusLineItem">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;com.amap.api.services.busline.<a href="../../../../../com/amap/api/services/busline/BusLineItem.html" title="com.amap.api.services.busline中的类">BusLineItem</a></h3>
<code><a href="../../../../../com/amap/api/services/busline/BusLineItem.html#getBasicPrice--">getBasicPrice</a>, <a href="../../../../../com/amap/api/services/busline/BusLineItem.html#getBounds--">getBounds</a>, <a href="../../../../../com/amap/api/services/busline/BusLineItem.html#getBusCompany--">getBusCompany</a>, <a href="../../../../../com/amap/api/services/busline/BusLineItem.html#getBusLineId--">getBusLineId</a>, <a href="../../../../../com/amap/api/services/busline/BusLineItem.html#getBusLineName--">getBusLineName</a>, <a href="../../../../../com/amap/api/services/busline/BusLineItem.html#getBusLineType--">getBusLineType</a>, <a href="../../../../../com/amap/api/services/busline/BusLineItem.html#getBusStations--">getBusStations</a>, <a href="../../../../../com/amap/api/services/busline/BusLineItem.html#getCityCode--">getCityCode</a>, <a href="../../../../../com/amap/api/services/busline/BusLineItem.html#getDirectionsCoordinates--">getDirectionsCoordinates</a>, <a href="../../../../../com/amap/api/services/busline/BusLineItem.html#getDistance--">getDistance</a>, <a href="../../../../../com/amap/api/services/busline/BusLineItem.html#getFirstBusTime--">getFirstBusTime</a>, <a href="../../../../../com/amap/api/services/busline/BusLineItem.html#getLastBusTime--">getLastBusTime</a>, <a href="../../../../../com/amap/api/services/busline/BusLineItem.html#getOriginatingStation--">getOriginatingStation</a>, <a href="../../../../../com/amap/api/services/busline/BusLineItem.html#getTerminalStation--">getTerminalStation</a>, <a href="../../../../../com/amap/api/services/busline/BusLineItem.html#getTotalPrice--">getTotalPrice</a>, <a href="../../../../../com/amap/api/services/busline/BusLineItem.html#toString--">toString</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, finalize, getClass, hashCode, notify, notifyAll, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="getDepartureBusStation--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDepartureBusStation</h4>
<pre>public&nbsp;<a href="../../../../../com/amap/api/services/busline/BusStationItem.html" title="com.amap.api.services.busline中的类">BusStationItem</a>&nbsp;getDepartureBusStation()</pre>
<div class="block">返回此公交换乘路段的出发站。此站有可能不是公交线路的始发车站。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>此公交换乘路段的出发站。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="getArrivalBusStation--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getArrivalBusStation</h4>
<pre>public&nbsp;<a href="../../../../../com/amap/api/services/busline/BusStationItem.html" title="com.amap.api.services.busline中的类">BusStationItem</a>&nbsp;getArrivalBusStation()</pre>
<div class="block">返回此公交换乘路段的到达站。此站有可能不是公交线路的终点站。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>此公交换乘路段的到达站。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="getPolyline--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPolyline</h4>
<pre>public&nbsp;java.util.List&lt;<a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&gt;&nbsp;getPolyline()</pre>
<div class="block">返回此公交换乘路段（出发站-到达站）的坐标点集合。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>此公交换乘路段（出发站-到达站）的坐标点集合。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="getPassStationNum--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPassStationNum</h4>
<pre>public&nbsp;int&nbsp;getPassStationNum()</pre>
<div class="block">返回此公交换乘路段经过的站点数目（除出发站、到达站）。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>此公交换乘路段经过的站点数目（除出发站、到达站）。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="getPassStations--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPassStations</h4>
<pre>public&nbsp;java.util.List&lt;<a href="../../../../../com/amap/api/services/busline/BusStationItem.html" title="com.amap.api.services.busline中的类">BusStationItem</a>&gt;&nbsp;getPassStations()</pre>
<div class="block">返回此公交换乘路段经过的站点名称 。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>此公交换乘路段经过的站点名称 。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="getDuration--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDuration</h4>
<pre>public&nbsp;float&nbsp;getDuration()</pre>
<div class="block">返回此公交换乘路段公交预计行驶时间 。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>此公交换乘路段公交预计行驶时间 。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="equals-java.lang.Object-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>equals</h4>
<pre>public&nbsp;boolean&nbsp;equals(java.lang.Object&nbsp;obj)</pre>
<div class="block">比较两个公交换乘规划是否相同。</div>
<dl>
<dt><span class="overrideSpecifyLabel">覆盖:</span></dt>
<dd><code><a href="../../../../../com/amap/api/services/busline/BusLineItem.html#equals-java.lang.Object-">equals</a></code>&nbsp;在类中&nbsp;<code><a href="../../../../../com/amap/api/services/busline/BusLineItem.html" title="com.amap.api.services.busline中的类">BusLineItem</a></code></dd>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>obj</code> - 待比较的对象。</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>公交换乘规划是否相同。</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/RouteBusLineItem.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/route/RideStep.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/amap/api/services/route/RouteBusWalkItem.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/route/RouteBusLineItem.html" target="_top">框架</a></li>
<li><a href="RouteBusLineItem.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
