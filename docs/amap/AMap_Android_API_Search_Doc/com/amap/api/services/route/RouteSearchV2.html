<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>RouteSearchV2</title>
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="RouteSearchV2";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10};
var tabs = {65535:["t0","所有方法"],2:["t2","实例方法"],8:["t4","具体方法"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/RouteSearchV2.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/route/RouteSearchCity.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/amap/api/services/route/RouteSearchV2.AlternativeRoute.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/route/RouteSearchV2.html" target="_top">框架</a></li>
<li><a href="RouteSearchV2.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li><a href="#nested.class.summary">嵌套</a>&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.amap.api.services.route</div>
<h2 title="类 RouteSearchV2" class="title">类 RouteSearchV2</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.amap.api.services.route.RouteSearchV2</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">RouteSearchV2</span>
extends java.lang.Object</pre>
<div class="block">该类路径规划搜索的入口，定义此类开始路径规划搜索</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.2.0</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>嵌套类概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="嵌套类概要表, 列表嵌套类和解释">
<caption><span>嵌套类</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">类和说明</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.AlternativeRoute.html" title="com.amap.api.services.route中的类">RouteSearchV2.AlternativeRoute</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.BusMode.html" title="com.amap.api.services.route中的类">RouteSearchV2.BusMode</a></span></code>
<div class="block">公交线路模式</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.BusRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearchV2.BusRouteQuery</a></span></code>
<div class="block">此类定义了公交路径规划查询路径的起终点、计算路径的模式、城市和是否计算夜班车。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.DriveRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearchV2.DriveRouteQuery</a></span></code>
<div class="block">此类定义了驾车路径查询规划。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.DrivingStrategy.html" title="com.amap.api.services.route中的枚举">RouteSearchV2.DrivingStrategy</a></span></code>
<div class="block">驾车策略</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.FromAndTo.html" title="com.amap.api.services.route中的类">RouteSearchV2.FromAndTo</a></span></code>
<div class="block">构造路径规划的起点和终点坐标。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static interface&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.OnRoutePlanSearchListener.html" title="com.amap.api.services.route中的接口">RouteSearchV2.OnRoutePlanSearchListener</a></span></code>
<div class="block">未来路径规划回调方法</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static interface&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.OnRouteSearchListener.html" title="com.amap.api.services.route中的接口">RouteSearchV2.OnRouteSearchListener</a></span></code>
<div class="block">本类为路径搜索结果的异步处理回调接口。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static interface&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.OnTruckRouteSearchListener.html" title="com.amap.api.services.route中的接口">RouteSearchV2.OnTruckRouteSearchListener</a></span></code>
<div class="block">货车路径规划回调方法</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.RideRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearchV2.RideRouteQuery</a></span></code>
<div class="block">此类定义了骑行路径的起终点和计算路径的模式。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.ShowFields.html" title="com.amap.api.services.route中的类">RouteSearchV2.ShowFields</a></span></code>
<div class="block">扩展字段</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.WalkRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearchV2.WalkRouteQuery</a></span></code>
<div class="block">此类定义了步行路径的起终点和计算路径的模式。</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.html#RouteSearchV2-Context-">RouteSearchV2</a></span>(Context&nbsp;context)</code>
<div class="block">根据给定的参数来构造一个RouteSearch的新对象。</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>所有方法</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">实例方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">具体方法</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/services/route/BusRouteResultV2.html" title="com.amap.api.services.route中的类">BusRouteResultV2</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.html#calculateBusRoute-com.amap.api.services.route.RouteSearchV2.BusRouteQuery-">calculateBusRoute</a></span>(<a href="../../../../../com/amap/api/services/route/RouteSearchV2.BusRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearchV2.BusRouteQuery</a>&nbsp;busQuery)</code>
<div class="block">根据指定的参数来计算公交路径。</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.html#calculateBusRouteAsyn-com.amap.api.services.route.RouteSearchV2.BusRouteQuery-">calculateBusRouteAsyn</a></span>(<a href="../../../../../com/amap/api/services/route/RouteSearchV2.BusRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearchV2.BusRouteQuery</a>&nbsp;busQuery)</code>
<div class="block">异步处理。</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/services/route/DriveRouteResultV2.html" title="com.amap.api.services.route中的类">DriveRouteResultV2</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.html#calculateDriveRoute-com.amap.api.services.route.RouteSearchV2.DriveRouteQuery-">calculateDriveRoute</a></span>(<a href="../../../../../com/amap/api/services/route/RouteSearchV2.DriveRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearchV2.DriveRouteQuery</a>&nbsp;driveQuery)</code>
<div class="block">根据指定的参数来计算驾车路径。</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.html#calculateDriveRouteAsyn-com.amap.api.services.route.RouteSearchV2.DriveRouteQuery-">calculateDriveRouteAsyn</a></span>(<a href="../../../../../com/amap/api/services/route/RouteSearchV2.DriveRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearchV2.DriveRouteQuery</a>&nbsp;driveQuery)</code>
<div class="block">异步处理。</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/services/route/RideRouteResultV2.html" title="com.amap.api.services.route中的类">RideRouteResultV2</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.html#calculateRideRoute-com.amap.api.services.route.RouteSearchV2.RideRouteQuery-">calculateRideRoute</a></span>(<a href="../../../../../com/amap/api/services/route/RouteSearchV2.RideRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearchV2.RideRouteQuery</a>&nbsp;rideQuery)</code>
<div class="block">根据指定的参数来计算骑行路径，同步接口。</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.html#calculateRideRouteAsyn-com.amap.api.services.route.RouteSearchV2.RideRouteQuery-">calculateRideRouteAsyn</a></span>(<a href="../../../../../com/amap/api/services/route/RouteSearchV2.RideRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearchV2.RideRouteQuery</a>&nbsp;rideQuery)</code>
<div class="block">异步处理。</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/services/route/WalkRouteResultV2.html" title="com.amap.api.services.route中的类">WalkRouteResultV2</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.html#calculateWalkRoute-com.amap.api.services.route.RouteSearchV2.WalkRouteQuery-">calculateWalkRoute</a></span>(<a href="../../../../../com/amap/api/services/route/RouteSearchV2.WalkRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearchV2.WalkRouteQuery</a>&nbsp;walkQuery)</code>
<div class="block">根据指定的参数来计算步行路径。</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.html#calculateWalkRouteAsyn-com.amap.api.services.route.RouteSearchV2.WalkRouteQuery-">calculateWalkRouteAsyn</a></span>(<a href="../../../../../com/amap/api/services/route/RouteSearchV2.WalkRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearchV2.WalkRouteQuery</a>&nbsp;walkQuery)</code>
<div class="block">异步处理。</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.html#setRouteSearchListener-com.amap.api.services.route.RouteSearchV2.OnRouteSearchListener-">setRouteSearchListener</a></span>(<a href="../../../../../com/amap/api/services/route/RouteSearchV2.OnRouteSearchListener.html" title="com.amap.api.services.route中的接口">RouteSearchV2.OnRouteSearchListener</a>&nbsp;listener)</code>
<div class="block">路径搜索结果监听接口设置。</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="RouteSearchV2-Context-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>RouteSearchV2</h4>
<pre>public&nbsp;RouteSearchV2(Context&nbsp;context)
              throws <a href="../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></pre>
<div class="block">根据给定的参数来构造一个RouteSearch的新对象。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>context</code> - 对应的Context。</dd>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code><a href="../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></code></dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.2.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="setRouteSearchListener-com.amap.api.services.route.RouteSearchV2.OnRouteSearchListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRouteSearchListener</h4>
<pre>public&nbsp;void&nbsp;setRouteSearchListener(<a href="../../../../../com/amap/api/services/route/RouteSearchV2.OnRouteSearchListener.html" title="com.amap.api.services.route中的接口">RouteSearchV2.OnRouteSearchListener</a>&nbsp;listener)</pre>
<div class="block">路径搜索结果监听接口设置。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>listener</code> - 路径搜索结果监听接口。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.2.0</dd>
</dl>
</li>
</ul>
<a name="calculateDriveRoute-com.amap.api.services.route.RouteSearchV2.DriveRouteQuery-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>calculateDriveRoute</h4>
<pre>public&nbsp;<a href="../../../../../com/amap/api/services/route/DriveRouteResultV2.html" title="com.amap.api.services.route中的类">DriveRouteResultV2</a>&nbsp;calculateDriveRoute(<a href="../../../../../com/amap/api/services/route/RouteSearchV2.DriveRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearchV2.DriveRouteQuery</a>&nbsp;driveQuery)
                                       throws <a href="../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></pre>
<div class="block">根据指定的参数来计算驾车路径。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>driveQuery</code> - 查询参数。</dd>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code><a href="../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></code> - 途经点太多或避让区域太大会返回非法参数</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.2.0</dd>
</dl>
</li>
</ul>
<a name="calculateDriveRouteAsyn-com.amap.api.services.route.RouteSearchV2.DriveRouteQuery-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>calculateDriveRouteAsyn</h4>
<pre>public&nbsp;void&nbsp;calculateDriveRouteAsyn(<a href="../../../../../com/amap/api/services/route/RouteSearchV2.DriveRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearchV2.DriveRouteQuery</a>&nbsp;driveQuery)</pre>
<div class="block">异步处理。根据指定的参数来计算驾车路径的异步处理。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>driveQuery</code> - 查询参数。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.2.0</dd>
</dl>
</li>
</ul>
<a name="calculateWalkRoute-com.amap.api.services.route.RouteSearchV2.WalkRouteQuery-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>calculateWalkRoute</h4>
<pre>public&nbsp;<a href="../../../../../com/amap/api/services/route/WalkRouteResultV2.html" title="com.amap.api.services.route中的类">WalkRouteResultV2</a>&nbsp;calculateWalkRoute(<a href="../../../../../com/amap/api/services/route/RouteSearchV2.WalkRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearchV2.WalkRouteQuery</a>&nbsp;walkQuery)
                                     throws <a href="../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></pre>
<div class="block">根据指定的参数来计算步行路径。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>walkQuery</code> - 查询参数。</dd>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code><a href="../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></code></dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.4.0</dd>
</dl>
</li>
</ul>
<a name="calculateWalkRouteAsyn-com.amap.api.services.route.RouteSearchV2.WalkRouteQuery-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>calculateWalkRouteAsyn</h4>
<pre>public&nbsp;void&nbsp;calculateWalkRouteAsyn(<a href="../../../../../com/amap/api/services/route/RouteSearchV2.WalkRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearchV2.WalkRouteQuery</a>&nbsp;walkQuery)</pre>
<div class="block">异步处理。根据指定的参数来计算步行路径的异步处理。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>walkQuery</code> - 查询参数。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.4.0</dd>
</dl>
</li>
</ul>
<a name="calculateRideRouteAsyn-com.amap.api.services.route.RouteSearchV2.RideRouteQuery-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>calculateRideRouteAsyn</h4>
<pre>public&nbsp;void&nbsp;calculateRideRouteAsyn(<a href="../../../../../com/amap/api/services/route/RouteSearchV2.RideRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearchV2.RideRouteQuery</a>&nbsp;rideQuery)</pre>
<div class="block">异步处理。根据指定的参数来计算骑行路径的异步处理。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>rideQuery</code> - 查询参数。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.4.0</dd>
</dl>
</li>
</ul>
<a name="calculateRideRoute-com.amap.api.services.route.RouteSearchV2.RideRouteQuery-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>calculateRideRoute</h4>
<pre>public&nbsp;<a href="../../../../../com/amap/api/services/route/RideRouteResultV2.html" title="com.amap.api.services.route中的类">RideRouteResultV2</a>&nbsp;calculateRideRoute(<a href="../../../../../com/amap/api/services/route/RouteSearchV2.RideRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearchV2.RideRouteQuery</a>&nbsp;rideQuery)
                                     throws <a href="../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></pre>
<div class="block">根据指定的参数来计算骑行路径，同步接口。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>rideQuery</code> - 查询参数。</dd>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code><a href="../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></code></dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.4.0</dd>
</dl>
</li>
</ul>
<a name="calculateBusRoute-com.amap.api.services.route.RouteSearchV2.BusRouteQuery-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>calculateBusRoute</h4>
<pre>public&nbsp;<a href="../../../../../com/amap/api/services/route/BusRouteResultV2.html" title="com.amap.api.services.route中的类">BusRouteResultV2</a>&nbsp;calculateBusRoute(<a href="../../../../../com/amap/api/services/route/RouteSearchV2.BusRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearchV2.BusRouteQuery</a>&nbsp;busQuery)
                                   throws <a href="../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></pre>
<div class="block">根据指定的参数来计算公交路径。只支持市内公交换乘。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>busQuery</code> - 查询参数。</dd>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code><a href="../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></code></dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.4.0</dd>
</dl>
</li>
</ul>
<a name="calculateBusRouteAsyn-com.amap.api.services.route.RouteSearchV2.BusRouteQuery-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>calculateBusRouteAsyn</h4>
<pre>public&nbsp;void&nbsp;calculateBusRouteAsyn(<a href="../../../../../com/amap/api/services/route/RouteSearchV2.BusRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearchV2.BusRouteQuery</a>&nbsp;busQuery)</pre>
<div class="block">异步处理。根据指定的参数来计算公交换乘路径的异步处理。只支持市内公交换乘。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>busQuery</code> - 查询参数。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.4.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/RouteSearchV2.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/route/RouteSearchCity.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/amap/api/services/route/RouteSearchV2.AlternativeRoute.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/route/RouteSearchV2.html" target="_top">框架</a></li>
<li><a href="RouteSearchV2.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li><a href="#nested.class.summary">嵌套</a>&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
