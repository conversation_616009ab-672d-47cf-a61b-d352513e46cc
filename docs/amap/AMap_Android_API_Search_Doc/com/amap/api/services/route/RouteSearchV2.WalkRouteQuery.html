<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>RouteSearchV2.WalkRouteQuery</title>
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="RouteSearchV2.WalkRouteQuery";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10};
var tabs = {65535:["t0","所有方法"],2:["t2","实例方法"],8:["t4","具体方法"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/RouteSearchV2.WalkRouteQuery.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/route/RouteSearchV2.ShowFields.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/amap/api/services/route/SearchCity.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/route/RouteSearchV2.WalkRouteQuery.html" target="_top">框架</a></li>
<li><a href="RouteSearchV2.WalkRouteQuery.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.amap.api.services.route</div>
<h2 title="类 RouteSearchV2.WalkRouteQuery" class="title">类 RouteSearchV2.WalkRouteQuery</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.amap.api.services.route.RouteSearchV2.WalkRouteQuery</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>所有已实现的接口:</dt>
<dd>java.lang.Cloneable</dd>
</dl>
<dl>
<dt>封闭类:</dt>
<dd><a href="../../../../../com/amap/api/services/route/RouteSearchV2.html" title="com.amap.api.services.route中的类">RouteSearchV2</a></dd>
</dl>
<hr>
<br>
<pre>public static class <span class="typeNameLabel">RouteSearchV2.WalkRouteQuery</span>
extends java.lang.Object
implements java.lang.Cloneable</pre>
<div class="block">此类定义了步行路径的起终点和计算路径的模式。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.4.0</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.WalkRouteQuery.html#WalkRouteQuery-Parcel-">WalkRouteQuery</a></span>(Parcel&nbsp;source)</code>
<div class="block">序列化实现。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.WalkRouteQuery.html#WalkRouteQuery-com.amap.api.services.route.RouteSearchV2.FromAndTo-">WalkRouteQuery</a></span>(<a href="../../../../../com/amap/api/services/route/RouteSearchV2.FromAndTo.html" title="com.amap.api.services.route中的类">RouteSearchV2.FromAndTo</a>&nbsp;fromAndTo)</code>
<div class="block">WalkRouteQuery构造函数。</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>所有方法</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">实例方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">具体方法</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.WalkRouteQuery.html#equals-java.lang.Object-">equals</a></span>(java.lang.Object&nbsp;o)</code>
<div class="block">比较两个查询条件是否相同。</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.WalkRouteQuery.html#getAlternativeRoute--">getAlternativeRoute</a></span>()</code>
<div class="block">返回路线条数</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/services/route/RouteSearchV2.FromAndTo.html" title="com.amap.api.services.route中的类">RouteSearchV2.FromAndTo</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.WalkRouteQuery.html#getFromAndTo--">getFromAndTo</a></span>()</code>
<div class="block">返回查询路径的起终点。</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.WalkRouteQuery.html#getShowFields--">getShowFields</a></span>()</code>
<div class="block">扩展字段 <a href="../../../../../com/amap/api/services/route/RouteSearchV2.ShowFields.html" title="com.amap.api.services.route中的类"><code>RouteSearchV2.ShowFields</code></a></div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.WalkRouteQuery.html#isIndoor--">isIndoor</a></span>()</code>
<div class="block">是否需要室内算路</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.WalkRouteQuery.html#setAlternativeRoute-int-">setAlternativeRoute</a></span>(int&nbsp;alternativeRoute)</code>
<div class="block">设置返回路线条数</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.WalkRouteQuery.html#setIndoor-boolean-">setIndoor</a></span>(boolean&nbsp;indoor)</code>
<div class="block">设置是否需要室内算路</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/route/RouteSearchV2.WalkRouteQuery.html#setShowFields-int-">setShowFields</a></span>(int&nbsp;showFields)</code>
<div class="block">扩展字段 <a href="../../../../../com/amap/api/services/route/RouteSearchV2.ShowFields.html" title="com.amap.api.services.route中的类"><code>RouteSearchV2.ShowFields</code></a></div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="WalkRouteQuery-com.amap.api.services.route.RouteSearchV2.FromAndTo-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>WalkRouteQuery</h4>
<pre>public&nbsp;WalkRouteQuery(<a href="../../../../../com/amap/api/services/route/RouteSearchV2.FromAndTo.html" title="com.amap.api.services.route中的类">RouteSearchV2.FromAndTo</a>&nbsp;fromAndTo)</pre>
<div class="block">WalkRouteQuery构造函数。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>fromAndTo</code> - 路径的起终点。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.4.0</dd>
</dl>
</li>
</ul>
<a name="WalkRouteQuery-Parcel-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>WalkRouteQuery</h4>
<pre>public&nbsp;WalkRouteQuery(Parcel&nbsp;source)</pre>
<div class="block">序列化实现。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.4.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="getFromAndTo--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFromAndTo</h4>
<pre>public&nbsp;<a href="../../../../../com/amap/api/services/route/RouteSearchV2.FromAndTo.html" title="com.amap.api.services.route中的类">RouteSearchV2.FromAndTo</a>&nbsp;getFromAndTo()</pre>
<div class="block">返回查询路径的起终点。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>查询路径的起终点。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.4.0</dd>
</dl>
</li>
</ul>
<a name="isIndoor--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isIndoor</h4>
<pre>public&nbsp;boolean&nbsp;isIndoor()</pre>
<div class="block">是否需要室内算路</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
</dl>
</li>
</ul>
<a name="setIndoor-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setIndoor</h4>
<pre>public&nbsp;void&nbsp;setIndoor(boolean&nbsp;indoor)</pre>
<div class="block">设置是否需要室内算路</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>indoor</code> - </dd>
</dl>
</li>
</ul>
<a name="getAlternativeRoute--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAlternativeRoute</h4>
<pre>public&nbsp;int&nbsp;getAlternativeRoute()</pre>
<div class="block">返回路线条数</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
</dl>
</li>
</ul>
<a name="setAlternativeRoute-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAlternativeRoute</h4>
<pre>public&nbsp;void&nbsp;setAlternativeRoute(int&nbsp;alternativeRoute)</pre>
<div class="block">设置返回路线条数</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>alternativeRoute</code> - </dd>
</dl>
</li>
</ul>
<a name="getShowFields--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getShowFields</h4>
<pre>public&nbsp;int&nbsp;getShowFields()</pre>
<div class="block">扩展字段 <a href="../../../../../com/amap/api/services/route/RouteSearchV2.ShowFields.html" title="com.amap.api.services.route中的类"><code>RouteSearchV2.ShowFields</code></a></div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.4.0</dd>
</dl>
</li>
</ul>
<a name="setShowFields-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setShowFields</h4>
<pre>public&nbsp;void&nbsp;setShowFields(int&nbsp;showFields)</pre>
<div class="block">扩展字段 <a href="../../../../../com/amap/api/services/route/RouteSearchV2.ShowFields.html" title="com.amap.api.services.route中的类"><code>RouteSearchV2.ShowFields</code></a></div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.4.0</dd>
</dl>
</li>
</ul>
<a name="equals-java.lang.Object-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>equals</h4>
<pre>public&nbsp;boolean&nbsp;equals(java.lang.Object&nbsp;o)</pre>
<div class="block">比较两个查询条件是否相同。</div>
<dl>
<dt><span class="overrideSpecifyLabel">覆盖:</span></dt>
<dd><code>equals</code>&nbsp;在类中&nbsp;<code>java.lang.Object</code></dd>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>obj</code> - 查询条件。</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>查询条件是否相同。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.4.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/RouteSearchV2.WalkRouteQuery.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/route/RouteSearchV2.ShowFields.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/amap/api/services/route/SearchCity.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/route/RouteSearchV2.WalkRouteQuery.html" target="_top">框架</a></li>
<li><a href="RouteSearchV2.WalkRouteQuery.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
