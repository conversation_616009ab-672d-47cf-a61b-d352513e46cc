<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>程序包 com.amap.api.services.route的使用</title>
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="\u7A0B\u5E8F\u5305 com.amap.api.services.route\u7684\u4F7F\u7528";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li>类</li>
<li class="navBarCell1Rev">使用</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个</li>
<li>下一个</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/route/package-use.html" target="_top">框架</a></li>
<li><a href="package-use.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 title="程序包的使用 com.amap.api.services.route" class="title">程序包的使用<br>com.amap.api.services.route</h1>
</div>
<div class="contentContainer">
<ul class="blockList">
<li class="blockList">
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表程序包和解释">
<caption><span>使用<a href="../../../../../com/amap/api/services/route/package-summary.html">com.amap.api.services.route</a>的程序包</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">程序包</th>
<th class="colLast" scope="col">说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="#com.amap.api.services.route">com.amap.api.services.route</a></td>
<td class="colLast">
<div class="block">
路径查询包，包含了在公交、自驾或步行路线规划中的详细信息。</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.amap.api.services.route">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表类和解释">
<caption><span><a href="../../../../../com/amap/api/services/route/package-summary.html">com.amap.api.services.route</a>使用的<a href="../../../../../com/amap/api/services/route/package-summary.html">com.amap.api.services.route</a>中的类</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">类和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/route/class-use/BusPath.html#com.amap.api.services.route">BusPath</a>
<div class="block">定义了公交换乘路径规划的一个方案。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/route/class-use/BusPathV2.html#com.amap.api.services.route">BusPathV2</a>
<div class="block">定义了公交换乘路径规划的一个方案。</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/route/class-use/BusRouteResult.html#com.amap.api.services.route">BusRouteResult</a>
<div class="block">定义了公交路径规划的结果集。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/route/class-use/BusRouteResultV2.html#com.amap.api.services.route">BusRouteResultV2</a>
<div class="block">定义了公交路径规划的结果集。</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/route/class-use/BusStep.html#com.amap.api.services.route">BusStep</a>
<div class="block">定义了公交路径规划的一个路段。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/route/class-use/BusStepV2.html#com.amap.api.services.route">BusStepV2</a>
<div class="block">定义了公交路径规划的一个路段。</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/route/class-use/Cost.html#com.amap.api.services.route">Cost</a>
<div class="block">路线耗时等信息</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/route/class-use/DistanceItem.html#com.amap.api.services.route">DistanceItem</a>
<div class="block">距离测量返回结果中某一个结果的具体信息</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/route/class-use/DistanceResult.html#com.amap.api.services.route">DistanceResult</a>
<div class="block">距离测量返回结果</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/route/class-use/DistanceSearch.DistanceQuery.html#com.amap.api.services.route">DistanceSearch.DistanceQuery</a>
<div class="block">距离测量属性设置类，其中包含起终点以及测量类型的设置。</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/route/class-use/DistanceSearch.OnDistanceSearchListener.html#com.amap.api.services.route">DistanceSearch.OnDistanceSearchListener</a>
<div class="block">距离测量异步请求回调。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/route/class-use/District.html#com.amap.api.services.route">District</a>
<div class="block">此类定义搜索返回行政区的名称和编码。</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/route/class-use/Doorway.html#com.amap.api.services.route">Doorway</a>
<div class="block">定义了公交换乘路径规划的一个换乘点的出入口信息。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/route/class-use/DrivePath.html#com.amap.api.services.route">DrivePath</a>
<div class="block">定义了驾车路径规划的一个方案。</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/route/class-use/DrivePathV2.html#com.amap.api.services.route">DrivePathV2</a>
<div class="block">定义了驾车路径规划的一个方案。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/route/class-use/DrivePlanPath.html#com.amap.api.services.route">DrivePlanPath</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/route/class-use/DrivePlanStep.html#com.amap.api.services.route">DrivePlanStep</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/route/class-use/DriveRoutePlanResult.html#com.amap.api.services.route">DriveRoutePlanResult</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/route/class-use/DriveRouteResult.html#com.amap.api.services.route">DriveRouteResult</a>
<div class="block">定义了驾车路径规划的结果集。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/route/class-use/DriveRouteResultV2.html#com.amap.api.services.route">DriveRouteResultV2</a>
<div class="block">定义了驾车路径规划的结果集。</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/route/class-use/DriveStep.html#com.amap.api.services.route">DriveStep</a>
<div class="block">定义了驾车路径规划的一个路段。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/route/class-use/DriveStepV2.html#com.amap.api.services.route">DriveStepV2</a>
<div class="block">定义了驾车路径规划的一个路段。</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/route/class-use/Navi.html#com.amap.api.services.route">Navi</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/route/class-use/Path.html#com.amap.api.services.route">Path</a>
<div class="block">定义了路径规划的一个方案。</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/route/class-use/Railway.html#com.amap.api.services.route">Railway</a>
<div class="block">列车信息</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/route/class-use/RailwaySpace.html#com.amap.api.services.route">RailwaySpace</a>
<div class="block">列车的舱位及价格信息
 舱位：动车分一般为一等软座和二等软座</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/route/class-use/RailwayStationItem.html#com.amap.api.services.route">RailwayStationItem</a>
<div class="block">定义了火车站点信息</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/route/class-use/RidePath.html#com.amap.api.services.route">RidePath</a>
<div class="block">定义了骑行路径规划的一个方案。</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/route/class-use/RideRouteResult.html#com.amap.api.services.route">RideRouteResult</a>
<div class="block">定义了骑行路径规划的结果集。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/route/class-use/RideRouteResultV2.html#com.amap.api.services.route">RideRouteResultV2</a>
<div class="block">定义了骑行路径规划的结果集。</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/route/class-use/RideStep.html#com.amap.api.services.route">RideStep</a>
<div class="block">定义了骑行路径规划的一个路段。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/route/class-use/RouteBusLineItem.html#com.amap.api.services.route">RouteBusLineItem</a>
<div class="block">定义了公交换乘路径规划的一个换乘段的公交信息。</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/route/class-use/RouteBusWalkItem.html#com.amap.api.services.route">RouteBusWalkItem</a>
<div class="block">定义了公交换乘路径规划的一个换乘段的步行信息。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/route/class-use/RoutePlanResult.html#com.amap.api.services.route">RoutePlanResult</a>
<div class="block">定义了路径规划的结果集。</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/route/class-use/RouteRailwayItem.html#com.amap.api.services.route">RouteRailwayItem</a>
<div class="block">火车乘坐信息</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/route/class-use/RouteResult.html#com.amap.api.services.route">RouteResult</a>
<div class="block">定义了路径规划的结果集。</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/route/class-use/RouteSearch.BusRouteQuery.html#com.amap.api.services.route">RouteSearch.BusRouteQuery</a>
<div class="block">此类定义了公交路径规划查询路径的起终点、计算路径的模式、城市和是否计算夜班车。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/route/class-use/RouteSearch.DrivePlanQuery.html#com.amap.api.services.route">RouteSearch.DrivePlanQuery</a>
<div class="block">此类定义了驾车未来路径查询规划,最大支持未来七天的规划。</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/route/class-use/RouteSearch.DriveRouteQuery.html#com.amap.api.services.route">RouteSearch.DriveRouteQuery</a>
<div class="block">此类定义了驾车路径查询规划。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/route/class-use/RouteSearch.FromAndTo.html#com.amap.api.services.route">RouteSearch.FromAndTo</a>
<div class="block">构造路径规划的起点和终点坐标。</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/route/class-use/RouteSearch.OnRoutePlanSearchListener.html#com.amap.api.services.route">RouteSearch.OnRoutePlanSearchListener</a>
<div class="block">未来路径规划回调方法</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/route/class-use/RouteSearch.OnRouteSearchListener.html#com.amap.api.services.route">RouteSearch.OnRouteSearchListener</a>
<div class="block">本类为路径搜索结果的异步处理回调接口。</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/route/class-use/RouteSearch.OnTruckRouteSearchListener.html#com.amap.api.services.route">RouteSearch.OnTruckRouteSearchListener</a>
<div class="block">货车路径规划回调方法</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/route/class-use/RouteSearch.RideRouteQuery.html#com.amap.api.services.route">RouteSearch.RideRouteQuery</a>
<div class="block">此类定义了骑行路径的起终点和计算路径的模式。</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/route/class-use/RouteSearch.TruckRouteQuery.html#com.amap.api.services.route">RouteSearch.TruckRouteQuery</a>
<div class="block">此类定义了货车路径的起终点和计算路径的模式。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/route/class-use/RouteSearch.WalkRouteQuery.html#com.amap.api.services.route">RouteSearch.WalkRouteQuery</a>
<div class="block">此类定义了步行路径的起终点和计算路径的模式。</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/route/class-use/RouteSearchCity.html#com.amap.api.services.route">RouteSearchCity</a>
<div class="block">此类定义路径规划返回的城市名称、编码和行政区的名称和编码。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/route/class-use/RouteSearchV2.BusRouteQuery.html#com.amap.api.services.route">RouteSearchV2.BusRouteQuery</a>
<div class="block">此类定义了公交路径规划查询路径的起终点、计算路径的模式、城市和是否计算夜班车。</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/route/class-use/RouteSearchV2.DriveRouteQuery.html#com.amap.api.services.route">RouteSearchV2.DriveRouteQuery</a>
<div class="block">此类定义了驾车路径查询规划。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/route/class-use/RouteSearchV2.DrivingStrategy.html#com.amap.api.services.route">RouteSearchV2.DrivingStrategy</a>
<div class="block">驾车策略</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/route/class-use/RouteSearchV2.FromAndTo.html#com.amap.api.services.route">RouteSearchV2.FromAndTo</a>
<div class="block">构造路径规划的起点和终点坐标。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/route/class-use/RouteSearchV2.OnRouteSearchListener.html#com.amap.api.services.route">RouteSearchV2.OnRouteSearchListener</a>
<div class="block">本类为路径搜索结果的异步处理回调接口。</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/route/class-use/RouteSearchV2.RideRouteQuery.html#com.amap.api.services.route">RouteSearchV2.RideRouteQuery</a>
<div class="block">此类定义了骑行路径的起终点和计算路径的模式。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/route/class-use/RouteSearchV2.WalkRouteQuery.html#com.amap.api.services.route">RouteSearchV2.WalkRouteQuery</a>
<div class="block">此类定义了步行路径的起终点和计算路径的模式。</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/route/class-use/SearchCity.html#com.amap.api.services.route">SearchCity</a>
<div class="block">此类定义搜索返回城市的名称和编码。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/route/class-use/TaxiItem.html#com.amap.api.services.route">TaxiItem</a>
<div class="block">此类定义了打车路段信息</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/route/class-use/TaxiItemV2.html#com.amap.api.services.route">TaxiItemV2</a>
<div class="block">此类定义了打车路段信息</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/route/class-use/TimeInfo.html#com.amap.api.services.route">TimeInfo</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/route/class-use/TimeInfosElement.html#com.amap.api.services.route">TimeInfosElement</a>
<div class="block">对应的路线</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/route/class-use/TMC.html#com.amap.api.services.route">TMC</a>
<div class="block">交通信息类</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/route/class-use/TruckPath.html#com.amap.api.services.route">TruckPath</a>
<div class="block">定义了货车路径规划的一个方案。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/route/class-use/TruckRouteRestult.html#com.amap.api.services.route">TruckRouteRestult</a>
<div class="block">定义了货车路径规划的结果集。</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/route/class-use/TruckStep.html#com.amap.api.services.route">TruckStep</a>
<div class="block">定义了货车路径规划的一个路段。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/route/class-use/WalkPath.html#com.amap.api.services.route">WalkPath</a>
<div class="block">定义了步行路径规划的一个方案。</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/route/class-use/WalkRouteResult.html#com.amap.api.services.route">WalkRouteResult</a>
<div class="block">定义了步行路径规划的结果集。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/route/class-use/WalkRouteResultV2.html#com.amap.api.services.route">WalkRouteResultV2</a>
<div class="block">定义了步行路径规划的结果集。</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/route/class-use/WalkStep.html#com.amap.api.services.route">WalkStep</a>
<div class="block">定义了步行路径规划的一个路段。</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li>类</li>
<li class="navBarCell1Rev">使用</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个</li>
<li>下一个</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/route/package-use.html" target="_top">框架</a></li>
<li><a href="package-use.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
