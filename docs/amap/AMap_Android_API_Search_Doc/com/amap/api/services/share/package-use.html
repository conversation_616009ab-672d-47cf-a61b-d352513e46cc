<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>程序包 com.amap.api.services.share的使用</title>
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="\u7A0B\u5E8F\u5305 com.amap.api.services.share\u7684\u4F7F\u7528";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li>类</li>
<li class="navBarCell1Rev">使用</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个</li>
<li>下一个</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/share/package-use.html" target="_top">框架</a></li>
<li><a href="package-use.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 title="程序包的使用 com.amap.api.services.share" class="title">程序包的使用<br>com.amap.api.services.share</h1>
</div>
<div class="contentContainer">
<ul class="blockList">
<li class="blockList">
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表程序包和解释">
<caption><span>使用<a href="../../../../../com/amap/api/services/share/package-summary.html">com.amap.api.services.share</a>的程序包</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">程序包</th>
<th class="colLast" scope="col">说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="#com.amap.api.services.share">com.amap.api.services.share</a></td>
<td class="colLast">
<div class="block">
短串分享包，包含了位置/POI/路径规划/导航的分享功能。</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.amap.api.services.share">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表类和解释">
<caption><span><a href="../../../../../com/amap/api/services/share/package-summary.html">com.amap.api.services.share</a>使用的<a href="../../../../../com/amap/api/services/share/package-summary.html">com.amap.api.services.share</a>中的类</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">类和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/share/class-use/ShareSearch.OnShareSearchListener.html#com.amap.api.services.share">ShareSearch.OnShareSearchListener</a>
<div class="block">此接口定义了短串分享的异步处理回调接口。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/share/class-use/ShareSearch.ShareBusRouteQuery.html#com.amap.api.services.share">ShareSearch.ShareBusRouteQuery</a>
<div class="block">此类定义了公交路径规划的起点、终点、公交路径规划策略。</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/share/class-use/ShareSearch.ShareDrivingRouteQuery.html#com.amap.api.services.share">ShareSearch.ShareDrivingRouteQuery</a>
<div class="block">此类定义了驾车路径规划的起点、终点、路径规划策略。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/share/class-use/ShareSearch.ShareFromAndTo.html#com.amap.api.services.share">ShareSearch.ShareFromAndTo</a>
<div class="block">此类定义了短串分享的坐标、分享名称。</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/share/class-use/ShareSearch.ShareNaviQuery.html#com.amap.api.services.share">ShareSearch.ShareNaviQuery</a>
<div class="block">此类定义了导航分享的起点、终点、驾车策略。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/share/class-use/ShareSearch.ShareWalkRouteQuery.html#com.amap.api.services.share">ShareSearch.ShareWalkRouteQuery</a>
<div class="block">此类定义了步行路径规划的起点、终点、步行路径规划策略。</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li>类</li>
<li class="navBarCell1Rev">使用</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个</li>
<li>下一个</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/share/package-use.html" target="_top">框架</a></li>
<li><a href="package-use.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
