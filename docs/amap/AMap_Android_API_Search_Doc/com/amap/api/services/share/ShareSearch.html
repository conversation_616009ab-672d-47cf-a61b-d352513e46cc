<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>ShareSearch</title>
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ShareSearch";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10};
var tabs = {65535:["t0","所有方法"],2:["t2","实例方法"],8:["t4","具体方法"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/ShareSearch.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个类</li>
<li><a href="../../../../../com/amap/api/services/share/ShareSearch.OnShareSearchListener.html" title="com.amap.api.services.share中的接口"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/share/ShareSearch.html" target="_top">框架</a></li>
<li><a href="ShareSearch.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li><a href="#nested.class.summary">嵌套</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li><a href="#field.detail">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.amap.api.services.share</div>
<h2 title="类 ShareSearch" class="title">类 ShareSearch</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.amap.api.services.share.ShareSearch</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">ShareSearch</span>
extends java.lang.Object</pre>
<div class="block">本类为短串分享的入口类。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>嵌套类概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="嵌套类概要表, 列表嵌套类和解释">
<caption><span>嵌套类</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">类和说明</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static interface&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/share/ShareSearch.OnShareSearchListener.html" title="com.amap.api.services.share中的接口">ShareSearch.OnShareSearchListener</a></span></code>
<div class="block">此接口定义了短串分享的异步处理回调接口。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/share/ShareSearch.ShareBusRouteQuery.html" title="com.amap.api.services.share中的类">ShareSearch.ShareBusRouteQuery</a></span></code>
<div class="block">此类定义了公交路径规划的起点、终点、公交路径规划策略。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/share/ShareSearch.ShareDrivingRouteQuery.html" title="com.amap.api.services.share中的类">ShareSearch.ShareDrivingRouteQuery</a></span></code>
<div class="block">此类定义了驾车路径规划的起点、终点、路径规划策略。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/share/ShareSearch.ShareFromAndTo.html" title="com.amap.api.services.share中的类">ShareSearch.ShareFromAndTo</a></span></code>
<div class="block">此类定义了短串分享的坐标、分享名称。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/share/ShareSearch.ShareNaviQuery.html" title="com.amap.api.services.share中的类">ShareSearch.ShareNaviQuery</a></span></code>
<div class="block">此类定义了导航分享的起点、终点、驾车策略。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/share/ShareSearch.ShareWalkRouteQuery.html" title="com.amap.api.services.share中的类">ShareSearch.ShareWalkRouteQuery</a></span></code>
<div class="block">此类定义了步行路径规划的起点、终点、步行路径规划策略。</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>字段概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="字段概要表, 列表字段和解释">
<caption><span>字段</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">字段和说明</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/share/ShareSearch.html#BusComfortable">BusComfortable</a></span></code>
<div class="block">最舒适模式（公交路径规划）</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/share/ShareSearch.html#BusDefault">BusDefault</a></span></code>
<div class="block">最快捷模式（公交路径规划）</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/share/ShareSearch.html#BusLeaseChange">BusLeaseChange</a></span></code>
<div class="block">最少换乘模式（公交路径规划）</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/share/ShareSearch.html#BusLeaseWalk">BusLeaseWalk</a></span></code>
<div class="block">最少步行模式（公交路径规划）</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/share/ShareSearch.html#BusNoSubway">BusNoSubway</a></span></code>
<div class="block">不乘地铁模式（公交路径规划）</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/share/ShareSearch.html#BusSaveMoney">BusSaveMoney</a></span></code>
<div class="block">最经济模式（公交路径规划）</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/share/ShareSearch.html#DrivingAvoidCongestion">DrivingAvoidCongestion</a></span></code>
<div class="block">躲避拥堵（驾车路径规划）</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/share/ShareSearch.html#DrivingDefault">DrivingDefault</a></span></code>
<div class="block">速度最快（驾车路径规划）</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/share/ShareSearch.html#DrivingNoHighWay">DrivingNoHighWay</a></span></code>
<div class="block">不走高速（驾车路径规划）</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/share/ShareSearch.html#DrivingNoHighWayAvoidCongestion">DrivingNoHighWayAvoidCongestion</a></span></code>
<div class="block">不走高速且躲避拥堵（驾车路径规划）</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/share/ShareSearch.html#DrivingNoHighWaySaveMoney">DrivingNoHighWaySaveMoney</a></span></code>
<div class="block">不走高速且避免收费（驾车路径规划）</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/share/ShareSearch.html#DrivingNoHighWaySaveMoneyAvoidCongestion">DrivingNoHighWaySaveMoneyAvoidCongestion</a></span></code>
<div class="block">不走高速躲避收费和拥堵（驾车路径规划）</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/share/ShareSearch.html#DrivingSaveMoney">DrivingSaveMoney</a></span></code>
<div class="block">费用少（驾车路径规划）</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/share/ShareSearch.html#DrivingSaveMoneyAvoidCongestion">DrivingSaveMoneyAvoidCongestion</a></span></code>
<div class="block">躲避收费和拥堵（驾车路径规划）</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/share/ShareSearch.html#DrivingShortDistance">DrivingShortDistance</a></span></code>
<div class="block">距离优先（驾车路径规划）</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/share/ShareSearch.html#NaviAvoidCongestion">NaviAvoidCongestion</a></span></code>
<div class="block">躲避拥堵</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/share/ShareSearch.html#NaviDefault">NaviDefault</a></span></code>
<div class="block">速度最快</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/share/ShareSearch.html#NaviNoHighWay">NaviNoHighWay</a></span></code>
<div class="block">不走高速</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/share/ShareSearch.html#NaviNoHighWayAvoidCongestion">NaviNoHighWayAvoidCongestion</a></span></code>
<div class="block">不走高速且躲避拥堵</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/share/ShareSearch.html#NaviNoHighWaySaveMoney">NaviNoHighWaySaveMoney</a></span></code>
<div class="block">不走高速且避免收费</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/share/ShareSearch.html#NaviNoHighWaySaveMoneyAvoidCongestion">NaviNoHighWaySaveMoneyAvoidCongestion</a></span></code>
<div class="block">不走高速躲避收费和拥堵</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/share/ShareSearch.html#NaviSaveMoney">NaviSaveMoney</a></span></code>
<div class="block">费用少</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/share/ShareSearch.html#NaviSaveMoneyAvoidCongestion">NaviSaveMoneyAvoidCongestion</a></span></code>
<div class="block">躲避收费和拥堵</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/share/ShareSearch.html#NaviShortDistance">NaviShortDistance</a></span></code>
<div class="block">距离优先</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/share/ShareSearch.html#ShareSearch-Context-">ShareSearch</a></span>(Context&nbsp;context)</code>
<div class="block">根据给定的参数来构造一个ShareSearch的新对象。</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>所有方法</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">实例方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">具体方法</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/share/ShareSearch.html#searchBusRouteShareUrl-com.amap.api.services.share.ShareSearch.ShareBusRouteQuery-">searchBusRouteShareUrl</a></span>(<a href="../../../../../com/amap/api/services/share/ShareSearch.ShareBusRouteQuery.html" title="com.amap.api.services.share中的类">ShareSearch.ShareBusRouteQuery</a>&nbsp;shareBusRouteQuery)</code>
<div class="block">根据指定的参数获取公交路径规划分享的短串地址。</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/share/ShareSearch.html#searchBusRouteShareUrlAsyn-com.amap.api.services.share.ShareSearch.ShareBusRouteQuery-">searchBusRouteShareUrlAsyn</a></span>(<a href="../../../../../com/amap/api/services/share/ShareSearch.ShareBusRouteQuery.html" title="com.amap.api.services.share中的类">ShareSearch.ShareBusRouteQuery</a>&nbsp;shareBusRouteQuery)</code>
<div class="block">（异步处理）根据指定的参数来进行公交路径规划分享的异步处理。</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/share/ShareSearch.html#searchDrivingRouteShareUrl-com.amap.api.services.share.ShareSearch.ShareDrivingRouteQuery-">searchDrivingRouteShareUrl</a></span>(<a href="../../../../../com/amap/api/services/share/ShareSearch.ShareDrivingRouteQuery.html" title="com.amap.api.services.share中的类">ShareSearch.ShareDrivingRouteQuery</a>&nbsp;drivingRouteQuery)</code>
<div class="block">根据指定的参数获取驾车路径规划分享的短串地址。</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/share/ShareSearch.html#searchDrivingRouteShareUrlAsyn-com.amap.api.services.share.ShareSearch.ShareDrivingRouteQuery-">searchDrivingRouteShareUrlAsyn</a></span>(<a href="../../../../../com/amap/api/services/share/ShareSearch.ShareDrivingRouteQuery.html" title="com.amap.api.services.share中的类">ShareSearch.ShareDrivingRouteQuery</a>&nbsp;drivingRouteQuery)</code>
<div class="block">（异步处理）根据指定的参数来进行驾车路径规划分享的异步处理。</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/share/ShareSearch.html#searchLocationShareUrl-com.amap.api.services.core.LatLonSharePoint-">searchLocationShareUrl</a></span>(<a href="../../../../../com/amap/api/services/core/LatLonSharePoint.html" title="com.amap.api.services.core中的类">LatLonSharePoint</a>&nbsp;locationQuery)</code>
<div class="block">根据指定的参数获取位置分享的短串地址。</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/share/ShareSearch.html#searchLocationShareUrlAsyn-com.amap.api.services.core.LatLonSharePoint-">searchLocationShareUrlAsyn</a></span>(<a href="../../../../../com/amap/api/services/core/LatLonSharePoint.html" title="com.amap.api.services.core中的类">LatLonSharePoint</a>&nbsp;locationQuery)</code>
<div class="block">（异步处理）根据指定的参数来进行定位位置分享的异步处理。</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/share/ShareSearch.html#searchNaviShareUrl-com.amap.api.services.share.ShareSearch.ShareNaviQuery-">searchNaviShareUrl</a></span>(<a href="../../../../../com/amap/api/services/share/ShareSearch.ShareNaviQuery.html" title="com.amap.api.services.share中的类">ShareSearch.ShareNaviQuery</a>&nbsp;naviQuery)</code>
<div class="block">根据指定的参数获取导航分享的短串地址。</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/share/ShareSearch.html#searchNaviShareUrlAsyn-com.amap.api.services.share.ShareSearch.ShareNaviQuery-">searchNaviShareUrlAsyn</a></span>(<a href="../../../../../com/amap/api/services/share/ShareSearch.ShareNaviQuery.html" title="com.amap.api.services.share中的类">ShareSearch.ShareNaviQuery</a>&nbsp;naviQuery)</code>
<div class="block">（异步处理）根据指定的参数来进行导航分享的异步处理。</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/share/ShareSearch.html#searchPoiShareUrl-com.amap.api.services.core.PoiItem-">searchPoiShareUrl</a></span>(<a href="../../../../../com/amap/api/services/core/PoiItem.html" title="com.amap.api.services.core中的类">PoiItem</a>&nbsp;poiitem)</code>
<div class="block">根据指定的参数获取POI分享的短串地址。</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/share/ShareSearch.html#searchPoiShareUrlAsyn-com.amap.api.services.core.PoiItem-">searchPoiShareUrlAsyn</a></span>(<a href="../../../../../com/amap/api/services/core/PoiItem.html" title="com.amap.api.services.core中的类">PoiItem</a>&nbsp;poiitem)</code>
<div class="block">（异步处理）根据指定的参数来进行POI分享的异步处理。</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/share/ShareSearch.html#searchWalkRouteShareUrl-com.amap.api.services.share.ShareSearch.ShareWalkRouteQuery-">searchWalkRouteShareUrl</a></span>(<a href="../../../../../com/amap/api/services/share/ShareSearch.ShareWalkRouteQuery.html" title="com.amap.api.services.share中的类">ShareSearch.ShareWalkRouteQuery</a>&nbsp;walkRouteQuery)</code>
<div class="block">根据指定的参数获取步行路径规划分享的短串地址。</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/share/ShareSearch.html#searchWalkRouteShareUrlAsyn-com.amap.api.services.share.ShareSearch.ShareWalkRouteQuery-">searchWalkRouteShareUrlAsyn</a></span>(<a href="../../../../../com/amap/api/services/share/ShareSearch.ShareWalkRouteQuery.html" title="com.amap.api.services.share中的类">ShareSearch.ShareWalkRouteQuery</a>&nbsp;walkRouteQuery)</code>
<div class="block">（异步处理）根据指定的参数来进行步行路径规划分享的异步处理。</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/share/ShareSearch.html#setOnShareSearchListener-com.amap.api.services.share.ShareSearch.OnShareSearchListener-">setOnShareSearchListener</a></span>(<a href="../../../../../com/amap/api/services/share/ShareSearch.OnShareSearchListener.html" title="com.amap.api.services.share中的接口">ShareSearch.OnShareSearchListener</a>&nbsp;onShareSearchListener)</code>
<div class="block">短串分享结果监听接口设置。</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>字段详细资料</h3>
<a name="BusDefault">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BusDefault</h4>
<pre>public static final&nbsp;int BusDefault</pre>
<div class="block">最快捷模式（公交路径规划）</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.share.ShareSearch.BusDefault">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="BusSaveMoney">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BusSaveMoney</h4>
<pre>public static final&nbsp;int BusSaveMoney</pre>
<div class="block">最经济模式（公交路径规划）</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.share.ShareSearch.BusSaveMoney">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="BusLeaseChange">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BusLeaseChange</h4>
<pre>public static final&nbsp;int BusLeaseChange</pre>
<div class="block">最少换乘模式（公交路径规划）</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.share.ShareSearch.BusLeaseChange">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="BusLeaseWalk">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BusLeaseWalk</h4>
<pre>public static final&nbsp;int BusLeaseWalk</pre>
<div class="block">最少步行模式（公交路径规划）</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.share.ShareSearch.BusLeaseWalk">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="BusComfortable">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BusComfortable</h4>
<pre>public static final&nbsp;int BusComfortable</pre>
<div class="block">最舒适模式（公交路径规划）</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.share.ShareSearch.BusComfortable">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="BusNoSubway">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BusNoSubway</h4>
<pre>public static final&nbsp;int BusNoSubway</pre>
<div class="block">不乘地铁模式（公交路径规划）</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.share.ShareSearch.BusNoSubway">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="DrivingDefault">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DrivingDefault</h4>
<pre>public static final&nbsp;int DrivingDefault</pre>
<div class="block">速度最快（驾车路径规划）</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.share.ShareSearch.DrivingDefault">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="DrivingSaveMoney">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DrivingSaveMoney</h4>
<pre>public static final&nbsp;int DrivingSaveMoney</pre>
<div class="block">费用少（驾车路径规划）</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.share.ShareSearch.DrivingSaveMoney">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="DrivingShortDistance">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DrivingShortDistance</h4>
<pre>public static final&nbsp;int DrivingShortDistance</pre>
<div class="block">距离优先（驾车路径规划）</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.share.ShareSearch.DrivingShortDistance">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="DrivingNoHighWay">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DrivingNoHighWay</h4>
<pre>public static final&nbsp;int DrivingNoHighWay</pre>
<div class="block">不走高速（驾车路径规划）</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.share.ShareSearch.DrivingNoHighWay">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="DrivingAvoidCongestion">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DrivingAvoidCongestion</h4>
<pre>public static final&nbsp;int DrivingAvoidCongestion</pre>
<div class="block">躲避拥堵（驾车路径规划）</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.share.ShareSearch.DrivingAvoidCongestion">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="DrivingNoHighWaySaveMoney">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DrivingNoHighWaySaveMoney</h4>
<pre>public static final&nbsp;int DrivingNoHighWaySaveMoney</pre>
<div class="block">不走高速且避免收费（驾车路径规划）</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.share.ShareSearch.DrivingNoHighWaySaveMoney">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="DrivingNoHighWayAvoidCongestion">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DrivingNoHighWayAvoidCongestion</h4>
<pre>public static final&nbsp;int DrivingNoHighWayAvoidCongestion</pre>
<div class="block">不走高速且躲避拥堵（驾车路径规划）</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.share.ShareSearch.DrivingNoHighWayAvoidCongestion">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="DrivingSaveMoneyAvoidCongestion">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DrivingSaveMoneyAvoidCongestion</h4>
<pre>public static final&nbsp;int DrivingSaveMoneyAvoidCongestion</pre>
<div class="block">躲避收费和拥堵（驾车路径规划）</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.share.ShareSearch.DrivingSaveMoneyAvoidCongestion">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="DrivingNoHighWaySaveMoneyAvoidCongestion">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DrivingNoHighWaySaveMoneyAvoidCongestion</h4>
<pre>public static final&nbsp;int DrivingNoHighWaySaveMoneyAvoidCongestion</pre>
<div class="block">不走高速躲避收费和拥堵（驾车路径规划）</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.share.ShareSearch.DrivingNoHighWaySaveMoneyAvoidCongestion">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="NaviDefault">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NaviDefault</h4>
<pre>public static final&nbsp;int NaviDefault</pre>
<div class="block">速度最快</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.share.ShareSearch.NaviDefault">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="NaviSaveMoney">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NaviSaveMoney</h4>
<pre>public static final&nbsp;int NaviSaveMoney</pre>
<div class="block">费用少</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.share.ShareSearch.NaviSaveMoney">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="NaviShortDistance">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NaviShortDistance</h4>
<pre>public static final&nbsp;int NaviShortDistance</pre>
<div class="block">距离优先</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.share.ShareSearch.NaviShortDistance">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="NaviNoHighWay">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NaviNoHighWay</h4>
<pre>public static final&nbsp;int NaviNoHighWay</pre>
<div class="block">不走高速</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.share.ShareSearch.NaviNoHighWay">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="NaviAvoidCongestion">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NaviAvoidCongestion</h4>
<pre>public static final&nbsp;int NaviAvoidCongestion</pre>
<div class="block">躲避拥堵</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.share.ShareSearch.NaviAvoidCongestion">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="NaviNoHighWaySaveMoney">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NaviNoHighWaySaveMoney</h4>
<pre>public static final&nbsp;int NaviNoHighWaySaveMoney</pre>
<div class="block">不走高速且避免收费</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.share.ShareSearch.NaviNoHighWaySaveMoney">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="NaviNoHighWayAvoidCongestion">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NaviNoHighWayAvoidCongestion</h4>
<pre>public static final&nbsp;int NaviNoHighWayAvoidCongestion</pre>
<div class="block">不走高速且躲避拥堵</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.share.ShareSearch.NaviNoHighWayAvoidCongestion">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="NaviSaveMoneyAvoidCongestion">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NaviSaveMoneyAvoidCongestion</h4>
<pre>public static final&nbsp;int NaviSaveMoneyAvoidCongestion</pre>
<div class="block">躲避收费和拥堵</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.share.ShareSearch.NaviSaveMoneyAvoidCongestion">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="NaviNoHighWaySaveMoneyAvoidCongestion">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>NaviNoHighWaySaveMoneyAvoidCongestion</h4>
<pre>public static final&nbsp;int NaviNoHighWaySaveMoneyAvoidCongestion</pre>
<div class="block">不走高速躲避收费和拥堵</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.share.ShareSearch.NaviNoHighWaySaveMoneyAvoidCongestion">常量字段值</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="ShareSearch-Context-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>ShareSearch</h4>
<pre>public&nbsp;ShareSearch(Context&nbsp;context)
            throws <a href="../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></pre>
<div class="block">根据给定的参数来构造一个ShareSearch的新对象。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>context</code> - 对应的Context。</dd>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code><a href="../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></code></dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="setOnShareSearchListener-com.amap.api.services.share.ShareSearch.OnShareSearchListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setOnShareSearchListener</h4>
<pre>public&nbsp;void&nbsp;setOnShareSearchListener(<a href="../../../../../com/amap/api/services/share/ShareSearch.OnShareSearchListener.html" title="com.amap.api.services.share中的接口">ShareSearch.OnShareSearchListener</a>&nbsp;onShareSearchListener)</pre>
<div class="block">短串分享结果监听接口设置。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>onShareSearchListener</code> - 短串分享结果监听接口。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
<a name="searchPoiShareUrlAsyn-com.amap.api.services.core.PoiItem-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>searchPoiShareUrlAsyn</h4>
<pre>public&nbsp;void&nbsp;searchPoiShareUrlAsyn(<a href="../../../../../com/amap/api/services/core/PoiItem.html" title="com.amap.api.services.core中的类">PoiItem</a>&nbsp;poiitem)</pre>
<div class="block">（异步处理）根据指定的参数来进行POI分享的异步处理。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>poiitem</code> - 分享的信息。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
<a name="searchBusRouteShareUrlAsyn-com.amap.api.services.share.ShareSearch.ShareBusRouteQuery-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>searchBusRouteShareUrlAsyn</h4>
<pre>public&nbsp;void&nbsp;searchBusRouteShareUrlAsyn(<a href="../../../../../com/amap/api/services/share/ShareSearch.ShareBusRouteQuery.html" title="com.amap.api.services.share中的类">ShareSearch.ShareBusRouteQuery</a>&nbsp;shareBusRouteQuery)</pre>
<div class="block">（异步处理）根据指定的参数来进行公交路径规划分享的异步处理。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>shareBusRouteQuery</code> - 分享的信息。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
<a name="searchWalkRouteShareUrlAsyn-com.amap.api.services.share.ShareSearch.ShareWalkRouteQuery-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>searchWalkRouteShareUrlAsyn</h4>
<pre>public&nbsp;void&nbsp;searchWalkRouteShareUrlAsyn(<a href="../../../../../com/amap/api/services/share/ShareSearch.ShareWalkRouteQuery.html" title="com.amap.api.services.share中的类">ShareSearch.ShareWalkRouteQuery</a>&nbsp;walkRouteQuery)</pre>
<div class="block">（异步处理）根据指定的参数来进行步行路径规划分享的异步处理。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>walkRouteQuery</code> - 分享的信息。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
<a name="searchDrivingRouteShareUrlAsyn-com.amap.api.services.share.ShareSearch.ShareDrivingRouteQuery-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>searchDrivingRouteShareUrlAsyn</h4>
<pre>public&nbsp;void&nbsp;searchDrivingRouteShareUrlAsyn(<a href="../../../../../com/amap/api/services/share/ShareSearch.ShareDrivingRouteQuery.html" title="com.amap.api.services.share中的类">ShareSearch.ShareDrivingRouteQuery</a>&nbsp;drivingRouteQuery)</pre>
<div class="block">（异步处理）根据指定的参数来进行驾车路径规划分享的异步处理。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>drivingRouteQuery</code> - 分享的信息。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
<a name="searchNaviShareUrlAsyn-com.amap.api.services.share.ShareSearch.ShareNaviQuery-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>searchNaviShareUrlAsyn</h4>
<pre>public&nbsp;void&nbsp;searchNaviShareUrlAsyn(<a href="../../../../../com/amap/api/services/share/ShareSearch.ShareNaviQuery.html" title="com.amap.api.services.share中的类">ShareSearch.ShareNaviQuery</a>&nbsp;naviQuery)</pre>
<div class="block">（异步处理）根据指定的参数来进行导航分享的异步处理。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>naviQuery</code> - 分享的信息。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
<a name="searchLocationShareUrlAsyn-com.amap.api.services.core.LatLonSharePoint-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>searchLocationShareUrlAsyn</h4>
<pre>public&nbsp;void&nbsp;searchLocationShareUrlAsyn(<a href="../../../../../com/amap/api/services/core/LatLonSharePoint.html" title="com.amap.api.services.core中的类">LatLonSharePoint</a>&nbsp;locationQuery)</pre>
<div class="block">（异步处理）根据指定的参数来进行定位位置分享的异步处理。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>locationQuery</code> - 分享的信息。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
<a name="searchPoiShareUrl-com.amap.api.services.core.PoiItem-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>searchPoiShareUrl</h4>
<pre>public&nbsp;java.lang.String&nbsp;searchPoiShareUrl(<a href="../../../../../com/amap/api/services/core/PoiItem.html" title="com.amap.api.services.core中的类">PoiItem</a>&nbsp;poiitem)
                                   throws <a href="../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></pre>
<div class="block">根据指定的参数获取POI分享的短串地址。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>poiitem</code> - 分享的参数。仅支持POI id、经纬度、名称、地址的传入。</dd>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code><a href="../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></code></dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
<a name="searchNaviShareUrl-com.amap.api.services.share.ShareSearch.ShareNaviQuery-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>searchNaviShareUrl</h4>
<pre>public&nbsp;java.lang.String&nbsp;searchNaviShareUrl(<a href="../../../../../com/amap/api/services/share/ShareSearch.ShareNaviQuery.html" title="com.amap.api.services.share中的类">ShareSearch.ShareNaviQuery</a>&nbsp;naviQuery)
                                    throws <a href="../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></pre>
<div class="block">根据指定的参数获取导航分享的短串地址。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>naviQuery</code> - 分享的参数。</dd>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code><a href="../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></code></dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
<a name="searchLocationShareUrl-com.amap.api.services.core.LatLonSharePoint-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>searchLocationShareUrl</h4>
<pre>public&nbsp;java.lang.String&nbsp;searchLocationShareUrl(<a href="../../../../../com/amap/api/services/core/LatLonSharePoint.html" title="com.amap.api.services.core中的类">LatLonSharePoint</a>&nbsp;locationQuery)
                                        throws <a href="../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></pre>
<div class="block">根据指定的参数获取位置分享的短串地址。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>locationQuery</code> - 分享的参数。</dd>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code><a href="../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></code></dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
<a name="searchBusRouteShareUrl-com.amap.api.services.share.ShareSearch.ShareBusRouteQuery-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>searchBusRouteShareUrl</h4>
<pre>public&nbsp;java.lang.String&nbsp;searchBusRouteShareUrl(<a href="../../../../../com/amap/api/services/share/ShareSearch.ShareBusRouteQuery.html" title="com.amap.api.services.share中的类">ShareSearch.ShareBusRouteQuery</a>&nbsp;shareBusRouteQuery)
                                        throws <a href="../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></pre>
<div class="block">根据指定的参数获取公交路径规划分享的短串地址。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>shareBusRouteQuery</code> - 分享的参数。</dd>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code><a href="../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></code></dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
<a name="searchDrivingRouteShareUrl-com.amap.api.services.share.ShareSearch.ShareDrivingRouteQuery-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>searchDrivingRouteShareUrl</h4>
<pre>public&nbsp;java.lang.String&nbsp;searchDrivingRouteShareUrl(<a href="../../../../../com/amap/api/services/share/ShareSearch.ShareDrivingRouteQuery.html" title="com.amap.api.services.share中的类">ShareSearch.ShareDrivingRouteQuery</a>&nbsp;drivingRouteQuery)
                                            throws <a href="../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></pre>
<div class="block">根据指定的参数获取驾车路径规划分享的短串地址。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>drivingRouteQuery</code> - 分享的参数。</dd>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code><a href="../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></code></dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
<a name="searchWalkRouteShareUrl-com.amap.api.services.share.ShareSearch.ShareWalkRouteQuery-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>searchWalkRouteShareUrl</h4>
<pre>public&nbsp;java.lang.String&nbsp;searchWalkRouteShareUrl(<a href="../../../../../com/amap/api/services/share/ShareSearch.ShareWalkRouteQuery.html" title="com.amap.api.services.share中的类">ShareSearch.ShareWalkRouteQuery</a>&nbsp;walkRouteQuery)
                                         throws <a href="../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></pre>
<div class="block">根据指定的参数获取步行路径规划分享的短串地址。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>walkRouteQuery</code> - 分享的参数。</dd>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code><a href="../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></code></dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/ShareSearch.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个类</li>
<li><a href="../../../../../com/amap/api/services/share/ShareSearch.OnShareSearchListener.html" title="com.amap.api.services.share中的接口"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/share/ShareSearch.html" target="_top">框架</a></li>
<li><a href="ShareSearch.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li><a href="#nested.class.summary">嵌套</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li><a href="#field.detail">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
