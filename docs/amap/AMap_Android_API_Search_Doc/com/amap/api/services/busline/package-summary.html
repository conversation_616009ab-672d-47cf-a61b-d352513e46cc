<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>com.amap.api.services.busline</title>
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="com.amap.api.services.busline";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li class="navBarCell1Rev">程序包</li>
<li>类</li>
<li><a href="package-use.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个程序包</li>
<li><a href="../../../../../com/amap/api/services/cloud/package-summary.html">下一个程序包</a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/busline/package-summary.html" target="_top">框架</a></li>
<li><a href="package-summary.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 title="程序包" class="title">程序包&nbsp;com.amap.api.services.busline</h1>
<div class="docSummary">
<div class="block">
公交线路和公交站点查询包。</div>
</div>
<p>请参阅:&nbsp;<a href="#package.description">说明</a></p>
</div>
<div class="contentContainer">
<ul class="blockList">
<li class="blockList">
<table class="typeSummary" border="0" cellpadding="3" cellspacing="0" summary="接口概要表, 列表接口和解释">
<caption><span>接口概要</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">接口</th>
<th class="colLast" scope="col">说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/busline/BusLineSearch.OnBusLineSearchListener.html" title="com.amap.api.services.busline中的接口">BusLineSearch.OnBusLineSearchListener</a></td>
<td class="colLast">
<div class="block">此接口定义了公交线路查询异步处理回调接口。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/busline/BusStationSearch.OnBusStationSearchListener.html" title="com.amap.api.services.busline中的接口">BusStationSearch.OnBusStationSearchListener</a></td>
<td class="colLast">
<div class="block">此接口定义了公交站点查询异步处理回调接口。</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="typeSummary" border="0" cellpadding="3" cellspacing="0" summary="类概要表, 列表类和解释">
<caption><span>类概要</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">类</th>
<th class="colLast" scope="col">说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/busline/BusLineItem.html" title="com.amap.api.services.busline中的类">BusLineItem</a></td>
<td class="colLast">
<div class="block">此类定义了公交线路信息。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/busline/BusLineQuery.html" title="com.amap.api.services.busline中的类">BusLineQuery</a></td>
<td class="colLast">
<div class="block">此类定义了公交线路搜索的关键字、类别及城市。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/busline/BusLineResult.html" title="com.amap.api.services.busline中的类">BusLineResult</a></td>
<td class="colLast">
<div class="block">公交线路搜索结果是分页显示的，从第0页开始，每页默认显示20个BusLineItem。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/busline/BusLineSearch.html" title="com.amap.api.services.busline中的类">BusLineSearch</a></td>
<td class="colLast">
<div class="block">本类为公交线路搜索的“入口”类，定义此类，开始搜索。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/busline/BusStationItem.html" title="com.amap.api.services.busline中的类">BusStationItem</a></td>
<td class="colLast">
<div class="block">此类定义了公交站点信息。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/busline/BusStationQuery.html" title="com.amap.api.services.busline中的类">BusStationQuery</a></td>
<td class="colLast">
<div class="block">此类定义了公交站点搜索的关键字和城市。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/busline/BusStationResult.html" title="com.amap.api.services.busline中的类">BusStationResult</a></td>
<td class="colLast">
<div class="block">公交站点搜索结果是分页显示的，从第0页开始，每页默认显示20个BusStationItem。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/busline/BusStationSearch.html" title="com.amap.api.services.busline中的类">BusStationSearch</a></td>
<td class="colLast">
<div class="block">本类为公交站点搜索的“入口”类，定义此类，开始搜索。</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="typeSummary" border="0" cellpadding="3" cellspacing="0" summary="枚举概要表, 列表枚举和解释">
<caption><span>枚举概要</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">枚举</th>
<th class="colLast" scope="col">说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/amap/api/services/busline/BusLineQuery.SearchType.html" title="com.amap.api.services.busline中的枚举">BusLineQuery.SearchType</a></td>
<td class="colLast">
<div class="block">定义公交线路搜索类型。</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
<a name="package.description">
<!--   -->
</a>
<h2 title="程序包com.amap.api.services.busline的说明">程序包com.amap.api.services.busline的说明</h2>
<div class="block"><p>
公交线路和公交站点查询包。
</p></div>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li class="navBarCell1Rev">程序包</li>
<li>类</li>
<li><a href="package-use.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个程序包</li>
<li><a href="../../../../../com/amap/api/services/cloud/package-summary.html">下一个程序包</a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/busline/package-summary.html" target="_top">框架</a></li>
<li><a href="package-summary.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
