<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>BusLineItem</title>
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="BusLineItem";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10};
var tabs = {65535:["t0","所有方法"],2:["t2","实例方法"],8:["t4","具体方法"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/BusLineItem.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个类</li>
<li><a href="../../../../../com/amap/api/services/busline/BusLineQuery.html" title="com.amap.api.services.busline中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/busline/BusLineItem.html" target="_top">框架</a></li>
<li><a href="BusLineItem.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.amap.api.services.busline</div>
<h2 title="类 BusLineItem" class="title">类 BusLineItem</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.amap.api.services.busline.BusLineItem</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>直接已知子类:</dt>
<dd><a href="../../../../../com/amap/api/services/route/RouteBusLineItem.html" title="com.amap.api.services.route中的类">RouteBusLineItem</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">BusLineItem</span>
extends java.lang.Object</pre>
<div class="block">此类定义了公交线路信息。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>所有方法</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">实例方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">具体方法</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/busline/BusLineItem.html#equals-java.lang.Object-">equals</a></span>(java.lang.Object&nbsp;obj)</code>
<div class="block">比较两个公交线路ID是否相同。</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/busline/BusLineItem.html#getBasicPrice--">getBasicPrice</a></span>()</code>
<div class="block">返回公交线路的起步价，单位元。</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/busline/BusLineItem.html#getBounds--">getBounds</a></span>()</code>
<div class="block">返回公交线路外包矩形的左下与右上顶点坐标。</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/busline/BusLineItem.html#getBusCompany--">getBusCompany</a></span>()</code>
<div class="block">返回公交线路所属的公交公司。</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/busline/BusLineItem.html#getBusLineId--">getBusLineId</a></span>()</code>
<div class="block">返回公交线路的唯一ID。</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/busline/BusLineItem.html#getBusLineName--">getBusLineName</a></span>()</code>
<div class="block">返回公交线路的名称，包含线路编号和文字名称、类型、首发站、终点站。</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/busline/BusLineItem.html#getBusLineType--">getBusLineType</a></span>()</code>
<div class="block">返回公交线路的类型，类型为中文名称。</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../../com/amap/api/services/busline/BusStationItem.html" title="com.amap.api.services.busline中的类">BusStationItem</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/busline/BusLineItem.html#getBusStations--">getBusStations</a></span>()</code>
<div class="block">返回公交线路的站点列表。</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/busline/BusLineItem.html#getCityCode--">getCityCode</a></span>()</code>
<div class="block">返回公交线路的城市编码。</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/busline/BusLineItem.html#getDirectionsCoordinates--">getDirectionsCoordinates</a></span>()</code>
<div class="block">返回公交线路的沿途坐标，包含首发站和终点站坐标。</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/busline/BusLineItem.html#getDistance--">getDistance</a></span>()</code>
<div class="block">返回公交线路全程里程，单位千米。</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>java.util.Date</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/busline/BusLineItem.html#getFirstBusTime--">getFirstBusTime</a></span>()</code>
<div class="block">返回公交线路的首班车时间。</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>java.util.Date</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/busline/BusLineItem.html#getLastBusTime--">getLastBusTime</a></span>()</code>
<div class="block">返回公交线路的末班车时间。</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/busline/BusLineItem.html#getOriginatingStation--">getOriginatingStation</a></span>()</code>
<div class="block">返回公交线路的始发站名称。</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/busline/BusLineItem.html#getTerminalStation--">getTerminalStation</a></span>()</code>
<div class="block">返回公交线路的终点站名称。</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/busline/BusLineItem.html#getTotalPrice--">getTotalPrice</a></span>()</code>
<div class="block">返回公交线路的全程票价，单位元。</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/busline/BusLineItem.html#toString--">toString</a></span>()</code>
<div class="block">将公交线路信息转换为字符串输出。</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, finalize, getClass, hashCode, notify, notifyAll, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="getDistance--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDistance</h4>
<pre>public&nbsp;float&nbsp;getDistance()</pre>
<div class="block">返回公交线路全程里程，单位千米。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>公交线路全程里程。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="getBusLineName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBusLineName</h4>
<pre>public&nbsp;java.lang.String&nbsp;getBusLineName()</pre>
<div class="block">返回公交线路的名称，包含线路编号和文字名称、类型、首发站、终点站。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>公交线路的名称，包含线路编号和文字名称、类型、首发站、终点站。。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="getBusLineType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBusLineType</h4>
<pre>public&nbsp;java.lang.String&nbsp;getBusLineType()</pre>
<div class="block">返回公交线路的类型，类型为中文名称。
 公交线路的类型有普通公交、地铁、轻轨、 有轨电车、无轨电车、 旅游专线、 机场大巴、 社区专车、 磁悬浮列车、 轮渡、 索道交通、 其他。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>公交线路的类型</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="getCityCode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCityCode</h4>
<pre>public&nbsp;java.lang.String&nbsp;getCityCode()</pre>
<div class="block">返回公交线路的城市编码。城市编码参考请在网站“相关下载”处获取。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>公交线路的城市编码。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="getDirectionsCoordinates--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDirectionsCoordinates</h4>
<pre>public&nbsp;java.util.List&lt;<a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&gt;&nbsp;getDirectionsCoordinates()</pre>
<div class="block">返回公交线路的沿途坐标，包含首发站和终点站坐标。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>公交线路的沿途坐标。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="getBounds--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBounds</h4>
<pre>public&nbsp;java.util.List&lt;<a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&gt;&nbsp;getBounds()</pre>
<div class="block">返回公交线路外包矩形的左下与右上顶点坐标。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>公交线路外包矩形的左下右上顶点坐标。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="getBusLineId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBusLineId</h4>
<pre>public&nbsp;java.lang.String&nbsp;getBusLineId()</pre>
<div class="block">返回公交线路的唯一ID。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>公交线路的唯一ID。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="getOriginatingStation--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getOriginatingStation</h4>
<pre>public&nbsp;java.lang.String&nbsp;getOriginatingStation()</pre>
<div class="block">返回公交线路的始发站名称。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>公交线路的始发站名称。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="getTerminalStation--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTerminalStation</h4>
<pre>public&nbsp;java.lang.String&nbsp;getTerminalStation()</pre>
<div class="block">返回公交线路的终点站名称。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>公交线路的终点站名称。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="getFirstBusTime--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFirstBusTime</h4>
<pre>public&nbsp;java.util.Date&nbsp;getFirstBusTime()</pre>
<div class="block">返回公交线路的首班车时间。格式为“HHMM”,时间为24小时制(0000--2359)。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>公交线路的首班车时间。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="getLastBusTime--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLastBusTime</h4>
<pre>public&nbsp;java.util.Date&nbsp;getLastBusTime()</pre>
<div class="block">返回公交线路的末班车时间。格式为“HHMM”,时间为24小时制(0000--2359)。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>返回公交线路的末班车时间。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="getBusCompany--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBusCompany</h4>
<pre>public&nbsp;java.lang.String&nbsp;getBusCompany()</pre>
<div class="block">返回公交线路所属的公交公司。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>公交线路所属的公交公司。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="getBasicPrice--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBasicPrice</h4>
<pre>public&nbsp;float&nbsp;getBasicPrice()</pre>
<div class="block">返回公交线路的起步价，单位元。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>公交线路的起步价。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="getTotalPrice--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTotalPrice</h4>
<pre>public&nbsp;float&nbsp;getTotalPrice()</pre>
<div class="block">返回公交线路的全程票价，单位元。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>公交线路的全程票价。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="getBusStations--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBusStations</h4>
<pre>public&nbsp;java.util.List&lt;<a href="../../../../../com/amap/api/services/busline/BusStationItem.html" title="com.amap.api.services.busline中的类">BusStationItem</a>&gt;&nbsp;getBusStations()</pre>
<div class="block">返回公交线路的站点列表。此列表返回的公交站数据有ID、站名、经纬度、序号。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>公交线路的站点列表。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="equals-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>equals</h4>
<pre>public&nbsp;boolean&nbsp;equals(java.lang.Object&nbsp;obj)</pre>
<div class="block">比较两个公交线路ID是否相同。</div>
<dl>
<dt><span class="overrideSpecifyLabel">覆盖:</span></dt>
<dd><code>equals</code>&nbsp;在类中&nbsp;<code>java.lang.Object</code></dd>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>obj</code> - 查询条件。</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>查询条件是否相同。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="toString--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>toString</h4>
<pre>public&nbsp;java.lang.String&nbsp;toString()</pre>
<div class="block">将公交线路信息转换为字符串输出。</div>
<dl>
<dt><span class="overrideSpecifyLabel">覆盖:</span></dt>
<dd><code>toString</code>&nbsp;在类中&nbsp;<code>java.lang.Object</code></dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>返回公交线路名称和首末班车时间的字符串。</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/BusLineItem.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个类</li>
<li><a href="../../../../../com/amap/api/services/busline/BusLineQuery.html" title="com.amap.api.services.busline中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/busline/BusLineItem.html" target="_top">框架</a></li>
<li><a href="BusLineItem.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
