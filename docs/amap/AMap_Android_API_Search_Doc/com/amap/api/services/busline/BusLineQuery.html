<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>BusLineQuery</title>
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="BusLineQuery";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10};
var tabs = {65535:["t0","所有方法"],2:["t2","实例方法"],8:["t4","具体方法"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/BusLineQuery.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/busline/BusLineItem.html" title="com.amap.api.services.busline中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/amap/api/services/busline/BusLineQuery.SearchType.html" title="com.amap.api.services.busline中的枚举"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/busline/BusLineQuery.html" target="_top">框架</a></li>
<li><a href="BusLineQuery.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li><a href="#nested.class.summary">嵌套</a>&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.amap.api.services.busline</div>
<h2 title="类 BusLineQuery" class="title">类 BusLineQuery</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.amap.api.services.busline.BusLineQuery</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>所有已实现的接口:</dt>
<dd>java.lang.Cloneable</dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">BusLineQuery</span>
extends java.lang.Object
implements java.lang.Cloneable</pre>
<div class="block">此类定义了公交线路搜索的关键字、类别及城市。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>嵌套类概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="嵌套类概要表, 列表嵌套类和解释">
<caption><span>嵌套类</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">类和说明</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/busline/BusLineQuery.SearchType.html" title="com.amap.api.services.busline中的枚举">BusLineQuery.SearchType</a></span></code>
<div class="block">定义公交线路搜索类型。</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/busline/BusLineQuery.html#BusLineQuery-java.lang.String-com.amap.api.services.busline.BusLineQuery.SearchType-java.lang.String-">BusLineQuery</a></span>(java.lang.String&nbsp;query,
            <a href="../../../../../com/amap/api/services/busline/BusLineQuery.SearchType.html" title="com.amap.api.services.busline中的枚举">BusLineQuery.SearchType</a>&nbsp;ctgr,
            java.lang.String&nbsp;city)</code>
<div class="block">BusLineQuery构造函数。</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>所有方法</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">实例方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">具体方法</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/busline/BusLineQuery.html#equals-java.lang.Object-">equals</a></span>(java.lang.Object&nbsp;obj)</code>
<div class="block">比较两个查询条件是否相同。</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/services/busline/BusLineQuery.SearchType.html" title="com.amap.api.services.busline中的枚举">BusLineQuery.SearchType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/busline/BusLineQuery.html#getCategory--">getCategory</a></span>()</code>
<div class="block">返回查询类型。</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/busline/BusLineQuery.html#getCity--">getCity</a></span>()</code>
<div class="block">返回查询城市编码/行政区划代码/城市名称。</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/busline/BusLineQuery.html#getExtensions--">getExtensions</a></span>()</code>
<div class="block">扩展字段 base表示只返回基础数据，all表示所有数据 ，默认 base</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/busline/BusLineQuery.html#getPageNumber--">getPageNumber</a></span>()</code>
<div class="block">获得查询第几页的数据。</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/busline/BusLineQuery.html#getPageSize--">getPageSize</a></span>()</code>
<div class="block">获得查询每页的结果数目。</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/busline/BusLineQuery.html#getQueryString--">getQueryString</a></span>()</code>
<div class="block">返回查询关键字。</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/busline/BusLineQuery.html#setCategory-com.amap.api.services.busline.BusLineQuery.SearchType-">setCategory</a></span>(<a href="../../../../../com/amap/api/services/busline/BusLineQuery.SearchType.html" title="com.amap.api.services.busline中的枚举">BusLineQuery.SearchType</a>&nbsp;category)</code>
<div class="block">设置查询类型。</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/busline/BusLineQuery.html#setCity-java.lang.String-">setCity</a></span>(java.lang.String&nbsp;city)</code>
<div class="block">设置查询城市参数，参数可以为城市编码/行政区划代码/城市名称。</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/busline/BusLineQuery.html#setExtensions-java.lang.String-">setExtensions</a></span>(java.lang.String&nbsp;extensions)</code>
<div class="block">扩展字段 base表示只返回基础数据，all表示所有数据 ，默认 base</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/busline/BusLineQuery.html#setPageNumber-int-">setPageNumber</a></span>(int&nbsp;pageNumber)</code>
<div class="block">设置查询第几页。</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/busline/BusLineQuery.html#setPageSize-int-">setPageSize</a></span>(int&nbsp;pageSize)</code>
<div class="block">设置查询每页的结果数目。</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/busline/BusLineQuery.html#setQueryString-java.lang.String-">setQueryString</a></span>(java.lang.String&nbsp;queryString)</code>
<div class="block">设置查询关键字。</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="BusLineQuery-java.lang.String-com.amap.api.services.busline.BusLineQuery.SearchType-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>BusLineQuery</h4>
<pre>public&nbsp;BusLineQuery(java.lang.String&nbsp;query,
                    <a href="../../../../../com/amap/api/services/busline/BusLineQuery.SearchType.html" title="com.amap.api.services.busline中的枚举">BusLineQuery.SearchType</a>&nbsp;ctgr,
                    java.lang.String&nbsp;city)</pre>
<div class="block">BusLineQuery构造函数。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>query</code> - 查询关键字。关键字规则：多个关键字用“|”分割，“空格"表示与，"双引号" 表示不可分割。</dd>
<dd><code>ctgr</code> - 查询类型。</dd>
<dd><code>city</code> - 可选值：cityname（中文或中文全拼）、citycode、adcode。如传入null或空字符串则为“全国”</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="getCategory--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCategory</h4>
<pre>public&nbsp;<a href="../../../../../com/amap/api/services/busline/BusLineQuery.SearchType.html" title="com.amap.api.services.busline中的枚举">BusLineQuery.SearchType</a>&nbsp;getCategory()</pre>
<div class="block">返回查询类型。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>该结果的查询类型。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="getQueryString--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getQueryString</h4>
<pre>public&nbsp;java.lang.String&nbsp;getQueryString()</pre>
<div class="block">返回查询关键字。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>该结果的查询关键字。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="setQueryString-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setQueryString</h4>
<pre>public&nbsp;void&nbsp;setQueryString(java.lang.String&nbsp;queryString)</pre>
<div class="block">设置查询关键字。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>queryString</code> - 查询关键字。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="getCity--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCity</h4>
<pre>public&nbsp;java.lang.String&nbsp;getCity()</pre>
<div class="block">返回查询城市编码/行政区划代码/城市名称。城市编码参考请在网站“相关下载”处获取。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>查询城市参数。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="setCity-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCity</h4>
<pre>public&nbsp;void&nbsp;setCity(java.lang.String&nbsp;city)</pre>
<div class="block">设置查询城市参数，参数可以为城市编码/行政区划代码/城市名称。城市编码参考请在网站“相关下载”处获取。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>city</code> - 查询城市参数。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="getPageSize--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPageSize</h4>
<pre>public&nbsp;int&nbsp;getPageSize()</pre>
<div class="block">获得查询每页的结果数目。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>查询每页的结果数目。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="setPageSize-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPageSize</h4>
<pre>public&nbsp;void&nbsp;setPageSize(int&nbsp;pageSize)</pre>
<div class="block">设置查询每页的结果数目。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>pageSize</code> - 新的查询条件。默认每页显示20条结果，数目大于100按默认值。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="getPageNumber--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPageNumber</h4>
<pre>public&nbsp;int&nbsp;getPageNumber()</pre>
<div class="block">获得查询第几页的数据。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>查询第几页，从0开始。<font color="red">自 5.2.1后修改成从1开始，和iOS保持一致。</font></dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="setPageNumber-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPageNumber</h4>
<pre>public&nbsp;void&nbsp;setPageNumber(int&nbsp;pageNumber)</pre>
<div class="block">设置查询第几页。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>pageNumber</code> - 查询第几页的数据，从0开始。<font color="red">自 5.2.1后修改成从1开始，和iOS保持一致。</font></dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="setCategory-com.amap.api.services.busline.BusLineQuery.SearchType-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCategory</h4>
<pre>public&nbsp;void&nbsp;setCategory(<a href="../../../../../com/amap/api/services/busline/BusLineQuery.SearchType.html" title="com.amap.api.services.busline中的枚举">BusLineQuery.SearchType</a>&nbsp;category)</pre>
<div class="block">设置查询类型。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>category</code> - 查询类型。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="getExtensions--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getExtensions</h4>
<pre>public&nbsp;java.lang.String&nbsp;getExtensions()</pre>
<div class="block">扩展字段 base表示只返回基础数据，all表示所有数据 ，默认 base</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>7.6.0</dd>
</dl>
</li>
</ul>
<a name="setExtensions-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setExtensions</h4>
<pre>public&nbsp;void&nbsp;setExtensions(java.lang.String&nbsp;extensions)</pre>
<div class="block">扩展字段 base表示只返回基础数据，all表示所有数据 ，默认 base</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>7.6.0</dd>
</dl>
</li>
</ul>
<a name="equals-java.lang.Object-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>equals</h4>
<pre>public&nbsp;boolean&nbsp;equals(java.lang.Object&nbsp;obj)</pre>
<div class="block">比较两个查询条件是否相同。</div>
<dl>
<dt><span class="overrideSpecifyLabel">覆盖:</span></dt>
<dd><code>equals</code>&nbsp;在类中&nbsp;<code>java.lang.Object</code></dd>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>obj</code> - 查询条件。</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>查询条件是否相同。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/BusLineQuery.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/busline/BusLineItem.html" title="com.amap.api.services.busline中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/amap/api/services/busline/BusLineQuery.SearchType.html" title="com.amap.api.services.busline中的枚举"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/busline/BusLineQuery.html" target="_top">框架</a></li>
<li><a href="BusLineQuery.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li><a href="#nested.class.summary">嵌套</a>&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
