<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>程序包 com.amap.api.services.busline的使用</title>
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="\u7A0B\u5E8F\u5305 com.amap.api.services.busline\u7684\u4F7F\u7528";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li>类</li>
<li class="navBarCell1Rev">使用</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个</li>
<li>下一个</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/busline/package-use.html" target="_top">框架</a></li>
<li><a href="package-use.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 title="程序包的使用 com.amap.api.services.busline" class="title">程序包的使用<br>com.amap.api.services.busline</h1>
</div>
<div class="contentContainer">
<ul class="blockList">
<li class="blockList">
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表程序包和解释">
<caption><span>使用<a href="../../../../../com/amap/api/services/busline/package-summary.html">com.amap.api.services.busline</a>的程序包</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">程序包</th>
<th class="colLast" scope="col">说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="#com.amap.api.services.busline">com.amap.api.services.busline</a></td>
<td class="colLast">
<div class="block">
公交线路和公交站点查询包。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.amap.api.services.route">com.amap.api.services.route</a></td>
<td class="colLast">
<div class="block">
路径查询包，包含了在公交、自驾或步行路线规划中的详细信息。</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.amap.api.services.busline">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表类和解释">
<caption><span><a href="../../../../../com/amap/api/services/busline/package-summary.html">com.amap.api.services.busline</a>使用的<a href="../../../../../com/amap/api/services/busline/package-summary.html">com.amap.api.services.busline</a>中的类</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">类和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/busline/class-use/BusLineItem.html#com.amap.api.services.busline">BusLineItem</a>
<div class="block">此类定义了公交线路信息。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/busline/class-use/BusLineQuery.html#com.amap.api.services.busline">BusLineQuery</a>
<div class="block">此类定义了公交线路搜索的关键字、类别及城市。</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/busline/class-use/BusLineQuery.SearchType.html#com.amap.api.services.busline">BusLineQuery.SearchType</a>
<div class="block">定义公交线路搜索类型。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/busline/class-use/BusLineResult.html#com.amap.api.services.busline">BusLineResult</a>
<div class="block">公交线路搜索结果是分页显示的，从第0页开始，每页默认显示20个BusLineItem。</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/busline/class-use/BusLineSearch.OnBusLineSearchListener.html#com.amap.api.services.busline">BusLineSearch.OnBusLineSearchListener</a>
<div class="block">此接口定义了公交线路查询异步处理回调接口。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/busline/class-use/BusStationItem.html#com.amap.api.services.busline">BusStationItem</a>
<div class="block">此类定义了公交站点信息。</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/busline/class-use/BusStationQuery.html#com.amap.api.services.busline">BusStationQuery</a>
<div class="block">此类定义了公交站点搜索的关键字和城市。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/busline/class-use/BusStationResult.html#com.amap.api.services.busline">BusStationResult</a>
<div class="block">公交站点搜索结果是分页显示的，从第0页开始，每页默认显示20个BusStationItem。</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/busline/class-use/BusStationSearch.OnBusStationSearchListener.html#com.amap.api.services.busline">BusStationSearch.OnBusStationSearchListener</a>
<div class="block">此接口定义了公交站点查询异步处理回调接口。</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.amap.api.services.route">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表类和解释">
<caption><span><a href="../../../../../com/amap/api/services/route/package-summary.html">com.amap.api.services.route</a>使用的<a href="../../../../../com/amap/api/services/busline/package-summary.html">com.amap.api.services.busline</a>中的类</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">类和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/busline/class-use/BusLineItem.html#com.amap.api.services.route">BusLineItem</a>
<div class="block">此类定义了公交线路信息。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/busline/class-use/BusStationItem.html#com.amap.api.services.route">BusStationItem</a>
<div class="block">此类定义了公交站点信息。</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li>类</li>
<li class="navBarCell1Rev">使用</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个</li>
<li>下一个</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/busline/package-use.html" target="_top">框架</a></li>
<li><a href="package-use.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
