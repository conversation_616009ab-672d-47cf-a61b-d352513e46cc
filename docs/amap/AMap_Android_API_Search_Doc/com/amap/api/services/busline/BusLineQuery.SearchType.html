<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>BusLineQuery.SearchType</title>
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="BusLineQuery.SearchType";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9};
var tabs = {65535:["t0","所有方法"],1:["t1","静态方法"],8:["t4","具体方法"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/BusLineQuery.SearchType.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/busline/BusLineQuery.html" title="com.amap.api.services.busline中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/amap/api/services/busline/BusLineResult.html" title="com.amap.api.services.busline中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/busline/BusLineQuery.SearchType.html" target="_top">框架</a></li>
<li><a href="BusLineQuery.SearchType.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li><a href="#enum.constant.summary">枚举常量</a>&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li><a href="#enum.constant.detail">枚举常量</a>&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.amap.api.services.busline</div>
<h2 title="枚举 BusLineQuery.SearchType" class="title">枚举 BusLineQuery.SearchType</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>java.lang.Enum&lt;<a href="../../../../../com/amap/api/services/busline/BusLineQuery.SearchType.html" title="com.amap.api.services.busline中的枚举">BusLineQuery.SearchType</a>&gt;</li>
<li>
<ul class="inheritance">
<li>com.amap.api.services.busline.BusLineQuery.SearchType</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>所有已实现的接口:</dt>
<dd>java.io.Serializable, java.lang.Comparable&lt;<a href="../../../../../com/amap/api/services/busline/BusLineQuery.SearchType.html" title="com.amap.api.services.busline中的枚举">BusLineQuery.SearchType</a>&gt;</dd>
</dl>
<dl>
<dt>封闭类:</dt>
<dd><a href="../../../../../com/amap/api/services/busline/BusLineQuery.html" title="com.amap.api.services.busline中的类">BusLineQuery</a></dd>
</dl>
<hr>
<br>
<pre>public static enum <span class="typeNameLabel">BusLineQuery.SearchType</span>
extends java.lang.Enum&lt;<a href="../../../../../com/amap/api/services/busline/BusLineQuery.SearchType.html" title="com.amap.api.services.busline中的枚举">BusLineQuery.SearchType</a>&gt;</pre>
<div class="block">定义公交线路搜索类型。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== ENUM CONSTANT SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="enum.constant.summary">
<!--   -->
</a>
<h3>枚举常量概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="枚举常量概要表, 列表枚举常量和解释">
<caption><span>枚举常量</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">枚举常量和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/busline/BusLineQuery.SearchType.html#BY_LINE_ID">BY_LINE_ID</a></span></code>
<div class="block">根据线路ID搜索。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/busline/BusLineQuery.SearchType.html#BY_LINE_NAME">BY_LINE_NAME</a></span></code>
<div class="block">根据线路名称搜索。</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>所有方法</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">静态方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">具体方法</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static <a href="../../../../../com/amap/api/services/busline/BusLineQuery.SearchType.html" title="com.amap.api.services.busline中的枚举">BusLineQuery.SearchType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/busline/BusLineQuery.SearchType.html#valueOf-java.lang.String-">valueOf</a></span>(java.lang.String&nbsp;name)</code>
<div class="block">返回带有指定名称的该类型的枚举常量。</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static <a href="../../../../../com/amap/api/services/busline/BusLineQuery.SearchType.html" title="com.amap.api.services.busline中的枚举">BusLineQuery.SearchType</a>[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/busline/BusLineQuery.SearchType.html#values--">values</a></span>()</code>
<div class="block">按照声明该枚举类型的常量的顺序, 返回
包含这些常量的数组。</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Enum">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Enum</h3>
<code>clone, compareTo, equals, finalize, getDeclaringClass, hashCode, name, ordinal, toString, valueOf</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>getClass, notify, notifyAll, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ ENUM CONSTANT DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="enum.constant.detail">
<!--   -->
</a>
<h3>枚举常量详细资料</h3>
<a name="BY_LINE_ID">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BY_LINE_ID</h4>
<pre>public static final&nbsp;<a href="../../../../../com/amap/api/services/busline/BusLineQuery.SearchType.html" title="com.amap.api.services.busline中的枚举">BusLineQuery.SearchType</a> BY_LINE_ID</pre>
<div class="block">根据线路ID搜索。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.0</dd>
</dl>
</li>
</ul>
<a name="BY_LINE_NAME">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>BY_LINE_NAME</h4>
<pre>public static final&nbsp;<a href="../../../../../com/amap/api/services/busline/BusLineQuery.SearchType.html" title="com.amap.api.services.busline中的枚举">BusLineQuery.SearchType</a> BY_LINE_NAME</pre>
<div class="block">根据线路名称搜索。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="values--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>values</h4>
<pre>public static&nbsp;<a href="../../../../../com/amap/api/services/busline/BusLineQuery.SearchType.html" title="com.amap.api.services.busline中的枚举">BusLineQuery.SearchType</a>[]&nbsp;values()</pre>
<div class="block">按照声明该枚举类型的常量的顺序, 返回
包含这些常量的数组。该方法可用于迭代
常量, 如下所示:
<pre>
for (BusLineQuery.SearchType c : BusLineQuery.SearchType.values())
&nbsp;   System.out.println(c);
</pre></div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>按照声明该枚举类型的常量的顺序返回的包含这些常量的数组</dd>
</dl>
</li>
</ul>
<a name="valueOf-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>valueOf</h4>
<pre>public static&nbsp;<a href="../../../../../com/amap/api/services/busline/BusLineQuery.SearchType.html" title="com.amap.api.services.busline中的枚举">BusLineQuery.SearchType</a>&nbsp;valueOf(java.lang.String&nbsp;name)</pre>
<div class="block">返回带有指定名称的该类型的枚举常量。
字符串必须与用于声明该类型的枚举常量的
标识符<i>完全</i>匹配。(不允许有多余
的空格字符。)</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>name</code> - 要返回的枚举常量的名称。</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>返回带有指定名称的枚举常量</dd>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code>java.lang.IllegalArgumentException</code> - 如果该枚举类型没有带有指定名称的常量</dd>
<dd><code>java.lang.NullPointerException</code> - 如果参数为空值</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/BusLineQuery.SearchType.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/busline/BusLineQuery.html" title="com.amap.api.services.busline中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/amap/api/services/busline/BusLineResult.html" title="com.amap.api.services.busline中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/busline/BusLineQuery.SearchType.html" target="_top">框架</a></li>
<li><a href="BusLineQuery.SearchType.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li><a href="#enum.constant.summary">枚举常量</a>&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li><a href="#enum.constant.detail">枚举常量</a>&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
