<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>BusLineSearch</title>
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="BusLineSearch";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10};
var tabs = {65535:["t0","所有方法"],2:["t2","实例方法"],8:["t4","具体方法"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/BusLineSearch.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/busline/BusLineResult.html" title="com.amap.api.services.busline中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/amap/api/services/busline/BusLineSearch.OnBusLineSearchListener.html" title="com.amap.api.services.busline中的接口"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/busline/BusLineSearch.html" target="_top">框架</a></li>
<li><a href="BusLineSearch.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li><a href="#nested.class.summary">嵌套</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li><a href="#field.detail">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.amap.api.services.busline</div>
<h2 title="类 BusLineSearch" class="title">类 BusLineSearch</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.amap.api.services.busline.BusLineSearch</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">BusLineSearch</span>
extends java.lang.Object</pre>
<div class="block">本类为公交线路搜索的“入口”类，定义此类，开始搜索。在类BusLineSearch 中，使用BusLineQuery 类设定搜索参数。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>嵌套类概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="嵌套类概要表, 列表嵌套类和解释">
<caption><span>嵌套类</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">类和说明</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static interface&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/busline/BusLineSearch.OnBusLineSearchListener.html" title="com.amap.api.services.busline中的接口">BusLineSearch.OnBusLineSearchListener</a></span></code>
<div class="block">此接口定义了公交线路查询异步处理回调接口。</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>字段概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="字段概要表, 列表字段和解释">
<caption><span>字段</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">字段和说明</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/busline/BusLineSearch.html#EXTENSIONS_ALL">EXTENSIONS_ALL</a></span></code>
<div class="block">扩展字段all，会返回完整参数</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/busline/BusLineSearch.html#EXTENSIONS_BASE">EXTENSIONS_BASE</a></span></code>
<div class="block">扩展字段base，会返回部分参数</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/busline/BusLineSearch.html#BusLineSearch-Context-com.amap.api.services.busline.BusLineQuery-">BusLineSearch</a></span>(Context&nbsp;act,
             <a href="../../../../../com/amap/api/services/busline/BusLineQuery.html" title="com.amap.api.services.busline中的类">BusLineQuery</a>&nbsp;query)</code>
<div class="block">BusLineSearch构造函数。</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>所有方法</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">实例方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">具体方法</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/services/busline/BusLineQuery.html" title="com.amap.api.services.busline中的类">BusLineQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/busline/BusLineSearch.html#getQuery--">getQuery</a></span>()</code>
<div class="block">返回查询条件。</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/services/busline/BusLineResult.html" title="com.amap.api.services.busline中的类">BusLineResult</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/busline/BusLineSearch.html#searchBusLine--">searchBusLine</a></span>()</code>
<div class="block">搜索公交线路。</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/busline/BusLineSearch.html#searchBusLineAsyn--">searchBusLineAsyn</a></span>()</code>
<div class="block">搜索公交线路的异步处理调用。</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/busline/BusLineSearch.html#setOnBusLineSearchListener-com.amap.api.services.busline.BusLineSearch.OnBusLineSearchListener-">setOnBusLineSearchListener</a></span>(<a href="../../../../../com/amap/api/services/busline/BusLineSearch.OnBusLineSearchListener.html" title="com.amap.api.services.busline中的接口">BusLineSearch.OnBusLineSearchListener</a>&nbsp;onBusLineSearchListener)</code>
<div class="block">设置查询监听接口。</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/busline/BusLineSearch.html#setQuery-com.amap.api.services.busline.BusLineQuery-">setQuery</a></span>(<a href="../../../../../com/amap/api/services/busline/BusLineQuery.html" title="com.amap.api.services.busline中的类">BusLineQuery</a>&nbsp;query)</code>
<div class="block">设置查询条件。</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>字段详细资料</h3>
<a name="EXTENSIONS_ALL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>EXTENSIONS_ALL</h4>
<pre>public static final&nbsp;java.lang.String EXTENSIONS_ALL</pre>
<div class="block">扩展字段all，会返回完整参数</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>7.6.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.busline.BusLineSearch.EXTENSIONS_ALL">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="EXTENSIONS_BASE">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>EXTENSIONS_BASE</h4>
<pre>public static final&nbsp;java.lang.String EXTENSIONS_BASE</pre>
<div class="block">扩展字段base，会返回部分参数</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>7.6.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.busline.BusLineSearch.EXTENSIONS_BASE">常量字段值</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="BusLineSearch-Context-com.amap.api.services.busline.BusLineQuery-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>BusLineSearch</h4>
<pre>public&nbsp;BusLineSearch(Context&nbsp;act,
                     <a href="../../../../../com/amap/api/services/busline/BusLineQuery.html" title="com.amap.api.services.busline中的类">BusLineQuery</a>&nbsp;query)
              throws <a href="../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></pre>
<div class="block">BusLineSearch构造函数。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>act</code> - 当前 Activity。</dd>
<dd><code>query</code> - 公交查询条件。</dd>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code><a href="../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></code></dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="searchBusLine--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>searchBusLine</h4>
<pre>public&nbsp;<a href="../../../../../com/amap/api/services/busline/BusLineResult.html" title="com.amap.api.services.busline中的类">BusLineResult</a>&nbsp;searchBusLine()
                            throws <a href="../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></pre>
<div class="block">搜索公交线路。 
 如果此时查询条件（BusLineQuery）中的行政区划代码已定义，则查询为该城市（地区）内的所有符合条件的公交线路,否则范围为全国。根据指定查询类型和关键字搜索公交线路结果。此接口在网络连接出现问题的情况下，会抛出AMapServicesException。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>BusLineResult查询公交线路的结果，结果是分页的，通过类BusLineResult的实例可以检索每一页。</dd>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code><a href="../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></code></dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="setOnBusLineSearchListener-com.amap.api.services.busline.BusLineSearch.OnBusLineSearchListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setOnBusLineSearchListener</h4>
<pre>public&nbsp;void&nbsp;setOnBusLineSearchListener(<a href="../../../../../com/amap/api/services/busline/BusLineSearch.OnBusLineSearchListener.html" title="com.amap.api.services.busline中的接口">BusLineSearch.OnBusLineSearchListener</a>&nbsp;onBusLineSearchListener)</pre>
<div class="block">设置查询监听接口。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>onBusLineSearchListener</code> - 公交路线查询监听接口。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="searchBusLineAsyn--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>searchBusLineAsyn</h4>
<pre>public&nbsp;void&nbsp;searchBusLineAsyn()</pre>
<div class="block">搜索公交线路的异步处理调用。
 如果此时查询条件（BusLineQuery）中的行政区划代码已定义，则查询为该城市（地区）内的所有符合条件的公交线路,否则范围为全国。根据指定查询类型和关键字搜索公交线路结果。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="setQuery-com.amap.api.services.busline.BusLineQuery-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setQuery</h4>
<pre>public&nbsp;void&nbsp;setQuery(<a href="../../../../../com/amap/api/services/busline/BusLineQuery.html" title="com.amap.api.services.busline中的类">BusLineQuery</a>&nbsp;query)</pre>
<div class="block">设置查询条件。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>query</code> - 新的查询条件。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="getQuery--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getQuery</h4>
<pre>public&nbsp;<a href="../../../../../com/amap/api/services/busline/BusLineQuery.html" title="com.amap.api.services.busline中的类">BusLineQuery</a>&nbsp;getQuery()</pre>
<div class="block">返回查询条件。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>查询条件。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/BusLineSearch.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/busline/BusLineResult.html" title="com.amap.api.services.busline中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/amap/api/services/busline/BusLineSearch.OnBusLineSearchListener.html" title="com.amap.api.services.busline中的接口"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/busline/BusLineSearch.html" target="_top">框架</a></li>
<li><a href="BusLineSearch.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li><a href="#nested.class.summary">嵌套</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li><a href="#field.detail">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
