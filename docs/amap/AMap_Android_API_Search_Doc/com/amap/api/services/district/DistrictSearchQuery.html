<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>DistrictSearchQuery</title>
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="DistrictSearchQuery";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":42,"i1":10,"i2":10,"i3":42,"i4":10,"i5":10,"i6":10,"i7":10,"i8":42,"i9":42,"i10":10,"i11":42,"i12":10,"i13":10,"i14":10,"i15":42,"i16":42,"i17":10};
var tabs = {65535:["t0","所有方法"],2:["t2","实例方法"],8:["t4","具体方法"],32:["t6","已过时的方法"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/DistrictSearchQuery.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/district/DistrictSearch.OnDistrictSearchListener.html" title="com.amap.api.services.district中的接口"><span class="typeNameLink">上一个类</span></a></li>
<li>下一个类</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/district/DistrictSearchQuery.html" target="_top">框架</a></li>
<li><a href="DistrictSearchQuery.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li><a href="#field.summary">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li><a href="#field.detail">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.amap.api.services.district</div>
<h2 title="类 DistrictSearchQuery" class="title">类 DistrictSearchQuery</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.amap.api.services.district.DistrictSearchQuery</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>所有已实现的接口:</dt>
<dd>java.lang.Cloneable</dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">DistrictSearchQuery</span>
extends java.lang.Object
implements java.lang.Cloneable</pre>
<div class="block">此类定义了行政区划搜索的参数。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.3.0</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>字段概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="字段概要表, 列表字段和解释">
<caption><span>字段</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">字段和说明</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/district/DistrictSearchQuery.html#KEYWORDS_BUSINESS">KEYWORDS_BUSINESS</a></span></code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;
<div class="block"><span class="deprecationComment">3.6.1 不再需要此参数限制</span></div>
</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/district/DistrictSearchQuery.html#KEYWORDS_CITY">KEYWORDS_CITY</a></span></code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;
<div class="block"><span class="deprecationComment">3.6.1 不再需要此参数限制</span></div>
</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/district/DistrictSearchQuery.html#KEYWORDS_COUNTRY">KEYWORDS_COUNTRY</a></span></code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;
<div class="block"><span class="deprecationComment">3.6.1 不再需要此参数限制</span></div>
</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/district/DistrictSearchQuery.html#KEYWORDS_DISTRICT">KEYWORDS_DISTRICT</a></span></code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;
<div class="block"><span class="deprecationComment">3.6.1 不再需要此参数限制</span></div>
</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/district/DistrictSearchQuery.html#KEYWORDS_PROVINCE">KEYWORDS_PROVINCE</a></span></code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;
<div class="block"><span class="deprecationComment">3.6.1 不再需要此参数限制</span></div>
</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/district/DistrictSearchQuery.html#DistrictSearchQuery--">DistrictSearchQuery</a></span>()</code>
<div class="block">构造 DistrictSearchQuery 对象。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/district/DistrictSearchQuery.html#DistrictSearchQuery-java.lang.String-java.lang.String-int-">DistrictSearchQuery</a></span>(java.lang.String&nbsp;keywords,
                   java.lang.String&nbsp;keywordsLevel,
                   int&nbsp;pageNum)</code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;
<div class="block"><span class="deprecationComment">自V3.6.1废除此接口，建议用 <a href="../../../../../com/amap/api/services/district/DistrictSearchQuery.html#DistrictSearchQuery--"><code>DistrictSearchQuery.DistrictSearchQuery()</code></a>  构造。</span></div>
</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/district/DistrictSearchQuery.html#DistrictSearchQuery-java.lang.String-java.lang.String-int-boolean-int-">DistrictSearchQuery</a></span>(java.lang.String&nbsp;keywords,
                   java.lang.String&nbsp;keywordsLevel,
                   int&nbsp;pageNum,
                   boolean&nbsp;showChild,
                   int&nbsp;pageSize)</code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;
<div class="block"><span class="deprecationComment">自V3.6.1废除此接口，建议用 <a href="../../../../../com/amap/api/services/district/DistrictSearchQuery.html#DistrictSearchQuery--"><code>DistrictSearchQuery.DistrictSearchQuery()</code></a> 构造。</span></div>
</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>所有方法</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">实例方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">具体方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t6" class="tableTab"><span><a href="javascript:show(32);">已过时的方法</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/district/DistrictSearchQuery.html#checkLevels--">checkLevels</a></span>()</code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;
<div class="block"><span class="deprecationComment">3.6.1 不再需要此参数限制</span></div>
</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/district/DistrictSearchQuery.html#equals-java.lang.Object-">equals</a></span>(java.lang.Object&nbsp;obj)</code>
<div class="block">比较两个查询条件是否相同。</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/district/DistrictSearchQuery.html#getKeywords--">getKeywords</a></span>()</code>
<div class="block">返回查询时所用字符串关键字。</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/district/DistrictSearchQuery.html#getKeywordsLevel--">getKeywordsLevel</a></span>()</code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;
<div class="block"><span class="deprecationComment">3.6.1 不再需要此参数限制</span></div>
</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/district/DistrictSearchQuery.html#getPageNum--">getPageNum</a></span>()</code>
<div class="block">获取设置查询的是第几页，从0开始。</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/district/DistrictSearchQuery.html#getPageSize--">getPageSize</a></span>()</code>
<div class="block">返回查询页面的结果数目。</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/district/DistrictSearchQuery.html#getSubDistrict--">getSubDistrict</a></span>()</code>
<div class="block">是否返回下级区划。</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/district/DistrictSearchQuery.html#isShowBoundary--">isShowBoundary</a></span>()</code>
<div class="block">返回行政区域查询是否返回边界值。</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/district/DistrictSearchQuery.html#isShowBusinessArea--">isShowBusinessArea</a></span>()</code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;
<div class="block"><span class="deprecationComment">自 5.1.0 废弃，用率极低，而且会影响精准度，取消商圈（biz_area）级别数据，用街道（street）顶替商圈位置</span></div>
</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/district/DistrictSearchQuery.html#isShowChild--">isShowChild</a></span>()</code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;
<div class="block"><span class="deprecationComment">自 7.1.0 废弃，返回下级行政区划参考 <a href="../../../../../com/amap/api/services/district/DistrictSearchQuery.html#getSubDistrict--"><code>DistrictSearchQuery.getSubDistrict()</code></a></span></div>
</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/district/DistrictSearchQuery.html#setKeywords-java.lang.String-">setKeywords</a></span>(java.lang.String&nbsp;mKeywords)</code>
<div class="block">设置查询字符串关键字。</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/district/DistrictSearchQuery.html#setKeywordsLevel-java.lang.String-">setKeywordsLevel</a></span>(java.lang.String&nbsp;mLevel)</code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;
<div class="block"><span class="deprecationComment">3.6.1 不再需要此参数限制</span></div>
</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/district/DistrictSearchQuery.html#setPageNum-int-">setPageNum</a></span>(int&nbsp;mPageNum)</code>
<div class="block">设置查询第几页的结果数目。</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/district/DistrictSearchQuery.html#setPageSize-int-">setPageSize</a></span>(int&nbsp;mPageSize)</code>
<div class="block">设置查询每页的结果数目， 默认值是20 条，取值范围在[1-50]。</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/district/DistrictSearchQuery.html#setShowBoundary-boolean-">setShowBoundary</a></span>(boolean&nbsp;isBoundary)</code>
<div class="block">设置行政区域搜索是否返回边界值，true为返回，false为不返回。</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/district/DistrictSearchQuery.html#setShowBusinessArea-boolean-">setShowBusinessArea</a></span>(boolean&nbsp;showbiz)</code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;
<div class="block"><span class="deprecationComment">自 5.1.0 废弃，使用率极低，而且会影响精准度，取消商圈（biz_area）级别数据，用街道（street）顶替商圈位置</span></div>
</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/district/DistrictSearchQuery.html#setShowChild-boolean-">setShowChild</a></span>(boolean&nbsp;mShowChild)</code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;
<div class="block"><span class="deprecationComment">自 7.1.0 废弃，设置下级行政区划参考 <a href="../../../../../com/amap/api/services/district/DistrictSearchQuery.html#setSubDistrict-int-"><code>DistrictSearchQuery.setSubDistrict(int)</code></a> ()}</span></div>
</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/district/DistrictSearchQuery.html#setSubDistrict-int-">setSubDistrict</a></span>(int&nbsp;subDistrict)</code>
<div class="block">设置显示下级行政区级数（行政区级别包括：国家、省/直辖市、市、区/县、乡镇/街道多级数据）</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>字段详细资料</h3>
<a name="KEYWORDS_COUNTRY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>KEYWORDS_COUNTRY</h4>
<pre>public static final&nbsp;java.lang.String KEYWORDS_COUNTRY</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;<span class="deprecationComment">3.6.1 不再需要此参数限制</span></div>
<div class="block">行政区划级别-国家。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.3.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.district.DistrictSearchQuery.KEYWORDS_COUNTRY">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="KEYWORDS_PROVINCE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>KEYWORDS_PROVINCE</h4>
<pre>public static final&nbsp;java.lang.String KEYWORDS_PROVINCE</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;<span class="deprecationComment">3.6.1 不再需要此参数限制</span></div>
<div class="block">行政区划级别-省。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.3.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.district.DistrictSearchQuery.KEYWORDS_PROVINCE">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="KEYWORDS_CITY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>KEYWORDS_CITY</h4>
<pre>public static final&nbsp;java.lang.String KEYWORDS_CITY</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;<span class="deprecationComment">3.6.1 不再需要此参数限制</span></div>
<div class="block">行政区划级别-市。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.3.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.district.DistrictSearchQuery.KEYWORDS_CITY">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="KEYWORDS_DISTRICT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>KEYWORDS_DISTRICT</h4>
<pre>public static final&nbsp;java.lang.String KEYWORDS_DISTRICT</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;<span class="deprecationComment">3.6.1 不再需要此参数限制</span></div>
<div class="block">行政区划级别-区/县。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.3.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.district.DistrictSearchQuery.KEYWORDS_DISTRICT">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="KEYWORDS_BUSINESS">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>KEYWORDS_BUSINESS</h4>
<pre>public static final&nbsp;java.lang.String KEYWORDS_BUSINESS</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;<span class="deprecationComment">3.6.1 不再需要此参数限制</span></div>
<div class="block">行政区划级别-商圈。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.3.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.district.DistrictSearchQuery.KEYWORDS_BUSINESS">常量字段值</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="DistrictSearchQuery--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DistrictSearchQuery</h4>
<pre>public&nbsp;DistrictSearchQuery()</pre>
<div class="block">构造 DistrictSearchQuery 对象。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.3.0</dd>
</dl>
</li>
</ul>
<a name="DistrictSearchQuery-java.lang.String-java.lang.String-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DistrictSearchQuery</h4>
<pre>public&nbsp;DistrictSearchQuery(java.lang.String&nbsp;keywords,
                           java.lang.String&nbsp;keywordsLevel,
                           int&nbsp;pageNum)</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;<span class="deprecationComment">自V3.6.1废除此接口，建议用 <a href="../../../../../com/amap/api/services/district/DistrictSearchQuery.html#DistrictSearchQuery--"><code>DistrictSearchQuery.DistrictSearchQuery()</code></a>  构造。</span></div>
<div class="block">根据给定的参数来构造一个 DistrictSearchQuery 的新对象。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>keywords</code> - 关键词，支持：行政区名称、城市编码、区域编码。</dd>
<dd><code>keywordsLevel</code> - 关键词级别，可参见 DistrictSearch 中的常量定义。（V3.6.1以后此参数已无效）</dd>
<dd><code>pageNum</code> - 查询第几页的结果，从0开始，默认为0，最大为第99页。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.3.0</dd>
</dl>
</li>
</ul>
<a name="DistrictSearchQuery-java.lang.String-java.lang.String-int-boolean-int-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>DistrictSearchQuery</h4>
<pre>public&nbsp;DistrictSearchQuery(java.lang.String&nbsp;keywords,
                           java.lang.String&nbsp;keywordsLevel,
                           int&nbsp;pageNum,
                           boolean&nbsp;showChild,
                           int&nbsp;pageSize)</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;<span class="deprecationComment">自V3.6.1废除此接口，建议用 <a href="../../../../../com/amap/api/services/district/DistrictSearchQuery.html#DistrictSearchQuery--"><code>DistrictSearchQuery.DistrictSearchQuery()</code></a> 构造。</span></div>
<div class="block">根据给定的参数来构造一个 DistrictSearchQuery 的新对象。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>keywords</code> - 关键词，支持：行政区名称、城市编码、区域编码。</dd>
<dd><code>keywordsLevel</code> - 关键词级别，可参见 DistrictSearch 中的常量定义。（V3.6.1以后此参数已无效）</dd>
<dd><code>pageNum</code> - 查询第几页的结果，从0开始，默认为0，最大为第99页。</dd>
<dd><code>showChild</code> - 是否显示下一级别的行政区。</dd>
<dd><code>pageSize</code> - 每页显示多少个数据，默认值是20 条，取值范围在[1-50] 。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.3.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="setShowBoundary-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setShowBoundary</h4>
<pre>public&nbsp;void&nbsp;setShowBoundary(boolean&nbsp;isBoundary)</pre>
<div class="block">设置行政区域搜索是否返回边界值，true为返回，false为不返回。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>isBoundary</code> - – true/false。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.5.0</dd>
</dl>
</li>
</ul>
<a name="isShowBoundary--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isShowBoundary</h4>
<pre>public&nbsp;boolean&nbsp;isShowBoundary()</pre>
<div class="block">返回行政区域查询是否返回边界值。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>true为返回边界值，false为不返回。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.5.0</dd>
</dl>
</li>
</ul>
<a name="getPageNum--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPageNum</h4>
<pre>public&nbsp;int&nbsp;getPageNum()</pre>
<div class="block">获取设置查询的是第几页，从0开始。<font color="red">自 5.2.1后修改成从1开始，和iOS保持一致。</font></div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>查询的是第几页。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.3.0</dd>
</dl>
</li>
</ul>
<a name="setPageNum-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPageNum</h4>
<pre>public&nbsp;void&nbsp;setPageNum(int&nbsp;mPageNum)</pre>
<div class="block">设置查询第几页的结果数目。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>mPageNum</code> - 查询第几页的结果，从0开始。<font color="red">自 5.2.1后修改成从1开始，和iOS保持一致。</font></dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.3.0</dd>
</dl>
</li>
</ul>
<a name="getPageSize--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPageSize</h4>
<pre>public&nbsp;int&nbsp;getPageSize()</pre>
<div class="block">返回查询页面的结果数目。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>查询页面的结果数目。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.3.0</dd>
</dl>
</li>
</ul>
<a name="setPageSize-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPageSize</h4>
<pre>public&nbsp;void&nbsp;setPageSize(int&nbsp;mPageSize)</pre>
<div class="block">设置查询每页的结果数目， 默认值是20 条，取值范围在[1-50]。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>mPageSize</code> - 每页的最大结果数目。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.3.0</dd>
</dl>
</li>
</ul>
<a name="getKeywords--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getKeywords</h4>
<pre>public&nbsp;java.lang.String&nbsp;getKeywords()</pre>
<div class="block">返回查询时所用字符串关键字。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>查询字符串关键字。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.3.0</dd>
</dl>
</li>
</ul>
<a name="setKeywords-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setKeywords</h4>
<pre>public&nbsp;void&nbsp;setKeywords(java.lang.String&nbsp;mKeywords)</pre>
<div class="block">设置查询字符串关键字。关键词支持：行政区名称、城市编码、区域编码。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>mKeywords</code> - 查询字符串关键字。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.3.0</dd>
</dl>
</li>
</ul>
<a name="getKeywordsLevel--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getKeywordsLevel</h4>
<pre>public&nbsp;java.lang.String&nbsp;getKeywordsLevel()</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;<span class="deprecationComment">3.6.1 不再需要此参数限制</span></div>
<div class="block">返回查询关键字的级别。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>查询关键字的级别。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.3.0</dd>
</dl>
</li>
</ul>
<a name="setKeywordsLevel-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setKeywordsLevel</h4>
<pre>public&nbsp;void&nbsp;setKeywordsLevel(java.lang.String&nbsp;mLevel)</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;<span class="deprecationComment">3.6.1 不再需要此参数限制</span></div>
<div class="block">设置查询关键字的级别。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>mLevel</code> - 查询关键字的级别，请参见 <a href="../../../../../com/amap/api/services/district/DistrictSearch.html" title="com.amap.api.services.district中的类"><code>DistrictSearch</code></a> 中定义的常量。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.3.0</dd>
</dl>
</li>
</ul>
<a name="isShowChild--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isShowChild</h4>
<pre>public&nbsp;boolean&nbsp;isShowChild()</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;<span class="deprecationComment">自 7.1.0 废弃，返回下级行政区划参考 <a href="../../../../../com/amap/api/services/district/DistrictSearchQuery.html#getSubDistrict--"><code>DistrictSearchQuery.getSubDistrict()</code></a></span></div>
<div class="block">是否返回下级区划。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>true表示返回下级区划，反之，不返回下级区划。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.3.0</dd>
</dl>
</li>
</ul>
<a name="setShowChild-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setShowChild</h4>
<pre>public&nbsp;void&nbsp;setShowChild(boolean&nbsp;mShowChild)</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;<span class="deprecationComment">自 7.1.0 废弃，设置下级行政区划参考 <a href="../../../../../com/amap/api/services/district/DistrictSearchQuery.html#setSubDistrict-int-"><code>DistrictSearchQuery.setSubDistrict(int)</code></a> ()}</span></div>
<div class="block">设置是否返回下级区划。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>mShowChild</code> - 是否返回下级区划，默认为true，返回下级区划。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.3.0</dd>
</dl>
</li>
</ul>
<a name="getSubDistrict--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSubDistrict</h4>
<pre>public&nbsp;int&nbsp;getSubDistrict()</pre>
<div class="block">是否返回下级区划。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>0:不返回下级行政区,1：返回下一级行政区;2：返回下两级行政区;3：返回下三级行政区。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>7.1.0</dd>
</dl>
</li>
</ul>
<a name="setSubDistrict-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSubDistrict</h4>
<pre>public&nbsp;void&nbsp;setSubDistrict(int&nbsp;subDistrict)</pre>
<div class="block">设置显示下级行政区级数（行政区级别包括：国家、省/直辖市、市、区/县、乡镇/街道多级数据）</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>subDistrict</code> - 0:不返回下级行政区,1：返回下一级行政区;2：返回下两级行政区;3：返回下三级行政区。
 需要在此特殊说明，目前部分城市和省直辖县因为没有区县的概念，故在市级下方直接显示街道。
 例如：广东-东莞、海南-文昌市</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>7.1.0</dd>
</dl>
</li>
</ul>
<a name="isShowBusinessArea--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isShowBusinessArea</h4>
<pre>public&nbsp;boolean&nbsp;isShowBusinessArea()</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;<span class="deprecationComment">自 5.1.0 废弃，用率极低，而且会影响精准度，取消商圈（biz_area）级别数据，用街道（street）顶替商圈位置</span></div>
<div class="block">返回是否显示商圈。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>true表示下级行政区划会返回商圈（keyword级别为区/县）。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.5.0</dd>
</dl>
</li>
</ul>
<a name="setShowBusinessArea-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setShowBusinessArea</h4>
<pre>public&nbsp;void&nbsp;setShowBusinessArea(boolean&nbsp;showbiz)</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;<span class="deprecationComment">自 5.1.0 废弃，使用率极低，而且会影响精准度，取消商圈（biz_area）级别数据，用街道（street）顶替商圈位置</span></div>
<div class="block">设置是否显示商圈</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>showbiz</code> - 可选为true/false，为了能够精准的定位到街道，特别是在快递、物流、送餐等场景下，强烈建议将此设置为false。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.5.0</dd>
</dl>
</li>
</ul>
<a name="checkLevels--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>checkLevels</h4>
<pre>public&nbsp;boolean&nbsp;checkLevels()</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;<span class="deprecationComment">3.6.1 不再需要此参数限制</span></div>
<div class="block">检查行政区划级别参数是否正确</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>行政区划级别参数是否正确</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.3.0</dd>
</dl>
</li>
</ul>
<a name="equals-java.lang.Object-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>equals</h4>
<pre>public&nbsp;boolean&nbsp;equals(java.lang.Object&nbsp;obj)</pre>
<div class="block">比较两个查询条件是否相同。</div>
<dl>
<dt><span class="overrideSpecifyLabel">覆盖:</span></dt>
<dd><code>equals</code>&nbsp;在类中&nbsp;<code>java.lang.Object</code></dd>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>obj</code> - 查询条件。</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>查询条件是否相同。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.3.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/DistrictSearchQuery.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/district/DistrictSearch.OnDistrictSearchListener.html" title="com.amap.api.services.district中的接口"><span class="typeNameLink">上一个类</span></a></li>
<li>下一个类</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/district/DistrictSearchQuery.html" target="_top">框架</a></li>
<li><a href="DistrictSearchQuery.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li><a href="#field.summary">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li><a href="#field.detail">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
