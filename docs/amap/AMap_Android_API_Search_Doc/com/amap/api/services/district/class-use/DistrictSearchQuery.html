<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>类 com.amap.api.services.district.DistrictSearchQuery的使用</title>
<link rel="stylesheet" type="text/css" href="../../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="\u7C7B com.amap.api.services.district.DistrictSearchQuery\u7684\u4F7F\u7528";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../../overview-summary.html">概览</a></li>
<li><a href="../package-summary.html">程序包</a></li>
<li><a href="../../../../../../com/amap/api/services/district/DistrictSearchQuery.html" title="com.amap.api.services.district中的类">类</a></li>
<li class="navBarCell1Rev">使用</li>
<li><a href="../../../../../../overview-tree.html">树</a></li>
<li><a href="../../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个</li>
<li>下一个</li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?com/amap/api/services/district/class-use/DistrictSearchQuery.html" target="_top">框架</a></li>
<li><a href="DistrictSearchQuery.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h2 title="类的使用 com.amap.api.services.district.DistrictSearchQuery" class="title">类的使用<br>com.amap.api.services.district.DistrictSearchQuery</h2>
</div>
<div class="classUseContainer">
<ul class="blockList">
<li class="blockList">
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表程序包和解释">
<caption><span>使用<a href="../../../../../../com/amap/api/services/district/DistrictSearchQuery.html" title="com.amap.api.services.district中的类">DistrictSearchQuery</a>的程序包</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">程序包</th>
<th class="colLast" scope="col">说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="#com.amap.api.services.district">com.amap.api.services.district</a></td>
<td class="colLast">
<div class="block">
行政区划查询包，查询某个行政级别的详细信息。</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<ul class="blockList">
<li class="blockList"><a name="com.amap.api.services.district">
<!--   -->
</a>
<h3><a href="../../../../../../com/amap/api/services/district/package-summary.html">com.amap.api.services.district</a>中<a href="../../../../../../com/amap/api/services/district/DistrictSearchQuery.html" title="com.amap.api.services.district中的类">DistrictSearchQuery</a>的使用</h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表方法和解释">
<caption><span>返回<a href="../../../../../../com/amap/api/services/district/DistrictSearchQuery.html" title="com.amap.api.services.district中的类">DistrictSearchQuery</a>的<a href="../../../../../../com/amap/api/services/district/package-summary.html">com.amap.api.services.district</a>中的方法</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/services/district/DistrictSearchQuery.html" title="com.amap.api.services.district中的类">DistrictSearchQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">DistrictResult.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/district/DistrictResult.html#getQuery--">getQuery</a></span>()</code>
<div class="block">返回查询结果对应的查询参数。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/services/district/DistrictSearchQuery.html" title="com.amap.api.services.district中的类">DistrictSearchQuery</a></code></td>
<td class="colLast"><span class="typeNameLabel">DistrictSearch.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/district/DistrictSearch.html#getQuery--">getQuery</a></span>()</code>
<div class="block">返回查询条件。</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表方法和解释">
<caption><span>参数类型为<a href="../../../../../../com/amap/api/services/district/DistrictSearchQuery.html" title="com.amap.api.services.district中的类">DistrictSearchQuery</a>的<a href="../../../../../../com/amap/api/services/district/package-summary.html">com.amap.api.services.district</a>中的方法</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">DistrictSearch.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/district/DistrictSearch.html#setQuery-com.amap.api.services.district.DistrictSearchQuery-">setQuery</a></span>(<a href="../../../../../../com/amap/api/services/district/DistrictSearchQuery.html" title="com.amap.api.services.district中的类">DistrictSearchQuery</a>&nbsp;districtSearchQuery)</code>
<div class="block">设置查询条件。</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表构造器和解释">
<caption><span>参数类型为<a href="../../../../../../com/amap/api/services/district/DistrictSearchQuery.html" title="com.amap.api.services.district中的类">DistrictSearchQuery</a>的<a href="../../../../../../com/amap/api/services/district/package-summary.html">com.amap.api.services.district</a>中的构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/district/DistrictResult.html#DistrictResult-com.amap.api.services.district.DistrictSearchQuery-java.util.ArrayList-">DistrictResult</a></span>(<a href="../../../../../../com/amap/api/services/district/DistrictSearchQuery.html" title="com.amap.api.services.district中的类">DistrictSearchQuery</a>&nbsp;query,
              java.util.ArrayList&lt;<a href="../../../../../../com/amap/api/services/district/DistrictItem.html" title="com.amap.api.services.district中的类">DistrictItem</a>&gt;&nbsp;mDistricts)</code>
<div class="block">依据参数构造行政区域查询结果</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../../overview-summary.html">概览</a></li>
<li><a href="../package-summary.html">程序包</a></li>
<li><a href="../../../../../../com/amap/api/services/district/DistrictSearchQuery.html" title="com.amap.api.services.district中的类">类</a></li>
<li class="navBarCell1Rev">使用</li>
<li><a href="../../../../../../overview-tree.html">树</a></li>
<li><a href="../../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个</li>
<li>下一个</li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?com/amap/api/services/district/class-use/DistrictSearchQuery.html" target="_top">框架</a></li>
<li><a href="DistrictSearchQuery.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
