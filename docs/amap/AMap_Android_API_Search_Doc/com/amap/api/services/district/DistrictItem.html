<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>DistrictItem</title>
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="DistrictItem";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10};
var tabs = {65535:["t0","所有方法"],2:["t2","实例方法"],8:["t4","具体方法"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/DistrictItem.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个类</li>
<li><a href="../../../../../com/amap/api/services/district/DistrictResult.html" title="com.amap.api.services.district中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/district/DistrictItem.html" target="_top">框架</a></li>
<li><a href="DistrictItem.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.amap.api.services.district</div>
<h2 title="类 DistrictItem" class="title">类 DistrictItem</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.amap.api.services.district.DistrictItem</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public final class <span class="typeNameLabel">DistrictItem</span>
extends java.lang.Object</pre>
<div class="block">行政区信息类。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.3.0</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>所有方法</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">实例方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">具体方法</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>java.lang.String[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/district/DistrictItem.html#districtBoundary--">districtBoundary</a></span>()</code>
<div class="block">以字符串数组形式返回行政区划边界值。</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/district/DistrictItem.html#equals-java.lang.Object-">equals</a></span>(java.lang.Object&nbsp;obj)</code>
<div class="block">比较两个行政区划是否相同</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/district/DistrictItem.html#getAdcode--">getAdcode</a></span>()</code>
<div class="block">返回区域编码。</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/district/DistrictItem.html#getCenter--">getCenter</a></span>()</code>
<div class="block">返回行政区域规划中心点的经纬度坐标。</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/district/DistrictItem.html#getCitycode--">getCitycode</a></span>()</code>
<div class="block">返回城市编码，如果行政区为省或者国家，此字段无返回值。</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/district/DistrictItem.html#getLevel--">getLevel</a></span>()</code>
<div class="block">返回当前行政区划的级别。</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/district/DistrictItem.html#getName--">getName</a></span>()</code>
<div class="block">返回行政区域的名称。</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../../com/amap/api/services/district/DistrictItem.html" title="com.amap.api.services.district中的类">DistrictItem</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/district/DistrictItem.html#getSubDistrict--">getSubDistrict</a></span>()</code>
<div class="block">返回下一级行政区划的结果，如果无下级行政区划，返回null。</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/district/DistrictItem.html#toString--">toString</a></span>()</code>
<div class="block">将行政区信息转换成字符串输出
 返回行政区的城市编码、区域编码、行政区名称、中心点、行政区划级别</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, finalize, getClass, hashCode, notify, notifyAll, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="districtBoundary--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>districtBoundary</h4>
<pre>public&nbsp;java.lang.String[]&nbsp;districtBoundary()</pre>
<div class="block">以字符串数组形式返回行政区划边界值。
 字符串拆分规则：
 经纬度，经度和纬度之间用","分隔，坐标点之间用";"分隔。例如：116.076498,40.115153;116.076603,40.115071;116.076333,40.115257;116.076498,40.115153。
 字符串数组由来：
 如果行政区包括的是群岛，则坐标点是各个岛屿的边界，各个岛屿之间的经纬度使用"|"分隔。一个字符串数组可包含多个封闭区域，一个字符串表示一个封闭区域。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>String []– 行政区划边界字符串数组</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.5.0</dd>
</dl>
</li>
</ul>
<a name="getCitycode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCitycode</h4>
<pre>public&nbsp;java.lang.String&nbsp;getCitycode()</pre>
<div class="block">返回城市编码，如果行政区为省或者国家，此字段无返回值。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>城市编码。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.3.0</dd>
</dl>
</li>
</ul>
<a name="getAdcode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAdcode</h4>
<pre>public&nbsp;java.lang.String&nbsp;getAdcode()</pre>
<div class="block">返回区域编码。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>区域编码。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.3.0</dd>
</dl>
</li>
</ul>
<a name="getName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getName</h4>
<pre>public&nbsp;java.lang.String&nbsp;getName()</pre>
<div class="block">返回行政区域的名称。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>行政区域的名称。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.3.0</dd>
</dl>
</li>
</ul>
<a name="getCenter--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCenter</h4>
<pre>public&nbsp;<a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&nbsp;getCenter()</pre>
<div class="block">返回行政区域规划中心点的经纬度坐标。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>行政区域规划中心点的经纬度坐标。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.3.0</dd>
</dl>
</li>
</ul>
<a name="getLevel--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLevel</h4>
<pre>public&nbsp;java.lang.String&nbsp;getLevel()</pre>
<div class="block">返回当前行政区划的级别。匹配级别共有五个级别。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>当前行政区划的级别。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.3.0</dd>
</dl>
</li>
</ul>
<a name="getSubDistrict--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSubDistrict</h4>
<pre>public&nbsp;java.util.List&lt;<a href="../../../../../com/amap/api/services/district/DistrictItem.html" title="com.amap.api.services.district中的类">DistrictItem</a>&gt;&nbsp;getSubDistrict()</pre>
<div class="block">返回下一级行政区划的结果，如果无下级行政区划，返回null。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>下一级行政区划的结果，如果无下级行政区划，返回null。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.3.0</dd>
</dl>
</li>
</ul>
<a name="equals-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>equals</h4>
<pre>public&nbsp;boolean&nbsp;equals(java.lang.Object&nbsp;obj)</pre>
<div class="block">比较两个行政区划是否相同</div>
<dl>
<dt><span class="overrideSpecifyLabel">覆盖:</span></dt>
<dd><code>equals</code>&nbsp;在类中&nbsp;<code>java.lang.Object</code></dd>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>obj</code> - 待比较的对象</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>相等返回true，不相等返回false。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.3.0</dd>
</dl>
</li>
</ul>
<a name="toString--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>toString</h4>
<pre>public&nbsp;java.lang.String&nbsp;toString()</pre>
<div class="block">将行政区信息转换成字符串输出
 返回行政区的城市编码、区域编码、行政区名称、中心点、行政区划级别</div>
<dl>
<dt><span class="overrideSpecifyLabel">覆盖:</span></dt>
<dd><code>toString</code>&nbsp;在类中&nbsp;<code>java.lang.Object</code></dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.3.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/DistrictItem.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个类</li>
<li><a href="../../../../../com/amap/api/services/district/DistrictResult.html" title="com.amap.api.services.district中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/district/DistrictItem.html" target="_top">框架</a></li>
<li><a href="DistrictItem.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
