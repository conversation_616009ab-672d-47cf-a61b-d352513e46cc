<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>com.amap.api.services.routepoisearch</title>
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<h1 class="bar"><a href="../../../../../com/amap/api/services/routepoisearch/package-summary.html" target="classFrame">com.amap.api.services.routepoisearch</a></h1>
<div class="indexContainer">
<h2 title="接口">接口</h2>
<ul title="接口">
<li><a href="RoutePOISearch.OnRoutePOISearchListener.html" title="com.amap.api.services.routepoisearch中的接口" target="classFrame"><span class="interfaceName">RoutePOISearch.OnRoutePOISearchListener</span></a></li>
</ul>
<h2 title="类">类</h2>
<ul title="类">
<li><a href="RoutePOIItem.html" title="com.amap.api.services.routepoisearch中的类" target="classFrame">RoutePOIItem</a></li>
<li><a href="RoutePOISearch.html" title="com.amap.api.services.routepoisearch中的类" target="classFrame">RoutePOISearch</a></li>
<li><a href="RoutePOISearchQuery.html" title="com.amap.api.services.routepoisearch中的类" target="classFrame">RoutePOISearchQuery</a></li>
<li><a href="RoutePOISearchResult.html" title="com.amap.api.services.routepoisearch中的类" target="classFrame">RoutePOISearchResult</a></li>
</ul>
<h2 title="枚举">枚举</h2>
<ul title="枚举">
<li><a href="RoutePOISearch.RoutePOISearchType.html" title="com.amap.api.services.routepoisearch中的枚举" target="classFrame">RoutePOISearch.RoutePOISearchType</a></li>
</ul>
</div>
</body>
</html>
