<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>RoutePOISearch</title>
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="RoutePOISearch";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10};
var tabs = {65535:["t0","所有方法"],2:["t2","实例方法"],8:["t4","具体方法"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/RoutePOISearch.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/routepoisearch/RoutePOIItem.html" title="com.amap.api.services.routepoisearch中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/amap/api/services/routepoisearch/RoutePOISearch.OnRoutePOISearchListener.html" title="com.amap.api.services.routepoisearch中的接口"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/routepoisearch/RoutePOISearch.html" target="_top">框架</a></li>
<li><a href="RoutePOISearch.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li><a href="#nested.class.summary">嵌套</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li><a href="#field.detail">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.amap.api.services.routepoisearch</div>
<h2 title="类 RoutePOISearch" class="title">类 RoutePOISearch</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.amap.api.services.routepoisearch.RoutePOISearch</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">RoutePOISearch</span>
extends java.lang.Object</pre>
<div class="block">本类为沿途POI搜索的“入口”类，定义此类，开始搜索。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.5.0</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>嵌套类概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="嵌套类概要表, 列表嵌套类和解释">
<caption><span>嵌套类</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">类和说明</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static interface&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/routepoisearch/RoutePOISearch.OnRoutePOISearchListener.html" title="com.amap.api.services.routepoisearch中的接口">RoutePOISearch.OnRoutePOISearchListener</a></span></code>
<div class="block">本类为沿途搜索POI（Point Of Interest，兴趣点）结果的异步处理回调接口。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/routepoisearch/RoutePOISearch.RoutePOISearchType.html" title="com.amap.api.services.routepoisearch中的枚举">RoutePOISearch.RoutePOISearchType</a></span></code>
<div class="block">搜索的type类型
 TypeGasStation 加油站；TypeMaintenanceStation 维修站；TypeATM 自助银行；TypeToilet 卫生间。</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>字段概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="字段概要表, 列表字段和解释">
<caption><span>字段</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">字段和说明</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/routepoisearch/RoutePOISearch.html#DrivingAvoidCongestion">DrivingAvoidCongestion</a></span></code>
<div class="block">避免拥堵。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/routepoisearch/RoutePOISearch.html#DrivingDefault">DrivingDefault</a></span></code>
<div class="block">速度优先。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/routepoisearch/RoutePOISearch.html#DrivingNoExpressways">DrivingNoExpressways</a></span></code>
<div class="block">不走快速路。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/routepoisearch/RoutePOISearch.html#DrivingNoHighAvoidCongestionSaveMoney">DrivingNoHighAvoidCongestionSaveMoney</a></span></code>
<div class="block">不走高速且躲避收费和拥堵。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/routepoisearch/RoutePOISearch.html#DrivingNoHighWay">DrivingNoHighWay</a></span></code>
<div class="block">不走高速。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/routepoisearch/RoutePOISearch.html#DrivingNoHighWaySaveMoney">DrivingNoHighWaySaveMoney</a></span></code>
<div class="block">不走高速且避免收费。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/routepoisearch/RoutePOISearch.html#DrivingSaveMoney">DrivingSaveMoney</a></span></code>
<div class="block">费用优先（不走收费路的最快道路）。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/routepoisearch/RoutePOISearch.html#DrivingSaveMoneyAvoidCongestion">DrivingSaveMoneyAvoidCongestion</a></span></code>
<div class="block">避免收费与拥堵。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/routepoisearch/RoutePOISearch.html#DrivingShortDistance">DrivingShortDistance</a></span></code>
<div class="block">距离优先。</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/routepoisearch/RoutePOISearch.html#RoutePOISearch-Context-com.amap.api.services.routepoisearch.RoutePOISearchQuery-">RoutePOISearch</a></span>(Context&nbsp;context,
              <a href="../../../../../com/amap/api/services/routepoisearch/RoutePOISearchQuery.html" title="com.amap.api.services.routepoisearch中的类">RoutePOISearchQuery</a>&nbsp;query)</code>
<div class="block">沿途搜索构造方法。</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>所有方法</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">实例方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">具体方法</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/services/routepoisearch/RoutePOISearchResult.html" title="com.amap.api.services.routepoisearch中的类">RoutePOISearchResult</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/routepoisearch/RoutePOISearch.html#searchRoutePOI--">searchRoutePOI</a></span>()</code>
<div class="block">沿途搜索同步查询接口。</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/routepoisearch/RoutePOISearch.html#searchRoutePOIAsyn--">searchRoutePOIAsyn</a></span>()</code>
<div class="block">沿途搜索POI异步查询接口。</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/routepoisearch/RoutePOISearch.html#setPoiSearchListener-com.amap.api.services.routepoisearch.RoutePOISearch.OnRoutePOISearchListener-">setPoiSearchListener</a></span>(<a href="../../../../../com/amap/api/services/routepoisearch/RoutePOISearch.OnRoutePOISearchListener.html" title="com.amap.api.services.routepoisearch中的接口">RoutePOISearch.OnRoutePOISearchListener</a>&nbsp;listener)</code>
<div class="block">设置查询监听接口。</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/routepoisearch/RoutePOISearch.html#setQuery-com.amap.api.services.routepoisearch.RoutePOISearchQuery-">setQuery</a></span>(<a href="../../../../../com/amap/api/services/routepoisearch/RoutePOISearchQuery.html" title="com.amap.api.services.routepoisearch中的类">RoutePOISearchQuery</a>&nbsp;query)</code>
<div class="block">设置查询条件。</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>字段详细资料</h3>
<a name="DrivingDefault">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DrivingDefault</h4>
<pre>public static final&nbsp;int DrivingDefault</pre>
<div class="block">速度优先。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.5.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.routepoisearch.RoutePOISearch.DrivingDefault">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="DrivingSaveMoney">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DrivingSaveMoney</h4>
<pre>public static final&nbsp;int DrivingSaveMoney</pre>
<div class="block">费用优先（不走收费路的最快道路）。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.5.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.routepoisearch.RoutePOISearch.DrivingSaveMoney">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="DrivingShortDistance">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DrivingShortDistance</h4>
<pre>public static final&nbsp;int DrivingShortDistance</pre>
<div class="block">距离优先。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.5.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.routepoisearch.RoutePOISearch.DrivingShortDistance">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="DrivingNoExpressways">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DrivingNoExpressways</h4>
<pre>public static final&nbsp;int DrivingNoExpressways</pre>
<div class="block">不走快速路。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.5.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.routepoisearch.RoutePOISearch.DrivingNoExpressways">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="DrivingAvoidCongestion">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DrivingAvoidCongestion</h4>
<pre>public static final&nbsp;int DrivingAvoidCongestion</pre>
<div class="block">避免拥堵。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.5.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.routepoisearch.RoutePOISearch.DrivingAvoidCongestion">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="DrivingNoHighWay">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DrivingNoHighWay</h4>
<pre>public static final&nbsp;int DrivingNoHighWay</pre>
<div class="block">不走高速。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.5.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.routepoisearch.RoutePOISearch.DrivingNoHighWay">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="DrivingNoHighWaySaveMoney">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DrivingNoHighWaySaveMoney</h4>
<pre>public static final&nbsp;int DrivingNoHighWaySaveMoney</pre>
<div class="block">不走高速且避免收费。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.5.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.routepoisearch.RoutePOISearch.DrivingNoHighWaySaveMoney">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="DrivingSaveMoneyAvoidCongestion">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DrivingSaveMoneyAvoidCongestion</h4>
<pre>public static final&nbsp;int DrivingSaveMoneyAvoidCongestion</pre>
<div class="block">避免收费与拥堵。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.5.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.routepoisearch.RoutePOISearch.DrivingSaveMoneyAvoidCongestion">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="DrivingNoHighAvoidCongestionSaveMoney">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>DrivingNoHighAvoidCongestionSaveMoney</h4>
<pre>public static final&nbsp;int DrivingNoHighAvoidCongestionSaveMoney</pre>
<div class="block">不走高速且躲避收费和拥堵。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.5.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.routepoisearch.RoutePOISearch.DrivingNoHighAvoidCongestionSaveMoney">常量字段值</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="RoutePOISearch-Context-com.amap.api.services.routepoisearch.RoutePOISearchQuery-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>RoutePOISearch</h4>
<pre>public&nbsp;RoutePOISearch(Context&nbsp;context,
                      <a href="../../../../../com/amap/api/services/routepoisearch/RoutePOISearchQuery.html" title="com.amap.api.services.routepoisearch中的类">RoutePOISearchQuery</a>&nbsp;query)
               throws <a href="../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></pre>
<div class="block">沿途搜索构造方法。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>context</code> - 对应的Context。</dd>
<dd><code>query</code> - 查询条件。</dd>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code><a href="../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></code></dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.5.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="setPoiSearchListener-com.amap.api.services.routepoisearch.RoutePOISearch.OnRoutePOISearchListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPoiSearchListener</h4>
<pre>public&nbsp;void&nbsp;setPoiSearchListener(<a href="../../../../../com/amap/api/services/routepoisearch/RoutePOISearch.OnRoutePOISearchListener.html" title="com.amap.api.services.routepoisearch中的接口">RoutePOISearch.OnRoutePOISearchListener</a>&nbsp;listener)</pre>
<div class="block">设置查询监听接口。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>listener</code> - 查询监听接口。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.5.0</dd>
</dl>
</li>
</ul>
<a name="setQuery-com.amap.api.services.routepoisearch.RoutePOISearchQuery-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setQuery</h4>
<pre>public&nbsp;void&nbsp;setQuery(<a href="../../../../../com/amap/api/services/routepoisearch/RoutePOISearchQuery.html" title="com.amap.api.services.routepoisearch中的类">RoutePOISearchQuery</a>&nbsp;query)</pre>
<div class="block">设置查询条件。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>query</code> - 查询条件。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.5.0</dd>
</dl>
</li>
</ul>
<a name="searchRoutePOIAsyn--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>searchRoutePOIAsyn</h4>
<pre>public&nbsp;void&nbsp;searchRoutePOIAsyn()</pre>
<div class="block">沿途搜索POI异步查询接口。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.5.0</dd>
</dl>
</li>
</ul>
<a name="searchRoutePOI--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>searchRoutePOI</h4>
<pre>public&nbsp;<a href="../../../../../com/amap/api/services/routepoisearch/RoutePOISearchResult.html" title="com.amap.api.services.routepoisearch中的类">RoutePOISearchResult</a>&nbsp;searchRoutePOI()
                                    throws <a href="../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></pre>
<div class="block">沿途搜索同步查询接口。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>沿途搜索结果。具体说明见RoutePOISearchResult。</dd>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code><a href="../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></code></dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.5.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/RoutePOISearch.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/routepoisearch/RoutePOIItem.html" title="com.amap.api.services.routepoisearch中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/amap/api/services/routepoisearch/RoutePOISearch.OnRoutePOISearchListener.html" title="com.amap.api.services.routepoisearch中的接口"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/routepoisearch/RoutePOISearch.html" target="_top">框架</a></li>
<li><a href="RoutePOISearch.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li><a href="#nested.class.summary">嵌套</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li><a href="#field.detail">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
