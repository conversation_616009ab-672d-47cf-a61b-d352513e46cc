<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>NearbySearch</title>
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="NearbySearch";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":42,"i1":42,"i2":41,"i3":41,"i4":42,"i5":42,"i6":42,"i7":42,"i8":42,"i9":42,"i10":42};
var tabs = {65535:["t0","所有方法"],1:["t1","静态方法"],2:["t2","实例方法"],8:["t4","具体方法"],32:["t6","已过时的方法"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/NearbySearch.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/nearby/NearbyInfo.html" title="com.amap.api.services.nearby中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/amap/api/services/nearby/NearbySearch.NearbyListener.html" title="com.amap.api.services.nearby中的接口"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/nearby/NearbySearch.html" target="_top">框架</a></li>
<li><a href="NearbySearch.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li><a href="#nested.class.summary">嵌套</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">字段</a>&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li><a href="#field.detail">字段</a>&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.amap.api.services.nearby</div>
<h2 title="类 NearbySearch" class="title">类 NearbySearch</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.amap.api.services.nearby.NearbySearch</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;
<div class="block"><span class="deprecationComment">自7.4.0起不再支持附件功能</span></div>
</div>
<br>
<pre>public class <span class="typeNameLabel">NearbySearch</span>
extends java.lang.Object</pre>
<div class="block">Nearby功能类，该类为单例模式，包含上传、清除、检索用户当前的基础信息和地理信息。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.6.0</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>嵌套类概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="嵌套类概要表, 列表嵌套类和解释">
<caption><span>嵌套类</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">类和说明</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static interface&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/nearby/NearbySearch.NearbyListener.html" title="com.amap.api.services.nearby中的接口">NearbySearch.NearbyListener</a></span></code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;</div>
<div class="block">本类为附近检索结果的异步处理回调接口。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/nearby/NearbySearch.NearbyQuery.html" title="com.amap.api.services.nearby中的类">NearbySearch.NearbyQuery</a></span></code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;</div>
<div class="block">NearbyQuery类为检索附近用户信息的条件类。</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>字段概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="字段概要表, 列表字段和解释">
<caption><span>字段</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">字段和说明</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/nearby/NearbySearch.html#AMAP">AMAP</a></span></code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;</div>
<div class="block">上传的坐标类型为gcj02坐标系，应用于UploadInfo.setCoordType(int coordType)方法。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/nearby/NearbySearch.html#GPS">GPS</a></span></code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;</div>
<div class="block">上传的坐标类型为原始GPS坐标系，应用于UploadInfo.setCoordType(int coordType)方法。</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>所有方法</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">静态方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">实例方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">具体方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t6" class="tableTab"><span><a href="javascript:show(32);">已过时的方法</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/nearby/NearbySearch.html#addNearbyListener-com.amap.api.services.nearby.NearbySearch.NearbyListener-">addNearbyListener</a></span>(<a href="../../../../../com/amap/api/services/nearby/NearbySearch.NearbyListener.html" title="com.amap.api.services.nearby中的接口">NearbySearch.NearbyListener</a>&nbsp;nearbyListener)</code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;</div>
<div class="block">设置NearbySearch.NearbyListener监听器对象，用作调用查询周边信息的监听回调函数，可以添加多个。</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/nearby/NearbySearch.html#clearUserInfoAsyn--">clearUserInfoAsyn</a></span>()</code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;</div>
<div class="block">清除当前用户的位置信息，异步方法，与自动上传信息方法互斥。</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/nearby/NearbySearch.html#destroy--">destroy</a></span>()</code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;</div>
<div class="block">释放NearbySearch单例对象。</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>static <a href="../../../../../com/amap/api/services/nearby/NearbySearch.html" title="com.amap.api.services.nearby中的类">NearbySearch</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/nearby/NearbySearch.html#getInstance-Context-">getInstance</a></span>(Context&nbsp;context)</code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;</div>
<div class="block">获取NearbySearch单例对象。</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/nearby/NearbySearch.html#removeNearbyListener-com.amap.api.services.nearby.NearbySearch.NearbyListener-">removeNearbyListener</a></span>(<a href="../../../../../com/amap/api/services/nearby/NearbySearch.NearbyListener.html" title="com.amap.api.services.nearby中的接口">NearbySearch.NearbyListener</a>&nbsp;nearbyListener)</code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;</div>
<div class="block">移除NearbySearch.NearbyListener监听器对象，可指定移除。</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/services/nearby/NearbySearchResult.html" title="com.amap.api.services.nearby中的类">NearbySearchResult</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/nearby/NearbySearch.html#searchNearbyInfo-com.amap.api.services.nearby.NearbySearch.NearbyQuery-">searchNearbyInfo</a></span>(<a href="../../../../../com/amap/api/services/nearby/NearbySearch.NearbyQuery.html" title="com.amap.api.services.nearby中的类">NearbySearch.NearbyQuery</a>&nbsp;query)</code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;</div>
<div class="block">检索附近的用户信息，同步方法。</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/nearby/NearbySearch.html#searchNearbyInfoAsyn-com.amap.api.services.nearby.NearbySearch.NearbyQuery-">searchNearbyInfoAsyn</a></span>(<a href="../../../../../com/amap/api/services/nearby/NearbySearch.NearbyQuery.html" title="com.amap.api.services.nearby中的类">NearbySearch.NearbyQuery</a>&nbsp;query)</code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;</div>
<div class="block">检索附近的用户信息，异步方法。</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/nearby/NearbySearch.html#setUserID-java.lang.String-">setUserID</a></span>(java.lang.String&nbsp;userID)</code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;</div>
<div class="block">设置用户id，此id可以是用户在业务逻辑中的私有id，设置的id用于检索结果区分用户，长度不超过32个字符，只能包含英文、数字、下划线、短横杠，反之在上传时会返回13。</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/nearby/NearbySearch.html#startUploadNearbyInfoAuto-com.amap.api.services.nearby.UploadInfoCallback-int-">startUploadNearbyInfoAuto</a></span>(<a href="../../../../../com/amap/api/services/nearby/UploadInfoCallback.html" title="com.amap.api.services.nearby中的接口">UploadInfoCallback</a>&nbsp;callback,
                         int&nbsp;internal)</code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;</div>
<div class="block">启动自动上传信息。</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/nearby/NearbySearch.html#stopUploadNearbyInfoAuto--">stopUploadNearbyInfoAuto</a></span>()</code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;</div>
<div class="block">停止自动上传信息接口。</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/nearby/NearbySearch.html#uploadNearbyInfoAsyn-com.amap.api.services.nearby.UploadInfo-">uploadNearbyInfoAsyn</a></span>(<a href="../../../../../com/amap/api/services/nearby/UploadInfo.html" title="com.amap.api.services.nearby中的类">UploadInfo</a>&nbsp;uploadInfo)</code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;</div>
<div class="block">主动上传信息，当没有启动自动上传时可用，用户调用时的时间间隔不低于10s，异步方法。</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>字段详细资料</h3>
<a name="GPS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>GPS</h4>
<pre>public static final&nbsp;int GPS</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;</div>
<div class="block">上传的坐标类型为原始GPS坐标系，应用于UploadInfo.setCoordType(int coordType)方法。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.6.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.nearby.NearbySearch.GPS">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="AMAP">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>AMAP</h4>
<pre>public static final&nbsp;int AMAP</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;</div>
<div class="block">上传的坐标类型为gcj02坐标系，应用于UploadInfo.setCoordType(int coordType)方法。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.6.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.nearby.NearbySearch.AMAP">常量字段值</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="getInstance-Context-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getInstance</h4>
<pre>public static&nbsp;<a href="../../../../../com/amap/api/services/nearby/NearbySearch.html" title="com.amap.api.services.nearby中的类">NearbySearch</a>&nbsp;getInstance(Context&nbsp;context)
                                throws <a href="../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;</div>
<div class="block">获取NearbySearch单例对象。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>context</code> - 当前 Activity。</dd>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code><a href="../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></code></dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.6.0</dd>
</dl>
</li>
</ul>
<a name="addNearbyListener-com.amap.api.services.nearby.NearbySearch.NearbyListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addNearbyListener</h4>
<pre>public&nbsp;void&nbsp;addNearbyListener(<a href="../../../../../com/amap/api/services/nearby/NearbySearch.NearbyListener.html" title="com.amap.api.services.nearby中的接口">NearbySearch.NearbyListener</a>&nbsp;nearbyListener)</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;</div>
<div class="block">设置NearbySearch.NearbyListener监听器对象，用作调用查询周边信息的监听回调函数，可以添加多个。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>nearbyListener</code> - 查询周边信息的监听器。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.6.0</dd>
</dl>
</li>
</ul>
<a name="removeNearbyListener-com.amap.api.services.nearby.NearbySearch.NearbyListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removeNearbyListener</h4>
<pre>public&nbsp;void&nbsp;removeNearbyListener(<a href="../../../../../com/amap/api/services/nearby/NearbySearch.NearbyListener.html" title="com.amap.api.services.nearby中的接口">NearbySearch.NearbyListener</a>&nbsp;nearbyListener)</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;</div>
<div class="block">移除NearbySearch.NearbyListener监听器对象，可指定移除。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>nearbyListener</code> - 查询周边信息的监听器。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.6.0</dd>
</dl>
</li>
</ul>
<a name="clearUserInfoAsyn--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>clearUserInfoAsyn</h4>
<pre>public&nbsp;void&nbsp;clearUserInfoAsyn()</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;</div>
<div class="block">清除当前用户的位置信息，异步方法，与自动上传信息方法互斥。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.6.0</dd>
</dl>
</li>
</ul>
<a name="setUserID-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setUserID</h4>
<pre>public&nbsp;void&nbsp;setUserID(java.lang.String&nbsp;userID)</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;</div>
<div class="block">设置用户id，此id可以是用户在业务逻辑中的私有id，设置的id用于检索结果区分用户，长度不超过32个字符，只能包含英文、数字、下划线、短横杠，反之在上传时会返回13。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.6.0</dd>
</dl>
</li>
</ul>
<a name="startUploadNearbyInfoAuto-com.amap.api.services.nearby.UploadInfoCallback-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>startUploadNearbyInfoAuto</h4>
<pre>public&nbsp;void&nbsp;startUploadNearbyInfoAuto(<a href="../../../../../com/amap/api/services/nearby/UploadInfoCallback.html" title="com.amap.api.services.nearby中的接口">UploadInfoCallback</a>&nbsp;callback,
                                      int&nbsp;internal)</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;</div>
<div class="block">启动自动上传信息。自动上传的信息包括当前用户的id以及位置信息。该方法与单次上传方法互斥，与清除当前用户位置信息方法互斥，直至调用stopUploadNearbyInfoAuto()方法，清除和单次上传的方法会生效。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>callback</code> - 设置用户数据的回调接口。</dd>
<dd><code>internal</code> - 上传时间间隔下线阈值为7000ms,单位为ms。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.6.0</dd>
</dl>
</li>
</ul>
<a name="stopUploadNearbyInfoAuto--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>stopUploadNearbyInfoAuto</h4>
<pre>public&nbsp;void&nbsp;stopUploadNearbyInfoAuto()</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;</div>
<div class="block">停止自动上传信息接口。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.6.0</dd>
</dl>
</li>
</ul>
<a name="uploadNearbyInfoAsyn-com.amap.api.services.nearby.UploadInfo-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>uploadNearbyInfoAsyn</h4>
<pre>public&nbsp;void&nbsp;uploadNearbyInfoAsyn(<a href="../../../../../com/amap/api/services/nearby/UploadInfo.html" title="com.amap.api.services.nearby中的类">UploadInfo</a>&nbsp;uploadInfo)</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;</div>
<div class="block">主动上传信息，当没有启动自动上传时可用，用户调用时的时间间隔不低于10s，异步方法。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>uploadInfo</code> - 用户数据对象，上传的信息包括用户id，以及用户位置信息。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.6.0</dd>
</dl>
</li>
</ul>
<a name="searchNearbyInfoAsyn-com.amap.api.services.nearby.NearbySearch.NearbyQuery-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>searchNearbyInfoAsyn</h4>
<pre>public&nbsp;void&nbsp;searchNearbyInfoAsyn(<a href="../../../../../com/amap/api/services/nearby/NearbySearch.NearbyQuery.html" title="com.amap.api.services.nearby中的类">NearbySearch.NearbyQuery</a>&nbsp;query)</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;</div>
<div class="block">检索附近的用户信息，异步方法。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>query</code> - 检索条件，详见NearbyQuery类。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.6.0</dd>
</dl>
</li>
</ul>
<a name="searchNearbyInfo-com.amap.api.services.nearby.NearbySearch.NearbyQuery-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>searchNearbyInfo</h4>
<pre>public&nbsp;<a href="../../../../../com/amap/api/services/nearby/NearbySearchResult.html" title="com.amap.api.services.nearby中的类">NearbySearchResult</a>&nbsp;searchNearbyInfo(<a href="../../../../../com/amap/api/services/nearby/NearbySearch.NearbyQuery.html" title="com.amap.api.services.nearby中的类">NearbySearch.NearbyQuery</a>&nbsp;query)
                                    throws <a href="../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;</div>
<div class="block">检索附近的用户信息，同步方法。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>query</code> - 检索条件，详见NearbyQuery类。</dd>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code><a href="../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></code></dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.6.0</dd>
</dl>
</li>
</ul>
<a name="destroy--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>destroy</h4>
<pre>public static&nbsp;void&nbsp;destroy()</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;</div>
<div class="block">释放NearbySearch单例对象。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.6.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/NearbySearch.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/nearby/NearbyInfo.html" title="com.amap.api.services.nearby中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/amap/api/services/nearby/NearbySearch.NearbyListener.html" title="com.amap.api.services.nearby中的接口"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/nearby/NearbySearch.html" target="_top">框架</a></li>
<li><a href="NearbySearch.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li><a href="#nested.class.summary">嵌套</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">字段</a>&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li><a href="#field.detail">字段</a>&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
