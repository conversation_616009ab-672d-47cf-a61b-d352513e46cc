<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>com.amap.api.services.nearby</title>
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<h1 class="bar"><a href="../../../../../com/amap/api/services/nearby/package-summary.html" target="classFrame">com.amap.api.services.nearby</a></h1>
<div class="indexContainer">
<h2 title="接口">接口</h2>
<ul title="接口">
<li><a href="NearbySearch.NearbyListener.html" title="com.amap.api.services.nearby中的接口" target="classFrame"><span class="interfaceName">NearbySearch.NearbyListener</span></a></li>
<li><a href="UploadInfoCallback.html" title="com.amap.api.services.nearby中的接口" target="classFrame"><span class="interfaceName">UploadInfoCallback</span></a></li>
</ul>
<h2 title="类">类</h2>
<ul title="类">
<li><a href="NearbyInfo.html" title="com.amap.api.services.nearby中的类" target="classFrame">NearbyInfo</a></li>
<li><a href="NearbySearch.html" title="com.amap.api.services.nearby中的类" target="classFrame">NearbySearch</a></li>
<li><a href="NearbySearch.NearbyQuery.html" title="com.amap.api.services.nearby中的类" target="classFrame">NearbySearch.NearbyQuery</a></li>
<li><a href="NearbySearchResult.html" title="com.amap.api.services.nearby中的类" target="classFrame">NearbySearchResult</a></li>
<li><a href="UploadInfo.html" title="com.amap.api.services.nearby中的类" target="classFrame">UploadInfo</a></li>
</ul>
<h2 title="枚举">枚举</h2>
<ul title="枚举">
<li><a href="NearbySearchFunctionType.html" title="com.amap.api.services.nearby中的枚举" target="classFrame">NearbySearchFunctionType</a></li>
</ul>
</div>
</body>
</html>
