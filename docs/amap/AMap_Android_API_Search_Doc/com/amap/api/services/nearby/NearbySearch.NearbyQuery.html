<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>NearbySearch.NearbyQuery</title>
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="NearbySearch.NearbyQuery";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10};
var tabs = {65535:["t0","所有方法"],2:["t2","实例方法"],8:["t4","具体方法"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/NearbySearch.NearbyQuery.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/nearby/NearbySearch.NearbyListener.html" title="com.amap.api.services.nearby中的接口"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/amap/api/services/nearby/NearbySearchFunctionType.html" title="com.amap.api.services.nearby中的枚举"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/nearby/NearbySearch.NearbyQuery.html" target="_top">框架</a></li>
<li><a href="NearbySearch.NearbyQuery.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.amap.api.services.nearby</div>
<h2 title="类 NearbySearch.NearbyQuery" class="title">类 NearbySearch.NearbyQuery</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.amap.api.services.nearby.NearbySearch.NearbyQuery</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>封闭类:</dt>
<dd><a href="../../../../../com/amap/api/services/nearby/NearbySearch.html" title="com.amap.api.services.nearby中的类">NearbySearch</a></dd>
</dl>
<hr>
<br>
<pre>public static class <span class="typeNameLabel">NearbySearch.NearbyQuery</span>
extends java.lang.Object</pre>
<div class="block">NearbyQuery类为检索附近用户信息的条件类。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.6.0</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/nearby/NearbySearch.NearbyQuery.html#NearbyQuery--">NearbyQuery</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>所有方法</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">实例方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">具体方法</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/nearby/NearbySearch.NearbyQuery.html#getCenterPoint--">getCenterPoint</a></span>()</code>
<div class="block">返回检索中心点。</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/nearby/NearbySearch.NearbyQuery.html#getCoordType--">getCoordType</a></span>()</code>
<div class="block">返回坐标类型。</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/nearby/NearbySearch.NearbyQuery.html#getRadius--">getRadius</a></span>()</code>
<div class="block">返回检索的半径。</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/nearby/NearbySearch.NearbyQuery.html#getTimeRange--">getTimeRange</a></span>()</code>
<div class="block">返回检索的时间范围。</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/nearby/NearbySearch.NearbyQuery.html#getType--">getType</a></span>()</code>
<div class="block">返回检索类型。</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/nearby/NearbySearch.NearbyQuery.html#setCenterPoint-com.amap.api.services.core.LatLonPoint-">setCenterPoint</a></span>(<a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&nbsp;latLonPoint)</code>
<div class="block">检索的中心点，必选参数。</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/nearby/NearbySearch.NearbyQuery.html#setCoordType-int-">setCoordType</a></span>(int&nbsp;coordType)</code>
<div class="block">设置坐标类型，NearbySearch.GPS代表GPS坐标系，NearbySearch.AMAP代表高德坐标系，默认为高德坐标系。</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/nearby/NearbySearch.NearbyQuery.html#setRadius-int-">setRadius</a></span>(int&nbsp;radius)</code>
<div class="block">设置检索的半径，下限为0米，默认为3000米，上限为10000米，可选参数。</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/nearby/NearbySearch.NearbyQuery.html#setTimeRange-int-">setTimeRange</a></span>(int&nbsp;period)</code>
<div class="block">设置检索的时间范围，默认从当前范围向前推移period长度的时间，单位秒级。</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/nearby/NearbySearch.NearbyQuery.html#setType-com.amap.api.services.nearby.NearbySearchFunctionType-">setType</a></span>(<a href="../../../../../com/amap/api/services/nearby/NearbySearchFunctionType.html" title="com.amap.api.services.nearby中的枚举">NearbySearchFunctionType</a>&nbsp;type)</code>
<div class="block">设置检索的类型。</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="NearbyQuery--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>NearbyQuery</h4>
<pre>public&nbsp;NearbyQuery()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="setCenterPoint-com.amap.api.services.core.LatLonPoint-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCenterPoint</h4>
<pre>public&nbsp;void&nbsp;setCenterPoint(<a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&nbsp;latLonPoint)</pre>
<div class="block">检索的中心点，必选参数。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>latLonPoint</code> - 检索的中心点坐标。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.6.0</dd>
</dl>
</li>
</ul>
<a name="getCenterPoint--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCenterPoint</h4>
<pre>public&nbsp;<a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&nbsp;getCenterPoint()</pre>
<div class="block">返回检索中心点。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>检索中心点的坐标。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.6.0</dd>
</dl>
</li>
</ul>
<a name="getRadius--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRadius</h4>
<pre>public&nbsp;int&nbsp;getRadius()</pre>
<div class="block">返回检索的半径。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>检索半径。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.6.0</dd>
</dl>
</li>
</ul>
<a name="setRadius-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRadius</h4>
<pre>public&nbsp;void&nbsp;setRadius(int&nbsp;radius)</pre>
<div class="block">设置检索的半径，下限为0米，默认为3000米，上限为10000米，可选参数。检索结果默认由近及远。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>radius</code> - 检索的半径。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.6.0</dd>
</dl>
</li>
</ul>
<a name="setType-com.amap.api.services.nearby.NearbySearchFunctionType-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setType</h4>
<pre>public&nbsp;void&nbsp;setType(<a href="../../../../../com/amap/api/services/nearby/NearbySearchFunctionType.html" title="com.amap.api.services.nearby中的枚举">NearbySearchFunctionType</a>&nbsp;type)</pre>
<div class="block">设置检索的类型。类型分为“DISTANCE_SEARCH”——直线距离检索、“DRIVING_DIATANCE_SEARCH”——驾车距离检索两种，可选参数，默认为“DISTANCE_SEARCH”。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>type</code> - NearbySearchFunctionType枚举类型。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.6.0</dd>
</dl>
</li>
</ul>
<a name="getType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getType</h4>
<pre>public&nbsp;int&nbsp;getType()</pre>
<div class="block">返回检索类型。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>检索类型，直线距离检索返回0，驾车距离检索返回1。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.6.0</dd>
</dl>
</li>
</ul>
<a name="setCoordType-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCoordType</h4>
<pre>public&nbsp;void&nbsp;setCoordType(int&nbsp;coordType)</pre>
<div class="block">设置坐标类型，NearbySearch.GPS代表GPS坐标系，NearbySearch.AMAP代表高德坐标系，默认为高德坐标系。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>coordType</code> - 坐标类型。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.6.0</dd>
</dl>
</li>
</ul>
<a name="getCoordType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCoordType</h4>
<pre>public&nbsp;int&nbsp;getCoordType()</pre>
<div class="block">返回坐标类型。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>坐标类型，0代表GPS坐标系，1代表高德坐标系。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.6.0</dd>
</dl>
</li>
</ul>
<a name="setTimeRange-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTimeRange</h4>
<pre>public&nbsp;void&nbsp;setTimeRange(int&nbsp;period)</pre>
<div class="block">设置检索的时间范围，默认从当前范围向前推移period长度的时间，单位秒级。上限为24小时，下限为5秒，默认为1800秒。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>period</code> - 检索的时间长度。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.6.0</dd>
</dl>
</li>
</ul>
<a name="getTimeRange--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getTimeRange</h4>
<pre>public&nbsp;int&nbsp;getTimeRange()</pre>
<div class="block">返回检索的时间范围。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>检索的时间长度，单位秒级。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.6.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/NearbySearch.NearbyQuery.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/nearby/NearbySearch.NearbyListener.html" title="com.amap.api.services.nearby中的接口"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/amap/api/services/nearby/NearbySearchFunctionType.html" title="com.amap.api.services.nearby中的枚举"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/nearby/NearbySearch.NearbyQuery.html" target="_top">框架</a></li>
<li><a href="NearbySearch.NearbyQuery.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
