<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Tip</title>
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Tip";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10};
var tabs = {65535:["t0","所有方法"],2:["t2","实例方法"],8:["t4","具体方法"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/Tip.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/help/InputtipsQuery.html" title="com.amap.api.services.help中的类"><span class="typeNameLink">上一个类</span></a></li>
<li>下一个类</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/help/Tip.html" target="_top">框架</a></li>
<li><a href="Tip.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.amap.api.services.help</div>
<h2 title="类 Tip" class="title">类 Tip</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.amap.api.services.help.Tip</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">Tip</span>
extends java.lang.Object</pre>
<div class="block">定义Tip的类。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>所有方法</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">实例方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">具体方法</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/help/Tip.html#getAdcode--">getAdcode</a></span>()</code>
<div class="block">返回提示区域编码。</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/help/Tip.html#getAddress--">getAddress</a></span>()</code>
<div class="block">返回详细地址。</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/help/Tip.html#getDistrict--">getDistrict</a></span>()</code>
<div class="block">返回提示区域。</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/help/Tip.html#getName--">getName</a></span>()</code>
<div class="block">返回提示名称。</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/help/Tip.html#getPoiID--">getPoiID</a></span>()</code>
<div class="block">获取Poi的ID，如果不存在id则为空。</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/help/Tip.html#getPoint--">getPoint</a></span>()</code>
<div class="block">获取Poi的经纬度，如果不存在经纬度则为空。</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/help/Tip.html#getTypeCode--">getTypeCode</a></span>()</code>
<div class="block">获取输入提示结果的类型编码</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/help/Tip.html#toString--">toString</a></span>()</code>
<div class="block">将提示转换为字符串输出</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="getPoiID--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPoiID</h4>
<pre>public&nbsp;java.lang.String&nbsp;getPoiID()</pre>
<div class="block">获取Poi的ID，如果不存在id则为空。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>Poi的ID。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.6.0</dd>
</dl>
</li>
</ul>
<a name="getPoint--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPoint</h4>
<pre>public&nbsp;<a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&nbsp;getPoint()</pre>
<div class="block">获取Poi的经纬度，如果不存在经纬度则为空。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>LatLngPoint 经纬度。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.6.0</dd>
</dl>
</li>
</ul>
<a name="getName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getName</h4>
<pre>public&nbsp;java.lang.String&nbsp;getName()</pre>
<div class="block">返回提示名称。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>提示名称。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="getDistrict--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDistrict</h4>
<pre>public&nbsp;java.lang.String&nbsp;getDistrict()</pre>
<div class="block">返回提示区域。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>提示区域。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="getAdcode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAdcode</h4>
<pre>public&nbsp;java.lang.String&nbsp;getAdcode()</pre>
<div class="block">返回提示区域编码。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>提示区域编码。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="getAddress--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAddress</h4>
<pre>public&nbsp;java.lang.String&nbsp;getAddress()</pre>
<div class="block">返回详细地址。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>详细地址。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.3.0</dd>
</dl>
</li>
</ul>
<a name="getTypeCode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTypeCode</h4>
<pre>public&nbsp;java.lang.String&nbsp;getTypeCode()</pre>
<div class="block">获取输入提示结果的类型编码</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>返回类型编码</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>4.0.0</dd>
</dl>
</li>
</ul>
<a name="toString--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>toString</h4>
<pre>public&nbsp;java.lang.String&nbsp;toString()</pre>
<div class="block">将提示转换为字符串输出</div>
<dl>
<dt><span class="overrideSpecifyLabel">覆盖:</span></dt>
<dd><code>toString</code>&nbsp;在类中&nbsp;<code>java.lang.Object</code></dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>提示名称、提示区域、提示区域编码的字符串</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/Tip.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/help/InputtipsQuery.html" title="com.amap.api.services.help中的类"><span class="typeNameLink">上一个类</span></a></li>
<li>下一个类</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/help/Tip.html" target="_top">框架</a></li>
<li><a href="Tip.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
