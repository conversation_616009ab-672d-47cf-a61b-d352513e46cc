<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Inputtips</title>
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Inputtips";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":42,"i3":42,"i4":10,"i5":10,"i6":10};
var tabs = {65535:["t0","所有方法"],2:["t2","实例方法"],8:["t4","具体方法"],32:["t6","已过时的方法"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/Inputtips.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个类</li>
<li><a href="../../../../../com/amap/api/services/help/Inputtips.InputtipsListener.html" title="com.amap.api.services.help中的接口"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/help/Inputtips.html" target="_top">框架</a></li>
<li><a href="Inputtips.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li><a href="#nested.class.summary">嵌套</a>&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.amap.api.services.help</div>
<h2 title="类 Inputtips" class="title">类 Inputtips</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.amap.api.services.help.Inputtips</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public final class <span class="typeNameLabel">Inputtips</span>
extends java.lang.Object</pre>
<div class="block">输入提示类，前提需要联网。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.2</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>嵌套类概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="嵌套类概要表, 列表嵌套类和解释">
<caption><span>嵌套类</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">类和说明</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static interface&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/help/Inputtips.InputtipsListener.html" title="com.amap.api.services.help中的接口">Inputtips.InputtipsListener</a></span></code>
<div class="block">输入提示回调的监听接口。</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/help/Inputtips.html#Inputtips-Context-com.amap.api.services.help.Inputtips.InputtipsListener-">Inputtips</a></span>(Context&nbsp;context,
         <a href="../../../../../com/amap/api/services/help/Inputtips.InputtipsListener.html" title="com.amap.api.services.help中的接口">Inputtips.InputtipsListener</a>&nbsp;listener)</code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;&nbsp;</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/help/Inputtips.html#Inputtips-Context-com.amap.api.services.help.InputtipsQuery-">Inputtips</a></span>(Context&nbsp;context,
         <a href="../../../../../com/amap/api/services/help/InputtipsQuery.html" title="com.amap.api.services.help中的类">InputtipsQuery</a>&nbsp;query)</code>
<div class="block">根据给定的参数构造一个Inputtips的新对象。</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>所有方法</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">实例方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">具体方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t6" class="tableTab"><span><a href="javascript:show(32);">已过时的方法</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/services/help/InputtipsQuery.html" title="com.amap.api.services.help中的类">InputtipsQuery</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/help/Inputtips.html#getQuery--">getQuery</a></span>()</code>
<div class="block">返回提示查询条件。</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../../com/amap/api/services/help/Tip.html" title="com.amap.api.services.help中的类">Tip</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/help/Inputtips.html#requestInputtips--">requestInputtips</a></span>()</code>
<div class="block">查询输入提示的同步接口。</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/help/Inputtips.html#requestInputtips-java.lang.String-java.lang.String-">requestInputtips</a></span>(java.lang.String&nbsp;keyword,
                java.lang.String&nbsp;city)</code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;
<div class="block"><span class="deprecationComment">请参考 <a href="../../../../../com/amap/api/services/help/Inputtips.html#requestInputtips--"><code>Inputtips.requestInputtips()</code></a></span></div>
</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/help/Inputtips.html#requestInputtips-java.lang.String-java.lang.String-java.lang.String-">requestInputtips</a></span>(java.lang.String&nbsp;keyword,
                java.lang.String&nbsp;city,
                java.lang.String&nbsp;type)</code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;
<div class="block"><span class="deprecationComment">请参考 <a href="../../../../../com/amap/api/services/help/Inputtips.html#requestInputtips--"><code>Inputtips.requestInputtips()</code></a></span></div>
</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/help/Inputtips.html#requestInputtipsAsyn--">requestInputtipsAsyn</a></span>()</code>
<div class="block">查询输入提示的异步接口。</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/help/Inputtips.html#setInputtipsListener-com.amap.api.services.help.Inputtips.InputtipsListener-">setInputtipsListener</a></span>(<a href="../../../../../com/amap/api/services/help/Inputtips.InputtipsListener.html" title="com.amap.api.services.help中的接口">Inputtips.InputtipsListener</a>&nbsp;listener)</code>
<div class="block">设置提示查询监听。</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/help/Inputtips.html#setQuery-com.amap.api.services.help.InputtipsQuery-">setQuery</a></span>(<a href="../../../../../com/amap/api/services/help/InputtipsQuery.html" title="com.amap.api.services.help中的类">InputtipsQuery</a>&nbsp;query)</code>
<div class="block">设置提示查询条件。</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="Inputtips-Context-com.amap.api.services.help.Inputtips.InputtipsListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Inputtips</h4>
<pre>public&nbsp;Inputtips(Context&nbsp;context,
                 <a href="../../../../../com/amap/api/services/help/Inputtips.InputtipsListener.html" title="com.amap.api.services.help中的接口">Inputtips.InputtipsListener</a>&nbsp;listener)
          throws <a href="../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;</div>
<div class="block">根据给定的参数构造一个Inputtips的新对象。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>context</code> - 当前 Activity。</dd>
<dd><code>listener</code> - 输入提示回调的监听接口</dd>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code><a href="../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></code></dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.2</dd>
</dl>
</li>
</ul>
<a name="Inputtips-Context-com.amap.api.services.help.InputtipsQuery-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>Inputtips</h4>
<pre>public&nbsp;Inputtips(Context&nbsp;context,
                 <a href="../../../../../com/amap/api/services/help/InputtipsQuery.html" title="com.amap.api.services.help中的类">InputtipsQuery</a>&nbsp;query)</pre>
<div class="block">根据给定的参数构造一个Inputtips的新对象。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>context</code> - 当前 Activity。</dd>
<dd><code>query</code> - 查询条件。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.2</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="getQuery--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getQuery</h4>
<pre>public&nbsp;<a href="../../../../../com/amap/api/services/help/InputtipsQuery.html" title="com.amap.api.services.help中的类">InputtipsQuery</a>&nbsp;getQuery()</pre>
<div class="block">返回提示查询条件。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>提示查询条件。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.8.0</dd>
</dl>
</li>
</ul>
<a name="setQuery-com.amap.api.services.help.InputtipsQuery-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setQuery</h4>
<pre>public&nbsp;void&nbsp;setQuery(<a href="../../../../../com/amap/api/services/help/InputtipsQuery.html" title="com.amap.api.services.help中的类">InputtipsQuery</a>&nbsp;query)</pre>
<div class="block">设置提示查询条件。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>query</code> - 提示查询条件。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.8.0</dd>
</dl>
</li>
</ul>
<a name="setInputtipsListener-com.amap.api.services.help.Inputtips.InputtipsListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setInputtipsListener</h4>
<pre>public&nbsp;void&nbsp;setInputtipsListener(<a href="../../../../../com/amap/api/services/help/Inputtips.InputtipsListener.html" title="com.amap.api.services.help中的接口">Inputtips.InputtipsListener</a>&nbsp;listener)</pre>
<div class="block">设置提示查询监听。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>listener</code> - 提示类查询监听。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.8.0</dd>
</dl>
</li>
</ul>
<a name="requestInputtipsAsyn--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>requestInputtipsAsyn</h4>
<pre>public&nbsp;void&nbsp;requestInputtipsAsyn()</pre>
<div class="block">查询输入提示的异步接口。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.8.0</dd>
</dl>
</li>
</ul>
<a name="requestInputtips--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>requestInputtips</h4>
<pre>public&nbsp;java.util.List&lt;<a href="../../../../../com/amap/api/services/help/Tip.html" title="com.amap.api.services.help中的类">Tip</a>&gt;&nbsp;requestInputtips()
                                     throws <a href="../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></pre>
<div class="block">查询输入提示的同步接口。</div>
<dl>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code><a href="../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></code> - <br>
     在网络连接出现问题的情况下，会抛出<a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_CLIENT_NETWORK_EXCEPTION"><code>AMapException.AMAP_CLIENT_NETWORK_EXCEPTION</code></a>。<br>
     在Query参数不正确的时候会抛出 <a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_CLIENT_INVALID_PARAMETER"><code>AMapException.AMAP_CLIENT_INVALID_PARAMETER</code></a></dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>4.0.0</dd>
</dl>
</li>
</ul>
<a name="requestInputtips-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>requestInputtips</h4>
<pre>public&nbsp;void&nbsp;requestInputtips(java.lang.String&nbsp;keyword,
                             java.lang.String&nbsp;city)
                      throws <a href="../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;<span class="deprecationComment">请参考 <a href="../../../../../com/amap/api/services/help/Inputtips.html#requestInputtips--"><code>Inputtips.requestInputtips()</code></a></span></div>
<div class="block">发送输入提示请求。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>keyword</code> - 输入的关键字。</dd>
<dd><code>city</code> - 查询的城市编码citycode、城市名称（中文或中文全拼）、行政区划代码adcode。设置null或“”为全国。</dd>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code><a href="../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></code></dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.2</dd>
</dl>
</li>
</ul>
<a name="requestInputtips-java.lang.String-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>requestInputtips</h4>
<pre>public&nbsp;void&nbsp;requestInputtips(java.lang.String&nbsp;keyword,
                             java.lang.String&nbsp;city,
                             java.lang.String&nbsp;type)
                      throws <a href="../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;<span class="deprecationComment">请参考 <a href="../../../../../com/amap/api/services/help/Inputtips.html#requestInputtips--"><code>Inputtips.requestInputtips()</code></a></span></div>
<div class="block">发送输入提示请求，限定搜索类型。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>keyword</code> - 输入的关键字。</dd>
<dd><code>city</code> - 查询的城市编码citycode、城市名称（中文或中文全拼）、行政区划代码adcode。设置null或“”为全国。</dd>
<dd><code>type</code> - 搜索类型的组合，仅支持数字，对照下载页面获取的POI分类表输入。多种类型使用“|”分隔。
 <p>举例：编码表 “050117 餐饮服务 ;中餐厅 ;火锅店”，输入“05”为餐饮服务、“0501”为中餐厅、“050117”为火锅店类别。/p></dd>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code><a href="../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></code></dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/Inputtips.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个类</li>
<li><a href="../../../../../com/amap/api/services/help/Inputtips.InputtipsListener.html" title="com.amap.api.services.help中的接口"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/help/Inputtips.html" target="_top">框架</a></li>
<li><a href="Inputtips.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li><a href="#nested.class.summary">嵌套</a>&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
