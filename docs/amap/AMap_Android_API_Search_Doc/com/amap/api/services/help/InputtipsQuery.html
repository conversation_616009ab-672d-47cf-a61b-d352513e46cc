<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>InputtipsQuery</title>
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="InputtipsQuery";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10};
var tabs = {65535:["t0","所有方法"],2:["t2","实例方法"],8:["t4","具体方法"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/InputtipsQuery.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/help/Inputtips.InputtipsListener.html" title="com.amap.api.services.help中的接口"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/amap/api/services/help/Tip.html" title="com.amap.api.services.help中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/help/InputtipsQuery.html" target="_top">框架</a></li>
<li><a href="InputtipsQuery.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.amap.api.services.help</div>
<h2 title="类 InputtipsQuery" class="title">类 InputtipsQuery</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.amap.api.services.help.InputtipsQuery</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>所有已实现的接口:</dt>
<dd>java.lang.Cloneable</dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">InputtipsQuery</span>
extends java.lang.Object
implements java.lang.Cloneable</pre>
<div class="block">此类定义了输入提示搜索的参数。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.8.0</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/help/InputtipsQuery.html#InputtipsQuery-java.lang.String-java.lang.String-">InputtipsQuery</a></span>(java.lang.String&nbsp;keyword,
              java.lang.String&nbsp;city)</code>
<div class="block">根据给定的参数来构造一个InputtipsQuery的对象</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>所有方法</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">实例方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">具体方法</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/help/InputtipsQuery.html#getCity--">getCity</a></span>()</code>
<div class="block">返回查询城市。</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/help/InputtipsQuery.html#getCityLimit--">getCityLimit</a></span>()</code>
<div class="block">返回是否严重按照设定城市返回结果。</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/help/InputtipsQuery.html#getKeyword--">getKeyword</a></span>()</code>
<div class="block">返回查询关键字。</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/help/InputtipsQuery.html#getLocation--">getLocation</a></span>()</code>
<div class="block">返回对结果进行位置限制的经纬度点</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/help/InputtipsQuery.html#getType--">getType</a></span>()</code>
<div class="block">返回搜索的类型。</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/help/InputtipsQuery.html#setCityLimit-boolean-">setCityLimit</a></span>(boolean&nbsp;isLimit)</code>
<div class="block">对获取结果进行严格城市限制。</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/help/InputtipsQuery.html#setLocation-com.amap.api.services.core.LatLonPoint-">setLocation</a></span>(<a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&nbsp;location)</code>
<div class="block">对获取结果进行经纬度位置限制</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/help/InputtipsQuery.html#setType-java.lang.String-">setType</a></span>(java.lang.String&nbsp;type)</code>
<div class="block">限定搜索类型。</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="InputtipsQuery-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>InputtipsQuery</h4>
<pre>public&nbsp;InputtipsQuery(java.lang.String&nbsp;keyword,
                      java.lang.String&nbsp;city)</pre>
<div class="block">根据给定的参数来构造一个InputtipsQuery的对象</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>keyword</code> - 输入的关键字</dd>
<dd><code>city</code> - 查询的城市编码 citycode、城市名称（中文或中文全拼）、行政区划代码adcode。设置null或“”为全国。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.8.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="getKeyword--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getKeyword</h4>
<pre>public&nbsp;java.lang.String&nbsp;getKeyword()</pre>
<div class="block">返回查询关键字。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>查询关键字。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.8.0</dd>
</dl>
</li>
</ul>
<a name="getCity--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCity</h4>
<pre>public&nbsp;java.lang.String&nbsp;getCity()</pre>
<div class="block">返回查询城市。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>查询城市。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.8.0</dd>
</dl>
</li>
</ul>
<a name="setType-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setType</h4>
<pre>public&nbsp;void&nbsp;setType(java.lang.String&nbsp;type)</pre>
<div class="block">限定搜索类型。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>type</code> - 搜索类型的组合，仅支持数字，对照下载页面获取的POI分类表输入。多种类型使用“|”分隔。
 <p>举例：编码表 “050117 餐饮服务 ;中餐厅 ;火锅店”，输入“05”为餐饮服务、“0501”为中餐厅、“050117”为火锅店类别。/p></dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.8.0</dd>
</dl>
</li>
</ul>
<a name="getType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getType</h4>
<pre>public&nbsp;java.lang.String&nbsp;getType()</pre>
<div class="block">返回搜索的类型。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>搜索类型。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.8.0</dd>
</dl>
</li>
</ul>
<a name="setCityLimit-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCityLimit</h4>
<pre>public&nbsp;void&nbsp;setCityLimit(boolean&nbsp;isLimit)</pre>
<div class="block">对获取结果进行严格城市限制。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>isLimit</code> - true，城市限制；false，输入文字是权重词汇时，全国搜索。默认为false。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.8.0</dd>
</dl>
</li>
</ul>
<a name="getCityLimit--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCityLimit</h4>
<pre>public&nbsp;boolean&nbsp;getCityLimit()</pre>
<div class="block">返回是否严重按照设定城市返回结果。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>true，限制；false，不限制。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.8.0</dd>
</dl>
</li>
</ul>
<a name="setLocation-com.amap.api.services.core.LatLonPoint-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLocation</h4>
<pre>public&nbsp;void&nbsp;setLocation(<a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&nbsp;location)</pre>
<div class="block">对获取结果进行经纬度位置限制</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>5.0.0</dd>
</dl>
</li>
</ul>
<a name="getLocation--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getLocation</h4>
<pre>public&nbsp;<a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&nbsp;getLocation()</pre>
<div class="block">返回对结果进行位置限制的经纬度点</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>限制点经纬度坐标。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>5.0.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/InputtipsQuery.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/help/Inputtips.InputtipsListener.html" title="com.amap.api.services.help中的接口"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/amap/api/services/help/Tip.html" title="com.amap.api.services.help中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/help/InputtipsQuery.html" target="_top">框架</a></li>
<li><a href="InputtipsQuery.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
