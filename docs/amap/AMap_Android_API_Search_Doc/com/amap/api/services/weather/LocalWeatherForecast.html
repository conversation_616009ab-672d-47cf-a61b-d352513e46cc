<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>LocalWeatherForecast</title>
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="LocalWeatherForecast";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10};
var tabs = {65535:["t0","所有方法"],2:["t2","实例方法"],8:["t4","具体方法"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/LocalWeatherForecast.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/weather/LocalDayWeatherForecast.html" title="com.amap.api.services.weather中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/amap/api/services/weather/LocalWeatherForecastResult.html" title="com.amap.api.services.weather中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/weather/LocalWeatherForecast.html" target="_top">框架</a></li>
<li><a href="LocalWeatherForecast.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.amap.api.services.weather</div>
<h2 title="类 LocalWeatherForecast" class="title">类 LocalWeatherForecast</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.amap.api.services.weather.LocalWeatherForecast</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">LocalWeatherForecast</span>
extends java.lang.Object</pre>
<div class="block">天气预报类。支持当前时间在内的4天的天气进行预报。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>所有方法</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">实例方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">具体方法</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/weather/LocalWeatherForecast.html#getAdCode--">getAdCode</a></span>()</code>
<div class="block">返回行政区划代码。</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/weather/LocalWeatherForecast.html#getCity--">getCity</a></span>()</code>
<div class="block">返回城市名称。</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/weather/LocalWeatherForecast.html#getProvince--">getProvince</a></span>()</code>
<div class="block">返回省份名称。</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/weather/LocalWeatherForecast.html#getReportTime--">getReportTime</a></span>()</code>
<div class="block">返回天气预报发布时间。</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../../com/amap/api/services/weather/LocalDayWeatherForecast.html" title="com.amap.api.services.weather中的类">LocalDayWeatherForecast</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/weather/LocalWeatherForecast.html#getWeatherForecast--">getWeatherForecast</a></span>()</code>
<div class="block">返回天气预报的数据的数组，数组中的对象为 LocalDayWeatherForecast。</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/weather/LocalWeatherForecast.html#setAdCode-java.lang.String-">setAdCode</a></span>(java.lang.String&nbsp;mAdCode)</code>
<div class="block">设置行政区划代码。</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/weather/LocalWeatherForecast.html#setCity-java.lang.String-">setCity</a></span>(java.lang.String&nbsp;mCity)</code>
<div class="block">设置城市名称。</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/weather/LocalWeatherForecast.html#setProvince-java.lang.String-">setProvince</a></span>(java.lang.String&nbsp;mProvince)</code>
<div class="block">设置省份名称。</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/weather/LocalWeatherForecast.html#setReportTime-java.lang.String-">setReportTime</a></span>(java.lang.String&nbsp;mReportTime)</code>
<div class="block">设置天气预报发布时间。</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/weather/LocalWeatherForecast.html#setWeatherForecast-java.util.List-">setWeatherForecast</a></span>(java.util.List&lt;<a href="../../../../../com/amap/api/services/weather/LocalDayWeatherForecast.html" title="com.amap.api.services.weather中的类">LocalDayWeatherForecast</a>&gt;&nbsp;WeatherForecastlist)</code>
<div class="block">设置天气预报的结果。</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="getProvince--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProvince</h4>
<pre>public&nbsp;java.lang.String&nbsp;getProvince()</pre>
<div class="block">返回省份名称。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>返回省份名称。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
<a name="setProvince-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setProvince</h4>
<pre>public&nbsp;void&nbsp;setProvince(java.lang.String&nbsp;mProvince)</pre>
<div class="block">设置省份名称。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>mProvince</code> - 省份名称。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
<a name="getCity--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCity</h4>
<pre>public&nbsp;java.lang.String&nbsp;getCity()</pre>
<div class="block">返回城市名称。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>城市名称。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
<a name="setCity-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCity</h4>
<pre>public&nbsp;void&nbsp;setCity(java.lang.String&nbsp;mCity)</pre>
<div class="block">设置城市名称。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>mCity</code> - 城市名称。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
<a name="getAdCode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAdCode</h4>
<pre>public&nbsp;java.lang.String&nbsp;getAdCode()</pre>
<div class="block">返回行政区划代码。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>行政区划代码。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
<a name="setAdCode-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAdCode</h4>
<pre>public&nbsp;void&nbsp;setAdCode(java.lang.String&nbsp;mAdCode)</pre>
<div class="block">设置行政区划代码。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>mAdCode</code> - 行政区划代码。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
<a name="getReportTime--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getReportTime</h4>
<pre>public&nbsp;java.lang.String&nbsp;getReportTime()</pre>
<div class="block">返回天气预报发布时间。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>天气预报发布时间。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
<a name="setReportTime-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setReportTime</h4>
<pre>public&nbsp;void&nbsp;setReportTime(java.lang.String&nbsp;mReportTime)</pre>
<div class="block">设置天气预报发布时间。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>mReportTime</code> - 天气预报发布时间。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
<a name="getWeatherForecast--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWeatherForecast</h4>
<pre>public&nbsp;java.util.List&lt;<a href="../../../../../com/amap/api/services/weather/LocalDayWeatherForecast.html" title="com.amap.api.services.weather中的类">LocalDayWeatherForecast</a>&gt;&nbsp;getWeatherForecast()</pre>
<div class="block">返回天气预报的数据的数组，数组中的对象为 LocalDayWeatherForecast。
 <p>数组中的第一个数据（即List.get(0)）表示今天的天气；第二个数据（即List.get(1)）表示明天的天气；第三个数据（即List.get(2)）表示后天的天气；第四个数据（即List.get(3)）表示大后天的天气。</p></div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>天气预报的数据的数组。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
<a name="setWeatherForecast-java.util.List-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setWeatherForecast</h4>
<pre>public&nbsp;void&nbsp;setWeatherForecast(java.util.List&lt;<a href="../../../../../com/amap/api/services/weather/LocalDayWeatherForecast.html" title="com.amap.api.services.weather中的类">LocalDayWeatherForecast</a>&gt;&nbsp;WeatherForecastlist)</pre>
<div class="block">设置天气预报的结果。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>WeatherForecastlist</code> - 天气预报结果。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/LocalWeatherForecast.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/weather/LocalDayWeatherForecast.html" title="com.amap.api.services.weather中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/amap/api/services/weather/LocalWeatherForecastResult.html" title="com.amap.api.services.weather中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/weather/LocalWeatherForecast.html" target="_top">框架</a></li>
<li><a href="LocalWeatherForecast.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
