<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>com.amap.api.services.weather</title>
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<h1 class="bar"><a href="../../../../../com/amap/api/services/weather/package-summary.html" target="classFrame">com.amap.api.services.weather</a></h1>
<div class="indexContainer">
<h2 title="接口">接口</h2>
<ul title="接口">
<li><a href="WeatherSearch.OnWeatherSearchListener.html" title="com.amap.api.services.weather中的接口" target="classFrame"><span class="interfaceName">WeatherSearch.OnWeatherSearchListener</span></a></li>
</ul>
<h2 title="类">类</h2>
<ul title="类">
<li><a href="LocalDayWeatherForecast.html" title="com.amap.api.services.weather中的类" target="classFrame">LocalDayWeatherForecast</a></li>
<li><a href="LocalWeatherForecast.html" title="com.amap.api.services.weather中的类" target="classFrame">LocalWeatherForecast</a></li>
<li><a href="LocalWeatherForecastResult.html" title="com.amap.api.services.weather中的类" target="classFrame">LocalWeatherForecastResult</a></li>
<li><a href="LocalWeatherLive.html" title="com.amap.api.services.weather中的类" target="classFrame">LocalWeatherLive</a></li>
<li><a href="LocalWeatherLiveResult.html" title="com.amap.api.services.weather中的类" target="classFrame">LocalWeatherLiveResult</a></li>
<li><a href="WeatherSearch.html" title="com.amap.api.services.weather中的类" target="classFrame">WeatherSearch</a></li>
<li><a href="WeatherSearchQuery.html" title="com.amap.api.services.weather中的类" target="classFrame">WeatherSearchQuery</a></li>
</ul>
</div>
</body>
</html>
