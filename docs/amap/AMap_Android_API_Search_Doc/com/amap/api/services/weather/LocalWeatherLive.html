<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>LocalWeatherLive</title>
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="LocalWeatherLive";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10};
var tabs = {65535:["t0","所有方法"],2:["t2","实例方法"],8:["t4","具体方法"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/LocalWeatherLive.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/weather/LocalWeatherForecastResult.html" title="com.amap.api.services.weather中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/amap/api/services/weather/LocalWeatherLiveResult.html" title="com.amap.api.services.weather中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/weather/LocalWeatherLive.html" target="_top">框架</a></li>
<li><a href="LocalWeatherLive.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.amap.api.services.weather</div>
<h2 title="类 LocalWeatherLive" class="title">类 LocalWeatherLive</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.amap.api.services.weather.LocalWeatherLive</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">LocalWeatherLive</span>
extends java.lang.Object</pre>
<div class="block">此类为实况天气返回结果类。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>所有方法</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">实例方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">具体方法</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/weather/LocalWeatherLive.html#getAdCode--">getAdCode</a></span>()</code>
<div class="block">返回行政区划代码。</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/weather/LocalWeatherLive.html#getCity--">getCity</a></span>()</code>
<div class="block">返回城市名称。</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/weather/LocalWeatherLive.html#getHumidity--">getHumidity</a></span>()</code>
<div class="block">返回空气湿度的百分比。</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/weather/LocalWeatherLive.html#getProvince--">getProvince</a></span>()</code>
<div class="block">返回省份名称。</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/weather/LocalWeatherLive.html#getReportTime--">getReportTime</a></span>()</code>
<div class="block">返回实时数据发布时间。</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/weather/LocalWeatherLive.html#getTemperature--">getTemperature</a></span>()</code>
<div class="block">返回实时气温，单位：摄氏度。</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/weather/LocalWeatherLive.html#getWeather--">getWeather</a></span>()</code>
<div class="block">返回天气现象描述，如“晴”、“多云”等。</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/weather/LocalWeatherLive.html#getWindDirection--">getWindDirection</a></span>()</code>
<div class="block">返回风向。</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/weather/LocalWeatherLive.html#getWindPower--">getWindPower</a></span>()</code>
<div class="block">返回风力，单位：级。</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="getProvince--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProvince</h4>
<pre>public&nbsp;java.lang.String&nbsp;getProvince()</pre>
<div class="block">返回省份名称。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>省份名称。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
<a name="getCity--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCity</h4>
<pre>public&nbsp;java.lang.String&nbsp;getCity()</pre>
<div class="block">返回城市名称。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>城市名称。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
<a name="getAdCode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAdCode</h4>
<pre>public&nbsp;java.lang.String&nbsp;getAdCode()</pre>
<div class="block">返回行政区划代码。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>行政区划代码。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
<a name="getWeather--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWeather</h4>
<pre>public&nbsp;java.lang.String&nbsp;getWeather()</pre>
<div class="block">返回天气现象描述，如“晴”、“多云”等。详细类型参见开发指南--天气对照表。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>天气现象描述。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
<a name="getTemperature--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTemperature</h4>
<pre>public&nbsp;java.lang.String&nbsp;getTemperature()</pre>
<div class="block">返回实时气温，单位：摄氏度。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>实时气温，单位：摄氏度。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
<a name="getWindDirection--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWindDirection</h4>
<pre>public&nbsp;java.lang.String&nbsp;getWindDirection()</pre>
<div class="block">返回风向。详细类型参见开发指南--天气对照表。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>风向。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
<a name="getWindPower--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWindPower</h4>
<pre>public&nbsp;java.lang.String&nbsp;getWindPower()</pre>
<div class="block">返回风力，单位：级。详细类型参见开发指南--天气对照表。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>风力，单位：级。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
<a name="getHumidity--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHumidity</h4>
<pre>public&nbsp;java.lang.String&nbsp;getHumidity()</pre>
<div class="block">返回空气湿度的百分比。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>空气湿度的百分比。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
<a name="getReportTime--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getReportTime</h4>
<pre>public&nbsp;java.lang.String&nbsp;getReportTime()</pre>
<div class="block">返回实时数据发布时间。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>实时数据发布时间。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/LocalWeatherLive.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/weather/LocalWeatherForecastResult.html" title="com.amap.api.services.weather中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/amap/api/services/weather/LocalWeatherLiveResult.html" title="com.amap.api.services.weather中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/weather/LocalWeatherLive.html" target="_top">框架</a></li>
<li><a href="LocalWeatherLive.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
