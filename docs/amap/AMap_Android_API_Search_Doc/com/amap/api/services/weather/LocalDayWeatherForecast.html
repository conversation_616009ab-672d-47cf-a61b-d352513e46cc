<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>LocalDayWeatherForecast</title>
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="LocalDayWeatherForecast";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10};
var tabs = {65535:["t0","所有方法"],2:["t2","实例方法"],8:["t4","具体方法"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/LocalDayWeatherForecast.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个类</li>
<li><a href="../../../../../com/amap/api/services/weather/LocalWeatherForecast.html" title="com.amap.api.services.weather中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/weather/LocalDayWeatherForecast.html" target="_top">框架</a></li>
<li><a href="LocalDayWeatherForecast.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.amap.api.services.weather</div>
<h2 title="类 LocalDayWeatherForecast" class="title">类 LocalDayWeatherForecast</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.amap.api.services.weather.LocalDayWeatherForecast</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">LocalDayWeatherForecast</span>
extends java.lang.Object</pre>
<div class="block">此类为预报天气中某天的天气预报类。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>所有方法</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">实例方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">具体方法</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/weather/LocalDayWeatherForecast.html#getDate--">getDate</a></span>()</code>
<div class="block">返回预报天气的年月日。</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/weather/LocalDayWeatherForecast.html#getDayTemp--">getDayTemp</a></span>()</code>
<div class="block">返回白天天气温度，单位：摄氏度。</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/weather/LocalDayWeatherForecast.html#getDayWeather--">getDayWeather</a></span>()</code>
<div class="block">返回白天天气现象，如“晴”、“多云”。</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/weather/LocalDayWeatherForecast.html#getDayWindDirection--">getDayWindDirection</a></span>()</code>
<div class="block">返回白天风向。</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/weather/LocalDayWeatherForecast.html#getDayWindPower--">getDayWindPower</a></span>()</code>
<div class="block">返回白天风力，单位：级。</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/weather/LocalDayWeatherForecast.html#getNightTemp--">getNightTemp</a></span>()</code>
<div class="block">返回夜间天气温度，单位：摄氏度。</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/weather/LocalDayWeatherForecast.html#getNightWeather--">getNightWeather</a></span>()</code>
<div class="block">返回夜间天气现象，如“晴”、“多云”。</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/weather/LocalDayWeatherForecast.html#getNightWindDirection--">getNightWindDirection</a></span>()</code>
<div class="block">返回夜间风向。</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/weather/LocalDayWeatherForecast.html#getNightWindPower--">getNightWindPower</a></span>()</code>
<div class="block">返回夜间风力，单位：级。</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/weather/LocalDayWeatherForecast.html#getWeek--">getWeek</a></span>()</code>
<div class="block">返回预报天气的星期。</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/weather/LocalDayWeatherForecast.html#setDate-java.lang.String-">setDate</a></span>(java.lang.String&nbsp;date)</code>
<div class="block">设置预报天气的年月日。</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/weather/LocalDayWeatherForecast.html#setDayTemp-java.lang.String-">setDayTemp</a></span>(java.lang.String&nbsp;dayTemp)</code>
<div class="block">设置白天天气温度</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/weather/LocalDayWeatherForecast.html#setDayWeather-java.lang.String-">setDayWeather</a></span>(java.lang.String&nbsp;dayWeather)</code>
<div class="block">设置白天的天气现象。</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/weather/LocalDayWeatherForecast.html#setDayWindDirection-java.lang.String-">setDayWindDirection</a></span>(java.lang.String&nbsp;dayWindDirection)</code>
<div class="block">设置白天风向。</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/weather/LocalDayWeatherForecast.html#setDayWindPower-java.lang.String-">setDayWindPower</a></span>(java.lang.String&nbsp;dayWindPower)</code>
<div class="block">设置白天风力。</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/weather/LocalDayWeatherForecast.html#setNightTemp-java.lang.String-">setNightTemp</a></span>(java.lang.String&nbsp;nightTemp)</code>
<div class="block">设置夜间天气温度。</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/weather/LocalDayWeatherForecast.html#setNightWeather-java.lang.String-">setNightWeather</a></span>(java.lang.String&nbsp;nightWeather)</code>
<div class="block">设置夜间天气现象。</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/weather/LocalDayWeatherForecast.html#setNightWindDirection-java.lang.String-">setNightWindDirection</a></span>(java.lang.String&nbsp;nightWindDirection)</code>
<div class="block">设置夜间风向。</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/weather/LocalDayWeatherForecast.html#setNightWindPower-java.lang.String-">setNightWindPower</a></span>(java.lang.String&nbsp;nightWindPower)</code>
<div class="block">设置夜间风力。</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/weather/LocalDayWeatherForecast.html#setWeek-java.lang.String-">setWeek</a></span>(java.lang.String&nbsp;week)</code>
<div class="block">设置预报天气的星期。</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="getDate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDate</h4>
<pre>public&nbsp;java.lang.String&nbsp;getDate()</pre>
<div class="block">返回预报天气的年月日。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>预报天气的时间（年月日）。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
<a name="setDate-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDate</h4>
<pre>public&nbsp;void&nbsp;setDate(java.lang.String&nbsp;date)</pre>
<div class="block">设置预报天气的年月日。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>date</code> - 预报天气的时间（年月日）。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
<a name="getWeek--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWeek</h4>
<pre>public&nbsp;java.lang.String&nbsp;getWeek()</pre>
<div class="block">返回预报天气的星期。返回阿拉伯数字1-7。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>预报天气的星期。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
<a name="setWeek-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setWeek</h4>
<pre>public&nbsp;void&nbsp;setWeek(java.lang.String&nbsp;week)</pre>
<div class="block">设置预报天气的星期。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>week</code> - 预报天气的星期，阿拉伯数字1-7。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
<a name="getDayWeather--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDayWeather</h4>
<pre>public&nbsp;java.lang.String&nbsp;getDayWeather()</pre>
<div class="block">返回白天天气现象，如“晴”、“多云”。详细类型参见开发指南--天气对照表。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>天气现象描述。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
<a name="setDayWeather-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDayWeather</h4>
<pre>public&nbsp;void&nbsp;setDayWeather(java.lang.String&nbsp;dayWeather)</pre>
<div class="block">设置白天的天气现象。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>dayWeather</code> - 天气现象描述。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
<a name="getNightWeather--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNightWeather</h4>
<pre>public&nbsp;java.lang.String&nbsp;getNightWeather()</pre>
<div class="block">返回夜间天气现象，如“晴”、“多云”。详细类型参见开发指南--天气对照表。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>晚上天气现象。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
<a name="setNightWeather-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setNightWeather</h4>
<pre>public&nbsp;void&nbsp;setNightWeather(java.lang.String&nbsp;nightWeather)</pre>
<div class="block">设置夜间天气现象。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>nightWeather</code> - 夜间天气现象。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
<a name="getDayTemp--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDayTemp</h4>
<pre>public&nbsp;java.lang.String&nbsp;getDayTemp()</pre>
<div class="block">返回白天天气温度，单位：摄氏度。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>白天的天气温度。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
<a name="setDayTemp-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDayTemp</h4>
<pre>public&nbsp;void&nbsp;setDayTemp(java.lang.String&nbsp;dayTemp)</pre>
<div class="block">设置白天天气温度</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>dayTemp</code> - 白天天气温度，单位：摄氏度。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
<a name="getNightTemp--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNightTemp</h4>
<pre>public&nbsp;java.lang.String&nbsp;getNightTemp()</pre>
<div class="block">返回夜间天气温度，单位：摄氏度。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>夜间的天气温度。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
<a name="setNightTemp-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setNightTemp</h4>
<pre>public&nbsp;void&nbsp;setNightTemp(java.lang.String&nbsp;nightTemp)</pre>
<div class="block">设置夜间天气温度。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>nightTemp</code> - 夜间天气温度，单位：摄氏度。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
<a name="getDayWindDirection--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDayWindDirection</h4>
<pre>public&nbsp;java.lang.String&nbsp;getDayWindDirection()</pre>
<div class="block">返回白天风向。详细类型参见开发指南--天气对照表。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>白天风向。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
<a name="setDayWindDirection-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDayWindDirection</h4>
<pre>public&nbsp;void&nbsp;setDayWindDirection(java.lang.String&nbsp;dayWindDirection)</pre>
<div class="block">设置白天风向。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>dayWindDirection</code> - 白天风向。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
<a name="getNightWindDirection--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNightWindDirection</h4>
<pre>public&nbsp;java.lang.String&nbsp;getNightWindDirection()</pre>
<div class="block">返回夜间风向。详细类型参见开发指南--天气对照表。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>夜间风向。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
<a name="setNightWindDirection-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setNightWindDirection</h4>
<pre>public&nbsp;void&nbsp;setNightWindDirection(java.lang.String&nbsp;nightWindDirection)</pre>
<div class="block">设置夜间风向。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>nightWindDirection</code> - 夜间风向。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
<a name="getDayWindPower--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDayWindPower</h4>
<pre>public&nbsp;java.lang.String&nbsp;getDayWindPower()</pre>
<div class="block">返回白天风力，单位：级。详细类型参见开发指南--天气对照表。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>白天风力。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
<a name="setDayWindPower-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDayWindPower</h4>
<pre>public&nbsp;void&nbsp;setDayWindPower(java.lang.String&nbsp;dayWindPower)</pre>
<div class="block">设置白天风力。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>dayWindPower</code> - 白天风力，单位：级。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
<a name="getNightWindPower--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNightWindPower</h4>
<pre>public&nbsp;java.lang.String&nbsp;getNightWindPower()</pre>
<div class="block">返回夜间风力，单位：级。详细类型参见开发指南--天气对照表。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>夜间风力。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
<a name="setNightWindPower-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setNightWindPower</h4>
<pre>public&nbsp;void&nbsp;setNightWindPower(java.lang.String&nbsp;nightWindPower)</pre>
<div class="block">设置夜间风力。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>nightWindPower</code> - 夜间风力，单位：级。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/LocalDayWeatherForecast.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个类</li>
<li><a href="../../../../../com/amap/api/services/weather/LocalWeatherForecast.html" title="com.amap.api.services.weather中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/weather/LocalDayWeatherForecast.html" target="_top">框架</a></li>
<li><a href="LocalDayWeatherForecast.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
