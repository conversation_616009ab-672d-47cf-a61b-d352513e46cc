<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>com.amap.api.services.geocoder</title>
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<h1 class="bar"><a href="../../../../../com/amap/api/services/geocoder/package-summary.html" target="classFrame">com.amap.api.services.geocoder</a></h1>
<div class="indexContainer">
<h2 title="接口">接口</h2>
<ul title="接口">
<li><a href="GeocodeSearch.OnGeocodeSearchListener.html" title="com.amap.api.services.geocoder中的接口" target="classFrame"><span class="interfaceName">GeocodeSearch.OnGeocodeSearchListener</span></a></li>
</ul>
<h2 title="类">类</h2>
<ul title="类">
<li><a href="AoiItem.html" title="com.amap.api.services.geocoder中的类" target="classFrame">AoiItem</a></li>
<li><a href="BusinessArea.html" title="com.amap.api.services.geocoder中的类" target="classFrame">BusinessArea</a></li>
<li><a href="GeocodeAddress.html" title="com.amap.api.services.geocoder中的类" target="classFrame">GeocodeAddress</a></li>
<li><a href="GeocodeQuery.html" title="com.amap.api.services.geocoder中的类" target="classFrame">GeocodeQuery</a></li>
<li><a href="GeocodeResult.html" title="com.amap.api.services.geocoder中的类" target="classFrame">GeocodeResult</a></li>
<li><a href="GeocodeSearch.html" title="com.amap.api.services.geocoder中的类" target="classFrame">GeocodeSearch</a></li>
<li><a href="RegeocodeAddress.html" title="com.amap.api.services.geocoder中的类" target="classFrame">RegeocodeAddress</a></li>
<li><a href="RegeocodeQuery.html" title="com.amap.api.services.geocoder中的类" target="classFrame">RegeocodeQuery</a></li>
<li><a href="RegeocodeResult.html" title="com.amap.api.services.geocoder中的类" target="classFrame">RegeocodeResult</a></li>
<li><a href="RegeocodeRoad.html" title="com.amap.api.services.geocoder中的类" target="classFrame">RegeocodeRoad</a></li>
<li><a href="StreetNumber.html" title="com.amap.api.services.geocoder中的类" target="classFrame">StreetNumber</a></li>
</ul>
</div>
</body>
</html>
