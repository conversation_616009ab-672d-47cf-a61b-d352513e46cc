<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>RegeocodeAddress</title>
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="RegeocodeAddress";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10};
var tabs = {65535:["t0","所有方法"],2:["t2","实例方法"],8:["t4","具体方法"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/RegeocodeAddress.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/geocoder/GeocodeSearch.OnGeocodeSearchListener.html" title="com.amap.api.services.geocoder中的接口"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/amap/api/services/geocoder/RegeocodeQuery.html" title="com.amap.api.services.geocoder中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/geocoder/RegeocodeAddress.html" target="_top">框架</a></li>
<li><a href="RegeocodeAddress.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.amap.api.services.geocoder</div>
<h2 title="类 RegeocodeAddress" class="title">类 RegeocodeAddress</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.amap.api.services.geocoder.RegeocodeAddress</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public final class <span class="typeNameLabel">RegeocodeAddress</span>
extends java.lang.Object</pre>
<div class="block">逆地理编码返回的结果。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>所有方法</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">实例方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">具体方法</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/geocoder/RegeocodeAddress.html#getAdCode--">getAdCode</a></span>()</code>
<div class="block">返回逆地理编码结果所在区（县）的编码。</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../../com/amap/api/services/geocoder/AoiItem.html" title="com.amap.api.services.geocoder中的类">AoiItem</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/geocoder/RegeocodeAddress.html#getAois--">getAois</a></span>()</code>
<div class="block">返回AOI（面状数据）的数据，如POI名称、区域编码、中心点坐标、POI类型等。</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/geocoder/RegeocodeAddress.html#getBuilding--">getBuilding</a></span>()</code>
<div class="block">逆地理编码返回的建筑物名称。</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../../com/amap/api/services/geocoder/BusinessArea.html" title="com.amap.api.services.geocoder中的类">BusinessArea</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/geocoder/RegeocodeAddress.html#getBusinessAreas--">getBusinessAreas</a></span>()</code>
<div class="block">返回商圈对象列表，若服务没有相应数据，则返回列表长度为0。</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/geocoder/RegeocodeAddress.html#getCity--">getCity</a></span>()</code>
<div class="block">逆地理编码返回的所在城市名称。</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/geocoder/RegeocodeAddress.html#getCityCode--">getCityCode</a></span>()</code>
<div class="block">返回逆地理编码结果所在城市编码。</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/geocoder/RegeocodeAddress.html#getCountry--">getCountry</a></span>()</code>
<div class="block">获取国家名称。</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/geocoder/RegeocodeAddress.html#getCountryCode--">getCountryCode</a></span>()</code>
<div class="block">海外生效
 国家简码</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../../com/amap/api/services/road/Crossroad.html" title="com.amap.api.services.road中的类">Crossroad</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/geocoder/RegeocodeAddress.html#getCrossroads--">getCrossroads</a></span>()</code>
<div class="block">逆地理编码返回的交叉路口列表。</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/geocoder/RegeocodeAddress.html#getDistrict--">getDistrict</a></span>()</code>
<div class="block">逆地理编码返回的所在区（县）名称。</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/geocoder/RegeocodeAddress.html#getFormatAddress--">getFormatAddress</a></span>()</code>
<div class="block">逆地理编码返回的格式化地址。</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/geocoder/RegeocodeAddress.html#getNeighborhood--">getNeighborhood</a></span>()</code>
<div class="block">逆地理编码返回的社区名称。</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../../com/amap/api/services/core/PoiItem.html" title="com.amap.api.services.core中的类">PoiItem</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/geocoder/RegeocodeAddress.html#getPois--">getPois</a></span>()</code>
<div class="block">逆地理编码返回的POI(兴趣点)列表。</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/geocoder/RegeocodeAddress.html#getProvince--">getProvince</a></span>()</code>
<div class="block">逆地理编码返回的所在省名称、直辖市的名称 。</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../../com/amap/api/services/geocoder/RegeocodeRoad.html" title="com.amap.api.services.geocoder中的类">RegeocodeRoad</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/geocoder/RegeocodeAddress.html#getRoads--">getRoads</a></span>()</code>
<div class="block">逆地理编码返回的道路列表。</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/services/geocoder/StreetNumber.html" title="com.amap.api.services.geocoder中的类">StreetNumber</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/geocoder/RegeocodeAddress.html#getStreetNumber--">getStreetNumber</a></span>()</code>
<div class="block">逆地理编码返回的门牌信息。</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/geocoder/RegeocodeAddress.html#getTowncode--">getTowncode</a></span>()</code>
<div class="block">返回乡镇街道编码。</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/geocoder/RegeocodeAddress.html#getTownship--">getTownship</a></span>()</code>
<div class="block">逆地理编码返回的乡镇名称。</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/geocoder/RegeocodeAddress.html#setCountryCode-java.lang.String-">setCountryCode</a></span>(java.lang.String&nbsp;countryCode)</code>
<div class="block">海外生效
 国家简码</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="getFormatAddress--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFormatAddress</h4>
<pre>public&nbsp;java.lang.String&nbsp;getFormatAddress()</pre>
<div class="block">逆地理编码返回的格式化地址。如返回北京市朝阳区方恒国际中心。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>结果的格式化地址。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="getProvince--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProvince</h4>
<pre>public&nbsp;java.lang.String&nbsp;getProvince()</pre>
<div class="block">逆地理编码返回的所在省名称、直辖市的名称 。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>结果的所在省名称、直辖市的名称。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="getCity--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCity</h4>
<pre>public&nbsp;java.lang.String&nbsp;getCity()</pre>
<div class="block">逆地理编码返回的所在城市名称。直辖市的名称和省份名称保持一致。当所在城市属于县级市的时候，此字段为空</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>结果的所在城市名称。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="getCityCode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCityCode</h4>
<pre>public&nbsp;java.lang.String&nbsp;getCityCode()</pre>
<div class="block">返回逆地理编码结果所在城市编码。直辖市的编码也会返回。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>结果的所在城市编码。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.2.1</dd>
</dl>
</li>
</ul>
<a name="getAdCode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAdCode</h4>
<pre>public&nbsp;java.lang.String&nbsp;getAdCode()</pre>
<div class="block">返回逆地理编码结果所在区（县）的编码。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>结果的所在区（县）的编码。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.2.1</dd>
</dl>
</li>
</ul>
<a name="getDistrict--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDistrict</h4>
<pre>public&nbsp;java.lang.String&nbsp;getDistrict()</pre>
<div class="block">逆地理编码返回的所在区（县）名称。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>结果的所在区（县）名称。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="getTownship--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTownship</h4>
<pre>public&nbsp;java.lang.String&nbsp;getTownship()</pre>
<div class="block">逆地理编码返回的乡镇名称。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>结果的乡镇名称。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="getNeighborhood--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNeighborhood</h4>
<pre>public&nbsp;java.lang.String&nbsp;getNeighborhood()</pre>
<div class="block">逆地理编码返回的社区名称。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>结果的社区名称。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="getBuilding--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBuilding</h4>
<pre>public&nbsp;java.lang.String&nbsp;getBuilding()</pre>
<div class="block">逆地理编码返回的建筑物名称。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>结果的建筑物名称。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="getStreetNumber--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStreetNumber</h4>
<pre>public&nbsp;<a href="../../../../../com/amap/api/services/geocoder/StreetNumber.html" title="com.amap.api.services.geocoder中的类">StreetNumber</a>&nbsp;getStreetNumber()</pre>
<div class="block">逆地理编码返回的门牌信息。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>结果的门牌信息。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="getRoads--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRoads</h4>
<pre>public&nbsp;java.util.List&lt;<a href="../../../../../com/amap/api/services/geocoder/RegeocodeRoad.html" title="com.amap.api.services.geocoder中的类">RegeocodeRoad</a>&gt;&nbsp;getRoads()</pre>
<div class="block">逆地理编码返回的道路列表。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>结果的道路列表。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="getPois--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPois</h4>
<pre>public&nbsp;java.util.List&lt;<a href="../../../../../com/amap/api/services/core/PoiItem.html" title="com.amap.api.services.core中的类">PoiItem</a>&gt;&nbsp;getPois()</pre>
<div class="block">逆地理编码返回的POI(兴趣点)列表。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>结果的POI(兴趣点)列表。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="getCrossroads--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCrossroads</h4>
<pre>public&nbsp;java.util.List&lt;<a href="../../../../../com/amap/api/services/road/Crossroad.html" title="com.amap.api.services.road中的类">Crossroad</a>&gt;&nbsp;getCrossroads()</pre>
<div class="block">逆地理编码返回的交叉路口列表。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>结果的交叉路口列表。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="getBusinessAreas--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBusinessAreas</h4>
<pre>public&nbsp;java.util.List&lt;<a href="../../../../../com/amap/api/services/geocoder/BusinessArea.html" title="com.amap.api.services.geocoder中的类">BusinessArea</a>&gt;&nbsp;getBusinessAreas()</pre>
<div class="block">返回商圈对象列表，若服务没有相应数据，则返回列表长度为0。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>商圈对象列表。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.3.1</dd>
</dl>
</li>
</ul>
<a name="getAois--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAois</h4>
<pre>public&nbsp;java.util.List&lt;<a href="../../../../../com/amap/api/services/geocoder/AoiItem.html" title="com.amap.api.services.geocoder中的类">AoiItem</a>&gt;&nbsp;getAois()</pre>
<div class="block">返回AOI（面状数据）的数据，如POI名称、区域编码、中心点坐标、POI类型等。AOI，是指含有区域范围的POI，如XX小区、XX大厦等。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>AOI（面状数据）的数据列表.</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
</dl>
</li>
</ul>
<a name="getTowncode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTowncode</h4>
<pre>public&nbsp;java.lang.String&nbsp;getTowncode()</pre>
<div class="block">返回乡镇街道编码。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>乡镇街道编码。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
</dl>
</li>
</ul>
<a name="getCountry--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCountry</h4>
<pre>public&nbsp;java.lang.String&nbsp;getCountry()</pre>
<div class="block">获取国家名称。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>返回国家名称</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>5.7.0</dd>
</dl>
</li>
</ul>
<a name="getCountryCode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCountryCode</h4>
<pre>public&nbsp;java.lang.String&nbsp;getCountryCode()</pre>
<div class="block">海外生效
 国家简码</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>7.4.0</dd>
</dl>
</li>
</ul>
<a name="setCountryCode-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setCountryCode</h4>
<pre>public&nbsp;void&nbsp;setCountryCode(java.lang.String&nbsp;countryCode)</pre>
<div class="block">海外生效
 国家简码</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>7.4.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/RegeocodeAddress.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/geocoder/GeocodeSearch.OnGeocodeSearchListener.html" title="com.amap.api.services.geocoder中的接口"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/amap/api/services/geocoder/RegeocodeQuery.html" title="com.amap.api.services.geocoder中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/geocoder/RegeocodeAddress.html" target="_top">框架</a></li>
<li><a href="RegeocodeAddress.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
