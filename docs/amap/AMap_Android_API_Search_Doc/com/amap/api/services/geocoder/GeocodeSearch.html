<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>GeocodeSearch</title>
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="GeocodeSearch";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10};
var tabs = {65535:["t0","所有方法"],2:["t2","实例方法"],8:["t4","具体方法"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/GeocodeSearch.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/geocoder/GeocodeResult.html" title="com.amap.api.services.geocoder中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/amap/api/services/geocoder/GeocodeSearch.OnGeocodeSearchListener.html" title="com.amap.api.services.geocoder中的接口"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/geocoder/GeocodeSearch.html" target="_top">框架</a></li>
<li><a href="GeocodeSearch.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li><a href="#nested.class.summary">嵌套</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li><a href="#field.detail">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.amap.api.services.geocoder</div>
<h2 title="类 GeocodeSearch" class="title">类 GeocodeSearch</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.amap.api.services.geocoder.GeocodeSearch</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public final class <span class="typeNameLabel">GeocodeSearch</span>
extends java.lang.Object</pre>
<div class="block">地理编码与逆地理编码类。
 
 地理编码又称地址匹配，指的是从已知的地址描述到对应的经纬 度坐标的转换，即根据地址信息，获取地址所对应的点坐标等。
 
 逆地理编码即地址解析服务，具体是指从已知的经纬度坐标到对 应的地址描述（如省市、街区、楼层、房间等）的转换。
 
 通过该类提供的方法可获取对应位置的地理描述，分为3种地物 类型：交叉路口、兴趣点、道路。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>嵌套类概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="嵌套类概要表, 列表嵌套类和解释">
<caption><span>嵌套类</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">类和说明</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static interface&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/geocoder/GeocodeSearch.OnGeocodeSearchListener.html" title="com.amap.api.services.geocoder中的接口">GeocodeSearch.OnGeocodeSearchListener</a></span></code>
<div class="block">此接口定义了逆地理编码异步处理回调接口。</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>字段概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="字段概要表, 列表字段和解释">
<caption><span>字段</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">字段和说明</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/geocoder/GeocodeSearch.html#AMAP">AMAP</a></span></code>
<div class="block">输入参数坐标为高德类型。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/geocoder/GeocodeSearch.html#EXTENSIONS_ALL">EXTENSIONS_ALL</a></span></code>
<div class="block">扩展字段all，会返回完整参数</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/geocoder/GeocodeSearch.html#EXTENSIONS_BASE">EXTENSIONS_BASE</a></span></code>
<div class="block">扩展字段base，会返回部分参数</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/geocoder/GeocodeSearch.html#GPS">GPS</a></span></code>
<div class="block">输入参数坐标为GPS类型。</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/geocoder/GeocodeSearch.html#GeocodeSearch-Context-">GeocodeSearch</a></span>(Context&nbsp;context)</code>
<div class="block">根据给定的参数来构造一个GeocodeSearch 新对象。</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>所有方法</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">实例方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">具体方法</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/services/geocoder/RegeocodeAddress.html" title="com.amap.api.services.geocoder中的类">RegeocodeAddress</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/geocoder/GeocodeSearch.html#getFromLocation-com.amap.api.services.geocoder.RegeocodeQuery-">getFromLocation</a></span>(<a href="../../../../../com/amap/api/services/geocoder/RegeocodeQuery.html" title="com.amap.api.services.geocoder中的类">RegeocodeQuery</a>&nbsp;regeocodeQuery)</code>
<div class="block">根据给定的经纬度和最大结果数返回逆地理编码的结果列表。</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/geocoder/GeocodeSearch.html#getFromLocationAsyn-com.amap.api.services.geocoder.RegeocodeQuery-">getFromLocationAsyn</a></span>(<a href="../../../../../com/amap/api/services/geocoder/RegeocodeQuery.html" title="com.amap.api.services.geocoder中的类">RegeocodeQuery</a>&nbsp;regeocodeQuery)</code>
<div class="block">逆地理编码查询的异步处理调用。</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../../com/amap/api/services/geocoder/GeocodeAddress.html" title="com.amap.api.services.geocoder中的类">GeocodeAddress</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/geocoder/GeocodeSearch.html#getFromLocationName-com.amap.api.services.geocoder.GeocodeQuery-">getFromLocationName</a></span>(<a href="../../../../../com/amap/api/services/geocoder/GeocodeQuery.html" title="com.amap.api.services.geocoder中的类">GeocodeQuery</a>&nbsp;geocodeQuery)</code>
<div class="block">根据给定的地理名称和查询城市，返回地理编码的结果列表。</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/geocoder/GeocodeSearch.html#getFromLocationNameAsyn-com.amap.api.services.geocoder.GeocodeQuery-">getFromLocationNameAsyn</a></span>(<a href="../../../../../com/amap/api/services/geocoder/GeocodeQuery.html" title="com.amap.api.services.geocoder中的类">GeocodeQuery</a>&nbsp;geocodeQuery)</code>
<div class="block">地理编码查询的异步处理调用。</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/geocoder/GeocodeSearch.html#setOnGeocodeSearchListener-com.amap.api.services.geocoder.GeocodeSearch.OnGeocodeSearchListener-">setOnGeocodeSearchListener</a></span>(<a href="../../../../../com/amap/api/services/geocoder/GeocodeSearch.OnGeocodeSearchListener.html" title="com.amap.api.services.geocoder中的接口">GeocodeSearch.OnGeocodeSearchListener</a>&nbsp;onGeocodeSearchListener)</code>
<div class="block">地理编码查询结果监听接口设置。</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>字段详细资料</h3>
<a name="GPS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>GPS</h4>
<pre>public static final&nbsp;java.lang.String GPS</pre>
<div class="block">输入参数坐标为GPS类型。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.geocoder.GeocodeSearch.GPS">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="AMAP">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>AMAP</h4>
<pre>public static final&nbsp;java.lang.String AMAP</pre>
<div class="block">输入参数坐标为高德类型。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.geocoder.GeocodeSearch.AMAP">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="EXTENSIONS_ALL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>EXTENSIONS_ALL</h4>
<pre>public static final&nbsp;java.lang.String EXTENSIONS_ALL</pre>
<div class="block">扩展字段all，会返回完整参数</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>7.6.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.geocoder.GeocodeSearch.EXTENSIONS_ALL">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="EXTENSIONS_BASE">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>EXTENSIONS_BASE</h4>
<pre>public static final&nbsp;java.lang.String EXTENSIONS_BASE</pre>
<div class="block">扩展字段base，会返回部分参数</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>7.6.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.geocoder.GeocodeSearch.EXTENSIONS_BASE">常量字段值</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="GeocodeSearch-Context-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>GeocodeSearch</h4>
<pre>public&nbsp;GeocodeSearch(Context&nbsp;context)
              throws <a href="../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></pre>
<div class="block">根据给定的参数来构造一个GeocodeSearch 新对象。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>context</code> - 一个Context，Context 指的是一个全局变量，程序的运行环境。</dd>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code><a href="../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></code></dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="getFromLocation-com.amap.api.services.geocoder.RegeocodeQuery-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFromLocation</h4>
<pre>public&nbsp;<a href="../../../../../com/amap/api/services/geocoder/RegeocodeAddress.html" title="com.amap.api.services.geocoder中的类">RegeocodeAddress</a>&nbsp;getFromLocation(<a href="../../../../../com/amap/api/services/geocoder/RegeocodeQuery.html" title="com.amap.api.services.geocoder中的类">RegeocodeQuery</a>&nbsp;regeocodeQuery)
                                 throws <a href="../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></pre>
<div class="block">根据给定的经纬度和最大结果数返回逆地理编码的结果列表。逆地理编码兴趣点返回结果最大返回数目为10，道路和交叉路口返回最大数目为3。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>regeocodeQuery</code> - 要进行逆地理编码查询的条件。</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>逆地理编码的结果列表。</dd>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code><a href="../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></code></dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="getFromLocationName-com.amap.api.services.geocoder.GeocodeQuery-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFromLocationName</h4>
<pre>public&nbsp;java.util.List&lt;<a href="../../../../../com/amap/api/services/geocoder/GeocodeAddress.html" title="com.amap.api.services.geocoder中的类">GeocodeAddress</a>&gt;&nbsp;getFromLocationName(<a href="../../../../../com/amap/api/services/geocoder/GeocodeQuery.html" title="com.amap.api.services.geocoder中的类">GeocodeQuery</a>&nbsp;geocodeQuery)
                                                   throws <a href="../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></pre>
<div class="block">根据给定的地理名称和查询城市，返回地理编码的结果列表。地理编码返回结果集默认最大返回数目为10。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>geocodeQuery</code> - 地理编码的查询条件。</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>地理编码的结果列表。</dd>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code><a href="../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></code></dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="setOnGeocodeSearchListener-com.amap.api.services.geocoder.GeocodeSearch.OnGeocodeSearchListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setOnGeocodeSearchListener</h4>
<pre>public&nbsp;void&nbsp;setOnGeocodeSearchListener(<a href="../../../../../com/amap/api/services/geocoder/GeocodeSearch.OnGeocodeSearchListener.html" title="com.amap.api.services.geocoder中的接口">GeocodeSearch.OnGeocodeSearchListener</a>&nbsp;onGeocodeSearchListener)</pre>
<div class="block">地理编码查询结果监听接口设置。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>onGeocodeSearchListener</code> - 地理编码查询结果监听接口。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="getFromLocationAsyn-com.amap.api.services.geocoder.RegeocodeQuery-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFromLocationAsyn</h4>
<pre>public&nbsp;void&nbsp;getFromLocationAsyn(<a href="../../../../../com/amap/api/services/geocoder/RegeocodeQuery.html" title="com.amap.api.services.geocoder中的类">RegeocodeQuery</a>&nbsp;regeocodeQuery)</pre>
<div class="block">逆地理编码查询的异步处理调用。
 根据给定的经纬度和最大结果数返回逆地理编码的结果列表。逆地理编码兴趣点返回结果最大返回数目为10，道路和交叉路口返回最大数目为3。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>regeocodeQuery</code> - 要进行逆地理编码查询的条件。</dd>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code><a href="../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></code></dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="getFromLocationNameAsyn-com.amap.api.services.geocoder.GeocodeQuery-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getFromLocationNameAsyn</h4>
<pre>public&nbsp;void&nbsp;getFromLocationNameAsyn(<a href="../../../../../com/amap/api/services/geocoder/GeocodeQuery.html" title="com.amap.api.services.geocoder中的类">GeocodeQuery</a>&nbsp;geocodeQuery)</pre>
<div class="block">地理编码查询的异步处理调用。
 根据给定的地理名称和查询城市返回地理编码的结果列表。地理编码返回结果集默认最大返回数目为10。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>geocodeQuery</code> - 地理编码的查询条件。</dd>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code><a href="../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></code></dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/GeocodeSearch.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/geocoder/GeocodeResult.html" title="com.amap.api.services.geocoder中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/amap/api/services/geocoder/GeocodeSearch.OnGeocodeSearchListener.html" title="com.amap.api.services.geocoder中的接口"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/geocoder/GeocodeSearch.html" target="_top">框架</a></li>
<li><a href="GeocodeSearch.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li><a href="#nested.class.summary">嵌套</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li><a href="#field.detail">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
