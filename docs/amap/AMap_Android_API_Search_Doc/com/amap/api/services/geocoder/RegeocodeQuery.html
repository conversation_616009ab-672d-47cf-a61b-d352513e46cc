<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>RegeocodeQuery</title>
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="RegeocodeQuery";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10};
var tabs = {65535:["t0","所有方法"],2:["t2","实例方法"],8:["t4","具体方法"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/RegeocodeQuery.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/geocoder/RegeocodeAddress.html" title="com.amap.api.services.geocoder中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/amap/api/services/geocoder/RegeocodeResult.html" title="com.amap.api.services.geocoder中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/geocoder/RegeocodeQuery.html" target="_top">框架</a></li>
<li><a href="RegeocodeQuery.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.amap.api.services.geocoder</div>
<h2 title="类 RegeocodeQuery" class="title">类 RegeocodeQuery</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.amap.api.services.geocoder.RegeocodeQuery</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">RegeocodeQuery</span>
extends java.lang.Object</pre>
<div class="block">此类定义了逆地理编码查询的地理坐标点、查询范围、坐标类型。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/geocoder/RegeocodeQuery.html#RegeocodeQuery-com.amap.api.services.core.LatLonPoint-float-java.lang.String-">RegeocodeQuery</a></span>(<a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&nbsp;point,
              float&nbsp;radius,
              java.lang.String&nbsp;latLonType)</code>
<div class="block">RegeocodeQuery构造函数。</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>所有方法</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">实例方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">具体方法</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/geocoder/RegeocodeQuery.html#equals-java.lang.Object-">equals</a></span>(java.lang.Object&nbsp;obj)</code>
<div class="block">比较两个查询条件是否相同。</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/geocoder/RegeocodeQuery.html#getExtensions--">getExtensions</a></span>()</code>
<div class="block">扩展字段 base表示只返回基础数据，all表示所有数据 ，默认 base</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/geocoder/RegeocodeQuery.html#getLatLonType--">getLatLonType</a></span>()</code>
<div class="block">返回参数坐标类型。</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/geocoder/RegeocodeQuery.html#getMode--">getMode</a></span>()</code>
<div class="block">海外生效
 返回策略，如传入无效mode值，则走默认策略
 distance 按距离返回
 score 按权重返回</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/geocoder/RegeocodeQuery.html#getPoint--">getPoint</a></span>()</code>
<div class="block">返回逆地理编码的地理坐标点。</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/geocoder/RegeocodeQuery.html#getPoiType--">getPoiType</a></span>()</code>
<div class="block">返回设置的附近POI类型。</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/geocoder/RegeocodeQuery.html#getRadius--">getRadius</a></span>()</code>
<div class="block">返回查找范围。</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/geocoder/RegeocodeQuery.html#setExtensions-java.lang.String-">setExtensions</a></span>(java.lang.String&nbsp;extensions)</code>
<div class="block">扩展字段 base表示只返回基础数据，all表示所有数据 ，默认 base</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/geocoder/RegeocodeQuery.html#setLatLonType-java.lang.String-">setLatLonType</a></span>(java.lang.String&nbsp;latLonType)</code>
<div class="block">设置参数坐标类型。</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/geocoder/RegeocodeQuery.html#setMode-java.lang.String-">setMode</a></span>(java.lang.String&nbsp;mode)</code>
<div class="block">海外生效
 返回策略，如传入无效mode值，则走默认策略
 distance 按距离返回
 score 按权重返回</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/geocoder/RegeocodeQuery.html#setPoint-com.amap.api.services.core.LatLonPoint-">setPoint</a></span>(<a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&nbsp;point)</code>
<div class="block">设置逆地理编码的地理坐标点。</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/geocoder/RegeocodeQuery.html#setPoiType-java.lang.String-">setPoiType</a></span>(java.lang.String&nbsp;poitype)</code>
<div class="block">设置附近POI类型，结果将会围绕这些类型进行返回。</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/geocoder/RegeocodeQuery.html#setRadius-float-">setRadius</a></span>(float&nbsp;radius)</code>
<div class="block">设置查找范围。</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="RegeocodeQuery-com.amap.api.services.core.LatLonPoint-float-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>RegeocodeQuery</h4>
<pre>public&nbsp;RegeocodeQuery(<a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&nbsp;point,
                      float&nbsp;radius,
                      java.lang.String&nbsp;latLonType)</pre>
<div class="block">RegeocodeQuery构造函数。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>point</code> - 要进行逆地理编码的地理坐标点。</dd>
<dd><code>radius</code> - 查找范围。默认值为1000，取值范围1-3000，单位米。</dd>
<dd><code>latLonType</code> - 输入参数坐标类型。包含GPS坐标和高德坐标。 可以参考<a href="../../../../../com/amap/api/services/geocoder/RegeocodeQuery.html#setLatLonType-java.lang.String-"><code>RegeocodeQuery.setLatLonType(String)</code></a></dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="getPoint--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPoint</h4>
<pre>public&nbsp;<a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&nbsp;getPoint()</pre>
<div class="block">返回逆地理编码的地理坐标点。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>该结果的逆地理编码的地理坐标点。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="setPoint-com.amap.api.services.core.LatLonPoint-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPoint</h4>
<pre>public&nbsp;void&nbsp;setPoint(<a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&nbsp;point)</pre>
<div class="block">设置逆地理编码的地理坐标点。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>point</code> - 逆地理编码的地理坐标点。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="getRadius--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRadius</h4>
<pre>public&nbsp;float&nbsp;getRadius()</pre>
<div class="block">返回查找范围。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>查找的范围。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="setRadius-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRadius</h4>
<pre>public&nbsp;void&nbsp;setRadius(float&nbsp;radius)</pre>
<div class="block">设置查找范围。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>radius</code> - 设置查找的范围。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="getLatLonType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLatLonType</h4>
<pre>public&nbsp;java.lang.String&nbsp;getLatLonType()</pre>
<div class="block">返回参数坐标类型。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>参数坐标类型。包含GPS坐标和高德坐标。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="setLatLonType-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLatLonType</h4>
<pre>public&nbsp;void&nbsp;setLatLonType(java.lang.String&nbsp;latLonType)</pre>
<div class="block">设置参数坐标类型。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>latLonType</code> - 参数坐标类型。包含GPS坐标和高德坐标。
                    可选 <a href="../../../../../com/amap/api/services/geocoder/GeocodeSearch.html#AMAP"><code>GeocodeSearch.AMAP</code></a> 、<a href="../../../../../com/amap/api/services/geocoder/GeocodeSearch.html#GPS"><code>GeocodeSearch.GPS</code></a></dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="getPoiType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPoiType</h4>
<pre>public&nbsp;java.lang.String&nbsp;getPoiType()</pre>
<div class="block">返回设置的附近POI类型。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>附近POI类型。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>5.1.0</dd>
</dl>
</li>
</ul>
<a name="setPoiType-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPoiType</h4>
<pre>public&nbsp;void&nbsp;setPoiType(java.lang.String&nbsp;poitype)</pre>
<div class="block">设置附近POI类型，结果将会围绕这些类型进行返回。
 支持传入多个POI类型，多值间用“|”分隔。<br></div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>5.1.0</dd>
</dl>
</li>
</ul>
<a name="getMode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMode</h4>
<pre>public&nbsp;java.lang.String&nbsp;getMode()</pre>
<div class="block">海外生效
 返回策略，如传入无效mode值，则走默认策略
 distance 按距离返回
 score 按权重返回</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>7.4.0</dd>
</dl>
</li>
</ul>
<a name="setMode-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMode</h4>
<pre>public&nbsp;void&nbsp;setMode(java.lang.String&nbsp;mode)</pre>
<div class="block">海外生效
 返回策略，如传入无效mode值，则走默认策略
 distance 按距离返回
 score 按权重返回</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>7.4.0</dd>
</dl>
</li>
</ul>
<a name="getExtensions--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getExtensions</h4>
<pre>public&nbsp;java.lang.String&nbsp;getExtensions()</pre>
<div class="block">扩展字段 base表示只返回基础数据，all表示所有数据 ，默认 base</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>7.6.0</dd>
</dl>
</li>
</ul>
<a name="setExtensions-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setExtensions</h4>
<pre>public&nbsp;void&nbsp;setExtensions(java.lang.String&nbsp;extensions)</pre>
<div class="block">扩展字段 base表示只返回基础数据，all表示所有数据 ，默认 base</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>7.6.0</dd>
</dl>
</li>
</ul>
<a name="equals-java.lang.Object-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>equals</h4>
<pre>public&nbsp;boolean&nbsp;equals(java.lang.Object&nbsp;obj)</pre>
<div class="block">比较两个查询条件是否相同。</div>
<dl>
<dt><span class="overrideSpecifyLabel">覆盖:</span></dt>
<dd><code>equals</code>&nbsp;在类中&nbsp;<code>java.lang.Object</code></dd>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>obj</code> - 查询条件。</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>查询条件是否相同。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/RegeocodeQuery.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/geocoder/RegeocodeAddress.html" title="com.amap.api.services.geocoder中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/amap/api/services/geocoder/RegeocodeResult.html" title="com.amap.api.services.geocoder中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/geocoder/RegeocodeQuery.html" target="_top">框架</a></li>
<li><a href="RegeocodeQuery.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
