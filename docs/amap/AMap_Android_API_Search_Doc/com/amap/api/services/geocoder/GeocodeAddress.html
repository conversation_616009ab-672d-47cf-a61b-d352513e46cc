<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>GeocodeAddress</title>
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="GeocodeAddress";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10};
var tabs = {65535:["t0","所有方法"],2:["t2","实例方法"],8:["t4","具体方法"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/GeocodeAddress.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/geocoder/BusinessArea.html" title="com.amap.api.services.geocoder中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/amap/api/services/geocoder/GeocodeQuery.html" title="com.amap.api.services.geocoder中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/geocoder/GeocodeAddress.html" target="_top">框架</a></li>
<li><a href="GeocodeAddress.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.amap.api.services.geocoder</div>
<h2 title="类 GeocodeAddress" class="title">类 GeocodeAddress</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.amap.api.services.geocoder.GeocodeAddress</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public final class <span class="typeNameLabel">GeocodeAddress</span>
extends java.lang.Object</pre>
<div class="block">地理编码返回的结果</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>所有方法</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">实例方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">具体方法</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/geocoder/GeocodeAddress.html#getAdcode--">getAdcode</a></span>()</code>
<div class="block">地理编码返回的区域编码。</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/geocoder/GeocodeAddress.html#getBuilding--">getBuilding</a></span>()</code>
<div class="block">地理编码返回的建筑物名称。</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/geocoder/GeocodeAddress.html#getCity--">getCity</a></span>()</code>
<div class="block">地理编码返回的所在城市名称。</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/geocoder/GeocodeAddress.html#getCountry--">getCountry</a></span>()</code>
<div class="block">海外生效
 国家名称</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/geocoder/GeocodeAddress.html#getDistrict--">getDistrict</a></span>()</code>
<div class="block">地理编码返回的所在区（县）名称。</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/geocoder/GeocodeAddress.html#getFormatAddress--">getFormatAddress</a></span>()</code>
<div class="block">地理编码返回的格式化地址。</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/geocoder/GeocodeAddress.html#getLatLonPoint--">getLatLonPoint</a></span>()</code>
<div class="block">地理编码返回的经纬度坐标。</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/geocoder/GeocodeAddress.html#getLevel--">getLevel</a></span>()</code>
<div class="block">地理编码返回的匹配级别。</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/geocoder/GeocodeAddress.html#getNeighborhood--">getNeighborhood</a></span>()</code>
<div class="block">地理编码返回的社区名称。</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/geocoder/GeocodeAddress.html#getPostcode--">getPostcode</a></span>()</code>
<div class="block">海外生效
 邮政编码</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/geocoder/GeocodeAddress.html#getProvince--">getProvince</a></span>()</code>
<div class="block">地理编码返回的所在省名称、直辖市的名称 。</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/geocoder/GeocodeAddress.html#getTownship--">getTownship</a></span>()</code>
<div class="block">地理编码返回的乡镇名称。</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/geocoder/GeocodeAddress.html#setCountry-java.lang.String-">setCountry</a></span>(java.lang.String&nbsp;country)</code>
<div class="block">海外生效
 国家名称</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/geocoder/GeocodeAddress.html#setPostcode-java.lang.String-">setPostcode</a></span>(java.lang.String&nbsp;postcode)</code>
<div class="block">海外生效
 邮政编码</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="getFormatAddress--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFormatAddress</h4>
<pre>public&nbsp;java.lang.String&nbsp;getFormatAddress()</pre>
<div class="block">地理编码返回的格式化地址。如返回北京市朝阳区方恒国际中心。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>结果的格式化地址。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="getProvince--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProvince</h4>
<pre>public&nbsp;java.lang.String&nbsp;getProvince()</pre>
<div class="block">地理编码返回的所在省名称、直辖市的名称 。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>结果的所在省名称、直辖市的名称。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="getCity--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCity</h4>
<pre>public&nbsp;java.lang.String&nbsp;getCity()</pre>
<div class="block">地理编码返回的所在城市名称。直辖市的名称和省份名称保持一致。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>结果的所在城市名称。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="getDistrict--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDistrict</h4>
<pre>public&nbsp;java.lang.String&nbsp;getDistrict()</pre>
<div class="block">地理编码返回的所在区（县）名称。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>结果的所在区（县）名称。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="getTownship--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTownship</h4>
<pre>public&nbsp;java.lang.String&nbsp;getTownship()</pre>
<div class="block">地理编码返回的乡镇名称。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>结果的乡镇名称。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="getNeighborhood--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNeighborhood</h4>
<pre>public&nbsp;java.lang.String&nbsp;getNeighborhood()</pre>
<div class="block">地理编码返回的社区名称。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>结果的社区名称。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="getBuilding--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBuilding</h4>
<pre>public&nbsp;java.lang.String&nbsp;getBuilding()</pre>
<div class="block">地理编码返回的建筑物名称。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>结果的建筑物名称。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="getAdcode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAdcode</h4>
<pre>public&nbsp;java.lang.String&nbsp;getAdcode()</pre>
<div class="block">地理编码返回的区域编码。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>结果的区域编码。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="getLatLonPoint--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLatLonPoint</h4>
<pre>public&nbsp;<a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&nbsp;getLatLonPoint()</pre>
<div class="block">地理编码返回的经纬度坐标。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>结果的经纬度坐标。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="getLevel--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLevel</h4>
<pre>public&nbsp;java.lang.String&nbsp;getLevel()</pre>
<div class="block">地理编码返回的匹配级别。匹配级别有“兴趣点”、“交叉路口”、“道路”。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>结果的匹配级别。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="getCountry--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCountry</h4>
<pre>public&nbsp;java.lang.String&nbsp;getCountry()</pre>
<div class="block">海外生效
 国家名称</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>7.4.0</dd>
</dl>
</li>
</ul>
<a name="setCountry-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCountry</h4>
<pre>public&nbsp;void&nbsp;setCountry(java.lang.String&nbsp;country)</pre>
<div class="block">海外生效
 国家名称</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>7.4.0</dd>
</dl>
</li>
</ul>
<a name="getPostcode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPostcode</h4>
<pre>public&nbsp;java.lang.String&nbsp;getPostcode()</pre>
<div class="block">海外生效
 邮政编码</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>7.4.0</dd>
</dl>
</li>
</ul>
<a name="setPostcode-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setPostcode</h4>
<pre>public&nbsp;void&nbsp;setPostcode(java.lang.String&nbsp;postcode)</pre>
<div class="block">海外生效
 邮政编码</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>7.4.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/GeocodeAddress.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/geocoder/BusinessArea.html" title="com.amap.api.services.geocoder中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/amap/api/services/geocoder/GeocodeQuery.html" title="com.amap.api.services.geocoder中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/geocoder/GeocodeAddress.html" target="_top">框架</a></li>
<li><a href="GeocodeAddress.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
