<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>StreetNumber</title>
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="StreetNumber";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10};
var tabs = {65535:["t0","所有方法"],2:["t2","实例方法"],8:["t4","具体方法"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/StreetNumber.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/geocoder/RegeocodeRoad.html" title="com.amap.api.services.geocoder中的类"><span class="typeNameLink">上一个类</span></a></li>
<li>下一个类</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/geocoder/StreetNumber.html" target="_top">框架</a></li>
<li><a href="StreetNumber.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.amap.api.services.geocoder</div>
<h2 title="类 StreetNumber" class="title">类 StreetNumber</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.amap.api.services.geocoder.StreetNumber</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public final class <span class="typeNameLabel">StreetNumber</span>
extends java.lang.Object</pre>
<div class="block">逆地理编码返回结果的门牌信息对象。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/geocoder/StreetNumber.html#StreetNumber--">StreetNumber</a></span>()</code>
<div class="block">StreetNumber的构造方法。</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>所有方法</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">实例方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">具体方法</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/geocoder/StreetNumber.html#getDirection--">getDirection</a></span>()</code>
<div class="block">返回门牌信息中的方向 ，指结果点相对地理坐标点的方向。</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/geocoder/StreetNumber.html#getDistance--">getDistance</a></span>()</code>
<div class="block">返回门牌信息中地理坐标点与结果点的垂直距离。</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/geocoder/StreetNumber.html#getLatLonPoint--">getLatLonPoint</a></span>()</code>
<div class="block">返回门牌信息中的经纬度坐标。</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/geocoder/StreetNumber.html#getNumber--">getNumber</a></span>()</code>
<div class="block">返回门牌信息中的门牌号码。</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/geocoder/StreetNumber.html#getStreet--">getStreet</a></span>()</code>
<div class="block">返回门牌信息中的街道名称。</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/geocoder/StreetNumber.html#setDirection-java.lang.String-">setDirection</a></span>(java.lang.String&nbsp;direction)</code>
<div class="block">设置门牌信息中的方向 ，指结果点相对地理坐标点的方向。</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/geocoder/StreetNumber.html#setDistance-float-">setDistance</a></span>(float&nbsp;distance)</code>
<div class="block">设置门牌信息中地理坐标点与结果点的垂直距离。</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/geocoder/StreetNumber.html#setLatLonPoint-com.amap.api.services.core.LatLonPoint-">setLatLonPoint</a></span>(<a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&nbsp;latLonPoint)</code>
<div class="block">设置门牌信息中的经纬度坐标。</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/geocoder/StreetNumber.html#setNumber-java.lang.String-">setNumber</a></span>(java.lang.String&nbsp;number)</code>
<div class="block">设置门牌信息中的门牌号码。</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/geocoder/StreetNumber.html#setStreet-java.lang.String-">setStreet</a></span>(java.lang.String&nbsp;street)</code>
<div class="block">设置门牌信息中的街道名称。</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="StreetNumber--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>StreetNumber</h4>
<pre>public&nbsp;StreetNumber()</pre>
<div class="block">StreetNumber的构造方法。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="getStreet--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStreet</h4>
<pre>public&nbsp;java.lang.String&nbsp;getStreet()</pre>
<div class="block">返回门牌信息中的街道名称。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>结果的街道名称。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="setStreet-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setStreet</h4>
<pre>public&nbsp;void&nbsp;setStreet(java.lang.String&nbsp;street)</pre>
<div class="block">设置门牌信息中的街道名称。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>street</code> - 街道名称。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="getNumber--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNumber</h4>
<pre>public&nbsp;java.lang.String&nbsp;getNumber()</pre>
<div class="block">返回门牌信息中的门牌号码。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>结果的门牌号码。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="setNumber-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setNumber</h4>
<pre>public&nbsp;void&nbsp;setNumber(java.lang.String&nbsp;number)</pre>
<div class="block">设置门牌信息中的门牌号码。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>number</code> - 门牌号码。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="getLatLonPoint--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLatLonPoint</h4>
<pre>public&nbsp;<a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&nbsp;getLatLonPoint()</pre>
<div class="block">返回门牌信息中的经纬度坐标。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>结果的经纬度坐标。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="setLatLonPoint-com.amap.api.services.core.LatLonPoint-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLatLonPoint</h4>
<pre>public&nbsp;void&nbsp;setLatLonPoint(<a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&nbsp;latLonPoint)</pre>
<div class="block">设置门牌信息中的经纬度坐标。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>latLonPoint</code> - 经纬度坐标。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="getDirection--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDirection</h4>
<pre>public&nbsp;java.lang.String&nbsp;getDirection()</pre>
<div class="block">返回门牌信息中的方向 ，指结果点相对地理坐标点的方向。方向显示为英文名称，如South、NorthEast。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>结果的方向。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="setDirection-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDirection</h4>
<pre>public&nbsp;void&nbsp;setDirection(java.lang.String&nbsp;direction)</pre>
<div class="block">设置门牌信息中的方向 ，指结果点相对地理坐标点的方向。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>direction</code> - 方向显示为英文名称，如South、NorthEast。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="getDistance--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDistance</h4>
<pre>public&nbsp;float&nbsp;getDistance()</pre>
<div class="block">返回门牌信息中地理坐标点与结果点的垂直距离。单位米。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>结果的距离。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="setDistance-float-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setDistance</h4>
<pre>public&nbsp;void&nbsp;setDistance(float&nbsp;distance)</pre>
<div class="block">设置门牌信息中地理坐标点与结果点的垂直距离。单位米。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>distance</code> - 距离。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/StreetNumber.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/geocoder/RegeocodeRoad.html" title="com.amap.api.services.geocoder中的类"><span class="typeNameLink">上一个类</span></a></li>
<li>下一个类</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/geocoder/StreetNumber.html" target="_top">框架</a></li>
<li><a href="StreetNumber.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
