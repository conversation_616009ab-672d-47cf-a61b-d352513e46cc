<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>CloudItemDetail</title>
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="CloudItemDetail";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/CloudItemDetail.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/cloud/CloudItem.html" title="com.amap.api.services.cloud中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/amap/api/services/cloud/CloudResult.html" title="com.amap.api.services.cloud中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/cloud/CloudItemDetail.html" target="_top">框架</a></li>
<li><a href="CloudItemDetail.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.com.amap.api.services.cloud.CloudItem">字段</a>&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#methods.inherited.from.class.com.amap.api.services.cloud.CloudItem">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li>方法</li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.amap.api.services.cloud</div>
<h2 title="类 CloudItemDetail" class="title">类 CloudItemDetail</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../../../com/amap/api/services/cloud/CloudItem.html" title="com.amap.api.services.cloud中的类">com.amap.api.services.cloud.CloudItem</a></li>
<li>
<ul class="inheritance">
<li>com.amap.api.services.cloud.CloudItemDetail</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">CloudItemDetail</span>
extends <a href="../../../../../com/amap/api/services/cloud/CloudItem.html" title="com.amap.api.services.cloud中的类">CloudItem</a></pre>
<div class="block">该类定义了一个企业地图数据的详细信息。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>字段概要</h3>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.com.amap.api.services.cloud.CloudItem">
<!--   -->
</a>
<h3>从类继承的字段&nbsp;com.amap.api.services.cloud.<a href="../../../../../com/amap/api/services/cloud/CloudItem.html" title="com.amap.api.services.cloud中的类">CloudItem</a></h3>
<code><a href="../../../../../com/amap/api/services/cloud/CloudItem.html#mPoint">mPoint</a>, <a href="../../../../../com/amap/api/services/cloud/CloudItem.html#mSnippet">mSnippet</a>, <a href="../../../../../com/amap/api/services/cloud/CloudItem.html#mTitle">mTitle</a></code></li>
</ul>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.amap.api.services.cloud.CloudItem">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;com.amap.api.services.cloud.<a href="../../../../../com/amap/api/services/cloud/CloudItem.html" title="com.amap.api.services.cloud中的类">CloudItem</a></h3>
<code><a href="../../../../../com/amap/api/services/cloud/CloudItem.html#getCloudImage--">getCloudImage</a>, <a href="../../../../../com/amap/api/services/cloud/CloudItem.html#getCreatetime--">getCreatetime</a>, <a href="../../../../../com/amap/api/services/cloud/CloudItem.html#getCustomfield--">getCustomfield</a>, <a href="../../../../../com/amap/api/services/cloud/CloudItem.html#getDistance--">getDistance</a>, <a href="../../../../../com/amap/api/services/cloud/CloudItem.html#getID--">getID</a>, <a href="../../../../../com/amap/api/services/cloud/CloudItem.html#getLatLonPoint--">getLatLonPoint</a>, <a href="../../../../../com/amap/api/services/cloud/CloudItem.html#getSnippet--">getSnippet</a>, <a href="../../../../../com/amap/api/services/cloud/CloudItem.html#getTitle--">getTitle</a>, <a href="../../../../../com/amap/api/services/cloud/CloudItem.html#getUpdatetime--">getUpdatetime</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/CloudItemDetail.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/cloud/CloudItem.html" title="com.amap.api.services.cloud中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/amap/api/services/cloud/CloudResult.html" title="com.amap.api.services.cloud中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/cloud/CloudItemDetail.html" target="_top">框架</a></li>
<li><a href="CloudItemDetail.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.com.amap.api.services.cloud.CloudItem">字段</a>&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#methods.inherited.from.class.com.amap.api.services.cloud.CloudItem">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li>方法</li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
