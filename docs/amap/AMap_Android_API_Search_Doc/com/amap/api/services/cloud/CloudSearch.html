<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>CloudSearch</title>
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="CloudSearch";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10};
var tabs = {65535:["t0","所有方法"],2:["t2","实例方法"],8:["t4","具体方法"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/CloudSearch.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/cloud/CloudResult.html" title="com.amap.api.services.cloud中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/amap/api/services/cloud/CloudSearch.OnCloudSearchListener.html" title="com.amap.api.services.cloud中的接口"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/cloud/CloudSearch.html" target="_top">框架</a></li>
<li><a href="CloudSearch.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li><a href="#nested.class.summary">嵌套</a>&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.amap.api.services.cloud</div>
<h2 title="类 CloudSearch" class="title">类 CloudSearch</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.amap.api.services.cloud.CloudSearch</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">CloudSearch</span>
extends java.lang.Object</pre>
<div class="block">企业地图数据进行搜索的“入口”类。https://me.amap.com/#/
 <p>
 该类中定义了3个内部类，Query、SearchBound 和 Sortingrules，通过使用这三个内部类设定搜索参数。
 </p></div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>嵌套类概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="嵌套类概要表, 列表嵌套类和解释">
<caption><span>嵌套类</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">类和说明</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static interface&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/cloud/CloudSearch.OnCloudSearchListener.html" title="com.amap.api.services.cloud中的接口">CloudSearch.OnCloudSearchListener</a></span></code>
<div class="block">本类为企业地图数据搜索结果的异步处理回调接口。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/cloud/CloudSearch.Query.html" title="com.amap.api.services.cloud中的类">CloudSearch.Query</a></span></code>
<div class="block">此类定义了搜索的关键字，区域范围。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/cloud/CloudSearch.SearchBound.html" title="com.amap.api.services.cloud中的类">CloudSearch.SearchBound</a></span></code>
<div class="block">此类定义了查询范围（圆形、矩形或者多边形），查询返回的企业地图数据的位置在此范围内。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/cloud/CloudSearch.Sortingrules.html" title="com.amap.api.services.cloud中的类">CloudSearch.Sortingrules</a></span></code>
<div class="block">此类定义了查询圆形和查询多边形返回数据的排序规则。</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/cloud/CloudSearch.html#CloudSearch-Context-">CloudSearch</a></span>(Context&nbsp;act)</code>
<div class="block">根据给定的参数构造一个 CloudSearch 的新对象。</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>所有方法</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">实例方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">具体方法</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/cloud/CloudSearch.html#searchCloudAsyn-com.amap.api.services.cloud.CloudSearch.Query-">searchCloudAsyn</a></span>(<a href="../../../../../com/amap/api/services/cloud/CloudSearch.Query.html" title="com.amap.api.services.cloud中的类">CloudSearch.Query</a>&nbsp;query)</code>
<div class="block">企业地图数据搜索的异步接口。</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/cloud/CloudSearch.html#searchCloudDetailAsyn-java.lang.String-java.lang.String-">searchCloudDetailAsyn</a></span>(java.lang.String&nbsp;tableid,
                     java.lang.String&nbsp;cloudID)</code>
<div class="block">根据CloudItem 的 ID 查找Cloud数据的详细信息。</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/cloud/CloudSearch.html#setOnCloudSearchListener-com.amap.api.services.cloud.CloudSearch.OnCloudSearchListener-">setOnCloudSearchListener</a></span>(<a href="../../../../../com/amap/api/services/cloud/CloudSearch.OnCloudSearchListener.html" title="com.amap.api.services.cloud中的接口">CloudSearch.OnCloudSearchListener</a>&nbsp;listener)</code>
<div class="block">设置查询监听接口。</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="CloudSearch-Context-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>CloudSearch</h4>
<pre>public&nbsp;CloudSearch(Context&nbsp;act)
            throws <a href="../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></pre>
<div class="block">根据给定的参数构造一个 CloudSearch 的新对象。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>act</code> - 对应的 Context。</dd>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code><a href="../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></code></dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="setOnCloudSearchListener-com.amap.api.services.cloud.CloudSearch.OnCloudSearchListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setOnCloudSearchListener</h4>
<pre>public&nbsp;void&nbsp;setOnCloudSearchListener(<a href="../../../../../com/amap/api/services/cloud/CloudSearch.OnCloudSearchListener.html" title="com.amap.api.services.cloud中的接口">CloudSearch.OnCloudSearchListener</a>&nbsp;listener)</pre>
<div class="block">设置查询监听接口。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>listener</code> - 查询监听接口。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
<a name="searchCloudAsyn-com.amap.api.services.cloud.CloudSearch.Query-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>searchCloudAsyn</h4>
<pre>public&nbsp;void&nbsp;searchCloudAsyn(<a href="../../../../../com/amap/api/services/cloud/CloudSearch.Query.html" title="com.amap.api.services.cloud中的类">CloudSearch.Query</a>&nbsp;query)</pre>
<div class="block">企业地图数据搜索的异步接口。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>query</code> - 查询条件。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
<a name="searchCloudDetailAsyn-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>searchCloudDetailAsyn</h4>
<pre>public&nbsp;void&nbsp;searchCloudDetailAsyn(java.lang.String&nbsp;tableid,
                                  java.lang.String&nbsp;cloudID)</pre>
<div class="block">根据CloudItem 的 ID 查找Cloud数据的详细信息。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>tableid</code> - 必填，为数据表的唯一标示。即 layer id</dd>
<dd><code>cloudID</code> - 必填，为Cloud数据id的唯一标示。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/CloudSearch.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/cloud/CloudResult.html" title="com.amap.api.services.cloud中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/amap/api/services/cloud/CloudSearch.OnCloudSearchListener.html" title="com.amap.api.services.cloud中的接口"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/cloud/CloudSearch.html" target="_top">框架</a></li>
<li><a href="CloudSearch.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li><a href="#nested.class.summary">嵌套</a>&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
