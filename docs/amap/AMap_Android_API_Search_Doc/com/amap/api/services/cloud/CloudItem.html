<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>CloudItem</title>
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="CloudItem";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":42,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10};
var tabs = {65535:["t0","所有方法"],2:["t2","实例方法"],8:["t4","具体方法"],32:["t6","已过时的方法"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/CloudItem.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/cloud/CloudImage.html" title="com.amap.api.services.cloud中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/amap/api/services/cloud/CloudItemDetail.html" title="com.amap.api.services.cloud中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/cloud/CloudItem.html" target="_top">框架</a></li>
<li><a href="CloudItem.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li><a href="#field.summary">字段</a>&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li><a href="#field.detail">字段</a>&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.amap.api.services.cloud</div>
<h2 title="类 CloudItem" class="title">类 CloudItem</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.amap.api.services.cloud.CloudItem</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>直接已知子类:</dt>
<dd><a href="../../../../../com/amap/api/services/cloud/CloudItemDetail.html" title="com.amap.api.services.cloud中的类">CloudItemDetail</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">CloudItem</span>
extends java.lang.Object</pre>
<div class="block">此类定义了一个企业地图数据对象。一个 CloudItem 由如下成员组成：
 <p>
 CloudItem 的唯一标识，即id。这个标识在不同的数据版本中延续。
 </p>
 <p>
 CloudItem 的经纬度。
 </p>
 <p>
 CloudItem 的名称。
 </p>
 <p>
 CloudItem 的地址。
 </p></div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>字段概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="字段概要表, 列表字段和解释">
<caption><span>字段</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">字段和说明</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/cloud/CloudItem.html#mPoint">mPoint</a></span></code>
<div class="block">CloudItem的位置</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/cloud/CloudItem.html#mSnippet">mSnippet</a></span></code>
<div class="block">CloudItem的地址</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/cloud/CloudItem.html#mTitle">mTitle</a></span></code>
<div class="block">CloudItem的名称</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>所有方法</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">实例方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">具体方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t6" class="tableTab"><span><a href="javascript:show(32);">已过时的方法</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../../com/amap/api/services/cloud/CloudImage.html" title="com.amap.api.services.cloud中的类">CloudImage</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/cloud/CloudItem.html#getCloudImage--">getCloudImage</a></span>()</code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;
<div class="block"><span class="deprecationComment">自7.4.0起不再支持企业地图图片</span></div>
</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/cloud/CloudItem.html#getCreatetime--">getCreatetime</a></span>()</code>
<div class="block">返回创建企业地图数据的时间。</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>java.util.HashMap&lt;java.lang.String,java.lang.String&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/cloud/CloudItem.html#getCustomfield--">getCustomfield</a></span>()</code>
<div class="block">返回用户自定义字段。</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/cloud/CloudItem.html#getDistance--">getDistance</a></span>()</code>
<div class="block">返回该企业地图数据距离中心点的距离，单位为米。</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/cloud/CloudItem.html#getID--">getID</a></span>()</code>
<div class="block">返回 CloudItem 的 id，即其唯一标识。</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/cloud/CloudItem.html#getLatLonPoint--">getLatLonPoint</a></span>()</code>
<div class="block">返回 CloudItem 的经纬度坐标 。</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/cloud/CloudItem.html#getSnippet--">getSnippet</a></span>()</code>
<div class="block">返回 CloudItem 的地址。</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/cloud/CloudItem.html#getTitle--">getTitle</a></span>()</code>
<div class="block">返回 CloudItem 的名称。</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/cloud/CloudItem.html#getUpdatetime--">getUpdatetime</a></span>()</code>
<div class="block">返回更新企业地图数据的时间。</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>字段详细资料</h3>
<a name="mPoint">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>mPoint</h4>
<pre>protected final&nbsp;<a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a> mPoint</pre>
<div class="block">CloudItem的位置</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
<a name="mTitle">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>mTitle</h4>
<pre>protected final&nbsp;java.lang.String mTitle</pre>
<div class="block">CloudItem的名称</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
<a name="mSnippet">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>mSnippet</h4>
<pre>protected final&nbsp;java.lang.String mSnippet</pre>
<div class="block">CloudItem的地址</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="getID--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getID</h4>
<pre>public&nbsp;java.lang.String&nbsp;getID()</pre>
<div class="block">返回 CloudItem 的 id，即其唯一标识。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>CloudItem 的 id。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
<a name="getDistance--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDistance</h4>
<pre>public&nbsp;int&nbsp;getDistance()</pre>
<div class="block">返回该企业地图数据距离中心点的距离，单位为米。
 <p>
 只在搜索范围为圆形时，此字段有数据。
 </p></div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>该企业地图数据距离中心点的距离，单位为米。正常返回值>0，返回值为 -1时，代表此字段无数据。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
<a name="getTitle--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTitle</h4>
<pre>public&nbsp;java.lang.String&nbsp;getTitle()</pre>
<div class="block">返回 CloudItem 的名称。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>CloudItem 的名称。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
<a name="getSnippet--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSnippet</h4>
<pre>public&nbsp;java.lang.String&nbsp;getSnippet()</pre>
<div class="block">返回 CloudItem 的地址。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>返回 CloudItem 的地址。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
<a name="getLatLonPoint--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLatLonPoint</h4>
<pre>public&nbsp;<a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&nbsp;getLatLonPoint()</pre>
<div class="block">返回 CloudItem 的经纬度坐标 。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>CloudItem 的经纬度坐标 。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
<a name="getCreatetime--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCreatetime</h4>
<pre>public&nbsp;java.lang.String&nbsp;getCreatetime()</pre>
<div class="block">返回创建企业地图数据的时间。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>创建企业地图数据的时间。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
<a name="getUpdatetime--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getUpdatetime</h4>
<pre>public&nbsp;java.lang.String&nbsp;getUpdatetime()</pre>
<div class="block">返回更新企业地图数据的时间。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>更新企业地图数据的时间。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
<a name="getCustomfield--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCustomfield</h4>
<pre>public&nbsp;java.util.HashMap&lt;java.lang.String,java.lang.String&gt;&nbsp;getCustomfield()</pre>
<div class="block">返回用户自定义字段。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>用户自定义字段。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
<a name="getCloudImage--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getCloudImage</h4>
<pre>public&nbsp;java.util.List&lt;<a href="../../../../../com/amap/api/services/cloud/CloudImage.html" title="com.amap.api.services.cloud中的类">CloudImage</a>&gt;&nbsp;getCloudImage()</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;<span class="deprecationComment">自7.4.0起不再支持企业地图图片</span></div>
<div class="block">返回企业地图数据的图片信息。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>企业地图数据的图片信息。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/CloudItem.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/cloud/CloudImage.html" title="com.amap.api.services.cloud中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/amap/api/services/cloud/CloudItemDetail.html" title="com.amap.api.services.cloud中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/cloud/CloudItem.html" target="_top">框架</a></li>
<li><a href="CloudItem.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li><a href="#field.summary">字段</a>&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li><a href="#field.detail">字段</a>&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
