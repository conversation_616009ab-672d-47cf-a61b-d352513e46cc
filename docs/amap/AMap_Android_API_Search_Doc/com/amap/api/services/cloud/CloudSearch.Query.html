<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>CloudSearch.Query</title>
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="CloudSearch.Query";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":42,"i1":10,"i2":10,"i3":10,"i4":42,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10};
var tabs = {65535:["t0","所有方法"],2:["t2","实例方法"],8:["t4","具体方法"],32:["t6","已过时的方法"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/CloudSearch.Query.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/cloud/CloudSearch.OnCloudSearchListener.html" title="com.amap.api.services.cloud中的接口"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/amap/api/services/cloud/CloudSearch.SearchBound.html" title="com.amap.api.services.cloud中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/cloud/CloudSearch.Query.html" target="_top">框架</a></li>
<li><a href="CloudSearch.Query.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.amap.api.services.cloud</div>
<h2 title="类 CloudSearch.Query" class="title">类 CloudSearch.Query</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.amap.api.services.cloud.CloudSearch.Query</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>所有已实现的接口:</dt>
<dd>java.lang.Cloneable</dd>
</dl>
<dl>
<dt>封闭类:</dt>
<dd><a href="../../../../../com/amap/api/services/cloud/CloudSearch.html" title="com.amap.api.services.cloud中的类">CloudSearch</a></dd>
</dl>
<hr>
<br>
<pre>public static class <span class="typeNameLabel">CloudSearch.Query</span>
extends java.lang.Object
implements java.lang.Cloneable</pre>
<div class="block">此类定义了搜索的关键字，区域范围。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/cloud/CloudSearch.Query.html#Query-java.lang.String-java.lang.String-com.amap.api.services.cloud.CloudSearch.SearchBound-">Query</a></span>(java.lang.String&nbsp;tableid,
     java.lang.String&nbsp;query,
     <a href="../../../../../com/amap/api/services/cloud/CloudSearch.SearchBound.html" title="com.amap.api.services.cloud中的类">CloudSearch.SearchBound</a>&nbsp;bound)</code>
<div class="block">根据给定的参数构造一个 CloudSearch.Query 的新对象。</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>所有方法</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">实例方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">具体方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t6" class="tableTab"><span><a href="javascript:show(32);">已过时的方法</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/cloud/CloudSearch.Query.html#addFilterNum-java.lang.String-java.lang.String-java.lang.String-">addFilterNum</a></span>(java.lang.String&nbsp;key,
            java.lang.String&nbsp;value1,
            java.lang.String&nbsp;value2)</code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;
<div class="block"><span class="deprecationComment">使用 <a href="../../../../../com/amap/api/services/cloud/CloudSearch.Query.html#addFilterString-java.lang.String-java.lang.String-"><code>CloudSearch.Query.addFilterString(String, String)</code></a> 替换</span></div>
</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/cloud/CloudSearch.Query.html#addFilterString-java.lang.String-java.lang.String-">addFilterString</a></span>(java.lang.String&nbsp;key,
               java.lang.String&nbsp;value)</code>
<div class="block">1：支持建立索引的字段根据多个条件筛选，多个条件用&&符号连接。</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/cloud/CloudSearch.Query.html#equals-java.lang.Object-">equals</a></span>(java.lang.Object&nbsp;query)</code>
<div class="block">比较两个查询条件是否相同(包括查询第几页)</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/services/cloud/CloudSearch.SearchBound.html" title="com.amap.api.services.cloud中的类">CloudSearch.SearchBound</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/cloud/CloudSearch.Query.html#getBound--">getBound</a></span>()</code>
<div class="block">返回查询的范围（本地、圆形、矩形或者多边形）。</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/cloud/CloudSearch.Query.html#getFilterNumString--">getFilterNumString</a></span>()</code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;
<div class="block"><span class="deprecationComment">使用 <a href="../../../../../com/amap/api/services/cloud/CloudSearch.Query.html#addFilterString-java.lang.String-java.lang.String-"><code>CloudSearch.Query.addFilterString(String, String)</code></a>替换</span></div>
</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/cloud/CloudSearch.Query.html#getFilterString--">getFilterString</a></span>()</code>
<div class="block">将筛选字段转换为字符串返回</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/cloud/CloudSearch.Query.html#getPageNum--">getPageNum</a></span>()</code>
<div class="block">返回设置查询的是第几页，从0开始。</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/cloud/CloudSearch.Query.html#getPageSize--">getPageSize</a></span>()</code>
<div class="block">返回查询每页的结果数目。</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/cloud/CloudSearch.Query.html#getQueryString--">getQueryString</a></span>()</code>
<div class="block">返回搜索的关键词。</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/services/cloud/CloudSearch.Sortingrules.html" title="com.amap.api.services.cloud中的类">CloudSearch.Sortingrules</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/cloud/CloudSearch.Query.html#getSortingrules--">getSortingrules</a></span>()</code>
<div class="block">返回排序规则。</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/cloud/CloudSearch.Query.html#getTableID--">getTableID</a></span>()</code>
<div class="block">返回搜索的表tableid。</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/cloud/CloudSearch.Query.html#queryEquals-com.amap.api.services.cloud.CloudSearch.Query-">queryEquals</a></span>(<a href="../../../../../com/amap/api/services/cloud/CloudSearch.Query.html" title="com.amap.api.services.cloud中的类">CloudSearch.Query</a>&nbsp;query)</code>
<div class="block">比较两个查询条件是否相同(不包括查询第几页)</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/cloud/CloudSearch.Query.html#setBound-com.amap.api.services.cloud.CloudSearch.SearchBound-">setBound</a></span>(<a href="../../../../../com/amap/api/services/cloud/CloudSearch.SearchBound.html" title="com.amap.api.services.cloud中的类">CloudSearch.SearchBound</a>&nbsp;bnd)</code>
<div class="block">设置查询的范围（本地、圆形、矩形或者多边形）。</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/cloud/CloudSearch.Query.html#setPageNum-int-">setPageNum</a></span>(int&nbsp;pageNum)</code>
<div class="block">设置查询第几页的结果数目。</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/cloud/CloudSearch.Query.html#setPageSize-int-">setPageSize</a></span>(int&nbsp;size)</code>&nbsp;</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/cloud/CloudSearch.Query.html#setSortingrules-com.amap.api.services.cloud.CloudSearch.Sortingrules-">setSortingrules</a></span>(<a href="../../../../../com/amap/api/services/cloud/CloudSearch.Sortingrules.html" title="com.amap.api.services.cloud中的类">CloudSearch.Sortingrules</a>&nbsp;sortingrules)</code>
<div class="block">设置排序规则。</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/cloud/CloudSearch.Query.html#setTableID-java.lang.String-">setTableID</a></span>(java.lang.String&nbsp;tableID)</code>
<div class="block">设置搜索的表tableid。</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="Query-java.lang.String-java.lang.String-com.amap.api.services.cloud.CloudSearch.SearchBound-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>Query</h4>
<pre>public&nbsp;Query(java.lang.String&nbsp;tableid,
             java.lang.String&nbsp;query,
             <a href="../../../../../com/amap/api/services/cloud/CloudSearch.SearchBound.html" title="com.amap.api.services.cloud中的类">CloudSearch.SearchBound</a>&nbsp;bound)
      throws <a href="../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></pre>
<div class="block">根据给定的参数构造一个 CloudSearch.Query 的新对象。
 <p>
 参数中：tableid和bound必填，query可选。
 </p>
 <p>
 如果tableid和bound为空，则抛出IllegalArgumentException 异常。
 </p>
 <p>
 如果查询范围不是圆形，则没有距离排序；如果查询范围是圆形，有关键字时默认为权重降序排列，无关键字时默认为距离升序排列。
 </p></div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>tableid</code> - 必填，数据表的唯一标示。</dd>
<dd><code>query</code> - <p>
            1. 支持关键字检索，即对建立过索引的字段进行检索；如keywords=工商银行，检索返回已建立文本索引列值中包含“工商”或者“银行”或者“工商银行”关键字的POI结果集。<br>
            2.支持关键字多值检索；（只支持建立过文本索引的字段查询）如keywords=招商银行&&华夏银行&&工商银行，检索返回已建立索引列值中包含“招商银行”且“华夏银行”且“工商银行”的POI结果集，不会返回检索词切分后，如仅包含“招商”或者“银行”的POI集；。</p></dd>
<dd><code>bound</code> - 查询的城市或范围（支持圆形、多边形）。</dd>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code><a href="../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></code> - 如果为空或者bounds为空时会抛出 <a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_CLIENT_INVALID_PARAMETER"><code>AMapException.AMAP_CLIENT_INVALID_PARAMETER</code></a></dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="getQueryString--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getQueryString</h4>
<pre>public&nbsp;java.lang.String&nbsp;getQueryString()</pre>
<div class="block">返回搜索的关键词。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>搜索的关键词。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
<a name="setTableID-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTableID</h4>
<pre>public&nbsp;void&nbsp;setTableID(java.lang.String&nbsp;tableID)</pre>
<div class="block">设置搜索的表tableid。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>tableID</code> - 数据表的唯一标示。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
<a name="getTableID--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTableID</h4>
<pre>public&nbsp;java.lang.String&nbsp;getTableID()</pre>
<div class="block">返回搜索的表tableid。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>tableid。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
<a name="getPageNum--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPageNum</h4>
<pre>public&nbsp;int&nbsp;getPageNum()</pre>
<div class="block">返回设置查询的是第几页，从0开始。<font color="red">自 5.2.1后修改成从1开始，和iOS保持一致。</font></div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>查询的是第几页。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
<a name="setPageNum-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPageNum</h4>
<pre>public&nbsp;void&nbsp;setPageNum(int&nbsp;pageNum)</pre>
<div class="block">设置查询第几页的结果数目。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>pageNum</code> - 查询第几页的结果，从0开始。<font color="red">自 5.2.1后修改成从1开始，和iOS保持一致。</font></dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
<a name="setPageSize-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPageSize</h4>
<pre>public&nbsp;void&nbsp;setPageSize(int&nbsp;size)</pre>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>size</code> - 每页结果数</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
<a name="getPageSize--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPageSize</h4>
<pre>public&nbsp;int&nbsp;getPageSize()</pre>
<div class="block">返回查询每页的结果数目。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>查询每页的结果数目。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
<a name="setBound-com.amap.api.services.cloud.CloudSearch.SearchBound-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBound</h4>
<pre>public&nbsp;void&nbsp;setBound(<a href="../../../../../com/amap/api/services/cloud/CloudSearch.SearchBound.html" title="com.amap.api.services.cloud中的类">CloudSearch.SearchBound</a>&nbsp;bnd)</pre>
<div class="block">设置查询的范围（本地、圆形、矩形或者多边形）。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>bnd</code> - 新的查询范围（本地、圆形、矩形或者多边形）。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
<a name="getBound--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBound</h4>
<pre>public&nbsp;<a href="../../../../../com/amap/api/services/cloud/CloudSearch.SearchBound.html" title="com.amap.api.services.cloud中的类">CloudSearch.SearchBound</a>&nbsp;getBound()</pre>
<div class="block">返回查询的范围（本地、圆形、矩形或者多边形）。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>查询的范围（本地、圆形、矩形或者多边形）。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
<a name="addFilterString-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addFilterString</h4>
<pre>public&nbsp;void&nbsp;addFilterString(java.lang.String&nbsp;key,
                            java.lang.String&nbsp;value)</pre>
<div class="block">1：支持建立索引的字段根据多个条件筛选，多个条件用&&符号连接。 <br>
 2：判断符合支持 <br>
 >= 大于等于 <br>
 <= 小于等于 <br>
 >大于 <br>
 <小于 <br>
 = 精确匹配(text索引不可用) <br>

 示例规则：key1=value1&&key2=value2&&lastloctime>=1469817532 <br>
 示例："name=王师傅|张师傅&&lastloctime>=1469817532 <br></div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>key</code> - 文字筛选条件精确匹配的键。</dd>
<dd><code>value</code> - 键对应的文字。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
<a name="getFilterString--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFilterString</h4>
<pre>public&nbsp;java.lang.String&nbsp;getFilterString()</pre>
<div class="block">将筛选字段转换为字符串返回</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>筛选字段名称和对应关键字的字符串</dd>
</dl>
</li>
</ul>
<a name="addFilterNum-java.lang.String-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addFilterNum</h4>
<pre>public&nbsp;void&nbsp;addFilterNum(java.lang.String&nbsp;key,
                         java.lang.String&nbsp;value1,
                         java.lang.String&nbsp;value2)</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;<span class="deprecationComment">使用 <a href="../../../../../com/amap/api/services/cloud/CloudSearch.Query.html#addFilterString-java.lang.String-java.lang.String-"><code>CloudSearch.Query.addFilterString(String, String)</code></a> 替换</span></div>
<div class="block">支持对数值字段的连续区间筛选，例如对酒店字段筛选3星-5星的酒店。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>key</code> - 数值筛选条件的键。</dd>
<dd><code>value1</code> - 键对应的数值连续区间的开始数值。</dd>
<dd><code>value2</code> - 键对应的数值连续区间的结束数值。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
<a name="getFilterNumString--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFilterNumString</h4>
<pre>public&nbsp;java.lang.String&nbsp;getFilterNumString()</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;<span class="deprecationComment">使用 <a href="../../../../../com/amap/api/services/cloud/CloudSearch.Query.html#addFilterString-java.lang.String-java.lang.String-"><code>CloudSearch.Query.addFilterString(String, String)</code></a>替换</span></div>
<div class="block">返回排序规则。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>排序规则。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
<a name="setSortingrules-com.amap.api.services.cloud.CloudSearch.Sortingrules-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSortingrules</h4>
<pre>public&nbsp;void&nbsp;setSortingrules(<a href="../../../../../com/amap/api/services/cloud/CloudSearch.Sortingrules.html" title="com.amap.api.services.cloud中的类">CloudSearch.Sortingrules</a>&nbsp;sortingrules)</pre>
<div class="block">设置排序规则。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>sortingrules</code> - 排序规则。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
<a name="getSortingrules--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSortingrules</h4>
<pre>public&nbsp;<a href="../../../../../com/amap/api/services/cloud/CloudSearch.Sortingrules.html" title="com.amap.api.services.cloud中的类">CloudSearch.Sortingrules</a>&nbsp;getSortingrules()</pre>
<div class="block">返回排序规则。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>排序规则。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
<a name="queryEquals-com.amap.api.services.cloud.CloudSearch.Query-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>queryEquals</h4>
<pre>public&nbsp;boolean&nbsp;queryEquals(<a href="../../../../../com/amap/api/services/cloud/CloudSearch.Query.html" title="com.amap.api.services.cloud中的类">CloudSearch.Query</a>&nbsp;query)</pre>
<div class="block">比较两个查询条件是否相同(不包括查询第几页)</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>query</code> - 查询条件</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>查询条件是否相同</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
<a name="equals-java.lang.Object-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>equals</h4>
<pre>public&nbsp;boolean&nbsp;equals(java.lang.Object&nbsp;query)</pre>
<div class="block">比较两个查询条件是否相同(包括查询第几页)</div>
<dl>
<dt><span class="overrideSpecifyLabel">覆盖:</span></dt>
<dd><code>equals</code>&nbsp;在类中&nbsp;<code>java.lang.Object</code></dd>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>query</code> - 查询条件</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>查询条件是否相同</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/CloudSearch.Query.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/cloud/CloudSearch.OnCloudSearchListener.html" title="com.amap.api.services.cloud中的接口"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/amap/api/services/cloud/CloudSearch.SearchBound.html" title="com.amap.api.services.cloud中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/cloud/CloudSearch.Query.html" target="_top">框架</a></li>
<li><a href="CloudSearch.Query.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
