<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>程序包 com.amap.api.services.cloud的使用</title>
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="\u7A0B\u5E8F\u5305 com.amap.api.services.cloud\u7684\u4F7F\u7528";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li>类</li>
<li class="navBarCell1Rev">使用</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个</li>
<li>下一个</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/cloud/package-use.html" target="_top">框架</a></li>
<li><a href="package-use.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 title="程序包的使用 com.amap.api.services.cloud" class="title">程序包的使用<br>com.amap.api.services.cloud</h1>
</div>
<div class="contentContainer">
<ul class="blockList">
<li class="blockList">
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表程序包和解释">
<caption><span>使用<a href="../../../../../com/amap/api/services/cloud/package-summary.html">com.amap.api.services.cloud</a>的程序包</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">程序包</th>
<th class="colLast" scope="col">说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="#com.amap.api.services.cloud">com.amap.api.services.cloud</a></td>
<td class="colLast">
<div class="block">
云检索包，包含了自有数据搜索查询的功能。</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.amap.api.services.cloud">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表类和解释">
<caption><span><a href="../../../../../com/amap/api/services/cloud/package-summary.html">com.amap.api.services.cloud</a>使用的<a href="../../../../../com/amap/api/services/cloud/package-summary.html">com.amap.api.services.cloud</a>中的类</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">类和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/cloud/class-use/CloudImage.html#com.amap.api.services.cloud">CloudImage</a>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;
<div class="block"><span class="deprecationComment">自7.4.0起不再支持云图图片</span></div>
</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/cloud/class-use/CloudItem.html#com.amap.api.services.cloud">CloudItem</a>
<div class="block">此类定义了一个企业地图数据对象。</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/cloud/class-use/CloudItemDetail.html#com.amap.api.services.cloud">CloudItemDetail</a>
<div class="block">该类定义了一个企业地图数据的详细信息。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/cloud/class-use/CloudResult.html#com.amap.api.services.cloud">CloudResult</a>
<div class="block">企业地图搜索结果类。</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/cloud/class-use/CloudSearch.OnCloudSearchListener.html#com.amap.api.services.cloud">CloudSearch.OnCloudSearchListener</a>
<div class="block">本类为企业地图数据搜索结果的异步处理回调接口。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/cloud/class-use/CloudSearch.Query.html#com.amap.api.services.cloud">CloudSearch.Query</a>
<div class="block">此类定义了搜索的关键字，区域范围。</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/cloud/class-use/CloudSearch.SearchBound.html#com.amap.api.services.cloud">CloudSearch.SearchBound</a>
<div class="block">此类定义了查询范围（圆形、矩形或者多边形），查询返回的企业地图数据的位置在此范围内。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/cloud/class-use/CloudSearch.Sortingrules.html#com.amap.api.services.cloud">CloudSearch.Sortingrules</a>
<div class="block">此类定义了查询圆形和查询多边形返回数据的排序规则。</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li>类</li>
<li class="navBarCell1Rev">使用</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个</li>
<li>下一个</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/cloud/package-use.html" target="_top">框架</a></li>
<li><a href="package-use.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
