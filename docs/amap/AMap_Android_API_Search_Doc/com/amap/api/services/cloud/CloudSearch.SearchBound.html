<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>CloudSearch.SearchBound</title>
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="CloudSearch.SearchBound";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10};
var tabs = {65535:["t0","所有方法"],2:["t2","实例方法"],8:["t4","具体方法"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/CloudSearch.SearchBound.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/cloud/CloudSearch.Query.html" title="com.amap.api.services.cloud中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/amap/api/services/cloud/CloudSearch.Sortingrules.html" title="com.amap.api.services.cloud中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/cloud/CloudSearch.SearchBound.html" target="_top">框架</a></li>
<li><a href="CloudSearch.SearchBound.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li><a href="#field.summary">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li><a href="#field.detail">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.amap.api.services.cloud</div>
<h2 title="类 CloudSearch.SearchBound" class="title">类 CloudSearch.SearchBound</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.amap.api.services.cloud.CloudSearch.SearchBound</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>所有已实现的接口:</dt>
<dd>java.lang.Cloneable</dd>
</dl>
<dl>
<dt>封闭类:</dt>
<dd><a href="../../../../../com/amap/api/services/cloud/CloudSearch.html" title="com.amap.api.services.cloud中的类">CloudSearch</a></dd>
</dl>
<hr>
<br>
<pre>public static class <span class="typeNameLabel">CloudSearch.SearchBound</span>
extends java.lang.Object
implements java.lang.Cloneable</pre>
<div class="block">此类定义了查询范围（圆形、矩形或者多边形），查询返回的企业地图数据的位置在此范围内。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>字段概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="字段概要表, 列表字段和解释">
<caption><span>字段</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">字段和说明</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/cloud/CloudSearch.SearchBound.html#BOUND_SHAPE">BOUND_SHAPE</a></span></code>
<div class="block">圆形区域</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/cloud/CloudSearch.SearchBound.html#LOCAL_SHAPE">LOCAL_SHAPE</a></span></code>
<div class="block">本地搜索</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/cloud/CloudSearch.SearchBound.html#POLYGON_SHAPE">POLYGON_SHAPE</a></span></code>
<div class="block">多边形区域</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/cloud/CloudSearch.SearchBound.html#RECTANGLE_SHAPE">RECTANGLE_SHAPE</a></span></code>
<div class="block">矩形区域</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/cloud/CloudSearch.SearchBound.html#SearchBound-com.amap.api.services.core.LatLonPoint-int-">SearchBound</a></span>(<a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&nbsp;center,
           int&nbsp;radiusInMeters)</code>
<div class="block">根据给定的参数来构造一个圆形查询范围对象。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/cloud/CloudSearch.SearchBound.html#SearchBound-com.amap.api.services.core.LatLonPoint-com.amap.api.services.core.LatLonPoint-">SearchBound</a></span>(<a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&nbsp;lowerLeft,
           <a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&nbsp;upperRight)</code>
<div class="block">根据给定的参数来构造一个矩形查询范围对象。</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/cloud/CloudSearch.SearchBound.html#SearchBound-java.util.List-">SearchBound</a></span>(java.util.List&lt;<a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&gt;&nbsp;polygonList)</code>
<div class="block">根据给定的参数来构造一个多边形查询范围对象。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/cloud/CloudSearch.SearchBound.html#SearchBound-java.lang.String-">SearchBound</a></span>(java.lang.String&nbsp;city)</code>
<div class="block">根据城市名称构造查询范围对象，适用于本地搜索。</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>所有方法</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">实例方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">具体方法</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/cloud/CloudSearch.SearchBound.html#equals-java.lang.Object-">equals</a></span>(java.lang.Object&nbsp;bound)</code>
<div class="block">比较两个企业地图查询范围是否相同</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/cloud/CloudSearch.SearchBound.html#getCenter--">getCenter</a></span>()</code>
<div class="block">返回查询圆形的中心点坐标。</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/cloud/CloudSearch.SearchBound.html#getCity--">getCity</a></span>()</code>
<div class="block">返回查询的城市名称，适用于本地搜索。</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/cloud/CloudSearch.SearchBound.html#getLowerLeft--">getLowerLeft</a></span>()</code>
<div class="block">返回查询矩形的左下角坐标。</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/cloud/CloudSearch.SearchBound.html#getPolyGonList--">getPolyGonList</a></span>()</code>
<div class="block">返回查询多边形的坐标点。</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/cloud/CloudSearch.SearchBound.html#getRange--">getRange</a></span>()</code>
<div class="block">返回查询圆形的范围（半径）。</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/cloud/CloudSearch.SearchBound.html#getShape--">getShape</a></span>()</code>
<div class="block">返回查询的形状。</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/cloud/CloudSearch.SearchBound.html#getUpperRight--">getUpperRight</a></span>()</code>
<div class="block">返回查询矩形的右上角坐标。</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>字段详细资料</h3>
<a name="BOUND_SHAPE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BOUND_SHAPE</h4>
<pre>public static final&nbsp;java.lang.String BOUND_SHAPE</pre>
<div class="block">圆形区域</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.cloud.CloudSearch.SearchBound.BOUND_SHAPE">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="POLYGON_SHAPE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>POLYGON_SHAPE</h4>
<pre>public static final&nbsp;java.lang.String POLYGON_SHAPE</pre>
<div class="block">多边形区域</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.cloud.CloudSearch.SearchBound.POLYGON_SHAPE">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="RECTANGLE_SHAPE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RECTANGLE_SHAPE</h4>
<pre>public static final&nbsp;java.lang.String RECTANGLE_SHAPE</pre>
<div class="block">矩形区域</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.cloud.CloudSearch.SearchBound.RECTANGLE_SHAPE">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="LOCAL_SHAPE">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>LOCAL_SHAPE</h4>
<pre>public static final&nbsp;java.lang.String LOCAL_SHAPE</pre>
<div class="block">本地搜索</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.cloud.CloudSearch.SearchBound.LOCAL_SHAPE">常量字段值</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="SearchBound-com.amap.api.services.core.LatLonPoint-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SearchBound</h4>
<pre>public&nbsp;SearchBound(<a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&nbsp;center,
                   int&nbsp;radiusInMeters)</pre>
<div class="block">根据给定的参数来构造一个圆形查询范围对象。<br>
 规则：取值范围[1,50000]，单位：米。缺省值3000</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>center</code> - 该范围的中心点。经纬度小数点后不建议超过6位</dd>
<dd><code>radiusInMeters</code> - 半径，单位：米。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
<a name="SearchBound-com.amap.api.services.core.LatLonPoint-com.amap.api.services.core.LatLonPoint-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SearchBound</h4>
<pre>public&nbsp;SearchBound(<a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&nbsp;lowerLeft,
                   <a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&nbsp;upperRight)</pre>
<div class="block">根据给定的参数来构造一个矩形查询范围对象。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>lowerLeft</code> - 矩形的左下角。</dd>
<dd><code>upperRight</code> - 矩形的右上角。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>#SearchBound(List)</code></dd>
</dl>
</li>
</ul>
<a name="SearchBound-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SearchBound</h4>
<pre>public&nbsp;SearchBound(java.util.List&lt;<a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&gt;&nbsp;polygonList)</pre>
<div class="block">根据给定的参数来构造一个多边形查询范围对象。 <br>
 规则：  <br>
 面积最大1万平方公里 <br>
 wkt格式并兼容“逗号分隔的一对经纬度代表一个坐标，用分号分割多个坐标” <br>
 如果只传两个坐标则认为这两坐标为矩形的左下和右上点； <br>
 多边形数据的起点和终点必须相同，保证图形闭合。 <br>
 经纬度小数点后不建议超过6位 <br>
 示例: <br>
 - 矩形： <br>
 polygon=116.374634,39.377362;116.673646,39.576462 <br>
 - 多边形： <br>
 polygon=115.7409668,40.12009038;115.59127808,39.98869502;115.8631897, 39.91816285;115.92224121,40.06546068;115.7409668,40.12009038 <br></div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>polygonList</code> - 首尾相接的几何点，可以组成多边形。注意：起始坐标点和最后坐标点需保持相同。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
<a name="SearchBound-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>SearchBound</h4>
<pre>public&nbsp;SearchBound(java.lang.String&nbsp;city)</pre>
<div class="block">根据城市名称构造查询范围对象，适用于本地搜索。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>city</code> - 城市名称，取值为省/市/区县。输入“全国”，为本表全部搜索。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="getLowerLeft--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLowerLeft</h4>
<pre>public&nbsp;<a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&nbsp;getLowerLeft()</pre>
<div class="block">返回查询矩形的左下角坐标。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>矩形的左下角坐标。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
<a name="getUpperRight--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getUpperRight</h4>
<pre>public&nbsp;<a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&nbsp;getUpperRight()</pre>
<div class="block">返回查询矩形的右上角坐标。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>矩形的右上角坐标。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
<a name="getCenter--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCenter</h4>
<pre>public&nbsp;<a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&nbsp;getCenter()</pre>
<div class="block">返回查询圆形的中心点坐标。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>圆形的中心点坐标。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
<a name="getRange--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRange</h4>
<pre>public&nbsp;int&nbsp;getRange()</pre>
<div class="block">返回查询圆形的范围（半径）。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>圆形的范围（半径）。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
<a name="getShape--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getShape</h4>
<pre>public&nbsp;java.lang.String&nbsp;getShape()</pre>
<div class="block">返回查询的形状。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>查询的形状。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
<a name="getCity--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCity</h4>
<pre>public&nbsp;java.lang.String&nbsp;getCity()</pre>
<div class="block">返回查询的城市名称，适用于本地搜索。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>查询的城市名称。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
<a name="getPolyGonList--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPolyGonList</h4>
<pre>public&nbsp;java.util.List&lt;<a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&gt;&nbsp;getPolyGonList()</pre>
<div class="block">返回查询多边形的坐标点。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>多边形的坐标点。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
<a name="equals-java.lang.Object-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>equals</h4>
<pre>public&nbsp;boolean&nbsp;equals(java.lang.Object&nbsp;bound)</pre>
<div class="block">比较两个企业地图查询范围是否相同</div>
<dl>
<dt><span class="overrideSpecifyLabel">覆盖:</span></dt>
<dd><code>equals</code>&nbsp;在类中&nbsp;<code>java.lang.Object</code></dd>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>bound</code> - 查询条件</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>查询范围是否相同</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/CloudSearch.SearchBound.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/cloud/CloudSearch.Query.html" title="com.amap.api.services.cloud中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/amap/api/services/cloud/CloudSearch.Sortingrules.html" title="com.amap.api.services.cloud中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/cloud/CloudSearch.SearchBound.html" target="_top">框架</a></li>
<li><a href="CloudSearch.SearchBound.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li><a href="#field.summary">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li><a href="#field.detail">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
