<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>程序包 com.amap.api.services.core的使用</title>
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="\u7A0B\u5E8F\u5305 com.amap.api.services.core\u7684\u4F7F\u7528";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li>类</li>
<li class="navBarCell1Rev">使用</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个</li>
<li>下一个</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/core/package-use.html" target="_top">框架</a></li>
<li><a href="package-use.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 title="程序包的使用 com.amap.api.services.core" class="title">程序包的使用<br>com.amap.api.services.core</h1>
</div>
<div class="contentContainer">
<ul class="blockList">
<li class="blockList">
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表程序包和解释">
<caption><span>使用<a href="../../../../../com/amap/api/services/core/package-summary.html">com.amap.api.services.core</a>的程序包</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">程序包</th>
<th class="colLast" scope="col">说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="#com.amap.api.services.busline">com.amap.api.services.busline</a></td>
<td class="colLast">
<div class="block">
公交线路和公交站点查询包。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.amap.api.services.cloud">com.amap.api.services.cloud</a></td>
<td class="colLast">
<div class="block">
云检索包，包含了自有数据搜索查询的功能。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#com.amap.api.services.core">com.amap.api.services.core</a></td>
<td class="colLast">
<div class="block">
工具类包。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.amap.api.services.district">com.amap.api.services.district</a></td>
<td class="colLast">
<div class="block">
行政区划查询包，查询某个行政级别的详细信息。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#com.amap.api.services.geocoder">com.amap.api.services.geocoder</a></td>
<td class="colLast">
<div class="block">
地理编码包，用以实现通过地理编码进行查询的功能。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.amap.api.services.help">com.amap.api.services.help</a></td>
<td class="colLast">
<div class="block">
服务帮助包，用来实现查询过程中提供的提示帮助。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#com.amap.api.services.nearby">com.amap.api.services.nearby</a></td>
<td class="colLast">
<div class="block">
附近派单功能包，包含上传位置信息、检索位置信息等 自7.4.0起不再支持附件功能
</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.amap.api.services.poisearch">com.amap.api.services.poisearch</a></td>
<td class="colLast">
<div class="block">
POI查询包，包含了兴趣点的详细信息。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#com.amap.api.services.road">com.amap.api.services.road</a></td>
<td class="colLast">
<div class="block">
道路查询包，包含了道路查询结果的详细信息。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.amap.api.services.route">com.amap.api.services.route</a></td>
<td class="colLast">
<div class="block">
路径查询包，包含了在公交、自驾或步行路线规划中的详细信息。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#com.amap.api.services.routepoisearch">com.amap.api.services.routepoisearch</a></td>
<td class="colLast">
<div class="block">
沿途POI搜索包，搜索驾车沿途的POI信息。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.amap.api.services.share">com.amap.api.services.share</a></td>
<td class="colLast">
<div class="block">
短串分享包，包含了位置/POI/路径规划/导航的分享功能。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#com.amap.api.services.weather">com.amap.api.services.weather</a></td>
<td class="colLast">
<div class="block">
天气查询包，包含了实况天气和预报天气的详细信息。</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.amap.api.services.busline">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表类和解释">
<caption><span><a href="../../../../../com/amap/api/services/busline/package-summary.html">com.amap.api.services.busline</a>使用的<a href="../../../../../com/amap/api/services/core/package-summary.html">com.amap.api.services.core</a>中的类</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">类和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/core/class-use/AMapException.html#com.amap.api.services.busline">AMapException</a>
<div class="block">服务异常信息类。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/core/class-use/LatLonPoint.html#com.amap.api.services.busline">LatLonPoint</a>
<div class="block">几何点对象类。</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/core/class-use/SuggestionCity.html#com.amap.api.services.busline">SuggestionCity</a>
<div class="block">定义了当POI搜索无结果时，引擎对于搜索的城市建议内容。</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.amap.api.services.cloud">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表类和解释">
<caption><span><a href="../../../../../com/amap/api/services/cloud/package-summary.html">com.amap.api.services.cloud</a>使用的<a href="../../../../../com/amap/api/services/core/package-summary.html">com.amap.api.services.core</a>中的类</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">类和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/core/class-use/AMapException.html#com.amap.api.services.cloud">AMapException</a>
<div class="block">服务异常信息类。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/core/class-use/LatLonPoint.html#com.amap.api.services.cloud">LatLonPoint</a>
<div class="block">几何点对象类。</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.amap.api.services.core">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表类和解释">
<caption><span><a href="../../../../../com/amap/api/services/core/package-summary.html">com.amap.api.services.core</a>使用的<a href="../../../../../com/amap/api/services/core/package-summary.html">com.amap.api.services.core</a>中的类</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">类和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/core/class-use/LatLonPoint.html#com.amap.api.services.core">LatLonPoint</a>
<div class="block">几何点对象类。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/core/class-use/ServiceSettings.html#com.amap.api.services.core">ServiceSettings</a>
<div class="block">设置API接口访问协议的单例。</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.amap.api.services.district">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表类和解释">
<caption><span><a href="../../../../../com/amap/api/services/district/package-summary.html">com.amap.api.services.district</a>使用的<a href="../../../../../com/amap/api/services/core/package-summary.html">com.amap.api.services.core</a>中的类</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">类和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/core/class-use/AMapException.html#com.amap.api.services.district">AMapException</a>
<div class="block">服务异常信息类。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/core/class-use/LatLonPoint.html#com.amap.api.services.district">LatLonPoint</a>
<div class="block">几何点对象类。</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.amap.api.services.geocoder">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表类和解释">
<caption><span><a href="../../../../../com/amap/api/services/geocoder/package-summary.html">com.amap.api.services.geocoder</a>使用的<a href="../../../../../com/amap/api/services/core/package-summary.html">com.amap.api.services.core</a>中的类</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">类和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/core/class-use/AMapException.html#com.amap.api.services.geocoder">AMapException</a>
<div class="block">服务异常信息类。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/core/class-use/LatLonPoint.html#com.amap.api.services.geocoder">LatLonPoint</a>
<div class="block">几何点对象类。</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/core/class-use/PoiItem.html#com.amap.api.services.geocoder">PoiItem</a>
<div class="block">定义一个POI（Point Of Interest，兴趣点）。</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.amap.api.services.help">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表类和解释">
<caption><span><a href="../../../../../com/amap/api/services/help/package-summary.html">com.amap.api.services.help</a>使用的<a href="../../../../../com/amap/api/services/core/package-summary.html">com.amap.api.services.core</a>中的类</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">类和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/core/class-use/AMapException.html#com.amap.api.services.help">AMapException</a>
<div class="block">服务异常信息类。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/core/class-use/LatLonPoint.html#com.amap.api.services.help">LatLonPoint</a>
<div class="block">几何点对象类。</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.amap.api.services.nearby">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表类和解释">
<caption><span><a href="../../../../../com/amap/api/services/nearby/package-summary.html">com.amap.api.services.nearby</a>使用的<a href="../../../../../com/amap/api/services/core/package-summary.html">com.amap.api.services.core</a>中的类</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">类和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/core/class-use/AMapException.html#com.amap.api.services.nearby">AMapException</a>
<div class="block">服务异常信息类。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/core/class-use/LatLonPoint.html#com.amap.api.services.nearby">LatLonPoint</a>
<div class="block">几何点对象类。</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.amap.api.services.poisearch">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表类和解释">
<caption><span><a href="../../../../../com/amap/api/services/poisearch/package-summary.html">com.amap.api.services.poisearch</a>使用的<a href="../../../../../com/amap/api/services/core/package-summary.html">com.amap.api.services.core</a>中的类</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">类和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/core/class-use/AMapException.html#com.amap.api.services.poisearch">AMapException</a>
<div class="block">服务异常信息类。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/core/class-use/LatLonPoint.html#com.amap.api.services.poisearch">LatLonPoint</a>
<div class="block">几何点对象类。</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/core/class-use/PoiItem.html#com.amap.api.services.poisearch">PoiItem</a>
<div class="block">定义一个POI（Point Of Interest，兴趣点）。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/core/class-use/SuggestionCity.html#com.amap.api.services.poisearch">SuggestionCity</a>
<div class="block">定义了当POI搜索无结果时，引擎对于搜索的城市建议内容。</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.amap.api.services.road">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表类和解释">
<caption><span><a href="../../../../../com/amap/api/services/road/package-summary.html">com.amap.api.services.road</a>使用的<a href="../../../../../com/amap/api/services/core/package-summary.html">com.amap.api.services.core</a>中的类</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">类和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/core/class-use/LatLonPoint.html#com.amap.api.services.road">LatLonPoint</a>
<div class="block">几何点对象类。</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.amap.api.services.route">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表类和解释">
<caption><span><a href="../../../../../com/amap/api/services/route/package-summary.html">com.amap.api.services.route</a>使用的<a href="../../../../../com/amap/api/services/core/package-summary.html">com.amap.api.services.core</a>中的类</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">类和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/core/class-use/AMapException.html#com.amap.api.services.route">AMapException</a>
<div class="block">服务异常信息类。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/core/class-use/LatLonPoint.html#com.amap.api.services.route">LatLonPoint</a>
<div class="block">几何点对象类。</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.amap.api.services.routepoisearch">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表类和解释">
<caption><span><a href="../../../../../com/amap/api/services/routepoisearch/package-summary.html">com.amap.api.services.routepoisearch</a>使用的<a href="../../../../../com/amap/api/services/core/package-summary.html">com.amap.api.services.core</a>中的类</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">类和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/core/class-use/AMapException.html#com.amap.api.services.routepoisearch">AMapException</a>
<div class="block">服务异常信息类。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/core/class-use/LatLonPoint.html#com.amap.api.services.routepoisearch">LatLonPoint</a>
<div class="block">几何点对象类。</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.amap.api.services.share">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表类和解释">
<caption><span><a href="../../../../../com/amap/api/services/share/package-summary.html">com.amap.api.services.share</a>使用的<a href="../../../../../com/amap/api/services/core/package-summary.html">com.amap.api.services.core</a>中的类</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">类和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/core/class-use/AMapException.html#com.amap.api.services.share">AMapException</a>
<div class="block">服务异常信息类。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/core/class-use/LatLonPoint.html#com.amap.api.services.share">LatLonPoint</a>
<div class="block">几何点对象类。</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/core/class-use/LatLonSharePoint.html#com.amap.api.services.share">LatLonSharePoint</a>
<div class="block">分享几何坐标对象类。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/core/class-use/PoiItem.html#com.amap.api.services.share">PoiItem</a>
<div class="block">定义一个POI（Point Of Interest，兴趣点）。</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.amap.api.services.weather">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表类和解释">
<caption><span><a href="../../../../../com/amap/api/services/weather/package-summary.html">com.amap.api.services.weather</a>使用的<a href="../../../../../com/amap/api/services/core/package-summary.html">com.amap.api.services.core</a>中的类</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">类和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/services/core/class-use/AMapException.html#com.amap.api.services.weather">AMapException</a>
<div class="block">服务异常信息类。</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li>类</li>
<li class="navBarCell1Rev">使用</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个</li>
<li>下一个</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/core/package-use.html" target="_top">框架</a></li>
<li><a href="package-use.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
