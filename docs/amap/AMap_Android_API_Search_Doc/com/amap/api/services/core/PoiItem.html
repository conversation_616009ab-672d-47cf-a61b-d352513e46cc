<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>PoiItem</title>
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="PoiItem";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10,"i27":10,"i28":10,"i29":10};
var tabs = {65535:["t0","所有方法"],2:["t2","实例方法"],8:["t4","具体方法"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/PoiItem.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/core/LatLonSharePoint.html" title="com.amap.api.services.core中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/amap/api/services/core/SearchUtils.html" title="com.amap.api.services.core中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/core/PoiItem.html" target="_top">框架</a></li>
<li><a href="PoiItem.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.amap.api.services.core</div>
<h2 title="类 PoiItem" class="title">类 PoiItem</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.amap.api.services.core.PoiItem</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">PoiItem</span>
extends java.lang.Object</pre>
<div class="block">定义一个POI（Point Of Interest，兴趣点）。一个POI 由如下成员组成:
 <p>POI 的唯一标识，即id。这个标识在不同的数据版本中延续。</p>
 <p>POI 的名称。</p>
 <p>POI 的经纬度。</p>
 <p>POI 的地址。</p>
 <p>POI 的类型代码。</p>
 <p>POI 的类型描述。</p>
 <p>POI 的行政区划代码。</p>
 <p>POI 的行政区划名称。</p>
 <p>POI 的电话号码。</p>
 <p>POI 的城市编码。</p>
 <p>POI 的城市名称。</p>
 <p>POI 的省/自治区/直辖市/特别行政区名称</p>
 <p>POI距离周边搜索中心点的距离。</p>
 <p>POI网址。</p>
 <p>POI邮编。</p>
 <p>POI电子邮箱。</p>  
 <p>POI在逆地理编码时，POI点相对地理坐标点的方向 。</p></div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.0</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/PoiItem.html#PoiItem-java.lang.String-com.amap.api.services.core.LatLonPoint-java.lang.String-java.lang.String-">PoiItem</a></span>(java.lang.String&nbsp;id,
       <a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&nbsp;point,
       java.lang.String&nbsp;title,
       java.lang.String&nbsp;snippet)</code>
<div class="block">根据给定的参数构造一个PoiItem 的新对象。</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>所有方法</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">实例方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">具体方法</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/PoiItem.html#equals-java.lang.Object-">equals</a></span>(java.lang.Object&nbsp;obj)</code>
<div class="block">比较两个POI 的id 是否相等。</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/PoiItem.html#getAdCode--">getAdCode</a></span>()</code>
<div class="block">返回POI 的行政区划代码。</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/PoiItem.html#getAdName--">getAdName</a></span>()</code>
<div class="block">返回POI 的行政区划名称。</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/PoiItem.html#getBusinessArea--">getBusinessArea</a></span>()</code>
<div class="block">返回POI的所在商圈。</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/PoiItem.html#getCityCode--">getCityCode</a></span>()</code>
<div class="block">返回POI的城市编码</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/PoiItem.html#getCityName--">getCityName</a></span>()</code>
<div class="block">返回POI的城市名称。</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/PoiItem.html#getDirection--">getDirection</a></span>()</code>
<div class="block">返回逆地理编码查询时POI坐标点相对于地理坐标点的方向。</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/PoiItem.html#getDistance--">getDistance</a></span>()</code>
<div class="block">获取 POI 距离中心点的距离。</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/PoiItem.html#getEmail--">getEmail</a></span>()</code>
<div class="block">返回POI的电子邮件地址。</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/PoiItem.html#getEnter--">getEnter</a></span>()</code>
<div class="block">返回POI入口经纬度。</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/PoiItem.html#getExit--">getExit</a></span>()</code>
<div class="block">返回POI出口经纬度。</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/services/poisearch/IndoorData.html" title="com.amap.api.services.poisearch中的类">IndoorData</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/PoiItem.html#getIndoorData--">getIndoorData</a></span>()</code>
<div class="block">返回POI的室内信息，如楼层、建筑物。</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/PoiItem.html#getLatLonPoint--">getLatLonPoint</a></span>()</code>
<div class="block">返回POI的经纬度坐标<br>

 如果使用该POI进行导航时，可以检查POI是否有<a href="../../../../../com/amap/api/services/core/PoiItem.html#getExit--"><code>PoiItem.getExit()</code></a> 和 <a href="../../../../../com/amap/api/services/core/PoiItem.html#getEnter--"><code>PoiItem.getEnter()</code></a>，如果有建议使用它们作为导航的起终点。</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/PoiItem.html#getParkingType--">getParkingType</a></span>()</code>
<div class="block">返回POI的停车场类型</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../../com/amap/api/services/poisearch/Photo.html" title="com.amap.api.services.poisearch中的类">Photo</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/PoiItem.html#getPhotos--">getPhotos</a></span>()</code>
<div class="block">返回POI的图片信息。</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/services/poisearch/PoiItemExtension.html" title="com.amap.api.services.poisearch中的类">PoiItemExtension</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/PoiItem.html#getPoiExtension--">getPoiExtension</a></span>()</code>
<div class="block">返回POI的扩展信息。</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/PoiItem.html#getPoiId--">getPoiId</a></span>()</code>
<div class="block">返回POI 的id，即其唯一标识。</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/PoiItem.html#getPostcode--">getPostcode</a></span>()</code>
<div class="block">返回POI的邮编。</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/PoiItem.html#getProvinceCode--">getProvinceCode</a></span>()</code>
<div class="block">返回 POI 的省/自治区/直辖市/特别行政区编码。</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/PoiItem.html#getProvinceName--">getProvinceName</a></span>()</code>
<div class="block">返回POI的省/自治区/直辖市/特别行政区名称 。</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/PoiItem.html#getSnippet--">getSnippet</a></span>()</code>
<div class="block">返回POI的地址。</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../../com/amap/api/services/poisearch/SubPoiItem.html" title="com.amap.api.services.poisearch中的类">SubPoiItem</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/PoiItem.html#getSubPois--">getSubPois</a></span>()</code>
<div class="block">子POI信息获取。</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/PoiItem.html#getTel--">getTel</a></span>()</code>
<div class="block">返回POI的电话号码。</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/PoiItem.html#getTitle--">getTitle</a></span>()</code>
<div class="block">返回POI的名称。</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/PoiItem.html#getTypeCode--">getTypeCode</a></span>()</code>
<div class="block">返回兴趣点类型编码。</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/PoiItem.html#getTypeDes--">getTypeDes</a></span>()</code>
<div class="block">返回POI 的类型描述。</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/PoiItem.html#getWebsite--">getWebsite</a></span>()</code>
<div class="block">返回POI的网址。</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/PoiItem.html#hashCode--">hashCode</a></span>()</code>
<div class="block">继承自Object，直接返回POI id 的hashcode。</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/PoiItem.html#isIndoorMap--">isIndoorMap</a></span>()</code>
<div class="block">返回是否支持室内地图 。</div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/PoiItem.html#toString--">toString</a></span>()</code>
<div class="block">继承自Object，返回POI 的名称（name）。</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, finalize, getClass, notify, notifyAll, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="PoiItem-java.lang.String-com.amap.api.services.core.LatLonPoint-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>PoiItem</h4>
<pre>public&nbsp;PoiItem(java.lang.String&nbsp;id,
               <a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&nbsp;point,
               java.lang.String&nbsp;title,
               java.lang.String&nbsp;snippet)</pre>
<div class="block">根据给定的参数构造一个PoiItem 的新对象。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>id</code> - POI 的标识。</dd>
<dd><code>point</code> - 该POI的位置。</dd>
<dd><code>title</code> - 该POI的名称。</dd>
<dd><code>snippet</code> - POI的地址。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="getBusinessArea--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBusinessArea</h4>
<pre>public&nbsp;java.lang.String&nbsp;getBusinessArea()</pre>
<div class="block">返回POI的所在商圈。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>POI的所在商圈。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
<a name="getAdName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAdName</h4>
<pre>public&nbsp;java.lang.String&nbsp;getAdName()</pre>
<div class="block">返回POI 的行政区划名称。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>POI 的行政区划名称。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.2.0</dd>
</dl>
</li>
</ul>
<a name="getCityName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCityName</h4>
<pre>public&nbsp;java.lang.String&nbsp;getCityName()</pre>
<div class="block">返回POI的城市名称。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>POI的城市名称。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.2.0</dd>
</dl>
</li>
</ul>
<a name="getProvinceName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProvinceName</h4>
<pre>public&nbsp;java.lang.String&nbsp;getProvinceName()</pre>
<div class="block">返回POI的省/自治区/直辖市/特别行政区名称 。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>POI的省/自治区/直辖市/特别行政区名称</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.2.0</dd>
</dl>
</li>
</ul>
<a name="getTypeDes--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTypeDes</h4>
<pre>public&nbsp;java.lang.String&nbsp;getTypeDes()</pre>
<div class="block">返回POI 的类型描述。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>POI 的类型描述。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.0</dd>
</dl>
</li>
</ul>
<a name="getTel--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTel</h4>
<pre>public&nbsp;java.lang.String&nbsp;getTel()</pre>
<div class="block">返回POI的电话号码。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>POI的电话号码。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.0</dd>
</dl>
</li>
</ul>
<a name="getAdCode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAdCode</h4>
<pre>public&nbsp;java.lang.String&nbsp;getAdCode()</pre>
<div class="block">返回POI 的行政区划代码。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>POI 的行政区划代码。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.0</dd>
</dl>
</li>
</ul>
<a name="getPoiId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPoiId</h4>
<pre>public&nbsp;java.lang.String&nbsp;getPoiId()</pre>
<div class="block">返回POI 的id，即其唯一标识。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>POI的id。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.0</dd>
</dl>
</li>
</ul>
<a name="getDistance--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDistance</h4>
<pre>public&nbsp;int&nbsp;getDistance()</pre>
<div class="block">获取 POI 距离中心点的距离。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>POI 到中心点的距离，单位：米。返回-1时，代表此字段无数据。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.0</dd>
</dl>
</li>
</ul>
<a name="getTitle--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTitle</h4>
<pre>public&nbsp;java.lang.String&nbsp;getTitle()</pre>
<div class="block">返回POI的名称。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>POI的名称。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="getSnippet--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSnippet</h4>
<pre>public&nbsp;java.lang.String&nbsp;getSnippet()</pre>
<div class="block">返回POI的地址。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>POI的地址。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="getLatLonPoint--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLatLonPoint</h4>
<pre>public&nbsp;<a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&nbsp;getLatLonPoint()</pre>
<div class="block">返回POI的经纬度坐标<br>

 如果使用该POI进行导航时，可以检查POI是否有<a href="../../../../../com/amap/api/services/core/PoiItem.html#getExit--"><code>PoiItem.getExit()</code></a> 和 <a href="../../../../../com/amap/api/services/core/PoiItem.html#getEnter--"><code>PoiItem.getEnter()</code></a>，如果有建议使用它们作为导航的起终点。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>POI的经纬度坐标</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.4</dd>
</dl>
</li>
</ul>
<a name="getCityCode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCityCode</h4>
<pre>public&nbsp;java.lang.String&nbsp;getCityCode()</pre>
<div class="block">返回POI的城市编码</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>POI的城市编码</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.2</dd>
</dl>
</li>
</ul>
<a name="getEnter--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEnter</h4>
<pre>public&nbsp;<a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&nbsp;getEnter()</pre>
<div class="block">返回POI入口经纬度。<br>
 POI入口一般指商场、停车场等的POI兴趣点入口。<br>

 导航时，如果目的地为该POI且POI存在入口，建议使用入口经纬度作为导航终点。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>POI入口经纬度。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="getExit--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getExit</h4>
<pre>public&nbsp;<a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&nbsp;getExit()</pre>
<div class="block">返回POI出口经纬度。<br>
 POI出口一般指商场、停车场等POI兴趣点的出口。<br>

 导航时，如果出发点为该POI且POI存在出口，建议使用出口经纬度作为导航起点。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>POI出口经纬度。 <br></></dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="getWebsite--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWebsite</h4>
<pre>public&nbsp;java.lang.String&nbsp;getWebsite()</pre>
<div class="block">返回POI的网址。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>POI的网址。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="getPostcode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPostcode</h4>
<pre>public&nbsp;java.lang.String&nbsp;getPostcode()</pre>
<div class="block">返回POI的邮编。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>POI的邮编。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="getEmail--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEmail</h4>
<pre>public&nbsp;java.lang.String&nbsp;getEmail()</pre>
<div class="block">返回POI的电子邮件地址。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>POI的电子邮件地址。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="getDirection--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDirection</h4>
<pre>public&nbsp;java.lang.String&nbsp;getDirection()</pre>
<div class="block">返回逆地理编码查询时POI坐标点相对于地理坐标点的方向。POI搜索时，此处为空。
 方向为中文名称，如东、西南等。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>POI坐标点相对于地理坐标点的方向。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="isIndoorMap--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isIndoorMap</h4>
<pre>public&nbsp;boolean&nbsp;isIndoorMap()</pre>
<div class="block">返回是否支持室内地图 。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>支持室内地图返回 true，否则返回 false。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.2.1</dd>
</dl>
</li>
</ul>
<a name="getProvinceCode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProvinceCode</h4>
<pre>public&nbsp;java.lang.String&nbsp;getProvinceCode()</pre>
<div class="block">返回 POI 的省/自治区/直辖市/特别行政区编码。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>POI 的省/自治区/直辖市/特别行政区编码。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.2.1</dd>
</dl>
</li>
</ul>
<a name="getParkingType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getParkingType</h4>
<pre>public&nbsp;java.lang.String&nbsp;getParkingType()</pre>
<div class="block">返回POI的停车场类型</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>停车场类型。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.8.0</dd>
</dl>
</li>
</ul>
<a name="getSubPois--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSubPois</h4>
<pre>public&nbsp;java.util.List&lt;<a href="../../../../../com/amap/api/services/poisearch/SubPoiItem.html" title="com.amap.api.services.poisearch中的类">SubPoiItem</a>&gt;&nbsp;getSubPois()</pre>
<div class="block">子POI信息获取。仅当该POI有子POI数据时存在。例：搜索北京大学，POI为北京大学，子POI为“北京大学(西2门)”、北京大学(西3门)等。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>子POI信息列表</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.8.0</dd>
</dl>
</li>
</ul>
<a name="getIndoorData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getIndoorData</h4>
<pre>public&nbsp;<a href="../../../../../com/amap/api/services/poisearch/IndoorData.html" title="com.amap.api.services.poisearch中的类">IndoorData</a>&nbsp;getIndoorData()</pre>
<div class="block">返回POI的室内信息，如楼层、建筑物。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>室内POI的相关信息</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
</dl>
</li>
</ul>
<a name="getPhotos--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPhotos</h4>
<pre>public&nbsp;java.util.List&lt;<a href="../../../../../com/amap/api/services/poisearch/Photo.html" title="com.amap.api.services.poisearch中的类">Photo</a>&gt;&nbsp;getPhotos()</pre>
<div class="block">返回POI的图片信息。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>图片类列表。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.5.0</dd>
</dl>
</li>
</ul>
<a name="getPoiExtension--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPoiExtension</h4>
<pre>public&nbsp;<a href="../../../../../com/amap/api/services/poisearch/PoiItemExtension.html" title="com.amap.api.services.poisearch中的类">PoiItemExtension</a>&nbsp;getPoiExtension()</pre>
<div class="block">返回POI的扩展信息。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>poi深度信息。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.5.0</dd>
</dl>
</li>
</ul>
<a name="getTypeCode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTypeCode</h4>
<pre>public&nbsp;java.lang.String&nbsp;getTypeCode()</pre>
<div class="block">返回兴趣点类型编码。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>POI的类型编码。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.6.0</dd>
</dl>
</li>
</ul>
<a name="equals-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>equals</h4>
<pre>public&nbsp;boolean&nbsp;equals(java.lang.Object&nbsp;obj)</pre>
<div class="block">比较两个POI 的id 是否相等。</div>
<dl>
<dt><span class="overrideSpecifyLabel">覆盖:</span></dt>
<dd><code>equals</code>&nbsp;在类中&nbsp;<code>java.lang.Object</code></dd>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>obj</code> - 要与之比较的引用对象。</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>一个布尔值，如果两个POI 的id 相等，则返回true,否则返回false。</dd>
</dl>
</li>
</ul>
<a name="hashCode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>hashCode</h4>
<pre>public&nbsp;int&nbsp;hashCode()</pre>
<div class="block">继承自Object，直接返回POI id 的hashcode。</div>
<dl>
<dt><span class="overrideSpecifyLabel">覆盖:</span></dt>
<dd><code>hashCode</code>&nbsp;在类中&nbsp;<code>java.lang.Object</code></dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>POI id 的hashcode。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.0</dd>
</dl>
</li>
</ul>
<a name="toString--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>toString</h4>
<pre>public&nbsp;java.lang.String&nbsp;toString()</pre>
<div class="block">继承自Object，返回POI 的名称（name）。</div>
<dl>
<dt><span class="overrideSpecifyLabel">覆盖:</span></dt>
<dd><code>toString</code>&nbsp;在类中&nbsp;<code>java.lang.Object</code></dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/PoiItem.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/core/LatLonSharePoint.html" title="com.amap.api.services.core中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/amap/api/services/core/SearchUtils.html" title="com.amap.api.services.core中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/core/PoiItem.html" target="_top">框架</a></li>
<li><a href="PoiItem.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
