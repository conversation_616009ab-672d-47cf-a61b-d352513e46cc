<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>类 com.amap.api.services.core.LatLonPoint的使用</title>
<link rel="stylesheet" type="text/css" href="../../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="\u7C7B com.amap.api.services.core.LatLonPoint\u7684\u4F7F\u7528";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../../overview-summary.html">概览</a></li>
<li><a href="../package-summary.html">程序包</a></li>
<li><a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">类</a></li>
<li class="navBarCell1Rev">使用</li>
<li><a href="../../../../../../overview-tree.html">树</a></li>
<li><a href="../../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个</li>
<li>下一个</li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?com/amap/api/services/core/class-use/LatLonPoint.html" target="_top">框架</a></li>
<li><a href="LatLonPoint.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h2 title="类的使用 com.amap.api.services.core.LatLonPoint" class="title">类的使用<br>com.amap.api.services.core.LatLonPoint</h2>
</div>
<div class="classUseContainer">
<ul class="blockList">
<li class="blockList">
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表程序包和解释">
<caption><span>使用<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>的程序包</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">程序包</th>
<th class="colLast" scope="col">说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="#com.amap.api.services.busline">com.amap.api.services.busline</a></td>
<td class="colLast">
<div class="block">
公交线路和公交站点查询包。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.amap.api.services.cloud">com.amap.api.services.cloud</a></td>
<td class="colLast">
<div class="block">
云检索包，包含了自有数据搜索查询的功能。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#com.amap.api.services.core">com.amap.api.services.core</a></td>
<td class="colLast">
<div class="block">
工具类包。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.amap.api.services.district">com.amap.api.services.district</a></td>
<td class="colLast">
<div class="block">
行政区划查询包，查询某个行政级别的详细信息。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#com.amap.api.services.geocoder">com.amap.api.services.geocoder</a></td>
<td class="colLast">
<div class="block">
地理编码包，用以实现通过地理编码进行查询的功能。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.amap.api.services.help">com.amap.api.services.help</a></td>
<td class="colLast">
<div class="block">
服务帮助包，用来实现查询过程中提供的提示帮助。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#com.amap.api.services.nearby">com.amap.api.services.nearby</a></td>
<td class="colLast">
<div class="block">
附近派单功能包，包含上传位置信息、检索位置信息等 自7.4.0起不再支持附件功能
</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.amap.api.services.poisearch">com.amap.api.services.poisearch</a></td>
<td class="colLast">
<div class="block">
POI查询包，包含了兴趣点的详细信息。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#com.amap.api.services.road">com.amap.api.services.road</a></td>
<td class="colLast">
<div class="block">
道路查询包，包含了道路查询结果的详细信息。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.amap.api.services.route">com.amap.api.services.route</a></td>
<td class="colLast">
<div class="block">
路径查询包，包含了在公交、自驾或步行路线规划中的详细信息。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#com.amap.api.services.routepoisearch">com.amap.api.services.routepoisearch</a></td>
<td class="colLast">
<div class="block">
沿途POI搜索包，搜索驾车沿途的POI信息。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.amap.api.services.share">com.amap.api.services.share</a></td>
<td class="colLast">
<div class="block">
短串分享包，包含了位置/POI/路径规划/导航的分享功能。</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<ul class="blockList">
<li class="blockList"><a name="com.amap.api.services.busline">
<!--   -->
</a>
<h3><a href="../../../../../../com/amap/api/services/busline/package-summary.html">com.amap.api.services.busline</a>中<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>的使用</h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表方法和解释">
<caption><span>返回<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>的<a href="../../../../../../com/amap/api/services/busline/package-summary.html">com.amap.api.services.busline</a>中的方法</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a></code></td>
<td class="colLast"><span class="typeNameLabel">BusStationItem.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/busline/BusStationItem.html#getLatLonPoint--">getLatLonPoint</a></span>()</code>
<div class="block">返回车站经纬度坐标。</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表方法和解释">
<caption><span>返回变量类型为<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>的类型的<a href="../../../../../../com/amap/api/services/busline/package-summary.html">com.amap.api.services.busline</a>中的方法</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">BusLineItem.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/busline/BusLineItem.html#getBounds--">getBounds</a></span>()</code>
<div class="block">返回公交线路外包矩形的左下与右上顶点坐标。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">BusLineItem.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/busline/BusLineItem.html#getDirectionsCoordinates--">getDirectionsCoordinates</a></span>()</code>
<div class="block">返回公交线路的沿途坐标，包含首发站和终点站坐标。</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.amap.api.services.cloud">
<!--   -->
</a>
<h3><a href="../../../../../../com/amap/api/services/cloud/package-summary.html">com.amap.api.services.cloud</a>中<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>的使用</h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表字段和解释">
<caption><span>声明为<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>的<a href="../../../../../../com/amap/api/services/cloud/package-summary.html">com.amap.api.services.cloud</a>中的字段</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">字段和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a></code></td>
<td class="colLast"><span class="typeNameLabel">CloudItem.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/cloud/CloudItem.html#mPoint">mPoint</a></span></code>
<div class="block">CloudItem的位置</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表方法和解释">
<caption><span>返回<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>的<a href="../../../../../../com/amap/api/services/cloud/package-summary.html">com.amap.api.services.cloud</a>中的方法</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a></code></td>
<td class="colLast"><span class="typeNameLabel">CloudSearch.SearchBound.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/cloud/CloudSearch.SearchBound.html#getCenter--">getCenter</a></span>()</code>
<div class="block">返回查询圆形的中心点坐标。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a></code></td>
<td class="colLast"><span class="typeNameLabel">CloudItem.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/cloud/CloudItem.html#getLatLonPoint--">getLatLonPoint</a></span>()</code>
<div class="block">返回 CloudItem 的经纬度坐标 。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a></code></td>
<td class="colLast"><span class="typeNameLabel">CloudSearch.SearchBound.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/cloud/CloudSearch.SearchBound.html#getLowerLeft--">getLowerLeft</a></span>()</code>
<div class="block">返回查询矩形的左下角坐标。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a></code></td>
<td class="colLast"><span class="typeNameLabel">CloudSearch.SearchBound.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/cloud/CloudSearch.SearchBound.html#getUpperRight--">getUpperRight</a></span>()</code>
<div class="block">返回查询矩形的右上角坐标。</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表方法和解释">
<caption><span>返回变量类型为<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>的类型的<a href="../../../../../../com/amap/api/services/cloud/package-summary.html">com.amap.api.services.cloud</a>中的方法</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">CloudSearch.SearchBound.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/cloud/CloudSearch.SearchBound.html#getPolyGonList--">getPolyGonList</a></span>()</code>
<div class="block">返回查询多边形的坐标点。</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表构造器和解释">
<caption><span>参数类型为<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>的<a href="../../../../../../com/amap/api/services/cloud/package-summary.html">com.amap.api.services.cloud</a>中的构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/cloud/CloudSearch.SearchBound.html#SearchBound-com.amap.api.services.core.LatLonPoint-int-">SearchBound</a></span>(<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&nbsp;center,
           int&nbsp;radiusInMeters)</code>
<div class="block">根据给定的参数来构造一个圆形查询范围对象。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/cloud/CloudSearch.SearchBound.html#SearchBound-com.amap.api.services.core.LatLonPoint-com.amap.api.services.core.LatLonPoint-">SearchBound</a></span>(<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&nbsp;lowerLeft,
           <a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&nbsp;upperRight)</code>
<div class="block">根据给定的参数来构造一个矩形查询范围对象。</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表构造器和解释">
<caption><span>类型变量类型为<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>的<a href="../../../../../../com/amap/api/services/cloud/package-summary.html">com.amap.api.services.cloud</a>中的构造器参数</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/cloud/CloudSearch.SearchBound.html#SearchBound-java.util.List-">SearchBound</a></span>(java.util.List&lt;<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&gt;&nbsp;polygonList)</code>
<div class="block">根据给定的参数来构造一个多边形查询范围对象。</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.amap.api.services.core">
<!--   -->
</a>
<h3><a href="../../../../../../com/amap/api/services/core/package-summary.html">com.amap.api.services.core</a>中<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>的使用</h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表子类和解释">
<caption><span><a href="../../../../../../com/amap/api/services/core/package-summary.html">com.amap.api.services.core</a>中<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>的子类</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">类和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/core/LatLonSharePoint.html" title="com.amap.api.services.core中的类">LatLonSharePoint</a></span></code>
<div class="block">分享几何坐标对象类。</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表方法和解释">
<caption><span>返回<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>的<a href="../../../../../../com/amap/api/services/core/package-summary.html">com.amap.api.services.core</a>中的方法</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a></code></td>
<td class="colLast"><span class="typeNameLabel">LatLonPoint.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/core/LatLonPoint.html#copy--">copy</a></span>()</code>
<div class="block">复制一个经纬度点对象。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a></code></td>
<td class="colLast"><span class="typeNameLabel">PoiItem.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/core/PoiItem.html#getEnter--">getEnter</a></span>()</code>
<div class="block">返回POI入口经纬度。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a></code></td>
<td class="colLast"><span class="typeNameLabel">PoiItem.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/core/PoiItem.html#getExit--">getExit</a></span>()</code>
<div class="block">返回POI出口经纬度。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a></code></td>
<td class="colLast"><span class="typeNameLabel">PoiItem.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/core/PoiItem.html#getLatLonPoint--">getLatLonPoint</a></span>()</code>
<div class="block">返回POI的经纬度坐标<br>

 如果使用该POI进行导航时，可以检查POI是否有<a href="../../../../../../com/amap/api/services/core/PoiItem.html#getExit--"><code>PoiItem.getExit()</code></a> 和 <a href="../../../../../../com/amap/api/services/core/PoiItem.html#getEnter--"><code>PoiItem.getEnter()</code></a>，如果有建议使用它们作为导航的起终点。</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表构造器和解释">
<caption><span>参数类型为<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>的<a href="../../../../../../com/amap/api/services/core/package-summary.html">com.amap.api.services.core</a>中的构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/core/PoiItem.html#PoiItem-java.lang.String-com.amap.api.services.core.LatLonPoint-java.lang.String-java.lang.String-">PoiItem</a></span>(java.lang.String&nbsp;id,
       <a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&nbsp;point,
       java.lang.String&nbsp;title,
       java.lang.String&nbsp;snippet)</code>
<div class="block">根据给定的参数构造一个PoiItem 的新对象。</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.amap.api.services.district">
<!--   -->
</a>
<h3><a href="../../../../../../com/amap/api/services/district/package-summary.html">com.amap.api.services.district</a>中<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>的使用</h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表方法和解释">
<caption><span>返回<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>的<a href="../../../../../../com/amap/api/services/district/package-summary.html">com.amap.api.services.district</a>中的方法</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a></code></td>
<td class="colLast"><span class="typeNameLabel">DistrictItem.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/district/DistrictItem.html#getCenter--">getCenter</a></span>()</code>
<div class="block">返回行政区域规划中心点的经纬度坐标。</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.amap.api.services.geocoder">
<!--   -->
</a>
<h3><a href="../../../../../../com/amap/api/services/geocoder/package-summary.html">com.amap.api.services.geocoder</a>中<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>的使用</h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表方法和解释">
<caption><span>返回<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>的<a href="../../../../../../com/amap/api/services/geocoder/package-summary.html">com.amap.api.services.geocoder</a>中的方法</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a></code></td>
<td class="colLast"><span class="typeNameLabel">AoiItem.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/geocoder/AoiItem.html#getAoiCenterPoint--">getAoiCenterPoint</a></span>()</code>
<div class="block">返回AOI的中心点坐标。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a></code></td>
<td class="colLast"><span class="typeNameLabel">BusinessArea.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/geocoder/BusinessArea.html#getCenterPoint--">getCenterPoint</a></span>()</code>
<div class="block">返回当前商圈的中心点坐标。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a></code></td>
<td class="colLast"><span class="typeNameLabel">RegeocodeRoad.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/geocoder/RegeocodeRoad.html#getLatLngPoint--">getLatLngPoint</a></span>()</code>
<div class="block">返回道路中心点的经纬度坐标。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a></code></td>
<td class="colLast"><span class="typeNameLabel">GeocodeAddress.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/geocoder/GeocodeAddress.html#getLatLonPoint--">getLatLonPoint</a></span>()</code>
<div class="block">地理编码返回的经纬度坐标。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a></code></td>
<td class="colLast"><span class="typeNameLabel">StreetNumber.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/geocoder/StreetNumber.html#getLatLonPoint--">getLatLonPoint</a></span>()</code>
<div class="block">返回门牌信息中的经纬度坐标。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a></code></td>
<td class="colLast"><span class="typeNameLabel">RegeocodeQuery.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/geocoder/RegeocodeQuery.html#getPoint--">getPoint</a></span>()</code>
<div class="block">返回逆地理编码的地理坐标点。</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表方法和解释">
<caption><span>参数类型为<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>的<a href="../../../../../../com/amap/api/services/geocoder/package-summary.html">com.amap.api.services.geocoder</a>中的方法</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">StreetNumber.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/geocoder/StreetNumber.html#setLatLonPoint-com.amap.api.services.core.LatLonPoint-">setLatLonPoint</a></span>(<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&nbsp;latLonPoint)</code>
<div class="block">设置门牌信息中的经纬度坐标。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">RegeocodeQuery.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/geocoder/RegeocodeQuery.html#setPoint-com.amap.api.services.core.LatLonPoint-">setPoint</a></span>(<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&nbsp;point)</code>
<div class="block">设置逆地理编码的地理坐标点。</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表构造器和解释">
<caption><span>参数类型为<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>的<a href="../../../../../../com/amap/api/services/geocoder/package-summary.html">com.amap.api.services.geocoder</a>中的构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/geocoder/RegeocodeQuery.html#RegeocodeQuery-com.amap.api.services.core.LatLonPoint-float-java.lang.String-">RegeocodeQuery</a></span>(<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&nbsp;point,
              float&nbsp;radius,
              java.lang.String&nbsp;latLonType)</code>
<div class="block">RegeocodeQuery构造函数。</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.amap.api.services.help">
<!--   -->
</a>
<h3><a href="../../../../../../com/amap/api/services/help/package-summary.html">com.amap.api.services.help</a>中<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>的使用</h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表方法和解释">
<caption><span>返回<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>的<a href="../../../../../../com/amap/api/services/help/package-summary.html">com.amap.api.services.help</a>中的方法</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a></code></td>
<td class="colLast"><span class="typeNameLabel">InputtipsQuery.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/help/InputtipsQuery.html#getLocation--">getLocation</a></span>()</code>
<div class="block">返回对结果进行位置限制的经纬度点</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a></code></td>
<td class="colLast"><span class="typeNameLabel">Tip.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/help/Tip.html#getPoint--">getPoint</a></span>()</code>
<div class="block">获取Poi的经纬度，如果不存在经纬度则为空。</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表方法和解释">
<caption><span>参数类型为<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>的<a href="../../../../../../com/amap/api/services/help/package-summary.html">com.amap.api.services.help</a>中的方法</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">InputtipsQuery.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/help/InputtipsQuery.html#setLocation-com.amap.api.services.core.LatLonPoint-">setLocation</a></span>(<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&nbsp;location)</code>
<div class="block">对获取结果进行经纬度位置限制</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.amap.api.services.nearby">
<!--   -->
</a>
<h3><a href="../../../../../../com/amap/api/services/nearby/package-summary.html">com.amap.api.services.nearby</a>中<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>的使用</h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表方法和解释">
<caption><span>返回<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>的<a href="../../../../../../com/amap/api/services/nearby/package-summary.html">com.amap.api.services.nearby</a>中的方法</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a></code></td>
<td class="colLast"><span class="typeNameLabel">NearbySearch.NearbyQuery.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/nearby/NearbySearch.NearbyQuery.html#getCenterPoint--">getCenterPoint</a></span>()</code>
<div class="block">返回检索中心点。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a></code></td>
<td class="colLast"><span class="typeNameLabel">NearbyInfo.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/nearby/NearbyInfo.html#getPoint--">getPoint</a></span>()</code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;</div>
<div class="block">获取用户位置信息。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a></code></td>
<td class="colLast"><span class="typeNameLabel">UploadInfo.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/nearby/UploadInfo.html#getPoint--">getPoint</a></span>()</code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;</div>
<div class="block">获取用户位置信息。</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表方法和解释">
<caption><span>参数类型为<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>的<a href="../../../../../../com/amap/api/services/nearby/package-summary.html">com.amap.api.services.nearby</a>中的方法</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">NearbySearch.NearbyQuery.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/nearby/NearbySearch.NearbyQuery.html#setCenterPoint-com.amap.api.services.core.LatLonPoint-">setCenterPoint</a></span>(<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&nbsp;latLonPoint)</code>
<div class="block">检索的中心点，必选参数。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">UploadInfo.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/nearby/UploadInfo.html#setPoint-com.amap.api.services.core.LatLonPoint-">setPoint</a></span>(<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&nbsp;point)</code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;</div>
<div class="block">设置用户位置信息。</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.amap.api.services.poisearch">
<!--   -->
</a>
<h3><a href="../../../../../../com/amap/api/services/poisearch/package-summary.html">com.amap.api.services.poisearch</a>中<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>的使用</h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表方法和解释">
<caption><span>返回<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>的<a href="../../../../../../com/amap/api/services/poisearch/package-summary.html">com.amap.api.services.poisearch</a>中的方法</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a></code></td>
<td class="colLast"><span class="typeNameLabel">PoiSearch.SearchBound.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/poisearch/PoiSearch.SearchBound.html#getCenter--">getCenter</a></span>()</code>
<div class="block">返回矩形中心点坐标。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a></code></td>
<td class="colLast"><span class="typeNameLabel">PoiSearchV2.SearchBound.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/poisearch/PoiSearchV2.SearchBound.html#getCenter--">getCenter</a></span>()</code>
<div class="block">返回矩形中心点坐标。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a></code></td>
<td class="colLast"><span class="typeNameLabel">PoiNavi.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/poisearch/PoiNavi.html#getEnter--">getEnter</a></span>()</code>
<div class="block">返回POI入口经纬度。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a></code></td>
<td class="colLast"><span class="typeNameLabel">PoiNavi.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/poisearch/PoiNavi.html#getExit--">getExit</a></span>()</code>
<div class="block">返回POI出口经纬度。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a></code></td>
<td class="colLast"><span class="typeNameLabel">SubPoiItem.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/poisearch/SubPoiItem.html#getLatLonPoint--">getLatLonPoint</a></span>()</code>
<div class="block">返回子POI的坐标。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a></code></td>
<td class="colLast"><span class="typeNameLabel">SubPoiItemV2.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/poisearch/SubPoiItemV2.html#getLatLonPoint--">getLatLonPoint</a></span>()</code>
<div class="block">返回子POI的坐标。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a></code></td>
<td class="colLast"><span class="typeNameLabel">PoiSearch.Query.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/poisearch/PoiSearch.Query.html#getLocation--">getLocation</a></span>()</code>
<div class="block">获取设置的经纬度</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a></code></td>
<td class="colLast"><span class="typeNameLabel">PoiSearchV2.Query.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/poisearch/PoiSearchV2.Query.html#getLocation--">getLocation</a></span>()</code>
<div class="block">获取设置的经纬度</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a></code></td>
<td class="colLast"><span class="typeNameLabel">PoiSearch.SearchBound.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/poisearch/PoiSearch.SearchBound.html#getLowerLeft--">getLowerLeft</a></span>()</code>
<div class="block">返回矩形左下角坐标。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a></code></td>
<td class="colLast"><span class="typeNameLabel">PoiSearchV2.SearchBound.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/poisearch/PoiSearchV2.SearchBound.html#getLowerLeft--">getLowerLeft</a></span>()</code>
<div class="block">返回矩形左下角坐标。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a></code></td>
<td class="colLast"><span class="typeNameLabel">PoiSearch.SearchBound.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/poisearch/PoiSearch.SearchBound.html#getUpperRight--">getUpperRight</a></span>()</code>
<div class="block">返回矩形右上角坐标。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a></code></td>
<td class="colLast"><span class="typeNameLabel">PoiSearchV2.SearchBound.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/poisearch/PoiSearchV2.SearchBound.html#getUpperRight--">getUpperRight</a></span>()</code>
<div class="block">返回矩形右上角坐标。</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表方法和解释">
<caption><span>返回变量类型为<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>的类型的<a href="../../../../../../com/amap/api/services/poisearch/package-summary.html">com.amap.api.services.poisearch</a>中的方法</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">PoiSearch.SearchBound.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/poisearch/PoiSearch.SearchBound.html#getPolyGonList--">getPolyGonList</a></span>()</code>
<div class="block">返回首尾相接的几何点，可以组成多边形。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">PoiSearchV2.SearchBound.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/poisearch/PoiSearchV2.SearchBound.html#getPolyGonList--">getPolyGonList</a></span>()</code>
<div class="block">返回首尾相接的几何点，可以组成多边形。</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表方法和解释">
<caption><span>参数类型为<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>的<a href="../../../../../../com/amap/api/services/poisearch/package-summary.html">com.amap.api.services.poisearch</a>中的方法</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">PoiSearch.Query.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/poisearch/PoiSearch.Query.html#setLocation-com.amap.api.services.core.LatLonPoint-">setLocation</a></span>(<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&nbsp;location)</code>
<div class="block">设置经纬度</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">PoiSearchV2.Query.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/poisearch/PoiSearchV2.Query.html#setLocation-com.amap.api.services.core.LatLonPoint-">setLocation</a></span>(<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&nbsp;location)</code>
<div class="block">设置经纬度</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表构造器和解释">
<caption><span>参数类型为<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>的<a href="../../../../../../com/amap/api/services/poisearch/package-summary.html">com.amap.api.services.poisearch</a>中的构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/poisearch/PoiSearch.SearchBound.html#SearchBound-com.amap.api.services.core.LatLonPoint-int-">SearchBound</a></span>(<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&nbsp;center,
           int&nbsp;radiusInMeters)</code>
<div class="block">根据给定的参数来构造PoiSearch.SearchBound 的新对象，默认由近到远排序。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/poisearch/PoiSearchV2.SearchBound.html#SearchBound-com.amap.api.services.core.LatLonPoint-int-">SearchBound</a></span>(<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&nbsp;center,
           int&nbsp;radiusInMeters)</code>
<div class="block">根据给定的参数来构造PoiSearchV2.SearchBound 的新对象，默认由近到远排序。</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/poisearch/PoiSearch.SearchBound.html#SearchBound-com.amap.api.services.core.LatLonPoint-int-boolean-">SearchBound</a></span>(<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&nbsp;center,
           int&nbsp;radiusInMeters,
           boolean&nbsp;isDistanceSort)</code>
<div class="block">根据给定的参数来构造PoiSearch.SearchBound 的新对象，默认由近到远排序。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/poisearch/PoiSearchV2.SearchBound.html#SearchBound-com.amap.api.services.core.LatLonPoint-int-boolean-">SearchBound</a></span>(<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&nbsp;center,
           int&nbsp;radiusInMeters,
           boolean&nbsp;isDistanceSort)</code>
<div class="block">根据给定的参数来构造PoiSearchV2.SearchBound 的新对象，默认由近到远排序。</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/poisearch/PoiSearch.SearchBound.html#SearchBound-com.amap.api.services.core.LatLonPoint-com.amap.api.services.core.LatLonPoint-">SearchBound</a></span>(<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&nbsp;lowerLeft,
           <a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&nbsp;upperRight)</code>
<div class="block">根据给定的参数来构造PoiSearch.SearchBound 的新对象。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/poisearch/PoiSearchV2.SearchBound.html#SearchBound-com.amap.api.services.core.LatLonPoint-com.amap.api.services.core.LatLonPoint-">SearchBound</a></span>(<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&nbsp;lowerLeft,
           <a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&nbsp;upperRight)</code>
<div class="block">根据给定的参数来构造PoiSearchV2.SearchBound 的新对象。</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表构造器和解释">
<caption><span>类型变量类型为<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>的<a href="../../../../../../com/amap/api/services/poisearch/package-summary.html">com.amap.api.services.poisearch</a>中的构造器参数</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/poisearch/PoiSearch.SearchBound.html#SearchBound-java.util.List-">SearchBound</a></span>(java.util.List&lt;<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&gt;&nbsp;list)</code>
<div class="block">根据给定的参数来构造PoiSearch.SearchBound 的新对象。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/poisearch/PoiSearchV2.SearchBound.html#SearchBound-java.util.List-">SearchBound</a></span>(java.util.List&lt;<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&gt;&nbsp;list)</code>
<div class="block">根据给定的参数来构造PoiSearchV2.SearchBound 的新对象。</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.amap.api.services.road">
<!--   -->
</a>
<h3><a href="../../../../../../com/amap/api/services/road/package-summary.html">com.amap.api.services.road</a>中<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>的使用</h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表方法和解释">
<caption><span>返回<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>的<a href="../../../../../../com/amap/api/services/road/package-summary.html">com.amap.api.services.road</a>中的方法</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a></code></td>
<td class="colLast"><span class="typeNameLabel">Road.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/road/Road.html#getCenterPoint--">getCenterPoint</a></span>()</code>
<div class="block">返回结果的道路中心点坐标。</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表方法和解释">
<caption><span>参数类型为<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>的<a href="../../../../../../com/amap/api/services/road/package-summary.html">com.amap.api.services.road</a>中的方法</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">Road.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/road/Road.html#setCenterPoint-com.amap.api.services.core.LatLonPoint-">setCenterPoint</a></span>(<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&nbsp;centerPoint)</code>
<div class="block">设置道路中心点的坐标。</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.amap.api.services.route">
<!--   -->
</a>
<h3><a href="../../../../../../com/amap/api/services/route/package-summary.html">com.amap.api.services.route</a>中<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>的使用</h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表方法和解释">
<caption><span>返回<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>的<a href="../../../../../../com/amap/api/services/route/package-summary.html">com.amap.api.services.route</a>中的方法</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a></code></td>
<td class="colLast"><span class="typeNameLabel">DistanceSearch.DistanceQuery.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/route/DistanceSearch.DistanceQuery.html#getDestination--">getDestination</a></span>()</code>
<div class="block">获取距离测量终点</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a></code></td>
<td class="colLast"><span class="typeNameLabel">RouteBusWalkItem.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/route/RouteBusWalkItem.html#getDestination--">getDestination</a></span>()</code>
<div class="block">返回此路段步行导航信息的终点坐标。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a></code></td>
<td class="colLast"><span class="typeNameLabel">TaxiItem.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/route/TaxiItem.html#getDestination--">getDestination</a></span>()</code>
<div class="block">获取Taxi终点坐标</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a></code></td>
<td class="colLast"><span class="typeNameLabel">TaxiItemV2.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/route/TaxiItemV2.html#getDestination--">getDestination</a></span>()</code>
<div class="block">获取Taxi终点坐标</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a></code></td>
<td class="colLast"><span class="typeNameLabel">RouteSearch.FromAndTo.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/route/RouteSearch.FromAndTo.html#getFrom--">getFrom</a></span>()</code>
<div class="block">返回路径规划的起点坐标。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a></code></td>
<td class="colLast"><span class="typeNameLabel">RouteSearchV2.FromAndTo.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/route/RouteSearchV2.FromAndTo.html#getFrom--">getFrom</a></span>()</code>
<div class="block">返回路径规划的起点坐标。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a></code></td>
<td class="colLast"><span class="typeNameLabel">Doorway.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/route/Doorway.html#getLatLonPoint--">getLatLonPoint</a></span>()</code>
<div class="block">返回公交换乘中换乘点的出（入）口坐标。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a></code></td>
<td class="colLast"><span class="typeNameLabel">RailwayStationItem.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/route/RailwayStationItem.html#getLocation--">getLocation</a></span>()</code>
<div class="block">返回站点经纬度。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a></code></td>
<td class="colLast"><span class="typeNameLabel">RouteBusWalkItem.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/route/RouteBusWalkItem.html#getOrigin--">getOrigin</a></span>()</code>
<div class="block">返回此路段步行导航信息的起点坐标。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a></code></td>
<td class="colLast"><span class="typeNameLabel">TaxiItem.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/route/TaxiItem.html#getOrigin--">getOrigin</a></span>()</code>
<div class="block">获取Taxi起点坐标</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a></code></td>
<td class="colLast"><span class="typeNameLabel">TaxiItemV2.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/route/TaxiItemV2.html#getOrigin--">getOrigin</a></span>()</code>
<div class="block">获取Taxi起点坐标</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a></code></td>
<td class="colLast"><span class="typeNameLabel">RoutePlanResult.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/route/RoutePlanResult.html#getStartPos--">getStartPos</a></span>()</code>
<div class="block">返回路径规划起点的位置。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a></code></td>
<td class="colLast"><span class="typeNameLabel">RouteResult.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/route/RouteResult.html#getStartPos--">getStartPos</a></span>()</code>
<div class="block">返回路径规划起点的位置。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a></code></td>
<td class="colLast"><span class="typeNameLabel">TruckRouteRestult.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/route/TruckRouteRestult.html#getStartPos--">getStartPos</a></span>()</code>
<div class="block">路线起点坐标</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a></code></td>
<td class="colLast"><span class="typeNameLabel">RoutePlanResult.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/route/RoutePlanResult.html#getTargetPos--">getTargetPos</a></span>()</code>
<div class="block">返回路径规划终点的位置。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a></code></td>
<td class="colLast"><span class="typeNameLabel">RouteResult.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/route/RouteResult.html#getTargetPos--">getTargetPos</a></span>()</code>
<div class="block">返回路径规划终点的位置。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a></code></td>
<td class="colLast"><span class="typeNameLabel">TruckRouteRestult.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/route/TruckRouteRestult.html#getTargetPos--">getTargetPos</a></span>()</code>
<div class="block">路线终点坐标</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a></code></td>
<td class="colLast"><span class="typeNameLabel">RouteSearch.FromAndTo.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/route/RouteSearch.FromAndTo.html#getTo--">getTo</a></span>()</code>
<div class="block">返回路径规划的终点坐标。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a></code></td>
<td class="colLast"><span class="typeNameLabel">RouteSearchV2.FromAndTo.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/route/RouteSearchV2.FromAndTo.html#getTo--">getTo</a></span>()</code>
<div class="block">返回路径规划的终点坐标。</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表方法和解释">
<caption><span>返回变量类型为<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>的类型的<a href="../../../../../../com/amap/api/services/route/package-summary.html">com.amap.api.services.route</a>中的方法</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>java.util.List&lt;java.util.List&lt;<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&gt;&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">RouteSearch.DriveRouteQuery.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/route/RouteSearch.DriveRouteQuery.html#getAvoidpolygons--">getAvoidpolygons</a></span>()</code>
<div class="block">返回设定查询的避让区域。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>java.util.List&lt;java.util.List&lt;<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&gt;&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">RouteSearchV2.DriveRouteQuery.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/route/RouteSearchV2.DriveRouteQuery.html#getAvoidpolygons--">getAvoidpolygons</a></span>()</code>
<div class="block">返回设定查询的避让区域。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">DistanceSearch.DistanceQuery.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/route/DistanceSearch.DistanceQuery.html#getOrigins--">getOrigins</a></span>()</code>
<div class="block">获取距离测量起点集合</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">RouteSearch.DriveRouteQuery.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/route/RouteSearch.DriveRouteQuery.html#getPassedByPoints--">getPassedByPoints</a></span>()</code>
<div class="block">返回设定查询的途经点。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">RouteSearchV2.DriveRouteQuery.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/route/RouteSearchV2.DriveRouteQuery.html#getPassedByPoints--">getPassedByPoints</a></span>()</code>
<div class="block">返回设定查询的途经点。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">DrivePlanStep.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/route/DrivePlanStep.html#getPolyline--">getPolyline</a></span>()</code>
<div class="block">返回驾车路段的坐标点集合。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">DriveStep.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/route/DriveStep.html#getPolyline--">getPolyline</a></span>()</code>
<div class="block">返回驾车路段的坐标点集合。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">DriveStepV2.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/route/DriveStepV2.html#getPolyline--">getPolyline</a></span>()</code>
<div class="block">返回驾车路段的坐标点集合。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">Path.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/route/Path.html#getPolyline--">getPolyline</a></span>()</code>
<div class="block">返回路线的坐标点集合。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">RideStep.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/route/RideStep.html#getPolyline--">getPolyline</a></span>()</code>
<div class="block">返回骑行路段的坐标点集合。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">RouteBusLineItem.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/route/RouteBusLineItem.html#getPolyline--">getPolyline</a></span>()</code>
<div class="block">返回此公交换乘路段（出发站-到达站）的坐标点集合。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">TMC.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/route/TMC.html#getPolyline--">getPolyline</a></span>()</code>
<div class="block">获取本段路况的坐标点集合。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">TaxiItemV2.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/route/TaxiItemV2.html#getPolyline--">getPolyline</a></span>()</code>
<div class="block">线路点的集合</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">TruckStep.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/route/TruckStep.html#getPolyline--">getPolyline</a></span>()</code>
<div class="block">此路段坐标点串 存放到 List中</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">WalkStep.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/route/WalkStep.html#getPolyline--">getPolyline</a></span>()</code>
<div class="block">返回步行路段的坐标点集合。</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表方法和解释">
<caption><span>参数类型为<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>的<a href="../../../../../../com/amap/api/services/route/package-summary.html">com.amap.api.services.route</a>中的方法</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">DistanceSearch.DistanceQuery.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/route/DistanceSearch.DistanceQuery.html#addOrigins-com.amap.api.services.core.LatLonPoint...-">addOrigins</a></span>(<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>...&nbsp;origins)</code>
<div class="block">设置距离测量起点数据集合,建议不超过s100个坐标
 和<a href="../../../../../../com/amap/api/services/route/DistanceSearch.DistanceQuery.html#setOrigins-java.util.List-"><code>DistanceSearch.DistanceQuery.setOrigins(List)</code></a> 不同，不需要自行创建集合对象
 如果已经调用了<a href="../../../../../../com/amap/api/services/route/DistanceSearch.DistanceQuery.html#setOrigins-java.util.List-"><code>DistanceSearch.DistanceQuery.setOrigins(List)</code></a>，再次调用此方法，之前的内容也会被保留</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">DistanceSearch.DistanceQuery.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/route/DistanceSearch.DistanceQuery.html#setDestination-com.amap.api.services.core.LatLonPoint-">setDestination</a></span>(<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&nbsp;destination)</code>
<div class="block">设置距离测量终点，和起点不同终点仅支持一个坐标</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表方法和解释">
<caption><span>类型变量类型为<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>的<a href="../../../../../../com/amap/api/services/route/package-summary.html">com.amap.api.services.route</a>中的方法参数</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">DistanceSearch.DistanceQuery.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/route/DistanceSearch.DistanceQuery.html#setOrigins-java.util.List-">setOrigins</a></span>(java.util.List&lt;<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&gt;&nbsp;origins)</code>
<div class="block">设置距离测量起点数据集合,建议不超过100个坐标</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">TaxiItemV2.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/route/TaxiItemV2.html#setPolyline-java.util.List-">setPolyline</a></span>(java.util.List&lt;<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&gt;&nbsp;mPolyline)</code>
<div class="block">线路点的集合</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表构造器和解释">
<caption><span>参数类型为<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>的<a href="../../../../../../com/amap/api/services/route/package-summary.html">com.amap.api.services.route</a>中的构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/route/RouteSearch.FromAndTo.html#FromAndTo-com.amap.api.services.core.LatLonPoint-com.amap.api.services.core.LatLonPoint-">FromAndTo</a></span>(<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&nbsp;from,
         <a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&nbsp;to)</code>
<div class="block">FromAndTo的构造函数。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/route/RouteSearchV2.FromAndTo.html#FromAndTo-com.amap.api.services.core.LatLonPoint-com.amap.api.services.core.LatLonPoint-">FromAndTo</a></span>(<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&nbsp;from,
         <a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&nbsp;to)</code>
<div class="block">FromAndTo的构造函数。</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表构造器和解释">
<caption><span>类型变量类型为<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>的<a href="../../../../../../com/amap/api/services/route/package-summary.html">com.amap.api.services.route</a>中的构造器参数</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/route/RouteSearch.DriveRouteQuery.html#DriveRouteQuery-com.amap.api.services.route.RouteSearch.FromAndTo-int-java.util.List-java.util.List-java.lang.String-">DriveRouteQuery</a></span>(<a href="../../../../../../com/amap/api/services/route/RouteSearch.FromAndTo.html" title="com.amap.api.services.route中的类">RouteSearch.FromAndTo</a>&nbsp;fromAndTo,
               int&nbsp;mode,
               java.util.List&lt;<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&gt;&nbsp;passedByPoints,
               java.util.List&lt;java.util.List&lt;<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&gt;&gt;&nbsp;avoidpolygons,
               java.lang.String&nbsp;avoidRoad)</code>
<div class="block">DriveRouteQuery构造函数。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/route/RouteSearch.DriveRouteQuery.html#DriveRouteQuery-com.amap.api.services.route.RouteSearch.FromAndTo-int-java.util.List-java.util.List-java.lang.String-">DriveRouteQuery</a></span>(<a href="../../../../../../com/amap/api/services/route/RouteSearch.FromAndTo.html" title="com.amap.api.services.route中的类">RouteSearch.FromAndTo</a>&nbsp;fromAndTo,
               int&nbsp;mode,
               java.util.List&lt;<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&gt;&nbsp;passedByPoints,
               java.util.List&lt;java.util.List&lt;<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&gt;&gt;&nbsp;avoidpolygons,
               java.lang.String&nbsp;avoidRoad)</code>
<div class="block">DriveRouteQuery构造函数。</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/route/RouteSearchV2.DriveRouteQuery.html#DriveRouteQuery-com.amap.api.services.route.RouteSearchV2.FromAndTo-com.amap.api.services.route.RouteSearchV2.DrivingStrategy-java.util.List-java.util.List-java.lang.String-">DriveRouteQuery</a></span>(<a href="../../../../../../com/amap/api/services/route/RouteSearchV2.FromAndTo.html" title="com.amap.api.services.route中的类">RouteSearchV2.FromAndTo</a>&nbsp;fromAndTo,
               <a href="../../../../../../com/amap/api/services/route/RouteSearchV2.DrivingStrategy.html" title="com.amap.api.services.route中的枚举">RouteSearchV2.DrivingStrategy</a>&nbsp;mode,
               java.util.List&lt;<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&gt;&nbsp;passedByPoints,
               java.util.List&lt;java.util.List&lt;<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&gt;&gt;&nbsp;avoidpolygons,
               java.lang.String&nbsp;avoidRoad)</code>
<div class="block">DriveRouteQuery构造函数。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/route/RouteSearchV2.DriveRouteQuery.html#DriveRouteQuery-com.amap.api.services.route.RouteSearchV2.FromAndTo-com.amap.api.services.route.RouteSearchV2.DrivingStrategy-java.util.List-java.util.List-java.lang.String-">DriveRouteQuery</a></span>(<a href="../../../../../../com/amap/api/services/route/RouteSearchV2.FromAndTo.html" title="com.amap.api.services.route中的类">RouteSearchV2.FromAndTo</a>&nbsp;fromAndTo,
               <a href="../../../../../../com/amap/api/services/route/RouteSearchV2.DrivingStrategy.html" title="com.amap.api.services.route中的枚举">RouteSearchV2.DrivingStrategy</a>&nbsp;mode,
               java.util.List&lt;<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&gt;&nbsp;passedByPoints,
               java.util.List&lt;java.util.List&lt;<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&gt;&gt;&nbsp;avoidpolygons,
               java.lang.String&nbsp;avoidRoad)</code>
<div class="block">DriveRouteQuery构造函数。</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/route/RouteSearch.TruckRouteQuery.html#TruckRouteQuery-com.amap.api.services.route.RouteSearch.FromAndTo-int-java.util.List-int-">TruckRouteQuery</a></span>(<a href="../../../../../../com/amap/api/services/route/RouteSearch.FromAndTo.html" title="com.amap.api.services.route中的类">RouteSearch.FromAndTo</a>&nbsp;fromAndTo,
               int&nbsp;mode,
               java.util.List&lt;<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&gt;&nbsp;passedByPoints,
               int&nbsp;truckSize)</code>
<div class="block">货车导航请求参数构造</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.amap.api.services.routepoisearch">
<!--   -->
</a>
<h3><a href="../../../../../../com/amap/api/services/routepoisearch/package-summary.html">com.amap.api.services.routepoisearch</a>中<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>的使用</h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表方法和解释">
<caption><span>返回<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>的<a href="../../../../../../com/amap/api/services/routepoisearch/package-summary.html">com.amap.api.services.routepoisearch</a>中的方法</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a></code></td>
<td class="colLast"><span class="typeNameLabel">RoutePOISearchQuery.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/routepoisearch/RoutePOISearchQuery.html#getFrom--">getFrom</a></span>()</code>
<div class="block">返回沿途搜索路径规划的起点坐标。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a></code></td>
<td class="colLast"><span class="typeNameLabel">RoutePOIItem.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/routepoisearch/RoutePOIItem.html#getPoint--">getPoint</a></span>()</code>
<div class="block">返回沿途POI的位置。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a></code></td>
<td class="colLast"><span class="typeNameLabel">RoutePOISearchQuery.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/routepoisearch/RoutePOISearchQuery.html#getTo--">getTo</a></span>()</code>
<div class="block">返回路径规划的终点坐标。</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表方法和解释">
<caption><span>返回变量类型为<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>的类型的<a href="../../../../../../com/amap/api/services/routepoisearch/package-summary.html">com.amap.api.services.routepoisearch</a>中的方法</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">RoutePOISearchQuery.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/routepoisearch/RoutePOISearchQuery.html#getPolylines--">getPolylines</a></span>()</code>
<div class="block">返回沿途搜索设置的坐标点集合。</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表构造器和解释">
<caption><span>参数类型为<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>的<a href="../../../../../../com/amap/api/services/routepoisearch/package-summary.html">com.amap.api.services.routepoisearch</a>中的构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/routepoisearch/RoutePOISearchQuery.html#RoutePOISearchQuery-com.amap.api.services.core.LatLonPoint-com.amap.api.services.core.LatLonPoint-int-com.amap.api.services.routepoisearch.RoutePOISearch.RoutePOISearchType-int-">RoutePOISearchQuery</a></span>(<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&nbsp;fromPoint,
                   <a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&nbsp;toPoint,
                   int&nbsp;mode,
                   <a href="../../../../../../com/amap/api/services/routepoisearch/RoutePOISearch.RoutePOISearchType.html" title="com.amap.api.services.routepoisearch中的枚举">RoutePOISearch.RoutePOISearchType</a>&nbsp;type,
                   int&nbsp;range)</code>
<div class="block">根据给定的参数来构造一个 RoutePOISearchQuery 的新对象。</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表构造器和解释">
<caption><span>类型变量类型为<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>的<a href="../../../../../../com/amap/api/services/routepoisearch/package-summary.html">com.amap.api.services.routepoisearch</a>中的构造器参数</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/routepoisearch/RoutePOISearchQuery.html#RoutePOISearchQuery-java.util.List-com.amap.api.services.routepoisearch.RoutePOISearch.RoutePOISearchType-int-">RoutePOISearchQuery</a></span>(java.util.List&lt;<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&gt;&nbsp;polyline,
                   <a href="../../../../../../com/amap/api/services/routepoisearch/RoutePOISearch.RoutePOISearchType.html" title="com.amap.api.services.routepoisearch中的枚举">RoutePOISearch.RoutePOISearchType</a>&nbsp;type,
                   int&nbsp;range)</code>
<div class="block">根据给定的参数来构造一个 RoutePOISearchQuery 的新对象。</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.amap.api.services.share">
<!--   -->
</a>
<h3><a href="../../../../../../com/amap/api/services/share/package-summary.html">com.amap.api.services.share</a>中<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>的使用</h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表方法和解释">
<caption><span>返回<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>的<a href="../../../../../../com/amap/api/services/share/package-summary.html">com.amap.api.services.share</a>中的方法</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a></code></td>
<td class="colLast"><span class="typeNameLabel">ShareSearch.ShareFromAndTo.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/share/ShareSearch.ShareFromAndTo.html#getFrom--">getFrom</a></span>()</code>
<div class="block">返回起点坐标。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a></code></td>
<td class="colLast"><span class="typeNameLabel">ShareSearch.ShareFromAndTo.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/share/ShareSearch.ShareFromAndTo.html#getTo--">getTo</a></span>()</code>
<div class="block">返回终点坐标。</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表构造器和解释">
<caption><span>参数类型为<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>的<a href="../../../../../../com/amap/api/services/share/package-summary.html">com.amap.api.services.share</a>中的构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/share/ShareSearch.ShareFromAndTo.html#ShareFromAndTo-com.amap.api.services.core.LatLonPoint-com.amap.api.services.core.LatLonPoint-">ShareFromAndTo</a></span>(<a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&nbsp;fromPoint,
              <a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a>&nbsp;endPoint)</code>
<div class="block">根据给定的参数来构造一个ShareFromAndTo的新对象。</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../../overview-summary.html">概览</a></li>
<li><a href="../package-summary.html">程序包</a></li>
<li><a href="../../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">类</a></li>
<li class="navBarCell1Rev">使用</li>
<li><a href="../../../../../../overview-tree.html">树</a></li>
<li><a href="../../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个</li>
<li>下一个</li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?com/amap/api/services/core/class-use/LatLonPoint.html" target="_top">框架</a></li>
<li><a href="LatLonPoint.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
