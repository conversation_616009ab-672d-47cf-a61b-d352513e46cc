<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>ServiceSettings</title>
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ServiceSettings";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":9,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":9,"i11":9};
var tabs = {65535:["t0","所有方法"],1:["t1","静态方法"],2:["t2","实例方法"],8:["t4","具体方法"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/ServiceSettings.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/core/SearchUtils.html" title="com.amap.api.services.core中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/amap/api/services/core/SuggestionCity.html" title="com.amap.api.services.core中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/core/ServiceSettings.html" target="_top">框架</a></li>
<li><a href="ServiceSettings.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li><a href="#field.summary">字段</a>&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li><a href="#field.detail">字段</a>&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.amap.api.services.core</div>
<h2 title="类 ServiceSettings" class="title">类 ServiceSettings</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.amap.api.services.core.ServiceSettings</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">ServiceSettings</span>
extends java.lang.Object</pre>
<div class="block">设置API接口访问协议的单例。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.3.1</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>字段概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="字段概要表, 列表字段和解释">
<caption><span>字段</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">字段和说明</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/ServiceSettings.html#CHINESE">CHINESE</a></span></code>
<div class="block">中文</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/ServiceSettings.html#ENGLISH">ENGLISH</a></span></code>
<div class="block">英文</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/ServiceSettings.html#HTTP">HTTP</a></span></code>
<div class="block">Http协议。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/ServiceSettings.html#HTTPS">HTTPS</a></span></code>
<div class="block">Https协议。</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>所有方法</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">静态方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">实例方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">具体方法</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/ServiceSettings.html#getConnectionTimeOut--">getConnectionTimeOut</a></span>()</code>
<div class="block">返回搜索服务建立连接超时限制。</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static <a href="../../../../../com/amap/api/services/core/ServiceSettings.html" title="com.amap.api.services.core中的类">ServiceSettings</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/ServiceSettings.html#getInstance--">getInstance</a></span>()</code>
<div class="block">初始化ServiceSettings的单例对象。</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/ServiceSettings.html#getLanguage--">getLanguage</a></span>()</code>
<div class="block">搜索、地理/逆地理编码、输入提示的返回结果语言类型，中英文。</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/ServiceSettings.html#getProtocol--">getProtocol</a></span>()</code>
<div class="block">返回访问使用的协议类别</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/ServiceSettings.html#getSoTimeOut--">getSoTimeOut</a></span>()</code>
<div class="block">读取返回结果超时限制</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/ServiceSettings.html#setApiKey-java.lang.String-">setApiKey</a></span>(java.lang.String&nbsp;apiKey)</code>
<div class="block">动态设置apiKey。</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/ServiceSettings.html#setConnectionTimeOut-int-">setConnectionTimeOut</a></span>(int&nbsp;connectionTimeout)</code>
<div class="block">设置搜索服务建立连接超时。</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/ServiceSettings.html#setLanguage-java.lang.String-">setLanguage</a></span>(java.lang.String&nbsp;language)</code>
<div class="block">设置搜索、地理/逆地理编码、输入提示中英文切换的接口。</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/ServiceSettings.html#setProtocol-int-">setProtocol</a></span>(int&nbsp;protocol)</code>
<div class="block">设置访问使用的协议类别。</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/ServiceSettings.html#setSoTimeOut-int-">setSoTimeOut</a></span>(int&nbsp;soTimeOut)</code>
<div class="block">设置搜索读取返回结果超时。</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/ServiceSettings.html#updatePrivacyAgree-Context-boolean-">updatePrivacyAgree</a></span>(Context&nbsp;context,
                  boolean&nbsp;isAgree)</code>
<div class="block">更新同意隐私状态,需要在初始化地图之前完成</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/ServiceSettings.html#updatePrivacyShow-Context-boolean-boolean-">updatePrivacyShow</a></span>(Context&nbsp;context,
                 boolean&nbsp;isContains,
                 boolean&nbsp;isShow)</code>
<div class="block">更新隐私合规状态,需要在初始化地图之前完成</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>字段详细资料</h3>
<a name="ENGLISH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENGLISH</h4>
<pre>public static final&nbsp;java.lang.String ENGLISH</pre>
<div class="block">英文</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.5.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/ServiceSettings.html#setLanguage-java.lang.String-"><code>ServiceSettings.setLanguage(String)</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.ServiceSettings.ENGLISH">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="CHINESE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CHINESE</h4>
<pre>public static final&nbsp;java.lang.String CHINESE</pre>
<div class="block">中文</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.5.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/ServiceSettings.html#setLanguage-java.lang.String-"><code>ServiceSettings.setLanguage(String)</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.ServiceSettings.CHINESE">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="HTTP">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HTTP</h4>
<pre>public static final&nbsp;int HTTP</pre>
<div class="block">Http协议。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.3.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.core.ServiceSettings.HTTP">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="HTTPS">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>HTTPS</h4>
<pre>public static final&nbsp;int HTTPS</pre>
<div class="block">Https协议。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.3.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.core.ServiceSettings.HTTPS">常量字段值</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="getConnectionTimeOut--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getConnectionTimeOut</h4>
<pre>public&nbsp;int&nbsp;getConnectionTimeOut()</pre>
<div class="block">返回搜索服务建立连接超时限制。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>毫秒级参数</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.5.0</dd>
</dl>
</li>
</ul>
<a name="getSoTimeOut--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSoTimeOut</h4>
<pre>public&nbsp;int&nbsp;getSoTimeOut()</pre>
<div class="block">读取返回结果超时限制</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>毫秒级参数</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.5.0</dd>
</dl>
</li>
</ul>
<a name="setConnectionTimeOut-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setConnectionTimeOut</h4>
<pre>public&nbsp;void&nbsp;setConnectionTimeOut(int&nbsp;connectionTimeout)</pre>
<div class="block">设置搜索服务建立连接超时。默认为20000，阈值下限为5000，上限为30000.</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>connectionTimeout</code> - 毫秒级参数。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.5.0</dd>
</dl>
</li>
</ul>
<a name="setSoTimeOut-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSoTimeOut</h4>
<pre>public&nbsp;void&nbsp;setSoTimeOut(int&nbsp;soTimeOut)</pre>
<div class="block">设置搜索读取返回结果超时。默认为20000，阈值下限为5000，上限为30000.</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>soTimeOut</code> - 毫秒级参数。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.5.0</dd>
</dl>
</li>
</ul>
<a name="getInstance--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getInstance</h4>
<pre>public static&nbsp;<a href="../../../../../com/amap/api/services/core/ServiceSettings.html" title="com.amap.api.services.core中的类">ServiceSettings</a>&nbsp;getInstance()</pre>
<div class="block">初始化ServiceSettings的单例对象。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>ServiceSettings单例对象。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.3.1</dd>
</dl>
</li>
</ul>
<a name="setLanguage-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLanguage</h4>
<pre>public&nbsp;void&nbsp;setLanguage(java.lang.String&nbsp;language)</pre>
<div class="block">设置搜索、地理/逆地理编码、输入提示中英文切换的接口。
 国内仅支持中文和英文

 如果在海外使用，还可以支持更多语言，7.4.0 开始支持<br>
 语言代码   语言_EN   语言<br>
 da     Danish  丹麦语<br>
 de     German  德语<br>
 en     English 英语<br>
 es     Spanish 西班牙语<br>
 es-ES  Spanish (Spain) 西班牙语（西班牙）<br>
 fi     Finnish 芬兰语<br>
 fr     French  法语<br>
 id     Indonesian      印尼语<br>
 it     Italian 意大利语<br>
 ko     Korean  韩语<br>
 my     Burmese 缅甸语<br>
 nl     Dutch   荷兰语<br>
 no     Norwegian (Bokmål)      挪威语<br>
 pt-BR  Portuguese (Brazil)     葡萄牙语（巴西）<br>
 pt-PT  Portuguese (Portugal)   葡萄牙语（葡萄牙）<br>
 ru     Russian 俄语<br>
 sv     Swedish 瑞典语<br>
 tr     Turkish 土耳其语<br>
 vi     Vietnamese      越南语<br></div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>language</code> - 语言类型 <a href="../../../../../com/amap/api/services/core/ServiceSettings.html#ENGLISH"><code>ServiceSettings.ENGLISH</code></a> 或者 <a href="../../../../../com/amap/api/services/core/ServiceSettings.html#CHINESE"><code>ServiceSettings.CHINESE</code></a>。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.5.0</dd>
</dl>
</li>
</ul>
<a name="setProtocol-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setProtocol</h4>
<pre>public&nbsp;void&nbsp;setProtocol(int&nbsp;protocol)</pre>
<div class="block">设置访问使用的协议类别。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>protocol</code> - 包含Http和Https两种协议。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.3.1</dd>
</dl>
</li>
</ul>
<a name="getLanguage--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLanguage</h4>
<pre>public&nbsp;java.lang.String&nbsp;getLanguage()</pre>
<div class="block">搜索、地理/逆地理编码、输入提示的返回结果语言类型，中英文。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>语言类型</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.5.0</dd>
</dl>
</li>
</ul>
<a name="getProtocol--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProtocol</h4>
<pre>public&nbsp;int&nbsp;getProtocol()</pre>
<div class="block">返回访问使用的协议类别</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>协议类别，包含Http和Https两种协议。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.3.1</dd>
</dl>
</li>
</ul>
<a name="setApiKey-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setApiKey</h4>
<pre>public&nbsp;void&nbsp;setApiKey(java.lang.String&nbsp;apiKey)</pre>
<div class="block">动态设置apiKey。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>apiKey</code> - 在高德官网上申请的apiKey。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.4.0</dd>
</dl>
</li>
</ul>
<a name="updatePrivacyShow-Context-boolean-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>updatePrivacyShow</h4>
<pre>public static&nbsp;void&nbsp;updatePrivacyShow(Context&nbsp;context,
                                     boolean&nbsp;isContains,
                                     boolean&nbsp;isShow)</pre>
<div class="block">更新隐私合规状态,需要在初始化地图之前完成</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>context:</code> - 上下文</dd>
<dd><code>isContains:</code> - 隐私权政策是否包含高德开平隐私权政策  true是包含</dd>
<dd><code>isShow: 隐私权政策是否弹窗展示告知用户 true是展示</code> - </dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>8.1.0</dd>
</dl>
</li>
</ul>
<a name="updatePrivacyAgree-Context-boolean-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>updatePrivacyAgree</h4>
<pre>public static&nbsp;void&nbsp;updatePrivacyAgree(Context&nbsp;context,
                                      boolean&nbsp;isAgree)</pre>
<div class="block">更新同意隐私状态,需要在初始化地图之前完成</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>context:</code> - 上下文</dd>
<dd><code>isAgree:</code> - 隐私权政策是否取得用户同意  true是用户同意</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>8.1.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/ServiceSettings.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/services/core/SearchUtils.html" title="com.amap.api.services.core中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/amap/api/services/core/SuggestionCity.html" title="com.amap.api.services.core中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/core/ServiceSettings.html" target="_top">框架</a></li>
<li><a href="ServiceSettings.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li><a href="#field.summary">字段</a>&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li><a href="#field.detail">字段</a>&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
