<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>类 com.amap.api.services.core.AMapException的使用</title>
<link rel="stylesheet" type="text/css" href="../../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="\u7C7B com.amap.api.services.core.AMapException\u7684\u4F7F\u7528";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../../overview-summary.html">概览</a></li>
<li><a href="../package-summary.html">程序包</a></li>
<li><a href="../../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">类</a></li>
<li class="navBarCell1Rev">使用</li>
<li><a href="../../../../../../overview-tree.html">树</a></li>
<li><a href="../../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个</li>
<li>下一个</li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?com/amap/api/services/core/class-use/AMapException.html" target="_top">框架</a></li>
<li><a href="AMapException.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h2 title="类的使用 com.amap.api.services.core.AMapException" class="title">类的使用<br>com.amap.api.services.core.AMapException</h2>
</div>
<div class="classUseContainer">
<ul class="blockList">
<li class="blockList">
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表程序包和解释">
<caption><span>使用<a href="../../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a>的程序包</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">程序包</th>
<th class="colLast" scope="col">说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="#com.amap.api.services.busline">com.amap.api.services.busline</a></td>
<td class="colLast">
<div class="block">
公交线路和公交站点查询包。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.amap.api.services.cloud">com.amap.api.services.cloud</a></td>
<td class="colLast">
<div class="block">
云检索包，包含了自有数据搜索查询的功能。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#com.amap.api.services.district">com.amap.api.services.district</a></td>
<td class="colLast">
<div class="block">
行政区划查询包，查询某个行政级别的详细信息。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.amap.api.services.geocoder">com.amap.api.services.geocoder</a></td>
<td class="colLast">
<div class="block">
地理编码包，用以实现通过地理编码进行查询的功能。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#com.amap.api.services.help">com.amap.api.services.help</a></td>
<td class="colLast">
<div class="block">
服务帮助包，用来实现查询过程中提供的提示帮助。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.amap.api.services.nearby">com.amap.api.services.nearby</a></td>
<td class="colLast">
<div class="block">
附近派单功能包，包含上传位置信息、检索位置信息等 自7.4.0起不再支持附件功能
</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#com.amap.api.services.poisearch">com.amap.api.services.poisearch</a></td>
<td class="colLast">
<div class="block">
POI查询包，包含了兴趣点的详细信息。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.amap.api.services.route">com.amap.api.services.route</a></td>
<td class="colLast">
<div class="block">
路径查询包，包含了在公交、自驾或步行路线规划中的详细信息。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#com.amap.api.services.routepoisearch">com.amap.api.services.routepoisearch</a></td>
<td class="colLast">
<div class="block">
沿途POI搜索包，搜索驾车沿途的POI信息。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.amap.api.services.share">com.amap.api.services.share</a></td>
<td class="colLast">
<div class="block">
短串分享包，包含了位置/POI/路径规划/导航的分享功能。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#com.amap.api.services.weather">com.amap.api.services.weather</a></td>
<td class="colLast">
<div class="block">
天气查询包，包含了实况天气和预报天气的详细信息。</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<ul class="blockList">
<li class="blockList"><a name="com.amap.api.services.busline">
<!--   -->
</a>
<h3><a href="../../../../../../com/amap/api/services/busline/package-summary.html">com.amap.api.services.busline</a>中<a href="../../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a>的使用</h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表方法和解释">
<caption><span>抛出<a href="../../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a>的<a href="../../../../../../com/amap/api/services/busline/package-summary.html">com.amap.api.services.busline</a>中的方法</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/services/busline/BusLineResult.html" title="com.amap.api.services.busline中的类">BusLineResult</a></code></td>
<td class="colLast"><span class="typeNameLabel">BusLineSearch.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/busline/BusLineSearch.html#searchBusLine--">searchBusLine</a></span>()</code>
<div class="block">搜索公交线路。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/services/busline/BusStationResult.html" title="com.amap.api.services.busline中的类">BusStationResult</a></code></td>
<td class="colLast"><span class="typeNameLabel">BusStationSearch.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/busline/BusStationSearch.html#searchBusStation--">searchBusStation</a></span>()</code>
<div class="block">搜索公交站点。</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表构造器和解释">
<caption><span>抛出<a href="../../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a>的<a href="../../../../../../com/amap/api/services/busline/package-summary.html">com.amap.api.services.busline</a>中的构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/busline/BusLineSearch.html#BusLineSearch-Context-com.amap.api.services.busline.BusLineQuery-">BusLineSearch</a></span>(Context&nbsp;act,
             <a href="../../../../../../com/amap/api/services/busline/BusLineQuery.html" title="com.amap.api.services.busline中的类">BusLineQuery</a>&nbsp;query)</code>
<div class="block">BusLineSearch构造函数。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/busline/BusStationSearch.html#BusStationSearch-Context-com.amap.api.services.busline.BusStationQuery-">BusStationSearch</a></span>(Context&nbsp;act,
                <a href="../../../../../../com/amap/api/services/busline/BusStationQuery.html" title="com.amap.api.services.busline中的类">BusStationQuery</a>&nbsp;query)</code>
<div class="block">BusStationSearch构造函数。</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.amap.api.services.cloud">
<!--   -->
</a>
<h3><a href="../../../../../../com/amap/api/services/cloud/package-summary.html">com.amap.api.services.cloud</a>中<a href="../../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a>的使用</h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表构造器和解释">
<caption><span>抛出<a href="../../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a>的<a href="../../../../../../com/amap/api/services/cloud/package-summary.html">com.amap.api.services.cloud</a>中的构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/cloud/CloudSearch.html#CloudSearch-Context-">CloudSearch</a></span>(Context&nbsp;act)</code>
<div class="block">根据给定的参数构造一个 CloudSearch 的新对象。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/cloud/CloudSearch.Query.html#Query-java.lang.String-java.lang.String-com.amap.api.services.cloud.CloudSearch.SearchBound-">Query</a></span>(java.lang.String&nbsp;tableid,
     java.lang.String&nbsp;query,
     <a href="../../../../../../com/amap/api/services/cloud/CloudSearch.SearchBound.html" title="com.amap.api.services.cloud中的类">CloudSearch.SearchBound</a>&nbsp;bound)</code>
<div class="block">根据给定的参数构造一个 CloudSearch.Query 的新对象。</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.amap.api.services.district">
<!--   -->
</a>
<h3><a href="../../../../../../com/amap/api/services/district/package-summary.html">com.amap.api.services.district</a>中<a href="../../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a>的使用</h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表方法和解释">
<caption><span>返回<a href="../../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a>的<a href="../../../../../../com/amap/api/services/district/package-summary.html">com.amap.api.services.district</a>中的方法</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></code></td>
<td class="colLast"><span class="typeNameLabel">DistrictResult.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/district/DistrictResult.html#getAMapException--">getAMapException</a></span>()</code>
<div class="block">返回服务异常对象。</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表方法和解释">
<caption><span>抛出<a href="../../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a>的<a href="../../../../../../com/amap/api/services/district/package-summary.html">com.amap.api.services.district</a>中的方法</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/services/district/DistrictResult.html" title="com.amap.api.services.district中的类">DistrictResult</a></code></td>
<td class="colLast"><span class="typeNameLabel">DistrictSearch.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/district/DistrictSearch.html#searchDistrict--">searchDistrict</a></span>()</code>
<div class="block">查询行政区的同步接口。</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表构造器和解释">
<caption><span>抛出<a href="../../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a>的<a href="../../../../../../com/amap/api/services/district/package-summary.html">com.amap.api.services.district</a>中的构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/district/DistrictSearch.html#DistrictSearch-Context-">DistrictSearch</a></span>(Context&nbsp;context)</code>
<div class="block">根据给定的参数构造一个 DistrictSearch 的新对象。</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.amap.api.services.geocoder">
<!--   -->
</a>
<h3><a href="../../../../../../com/amap/api/services/geocoder/package-summary.html">com.amap.api.services.geocoder</a>中<a href="../../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a>的使用</h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表方法和解释">
<caption><span>抛出<a href="../../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a>的<a href="../../../../../../com/amap/api/services/geocoder/package-summary.html">com.amap.api.services.geocoder</a>中的方法</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/services/geocoder/RegeocodeAddress.html" title="com.amap.api.services.geocoder中的类">RegeocodeAddress</a></code></td>
<td class="colLast"><span class="typeNameLabel">GeocodeSearch.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/geocoder/GeocodeSearch.html#getFromLocation-com.amap.api.services.geocoder.RegeocodeQuery-">getFromLocation</a></span>(<a href="../../../../../../com/amap/api/services/geocoder/RegeocodeQuery.html" title="com.amap.api.services.geocoder中的类">RegeocodeQuery</a>&nbsp;regeocodeQuery)</code>
<div class="block">根据给定的经纬度和最大结果数返回逆地理编码的结果列表。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../../../com/amap/api/services/geocoder/GeocodeAddress.html" title="com.amap.api.services.geocoder中的类">GeocodeAddress</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">GeocodeSearch.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/geocoder/GeocodeSearch.html#getFromLocationName-com.amap.api.services.geocoder.GeocodeQuery-">getFromLocationName</a></span>(<a href="../../../../../../com/amap/api/services/geocoder/GeocodeQuery.html" title="com.amap.api.services.geocoder中的类">GeocodeQuery</a>&nbsp;geocodeQuery)</code>
<div class="block">根据给定的地理名称和查询城市，返回地理编码的结果列表。</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表构造器和解释">
<caption><span>抛出<a href="../../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a>的<a href="../../../../../../com/amap/api/services/geocoder/package-summary.html">com.amap.api.services.geocoder</a>中的构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/geocoder/GeocodeSearch.html#GeocodeSearch-Context-">GeocodeSearch</a></span>(Context&nbsp;context)</code>
<div class="block">根据给定的参数来构造一个GeocodeSearch 新对象。</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.amap.api.services.help">
<!--   -->
</a>
<h3><a href="../../../../../../com/amap/api/services/help/package-summary.html">com.amap.api.services.help</a>中<a href="../../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a>的使用</h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表方法和解释">
<caption><span>抛出<a href="../../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a>的<a href="../../../../../../com/amap/api/services/help/package-summary.html">com.amap.api.services.help</a>中的方法</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../../../com/amap/api/services/help/Tip.html" title="com.amap.api.services.help中的类">Tip</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">Inputtips.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/help/Inputtips.html#requestInputtips--">requestInputtips</a></span>()</code>
<div class="block">查询输入提示的同步接口。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">Inputtips.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/help/Inputtips.html#requestInputtips-java.lang.String-java.lang.String-">requestInputtips</a></span>(java.lang.String&nbsp;keyword,
                java.lang.String&nbsp;city)</code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;
<div class="block"><span class="deprecationComment">请参考 <a href="../../../../../../com/amap/api/services/help/Inputtips.html#requestInputtips--"><code>Inputtips.requestInputtips()</code></a></span></div>
</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">Inputtips.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/help/Inputtips.html#requestInputtips-java.lang.String-java.lang.String-java.lang.String-">requestInputtips</a></span>(java.lang.String&nbsp;keyword,
                java.lang.String&nbsp;city,
                java.lang.String&nbsp;type)</code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;
<div class="block"><span class="deprecationComment">请参考 <a href="../../../../../../com/amap/api/services/help/Inputtips.html#requestInputtips--"><code>Inputtips.requestInputtips()</code></a></span></div>
</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表构造器和解释">
<caption><span>抛出<a href="../../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a>的<a href="../../../../../../com/amap/api/services/help/package-summary.html">com.amap.api.services.help</a>中的构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/help/Inputtips.html#Inputtips-Context-com.amap.api.services.help.Inputtips.InputtipsListener-">Inputtips</a></span>(Context&nbsp;context,
         <a href="../../../../../../com/amap/api/services/help/Inputtips.InputtipsListener.html" title="com.amap.api.services.help中的接口">Inputtips.InputtipsListener</a>&nbsp;listener)</code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;&nbsp;</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.amap.api.services.nearby">
<!--   -->
</a>
<h3><a href="../../../../../../com/amap/api/services/nearby/package-summary.html">com.amap.api.services.nearby</a>中<a href="../../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a>的使用</h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表方法和解释">
<caption><span>抛出<a href="../../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a>的<a href="../../../../../../com/amap/api/services/nearby/package-summary.html">com.amap.api.services.nearby</a>中的方法</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../../../com/amap/api/services/nearby/NearbySearch.html" title="com.amap.api.services.nearby中的类">NearbySearch</a></code></td>
<td class="colLast"><span class="typeNameLabel">NearbySearch.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/nearby/NearbySearch.html#getInstance-Context-">getInstance</a></span>(Context&nbsp;context)</code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;</div>
<div class="block">获取NearbySearch单例对象。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/services/nearby/NearbySearchResult.html" title="com.amap.api.services.nearby中的类">NearbySearchResult</a></code></td>
<td class="colLast"><span class="typeNameLabel">NearbySearch.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/nearby/NearbySearch.html#searchNearbyInfo-com.amap.api.services.nearby.NearbySearch.NearbyQuery-">searchNearbyInfo</a></span>(<a href="../../../../../../com/amap/api/services/nearby/NearbySearch.NearbyQuery.html" title="com.amap.api.services.nearby中的类">NearbySearch.NearbyQuery</a>&nbsp;query)</code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;</div>
<div class="block">检索附近的用户信息，同步方法。</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.amap.api.services.poisearch">
<!--   -->
</a>
<h3><a href="../../../../../../com/amap/api/services/poisearch/package-summary.html">com.amap.api.services.poisearch</a>中<a href="../../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a>的使用</h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表方法和解释">
<caption><span>抛出<a href="../../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a>的<a href="../../../../../../com/amap/api/services/poisearch/package-summary.html">com.amap.api.services.poisearch</a>中的方法</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/services/poisearch/PoiResult.html" title="com.amap.api.services.poisearch中的类">PoiResult</a></code></td>
<td class="colLast"><span class="typeNameLabel">PoiSearch.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/poisearch/PoiSearch.html#searchPOI--">searchPOI</a></span>()</code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;</div>
<div class="block">查询POI。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/services/poisearch/PoiResultV2.html" title="com.amap.api.services.poisearch中的类">PoiResultV2</a></code></td>
<td class="colLast"><span class="typeNameLabel">PoiSearchV2.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/poisearch/PoiSearchV2.html#searchPOI--">searchPOI</a></span>()</code>
<div class="block">查询POI。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/services/core/PoiItem.html" title="com.amap.api.services.core中的类">PoiItem</a></code></td>
<td class="colLast"><span class="typeNameLabel">PoiSearch.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/poisearch/PoiSearch.html#searchPOIId-java.lang.String-">searchPOIId</a></span>(java.lang.String&nbsp;poiID)</code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;</div>
<div class="block">已知poiid信息（点击地图底图），搜索POI的详细信息，同步</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>PoiItemV2</code></td>
<td class="colLast"><span class="typeNameLabel">PoiSearchV2.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/poisearch/PoiSearchV2.html#searchPOIId-java.lang.String-">searchPOIId</a></span>(java.lang.String&nbsp;poiID)</code>
<div class="block">已知poiid信息（点击地图底图），搜索POI的详细信息，同步</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表构造器和解释">
<caption><span>抛出<a href="../../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a>的<a href="../../../../../../com/amap/api/services/poisearch/package-summary.html">com.amap.api.services.poisearch</a>中的构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/poisearch/PoiSearch.html#PoiSearch-Context-com.amap.api.services.poisearch.PoiSearch.Query-">PoiSearch</a></span>(Context&nbsp;context,
         <a href="../../../../../../com/amap/api/services/poisearch/PoiSearch.Query.html" title="com.amap.api.services.poisearch中的类">PoiSearch.Query</a>&nbsp;query)</code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;</div>
<div class="block">根据给定的参数构造一个PoiSearch 的新对象。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/poisearch/PoiSearchV2.html#PoiSearchV2-Context-com.amap.api.services.poisearch.PoiSearchV2.Query-">PoiSearchV2</a></span>(Context&nbsp;context,
           <a href="../../../../../../com/amap/api/services/poisearch/PoiSearchV2.Query.html" title="com.amap.api.services.poisearch中的类">PoiSearchV2.Query</a>&nbsp;query)</code>
<div class="block">根据给定的参数构造一个PoiSearch 的新对象。</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.amap.api.services.route">
<!--   -->
</a>
<h3><a href="../../../../../../com/amap/api/services/route/package-summary.html">com.amap.api.services.route</a>中<a href="../../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a>的使用</h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表方法和解释">
<caption><span>抛出<a href="../../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a>的<a href="../../../../../../com/amap/api/services/route/package-summary.html">com.amap.api.services.route</a>中的方法</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/services/route/BusRouteResult.html" title="com.amap.api.services.route中的类">BusRouteResult</a></code></td>
<td class="colLast"><span class="typeNameLabel">RouteSearch.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/route/RouteSearch.html#calculateBusRoute-com.amap.api.services.route.RouteSearch.BusRouteQuery-">calculateBusRoute</a></span>(<a href="../../../../../../com/amap/api/services/route/RouteSearch.BusRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearch.BusRouteQuery</a>&nbsp;busQuery)</code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;
<div class="block"><span class="deprecationComment">自9.4.0废弃 <a href="../../../../../../com/amap/api/services/route/RouteSearchV2.html#calculateBusRoute-com.amap.api.services.route.RouteSearchV2.BusRouteQuery-"><code>RouteSearchV2.calculateBusRoute(com.amap.api.services.route.RouteSearchV2.BusRouteQuery)</code></a></span></div>
</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/services/route/BusRouteResultV2.html" title="com.amap.api.services.route中的类">BusRouteResultV2</a></code></td>
<td class="colLast"><span class="typeNameLabel">RouteSearchV2.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/route/RouteSearchV2.html#calculateBusRoute-com.amap.api.services.route.RouteSearchV2.BusRouteQuery-">calculateBusRoute</a></span>(<a href="../../../../../../com/amap/api/services/route/RouteSearchV2.BusRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearchV2.BusRouteQuery</a>&nbsp;busQuery)</code>
<div class="block">根据指定的参数来计算公交路径。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/services/route/DriveRoutePlanResult.html" title="com.amap.api.services.route中的类">DriveRoutePlanResult</a></code></td>
<td class="colLast"><span class="typeNameLabel">RouteSearch.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/route/RouteSearch.html#calculateDrivePlan-com.amap.api.services.route.RouteSearch.DrivePlanQuery-">calculateDrivePlan</a></span>(<a href="../../../../../../com/amap/api/services/route/RouteSearch.DrivePlanQuery.html" title="com.amap.api.services.route中的类">RouteSearch.DrivePlanQuery</a>&nbsp;driveQuery)</code>
<div class="block">根据指定的参数来计算驾车路径。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/services/route/DriveRouteResult.html" title="com.amap.api.services.route中的类">DriveRouteResult</a></code></td>
<td class="colLast"><span class="typeNameLabel">RouteSearch.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/route/RouteSearch.html#calculateDriveRoute-com.amap.api.services.route.RouteSearch.DriveRouteQuery-">calculateDriveRoute</a></span>(<a href="../../../../../../com/amap/api/services/route/RouteSearch.DriveRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearch.DriveRouteQuery</a>&nbsp;driveQuery)</code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;
<div class="block"><span class="deprecationComment">自9.4.0废弃 <a href="../../../../../../com/amap/api/services/route/RouteSearchV2.html#calculateDriveRoute-com.amap.api.services.route.RouteSearchV2.DriveRouteQuery-"><code>RouteSearchV2.calculateDriveRoute(com.amap.api.services.route.RouteSearchV2.DriveRouteQuery)</code></a></span></div>
</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/services/route/DriveRouteResultV2.html" title="com.amap.api.services.route中的类">DriveRouteResultV2</a></code></td>
<td class="colLast"><span class="typeNameLabel">RouteSearchV2.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/route/RouteSearchV2.html#calculateDriveRoute-com.amap.api.services.route.RouteSearchV2.DriveRouteQuery-">calculateDriveRoute</a></span>(<a href="../../../../../../com/amap/api/services/route/RouteSearchV2.DriveRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearchV2.DriveRouteQuery</a>&nbsp;driveQuery)</code>
<div class="block">根据指定的参数来计算驾车路径。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/services/route/RideRouteResult.html" title="com.amap.api.services.route中的类">RideRouteResult</a></code></td>
<td class="colLast"><span class="typeNameLabel">RouteSearch.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/route/RouteSearch.html#calculateRideRoute-com.amap.api.services.route.RouteSearch.RideRouteQuery-">calculateRideRoute</a></span>(<a href="../../../../../../com/amap/api/services/route/RouteSearch.RideRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearch.RideRouteQuery</a>&nbsp;rideQuery)</code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;
<div class="block"><span class="deprecationComment">自9.4.0废弃 <a href="../../../../../../com/amap/api/services/route/RouteSearchV2.html#calculateRideRoute-com.amap.api.services.route.RouteSearchV2.RideRouteQuery-"><code>RouteSearchV2.calculateRideRoute(com.amap.api.services.route.RouteSearchV2.RideRouteQuery)</code></a></span></div>
</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/services/route/RideRouteResultV2.html" title="com.amap.api.services.route中的类">RideRouteResultV2</a></code></td>
<td class="colLast"><span class="typeNameLabel">RouteSearchV2.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/route/RouteSearchV2.html#calculateRideRoute-com.amap.api.services.route.RouteSearchV2.RideRouteQuery-">calculateRideRoute</a></span>(<a href="../../../../../../com/amap/api/services/route/RouteSearchV2.RideRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearchV2.RideRouteQuery</a>&nbsp;rideQuery)</code>
<div class="block">根据指定的参数来计算骑行路径，同步接口。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/services/route/DistanceResult.html" title="com.amap.api.services.route中的类">DistanceResult</a></code></td>
<td class="colLast"><span class="typeNameLabel">DistanceSearch.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/route/DistanceSearch.html#calculateRouteDistance-com.amap.api.services.route.DistanceSearch.DistanceQuery-">calculateRouteDistance</a></span>(<a href="../../../../../../com/amap/api/services/route/DistanceSearch.DistanceQuery.html" title="com.amap.api.services.route中的类">DistanceSearch.DistanceQuery</a>&nbsp;query)</code>
<div class="block">测量距离请求接口，调用后会发起距离测量请求。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/services/route/TruckRouteRestult.html" title="com.amap.api.services.route中的类">TruckRouteRestult</a></code></td>
<td class="colLast"><span class="typeNameLabel">RouteSearch.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/route/RouteSearch.html#calculateTruckRoute-com.amap.api.services.route.RouteSearch.TruckRouteQuery-">calculateTruckRoute</a></span>(<a href="../../../../../../com/amap/api/services/route/RouteSearch.TruckRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearch.TruckRouteQuery</a>&nbsp;truckQuery)</code>
<div class="block">根据指定的参数来计算货车路径。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/services/route/WalkRouteResult.html" title="com.amap.api.services.route中的类">WalkRouteResult</a></code></td>
<td class="colLast"><span class="typeNameLabel">RouteSearch.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/route/RouteSearch.html#calculateWalkRoute-com.amap.api.services.route.RouteSearch.WalkRouteQuery-">calculateWalkRoute</a></span>(<a href="../../../../../../com/amap/api/services/route/RouteSearch.WalkRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearch.WalkRouteQuery</a>&nbsp;walkQuery)</code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;
<div class="block"><span class="deprecationComment">自9.4.0废弃 <a href="../../../../../../com/amap/api/services/route/RouteSearchV2.html#calculateWalkRoute-com.amap.api.services.route.RouteSearchV2.WalkRouteQuery-"><code>RouteSearchV2.calculateWalkRoute(com.amap.api.services.route.RouteSearchV2.WalkRouteQuery)</code></a></span></div>
</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/services/route/WalkRouteResultV2.html" title="com.amap.api.services.route中的类">WalkRouteResultV2</a></code></td>
<td class="colLast"><span class="typeNameLabel">RouteSearchV2.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/route/RouteSearchV2.html#calculateWalkRoute-com.amap.api.services.route.RouteSearchV2.WalkRouteQuery-">calculateWalkRoute</a></span>(<a href="../../../../../../com/amap/api/services/route/RouteSearchV2.WalkRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearchV2.WalkRouteQuery</a>&nbsp;walkQuery)</code>
<div class="block">根据指定的参数来计算步行路径。</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表构造器和解释">
<caption><span>抛出<a href="../../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a>的<a href="../../../../../../com/amap/api/services/route/package-summary.html">com.amap.api.services.route</a>中的构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/route/DistanceSearch.html#DistanceSearch-Context-">DistanceSearch</a></span>(Context&nbsp;context)</code>
<div class="block">距离测量搜索的构造函数</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/route/RouteSearch.html#RouteSearch-Context-">RouteSearch</a></span>(Context&nbsp;context)</code>
<div class="block">根据给定的参数来构造一个RouteSearch的新对象。</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/route/RouteSearchV2.html#RouteSearchV2-Context-">RouteSearchV2</a></span>(Context&nbsp;context)</code>
<div class="block">根据给定的参数来构造一个RouteSearch的新对象。</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.amap.api.services.routepoisearch">
<!--   -->
</a>
<h3><a href="../../../../../../com/amap/api/services/routepoisearch/package-summary.html">com.amap.api.services.routepoisearch</a>中<a href="../../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a>的使用</h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表方法和解释">
<caption><span>抛出<a href="../../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a>的<a href="../../../../../../com/amap/api/services/routepoisearch/package-summary.html">com.amap.api.services.routepoisearch</a>中的方法</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/services/routepoisearch/RoutePOISearchResult.html" title="com.amap.api.services.routepoisearch中的类">RoutePOISearchResult</a></code></td>
<td class="colLast"><span class="typeNameLabel">RoutePOISearch.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/routepoisearch/RoutePOISearch.html#searchRoutePOI--">searchRoutePOI</a></span>()</code>
<div class="block">沿途搜索同步查询接口。</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表构造器和解释">
<caption><span>抛出<a href="../../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a>的<a href="../../../../../../com/amap/api/services/routepoisearch/package-summary.html">com.amap.api.services.routepoisearch</a>中的构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/routepoisearch/RoutePOISearch.html#RoutePOISearch-Context-com.amap.api.services.routepoisearch.RoutePOISearchQuery-">RoutePOISearch</a></span>(Context&nbsp;context,
              <a href="../../../../../../com/amap/api/services/routepoisearch/RoutePOISearchQuery.html" title="com.amap.api.services.routepoisearch中的类">RoutePOISearchQuery</a>&nbsp;query)</code>
<div class="block">沿途搜索构造方法。</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.amap.api.services.share">
<!--   -->
</a>
<h3><a href="../../../../../../com/amap/api/services/share/package-summary.html">com.amap.api.services.share</a>中<a href="../../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a>的使用</h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表方法和解释">
<caption><span>抛出<a href="../../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a>的<a href="../../../../../../com/amap/api/services/share/package-summary.html">com.amap.api.services.share</a>中的方法</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><span class="typeNameLabel">ShareSearch.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/share/ShareSearch.html#searchBusRouteShareUrl-com.amap.api.services.share.ShareSearch.ShareBusRouteQuery-">searchBusRouteShareUrl</a></span>(<a href="../../../../../../com/amap/api/services/share/ShareSearch.ShareBusRouteQuery.html" title="com.amap.api.services.share中的类">ShareSearch.ShareBusRouteQuery</a>&nbsp;shareBusRouteQuery)</code>
<div class="block">根据指定的参数获取公交路径规划分享的短串地址。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><span class="typeNameLabel">ShareSearch.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/share/ShareSearch.html#searchDrivingRouteShareUrl-com.amap.api.services.share.ShareSearch.ShareDrivingRouteQuery-">searchDrivingRouteShareUrl</a></span>(<a href="../../../../../../com/amap/api/services/share/ShareSearch.ShareDrivingRouteQuery.html" title="com.amap.api.services.share中的类">ShareSearch.ShareDrivingRouteQuery</a>&nbsp;drivingRouteQuery)</code>
<div class="block">根据指定的参数获取驾车路径规划分享的短串地址。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><span class="typeNameLabel">ShareSearch.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/share/ShareSearch.html#searchLocationShareUrl-com.amap.api.services.core.LatLonSharePoint-">searchLocationShareUrl</a></span>(<a href="../../../../../../com/amap/api/services/core/LatLonSharePoint.html" title="com.amap.api.services.core中的类">LatLonSharePoint</a>&nbsp;locationQuery)</code>
<div class="block">根据指定的参数获取位置分享的短串地址。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><span class="typeNameLabel">ShareSearch.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/share/ShareSearch.html#searchNaviShareUrl-com.amap.api.services.share.ShareSearch.ShareNaviQuery-">searchNaviShareUrl</a></span>(<a href="../../../../../../com/amap/api/services/share/ShareSearch.ShareNaviQuery.html" title="com.amap.api.services.share中的类">ShareSearch.ShareNaviQuery</a>&nbsp;naviQuery)</code>
<div class="block">根据指定的参数获取导航分享的短串地址。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><span class="typeNameLabel">ShareSearch.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/share/ShareSearch.html#searchPoiShareUrl-com.amap.api.services.core.PoiItem-">searchPoiShareUrl</a></span>(<a href="../../../../../../com/amap/api/services/core/PoiItem.html" title="com.amap.api.services.core中的类">PoiItem</a>&nbsp;poiitem)</code>
<div class="block">根据指定的参数获取POI分享的短串地址。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><span class="typeNameLabel">ShareSearch.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/share/ShareSearch.html#searchWalkRouteShareUrl-com.amap.api.services.share.ShareSearch.ShareWalkRouteQuery-">searchWalkRouteShareUrl</a></span>(<a href="../../../../../../com/amap/api/services/share/ShareSearch.ShareWalkRouteQuery.html" title="com.amap.api.services.share中的类">ShareSearch.ShareWalkRouteQuery</a>&nbsp;walkRouteQuery)</code>
<div class="block">根据指定的参数获取步行路径规划分享的短串地址。</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表构造器和解释">
<caption><span>抛出<a href="../../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a>的<a href="../../../../../../com/amap/api/services/share/package-summary.html">com.amap.api.services.share</a>中的构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/share/ShareSearch.html#ShareSearch-Context-">ShareSearch</a></span>(Context&nbsp;context)</code>
<div class="block">根据给定的参数来构造一个ShareSearch的新对象。</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.amap.api.services.weather">
<!--   -->
</a>
<h3><a href="../../../../../../com/amap/api/services/weather/package-summary.html">com.amap.api.services.weather</a>中<a href="../../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a>的使用</h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表构造器和解释">
<caption><span>抛出<a href="../../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a>的<a href="../../../../../../com/amap/api/services/weather/package-summary.html">com.amap.api.services.weather</a>中的构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/weather/WeatherSearch.html#WeatherSearch-Context-">WeatherSearch</a></span>(Context&nbsp;context)</code>
<div class="block">根据给定的参数构造一个WeatherSearch 的新对象。</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../../overview-summary.html">概览</a></li>
<li><a href="../package-summary.html">程序包</a></li>
<li><a href="../../../../../../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">类</a></li>
<li class="navBarCell1Rev">使用</li>
<li><a href="../../../../../../overview-tree.html">树</a></li>
<li><a href="../../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个</li>
<li>下一个</li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?com/amap/api/services/core/class-use/AMapException.html" target="_top">框架</a></li>
<li><a href="AMapException.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
