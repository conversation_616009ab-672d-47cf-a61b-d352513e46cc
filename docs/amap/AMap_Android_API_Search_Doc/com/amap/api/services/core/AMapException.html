<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>AMapException</title>
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="AMapException";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10};
var tabs = {65535:["t0","所有方法"],2:["t2","实例方法"],8:["t4","具体方法"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/AMapException.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个类</li>
<li><a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/core/AMapException.html" target="_top">框架</a></li>
<li><a href="AMapException.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li><a href="#field.summary">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li><a href="#field.detail">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.amap.api.services.core</div>
<h2 title="类 AMapException" class="title">类 AMapException</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>java.lang.Throwable</li>
<li>
<ul class="inheritance">
<li>java.lang.Exception</li>
<li>
<ul class="inheritance">
<li>com.amap.api.services.core.AMapException</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>所有已实现的接口:</dt>
<dd>java.io.Serializable</dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">AMapException</span>
extends java.lang.Exception</pre>
<div class="block">服务异常信息类。
 具体信息请前往官网错误码对照表进行查看
 <strong><a href="http://lbs.amap.com/api/android-sdk/guide/map-tools/error-code/">官网错误码地址</a></strong></div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>1.2.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../serialized-form.html#com.amap.api.services.core.AMapException">序列化表格</a></dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>字段概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="字段概要表, 列表字段和解释">
<caption><span>字段</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">字段和说明</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_ACCESS_TOO_FREQUENT">AMAP_ACCESS_TOO_FREQUENT</a></span></code>
<div class="block">用户访问过于频繁 ErrorCode：1005</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_CLIENT_ERROR_PROTOCOL">AMAP_CLIENT_ERROR_PROTOCOL</a></span></code>
<div class="block">协议解析错误 - ProtocolException ErrorCode：1801</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_CLIENT_ERRORCODE_MISSSING">AMAP_CLIENT_ERRORCODE_MISSSING</a></span></code>
<div class="block">没有对应的错误 ErrorCode：1800</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_CLIENT_ERRORCODE_MISSSING_TPPE">AMAP_CLIENT_ERRORCODE_MISSSING_TPPE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_CLIENT_INVALID_PARAMETER">AMAP_CLIENT_INVALID_PARAMETER</a></span></code>
<div class="block">无效的参数 - IllegalArgumentException ErrorCode：1901</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_CLIENT_IO_EXCEPTION">AMAP_CLIENT_IO_EXCEPTION</a></span></code>
<div class="block">IO 操作异常 - IOException ErrorCode：1902</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_CLIENT_NEARBY_NULL_RESULT">AMAP_CLIENT_NEARBY_NULL_RESULT</a></span></code>
<div class="block">NearbyInfo对象为空 ErrorCode：2202</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_CLIENT_NETWORK_EXCEPTION">AMAP_CLIENT_NETWORK_EXCEPTION</a></span></code>
<div class="block">http或socket连接失败 - ConnectionException ErrorCode：1806</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_CLIENT_NULLPOINT_EXCEPTION">AMAP_CLIENT_NULLPOINT_EXCEPTION</a></span></code>
<div class="block">空指针异常 - NullPointException ErrorCode：1903</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_CLIENT_OVER_KEYWORD_LEN_MAX_COUNT_EXCEPTION">AMAP_CLIENT_OVER_KEYWORD_LEN_MAX_COUNT_EXCEPTION</a></span></code>
<div class="block">搜索关键字过长</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_CLIENT_OVER_PASSAREA_ITEM_POINT_COUNT_EXCEPTION">AMAP_CLIENT_OVER_PASSAREA_ITEM_POINT_COUNT_EXCEPTION</a></span></code>
<div class="block">避让区域点个数超限</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_CLIENT_OVER_PASSAREA_MAX_AREA_EXCEPTION">AMAP_CLIENT_OVER_PASSAREA_MAX_AREA_EXCEPTION</a></span></code>
<div class="block">避让区域大小超限</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_CLIENT_OVER_PASSAREA_MAX_COUNT_EXCEPTION">AMAP_CLIENT_OVER_PASSAREA_MAX_COUNT_EXCEPTION</a></span></code>
<div class="block">避让区域个数超限</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_CLIENT_OVER_PASSBY_MAX_COUNT_EXCEPTION">AMAP_CLIENT_OVER_PASSBY_MAX_COUNT_EXCEPTION</a></span></code>
<div class="block">途经点个数超限</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_CLIENT_SOCKET_TIMEOUT_EXCEPTION">AMAP_CLIENT_SOCKET_TIMEOUT_EXCEPTION</a></span></code>
<div class="block">socket 连接超时 - SocketTimeoutException ErrorCode：1802</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_CLIENT_UNKNOWHOST_EXCEPTION">AMAP_CLIENT_UNKNOWHOST_EXCEPTION</a></span></code>
<div class="block">未知主机 - UnKnowHostException ErrorCode：1804</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_CLIENT_UNKNOWN_ERROR">AMAP_CLIENT_UNKNOWN_ERROR</a></span></code>
<div class="block">未知错误 ErrorCode：1900</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_CLIENT_UNKNOWN_ERROR_TYPE">AMAP_CLIENT_UNKNOWN_ERROR_TYPE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_CLIENT_UPLOAD_LOCATION_ERROR">AMAP_CLIENT_UPLOAD_LOCATION_ERROR</a></span></code>
<div class="block">Point为空，或与前次上传的相同 ErrorCode：2204</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_CLIENT_UPLOAD_TOO_FREQUENT">AMAP_CLIENT_UPLOAD_TOO_FREQUENT</a></span></code>
<div class="block">两次单次上传的间隔低于7秒 ErrorCode：2203</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_CLIENT_UPLOADAUTO_STARTED_ERROR">AMAP_CLIENT_UPLOADAUTO_STARTED_ERROR</a></span></code>
<div class="block">已开启自动上传 ErrorCode：2200</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_CLIENT_URL_EXCEPTION">AMAP_CLIENT_URL_EXCEPTION</a></span></code>
<div class="block">url异常 - MalformedURLException ErrorCode：1803</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_CLIENT_USERID_ILLEGAL">AMAP_CLIENT_USERID_ILLEGAL</a></span></code>
<div class="block">USERID非法 ErrorCode：2201</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_DAILY_QUERY_OVER_LIMIT">AMAP_DAILY_QUERY_OVER_LIMIT</a></span></code>
<div class="block">访问已超出日访问量 ErrorCode：1004</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_ENGINE_CONNECT_TIMEOUT">AMAP_ENGINE_CONNECT_TIMEOUT</a></span></code>
<div class="block">服务端请求链接超时 ErrorCode：1102</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_ENGINE_RESPONSE_DATA_ERROR">AMAP_ENGINE_RESPONSE_DATA_ERROR</a></span></code>
<div class="block">引擎返回数据异常 ErrorCode：1101</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_ENGINE_RESPONSE_ERROR">AMAP_ENGINE_RESPONSE_ERROR</a></span></code>
<div class="block">请求服务响应错误 ErrorCode：1100</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_ENGINE_RETURN_TIMEOUT">AMAP_ENGINE_RETURN_TIMEOUT</a></span></code>
<div class="block">读取服务结果超时 ErrorCode：1103</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_ENGINE_TABLEID_NOT_EXIST">AMAP_ENGINE_TABLEID_NOT_EXIST</a></span></code>
<div class="block">key对应的tableID不存在 ErrorCode：2003</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_ID_NOT_EXIST">AMAP_ID_NOT_EXIST</a></span></code>
<div class="block">ID不存在 ErrorCode：2001</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_INSUFFICIENT_PRIVILEGES">AMAP_INSUFFICIENT_PRIVILEGES</a></span></code>
<div class="block">权限不足，服务请求被拒绝 ErrorCode：1012</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_INVALID_USER_DOMAIN">AMAP_INVALID_USER_DOMAIN</a></span></code>
<div class="block">用户域名无效 ErrorCode：1007</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_INVALID_USER_IP">AMAP_INVALID_USER_IP</a></span></code>
<div class="block">用户IP无效 ErrorCode：1006</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_INVALID_USER_KEY">AMAP_INVALID_USER_KEY</a></span></code>
<div class="block">用户key不正确或过期 ErrorCode：1002</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_INVALID_USER_SCODE">AMAP_INVALID_USER_SCODE</a></span></code>
<div class="block">用户MD5安全码未通过 ErrorCode：1008</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_IP_QUERY_OVER_LIMIT">AMAP_IP_QUERY_OVER_LIMIT</a></span></code>
<div class="block">IP访问超限 ErrorCode：1010</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_NEARBY_INVALID_USERID">AMAP_NEARBY_INVALID_USERID</a></span></code>
<div class="block">找不到对应的userid信息,请检查您提供的userid是否存在 ErrorCode：2100</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_NEARBY_KEY_NOT_BIND">AMAP_NEARBY_KEY_NOT_BIND</a></span></code>
<div class="block">App key未开通“附近”功能,请注册附近KEY ErrorCode：2101</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_NOT_SUPPORT_HTTPS">AMAP_NOT_SUPPORT_HTTPS</a></span></code>
<div class="block">服务不支持https请求 ErrorCode：1011</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_OVER_DIRECTION_RANGE">AMAP_OVER_DIRECTION_RANGE</a></span></code>
<div class="block">起点终点距离过长 ErrorCode：3003</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_ROUTE_FAIL">AMAP_ROUTE_FAIL</a></span></code>
<div class="block">路线计算失败，通常是由于道路连通关系导致 ErrorCode：3002</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_ROUTE_NO_ROADS_NEARBY">AMAP_ROUTE_NO_ROADS_NEARBY</a></span></code>
<div class="block">规划点（起点、终点、途经点）附近搜不到路 ErrorCode：3001</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_ROUTE_OUT_OF_SERVICE">AMAP_ROUTE_OUT_OF_SERVICE</a></span></code>
<div class="block">规划点（包括起点、终点、途经点）不在中国陆地范围内 ErrorCode：3000</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_SERVICE_ILLEGAL_REQUEST">AMAP_SERVICE_ILLEGAL_REQUEST</a></span></code>
<div class="block">请求协议非法 ErrorCode：1202</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_SERVICE_INVALID_PARAMS">AMAP_SERVICE_INVALID_PARAMS</a></span></code>
<div class="block">请求参数非法 ErrorCode：1200</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_SERVICE_MAINTENANCE">AMAP_SERVICE_MAINTENANCE</a></span></code>
<div class="block">服务器维护中 ErrorCode：2002</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_SERVICE_MISSING_REQUIRED_PARAMS">AMAP_SERVICE_MISSING_REQUIRED_PARAMS</a></span></code>
<div class="block">缺少必填参数 ErrorCode：1201</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_SERVICE_NOT_AVAILBALE">AMAP_SERVICE_NOT_AVAILBALE</a></span></code>
<div class="block">请求服务不存在 ErrorCode：1003</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_SERVICE_TABLEID_NOT_EXIST">AMAP_SERVICE_TABLEID_NOT_EXIST</a></span></code>
<div class="block">tableID格式不正确不存在 ErrorCode：2000</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_SERVICE_UNKNOWN_ERROR">AMAP_SERVICE_UNKNOWN_ERROR</a></span></code>
<div class="block">其他未知错误 ErrorCode：1203</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_SHARE_FAILURE">AMAP_SHARE_FAILURE</a></span></code>
<div class="block">短串请求失败 ErrorCode：4001</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_SHARE_LICENSE_IS_EXPIRED">AMAP_SHARE_LICENSE_IS_EXPIRED</a></span></code>
<div class="block">短串分享认证失败 ErrorCode：4000</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_SHARE_SIGNATURE_FAILURE">AMAP_SHARE_SIGNATURE_FAILURE</a></span></code>
<div class="block">短串分享用户签名未通过 ErrorCode：4002</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_SIGNATURE_ERROR">AMAP_SIGNATURE_ERROR</a></span></code>
<div class="block">用户签名未通过 ErrorCode：1001</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_USER_KEY_RECYCLED">AMAP_USER_KEY_RECYCLED</a></span></code>
<div class="block">开发者删除了key，key被删除后无法正常使用 ErrorCode：1013</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_USERKEY_PLAT_NOMATCH">AMAP_USERKEY_PLAT_NOMATCH</a></span></code>
<div class="block">请求key与绑定平台不符 ErrorCode：1009</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_ACCESS_TOO_FREQUENT">CODE_AMAP_ACCESS_TOO_FREQUENT</a></span></code>
<div class="block">用户访问过于频繁 ErrorCode：1005</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_CLIENT_ERROR_PROTOCOL">CODE_AMAP_CLIENT_ERROR_PROTOCOL</a></span></code>
<div class="block">协议解析错误 - ProtocolException ErrorCode：1801</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_CLIENT_ERRORCODE_MISSSING">CODE_AMAP_CLIENT_ERRORCODE_MISSSING</a></span></code>
<div class="block">没有对应的错误 ErrorCode：1800</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_CLIENT_INVALID_PARAMETER">CODE_AMAP_CLIENT_INVALID_PARAMETER</a></span></code>
<div class="block">无效的参数 - IllegalArgumentException ErrorCode：1901</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_CLIENT_IO_EXCEPTION">CODE_AMAP_CLIENT_IO_EXCEPTION</a></span></code>
<div class="block">IO 操作异常 - IOException ErrorCode：1902</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_CLIENT_NEARBY_NULL_RESULT">CODE_AMAP_CLIENT_NEARBY_NULL_RESULT</a></span></code>
<div class="block">NearbyInfo对象为空 ErrorCode：2202</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_CLIENT_NETWORK_EXCEPTION">CODE_AMAP_CLIENT_NETWORK_EXCEPTION</a></span></code>
<div class="block">http或socket连接失败 - ConnectionException ErrorCode：1806</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_CLIENT_NULLPOINT_EXCEPTION">CODE_AMAP_CLIENT_NULLPOINT_EXCEPTION</a></span></code>
<div class="block">空指针异常 - NullPointException ErrorCode：1903</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_CLIENT_OVER_KEYWORD_LEN_MAX_COUNT_EXCEPTION">CODE_AMAP_CLIENT_OVER_KEYWORD_LEN_MAX_COUNT_EXCEPTION</a></span></code>
<div class="block">搜索关键字过长</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_CLIENT_OVER_PASSAREA_ITEM_POINT_COUNT_EXCEPTION">CODE_AMAP_CLIENT_OVER_PASSAREA_ITEM_POINT_COUNT_EXCEPTION</a></span></code>
<div class="block">避让区域点个数超限</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_CLIENT_OVER_PASSAREA_MAX_AREA_EXCEPTION">CODE_AMAP_CLIENT_OVER_PASSAREA_MAX_AREA_EXCEPTION</a></span></code>
<div class="block">避让区域大小超限</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_CLIENT_OVER_PASSAREA_MAX_COUNT_EXCEPTION">CODE_AMAP_CLIENT_OVER_PASSAREA_MAX_COUNT_EXCEPTION</a></span></code>
<div class="block">避让区域个数超限</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_CLIENT_OVER_PASSBY_MAX_COUNT_EXCEPTION">CODE_AMAP_CLIENT_OVER_PASSBY_MAX_COUNT_EXCEPTION</a></span></code>
<div class="block">途经点个数超限</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_CLIENT_SOCKET_TIMEOUT_EXCEPTION">CODE_AMAP_CLIENT_SOCKET_TIMEOUT_EXCEPTION</a></span></code>
<div class="block">socket 连接超时 - SocketTimeoutException ErrorCode：1802</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_CLIENT_UNKNOWHOST_EXCEPTION">CODE_AMAP_CLIENT_UNKNOWHOST_EXCEPTION</a></span></code>
<div class="block">未知主机 - UnKnowHostException ErrorCode：1804</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_CLIENT_UNKNOWN_ERROR">CODE_AMAP_CLIENT_UNKNOWN_ERROR</a></span></code>
<div class="block">未知错误 ErrorCode：1900</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_CLIENT_UPLOAD_LOCATION_ERROR">CODE_AMAP_CLIENT_UPLOAD_LOCATION_ERROR</a></span></code>
<div class="block">Point为空，或与前次上传的相同 ErrorCode：2204</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_CLIENT_UPLOAD_TOO_FREQUENT">CODE_AMAP_CLIENT_UPLOAD_TOO_FREQUENT</a></span></code>
<div class="block">两次单次上传的间隔低于7秒 ErrorCode：2203</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_CLIENT_UPLOADAUTO_STARTED_ERROR">CODE_AMAP_CLIENT_UPLOADAUTO_STARTED_ERROR</a></span></code>
<div class="block">已开启自动上传 ErrorCode：2200</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_CLIENT_URL_EXCEPTION">CODE_AMAP_CLIENT_URL_EXCEPTION</a></span></code>
<div class="block">url异常 - MalformedURLException ErrorCode：1803</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_CLIENT_USERID_ILLEGAL">CODE_AMAP_CLIENT_USERID_ILLEGAL</a></span></code>
<div class="block">USERID非法 ErrorCode：2201</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_DAILY_QUERY_OVER_LIMIT">CODE_AMAP_DAILY_QUERY_OVER_LIMIT</a></span></code>
<div class="block">访问已超出日访问量 ErrorCode：1004</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_ENGINE_CONNECT_TIMEOUT">CODE_AMAP_ENGINE_CONNECT_TIMEOUT</a></span></code>
<div class="block">服务端请求链接超时 ErrorCode：1102</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_ENGINE_RESPONSE_DATA_ERROR">CODE_AMAP_ENGINE_RESPONSE_DATA_ERROR</a></span></code>
<div class="block">引擎返回数据异常 ErrorCode：1101</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_ENGINE_RESPONSE_ERROR">CODE_AMAP_ENGINE_RESPONSE_ERROR</a></span></code>
<div class="block">请求服务响应错误 ErrorCode：1100</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_ENGINE_RETURN_TIMEOUT">CODE_AMAP_ENGINE_RETURN_TIMEOUT</a></span></code>
<div class="block">读取服务结果超时 ErrorCode：1103</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_ENGINE_TABLEID_NOT_EXIST">CODE_AMAP_ENGINE_TABLEID_NOT_EXIST</a></span></code>
<div class="block">key对应的tableID不存在 ErrorCode：2003</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_ID_NOT_EXIST">CODE_AMAP_ID_NOT_EXIST</a></span></code>
<div class="block">ID不存在 ErrorCode：2001</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_INSUFFICIENT_PRIVILEGES">CODE_AMAP_INSUFFICIENT_PRIVILEGES</a></span></code>
<div class="block">权限不足，服务请求被拒绝 ErrorCode：1012</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_INVALID_USER_DOMAIN">CODE_AMAP_INVALID_USER_DOMAIN</a></span></code>
<div class="block">用户域名无效 ErrorCode：1007</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_INVALID_USER_IP">CODE_AMAP_INVALID_USER_IP</a></span></code>
<div class="block">用户IP无效 ErrorCode：1006</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_INVALID_USER_KEY">CODE_AMAP_INVALID_USER_KEY</a></span></code>
<div class="block">用户key不正确或过期 ErrorCode：1002</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_INVALID_USER_SCODE">CODE_AMAP_INVALID_USER_SCODE</a></span></code>
<div class="block">用户MD5安全码未通过 ErrorCode：1008</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_IP_QUERY_OVER_LIMIT">CODE_AMAP_IP_QUERY_OVER_LIMIT</a></span></code>
<div class="block">IP访问超限 ErrorCode：1010</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_NEARBY_INVALID_USERID">CODE_AMAP_NEARBY_INVALID_USERID</a></span></code>
<div class="block">找不到对应的userid信息,请检查您提供的userid是否存在 ErrorCode：2100</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_NEARBY_KEY_NOT_BIND">CODE_AMAP_NEARBY_KEY_NOT_BIND</a></span></code>
<div class="block">App key未开通“附近”功能,请注册附近KEY ErrorCode：2101</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_NOT_SUPPORT_HTTPS">CODE_AMAP_NOT_SUPPORT_HTTPS</a></span></code>
<div class="block">服务不支持https请求 ErrorCode：1011</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_OVER_DIRECTION_RANGE">CODE_AMAP_OVER_DIRECTION_RANGE</a></span></code>
<div class="block">起点终点距离过长 ErrorCode：3003</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_ROUTE_FAIL">CODE_AMAP_ROUTE_FAIL</a></span></code>
<div class="block">路线计算失败，通常是由于道路连通关系导致 ErrorCode：3002</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_ROUTE_NO_ROADS_NEARBY">CODE_AMAP_ROUTE_NO_ROADS_NEARBY</a></span></code>
<div class="block">规划点（起点、终点、途经点）附近搜不到路 ErrorCode：3001</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_ROUTE_OUT_OF_SERVICE">CODE_AMAP_ROUTE_OUT_OF_SERVICE</a></span></code>
<div class="block">规划点（包括起点、终点、途经点）不在中国陆地范围内 ErrorCode：3000</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_SERVICE_ILLEGAL_REQUEST">CODE_AMAP_SERVICE_ILLEGAL_REQUEST</a></span></code>
<div class="block">请求协议非法 ErrorCode：1202</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_SERVICE_INVALID_PARAMS">CODE_AMAP_SERVICE_INVALID_PARAMS</a></span></code>
<div class="block">请求参数非法 ErrorCode：1200</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_SERVICE_MAINTENANCE">CODE_AMAP_SERVICE_MAINTENANCE</a></span></code>
<div class="block">服务器维护中 ErrorCode：2002</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_SERVICE_MISSING_REQUIRED_PARAMS">CODE_AMAP_SERVICE_MISSING_REQUIRED_PARAMS</a></span></code>
<div class="block">缺少必填参数 ErrorCode：1201</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_SERVICE_NOT_AVAILBALE">CODE_AMAP_SERVICE_NOT_AVAILBALE</a></span></code>
<div class="block">请求服务不存在 ErrorCode：1003</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_SERVICE_TABLEID_NOT_EXIST">CODE_AMAP_SERVICE_TABLEID_NOT_EXIST</a></span></code>
<div class="block">tableID格式不正确不存在 ErrorCode：2000</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_SERVICE_UNKNOWN_ERROR">CODE_AMAP_SERVICE_UNKNOWN_ERROR</a></span></code>
<div class="block">其他未知错误 ErrorCode：1203</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_SHARE_FAILURE">CODE_AMAP_SHARE_FAILURE</a></span></code>
<div class="block">短串请求失败 ErrorCode：4001</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_SHARE_LICENSE_IS_EXPIRED">CODE_AMAP_SHARE_LICENSE_IS_EXPIRED</a></span></code>
<div class="block">短串分享认证失败 ErrorCode：4000</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_SHARE_SIGNATURE_FAILURE">CODE_AMAP_SHARE_SIGNATURE_FAILURE</a></span></code>
<div class="block">短串分享用户签名未通过 ErrorCode：4002</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_SIGNATURE_ERROR">CODE_AMAP_SIGNATURE_ERROR</a></span></code>
<div class="block">用户签名未通过 ErrorCode：1001</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_SUCCESS">CODE_AMAP_SUCCESS</a></span></code>
<div class="block">搜索成功 用 1000 表示。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_USER_KEY_RECYCLED">CODE_AMAP_USER_KEY_RECYCLED</a></span></code>
<div class="block">开发者删除了key，key被删除后无法正常使用 ErrorCode：1013</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_USERKEY_PLAT_NOMATCH">CODE_AMAP_USERKEY_PLAT_NOMATCH</a></span></code>
<div class="block">请求key与绑定平台不符 ErrorCode：1009</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#AMapException--">AMapException</a></span>()</code>
<div class="block">构造服务异常对象。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#AMapException-java.lang.String-">AMapException</a></span>(java.lang.String&nbsp;errorMessage)</code>
<div class="block">构造服务异常对象。</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>所有方法</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">实例方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">具体方法</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#getErrorCode--">getErrorCode</a></span>()</code>
<div class="block">返回错误码。</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/services/core/AMapException.html#getErrorMessage--">getErrorMessage</a></span>()</code>
<div class="block">返回异常信息。</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Throwable">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Throwable</h3>
<code>addSuppressed, fillInStackTrace, getCause, getLocalizedMessage, getMessage, getStackTrace, getSuppressed, initCause, printStackTrace, printStackTrace, printStackTrace, setStackTrace, toString</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>字段详细资料</h3>
<a name="CODE_AMAP_SUCCESS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CODE_AMAP_SUCCESS</h4>
<pre>public static final&nbsp;int CODE_AMAP_SUCCESS</pre>
<div class="block">搜索成功 用 1000 表示。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.6.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.CODE_AMAP_SUCCESS">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="AMAP_SIGNATURE_ERROR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>AMAP_SIGNATURE_ERROR</h4>
<pre>public static final&nbsp;java.lang.String AMAP_SIGNATURE_ERROR</pre>
<div class="block">用户签名未通过 ErrorCode：1001</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_SIGNATURE_ERROR"><code>AMapException.CODE_AMAP_SIGNATURE_ERROR</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.AMAP_SIGNATURE_ERROR">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="AMAP_INVALID_USER_KEY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>AMAP_INVALID_USER_KEY</h4>
<pre>public static final&nbsp;java.lang.String AMAP_INVALID_USER_KEY</pre>
<div class="block">用户key不正确或过期 ErrorCode：1002</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_INVALID_USER_KEY"><code>AMapException.CODE_AMAP_INVALID_USER_KEY</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.AMAP_INVALID_USER_KEY">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="AMAP_SERVICE_NOT_AVAILBALE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>AMAP_SERVICE_NOT_AVAILBALE</h4>
<pre>public static final&nbsp;java.lang.String AMAP_SERVICE_NOT_AVAILBALE</pre>
<div class="block">请求服务不存在 ErrorCode：1003</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_SERVICE_NOT_AVAILBALE"><code>AMapException.CODE_AMAP_SERVICE_NOT_AVAILBALE</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.AMAP_SERVICE_NOT_AVAILBALE">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="AMAP_DAILY_QUERY_OVER_LIMIT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>AMAP_DAILY_QUERY_OVER_LIMIT</h4>
<pre>public static final&nbsp;java.lang.String AMAP_DAILY_QUERY_OVER_LIMIT</pre>
<div class="block">访问已超出日访问量 ErrorCode：1004</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_DAILY_QUERY_OVER_LIMIT"><code>AMapException.CODE_AMAP_DAILY_QUERY_OVER_LIMIT</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.AMAP_DAILY_QUERY_OVER_LIMIT">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="AMAP_ACCESS_TOO_FREQUENT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>AMAP_ACCESS_TOO_FREQUENT</h4>
<pre>public static final&nbsp;java.lang.String AMAP_ACCESS_TOO_FREQUENT</pre>
<div class="block">用户访问过于频繁 ErrorCode：1005</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_ACCESS_TOO_FREQUENT"><code>AMapException.CODE_AMAP_ACCESS_TOO_FREQUENT</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.AMAP_ACCESS_TOO_FREQUENT">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="AMAP_INVALID_USER_IP">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>AMAP_INVALID_USER_IP</h4>
<pre>public static final&nbsp;java.lang.String AMAP_INVALID_USER_IP</pre>
<div class="block">用户IP无效 ErrorCode：1006</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_INVALID_USER_IP"><code>AMapException.CODE_AMAP_INVALID_USER_IP</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.AMAP_INVALID_USER_IP">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="AMAP_INVALID_USER_DOMAIN">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>AMAP_INVALID_USER_DOMAIN</h4>
<pre>public static final&nbsp;java.lang.String AMAP_INVALID_USER_DOMAIN</pre>
<div class="block">用户域名无效 ErrorCode：1007</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_INVALID_USER_DOMAIN"><code>AMapException.CODE_AMAP_INVALID_USER_DOMAIN</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.AMAP_INVALID_USER_DOMAIN">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="AMAP_INVALID_USER_SCODE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>AMAP_INVALID_USER_SCODE</h4>
<pre>public static final&nbsp;java.lang.String AMAP_INVALID_USER_SCODE</pre>
<div class="block">用户MD5安全码未通过 ErrorCode：1008</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_INVALID_USER_SCODE"><code>AMapException.CODE_AMAP_INVALID_USER_SCODE</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.AMAP_INVALID_USER_SCODE">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="AMAP_USERKEY_PLAT_NOMATCH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>AMAP_USERKEY_PLAT_NOMATCH</h4>
<pre>public static final&nbsp;java.lang.String AMAP_USERKEY_PLAT_NOMATCH</pre>
<div class="block">请求key与绑定平台不符 ErrorCode：1009</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_USERKEY_PLAT_NOMATCH"><code>AMapException.CODE_AMAP_USERKEY_PLAT_NOMATCH</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.AMAP_USERKEY_PLAT_NOMATCH">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="AMAP_IP_QUERY_OVER_LIMIT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>AMAP_IP_QUERY_OVER_LIMIT</h4>
<pre>public static final&nbsp;java.lang.String AMAP_IP_QUERY_OVER_LIMIT</pre>
<div class="block">IP访问超限 ErrorCode：1010</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_IP_QUERY_OVER_LIMIT"><code>AMapException.CODE_AMAP_IP_QUERY_OVER_LIMIT</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.AMAP_IP_QUERY_OVER_LIMIT">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="AMAP_NOT_SUPPORT_HTTPS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>AMAP_NOT_SUPPORT_HTTPS</h4>
<pre>public static final&nbsp;java.lang.String AMAP_NOT_SUPPORT_HTTPS</pre>
<div class="block">服务不支持https请求 ErrorCode：1011</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_NOT_SUPPORT_HTTPS"><code>AMapException.CODE_AMAP_NOT_SUPPORT_HTTPS</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.AMAP_NOT_SUPPORT_HTTPS">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="AMAP_INSUFFICIENT_PRIVILEGES">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>AMAP_INSUFFICIENT_PRIVILEGES</h4>
<pre>public static final&nbsp;java.lang.String AMAP_INSUFFICIENT_PRIVILEGES</pre>
<div class="block">权限不足，服务请求被拒绝 ErrorCode：1012</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_INSUFFICIENT_PRIVILEGES"><code>AMapException.CODE_AMAP_INSUFFICIENT_PRIVILEGES</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.AMAP_INSUFFICIENT_PRIVILEGES">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="AMAP_USER_KEY_RECYCLED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>AMAP_USER_KEY_RECYCLED</h4>
<pre>public static final&nbsp;java.lang.String AMAP_USER_KEY_RECYCLED</pre>
<div class="block">开发者删除了key，key被删除后无法正常使用 ErrorCode：1013</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_USER_KEY_RECYCLED"><code>AMapException.CODE_AMAP_USER_KEY_RECYCLED</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.AMAP_USER_KEY_RECYCLED">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="AMAP_ENGINE_RESPONSE_ERROR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>AMAP_ENGINE_RESPONSE_ERROR</h4>
<pre>public static final&nbsp;java.lang.String AMAP_ENGINE_RESPONSE_ERROR</pre>
<div class="block">请求服务响应错误 ErrorCode：1100</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_ENGINE_RESPONSE_ERROR"><code>AMapException.CODE_AMAP_ENGINE_RESPONSE_ERROR</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.AMAP_ENGINE_RESPONSE_ERROR">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="AMAP_ENGINE_RESPONSE_DATA_ERROR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>AMAP_ENGINE_RESPONSE_DATA_ERROR</h4>
<pre>public static final&nbsp;java.lang.String AMAP_ENGINE_RESPONSE_DATA_ERROR</pre>
<div class="block">引擎返回数据异常 ErrorCode：1101</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_ENGINE_RESPONSE_DATA_ERROR"><code>AMapException.CODE_AMAP_ENGINE_RESPONSE_DATA_ERROR</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.AMAP_ENGINE_RESPONSE_DATA_ERROR">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="AMAP_ENGINE_CONNECT_TIMEOUT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>AMAP_ENGINE_CONNECT_TIMEOUT</h4>
<pre>public static final&nbsp;java.lang.String AMAP_ENGINE_CONNECT_TIMEOUT</pre>
<div class="block">服务端请求链接超时 ErrorCode：1102</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_ENGINE_CONNECT_TIMEOUT"><code>AMapException.CODE_AMAP_ENGINE_CONNECT_TIMEOUT</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.AMAP_ENGINE_CONNECT_TIMEOUT">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="AMAP_ENGINE_RETURN_TIMEOUT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>AMAP_ENGINE_RETURN_TIMEOUT</h4>
<pre>public static final&nbsp;java.lang.String AMAP_ENGINE_RETURN_TIMEOUT</pre>
<div class="block">读取服务结果超时 ErrorCode：1103</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_ENGINE_RETURN_TIMEOUT"><code>AMapException.CODE_AMAP_ENGINE_RETURN_TIMEOUT</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.AMAP_ENGINE_RETURN_TIMEOUT">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="AMAP_SERVICE_INVALID_PARAMS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>AMAP_SERVICE_INVALID_PARAMS</h4>
<pre>public static final&nbsp;java.lang.String AMAP_SERVICE_INVALID_PARAMS</pre>
<div class="block">请求参数非法 ErrorCode：1200</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_SERVICE_INVALID_PARAMS"><code>AMapException.CODE_AMAP_SERVICE_INVALID_PARAMS</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.AMAP_SERVICE_INVALID_PARAMS">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="AMAP_SERVICE_MISSING_REQUIRED_PARAMS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>AMAP_SERVICE_MISSING_REQUIRED_PARAMS</h4>
<pre>public static final&nbsp;java.lang.String AMAP_SERVICE_MISSING_REQUIRED_PARAMS</pre>
<div class="block">缺少必填参数 ErrorCode：1201</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_SERVICE_MISSING_REQUIRED_PARAMS"><code>AMapException.CODE_AMAP_SERVICE_MISSING_REQUIRED_PARAMS</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.AMAP_SERVICE_MISSING_REQUIRED_PARAMS">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="AMAP_SERVICE_ILLEGAL_REQUEST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>AMAP_SERVICE_ILLEGAL_REQUEST</h4>
<pre>public static final&nbsp;java.lang.String AMAP_SERVICE_ILLEGAL_REQUEST</pre>
<div class="block">请求协议非法 ErrorCode：1202</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_SERVICE_ILLEGAL_REQUEST"><code>AMapException.CODE_AMAP_SERVICE_ILLEGAL_REQUEST</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.AMAP_SERVICE_ILLEGAL_REQUEST">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="AMAP_SERVICE_UNKNOWN_ERROR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>AMAP_SERVICE_UNKNOWN_ERROR</h4>
<pre>public static final&nbsp;java.lang.String AMAP_SERVICE_UNKNOWN_ERROR</pre>
<div class="block">其他未知错误 ErrorCode：1203</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_SERVICE_UNKNOWN_ERROR"><code>AMapException.CODE_AMAP_SERVICE_UNKNOWN_ERROR</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.AMAP_SERVICE_UNKNOWN_ERROR">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="AMAP_CLIENT_ERROR_PROTOCOL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>AMAP_CLIENT_ERROR_PROTOCOL</h4>
<pre>public static final&nbsp;java.lang.String AMAP_CLIENT_ERROR_PROTOCOL</pre>
<div class="block">协议解析错误 - ProtocolException ErrorCode：1801</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_CLIENT_ERROR_PROTOCOL"><code>AMapException.CODE_AMAP_CLIENT_ERROR_PROTOCOL</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.AMAP_CLIENT_ERROR_PROTOCOL">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="AMAP_CLIENT_SOCKET_TIMEOUT_EXCEPTION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>AMAP_CLIENT_SOCKET_TIMEOUT_EXCEPTION</h4>
<pre>public static final&nbsp;java.lang.String AMAP_CLIENT_SOCKET_TIMEOUT_EXCEPTION</pre>
<div class="block">socket 连接超时 - SocketTimeoutException ErrorCode：1802</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_CLIENT_SOCKET_TIMEOUT_EXCEPTION"><code>AMapException.CODE_AMAP_CLIENT_SOCKET_TIMEOUT_EXCEPTION</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.AMAP_CLIENT_SOCKET_TIMEOUT_EXCEPTION">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="AMAP_CLIENT_URL_EXCEPTION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>AMAP_CLIENT_URL_EXCEPTION</h4>
<pre>public static final&nbsp;java.lang.String AMAP_CLIENT_URL_EXCEPTION</pre>
<div class="block">url异常 - MalformedURLException ErrorCode：1803</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_CLIENT_URL_EXCEPTION"><code>AMapException.CODE_AMAP_CLIENT_URL_EXCEPTION</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.AMAP_CLIENT_URL_EXCEPTION">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="AMAP_CLIENT_UNKNOWHOST_EXCEPTION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>AMAP_CLIENT_UNKNOWHOST_EXCEPTION</h4>
<pre>public static final&nbsp;java.lang.String AMAP_CLIENT_UNKNOWHOST_EXCEPTION</pre>
<div class="block">未知主机 - UnKnowHostException ErrorCode：1804</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_CLIENT_UNKNOWHOST_EXCEPTION"><code>AMapException.CODE_AMAP_CLIENT_UNKNOWHOST_EXCEPTION</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.AMAP_CLIENT_UNKNOWHOST_EXCEPTION">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="AMAP_CLIENT_NETWORK_EXCEPTION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>AMAP_CLIENT_NETWORK_EXCEPTION</h4>
<pre>public static final&nbsp;java.lang.String AMAP_CLIENT_NETWORK_EXCEPTION</pre>
<div class="block">http或socket连接失败 - ConnectionException ErrorCode：1806</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_CLIENT_NETWORK_EXCEPTION"><code>AMapException.CODE_AMAP_CLIENT_NETWORK_EXCEPTION</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.AMAP_CLIENT_NETWORK_EXCEPTION">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="AMAP_CLIENT_UNKNOWN_ERROR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>AMAP_CLIENT_UNKNOWN_ERROR</h4>
<pre>public static final&nbsp;java.lang.String AMAP_CLIENT_UNKNOWN_ERROR</pre>
<div class="block">未知错误 ErrorCode：1900</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_CLIENT_UNKNOWN_ERROR"><code>AMapException.CODE_AMAP_CLIENT_UNKNOWN_ERROR</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.AMAP_CLIENT_UNKNOWN_ERROR">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="AMAP_CLIENT_UNKNOWN_ERROR_TYPE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>AMAP_CLIENT_UNKNOWN_ERROR_TYPE</h4>
<pre>public static final&nbsp;java.lang.String AMAP_CLIENT_UNKNOWN_ERROR_TYPE</pre>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.AMAP_CLIENT_UNKNOWN_ERROR_TYPE">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="AMAP_CLIENT_INVALID_PARAMETER">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>AMAP_CLIENT_INVALID_PARAMETER</h4>
<pre>public static final&nbsp;java.lang.String AMAP_CLIENT_INVALID_PARAMETER</pre>
<div class="block">无效的参数 - IllegalArgumentException ErrorCode：1901</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_CLIENT_INVALID_PARAMETER"><code>AMapException.CODE_AMAP_CLIENT_INVALID_PARAMETER</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.AMAP_CLIENT_INVALID_PARAMETER">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="AMAP_CLIENT_IO_EXCEPTION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>AMAP_CLIENT_IO_EXCEPTION</h4>
<pre>public static final&nbsp;java.lang.String AMAP_CLIENT_IO_EXCEPTION</pre>
<div class="block">IO 操作异常 - IOException ErrorCode：1902</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_CLIENT_IO_EXCEPTION"><code>AMapException.CODE_AMAP_CLIENT_IO_EXCEPTION</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.AMAP_CLIENT_IO_EXCEPTION">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="AMAP_CLIENT_NULLPOINT_EXCEPTION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>AMAP_CLIENT_NULLPOINT_EXCEPTION</h4>
<pre>public static final&nbsp;java.lang.String AMAP_CLIENT_NULLPOINT_EXCEPTION</pre>
<div class="block">空指针异常 - NullPointException ErrorCode：1903</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_CLIENT_NULLPOINT_EXCEPTION"><code>AMapException.CODE_AMAP_CLIENT_NULLPOINT_EXCEPTION</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.AMAP_CLIENT_NULLPOINT_EXCEPTION">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="AMAP_CLIENT_ERRORCODE_MISSSING">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>AMAP_CLIENT_ERRORCODE_MISSSING</h4>
<pre>public static final&nbsp;java.lang.String AMAP_CLIENT_ERRORCODE_MISSSING</pre>
<div class="block">没有对应的错误 ErrorCode：1800</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_CLIENT_ERRORCODE_MISSSING"><code>AMapException.CODE_AMAP_CLIENT_ERRORCODE_MISSSING</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.AMAP_CLIENT_ERRORCODE_MISSSING">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="AMAP_CLIENT_ERRORCODE_MISSSING_TPPE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>AMAP_CLIENT_ERRORCODE_MISSSING_TPPE</h4>
<pre>public static final&nbsp;java.lang.String AMAP_CLIENT_ERRORCODE_MISSSING_TPPE</pre>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.AMAP_CLIENT_ERRORCODE_MISSSING_TPPE">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="AMAP_SERVICE_TABLEID_NOT_EXIST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>AMAP_SERVICE_TABLEID_NOT_EXIST</h4>
<pre>public static final&nbsp;java.lang.String AMAP_SERVICE_TABLEID_NOT_EXIST</pre>
<div class="block">tableID格式不正确不存在 ErrorCode：2000</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_SERVICE_TABLEID_NOT_EXIST"><code>AMapException.CODE_AMAP_SERVICE_TABLEID_NOT_EXIST</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.AMAP_SERVICE_TABLEID_NOT_EXIST">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="AMAP_ID_NOT_EXIST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>AMAP_ID_NOT_EXIST</h4>
<pre>public static final&nbsp;java.lang.String AMAP_ID_NOT_EXIST</pre>
<div class="block">ID不存在 ErrorCode：2001</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_ID_NOT_EXIST"><code>AMapException.CODE_AMAP_ID_NOT_EXIST</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.AMAP_ID_NOT_EXIST">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="AMAP_SERVICE_MAINTENANCE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>AMAP_SERVICE_MAINTENANCE</h4>
<pre>public static final&nbsp;java.lang.String AMAP_SERVICE_MAINTENANCE</pre>
<div class="block">服务器维护中 ErrorCode：2002</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_SERVICE_MAINTENANCE"><code>AMapException.CODE_AMAP_SERVICE_MAINTENANCE</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.AMAP_SERVICE_MAINTENANCE">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="AMAP_ENGINE_TABLEID_NOT_EXIST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>AMAP_ENGINE_TABLEID_NOT_EXIST</h4>
<pre>public static final&nbsp;java.lang.String AMAP_ENGINE_TABLEID_NOT_EXIST</pre>
<div class="block">key对应的tableID不存在 ErrorCode：2003</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_ENGINE_TABLEID_NOT_EXIST"><code>AMapException.CODE_AMAP_ENGINE_TABLEID_NOT_EXIST</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.AMAP_ENGINE_TABLEID_NOT_EXIST">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="AMAP_NEARBY_INVALID_USERID">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>AMAP_NEARBY_INVALID_USERID</h4>
<pre>public static final&nbsp;java.lang.String AMAP_NEARBY_INVALID_USERID</pre>
<div class="block">找不到对应的userid信息,请检查您提供的userid是否存在 ErrorCode：2100</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_NEARBY_INVALID_USERID"><code>AMapException.CODE_AMAP_NEARBY_INVALID_USERID</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.AMAP_NEARBY_INVALID_USERID">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="AMAP_NEARBY_KEY_NOT_BIND">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>AMAP_NEARBY_KEY_NOT_BIND</h4>
<pre>public static final&nbsp;java.lang.String AMAP_NEARBY_KEY_NOT_BIND</pre>
<div class="block">App key未开通“附近”功能,请注册附近KEY ErrorCode：2101</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_NEARBY_KEY_NOT_BIND"><code>AMapException.CODE_AMAP_NEARBY_KEY_NOT_BIND</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.AMAP_NEARBY_KEY_NOT_BIND">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="AMAP_CLIENT_UPLOADAUTO_STARTED_ERROR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>AMAP_CLIENT_UPLOADAUTO_STARTED_ERROR</h4>
<pre>public static final&nbsp;java.lang.String AMAP_CLIENT_UPLOADAUTO_STARTED_ERROR</pre>
<div class="block">已开启自动上传 ErrorCode：2200</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_CLIENT_UPLOADAUTO_STARTED_ERROR"><code>AMapException.CODE_AMAP_CLIENT_UPLOADAUTO_STARTED_ERROR</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.AMAP_CLIENT_UPLOADAUTO_STARTED_ERROR">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="AMAP_CLIENT_USERID_ILLEGAL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>AMAP_CLIENT_USERID_ILLEGAL</h4>
<pre>public static final&nbsp;java.lang.String AMAP_CLIENT_USERID_ILLEGAL</pre>
<div class="block">USERID非法 ErrorCode：2201</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_CLIENT_USERID_ILLEGAL"><code>AMapException.CODE_AMAP_CLIENT_USERID_ILLEGAL</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.AMAP_CLIENT_USERID_ILLEGAL">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="AMAP_CLIENT_NEARBY_NULL_RESULT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>AMAP_CLIENT_NEARBY_NULL_RESULT</h4>
<pre>public static final&nbsp;java.lang.String AMAP_CLIENT_NEARBY_NULL_RESULT</pre>
<div class="block">NearbyInfo对象为空 ErrorCode：2202</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_CLIENT_NEARBY_NULL_RESULT"><code>AMapException.CODE_AMAP_CLIENT_NEARBY_NULL_RESULT</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.AMAP_CLIENT_NEARBY_NULL_RESULT">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="AMAP_CLIENT_UPLOAD_TOO_FREQUENT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>AMAP_CLIENT_UPLOAD_TOO_FREQUENT</h4>
<pre>public static final&nbsp;java.lang.String AMAP_CLIENT_UPLOAD_TOO_FREQUENT</pre>
<div class="block">两次单次上传的间隔低于7秒 ErrorCode：2203</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_CLIENT_UPLOAD_TOO_FREQUENT"><code>AMapException.CODE_AMAP_CLIENT_UPLOAD_TOO_FREQUENT</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.AMAP_CLIENT_UPLOAD_TOO_FREQUENT">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="AMAP_CLIENT_UPLOAD_LOCATION_ERROR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>AMAP_CLIENT_UPLOAD_LOCATION_ERROR</h4>
<pre>public static final&nbsp;java.lang.String AMAP_CLIENT_UPLOAD_LOCATION_ERROR</pre>
<div class="block">Point为空，或与前次上传的相同 ErrorCode：2204</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_CLIENT_UPLOAD_LOCATION_ERROR"><code>AMapException.CODE_AMAP_CLIENT_UPLOAD_LOCATION_ERROR</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.AMAP_CLIENT_UPLOAD_LOCATION_ERROR">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="AMAP_ROUTE_OUT_OF_SERVICE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>AMAP_ROUTE_OUT_OF_SERVICE</h4>
<pre>public static final&nbsp;java.lang.String AMAP_ROUTE_OUT_OF_SERVICE</pre>
<div class="block">规划点（包括起点、终点、途经点）不在中国陆地范围内 ErrorCode：3000</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_ROUTE_OUT_OF_SERVICE"><code>AMapException.CODE_AMAP_ROUTE_OUT_OF_SERVICE</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.AMAP_ROUTE_OUT_OF_SERVICE">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="AMAP_ROUTE_NO_ROADS_NEARBY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>AMAP_ROUTE_NO_ROADS_NEARBY</h4>
<pre>public static final&nbsp;java.lang.String AMAP_ROUTE_NO_ROADS_NEARBY</pre>
<div class="block">规划点（起点、终点、途经点）附近搜不到路 ErrorCode：3001</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_ROUTE_NO_ROADS_NEARBY"><code>AMapException.CODE_AMAP_ROUTE_NO_ROADS_NEARBY</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.AMAP_ROUTE_NO_ROADS_NEARBY">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="AMAP_ROUTE_FAIL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>AMAP_ROUTE_FAIL</h4>
<pre>public static final&nbsp;java.lang.String AMAP_ROUTE_FAIL</pre>
<div class="block">路线计算失败，通常是由于道路连通关系导致 ErrorCode：3002</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_ROUTE_FAIL"><code>AMapException.CODE_AMAP_ROUTE_FAIL</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.AMAP_ROUTE_FAIL">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="AMAP_OVER_DIRECTION_RANGE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>AMAP_OVER_DIRECTION_RANGE</h4>
<pre>public static final&nbsp;java.lang.String AMAP_OVER_DIRECTION_RANGE</pre>
<div class="block">起点终点距离过长 ErrorCode：3003</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_OVER_DIRECTION_RANGE"><code>AMapException.CODE_AMAP_OVER_DIRECTION_RANGE</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.AMAP_OVER_DIRECTION_RANGE">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="AMAP_SHARE_LICENSE_IS_EXPIRED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>AMAP_SHARE_LICENSE_IS_EXPIRED</h4>
<pre>public static final&nbsp;java.lang.String AMAP_SHARE_LICENSE_IS_EXPIRED</pre>
<div class="block">短串分享认证失败 ErrorCode：4000</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_SHARE_LICENSE_IS_EXPIRED"><code>AMapException.CODE_AMAP_SHARE_LICENSE_IS_EXPIRED</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.AMAP_SHARE_LICENSE_IS_EXPIRED">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="AMAP_CLIENT_OVER_PASSBY_MAX_COUNT_EXCEPTION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>AMAP_CLIENT_OVER_PASSBY_MAX_COUNT_EXCEPTION</h4>
<pre>public static final&nbsp;java.lang.String AMAP_CLIENT_OVER_PASSBY_MAX_COUNT_EXCEPTION</pre>
<div class="block">途经点个数超限</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>7.6.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.AMAP_CLIENT_OVER_PASSBY_MAX_COUNT_EXCEPTION">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="AMAP_CLIENT_OVER_PASSAREA_MAX_COUNT_EXCEPTION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>AMAP_CLIENT_OVER_PASSAREA_MAX_COUNT_EXCEPTION</h4>
<pre>public static final&nbsp;java.lang.String AMAP_CLIENT_OVER_PASSAREA_MAX_COUNT_EXCEPTION</pre>
<div class="block">避让区域个数超限</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>7.6.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.AMAP_CLIENT_OVER_PASSAREA_MAX_COUNT_EXCEPTION">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="AMAP_CLIENT_OVER_PASSAREA_MAX_AREA_EXCEPTION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>AMAP_CLIENT_OVER_PASSAREA_MAX_AREA_EXCEPTION</h4>
<pre>public static final&nbsp;java.lang.String AMAP_CLIENT_OVER_PASSAREA_MAX_AREA_EXCEPTION</pre>
<div class="block">避让区域大小超限</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>7.6.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.AMAP_CLIENT_OVER_PASSAREA_MAX_AREA_EXCEPTION">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="AMAP_CLIENT_OVER_PASSAREA_ITEM_POINT_COUNT_EXCEPTION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>AMAP_CLIENT_OVER_PASSAREA_ITEM_POINT_COUNT_EXCEPTION</h4>
<pre>public static final&nbsp;java.lang.String AMAP_CLIENT_OVER_PASSAREA_ITEM_POINT_COUNT_EXCEPTION</pre>
<div class="block">避让区域点个数超限</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>7.6.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.AMAP_CLIENT_OVER_PASSAREA_ITEM_POINT_COUNT_EXCEPTION">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="AMAP_CLIENT_OVER_KEYWORD_LEN_MAX_COUNT_EXCEPTION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>AMAP_CLIENT_OVER_KEYWORD_LEN_MAX_COUNT_EXCEPTION</h4>
<pre>public static final&nbsp;java.lang.String AMAP_CLIENT_OVER_KEYWORD_LEN_MAX_COUNT_EXCEPTION</pre>
<div class="block">搜索关键字过长</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>7.7.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.AMAP_CLIENT_OVER_KEYWORD_LEN_MAX_COUNT_EXCEPTION">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="AMAP_SHARE_FAILURE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>AMAP_SHARE_FAILURE</h4>
<pre>public static final&nbsp;java.lang.String AMAP_SHARE_FAILURE</pre>
<div class="block">短串请求失败 ErrorCode：4001</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_SHARE_FAILURE"><code>AMapException.CODE_AMAP_SHARE_FAILURE</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.AMAP_SHARE_FAILURE">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="AMAP_SHARE_SIGNATURE_FAILURE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>AMAP_SHARE_SIGNATURE_FAILURE</h4>
<pre>public static final&nbsp;java.lang.String AMAP_SHARE_SIGNATURE_FAILURE</pre>
<div class="block">短串分享用户签名未通过 ErrorCode：4002</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>5.3.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/AMapException.html#CODE_AMAP_SHARE_SIGNATURE_FAILURE"><code>AMapException.CODE_AMAP_SHARE_SIGNATURE_FAILURE</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.AMAP_SHARE_SIGNATURE_FAILURE">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="CODE_AMAP_SIGNATURE_ERROR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CODE_AMAP_SIGNATURE_ERROR</h4>
<pre>public static final&nbsp;int CODE_AMAP_SIGNATURE_ERROR</pre>
<div class="block">用户签名未通过 ErrorCode：1001</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_SIGNATURE_ERROR"><code>AMapException.AMAP_SIGNATURE_ERROR</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.CODE_AMAP_SIGNATURE_ERROR">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="CODE_AMAP_INVALID_USER_KEY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CODE_AMAP_INVALID_USER_KEY</h4>
<pre>public static final&nbsp;int CODE_AMAP_INVALID_USER_KEY</pre>
<div class="block">用户key不正确或过期 ErrorCode：1002</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_INVALID_USER_KEY"><code>AMapException.AMAP_INVALID_USER_KEY</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.CODE_AMAP_INVALID_USER_KEY">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="CODE_AMAP_SERVICE_NOT_AVAILBALE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CODE_AMAP_SERVICE_NOT_AVAILBALE</h4>
<pre>public static final&nbsp;int CODE_AMAP_SERVICE_NOT_AVAILBALE</pre>
<div class="block">请求服务不存在 ErrorCode：1003</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_SERVICE_NOT_AVAILBALE"><code>AMapException.AMAP_SERVICE_NOT_AVAILBALE</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.CODE_AMAP_SERVICE_NOT_AVAILBALE">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="CODE_AMAP_DAILY_QUERY_OVER_LIMIT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CODE_AMAP_DAILY_QUERY_OVER_LIMIT</h4>
<pre>public static final&nbsp;int CODE_AMAP_DAILY_QUERY_OVER_LIMIT</pre>
<div class="block">访问已超出日访问量 ErrorCode：1004</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_DAILY_QUERY_OVER_LIMIT"><code>AMapException.AMAP_DAILY_QUERY_OVER_LIMIT</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.CODE_AMAP_DAILY_QUERY_OVER_LIMIT">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="CODE_AMAP_ACCESS_TOO_FREQUENT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CODE_AMAP_ACCESS_TOO_FREQUENT</h4>
<pre>public static final&nbsp;int CODE_AMAP_ACCESS_TOO_FREQUENT</pre>
<div class="block">用户访问过于频繁 ErrorCode：1005</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_ACCESS_TOO_FREQUENT"><code>AMapException.AMAP_ACCESS_TOO_FREQUENT</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.CODE_AMAP_ACCESS_TOO_FREQUENT">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="CODE_AMAP_INVALID_USER_IP">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CODE_AMAP_INVALID_USER_IP</h4>
<pre>public static final&nbsp;int CODE_AMAP_INVALID_USER_IP</pre>
<div class="block">用户IP无效 ErrorCode：1006</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_INVALID_USER_IP"><code>AMapException.AMAP_INVALID_USER_IP</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.CODE_AMAP_INVALID_USER_IP">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="CODE_AMAP_INVALID_USER_DOMAIN">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CODE_AMAP_INVALID_USER_DOMAIN</h4>
<pre>public static final&nbsp;int CODE_AMAP_INVALID_USER_DOMAIN</pre>
<div class="block">用户域名无效 ErrorCode：1007</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_INVALID_USER_DOMAIN"><code>AMapException.AMAP_INVALID_USER_DOMAIN</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.CODE_AMAP_INVALID_USER_DOMAIN">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="CODE_AMAP_INVALID_USER_SCODE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CODE_AMAP_INVALID_USER_SCODE</h4>
<pre>public static final&nbsp;int CODE_AMAP_INVALID_USER_SCODE</pre>
<div class="block">用户MD5安全码未通过 ErrorCode：1008</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_INVALID_USER_SCODE"><code>AMapException.AMAP_INVALID_USER_SCODE</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.CODE_AMAP_INVALID_USER_SCODE">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="CODE_AMAP_USERKEY_PLAT_NOMATCH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CODE_AMAP_USERKEY_PLAT_NOMATCH</h4>
<pre>public static final&nbsp;int CODE_AMAP_USERKEY_PLAT_NOMATCH</pre>
<div class="block">请求key与绑定平台不符 ErrorCode：1009</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_USERKEY_PLAT_NOMATCH"><code>AMapException.AMAP_USERKEY_PLAT_NOMATCH</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.CODE_AMAP_USERKEY_PLAT_NOMATCH">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="CODE_AMAP_IP_QUERY_OVER_LIMIT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CODE_AMAP_IP_QUERY_OVER_LIMIT</h4>
<pre>public static final&nbsp;int CODE_AMAP_IP_QUERY_OVER_LIMIT</pre>
<div class="block">IP访问超限 ErrorCode：1010</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_IP_QUERY_OVER_LIMIT"><code>AMapException.AMAP_IP_QUERY_OVER_LIMIT</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.CODE_AMAP_IP_QUERY_OVER_LIMIT">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="CODE_AMAP_NOT_SUPPORT_HTTPS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CODE_AMAP_NOT_SUPPORT_HTTPS</h4>
<pre>public static final&nbsp;int CODE_AMAP_NOT_SUPPORT_HTTPS</pre>
<div class="block">服务不支持https请求 ErrorCode：1011</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_NOT_SUPPORT_HTTPS"><code>AMapException.AMAP_NOT_SUPPORT_HTTPS</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.CODE_AMAP_NOT_SUPPORT_HTTPS">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="CODE_AMAP_INSUFFICIENT_PRIVILEGES">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CODE_AMAP_INSUFFICIENT_PRIVILEGES</h4>
<pre>public static final&nbsp;int CODE_AMAP_INSUFFICIENT_PRIVILEGES</pre>
<div class="block">权限不足，服务请求被拒绝 ErrorCode：1012</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_INSUFFICIENT_PRIVILEGES"><code>AMapException.AMAP_INSUFFICIENT_PRIVILEGES</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.CODE_AMAP_INSUFFICIENT_PRIVILEGES">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="CODE_AMAP_USER_KEY_RECYCLED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CODE_AMAP_USER_KEY_RECYCLED</h4>
<pre>public static final&nbsp;int CODE_AMAP_USER_KEY_RECYCLED</pre>
<div class="block">开发者删除了key，key被删除后无法正常使用 ErrorCode：1013</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_USER_KEY_RECYCLED"><code>AMapException.AMAP_USER_KEY_RECYCLED</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.CODE_AMAP_USER_KEY_RECYCLED">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="CODE_AMAP_ENGINE_RESPONSE_ERROR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CODE_AMAP_ENGINE_RESPONSE_ERROR</h4>
<pre>public static final&nbsp;int CODE_AMAP_ENGINE_RESPONSE_ERROR</pre>
<div class="block">请求服务响应错误 ErrorCode：1100</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_ENGINE_RESPONSE_ERROR"><code>AMapException.AMAP_ENGINE_RESPONSE_ERROR</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.CODE_AMAP_ENGINE_RESPONSE_ERROR">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="CODE_AMAP_ENGINE_RESPONSE_DATA_ERROR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CODE_AMAP_ENGINE_RESPONSE_DATA_ERROR</h4>
<pre>public static final&nbsp;int CODE_AMAP_ENGINE_RESPONSE_DATA_ERROR</pre>
<div class="block">引擎返回数据异常 ErrorCode：1101</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_ENGINE_RESPONSE_DATA_ERROR"><code>AMapException.AMAP_ENGINE_RESPONSE_DATA_ERROR</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.CODE_AMAP_ENGINE_RESPONSE_DATA_ERROR">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="CODE_AMAP_ENGINE_CONNECT_TIMEOUT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CODE_AMAP_ENGINE_CONNECT_TIMEOUT</h4>
<pre>public static final&nbsp;int CODE_AMAP_ENGINE_CONNECT_TIMEOUT</pre>
<div class="block">服务端请求链接超时 ErrorCode：1102</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_ENGINE_CONNECT_TIMEOUT"><code>AMapException.AMAP_ENGINE_CONNECT_TIMEOUT</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.CODE_AMAP_ENGINE_CONNECT_TIMEOUT">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="CODE_AMAP_ENGINE_RETURN_TIMEOUT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CODE_AMAP_ENGINE_RETURN_TIMEOUT</h4>
<pre>public static final&nbsp;int CODE_AMAP_ENGINE_RETURN_TIMEOUT</pre>
<div class="block">读取服务结果超时 ErrorCode：1103</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_ENGINE_RETURN_TIMEOUT"><code>AMapException.AMAP_ENGINE_RETURN_TIMEOUT</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.CODE_AMAP_ENGINE_RETURN_TIMEOUT">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="CODE_AMAP_SERVICE_INVALID_PARAMS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CODE_AMAP_SERVICE_INVALID_PARAMS</h4>
<pre>public static final&nbsp;int CODE_AMAP_SERVICE_INVALID_PARAMS</pre>
<div class="block">请求参数非法 ErrorCode：1200</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_SERVICE_INVALID_PARAMS"><code>AMapException.AMAP_SERVICE_INVALID_PARAMS</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.CODE_AMAP_SERVICE_INVALID_PARAMS">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="CODE_AMAP_SERVICE_MISSING_REQUIRED_PARAMS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CODE_AMAP_SERVICE_MISSING_REQUIRED_PARAMS</h4>
<pre>public static final&nbsp;int CODE_AMAP_SERVICE_MISSING_REQUIRED_PARAMS</pre>
<div class="block">缺少必填参数 ErrorCode：1201</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_SERVICE_MISSING_REQUIRED_PARAMS"><code>AMapException.AMAP_SERVICE_MISSING_REQUIRED_PARAMS</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.CODE_AMAP_SERVICE_MISSING_REQUIRED_PARAMS">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="CODE_AMAP_SERVICE_ILLEGAL_REQUEST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CODE_AMAP_SERVICE_ILLEGAL_REQUEST</h4>
<pre>public static final&nbsp;int CODE_AMAP_SERVICE_ILLEGAL_REQUEST</pre>
<div class="block">请求协议非法 ErrorCode：1202</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_SERVICE_ILLEGAL_REQUEST"><code>AMapException.AMAP_SERVICE_ILLEGAL_REQUEST</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.CODE_AMAP_SERVICE_ILLEGAL_REQUEST">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="CODE_AMAP_SERVICE_UNKNOWN_ERROR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CODE_AMAP_SERVICE_UNKNOWN_ERROR</h4>
<pre>public static final&nbsp;int CODE_AMAP_SERVICE_UNKNOWN_ERROR</pre>
<div class="block">其他未知错误 ErrorCode：1203</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_SERVICE_UNKNOWN_ERROR"><code>AMapException.AMAP_SERVICE_UNKNOWN_ERROR</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.CODE_AMAP_SERVICE_UNKNOWN_ERROR">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="CODE_AMAP_CLIENT_ERRORCODE_MISSSING">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CODE_AMAP_CLIENT_ERRORCODE_MISSSING</h4>
<pre>public static final&nbsp;int CODE_AMAP_CLIENT_ERRORCODE_MISSSING</pre>
<div class="block">没有对应的错误 ErrorCode：1800</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_CLIENT_ERRORCODE_MISSSING"><code>AMapException.AMAP_CLIENT_ERRORCODE_MISSSING</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.CODE_AMAP_CLIENT_ERRORCODE_MISSSING">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="CODE_AMAP_CLIENT_ERROR_PROTOCOL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CODE_AMAP_CLIENT_ERROR_PROTOCOL</h4>
<pre>public static final&nbsp;int CODE_AMAP_CLIENT_ERROR_PROTOCOL</pre>
<div class="block">协议解析错误 - ProtocolException ErrorCode：1801</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_CLIENT_ERROR_PROTOCOL"><code>AMapException.AMAP_CLIENT_ERROR_PROTOCOL</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.CODE_AMAP_CLIENT_ERROR_PROTOCOL">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="CODE_AMAP_CLIENT_SOCKET_TIMEOUT_EXCEPTION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CODE_AMAP_CLIENT_SOCKET_TIMEOUT_EXCEPTION</h4>
<pre>public static final&nbsp;int CODE_AMAP_CLIENT_SOCKET_TIMEOUT_EXCEPTION</pre>
<div class="block">socket 连接超时 - SocketTimeoutException ErrorCode：1802</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_CLIENT_SOCKET_TIMEOUT_EXCEPTION"><code>AMapException.AMAP_CLIENT_SOCKET_TIMEOUT_EXCEPTION</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.CODE_AMAP_CLIENT_SOCKET_TIMEOUT_EXCEPTION">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="CODE_AMAP_CLIENT_URL_EXCEPTION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CODE_AMAP_CLIENT_URL_EXCEPTION</h4>
<pre>public static final&nbsp;int CODE_AMAP_CLIENT_URL_EXCEPTION</pre>
<div class="block">url异常 - MalformedURLException ErrorCode：1803</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_CLIENT_URL_EXCEPTION"><code>AMapException.AMAP_CLIENT_URL_EXCEPTION</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.CODE_AMAP_CLIENT_URL_EXCEPTION">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="CODE_AMAP_CLIENT_UNKNOWHOST_EXCEPTION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CODE_AMAP_CLIENT_UNKNOWHOST_EXCEPTION</h4>
<pre>public static final&nbsp;int CODE_AMAP_CLIENT_UNKNOWHOST_EXCEPTION</pre>
<div class="block">未知主机 - UnKnowHostException ErrorCode：1804</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_CLIENT_UNKNOWHOST_EXCEPTION"><code>AMapException.AMAP_CLIENT_UNKNOWHOST_EXCEPTION</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.CODE_AMAP_CLIENT_UNKNOWHOST_EXCEPTION">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="CODE_AMAP_CLIENT_NETWORK_EXCEPTION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CODE_AMAP_CLIENT_NETWORK_EXCEPTION</h4>
<pre>public static final&nbsp;int CODE_AMAP_CLIENT_NETWORK_EXCEPTION</pre>
<div class="block">http或socket连接失败 - ConnectionException ErrorCode：1806</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_CLIENT_NETWORK_EXCEPTION"><code>AMapException.AMAP_CLIENT_NETWORK_EXCEPTION</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.CODE_AMAP_CLIENT_NETWORK_EXCEPTION">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="CODE_AMAP_CLIENT_OVER_PASSBY_MAX_COUNT_EXCEPTION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CODE_AMAP_CLIENT_OVER_PASSBY_MAX_COUNT_EXCEPTION</h4>
<pre>public static final&nbsp;int CODE_AMAP_CLIENT_OVER_PASSBY_MAX_COUNT_EXCEPTION</pre>
<div class="block">途经点个数超限</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>7.6.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.CODE_AMAP_CLIENT_OVER_PASSBY_MAX_COUNT_EXCEPTION">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="CODE_AMAP_CLIENT_OVER_PASSAREA_MAX_COUNT_EXCEPTION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CODE_AMAP_CLIENT_OVER_PASSAREA_MAX_COUNT_EXCEPTION</h4>
<pre>public static final&nbsp;int CODE_AMAP_CLIENT_OVER_PASSAREA_MAX_COUNT_EXCEPTION</pre>
<div class="block">避让区域个数超限</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>7.6.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.CODE_AMAP_CLIENT_OVER_PASSAREA_MAX_COUNT_EXCEPTION">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="CODE_AMAP_CLIENT_OVER_PASSAREA_MAX_AREA_EXCEPTION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CODE_AMAP_CLIENT_OVER_PASSAREA_MAX_AREA_EXCEPTION</h4>
<pre>public static final&nbsp;int CODE_AMAP_CLIENT_OVER_PASSAREA_MAX_AREA_EXCEPTION</pre>
<div class="block">避让区域大小超限</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>7.6.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.CODE_AMAP_CLIENT_OVER_PASSAREA_MAX_AREA_EXCEPTION">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="CODE_AMAP_CLIENT_OVER_PASSAREA_ITEM_POINT_COUNT_EXCEPTION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CODE_AMAP_CLIENT_OVER_PASSAREA_ITEM_POINT_COUNT_EXCEPTION</h4>
<pre>public static final&nbsp;int CODE_AMAP_CLIENT_OVER_PASSAREA_ITEM_POINT_COUNT_EXCEPTION</pre>
<div class="block">避让区域点个数超限</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>7.6.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.CODE_AMAP_CLIENT_OVER_PASSAREA_ITEM_POINT_COUNT_EXCEPTION">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="CODE_AMAP_CLIENT_OVER_KEYWORD_LEN_MAX_COUNT_EXCEPTION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CODE_AMAP_CLIENT_OVER_KEYWORD_LEN_MAX_COUNT_EXCEPTION</h4>
<pre>public static final&nbsp;int CODE_AMAP_CLIENT_OVER_KEYWORD_LEN_MAX_COUNT_EXCEPTION</pre>
<div class="block">搜索关键字过长</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>7.7.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.CODE_AMAP_CLIENT_OVER_KEYWORD_LEN_MAX_COUNT_EXCEPTION">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="CODE_AMAP_CLIENT_UNKNOWN_ERROR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CODE_AMAP_CLIENT_UNKNOWN_ERROR</h4>
<pre>public static final&nbsp;int CODE_AMAP_CLIENT_UNKNOWN_ERROR</pre>
<div class="block">未知错误 ErrorCode：1900</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_CLIENT_UNKNOWN_ERROR"><code>AMapException.AMAP_CLIENT_UNKNOWN_ERROR</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.CODE_AMAP_CLIENT_UNKNOWN_ERROR">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="CODE_AMAP_CLIENT_INVALID_PARAMETER">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CODE_AMAP_CLIENT_INVALID_PARAMETER</h4>
<pre>public static final&nbsp;int CODE_AMAP_CLIENT_INVALID_PARAMETER</pre>
<div class="block">无效的参数 - IllegalArgumentException ErrorCode：1901</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_CLIENT_INVALID_PARAMETER"><code>AMapException.AMAP_CLIENT_INVALID_PARAMETER</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.CODE_AMAP_CLIENT_INVALID_PARAMETER">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="CODE_AMAP_CLIENT_IO_EXCEPTION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CODE_AMAP_CLIENT_IO_EXCEPTION</h4>
<pre>public static final&nbsp;int CODE_AMAP_CLIENT_IO_EXCEPTION</pre>
<div class="block">IO 操作异常 - IOException ErrorCode：1902</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_CLIENT_IO_EXCEPTION"><code>AMapException.AMAP_CLIENT_IO_EXCEPTION</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.CODE_AMAP_CLIENT_IO_EXCEPTION">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="CODE_AMAP_CLIENT_NULLPOINT_EXCEPTION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CODE_AMAP_CLIENT_NULLPOINT_EXCEPTION</h4>
<pre>public static final&nbsp;int CODE_AMAP_CLIENT_NULLPOINT_EXCEPTION</pre>
<div class="block">空指针异常 - NullPointException ErrorCode：1903</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_CLIENT_NULLPOINT_EXCEPTION"><code>AMapException.AMAP_CLIENT_NULLPOINT_EXCEPTION</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.CODE_AMAP_CLIENT_NULLPOINT_EXCEPTION">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="CODE_AMAP_SERVICE_TABLEID_NOT_EXIST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CODE_AMAP_SERVICE_TABLEID_NOT_EXIST</h4>
<pre>public static final&nbsp;int CODE_AMAP_SERVICE_TABLEID_NOT_EXIST</pre>
<div class="block">tableID格式不正确不存在 ErrorCode：2000</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_SERVICE_TABLEID_NOT_EXIST"><code>AMapException.AMAP_SERVICE_TABLEID_NOT_EXIST</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.CODE_AMAP_SERVICE_TABLEID_NOT_EXIST">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="CODE_AMAP_ID_NOT_EXIST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CODE_AMAP_ID_NOT_EXIST</h4>
<pre>public static final&nbsp;int CODE_AMAP_ID_NOT_EXIST</pre>
<div class="block">ID不存在 ErrorCode：2001</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_ID_NOT_EXIST"><code>AMapException.AMAP_ID_NOT_EXIST</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.CODE_AMAP_ID_NOT_EXIST">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="CODE_AMAP_SERVICE_MAINTENANCE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CODE_AMAP_SERVICE_MAINTENANCE</h4>
<pre>public static final&nbsp;int CODE_AMAP_SERVICE_MAINTENANCE</pre>
<div class="block">服务器维护中 ErrorCode：2002</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_SERVICE_MAINTENANCE"><code>AMapException.AMAP_SERVICE_MAINTENANCE</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.CODE_AMAP_SERVICE_MAINTENANCE">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="CODE_AMAP_ENGINE_TABLEID_NOT_EXIST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CODE_AMAP_ENGINE_TABLEID_NOT_EXIST</h4>
<pre>public static final&nbsp;int CODE_AMAP_ENGINE_TABLEID_NOT_EXIST</pre>
<div class="block">key对应的tableID不存在 ErrorCode：2003</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_ENGINE_TABLEID_NOT_EXIST"><code>AMapException.AMAP_ENGINE_TABLEID_NOT_EXIST</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.CODE_AMAP_ENGINE_TABLEID_NOT_EXIST">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="CODE_AMAP_NEARBY_INVALID_USERID">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CODE_AMAP_NEARBY_INVALID_USERID</h4>
<pre>public static final&nbsp;int CODE_AMAP_NEARBY_INVALID_USERID</pre>
<div class="block">找不到对应的userid信息,请检查您提供的userid是否存在 ErrorCode：2100</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_NEARBY_INVALID_USERID"><code>AMapException.AMAP_NEARBY_INVALID_USERID</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.CODE_AMAP_NEARBY_INVALID_USERID">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="CODE_AMAP_NEARBY_KEY_NOT_BIND">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CODE_AMAP_NEARBY_KEY_NOT_BIND</h4>
<pre>public static final&nbsp;int CODE_AMAP_NEARBY_KEY_NOT_BIND</pre>
<div class="block">App key未开通“附近”功能,请注册附近KEY ErrorCode：2101</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_NEARBY_KEY_NOT_BIND"><code>AMapException.AMAP_NEARBY_KEY_NOT_BIND</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.CODE_AMAP_NEARBY_KEY_NOT_BIND">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="CODE_AMAP_CLIENT_UPLOADAUTO_STARTED_ERROR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CODE_AMAP_CLIENT_UPLOADAUTO_STARTED_ERROR</h4>
<pre>public static final&nbsp;int CODE_AMAP_CLIENT_UPLOADAUTO_STARTED_ERROR</pre>
<div class="block">已开启自动上传 ErrorCode：2200</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_CLIENT_UPLOADAUTO_STARTED_ERROR"><code>AMapException.AMAP_CLIENT_UPLOADAUTO_STARTED_ERROR</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.CODE_AMAP_CLIENT_UPLOADAUTO_STARTED_ERROR">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="CODE_AMAP_CLIENT_USERID_ILLEGAL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CODE_AMAP_CLIENT_USERID_ILLEGAL</h4>
<pre>public static final&nbsp;int CODE_AMAP_CLIENT_USERID_ILLEGAL</pre>
<div class="block">USERID非法 ErrorCode：2201</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_CLIENT_USERID_ILLEGAL"><code>AMapException.AMAP_CLIENT_USERID_ILLEGAL</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.CODE_AMAP_CLIENT_USERID_ILLEGAL">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="CODE_AMAP_CLIENT_NEARBY_NULL_RESULT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CODE_AMAP_CLIENT_NEARBY_NULL_RESULT</h4>
<pre>public static final&nbsp;int CODE_AMAP_CLIENT_NEARBY_NULL_RESULT</pre>
<div class="block">NearbyInfo对象为空 ErrorCode：2202</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_CLIENT_NEARBY_NULL_RESULT"><code>AMapException.AMAP_CLIENT_NEARBY_NULL_RESULT</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.CODE_AMAP_CLIENT_NEARBY_NULL_RESULT">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="CODE_AMAP_CLIENT_UPLOAD_TOO_FREQUENT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CODE_AMAP_CLIENT_UPLOAD_TOO_FREQUENT</h4>
<pre>public static final&nbsp;int CODE_AMAP_CLIENT_UPLOAD_TOO_FREQUENT</pre>
<div class="block">两次单次上传的间隔低于7秒 ErrorCode：2203</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_CLIENT_UPLOAD_TOO_FREQUENT"><code>AMapException.AMAP_CLIENT_UPLOAD_TOO_FREQUENT</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.CODE_AMAP_CLIENT_UPLOAD_TOO_FREQUENT">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="CODE_AMAP_CLIENT_UPLOAD_LOCATION_ERROR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CODE_AMAP_CLIENT_UPLOAD_LOCATION_ERROR</h4>
<pre>public static final&nbsp;int CODE_AMAP_CLIENT_UPLOAD_LOCATION_ERROR</pre>
<div class="block">Point为空，或与前次上传的相同 ErrorCode：2204</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_CLIENT_UPLOAD_LOCATION_ERROR"><code>AMapException.AMAP_CLIENT_UPLOAD_LOCATION_ERROR</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.CODE_AMAP_CLIENT_UPLOAD_LOCATION_ERROR">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="CODE_AMAP_ROUTE_OUT_OF_SERVICE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CODE_AMAP_ROUTE_OUT_OF_SERVICE</h4>
<pre>public static final&nbsp;int CODE_AMAP_ROUTE_OUT_OF_SERVICE</pre>
<div class="block">规划点（包括起点、终点、途经点）不在中国陆地范围内 ErrorCode：3000</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_ROUTE_OUT_OF_SERVICE"><code>AMapException.AMAP_ROUTE_OUT_OF_SERVICE</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.CODE_AMAP_ROUTE_OUT_OF_SERVICE">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="CODE_AMAP_ROUTE_NO_ROADS_NEARBY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CODE_AMAP_ROUTE_NO_ROADS_NEARBY</h4>
<pre>public static final&nbsp;int CODE_AMAP_ROUTE_NO_ROADS_NEARBY</pre>
<div class="block">规划点（起点、终点、途经点）附近搜不到路 ErrorCode：3001</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_ROUTE_NO_ROADS_NEARBY"><code>AMapException.AMAP_ROUTE_NO_ROADS_NEARBY</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.CODE_AMAP_ROUTE_NO_ROADS_NEARBY">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="CODE_AMAP_ROUTE_FAIL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CODE_AMAP_ROUTE_FAIL</h4>
<pre>public static final&nbsp;int CODE_AMAP_ROUTE_FAIL</pre>
<div class="block">路线计算失败，通常是由于道路连通关系导致 ErrorCode：3002</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_ROUTE_FAIL"><code>AMapException.AMAP_ROUTE_FAIL</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.CODE_AMAP_ROUTE_FAIL">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="CODE_AMAP_OVER_DIRECTION_RANGE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CODE_AMAP_OVER_DIRECTION_RANGE</h4>
<pre>public static final&nbsp;int CODE_AMAP_OVER_DIRECTION_RANGE</pre>
<div class="block">起点终点距离过长 ErrorCode：3003</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_OVER_DIRECTION_RANGE"><code>AMapException.AMAP_OVER_DIRECTION_RANGE</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.CODE_AMAP_OVER_DIRECTION_RANGE">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="CODE_AMAP_SHARE_LICENSE_IS_EXPIRED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CODE_AMAP_SHARE_LICENSE_IS_EXPIRED</h4>
<pre>public static final&nbsp;int CODE_AMAP_SHARE_LICENSE_IS_EXPIRED</pre>
<div class="block">短串分享认证失败 ErrorCode：4000</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_SHARE_LICENSE_IS_EXPIRED"><code>AMapException.AMAP_SHARE_LICENSE_IS_EXPIRED</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.CODE_AMAP_SHARE_LICENSE_IS_EXPIRED">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="CODE_AMAP_SHARE_FAILURE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CODE_AMAP_SHARE_FAILURE</h4>
<pre>public static final&nbsp;int CODE_AMAP_SHARE_FAILURE</pre>
<div class="block">短串请求失败 ErrorCode：4001</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_SHARE_FAILURE"><code>AMapException.AMAP_SHARE_FAILURE</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.CODE_AMAP_SHARE_FAILURE">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="CODE_AMAP_SHARE_SIGNATURE_FAILURE">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>CODE_AMAP_SHARE_SIGNATURE_FAILURE</h4>
<pre>public static final&nbsp;int CODE_AMAP_SHARE_SIGNATURE_FAILURE</pre>
<div class="block">短串分享用户签名未通过 ErrorCode：4002</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>5.3.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/amap/api/services/core/AMapException.html#AMAP_SHARE_SIGNATURE_FAILURE"><code>AMapException.AMAP_SHARE_SIGNATURE_FAILURE</code></a>, 
<a href="../../../../../constant-values.html#com.amap.api.services.core.AMapException.CODE_AMAP_SHARE_SIGNATURE_FAILURE">常量字段值</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="AMapException-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>AMapException</h4>
<pre>public&nbsp;AMapException(java.lang.String&nbsp;errorMessage)</pre>
<div class="block">构造服务异常对象。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>errorMessage</code> - 输入的异常信息。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>1.2.0</dd>
</dl>
</li>
</ul>
<a name="AMapException--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>AMapException</h4>
<pre>public&nbsp;AMapException()</pre>
<div class="block">构造服务异常对象。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>1.2.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="getErrorMessage--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getErrorMessage</h4>
<pre>public&nbsp;java.lang.String&nbsp;getErrorMessage()</pre>
<div class="block">返回异常信息。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>异常信息。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>1.2.0</dd>
</dl>
</li>
</ul>
<a name="getErrorCode--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getErrorCode</h4>
<pre>public&nbsp;int&nbsp;getErrorCode()</pre>
<div class="block">返回错误码。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>错误码。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/AMapException.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个类</li>
<li><a href="../../../../../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/services/core/AMapException.html" target="_top">框架</a></li>
<li><a href="AMapException.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li><a href="#field.summary">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li><a href="#field.detail">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
