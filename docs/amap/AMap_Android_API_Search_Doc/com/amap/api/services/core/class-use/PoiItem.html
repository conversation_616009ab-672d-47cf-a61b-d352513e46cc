<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>类 com.amap.api.services.core.PoiItem的使用</title>
<link rel="stylesheet" type="text/css" href="../../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="\u7C7B com.amap.api.services.core.PoiItem\u7684\u4F7F\u7528";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../../overview-summary.html">概览</a></li>
<li><a href="../package-summary.html">程序包</a></li>
<li><a href="../../../../../../com/amap/api/services/core/PoiItem.html" title="com.amap.api.services.core中的类">类</a></li>
<li class="navBarCell1Rev">使用</li>
<li><a href="../../../../../../overview-tree.html">树</a></li>
<li><a href="../../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个</li>
<li>下一个</li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?com/amap/api/services/core/class-use/PoiItem.html" target="_top">框架</a></li>
<li><a href="PoiItem.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h2 title="类的使用 com.amap.api.services.core.PoiItem" class="title">类的使用<br>com.amap.api.services.core.PoiItem</h2>
</div>
<div class="classUseContainer">
<ul class="blockList">
<li class="blockList">
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表程序包和解释">
<caption><span>使用<a href="../../../../../../com/amap/api/services/core/PoiItem.html" title="com.amap.api.services.core中的类">PoiItem</a>的程序包</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">程序包</th>
<th class="colLast" scope="col">说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="#com.amap.api.services.geocoder">com.amap.api.services.geocoder</a></td>
<td class="colLast">
<div class="block">
地理编码包，用以实现通过地理编码进行查询的功能。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.amap.api.services.poisearch">com.amap.api.services.poisearch</a></td>
<td class="colLast">
<div class="block">
POI查询包，包含了兴趣点的详细信息。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#com.amap.api.services.share">com.amap.api.services.share</a></td>
<td class="colLast">
<div class="block">
短串分享包，包含了位置/POI/路径规划/导航的分享功能。</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<ul class="blockList">
<li class="blockList"><a name="com.amap.api.services.geocoder">
<!--   -->
</a>
<h3><a href="../../../../../../com/amap/api/services/geocoder/package-summary.html">com.amap.api.services.geocoder</a>中<a href="../../../../../../com/amap/api/services/core/PoiItem.html" title="com.amap.api.services.core中的类">PoiItem</a>的使用</h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表方法和解释">
<caption><span>返回变量类型为<a href="../../../../../../com/amap/api/services/core/PoiItem.html" title="com.amap.api.services.core中的类">PoiItem</a>的类型的<a href="../../../../../../com/amap/api/services/geocoder/package-summary.html">com.amap.api.services.geocoder</a>中的方法</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../../../com/amap/api/services/core/PoiItem.html" title="com.amap.api.services.core中的类">PoiItem</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">RegeocodeAddress.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/geocoder/RegeocodeAddress.html#getPois--">getPois</a></span>()</code>
<div class="block">逆地理编码返回的POI(兴趣点)列表。</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.amap.api.services.poisearch">
<!--   -->
</a>
<h3><a href="../../../../../../com/amap/api/services/poisearch/package-summary.html">com.amap.api.services.poisearch</a>中<a href="../../../../../../com/amap/api/services/core/PoiItem.html" title="com.amap.api.services.core中的类">PoiItem</a>的使用</h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表方法和解释">
<caption><span>返回<a href="../../../../../../com/amap/api/services/core/PoiItem.html" title="com.amap.api.services.core中的类">PoiItem</a>的<a href="../../../../../../com/amap/api/services/poisearch/package-summary.html">com.amap.api.services.poisearch</a>中的方法</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/services/core/PoiItem.html" title="com.amap.api.services.core中的类">PoiItem</a></code></td>
<td class="colLast"><span class="typeNameLabel">PoiSearch.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/poisearch/PoiSearch.html#searchPOIId-java.lang.String-">searchPOIId</a></span>(java.lang.String&nbsp;poiID)</code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;</div>
<div class="block">已知poiid信息（点击地图底图），搜索POI的详细信息，同步</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表方法和解释">
<caption><span>返回变量类型为<a href="../../../../../../com/amap/api/services/core/PoiItem.html" title="com.amap.api.services.core中的类">PoiItem</a>的类型的<a href="../../../../../../com/amap/api/services/poisearch/package-summary.html">com.amap.api.services.poisearch</a>中的方法</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>java.util.ArrayList&lt;<a href="../../../../../../com/amap/api/services/core/PoiItem.html" title="com.amap.api.services.core中的类">PoiItem</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">PoiResult.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/poisearch/PoiResult.html#getPois--">getPois</a></span>()</code>
<div class="block">返回当前页所有POI结果。</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表方法和解释">
<caption><span>参数类型为<a href="../../../../../../com/amap/api/services/core/PoiItem.html" title="com.amap.api.services.core中的类">PoiItem</a>的<a href="../../../../../../com/amap/api/services/poisearch/package-summary.html">com.amap.api.services.poisearch</a>中的方法</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">PoiSearch.OnPoiSearchListener.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/poisearch/PoiSearch.OnPoiSearchListener.html#onPoiItemSearched-com.amap.api.services.core.PoiItem-int-">onPoiItemSearched</a></span>(<a href="../../../../../../com/amap/api/services/core/PoiItem.html" title="com.amap.api.services.core中的类">PoiItem</a>&nbsp;poiItem,
                 int&nbsp;errorCode)</code>
<div class="block">poi id搜索的结果回调</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.amap.api.services.share">
<!--   -->
</a>
<h3><a href="../../../../../../com/amap/api/services/share/package-summary.html">com.amap.api.services.share</a>中<a href="../../../../../../com/amap/api/services/core/PoiItem.html" title="com.amap.api.services.core中的类">PoiItem</a>的使用</h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表方法和解释">
<caption><span>参数类型为<a href="../../../../../../com/amap/api/services/core/PoiItem.html" title="com.amap.api.services.core中的类">PoiItem</a>的<a href="../../../../../../com/amap/api/services/share/package-summary.html">com.amap.api.services.share</a>中的方法</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><span class="typeNameLabel">ShareSearch.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/share/ShareSearch.html#searchPoiShareUrl-com.amap.api.services.core.PoiItem-">searchPoiShareUrl</a></span>(<a href="../../../../../../com/amap/api/services/core/PoiItem.html" title="com.amap.api.services.core中的类">PoiItem</a>&nbsp;poiitem)</code>
<div class="block">根据指定的参数获取POI分享的短串地址。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">ShareSearch.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/services/share/ShareSearch.html#searchPoiShareUrlAsyn-com.amap.api.services.core.PoiItem-">searchPoiShareUrlAsyn</a></span>(<a href="../../../../../../com/amap/api/services/core/PoiItem.html" title="com.amap.api.services.core中的类">PoiItem</a>&nbsp;poiitem)</code>
<div class="block">（异步处理）根据指定的参数来进行POI分享的异步处理。</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../../overview-summary.html">概览</a></li>
<li><a href="../package-summary.html">程序包</a></li>
<li><a href="../../../../../../com/amap/api/services/core/PoiItem.html" title="com.amap.api.services.core中的类">类</a></li>
<li class="navBarCell1Rev">使用</li>
<li><a href="../../../../../../overview-tree.html">树</a></li>
<li><a href="../../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个</li>
<li>下一个</li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?com/amap/api/services/core/class-use/PoiItem.html" target="_top">框架</a></li>
<li><a href="PoiItem.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
