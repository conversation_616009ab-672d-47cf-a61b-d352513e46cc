<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>com.amap.api.services.core</title>
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<h1 class="bar"><a href="../../../../../com/amap/api/services/core/package-summary.html" target="classFrame">com.amap.api.services.core</a></h1>
<div class="indexContainer">
<h2 title="类">类</h2>
<ul title="类">
<li><a href="LatLonPoint.html" title="com.amap.api.services.core中的类" target="classFrame">LatLonPoint</a></li>
<li><a href="LatLonSharePoint.html" title="com.amap.api.services.core中的类" target="classFrame">LatLonSharePoint</a></li>
<li><a href="PoiItem.html" title="com.amap.api.services.core中的类" target="classFrame">PoiItem</a></li>
<li><a href="SearchUtils.html" title="com.amap.api.services.core中的类" target="classFrame">SearchUtils</a></li>
<li><a href="ServiceSettings.html" title="com.amap.api.services.core中的类" target="classFrame">ServiceSettings</a></li>
<li><a href="SuggestionCity.html" title="com.amap.api.services.core中的类" target="classFrame">SuggestionCity</a></li>
</ul>
<h2 title="异常错误">异常错误</h2>
<ul title="异常错误">
<li><a href="AMapException.html" title="com.amap.api.services.core中的类" target="classFrame">AMapException</a></li>
</ul>
</div>
</body>
</html>
