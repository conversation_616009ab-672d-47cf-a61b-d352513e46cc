<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>类分层结构</title>
<link rel="stylesheet" type="text/css" href="stylesheet.css" title="Style">
<script type="text/javascript" src="script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="\u7C7B\u5206\u5C42\u7ED3\u6784";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li class="navBarCell1Rev">树</li>
<li><a href="deprecated-list.html">已过时</a></li>
<li><a href="index-files/index-1.html">索引</a></li>
<li><a href="help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个</li>
<li>下一个</li>
</ul>
<ul class="navList">
<li><a href="index.html?overview-tree.html" target="_top">框架</a></li>
<li><a href="overview-tree.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 class="title">所有程序包的分层结构</h1>
<span class="packageHierarchyLabel">程序包分层结构:</span>
<ul class="horizontal">
<li><a href="com/amap/api/services/busline/package-tree.html">com.amap.api.services.busline</a>, </li>
<li><a href="com/amap/api/services/cloud/package-tree.html">com.amap.api.services.cloud</a>, </li>
<li><a href="com/amap/api/services/core/package-tree.html">com.amap.api.services.core</a>, </li>
<li><a href="com/amap/api/services/district/package-tree.html">com.amap.api.services.district</a>, </li>
<li><a href="com/amap/api/services/geocoder/package-tree.html">com.amap.api.services.geocoder</a>, </li>
<li><a href="com/amap/api/services/help/package-tree.html">com.amap.api.services.help</a>, </li>
<li><a href="com/amap/api/services/nearby/package-tree.html">com.amap.api.services.nearby</a>, </li>
<li><a href="com/amap/api/services/poisearch/package-tree.html">com.amap.api.services.poisearch</a>, </li>
<li><a href="com/amap/api/services/road/package-tree.html">com.amap.api.services.road</a>, </li>
<li><a href="com/amap/api/services/route/package-tree.html">com.amap.api.services.route</a>, </li>
<li><a href="com/amap/api/services/routepoisearch/package-tree.html">com.amap.api.services.routepoisearch</a>, </li>
<li><a href="com/amap/api/services/share/package-tree.html">com.amap.api.services.share</a>, </li>
<li><a href="com/amap/api/services/weather/package-tree.html">com.amap.api.services.weather</a></li>
</ul>
</div>
<div class="contentContainer">
<h2 title="类分层结构">类分层结构</h2>
<ul>
<li type="circle">java.lang.Object
<ul>
<li type="circle">com.amap.api.services.geocoder.<a href="com/amap/api/services/geocoder/AoiItem.html" title="com.amap.api.services.geocoder中的类"><span class="typeNameLink">AoiItem</span></a></li>
<li type="circle">com.amap.api.services.poisearch.<a href="com/amap/api/services/poisearch/Business.html" title="com.amap.api.services.poisearch中的类"><span class="typeNameLink">Business</span></a></li>
<li type="circle">com.amap.api.services.geocoder.<a href="com/amap/api/services/geocoder/BusinessArea.html" title="com.amap.api.services.geocoder中的类"><span class="typeNameLink">BusinessArea</span></a></li>
<li type="circle">com.amap.api.services.busline.<a href="com/amap/api/services/busline/BusLineItem.html" title="com.amap.api.services.busline中的类"><span class="typeNameLink">BusLineItem</span></a>
<ul>
<li type="circle">com.amap.api.services.route.<a href="com/amap/api/services/route/RouteBusLineItem.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">RouteBusLineItem</span></a></li>
</ul>
</li>
<li type="circle">com.amap.api.services.busline.<a href="com/amap/api/services/busline/BusLineQuery.html" title="com.amap.api.services.busline中的类"><span class="typeNameLink">BusLineQuery</span></a> (implements java.lang.Cloneable)</li>
<li type="circle">com.amap.api.services.busline.<a href="com/amap/api/services/busline/BusLineResult.html" title="com.amap.api.services.busline中的类"><span class="typeNameLink">BusLineResult</span></a></li>
<li type="circle">com.amap.api.services.busline.<a href="com/amap/api/services/busline/BusLineSearch.html" title="com.amap.api.services.busline中的类"><span class="typeNameLink">BusLineSearch</span></a></li>
<li type="circle">com.amap.api.services.busline.<a href="com/amap/api/services/busline/BusStationItem.html" title="com.amap.api.services.busline中的类"><span class="typeNameLink">BusStationItem</span></a></li>
<li type="circle">com.amap.api.services.busline.<a href="com/amap/api/services/busline/BusStationQuery.html" title="com.amap.api.services.busline中的类"><span class="typeNameLink">BusStationQuery</span></a> (implements java.lang.Cloneable)</li>
<li type="circle">com.amap.api.services.busline.<a href="com/amap/api/services/busline/BusStationResult.html" title="com.amap.api.services.busline中的类"><span class="typeNameLink">BusStationResult</span></a></li>
<li type="circle">com.amap.api.services.busline.<a href="com/amap/api/services/busline/BusStationSearch.html" title="com.amap.api.services.busline中的类"><span class="typeNameLink">BusStationSearch</span></a></li>
<li type="circle">com.amap.api.services.route.<a href="com/amap/api/services/route/BusStep.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">BusStep</span></a></li>
<li type="circle">com.amap.api.services.route.<a href="com/amap/api/services/route/BusStepV2.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">BusStepV2</span></a></li>
<li type="circle">com.amap.api.services.cloud.<a href="com/amap/api/services/cloud/CloudImage.html" title="com.amap.api.services.cloud中的类"><span class="typeNameLink">CloudImage</span></a></li>
<li type="circle">com.amap.api.services.cloud.<a href="com/amap/api/services/cloud/CloudItem.html" title="com.amap.api.services.cloud中的类"><span class="typeNameLink">CloudItem</span></a>
<ul>
<li type="circle">com.amap.api.services.cloud.<a href="com/amap/api/services/cloud/CloudItemDetail.html" title="com.amap.api.services.cloud中的类"><span class="typeNameLink">CloudItemDetail</span></a></li>
</ul>
</li>
<li type="circle">com.amap.api.services.cloud.<a href="com/amap/api/services/cloud/CloudResult.html" title="com.amap.api.services.cloud中的类"><span class="typeNameLink">CloudResult</span></a></li>
<li type="circle">com.amap.api.services.cloud.<a href="com/amap/api/services/cloud/CloudSearch.html" title="com.amap.api.services.cloud中的类"><span class="typeNameLink">CloudSearch</span></a></li>
<li type="circle">com.amap.api.services.cloud.<a href="com/amap/api/services/cloud/CloudSearch.Query.html" title="com.amap.api.services.cloud中的类"><span class="typeNameLink">CloudSearch.Query</span></a> (implements java.lang.Cloneable)</li>
<li type="circle">com.amap.api.services.cloud.<a href="com/amap/api/services/cloud/CloudSearch.SearchBound.html" title="com.amap.api.services.cloud中的类"><span class="typeNameLink">CloudSearch.SearchBound</span></a> (implements java.lang.Cloneable)</li>
<li type="circle">com.amap.api.services.cloud.<a href="com/amap/api/services/cloud/CloudSearch.Sortingrules.html" title="com.amap.api.services.cloud中的类"><span class="typeNameLink">CloudSearch.Sortingrules</span></a></li>
<li type="circle">com.amap.api.services.route.<a href="com/amap/api/services/route/Cost.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">Cost</span></a></li>
<li type="circle">com.amap.api.services.route.<a href="com/amap/api/services/route/DistanceItem.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">DistanceItem</span></a></li>
<li type="circle">com.amap.api.services.route.<a href="com/amap/api/services/route/DistanceResult.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">DistanceResult</span></a></li>
<li type="circle">com.amap.api.services.route.<a href="com/amap/api/services/route/DistanceSearch.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">DistanceSearch</span></a></li>
<li type="circle">com.amap.api.services.route.<a href="com/amap/api/services/route/DistanceSearch.DistanceQuery.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">DistanceSearch.DistanceQuery</span></a> (implements java.lang.Cloneable)</li>
<li type="circle">com.amap.api.services.route.<a href="com/amap/api/services/route/District.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">District</span></a></li>
<li type="circle">com.amap.api.services.district.<a href="com/amap/api/services/district/DistrictItem.html" title="com.amap.api.services.district中的类"><span class="typeNameLink">DistrictItem</span></a></li>
<li type="circle">com.amap.api.services.district.<a href="com/amap/api/services/district/DistrictResult.html" title="com.amap.api.services.district中的类"><span class="typeNameLink">DistrictResult</span></a></li>
<li type="circle">com.amap.api.services.district.<a href="com/amap/api/services/district/DistrictSearch.html" title="com.amap.api.services.district中的类"><span class="typeNameLink">DistrictSearch</span></a></li>
<li type="circle">com.amap.api.services.district.<a href="com/amap/api/services/district/DistrictSearchQuery.html" title="com.amap.api.services.district中的类"><span class="typeNameLink">DistrictSearchQuery</span></a> (implements java.lang.Cloneable)</li>
<li type="circle">com.amap.api.services.route.<a href="com/amap/api/services/route/Doorway.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">Doorway</span></a></li>
<li type="circle">com.amap.api.services.route.<a href="com/amap/api/services/route/DrivePlanPath.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">DrivePlanPath</span></a></li>
<li type="circle">com.amap.api.services.route.<a href="com/amap/api/services/route/DrivePlanStep.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">DrivePlanStep</span></a></li>
<li type="circle">com.amap.api.services.route.<a href="com/amap/api/services/route/DriveStep.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">DriveStep</span></a></li>
<li type="circle">com.amap.api.services.route.<a href="com/amap/api/services/route/DriveStepV2.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">DriveStepV2</span></a></li>
<li type="circle">com.amap.api.services.geocoder.<a href="com/amap/api/services/geocoder/GeocodeAddress.html" title="com.amap.api.services.geocoder中的类"><span class="typeNameLink">GeocodeAddress</span></a></li>
<li type="circle">com.amap.api.services.geocoder.<a href="com/amap/api/services/geocoder/GeocodeQuery.html" title="com.amap.api.services.geocoder中的类"><span class="typeNameLink">GeocodeQuery</span></a></li>
<li type="circle">com.amap.api.services.geocoder.<a href="com/amap/api/services/geocoder/GeocodeResult.html" title="com.amap.api.services.geocoder中的类"><span class="typeNameLink">GeocodeResult</span></a></li>
<li type="circle">com.amap.api.services.geocoder.<a href="com/amap/api/services/geocoder/GeocodeSearch.html" title="com.amap.api.services.geocoder中的类"><span class="typeNameLink">GeocodeSearch</span></a></li>
<li type="circle">com.amap.api.services.poisearch.<a href="com/amap/api/services/poisearch/IndoorData.html" title="com.amap.api.services.poisearch中的类"><span class="typeNameLink">IndoorData</span></a></li>
<li type="circle">com.amap.api.services.poisearch.<a href="com/amap/api/services/poisearch/IndoorDataV2.html" title="com.amap.api.services.poisearch中的类"><span class="typeNameLink">IndoorDataV2</span></a></li>
<li type="circle">com.amap.api.services.help.<a href="com/amap/api/services/help/Inputtips.html" title="com.amap.api.services.help中的类"><span class="typeNameLink">Inputtips</span></a></li>
<li type="circle">com.amap.api.services.help.<a href="com/amap/api/services/help/InputtipsQuery.html" title="com.amap.api.services.help中的类"><span class="typeNameLink">InputtipsQuery</span></a> (implements java.lang.Cloneable)</li>
<li type="circle">com.amap.api.services.core.<a href="com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类"><span class="typeNameLink">LatLonPoint</span></a>
<ul>
<li type="circle">com.amap.api.services.core.<a href="com/amap/api/services/core/LatLonSharePoint.html" title="com.amap.api.services.core中的类"><span class="typeNameLink">LatLonSharePoint</span></a></li>
</ul>
</li>
<li type="circle">com.amap.api.services.weather.<a href="com/amap/api/services/weather/LocalDayWeatherForecast.html" title="com.amap.api.services.weather中的类"><span class="typeNameLink">LocalDayWeatherForecast</span></a></li>
<li type="circle">com.amap.api.services.weather.<a href="com/amap/api/services/weather/LocalWeatherForecast.html" title="com.amap.api.services.weather中的类"><span class="typeNameLink">LocalWeatherForecast</span></a></li>
<li type="circle">com.amap.api.services.weather.<a href="com/amap/api/services/weather/LocalWeatherForecastResult.html" title="com.amap.api.services.weather中的类"><span class="typeNameLink">LocalWeatherForecastResult</span></a></li>
<li type="circle">com.amap.api.services.weather.<a href="com/amap/api/services/weather/LocalWeatherLive.html" title="com.amap.api.services.weather中的类"><span class="typeNameLink">LocalWeatherLive</span></a></li>
<li type="circle">com.amap.api.services.weather.<a href="com/amap/api/services/weather/LocalWeatherLiveResult.html" title="com.amap.api.services.weather中的类"><span class="typeNameLink">LocalWeatherLiveResult</span></a></li>
<li type="circle">com.amap.api.services.route.<a href="com/amap/api/services/route/Navi.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">Navi</span></a>
<ul>
<li type="circle">com.amap.api.services.route.<a href="com/amap/api/services/route/NaviWalkType.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">NaviWalkType</span></a></li>
</ul>
</li>
<li type="circle">com.amap.api.services.nearby.<a href="com/amap/api/services/nearby/NearbyInfo.html" title="com.amap.api.services.nearby中的类"><span class="typeNameLink">NearbyInfo</span></a></li>
<li type="circle">com.amap.api.services.nearby.<a href="com/amap/api/services/nearby/NearbySearch.html" title="com.amap.api.services.nearby中的类"><span class="typeNameLink">NearbySearch</span></a></li>
<li type="circle">com.amap.api.services.nearby.<a href="com/amap/api/services/nearby/NearbySearch.NearbyQuery.html" title="com.amap.api.services.nearby中的类"><span class="typeNameLink">NearbySearch.NearbyQuery</span></a></li>
<li type="circle">com.amap.api.services.nearby.<a href="com/amap/api/services/nearby/NearbySearchResult.html" title="com.amap.api.services.nearby中的类"><span class="typeNameLink">NearbySearchResult</span></a></li>
<li type="circle">com.amap.api.services.route.<a href="com/amap/api/services/route/Path.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">Path</span></a>
<ul>
<li type="circle">com.amap.api.services.route.<a href="com/amap/api/services/route/BusPath.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">BusPath</span></a></li>
<li type="circle">com.amap.api.services.route.<a href="com/amap/api/services/route/BusPathV2.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">BusPathV2</span></a></li>
<li type="circle">com.amap.api.services.route.<a href="com/amap/api/services/route/DrivePath.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">DrivePath</span></a></li>
<li type="circle">com.amap.api.services.route.<a href="com/amap/api/services/route/DrivePathV2.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">DrivePathV2</span></a></li>
<li type="circle">com.amap.api.services.route.<a href="com/amap/api/services/route/RidePath.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">RidePath</span></a></li>
<li type="circle">com.amap.api.services.route.<a href="com/amap/api/services/route/WalkPath.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">WalkPath</span></a>
<ul>
<li type="circle">com.amap.api.services.route.<a href="com/amap/api/services/route/RouteBusWalkItem.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">RouteBusWalkItem</span></a></li>
</ul>
</li>
</ul>
</li>
<li type="circle">com.amap.api.services.poisearch.<a href="com/amap/api/services/poisearch/Photo.html" title="com.amap.api.services.poisearch中的类"><span class="typeNameLink">Photo</span></a></li>
<li type="circle">com.amap.api.services.core.<a href="com/amap/api/services/core/PoiItem.html" title="com.amap.api.services.core中的类"><span class="typeNameLink">PoiItem</span></a></li>
<li type="circle">com.amap.api.services.poisearch.<a href="com/amap/api/services/poisearch/PoiItemExtension.html" title="com.amap.api.services.poisearch中的类"><span class="typeNameLink">PoiItemExtension</span></a></li>
<li type="circle">com.amap.api.services.poisearch.<a href="com/amap/api/services/poisearch/PoiNavi.html" title="com.amap.api.services.poisearch中的类"><span class="typeNameLink">PoiNavi</span></a></li>
<li type="circle">com.amap.api.services.poisearch.<a href="com/amap/api/services/poisearch/PoiResult.html" title="com.amap.api.services.poisearch中的类"><span class="typeNameLink">PoiResult</span></a></li>
<li type="circle">com.amap.api.services.poisearch.<a href="com/amap/api/services/poisearch/PoiResultV2.html" title="com.amap.api.services.poisearch中的类"><span class="typeNameLink">PoiResultV2</span></a></li>
<li type="circle">com.amap.api.services.poisearch.<a href="com/amap/api/services/poisearch/PoiSearch.html" title="com.amap.api.services.poisearch中的类"><span class="typeNameLink">PoiSearch</span></a></li>
<li type="circle">com.amap.api.services.poisearch.<a href="com/amap/api/services/poisearch/PoiSearch.Query.html" title="com.amap.api.services.poisearch中的类"><span class="typeNameLink">PoiSearch.Query</span></a> (implements java.lang.Cloneable)</li>
<li type="circle">com.amap.api.services.poisearch.<a href="com/amap/api/services/poisearch/PoiSearch.SearchBound.html" title="com.amap.api.services.poisearch中的类"><span class="typeNameLink">PoiSearch.SearchBound</span></a> (implements java.lang.Cloneable)</li>
<li type="circle">com.amap.api.services.poisearch.<a href="com/amap/api/services/poisearch/PoiSearchV2.html" title="com.amap.api.services.poisearch中的类"><span class="typeNameLink">PoiSearchV2</span></a></li>
<li type="circle">com.amap.api.services.poisearch.<a href="com/amap/api/services/poisearch/PoiSearchV2.Query.html" title="com.amap.api.services.poisearch中的类"><span class="typeNameLink">PoiSearchV2.Query</span></a> (implements java.lang.Cloneable)</li>
<li type="circle">com.amap.api.services.poisearch.<a href="com/amap/api/services/poisearch/PoiSearchV2.SearchBound.html" title="com.amap.api.services.poisearch中的类"><span class="typeNameLink">PoiSearchV2.SearchBound</span></a> (implements java.lang.Cloneable)</li>
<li type="circle">com.amap.api.services.poisearch.<a href="com/amap/api/services/poisearch/PoiSearchV2.ShowFields.html" title="com.amap.api.services.poisearch中的类"><span class="typeNameLink">PoiSearchV2.ShowFields</span></a></li>
<li type="circle">com.amap.api.services.route.<a href="com/amap/api/services/route/Railway.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">Railway</span></a>
<ul>
<li type="circle">com.amap.api.services.route.<a href="com/amap/api/services/route/RouteRailwayItem.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">RouteRailwayItem</span></a></li>
</ul>
</li>
<li type="circle">com.amap.api.services.route.<a href="com/amap/api/services/route/RailwaySpace.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">RailwaySpace</span></a></li>
<li type="circle">com.amap.api.services.route.<a href="com/amap/api/services/route/RailwayStationItem.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">RailwayStationItem</span></a></li>
<li type="circle">com.amap.api.services.geocoder.<a href="com/amap/api/services/geocoder/RegeocodeAddress.html" title="com.amap.api.services.geocoder中的类"><span class="typeNameLink">RegeocodeAddress</span></a></li>
<li type="circle">com.amap.api.services.geocoder.<a href="com/amap/api/services/geocoder/RegeocodeQuery.html" title="com.amap.api.services.geocoder中的类"><span class="typeNameLink">RegeocodeQuery</span></a></li>
<li type="circle">com.amap.api.services.geocoder.<a href="com/amap/api/services/geocoder/RegeocodeResult.html" title="com.amap.api.services.geocoder中的类"><span class="typeNameLink">RegeocodeResult</span></a></li>
<li type="circle">com.amap.api.services.geocoder.<a href="com/amap/api/services/geocoder/RegeocodeRoad.html" title="com.amap.api.services.geocoder中的类"><span class="typeNameLink">RegeocodeRoad</span></a></li>
<li type="circle">com.amap.api.services.route.<a href="com/amap/api/services/route/RideStep.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">RideStep</span></a></li>
<li type="circle">com.amap.api.services.road.<a href="com/amap/api/services/road/Road.html" title="com.amap.api.services.road中的类"><span class="typeNameLink">Road</span></a>
<ul>
<li type="circle">com.amap.api.services.road.<a href="com/amap/api/services/road/Crossroad.html" title="com.amap.api.services.road中的类"><span class="typeNameLink">Crossroad</span></a></li>
</ul>
</li>
<li type="circle">com.amap.api.services.route.<a href="com/amap/api/services/route/RoutePlanResult.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">RoutePlanResult</span></a>
<ul>
<li type="circle">com.amap.api.services.route.<a href="com/amap/api/services/route/DriveRoutePlanResult.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">DriveRoutePlanResult</span></a></li>
</ul>
</li>
<li type="circle">com.amap.api.services.routepoisearch.<a href="com/amap/api/services/routepoisearch/RoutePOIItem.html" title="com.amap.api.services.routepoisearch中的类"><span class="typeNameLink">RoutePOIItem</span></a></li>
<li type="circle">com.amap.api.services.routepoisearch.<a href="com/amap/api/services/routepoisearch/RoutePOISearch.html" title="com.amap.api.services.routepoisearch中的类"><span class="typeNameLink">RoutePOISearch</span></a></li>
<li type="circle">com.amap.api.services.routepoisearch.<a href="com/amap/api/services/routepoisearch/RoutePOISearchQuery.html" title="com.amap.api.services.routepoisearch中的类"><span class="typeNameLink">RoutePOISearchQuery</span></a> (implements java.lang.Cloneable)</li>
<li type="circle">com.amap.api.services.routepoisearch.<a href="com/amap/api/services/routepoisearch/RoutePOISearchResult.html" title="com.amap.api.services.routepoisearch中的类"><span class="typeNameLink">RoutePOISearchResult</span></a></li>
<li type="circle">com.amap.api.services.route.<a href="com/amap/api/services/route/RouteResult.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">RouteResult</span></a>
<ul>
<li type="circle">com.amap.api.services.route.<a href="com/amap/api/services/route/BusRouteResult.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">BusRouteResult</span></a></li>
<li type="circle">com.amap.api.services.route.<a href="com/amap/api/services/route/BusRouteResultV2.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">BusRouteResultV2</span></a></li>
<li type="circle">com.amap.api.services.route.<a href="com/amap/api/services/route/DriveRouteResult.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">DriveRouteResult</span></a></li>
<li type="circle">com.amap.api.services.route.<a href="com/amap/api/services/route/DriveRouteResultV2.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">DriveRouteResultV2</span></a></li>
<li type="circle">com.amap.api.services.route.<a href="com/amap/api/services/route/RideRouteResult.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">RideRouteResult</span></a></li>
<li type="circle">com.amap.api.services.route.<a href="com/amap/api/services/route/RideRouteResultV2.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">RideRouteResultV2</span></a></li>
<li type="circle">com.amap.api.services.route.<a href="com/amap/api/services/route/WalkRouteResult.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">WalkRouteResult</span></a></li>
<li type="circle">com.amap.api.services.route.<a href="com/amap/api/services/route/WalkRouteResultV2.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">WalkRouteResultV2</span></a></li>
</ul>
</li>
<li type="circle">com.amap.api.services.route.<a href="com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">RouteSearch</span></a></li>
<li type="circle">com.amap.api.services.route.<a href="com/amap/api/services/route/RouteSearch.BusRouteQuery.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">RouteSearch.BusRouteQuery</span></a> (implements java.lang.Cloneable)</li>
<li type="circle">com.amap.api.services.route.<a href="com/amap/api/services/route/RouteSearch.DrivePlanQuery.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">RouteSearch.DrivePlanQuery</span></a> (implements java.lang.Cloneable)</li>
<li type="circle">com.amap.api.services.route.<a href="com/amap/api/services/route/RouteSearch.DriveRouteQuery.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">RouteSearch.DriveRouteQuery</span></a> (implements java.lang.Cloneable)</li>
<li type="circle">com.amap.api.services.route.<a href="com/amap/api/services/route/RouteSearch.FromAndTo.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">RouteSearch.FromAndTo</span></a> (implements java.lang.Cloneable)</li>
<li type="circle">com.amap.api.services.route.<a href="com/amap/api/services/route/RouteSearch.RideRouteQuery.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">RouteSearch.RideRouteQuery</span></a> (implements java.lang.Cloneable)</li>
<li type="circle">com.amap.api.services.route.<a href="com/amap/api/services/route/RouteSearch.TruckRouteQuery.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">RouteSearch.TruckRouteQuery</span></a> (implements java.lang.Cloneable)</li>
<li type="circle">com.amap.api.services.route.<a href="com/amap/api/services/route/RouteSearch.WalkRouteQuery.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">RouteSearch.WalkRouteQuery</span></a> (implements java.lang.Cloneable)</li>
<li type="circle">com.amap.api.services.route.<a href="com/amap/api/services/route/RouteSearchV2.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">RouteSearchV2</span></a></li>
<li type="circle">com.amap.api.services.route.<a href="com/amap/api/services/route/RouteSearchV2.AlternativeRoute.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">RouteSearchV2.AlternativeRoute</span></a></li>
<li type="circle">com.amap.api.services.route.<a href="com/amap/api/services/route/RouteSearchV2.BusMode.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">RouteSearchV2.BusMode</span></a></li>
<li type="circle">com.amap.api.services.route.<a href="com/amap/api/services/route/RouteSearchV2.BusRouteQuery.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">RouteSearchV2.BusRouteQuery</span></a> (implements java.lang.Cloneable)</li>
<li type="circle">com.amap.api.services.route.<a href="com/amap/api/services/route/RouteSearchV2.DriveRouteQuery.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">RouteSearchV2.DriveRouteQuery</span></a> (implements java.lang.Cloneable)</li>
<li type="circle">com.amap.api.services.route.<a href="com/amap/api/services/route/RouteSearchV2.FromAndTo.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">RouteSearchV2.FromAndTo</span></a> (implements java.lang.Cloneable)</li>
<li type="circle">com.amap.api.services.route.<a href="com/amap/api/services/route/RouteSearchV2.RideRouteQuery.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">RouteSearchV2.RideRouteQuery</span></a> (implements java.lang.Cloneable)</li>
<li type="circle">com.amap.api.services.route.<a href="com/amap/api/services/route/RouteSearchV2.ShowFields.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">RouteSearchV2.ShowFields</span></a></li>
<li type="circle">com.amap.api.services.route.<a href="com/amap/api/services/route/RouteSearchV2.WalkRouteQuery.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">RouteSearchV2.WalkRouteQuery</span></a> (implements java.lang.Cloneable)</li>
<li type="circle">com.amap.api.services.route.<a href="com/amap/api/services/route/SearchCity.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">SearchCity</span></a>
<ul>
<li type="circle">com.amap.api.services.route.<a href="com/amap/api/services/route/RouteSearchCity.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">RouteSearchCity</span></a></li>
</ul>
</li>
<li type="circle">com.amap.api.services.core.<a href="com/amap/api/services/core/SearchUtils.html" title="com.amap.api.services.core中的类"><span class="typeNameLink">SearchUtils</span></a></li>
<li type="circle">com.amap.api.services.core.<a href="com/amap/api/services/core/ServiceSettings.html" title="com.amap.api.services.core中的类"><span class="typeNameLink">ServiceSettings</span></a></li>
<li type="circle">com.amap.api.services.share.<a href="com/amap/api/services/share/ShareSearch.html" title="com.amap.api.services.share中的类"><span class="typeNameLink">ShareSearch</span></a></li>
<li type="circle">com.amap.api.services.share.<a href="com/amap/api/services/share/ShareSearch.ShareBusRouteQuery.html" title="com.amap.api.services.share中的类"><span class="typeNameLink">ShareSearch.ShareBusRouteQuery</span></a></li>
<li type="circle">com.amap.api.services.share.<a href="com/amap/api/services/share/ShareSearch.ShareDrivingRouteQuery.html" title="com.amap.api.services.share中的类"><span class="typeNameLink">ShareSearch.ShareDrivingRouteQuery</span></a></li>
<li type="circle">com.amap.api.services.share.<a href="com/amap/api/services/share/ShareSearch.ShareFromAndTo.html" title="com.amap.api.services.share中的类"><span class="typeNameLink">ShareSearch.ShareFromAndTo</span></a></li>
<li type="circle">com.amap.api.services.share.<a href="com/amap/api/services/share/ShareSearch.ShareNaviQuery.html" title="com.amap.api.services.share中的类"><span class="typeNameLink">ShareSearch.ShareNaviQuery</span></a></li>
<li type="circle">com.amap.api.services.share.<a href="com/amap/api/services/share/ShareSearch.ShareWalkRouteQuery.html" title="com.amap.api.services.share中的类"><span class="typeNameLink">ShareSearch.ShareWalkRouteQuery</span></a></li>
<li type="circle">com.amap.api.services.geocoder.<a href="com/amap/api/services/geocoder/StreetNumber.html" title="com.amap.api.services.geocoder中的类"><span class="typeNameLink">StreetNumber</span></a></li>
<li type="circle">com.amap.api.services.poisearch.<a href="com/amap/api/services/poisearch/SubPoiItem.html" title="com.amap.api.services.poisearch中的类"><span class="typeNameLink">SubPoiItem</span></a></li>
<li type="circle">com.amap.api.services.poisearch.<a href="com/amap/api/services/poisearch/SubPoiItemV2.html" title="com.amap.api.services.poisearch中的类"><span class="typeNameLink">SubPoiItemV2</span></a></li>
<li type="circle">com.amap.api.services.core.<a href="com/amap/api/services/core/SuggestionCity.html" title="com.amap.api.services.core中的类"><span class="typeNameLink">SuggestionCity</span></a></li>
<li type="circle">com.amap.api.services.route.<a href="com/amap/api/services/route/TaxiItem.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">TaxiItem</span></a></li>
<li type="circle">com.amap.api.services.route.<a href="com/amap/api/services/route/TaxiItemV2.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">TaxiItemV2</span></a></li>
<li type="circle">java.lang.Throwable (implements java.io.Serializable)
<ul>
<li type="circle">java.lang.Exception
<ul>
<li type="circle">com.amap.api.services.core.<a href="com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类"><span class="typeNameLink">AMapException</span></a></li>
</ul>
</li>
</ul>
</li>
<li type="circle">com.amap.api.services.route.<a href="com/amap/api/services/route/TimeInfo.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">TimeInfo</span></a></li>
<li type="circle">com.amap.api.services.route.<a href="com/amap/api/services/route/TimeInfosElement.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">TimeInfosElement</span></a></li>
<li type="circle">com.amap.api.services.help.<a href="com/amap/api/services/help/Tip.html" title="com.amap.api.services.help中的类"><span class="typeNameLink">Tip</span></a></li>
<li type="circle">com.amap.api.services.route.<a href="com/amap/api/services/route/TMC.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">TMC</span></a></li>
<li type="circle">com.amap.api.services.route.<a href="com/amap/api/services/route/TruckPath.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">TruckPath</span></a></li>
<li type="circle">com.amap.api.services.route.<a href="com/amap/api/services/route/TruckRouteRestult.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">TruckRouteRestult</span></a></li>
<li type="circle">com.amap.api.services.route.<a href="com/amap/api/services/route/TruckStep.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">TruckStep</span></a></li>
<li type="circle">com.amap.api.services.nearby.<a href="com/amap/api/services/nearby/UploadInfo.html" title="com.amap.api.services.nearby中的类"><span class="typeNameLink">UploadInfo</span></a></li>
<li type="circle">com.amap.api.services.route.<a href="com/amap/api/services/route/WalkStep.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">WalkStep</span></a></li>
<li type="circle">com.amap.api.services.weather.<a href="com/amap/api/services/weather/WeatherSearch.html" title="com.amap.api.services.weather中的类"><span class="typeNameLink">WeatherSearch</span></a></li>
<li type="circle">com.amap.api.services.weather.<a href="com/amap/api/services/weather/WeatherSearchQuery.html" title="com.amap.api.services.weather中的类"><span class="typeNameLink">WeatherSearchQuery</span></a> (implements java.lang.Cloneable)</li>
</ul>
</li>
</ul>
<h2 title="接口分层结构">接口分层结构</h2>
<ul>
<li type="circle">com.amap.api.services.busline.<a href="com/amap/api/services/busline/BusLineSearch.OnBusLineSearchListener.html" title="com.amap.api.services.busline中的接口"><span class="typeNameLink">BusLineSearch.OnBusLineSearchListener</span></a></li>
<li type="circle">com.amap.api.services.busline.<a href="com/amap/api/services/busline/BusStationSearch.OnBusStationSearchListener.html" title="com.amap.api.services.busline中的接口"><span class="typeNameLink">BusStationSearch.OnBusStationSearchListener</span></a></li>
<li type="circle">com.amap.api.services.cloud.<a href="com/amap/api/services/cloud/CloudSearch.OnCloudSearchListener.html" title="com.amap.api.services.cloud中的接口"><span class="typeNameLink">CloudSearch.OnCloudSearchListener</span></a></li>
<li type="circle">com.amap.api.services.route.<a href="com/amap/api/services/route/DistanceSearch.OnDistanceSearchListener.html" title="com.amap.api.services.route中的接口"><span class="typeNameLink">DistanceSearch.OnDistanceSearchListener</span></a></li>
<li type="circle">com.amap.api.services.district.<a href="com/amap/api/services/district/DistrictSearch.OnDistrictSearchListener.html" title="com.amap.api.services.district中的接口"><span class="typeNameLink">DistrictSearch.OnDistrictSearchListener</span></a></li>
<li type="circle">com.amap.api.services.geocoder.<a href="com/amap/api/services/geocoder/GeocodeSearch.OnGeocodeSearchListener.html" title="com.amap.api.services.geocoder中的接口"><span class="typeNameLink">GeocodeSearch.OnGeocodeSearchListener</span></a></li>
<li type="circle">com.amap.api.services.help.<a href="com/amap/api/services/help/Inputtips.InputtipsListener.html" title="com.amap.api.services.help中的接口"><span class="typeNameLink">Inputtips.InputtipsListener</span></a></li>
<li type="circle">com.amap.api.services.nearby.<a href="com/amap/api/services/nearby/NearbySearch.NearbyListener.html" title="com.amap.api.services.nearby中的接口"><span class="typeNameLink">NearbySearch.NearbyListener</span></a></li>
<li type="circle">com.amap.api.services.poisearch.<a href="com/amap/api/services/poisearch/PoiSearch.OnPoiSearchListener.html" title="com.amap.api.services.poisearch中的接口"><span class="typeNameLink">PoiSearch.OnPoiSearchListener</span></a></li>
<li type="circle">com.amap.api.services.poisearch.<a href="com/amap/api/services/poisearch/PoiSearchV2.OnPoiSearchListener.html" title="com.amap.api.services.poisearch中的接口"><span class="typeNameLink">PoiSearchV2.OnPoiSearchListener</span></a></li>
<li type="circle">com.amap.api.services.routepoisearch.<a href="com/amap/api/services/routepoisearch/RoutePOISearch.OnRoutePOISearchListener.html" title="com.amap.api.services.routepoisearch中的接口"><span class="typeNameLink">RoutePOISearch.OnRoutePOISearchListener</span></a></li>
<li type="circle">com.amap.api.services.route.<a href="com/amap/api/services/route/RouteSearch.OnRoutePlanSearchListener.html" title="com.amap.api.services.route中的接口"><span class="typeNameLink">RouteSearch.OnRoutePlanSearchListener</span></a></li>
<li type="circle">com.amap.api.services.route.<a href="com/amap/api/services/route/RouteSearch.OnRouteSearchListener.html" title="com.amap.api.services.route中的接口"><span class="typeNameLink">RouteSearch.OnRouteSearchListener</span></a></li>
<li type="circle">com.amap.api.services.route.<a href="com/amap/api/services/route/RouteSearch.OnTruckRouteSearchListener.html" title="com.amap.api.services.route中的接口"><span class="typeNameLink">RouteSearch.OnTruckRouteSearchListener</span></a></li>
<li type="circle">com.amap.api.services.route.<a href="com/amap/api/services/route/RouteSearchV2.OnRoutePlanSearchListener.html" title="com.amap.api.services.route中的接口"><span class="typeNameLink">RouteSearchV2.OnRoutePlanSearchListener</span></a></li>
<li type="circle">com.amap.api.services.route.<a href="com/amap/api/services/route/RouteSearchV2.OnRouteSearchListener.html" title="com.amap.api.services.route中的接口"><span class="typeNameLink">RouteSearchV2.OnRouteSearchListener</span></a></li>
<li type="circle">com.amap.api.services.route.<a href="com/amap/api/services/route/RouteSearchV2.OnTruckRouteSearchListener.html" title="com.amap.api.services.route中的接口"><span class="typeNameLink">RouteSearchV2.OnTruckRouteSearchListener</span></a></li>
<li type="circle">com.amap.api.services.share.<a href="com/amap/api/services/share/ShareSearch.OnShareSearchListener.html" title="com.amap.api.services.share中的接口"><span class="typeNameLink">ShareSearch.OnShareSearchListener</span></a></li>
<li type="circle">com.amap.api.services.nearby.<a href="com/amap/api/services/nearby/UploadInfoCallback.html" title="com.amap.api.services.nearby中的接口"><span class="typeNameLink">UploadInfoCallback</span></a></li>
<li type="circle">com.amap.api.services.weather.<a href="com/amap/api/services/weather/WeatherSearch.OnWeatherSearchListener.html" title="com.amap.api.services.weather中的接口"><span class="typeNameLink">WeatherSearch.OnWeatherSearchListener</span></a></li>
</ul>
<h2 title="枚举分层结构">枚举分层结构</h2>
<ul>
<li type="circle">java.lang.Object
<ul>
<li type="circle">java.lang.Enum&lt;E&gt; (implements java.lang.Comparable&lt;T&gt;, java.io.Serializable)
<ul>
<li type="circle">com.amap.api.services.busline.<a href="com/amap/api/services/busline/BusLineQuery.SearchType.html" title="com.amap.api.services.busline中的枚举"><span class="typeNameLink">BusLineQuery.SearchType</span></a></li>
<li type="circle">com.amap.api.services.nearby.<a href="com/amap/api/services/nearby/NearbySearchFunctionType.html" title="com.amap.api.services.nearby中的枚举"><span class="typeNameLink">NearbySearchFunctionType</span></a></li>
<li type="circle">com.amap.api.services.route.<a href="com/amap/api/services/route/RouteSearchV2.DrivingStrategy.html" title="com.amap.api.services.route中的枚举"><span class="typeNameLink">RouteSearchV2.DrivingStrategy</span></a></li>
<li type="circle">com.amap.api.services.routepoisearch.<a href="com/amap/api/services/routepoisearch/RoutePOISearch.RoutePOISearchType.html" title="com.amap.api.services.routepoisearch中的枚举"><span class="typeNameLink">RoutePOISearch.RoutePOISearchType</span></a></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li class="navBarCell1Rev">树</li>
<li><a href="deprecated-list.html">已过时</a></li>
<li><a href="index-files/index-1.html">索引</a></li>
<li><a href="help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个</li>
<li>下一个</li>
</ul>
<ul class="navList">
<li><a href="index.html?overview-tree.html" target="_top">框架</a></li>
<li><a href="overview-tree.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
