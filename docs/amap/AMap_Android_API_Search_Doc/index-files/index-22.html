<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>W - 索引</title>
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="W - \u7D22\u5F15";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-21.html">上一个字母</a></li>
<li>下一个字母</li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-22.html" target="_top">框架</a></li>
<li><a href="index-22.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="contentContainer"><a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">K</a>&nbsp;<a href="index-11.html">L</a>&nbsp;<a href="index-12.html">M</a>&nbsp;<a href="index-13.html">N</a>&nbsp;<a href="index-14.html">O</a>&nbsp;<a href="index-15.html">P</a>&nbsp;<a href="index-16.html">Q</a>&nbsp;<a href="index-17.html">R</a>&nbsp;<a href="index-18.html">S</a>&nbsp;<a href="index-19.html">T</a>&nbsp;<a href="index-20.html">U</a>&nbsp;<a href="index-21.html">V</a>&nbsp;<a href="index-22.html">W</a>&nbsp;<a name="I:W">
<!--   -->
</a>
<h2 class="title">W</h2>
<dl>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#WALK_DEFAULT">WALK_DEFAULT</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span>
<div class="block"><span class="deprecationComment">步行不再提供模式相关设置</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#WALK_MULTI_PATH">WALK_MULTI_PATH</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span>
<div class="block"><span class="deprecationComment">步行不再提供模式相关设置</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#WalkDefault">WalkDefault</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span>
<div class="block"><span class="deprecationComment">自V4.0.0以后，请参考 <a href="../com/amap/api/services/route/RouteSearch.html#WALK_DEFAULT"><code>RouteSearch.WALK_DEFAULT</code></a></span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#WalkMultipath">WalkMultipath</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span>
<div class="block"><span class="deprecationComment">自V4.0.0以后，请参考 <a href="../com/amap/api/services/route/RouteSearch.html#WALK_MULTI_PATH"><code>RouteSearch.WALK_MULTI_PATH</code></a></span></div>
</div>
</dd>
<dt><a href="../com/amap/api/services/route/WalkPath.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">WalkPath</span></a> - <a href="../com/amap/api/services/route/package-summary.html">com.amap.api.services.route</a>中的类</dt>
<dd>
<div class="block">定义了步行路径规划的一个方案。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.WalkRouteQuery.html#WalkRouteQuery-com.amap.api.services.route.RouteSearch.FromAndTo-int-">WalkRouteQuery(RouteSearch.FromAndTo, int)</a></span> - 类 的构造器com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.WalkRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearch.WalkRouteQuery</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span>
<div class="block"><span class="deprecationComment">因取消mode参数而废弃</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.WalkRouteQuery.html#WalkRouteQuery-com.amap.api.services.route.RouteSearch.FromAndTo-">WalkRouteQuery(RouteSearch.FromAndTo)</a></span> - 类 的构造器com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.WalkRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearch.WalkRouteQuery</a></dt>
<dd>
<div class="block">WalkRouteQuery构造函数。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.WalkRouteQuery.html#WalkRouteQuery-Parcel-">WalkRouteQuery(Parcel)</a></span> - 类 的构造器com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.WalkRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearch.WalkRouteQuery</a></dt>
<dd>
<div class="block">序列化实现。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.WalkRouteQuery.html#WalkRouteQuery-com.amap.api.services.route.RouteSearchV2.FromAndTo-">WalkRouteQuery(RouteSearchV2.FromAndTo)</a></span> - 类 的构造器com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.WalkRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearchV2.WalkRouteQuery</a></dt>
<dd>
<div class="block">WalkRouteQuery构造函数。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.WalkRouteQuery.html#WalkRouteQuery-Parcel-">WalkRouteQuery(Parcel)</a></span> - 类 的构造器com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.WalkRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearchV2.WalkRouteQuery</a></dt>
<dd>
<div class="block">序列化实现。</div>
</dd>
<dt><a href="../com/amap/api/services/route/WalkRouteResult.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">WalkRouteResult</span></a> - <a href="../com/amap/api/services/route/package-summary.html">com.amap.api.services.route</a>中的类</dt>
<dd>
<div class="block">定义了步行路径规划的结果集。</div>
</dd>
<dt><a href="../com/amap/api/services/route/WalkRouteResultV2.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">WalkRouteResultV2</span></a> - <a href="../com/amap/api/services/route/package-summary.html">com.amap.api.services.route</a>中的类</dt>
<dd>
<div class="block">定义了步行路径规划的结果集。</div>
</dd>
<dt><a href="../com/amap/api/services/route/WalkStep.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">WalkStep</span></a> - <a href="../com/amap/api/services/route/package-summary.html">com.amap.api.services.route</a>中的类</dt>
<dd>
<div class="block">定义了步行路径规划的一个路段。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/weather/WeatherSearchQuery.html#WEATHER_TYPE_FORECAST">WEATHER_TYPE_FORECAST</a></span> - 类 中的静态变量com.amap.api.services.weather.<a href="../com/amap/api/services/weather/WeatherSearchQuery.html" title="com.amap.api.services.weather中的类">WeatherSearchQuery</a></dt>
<dd>
<div class="block">预报天气，值为2。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/weather/WeatherSearchQuery.html#WEATHER_TYPE_LIVE">WEATHER_TYPE_LIVE</a></span> - 类 中的静态变量com.amap.api.services.weather.<a href="../com/amap/api/services/weather/WeatherSearchQuery.html" title="com.amap.api.services.weather中的类">WeatherSearchQuery</a></dt>
<dd>
<div class="block">实况天气，值为1。</div>
</dd>
<dt><a href="../com/amap/api/services/weather/WeatherSearch.html" title="com.amap.api.services.weather中的类"><span class="typeNameLink">WeatherSearch</span></a> - <a href="../com/amap/api/services/weather/package-summary.html">com.amap.api.services.weather</a>中的类</dt>
<dd>
<div class="block">本类为天气查询的“入口”类，定义此类，开始搜索。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/weather/WeatherSearch.html#WeatherSearch-Context-">WeatherSearch(Context)</a></span> - 类 的构造器com.amap.api.services.weather.<a href="../com/amap/api/services/weather/WeatherSearch.html" title="com.amap.api.services.weather中的类">WeatherSearch</a></dt>
<dd>
<div class="block">根据给定的参数构造一个WeatherSearch 的新对象。</div>
</dd>
<dt><a href="../com/amap/api/services/weather/WeatherSearch.OnWeatherSearchListener.html" title="com.amap.api.services.weather中的接口"><span class="typeNameLink">WeatherSearch.OnWeatherSearchListener</span></a> - <a href="../com/amap/api/services/weather/package-summary.html">com.amap.api.services.weather</a>中的接口</dt>
<dd>
<div class="block">此接口定义了天气搜索的异步处理回调接口。</div>
</dd>
<dt><a href="../com/amap/api/services/weather/WeatherSearchQuery.html" title="com.amap.api.services.weather中的类"><span class="typeNameLink">WeatherSearchQuery</span></a> - <a href="../com/amap/api/services/weather/package-summary.html">com.amap.api.services.weather</a>中的类</dt>
<dd>
<div class="block">此类定义了天气信息搜索的参数。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/weather/WeatherSearchQuery.html#WeatherSearchQuery-java.lang.String-int-">WeatherSearchQuery(String, int)</a></span> - 类 的构造器com.amap.api.services.weather.<a href="../com/amap/api/services/weather/WeatherSearchQuery.html" title="com.amap.api.services.weather中的类">WeatherSearchQuery</a></dt>
<dd>
<div class="block">根据给定的参数来构造一个 WeatherSearchQuery 的新对象。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/cloud/CloudSearch.Sortingrules.html#WEIGHT">WEIGHT</a></span> - 类 中的静态变量com.amap.api.services.cloud.<a href="../com/amap/api/services/cloud/CloudSearch.Sortingrules.html" title="com.amap.api.services.cloud中的类">CloudSearch.Sortingrules</a></dt>
<dd>
<div class="block">权重排序</div>
</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">K</a>&nbsp;<a href="index-11.html">L</a>&nbsp;<a href="index-12.html">M</a>&nbsp;<a href="index-13.html">N</a>&nbsp;<a href="index-14.html">O</a>&nbsp;<a href="index-15.html">P</a>&nbsp;<a href="index-16.html">Q</a>&nbsp;<a href="index-17.html">R</a>&nbsp;<a href="index-18.html">S</a>&nbsp;<a href="index-19.html">T</a>&nbsp;<a href="index-20.html">U</a>&nbsp;<a href="index-21.html">V</a>&nbsp;<a href="index-22.html">W</a>&nbsp;</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-21.html">上一个字母</a></li>
<li>下一个字母</li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-22.html" target="_top">框架</a></li>
<li><a href="index-22.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
