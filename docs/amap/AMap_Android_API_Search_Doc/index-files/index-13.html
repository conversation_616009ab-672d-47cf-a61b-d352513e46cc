<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>N - 索引</title>
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="N - \u7D22\u5F15";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-12.html">上一个字母</a></li>
<li><a href="index-14.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-13.html" target="_top">框架</a></li>
<li><a href="index-13.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="contentContainer"><a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">K</a>&nbsp;<a href="index-11.html">L</a>&nbsp;<a href="index-12.html">M</a>&nbsp;<a href="index-13.html">N</a>&nbsp;<a href="index-14.html">O</a>&nbsp;<a href="index-15.html">P</a>&nbsp;<a href="index-16.html">Q</a>&nbsp;<a href="index-17.html">R</a>&nbsp;<a href="index-18.html">S</a>&nbsp;<a href="index-19.html">T</a>&nbsp;<a href="index-20.html">U</a>&nbsp;<a href="index-21.html">V</a>&nbsp;<a href="index-22.html">W</a>&nbsp;<a name="I:N">
<!--   -->
</a>
<h2 class="title">N</h2>
<dl>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearchV2.ShowFields.html#NAVI">NAVI</a></span> - 类 中的静态变量com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearchV2.ShowFields.html" title="com.amap.api.services.poisearch中的类">PoiSearchV2.ShowFields</a></dt>
<dd>
<div class="block">设置后返回导航位置相关信息</div>
</dd>
<dt><a href="../com/amap/api/services/route/Navi.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">Navi</span></a> - <a href="../com/amap/api/services/route/package-summary.html">com.amap.api.services.route</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/Navi.html#Navi--">Navi()</a></span> - 类 的构造器com.amap.api.services.route.<a href="../com/amap/api/services/route/Navi.html" title="com.amap.api.services.route中的类">Navi</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.ShowFields.html#NAVI">NAVI</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.ShowFields.html" title="com.amap.api.services.route中的类">RouteSearchV2.ShowFields</a></dt>
<dd>
<div class="block">设置后可返回详细导航动作指令</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/share/ShareSearch.html#NaviAvoidCongestion">NaviAvoidCongestion</a></span> - 类 中的静态变量com.amap.api.services.share.<a href="../com/amap/api/services/share/ShareSearch.html" title="com.amap.api.services.share中的类">ShareSearch</a></dt>
<dd>
<div class="block">躲避拥堵</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/share/ShareSearch.html#NaviDefault">NaviDefault</a></span> - 类 中的静态变量com.amap.api.services.share.<a href="../com/amap/api/services/share/ShareSearch.html" title="com.amap.api.services.share中的类">ShareSearch</a></dt>
<dd>
<div class="block">速度最快</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/share/ShareSearch.html#NaviNoHighWay">NaviNoHighWay</a></span> - 类 中的静态变量com.amap.api.services.share.<a href="../com/amap/api/services/share/ShareSearch.html" title="com.amap.api.services.share中的类">ShareSearch</a></dt>
<dd>
<div class="block">不走高速</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/share/ShareSearch.html#NaviNoHighWayAvoidCongestion">NaviNoHighWayAvoidCongestion</a></span> - 类 中的静态变量com.amap.api.services.share.<a href="../com/amap/api/services/share/ShareSearch.html" title="com.amap.api.services.share中的类">ShareSearch</a></dt>
<dd>
<div class="block">不走高速且躲避拥堵</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/share/ShareSearch.html#NaviNoHighWaySaveMoney">NaviNoHighWaySaveMoney</a></span> - 类 中的静态变量com.amap.api.services.share.<a href="../com/amap/api/services/share/ShareSearch.html" title="com.amap.api.services.share中的类">ShareSearch</a></dt>
<dd>
<div class="block">不走高速且避免收费</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/share/ShareSearch.html#NaviNoHighWaySaveMoneyAvoidCongestion">NaviNoHighWaySaveMoneyAvoidCongestion</a></span> - 类 中的静态变量com.amap.api.services.share.<a href="../com/amap/api/services/share/ShareSearch.html" title="com.amap.api.services.share中的类">ShareSearch</a></dt>
<dd>
<div class="block">不走高速躲避收费和拥堵</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/share/ShareSearch.html#NaviSaveMoney">NaviSaveMoney</a></span> - 类 中的静态变量com.amap.api.services.share.<a href="../com/amap/api/services/share/ShareSearch.html" title="com.amap.api.services.share中的类">ShareSearch</a></dt>
<dd>
<div class="block">费用少</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/share/ShareSearch.html#NaviSaveMoneyAvoidCongestion">NaviSaveMoneyAvoidCongestion</a></span> - 类 中的静态变量com.amap.api.services.share.<a href="../com/amap/api/services/share/ShareSearch.html" title="com.amap.api.services.share中的类">ShareSearch</a></dt>
<dd>
<div class="block">躲避收费和拥堵</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/share/ShareSearch.html#NaviShortDistance">NaviShortDistance</a></span> - 类 中的静态变量com.amap.api.services.share.<a href="../com/amap/api/services/share/ShareSearch.html" title="com.amap.api.services.share中的类">ShareSearch</a></dt>
<dd>
<div class="block">距离优先</div>
</dd>
<dt><a href="../com/amap/api/services/route/NaviWalkType.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">NaviWalkType</span></a> - <a href="../com/amap/api/services/route/package-summary.html">com.amap.api.services.route</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/NaviWalkType.html#NaviWalkType--">NaviWalkType()</a></span> - 类 的构造器com.amap.api.services.route.<a href="../com/amap/api/services/route/NaviWalkType.html" title="com.amap.api.services.route中的类">NaviWalkType</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/amap/api/services/nearby/NearbyInfo.html" title="com.amap.api.services.nearby中的类"><span class="typeNameLink">NearbyInfo</span></a> - <a href="../com/amap/api/services/nearby/package-summary.html">com.amap.api.services.nearby</a>中的类</dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span>
<div class="block"><span class="deprecationComment">自7.4.0起不再支持附件功能</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/nearby/NearbyInfo.html#NearbyInfo--">NearbyInfo()</a></span> - 类 的构造器com.amap.api.services.nearby.<a href="../com/amap/api/services/nearby/NearbyInfo.html" title="com.amap.api.services.nearby中的类">NearbyInfo</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/nearby/NearbySearch.NearbyQuery.html#NearbyQuery--">NearbyQuery()</a></span> - 类 的构造器com.amap.api.services.nearby.<a href="../com/amap/api/services/nearby/NearbySearch.NearbyQuery.html" title="com.amap.api.services.nearby中的类">NearbySearch.NearbyQuery</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
&nbsp;</dd>
<dt><a href="../com/amap/api/services/nearby/NearbySearch.html" title="com.amap.api.services.nearby中的类"><span class="typeNameLink">NearbySearch</span></a> - <a href="../com/amap/api/services/nearby/package-summary.html">com.amap.api.services.nearby</a>中的类</dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span>
<div class="block"><span class="deprecationComment">自7.4.0起不再支持附件功能</span></div>
</div>
</dd>
<dt><a href="../com/amap/api/services/nearby/NearbySearch.NearbyListener.html" title="com.amap.api.services.nearby中的接口"><span class="typeNameLink">NearbySearch.NearbyListener</span></a> - <a href="../com/amap/api/services/nearby/package-summary.html">com.amap.api.services.nearby</a>中的接口</dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">本类为附近检索结果的异步处理回调接口。</div>
</dd>
<dt><a href="../com/amap/api/services/nearby/NearbySearch.NearbyQuery.html" title="com.amap.api.services.nearby中的类"><span class="typeNameLink">NearbySearch.NearbyQuery</span></a> - <a href="../com/amap/api/services/nearby/package-summary.html">com.amap.api.services.nearby</a>中的类</dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">NearbyQuery类为检索附近用户信息的条件类。</div>
</dd>
<dt><a href="../com/amap/api/services/nearby/NearbySearchFunctionType.html" title="com.amap.api.services.nearby中的枚举"><span class="typeNameLink">NearbySearchFunctionType</span></a> - <a href="../com/amap/api/services/nearby/package-summary.html">com.amap.api.services.nearby</a>中的枚举</dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span>
<div class="block"><span class="deprecationComment">自7.4.0起不再支持附件功能</span></div>
</div>
</dd>
<dt><a href="../com/amap/api/services/nearby/NearbySearchResult.html" title="com.amap.api.services.nearby中的类"><span class="typeNameLink">NearbySearchResult</span></a> - <a href="../com/amap/api/services/nearby/package-summary.html">com.amap.api.services.nearby</a>中的类</dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span>
<div class="block"><span class="deprecationComment">自7.4.0起不再支持附件功能</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/nearby/NearbySearchResult.html#NearbySearchResult--">NearbySearchResult()</a></span> - 类 的构造器com.amap.api.services.nearby.<a href="../com/amap/api/services/nearby/NearbySearchResult.html" title="com.amap.api.services.nearby中的类">NearbySearchResult</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
&nbsp;</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">K</a>&nbsp;<a href="index-11.html">L</a>&nbsp;<a href="index-12.html">M</a>&nbsp;<a href="index-13.html">N</a>&nbsp;<a href="index-14.html">O</a>&nbsp;<a href="index-15.html">P</a>&nbsp;<a href="index-16.html">Q</a>&nbsp;<a href="index-17.html">R</a>&nbsp;<a href="index-18.html">S</a>&nbsp;<a href="index-19.html">T</a>&nbsp;<a href="index-20.html">U</a>&nbsp;<a href="index-21.html">V</a>&nbsp;<a href="index-22.html">W</a>&nbsp;</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-12.html">上一个字母</a></li>
<li><a href="index-14.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-13.html" target="_top">框架</a></li>
<li><a href="index-13.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
