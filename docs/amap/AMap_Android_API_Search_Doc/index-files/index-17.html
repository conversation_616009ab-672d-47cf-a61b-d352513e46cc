<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>R - 索引</title>
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="R - \u7D22\u5F15";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-16.html">上一个字母</a></li>
<li><a href="index-18.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-17.html" target="_top">框架</a></li>
<li><a href="index-17.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="contentContainer"><a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">K</a>&nbsp;<a href="index-11.html">L</a>&nbsp;<a href="index-12.html">M</a>&nbsp;<a href="index-13.html">N</a>&nbsp;<a href="index-14.html">O</a>&nbsp;<a href="index-15.html">P</a>&nbsp;<a href="index-16.html">Q</a>&nbsp;<a href="index-17.html">R</a>&nbsp;<a href="index-18.html">S</a>&nbsp;<a href="index-19.html">T</a>&nbsp;<a href="index-20.html">U</a>&nbsp;<a href="index-21.html">V</a>&nbsp;<a href="index-22.html">W</a>&nbsp;<a name="I:R">
<!--   -->
</a>
<h2 class="title">R</h2>
<dl>
<dt><a href="../com/amap/api/services/route/Railway.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">Railway</span></a> - <a href="../com/amap/api/services/route/package-summary.html">com.amap.api.services.route</a>中的类</dt>
<dd>
<div class="block">列车信息</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/Railway.html#Railway--">Railway()</a></span> - 类 的构造器com.amap.api.services.route.<a href="../com/amap/api/services/route/Railway.html" title="com.amap.api.services.route中的类">Railway</a></dt>
<dd>
<div class="block">Railway构造方法。</div>
</dd>
<dt><a href="../com/amap/api/services/route/RailwaySpace.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">RailwaySpace</span></a> - <a href="../com/amap/api/services/route/package-summary.html">com.amap.api.services.route</a>中的类</dt>
<dd>
<div class="block">列车的舱位及价格信息
 舱位：动车分一般为一等软座和二等软座</div>
</dd>
<dt><a href="../com/amap/api/services/route/RailwayStationItem.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">RailwayStationItem</span></a> - <a href="../com/amap/api/services/route/package-summary.html">com.amap.api.services.route</a>中的类</dt>
<dd>
<div class="block">定义了火车站点信息</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RailwayStationItem.html#RailwayStationItem--">RailwayStationItem()</a></span> - 类 的构造器com.amap.api.services.route.<a href="../com/amap/api/services/route/RailwayStationItem.html" title="com.amap.api.services.route中的类">RailwayStationItem</a></dt>
<dd>
<div class="block">RailwayStationItem构造方法。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/cloud/CloudSearch.SearchBound.html#RECTANGLE_SHAPE">RECTANGLE_SHAPE</a></span> - 类 中的静态变量com.amap.api.services.cloud.<a href="../com/amap/api/services/cloud/CloudSearch.SearchBound.html" title="com.amap.api.services.cloud中的类">CloudSearch.SearchBound</a></dt>
<dd>
<div class="block">矩形区域</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearch.SearchBound.html#RECTANGLE_SHAPE">RECTANGLE_SHAPE</a></span> - 类 中的静态变量com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearch.SearchBound.html" title="com.amap.api.services.poisearch中的类">PoiSearch.SearchBound</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">矩形区域</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearchV2.SearchBound.html#RECTANGLE_SHAPE">RECTANGLE_SHAPE</a></span> - 类 中的静态变量com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearchV2.SearchBound.html" title="com.amap.api.services.poisearch中的类">PoiSearchV2.SearchBound</a></dt>
<dd>
<div class="block">矩形区域</div>
</dd>
<dt><a href="../com/amap/api/services/geocoder/RegeocodeAddress.html" title="com.amap.api.services.geocoder中的类"><span class="typeNameLink">RegeocodeAddress</span></a> - <a href="../com/amap/api/services/geocoder/package-summary.html">com.amap.api.services.geocoder</a>中的类</dt>
<dd>
<div class="block">逆地理编码返回的结果。</div>
</dd>
<dt><a href="../com/amap/api/services/geocoder/RegeocodeQuery.html" title="com.amap.api.services.geocoder中的类"><span class="typeNameLink">RegeocodeQuery</span></a> - <a href="../com/amap/api/services/geocoder/package-summary.html">com.amap.api.services.geocoder</a>中的类</dt>
<dd>
<div class="block">此类定义了逆地理编码查询的地理坐标点、查询范围、坐标类型。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/RegeocodeQuery.html#RegeocodeQuery-com.amap.api.services.core.LatLonPoint-float-java.lang.String-">RegeocodeQuery(LatLonPoint, float, String)</a></span> - 类 的构造器com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/RegeocodeQuery.html" title="com.amap.api.services.geocoder中的类">RegeocodeQuery</a></dt>
<dd>
<div class="block">RegeocodeQuery构造函数。</div>
</dd>
<dt><a href="../com/amap/api/services/geocoder/RegeocodeResult.html" title="com.amap.api.services.geocoder中的类"><span class="typeNameLink">RegeocodeResult</span></a> - <a href="../com/amap/api/services/geocoder/package-summary.html">com.amap.api.services.geocoder</a>中的类</dt>
<dd>
<div class="block">逆地理编码的搜索结果。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/RegeocodeResult.html#RegeocodeResult-com.amap.api.services.geocoder.RegeocodeQuery-com.amap.api.services.geocoder.RegeocodeAddress-">RegeocodeResult(RegeocodeQuery, RegeocodeAddress)</a></span> - 类 的构造器com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/RegeocodeResult.html" title="com.amap.api.services.geocoder中的类">RegeocodeResult</a></dt>
<dd>
<div class="block">RegeocodeResult构造函数</div>
</dd>
<dt><a href="../com/amap/api/services/geocoder/RegeocodeRoad.html" title="com.amap.api.services.geocoder中的类"><span class="typeNameLink">RegeocodeRoad</span></a> - <a href="../com/amap/api/services/geocoder/package-summary.html">com.amap.api.services.geocoder</a>中的类</dt>
<dd>
<div class="block">逆地理编码返回结果的道路对象。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/nearby/NearbySearch.html#removeNearbyListener-com.amap.api.services.nearby.NearbySearch.NearbyListener-">removeNearbyListener(NearbySearch.NearbyListener)</a></span> - 类 中的方法com.amap.api.services.nearby.<a href="../com/amap/api/services/nearby/NearbySearch.html" title="com.amap.api.services.nearby中的类">NearbySearch</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">移除NearbySearch.NearbyListener监听器对象，可指定移除。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/help/Inputtips.html#requestInputtips--">requestInputtips()</a></span> - 类 中的方法com.amap.api.services.help.<a href="../com/amap/api/services/help/Inputtips.html" title="com.amap.api.services.help中的类">Inputtips</a></dt>
<dd>
<div class="block">查询输入提示的同步接口。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/help/Inputtips.html#requestInputtips-java.lang.String-java.lang.String-">requestInputtips(String, String)</a></span> - 类 中的方法com.amap.api.services.help.<a href="../com/amap/api/services/help/Inputtips.html" title="com.amap.api.services.help中的类">Inputtips</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span>
<div class="block"><span class="deprecationComment">请参考 <a href="../com/amap/api/services/help/Inputtips.html#requestInputtips--"><code>Inputtips.requestInputtips()</code></a></span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/help/Inputtips.html#requestInputtips-java.lang.String-java.lang.String-java.lang.String-">requestInputtips(String, String, String)</a></span> - 类 中的方法com.amap.api.services.help.<a href="../com/amap/api/services/help/Inputtips.html" title="com.amap.api.services.help中的类">Inputtips</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span>
<div class="block"><span class="deprecationComment">请参考 <a href="../com/amap/api/services/help/Inputtips.html#requestInputtips--"><code>Inputtips.requestInputtips()</code></a></span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/help/Inputtips.html#requestInputtipsAsyn--">requestInputtipsAsyn()</a></span> - 类 中的方法com.amap.api.services.help.<a href="../com/amap/api/services/help/Inputtips.html" title="com.amap.api.services.help中的类">Inputtips</a></dt>
<dd>
<div class="block">查询输入提示的异步接口。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearch.Query.html#requireSubPois-boolean-">requireSubPois(boolean)</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearch.Query.html" title="com.amap.api.services.poisearch中的类">PoiSearch.Query</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">设置是否返回父子关系。</div>
</dd>
<dt><a href="../com/amap/api/services/route/RidePath.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">RidePath</span></a> - <a href="../com/amap/api/services/route/package-summary.html">com.amap.api.services.route</a>中的类</dt>
<dd>
<div class="block">定义了骑行路径规划的一个方案。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.RideRouteQuery.html#RideRouteQuery-com.amap.api.services.route.RouteSearch.FromAndTo-int-">RideRouteQuery(RouteSearch.FromAndTo, int)</a></span> - 类 的构造器com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.RideRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearch.RideRouteQuery</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span>
<div class="block"><span class="deprecationComment">因取消mode参数而废弃</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.RideRouteQuery.html#RideRouteQuery-com.amap.api.services.route.RouteSearch.FromAndTo-">RideRouteQuery(RouteSearch.FromAndTo)</a></span> - 类 的构造器com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.RideRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearch.RideRouteQuery</a></dt>
<dd>
<div class="block">RideRouteQuery构造函数。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.RideRouteQuery.html#RideRouteQuery-com.amap.api.services.route.RouteSearchV2.FromAndTo-">RideRouteQuery(RouteSearchV2.FromAndTo)</a></span> - 类 的构造器com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.RideRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearchV2.RideRouteQuery</a></dt>
<dd>
<div class="block">RideRouteQuery构造函数。</div>
</dd>
<dt><a href="../com/amap/api/services/route/RideRouteResult.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">RideRouteResult</span></a> - <a href="../com/amap/api/services/route/package-summary.html">com.amap.api.services.route</a>中的类</dt>
<dd>
<div class="block">定义了骑行路径规划的结果集。</div>
</dd>
<dt><a href="../com/amap/api/services/route/RideRouteResultV2.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">RideRouteResultV2</span></a> - <a href="../com/amap/api/services/route/package-summary.html">com.amap.api.services.route</a>中的类</dt>
<dd>
<div class="block">定义了骑行路径规划的结果集。</div>
</dd>
<dt><a href="../com/amap/api/services/route/RideStep.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">RideStep</span></a> - <a href="../com/amap/api/services/route/package-summary.html">com.amap.api.services.route</a>中的类</dt>
<dd>
<div class="block">定义了骑行路径规划的一个路段。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#RIDING_DEFAULT">RIDING_DEFAULT</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span>
<div class="block"><span class="deprecationComment">骑行不再提供模式相关设置</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#RIDING_FAST">RIDING_FAST</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span>
<div class="block"><span class="deprecationComment">骑行不再提供模式相关设置</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#RIDING_RECOMMEND">RIDING_RECOMMEND</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span>
<div class="block"><span class="deprecationComment">骑行不再提供骑行相关设置</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#RidingDefault">RidingDefault</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span>
<div class="block"><span class="deprecationComment">自V4.0.0以后，请参考 <a href="../com/amap/api/services/route/RouteSearch.html#RIDING_DEFAULT"><code>RouteSearch.RIDING_DEFAULT</code></a></span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#RidingFast">RidingFast</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span>
<div class="block"><span class="deprecationComment">自V4.0.0以后，请参考 <a href="../com/amap/api/services/route/RouteSearch.html#RIDING_FAST"><code>RouteSearch.RIDING_FAST</code></a></span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#RidingRecommend">RidingRecommend</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span>
<div class="block"><span class="deprecationComment">自V4.0.0以后，请参考 <a href="../com/amap/api/services/route/RouteSearch.html#RIDING_RECOMMEND"><code>RouteSearch.RIDING_RECOMMEND</code></a></span></div>
</div>
</dd>
<dt><a href="../com/amap/api/services/road/Road.html" title="com.amap.api.services.road中的类"><span class="typeNameLink">Road</span></a> - <a href="../com/amap/api/services/road/package-summary.html">com.amap.api.services.road</a>中的类</dt>
<dd>
<div class="block">定义道路Road的类。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/road/Road.html#Road-java.lang.String-java.lang.String-">Road(String, String)</a></span> - 类 的构造器com.amap.api.services.road.<a href="../com/amap/api/services/road/Road.html" title="com.amap.api.services.road中的类">Road</a></dt>
<dd>
<div class="block">Road的构造函数。</div>
</dd>
<dt><a href="../com/amap/api/services/route/RouteBusLineItem.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">RouteBusLineItem</span></a> - <a href="../com/amap/api/services/route/package-summary.html">com.amap.api.services.route</a>中的类</dt>
<dd>
<div class="block">定义了公交换乘路径规划的一个换乘段的公交信息。</div>
</dd>
<dt><a href="../com/amap/api/services/route/RouteBusWalkItem.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">RouteBusWalkItem</span></a> - <a href="../com/amap/api/services/route/package-summary.html">com.amap.api.services.route</a>中的类</dt>
<dd>
<div class="block">定义了公交换乘路径规划的一个换乘段的步行信息。</div>
</dd>
<dt><a href="../com/amap/api/services/route/RoutePlanResult.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">RoutePlanResult</span></a> - <a href="../com/amap/api/services/route/package-summary.html">com.amap.api.services.route</a>中的类</dt>
<dd>
<div class="block">定义了路径规划的结果集。</div>
</dd>
<dt><a href="../com/amap/api/services/routepoisearch/RoutePOIItem.html" title="com.amap.api.services.routepoisearch中的类"><span class="typeNameLink">RoutePOIItem</span></a> - <a href="../com/amap/api/services/routepoisearch/package-summary.html">com.amap.api.services.routepoisearch</a>中的类</dt>
<dd>
<div class="block">定义一个沿途搜索POI（Point Of Interest，兴趣点）。</div>
</dd>
<dt><a href="../com/amap/api/services/routepoisearch/RoutePOISearch.html" title="com.amap.api.services.routepoisearch中的类"><span class="typeNameLink">RoutePOISearch</span></a> - <a href="../com/amap/api/services/routepoisearch/package-summary.html">com.amap.api.services.routepoisearch</a>中的类</dt>
<dd>
<div class="block">本类为沿途POI搜索的“入口”类，定义此类，开始搜索。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/routepoisearch/RoutePOISearch.html#RoutePOISearch-Context-com.amap.api.services.routepoisearch.RoutePOISearchQuery-">RoutePOISearch(Context, RoutePOISearchQuery)</a></span> - 类 的构造器com.amap.api.services.routepoisearch.<a href="../com/amap/api/services/routepoisearch/RoutePOISearch.html" title="com.amap.api.services.routepoisearch中的类">RoutePOISearch</a></dt>
<dd>
<div class="block">沿途搜索构造方法。</div>
</dd>
<dt><a href="../com/amap/api/services/routepoisearch/RoutePOISearch.OnRoutePOISearchListener.html" title="com.amap.api.services.routepoisearch中的接口"><span class="typeNameLink">RoutePOISearch.OnRoutePOISearchListener</span></a> - <a href="../com/amap/api/services/routepoisearch/package-summary.html">com.amap.api.services.routepoisearch</a>中的接口</dt>
<dd>
<div class="block">本类为沿途搜索POI（Point Of Interest，兴趣点）结果的异步处理回调接口。</div>
</dd>
<dt><a href="../com/amap/api/services/routepoisearch/RoutePOISearch.RoutePOISearchType.html" title="com.amap.api.services.routepoisearch中的枚举"><span class="typeNameLink">RoutePOISearch.RoutePOISearchType</span></a> - <a href="../com/amap/api/services/routepoisearch/package-summary.html">com.amap.api.services.routepoisearch</a>中的枚举</dt>
<dd>
<div class="block">搜索的type类型
 TypeGasStation 加油站；TypeMaintenanceStation 维修站；TypeATM 自助银行；TypeToilet 卫生间。</div>
</dd>
<dt><a href="../com/amap/api/services/routepoisearch/RoutePOISearchQuery.html" title="com.amap.api.services.routepoisearch中的类"><span class="typeNameLink">RoutePOISearchQuery</span></a> - <a href="../com/amap/api/services/routepoisearch/package-summary.html">com.amap.api.services.routepoisearch</a>中的类</dt>
<dd>
<div class="block">此类定义了沿途POI搜索的参数。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/routepoisearch/RoutePOISearchQuery.html#RoutePOISearchQuery-com.amap.api.services.core.LatLonPoint-com.amap.api.services.core.LatLonPoint-int-com.amap.api.services.routepoisearch.RoutePOISearch.RoutePOISearchType-int-">RoutePOISearchQuery(LatLonPoint, LatLonPoint, int, RoutePOISearch.RoutePOISearchType, int)</a></span> - 类 的构造器com.amap.api.services.routepoisearch.<a href="../com/amap/api/services/routepoisearch/RoutePOISearchQuery.html" title="com.amap.api.services.routepoisearch中的类">RoutePOISearchQuery</a></dt>
<dd>
<div class="block">根据给定的参数来构造一个 RoutePOISearchQuery 的新对象。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/routepoisearch/RoutePOISearchQuery.html#RoutePOISearchQuery-java.util.List-com.amap.api.services.routepoisearch.RoutePOISearch.RoutePOISearchType-int-">RoutePOISearchQuery(List&lt;LatLonPoint&gt;, RoutePOISearch.RoutePOISearchType, int)</a></span> - 类 的构造器com.amap.api.services.routepoisearch.<a href="../com/amap/api/services/routepoisearch/RoutePOISearchQuery.html" title="com.amap.api.services.routepoisearch中的类">RoutePOISearchQuery</a></dt>
<dd>
<div class="block">根据给定的参数来构造一个 RoutePOISearchQuery 的新对象。</div>
</dd>
<dt><a href="../com/amap/api/services/routepoisearch/RoutePOISearchResult.html" title="com.amap.api.services.routepoisearch中的类"><span class="typeNameLink">RoutePOISearchResult</span></a> - <a href="../com/amap/api/services/routepoisearch/package-summary.html">com.amap.api.services.routepoisearch</a>中的类</dt>
<dd>
<div class="block">此类定义了沿途搜索POI的结果集。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/routepoisearch/RoutePOISearchResult.html#RoutePOISearchResult-java.util.ArrayList-com.amap.api.services.routepoisearch.RoutePOISearchQuery-">RoutePOISearchResult(ArrayList&lt;RoutePOIItem&gt;, RoutePOISearchQuery)</a></span> - 类 的构造器com.amap.api.services.routepoisearch.<a href="../com/amap/api/services/routepoisearch/RoutePOISearchResult.html" title="com.amap.api.services.routepoisearch中的类">RoutePOISearchResult</a></dt>
<dd>
<div class="block">依据参数构造沿途搜索查询结果</div>
</dd>
<dt><a href="../com/amap/api/services/route/RouteRailwayItem.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">RouteRailwayItem</span></a> - <a href="../com/amap/api/services/route/package-summary.html">com.amap.api.services.route</a>中的类</dt>
<dd>
<div class="block">火车乘坐信息</div>
</dd>
<dt><a href="../com/amap/api/services/route/RouteResult.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">RouteResult</span></a> - <a href="../com/amap/api/services/route/package-summary.html">com.amap.api.services.route</a>中的类</dt>
<dd>
<div class="block">定义了路径规划的结果集。</div>
</dd>
<dt><a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">RouteSearch</span></a> - <a href="../com/amap/api/services/route/package-summary.html">com.amap.api.services.route</a>中的类</dt>
<dd>
<div class="block">该类路径规划搜索的入口，定义此类开始路径规划搜索</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#RouteSearch-Context-">RouteSearch(Context)</a></span> - 类 的构造器com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block">根据给定的参数来构造一个RouteSearch的新对象。</div>
</dd>
<dt><a href="../com/amap/api/services/route/RouteSearch.BusRouteQuery.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">RouteSearch.BusRouteQuery</span></a> - <a href="../com/amap/api/services/route/package-summary.html">com.amap.api.services.route</a>中的类</dt>
<dd>
<div class="block">此类定义了公交路径规划查询路径的起终点、计算路径的模式、城市和是否计算夜班车。</div>
</dd>
<dt><a href="../com/amap/api/services/route/RouteSearch.DrivePlanQuery.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">RouteSearch.DrivePlanQuery</span></a> - <a href="../com/amap/api/services/route/package-summary.html">com.amap.api.services.route</a>中的类</dt>
<dd>
<div class="block">此类定义了驾车未来路径查询规划,最大支持未来七天的规划。</div>
</dd>
<dt><a href="../com/amap/api/services/route/RouteSearch.DriveRouteQuery.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">RouteSearch.DriveRouteQuery</span></a> - <a href="../com/amap/api/services/route/package-summary.html">com.amap.api.services.route</a>中的类</dt>
<dd>
<div class="block">此类定义了驾车路径查询规划。</div>
</dd>
<dt><a href="../com/amap/api/services/route/RouteSearch.FromAndTo.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">RouteSearch.FromAndTo</span></a> - <a href="../com/amap/api/services/route/package-summary.html">com.amap.api.services.route</a>中的类</dt>
<dd>
<div class="block">构造路径规划的起点和终点坐标。</div>
</dd>
<dt><a href="../com/amap/api/services/route/RouteSearch.OnRoutePlanSearchListener.html" title="com.amap.api.services.route中的接口"><span class="typeNameLink">RouteSearch.OnRoutePlanSearchListener</span></a> - <a href="../com/amap/api/services/route/package-summary.html">com.amap.api.services.route</a>中的接口</dt>
<dd>
<div class="block">未来路径规划回调方法</div>
</dd>
<dt><a href="../com/amap/api/services/route/RouteSearch.OnRouteSearchListener.html" title="com.amap.api.services.route中的接口"><span class="typeNameLink">RouteSearch.OnRouteSearchListener</span></a> - <a href="../com/amap/api/services/route/package-summary.html">com.amap.api.services.route</a>中的接口</dt>
<dd>
<div class="block">本类为路径搜索结果的异步处理回调接口。</div>
</dd>
<dt><a href="../com/amap/api/services/route/RouteSearch.OnTruckRouteSearchListener.html" title="com.amap.api.services.route中的接口"><span class="typeNameLink">RouteSearch.OnTruckRouteSearchListener</span></a> - <a href="../com/amap/api/services/route/package-summary.html">com.amap.api.services.route</a>中的接口</dt>
<dd>
<div class="block">货车路径规划回调方法</div>
</dd>
<dt><a href="../com/amap/api/services/route/RouteSearch.RideRouteQuery.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">RouteSearch.RideRouteQuery</span></a> - <a href="../com/amap/api/services/route/package-summary.html">com.amap.api.services.route</a>中的类</dt>
<dd>
<div class="block">此类定义了骑行路径的起终点和计算路径的模式。</div>
</dd>
<dt><a href="../com/amap/api/services/route/RouteSearch.TruckRouteQuery.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">RouteSearch.TruckRouteQuery</span></a> - <a href="../com/amap/api/services/route/package-summary.html">com.amap.api.services.route</a>中的类</dt>
<dd>
<div class="block">此类定义了货车路径的起终点和计算路径的模式。</div>
</dd>
<dt><a href="../com/amap/api/services/route/RouteSearch.WalkRouteQuery.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">RouteSearch.WalkRouteQuery</span></a> - <a href="../com/amap/api/services/route/package-summary.html">com.amap.api.services.route</a>中的类</dt>
<dd>
<div class="block">此类定义了步行路径的起终点和计算路径的模式。</div>
</dd>
<dt><a href="../com/amap/api/services/route/RouteSearchCity.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">RouteSearchCity</span></a> - <a href="../com/amap/api/services/route/package-summary.html">com.amap.api.services.route</a>中的类</dt>
<dd>
<div class="block">此类定义路径规划返回的城市名称、编码和行政区的名称和编码。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchCity.html#RouteSearchCity--">RouteSearchCity()</a></span> - 类 的构造器com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchCity.html" title="com.amap.api.services.route中的类">RouteSearchCity</a></dt>
<dd>
<div class="block">搜索路径规划返回城市和行政区的构造方法。</div>
</dd>
<dt><a href="../com/amap/api/services/route/RouteSearchV2.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">RouteSearchV2</span></a> - <a href="../com/amap/api/services/route/package-summary.html">com.amap.api.services.route</a>中的类</dt>
<dd>
<div class="block">该类路径规划搜索的入口，定义此类开始路径规划搜索</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.html#RouteSearchV2-Context-">RouteSearchV2(Context)</a></span> - 类 的构造器com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.html" title="com.amap.api.services.route中的类">RouteSearchV2</a></dt>
<dd>
<div class="block">根据给定的参数来构造一个RouteSearch的新对象。</div>
</dd>
<dt><a href="../com/amap/api/services/route/RouteSearchV2.AlternativeRoute.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">RouteSearchV2.AlternativeRoute</span></a> - <a href="../com/amap/api/services/route/package-summary.html">com.amap.api.services.route</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><a href="../com/amap/api/services/route/RouteSearchV2.BusMode.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">RouteSearchV2.BusMode</span></a> - <a href="../com/amap/api/services/route/package-summary.html">com.amap.api.services.route</a>中的类</dt>
<dd>
<div class="block">公交线路模式</div>
</dd>
<dt><a href="../com/amap/api/services/route/RouteSearchV2.BusRouteQuery.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">RouteSearchV2.BusRouteQuery</span></a> - <a href="../com/amap/api/services/route/package-summary.html">com.amap.api.services.route</a>中的类</dt>
<dd>
<div class="block">此类定义了公交路径规划查询路径的起终点、计算路径的模式、城市和是否计算夜班车。</div>
</dd>
<dt><a href="../com/amap/api/services/route/RouteSearchV2.DriveRouteQuery.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">RouteSearchV2.DriveRouteQuery</span></a> - <a href="../com/amap/api/services/route/package-summary.html">com.amap.api.services.route</a>中的类</dt>
<dd>
<div class="block">此类定义了驾车路径查询规划。</div>
</dd>
<dt><a href="../com/amap/api/services/route/RouteSearchV2.DrivingStrategy.html" title="com.amap.api.services.route中的枚举"><span class="typeNameLink">RouteSearchV2.DrivingStrategy</span></a> - <a href="../com/amap/api/services/route/package-summary.html">com.amap.api.services.route</a>中的枚举</dt>
<dd>
<div class="block">驾车策略</div>
</dd>
<dt><a href="../com/amap/api/services/route/RouteSearchV2.FromAndTo.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">RouteSearchV2.FromAndTo</span></a> - <a href="../com/amap/api/services/route/package-summary.html">com.amap.api.services.route</a>中的类</dt>
<dd>
<div class="block">构造路径规划的起点和终点坐标。</div>
</dd>
<dt><a href="../com/amap/api/services/route/RouteSearchV2.OnRoutePlanSearchListener.html" title="com.amap.api.services.route中的接口"><span class="typeNameLink">RouteSearchV2.OnRoutePlanSearchListener</span></a> - <a href="../com/amap/api/services/route/package-summary.html">com.amap.api.services.route</a>中的接口</dt>
<dd>
<div class="block">未来路径规划回调方法</div>
</dd>
<dt><a href="../com/amap/api/services/route/RouteSearchV2.OnRouteSearchListener.html" title="com.amap.api.services.route中的接口"><span class="typeNameLink">RouteSearchV2.OnRouteSearchListener</span></a> - <a href="../com/amap/api/services/route/package-summary.html">com.amap.api.services.route</a>中的接口</dt>
<dd>
<div class="block">本类为路径搜索结果的异步处理回调接口。</div>
</dd>
<dt><a href="../com/amap/api/services/route/RouteSearchV2.OnTruckRouteSearchListener.html" title="com.amap.api.services.route中的接口"><span class="typeNameLink">RouteSearchV2.OnTruckRouteSearchListener</span></a> - <a href="../com/amap/api/services/route/package-summary.html">com.amap.api.services.route</a>中的接口</dt>
<dd>
<div class="block">货车路径规划回调方法</div>
</dd>
<dt><a href="../com/amap/api/services/route/RouteSearchV2.RideRouteQuery.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">RouteSearchV2.RideRouteQuery</span></a> - <a href="../com/amap/api/services/route/package-summary.html">com.amap.api.services.route</a>中的类</dt>
<dd>
<div class="block">此类定义了骑行路径的起终点和计算路径的模式。</div>
</dd>
<dt><a href="../com/amap/api/services/route/RouteSearchV2.ShowFields.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">RouteSearchV2.ShowFields</span></a> - <a href="../com/amap/api/services/route/package-summary.html">com.amap.api.services.route</a>中的类</dt>
<dd>
<div class="block">扩展字段</div>
</dd>
<dt><a href="../com/amap/api/services/route/RouteSearchV2.WalkRouteQuery.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">RouteSearchV2.WalkRouteQuery</span></a> - <a href="../com/amap/api/services/route/package-summary.html">com.amap.api.services.route</a>中的类</dt>
<dd>
<div class="block">此类定义了步行路径的起终点和计算路径的模式。</div>
</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">K</a>&nbsp;<a href="index-11.html">L</a>&nbsp;<a href="index-12.html">M</a>&nbsp;<a href="index-13.html">N</a>&nbsp;<a href="index-14.html">O</a>&nbsp;<a href="index-15.html">P</a>&nbsp;<a href="index-16.html">Q</a>&nbsp;<a href="index-17.html">R</a>&nbsp;<a href="index-18.html">S</a>&nbsp;<a href="index-19.html">T</a>&nbsp;<a href="index-20.html">U</a>&nbsp;<a href="index-21.html">V</a>&nbsp;<a href="index-22.html">W</a>&nbsp;</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-16.html">上一个字母</a></li>
<li><a href="index-18.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-17.html" target="_top">框架</a></li>
<li><a href="index-17.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
