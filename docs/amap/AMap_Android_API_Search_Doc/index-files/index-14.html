<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>O - 索引</title>
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="O - \u7D22\u5F15";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-13.html">上一个字母</a></li>
<li><a href="index-15.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-14.html" target="_top">框架</a></li>
<li><a href="index-14.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="contentContainer"><a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">K</a>&nbsp;<a href="index-11.html">L</a>&nbsp;<a href="index-12.html">M</a>&nbsp;<a href="index-13.html">N</a>&nbsp;<a href="index-14.html">O</a>&nbsp;<a href="index-15.html">P</a>&nbsp;<a href="index-16.html">Q</a>&nbsp;<a href="index-17.html">R</a>&nbsp;<a href="index-18.html">S</a>&nbsp;<a href="index-19.html">T</a>&nbsp;<a href="index-20.html">U</a>&nbsp;<a href="index-21.html">V</a>&nbsp;<a href="index-22.html">W</a>&nbsp;<a name="I:O">
<!--   -->
</a>
<h2 class="title">O</h2>
<dl>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/busline/BusLineSearch.OnBusLineSearchListener.html#onBusLineSearched-com.amap.api.services.busline.BusLineResult-int-">onBusLineSearched(BusLineResult, int)</a></span> - 接口 中的方法com.amap.api.services.busline.<a href="../com/amap/api/services/busline/BusLineSearch.OnBusLineSearchListener.html" title="com.amap.api.services.busline中的接口">BusLineSearch.OnBusLineSearchListener</a></dt>
<dd>
<div class="block">公交路线的查询异步处理。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.OnRouteSearchListener.html#onBusRouteSearched-com.amap.api.services.route.BusRouteResult-int-">onBusRouteSearched(BusRouteResult, int)</a></span> - 接口 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.OnRouteSearchListener.html" title="com.amap.api.services.route中的接口">RouteSearch.OnRouteSearchListener</a></dt>
<dd>
<div class="block">公交换乘路径规划结果的回调方法。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.OnRouteSearchListener.html#onBusRouteSearched-com.amap.api.services.route.BusRouteResultV2-int-">onBusRouteSearched(BusRouteResultV2, int)</a></span> - 接口 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.OnRouteSearchListener.html" title="com.amap.api.services.route中的接口">RouteSearchV2.OnRouteSearchListener</a></dt>
<dd>
<div class="block">公交换乘路径规划结果的回调方法。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/share/ShareSearch.OnShareSearchListener.html#onBusRouteShareUrlSearched-java.lang.String-int-">onBusRouteShareUrlSearched(String, int)</a></span> - 接口 中的方法com.amap.api.services.share.<a href="../com/amap/api/services/share/ShareSearch.OnShareSearchListener.html" title="com.amap.api.services.share中的接口">ShareSearch.OnShareSearchListener</a></dt>
<dd>
<div class="block">公交路径规划分享的异步处理。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/busline/BusStationSearch.OnBusStationSearchListener.html#onBusStationSearched-com.amap.api.services.busline.BusStationResult-int-">onBusStationSearched(BusStationResult, int)</a></span> - 接口 中的方法com.amap.api.services.busline.<a href="../com/amap/api/services/busline/BusStationSearch.OnBusStationSearchListener.html" title="com.amap.api.services.busline中的接口">BusStationSearch.OnBusStationSearchListener</a></dt>
<dd>
<div class="block">公交站点的查询异步处理。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/cloud/CloudSearch.OnCloudSearchListener.html#onCloudItemDetailSearched-com.amap.api.services.cloud.CloudItemDetail-int-">onCloudItemDetailSearched(CloudItemDetail, int)</a></span> - 接口 中的方法com.amap.api.services.cloud.<a href="../com/amap/api/services/cloud/CloudSearch.OnCloudSearchListener.html" title="com.amap.api.services.cloud中的接口">CloudSearch.OnCloudSearchListener</a></dt>
<dd>
<div class="block">返回Cloud详情搜索异步处理的结果。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/cloud/CloudSearch.OnCloudSearchListener.html#onCloudSearched-com.amap.api.services.cloud.CloudResult-int-">onCloudSearched(CloudResult, int)</a></span> - 接口 中的方法com.amap.api.services.cloud.<a href="../com/amap/api/services/cloud/CloudSearch.OnCloudSearchListener.html" title="com.amap.api.services.cloud中的接口">CloudSearch.OnCloudSearchListener</a></dt>
<dd>
<div class="block">返回Cloud搜索异步处理的结果。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/DistanceSearch.OnDistanceSearchListener.html#onDistanceSearched-com.amap.api.services.route.DistanceResult-int-">onDistanceSearched(DistanceResult, int)</a></span> - 接口 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/DistanceSearch.OnDistanceSearchListener.html" title="com.amap.api.services.route中的接口">DistanceSearch.OnDistanceSearchListener</a></dt>
<dd>
<div class="block">返回距离测量搜索异步处理结果</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/district/DistrictSearch.OnDistrictSearchListener.html#onDistrictSearched-com.amap.api.services.district.DistrictResult-">onDistrictSearched(DistrictResult)</a></span> - 接口 中的方法com.amap.api.services.district.<a href="../com/amap/api/services/district/DistrictSearch.OnDistrictSearchListener.html" title="com.amap.api.services.district中的接口">DistrictSearch.OnDistrictSearchListener</a></dt>
<dd>
<div class="block">返回District（行政区划）异步处理的结果。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.OnRoutePlanSearchListener.html#onDriveRoutePlanSearched-com.amap.api.services.route.DriveRoutePlanResult-int-">onDriveRoutePlanSearched(DriveRoutePlanResult, int)</a></span> - 接口 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.OnRoutePlanSearchListener.html" title="com.amap.api.services.route中的接口">RouteSearch.OnRoutePlanSearchListener</a></dt>
<dd>
<div class="block">货车路径规划回调方法</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.OnRoutePlanSearchListener.html#onDriveRoutePlanSearched-com.amap.api.services.route.DriveRoutePlanResult-int-">onDriveRoutePlanSearched(DriveRoutePlanResult, int)</a></span> - 接口 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.OnRoutePlanSearchListener.html" title="com.amap.api.services.route中的接口">RouteSearchV2.OnRoutePlanSearchListener</a></dt>
<dd>
<div class="block">货车路径规划回调方法</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.OnRouteSearchListener.html#onDriveRouteSearched-com.amap.api.services.route.DriveRouteResult-int-">onDriveRouteSearched(DriveRouteResult, int)</a></span> - 接口 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.OnRouteSearchListener.html" title="com.amap.api.services.route中的接口">RouteSearch.OnRouteSearchListener</a></dt>
<dd>
<div class="block">驾车路径规划结果的回调方法。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.OnRouteSearchListener.html#onDriveRouteSearched-com.amap.api.services.route.DriveRouteResultV2-int-">onDriveRouteSearched(DriveRouteResultV2, int)</a></span> - 接口 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.OnRouteSearchListener.html" title="com.amap.api.services.route中的接口">RouteSearchV2.OnRouteSearchListener</a></dt>
<dd>
<div class="block">驾车路径规划结果的回调方法。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/share/ShareSearch.OnShareSearchListener.html#onDrivingRouteShareUrlSearched-java.lang.String-int-">onDrivingRouteShareUrlSearched(String, int)</a></span> - 接口 中的方法com.amap.api.services.share.<a href="../com/amap/api/services/share/ShareSearch.OnShareSearchListener.html" title="com.amap.api.services.share中的接口">ShareSearch.OnShareSearchListener</a></dt>
<dd>
<div class="block">驾车路径规划分享的异步处理。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/GeocodeSearch.OnGeocodeSearchListener.html#onGeocodeSearched-com.amap.api.services.geocoder.GeocodeResult-int-">onGeocodeSearched(GeocodeResult, int)</a></span> - 接口 中的方法com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/GeocodeSearch.OnGeocodeSearchListener.html" title="com.amap.api.services.geocoder中的接口">GeocodeSearch.OnGeocodeSearchListener</a></dt>
<dd>
<div class="block">根据给定的地理名称和查询城市，返回地理编码的结果列表。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/help/Inputtips.InputtipsListener.html#onGetInputtips-java.util.List-int-">onGetInputtips(List&lt;Tip&gt;, int)</a></span> - 接口 中的方法com.amap.api.services.help.<a href="../com/amap/api/services/help/Inputtips.InputtipsListener.html" title="com.amap.api.services.help中的接口">Inputtips.InputtipsListener</a></dt>
<dd>
<div class="block">输入提示回调的方法。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/share/ShareSearch.OnShareSearchListener.html#onLocationShareUrlSearched-java.lang.String-int-">onLocationShareUrlSearched(String, int)</a></span> - 接口 中的方法com.amap.api.services.share.<a href="../com/amap/api/services/share/ShareSearch.OnShareSearchListener.html" title="com.amap.api.services.share中的接口">ShareSearch.OnShareSearchListener</a></dt>
<dd>
<div class="block">位置分享的异步处理。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/share/ShareSearch.OnShareSearchListener.html#onNaviShareUrlSearched-java.lang.String-int-">onNaviShareUrlSearched(String, int)</a></span> - 接口 中的方法com.amap.api.services.share.<a href="../com/amap/api/services/share/ShareSearch.OnShareSearchListener.html" title="com.amap.api.services.share中的接口">ShareSearch.OnShareSearchListener</a></dt>
<dd>
<div class="block">导航分享的异步处理。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/nearby/NearbySearch.NearbyListener.html#onNearbyInfoSearched-com.amap.api.services.nearby.NearbySearchResult-int-">onNearbyInfoSearched(NearbySearchResult, int)</a></span> - 接口 中的方法com.amap.api.services.nearby.<a href="../com/amap/api/services/nearby/NearbySearch.NearbyListener.html" title="com.amap.api.services.nearby中的接口">NearbySearch.NearbyListener</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">查询周边的人的回调接口。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/nearby/NearbySearch.NearbyListener.html#onNearbyInfoUploaded-int-">onNearbyInfoUploaded(int)</a></span> - 接口 中的方法com.amap.api.services.nearby.<a href="../com/amap/api/services/nearby/NearbySearch.NearbyListener.html" title="com.amap.api.services.nearby中的接口">NearbySearch.NearbyListener</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">上传用户信息的回调监听。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearch.OnPoiSearchListener.html#onPoiItemSearched-com.amap.api.services.core.PoiItem-int-">onPoiItemSearched(PoiItem, int)</a></span> - 接口 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearch.OnPoiSearchListener.html" title="com.amap.api.services.poisearch中的接口">PoiSearch.OnPoiSearchListener</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">poi id搜索的结果回调</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearchV2.OnPoiSearchListener.html#onPoiItemSearched-PoiItemV2-int-">onPoiItemSearched(PoiItemV2, int)</a></span> - 接口 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearchV2.OnPoiSearchListener.html" title="com.amap.api.services.poisearch中的接口">PoiSearchV2.OnPoiSearchListener</a></dt>
<dd>
<div class="block">poi id搜索的结果回调</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearch.OnPoiSearchListener.html#onPoiSearched-com.amap.api.services.poisearch.PoiResult-int-">onPoiSearched(PoiResult, int)</a></span> - 接口 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearch.OnPoiSearchListener.html" title="com.amap.api.services.poisearch中的接口">PoiSearch.OnPoiSearchListener</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">返回POI搜索异步处理的结果。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearchV2.OnPoiSearchListener.html#onPoiSearched-com.amap.api.services.poisearch.PoiResultV2-int-">onPoiSearched(PoiResultV2, int)</a></span> - 接口 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearchV2.OnPoiSearchListener.html" title="com.amap.api.services.poisearch中的接口">PoiSearchV2.OnPoiSearchListener</a></dt>
<dd>
<div class="block">返回POI搜索异步处理的结果。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/share/ShareSearch.OnShareSearchListener.html#onPoiShareUrlSearched-java.lang.String-int-">onPoiShareUrlSearched(String, int)</a></span> - 接口 中的方法com.amap.api.services.share.<a href="../com/amap/api/services/share/ShareSearch.OnShareSearchListener.html" title="com.amap.api.services.share中的接口">ShareSearch.OnShareSearchListener</a></dt>
<dd>
<div class="block">POI分享的异步处理。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/GeocodeSearch.OnGeocodeSearchListener.html#onRegeocodeSearched-com.amap.api.services.geocoder.RegeocodeResult-int-">onRegeocodeSearched(RegeocodeResult, int)</a></span> - 接口 中的方法com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/GeocodeSearch.OnGeocodeSearchListener.html" title="com.amap.api.services.geocoder中的接口">GeocodeSearch.OnGeocodeSearchListener</a></dt>
<dd>
<div class="block">根据给定的经纬度和最大结果数返回逆地理编码的结果列表。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.OnRouteSearchListener.html#onRideRouteSearched-com.amap.api.services.route.RideRouteResult-int-">onRideRouteSearched(RideRouteResult, int)</a></span> - 接口 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.OnRouteSearchListener.html" title="com.amap.api.services.route中的接口">RouteSearch.OnRouteSearchListener</a></dt>
<dd>
<div class="block">骑行路径规划结果的回调方法。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.OnRouteSearchListener.html#onRideRouteSearched-com.amap.api.services.route.RideRouteResultV2-int-">onRideRouteSearched(RideRouteResultV2, int)</a></span> - 接口 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.OnRouteSearchListener.html" title="com.amap.api.services.route中的接口">RouteSearchV2.OnRouteSearchListener</a></dt>
<dd>
<div class="block">骑行路径规划结果的回调方法。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/routepoisearch/RoutePOISearch.OnRoutePOISearchListener.html#onRoutePoiSearched-com.amap.api.services.routepoisearch.RoutePOISearchResult-int-">onRoutePoiSearched(RoutePOISearchResult, int)</a></span> - 接口 中的方法com.amap.api.services.routepoisearch.<a href="../com/amap/api/services/routepoisearch/RoutePOISearch.OnRoutePOISearchListener.html" title="com.amap.api.services.routepoisearch中的接口">RoutePOISearch.OnRoutePOISearchListener</a></dt>
<dd>
<div class="block">返回POI搜索异步处理的结果。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.OnTruckRouteSearchListener.html#onTruckRouteSearched-com.amap.api.services.route.TruckRouteRestult-int-">onTruckRouteSearched(TruckRouteRestult, int)</a></span> - 接口 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.OnTruckRouteSearchListener.html" title="com.amap.api.services.route中的接口">RouteSearch.OnTruckRouteSearchListener</a></dt>
<dd>
<div class="block">货车路径规划回调方法</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.OnTruckRouteSearchListener.html#onTruckRouteSearched-com.amap.api.services.route.TruckRouteRestult-int-">onTruckRouteSearched(TruckRouteRestult, int)</a></span> - 接口 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.OnTruckRouteSearchListener.html" title="com.amap.api.services.route中的接口">RouteSearchV2.OnTruckRouteSearchListener</a></dt>
<dd>
<div class="block">货车路径规划回调方法</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/nearby/UploadInfoCallback.html#OnUploadInfoCallback--">OnUploadInfoCallback()</a></span> - 接口 中的方法com.amap.api.services.nearby.<a href="../com/amap/api/services/nearby/UploadInfoCallback.html" title="com.amap.api.services.nearby中的接口">UploadInfoCallback</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">构造上传信息的参数接口回调函数。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/nearby/NearbySearch.NearbyListener.html#onUserInfoCleared-int-">onUserInfoCleared(int)</a></span> - 接口 中的方法com.amap.api.services.nearby.<a href="../com/amap/api/services/nearby/NearbySearch.NearbyListener.html" title="com.amap.api.services.nearby中的接口">NearbySearch.NearbyListener</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">用户信息清除回调接口。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.OnRouteSearchListener.html#onWalkRouteSearched-com.amap.api.services.route.WalkRouteResult-int-">onWalkRouteSearched(WalkRouteResult, int)</a></span> - 接口 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.OnRouteSearchListener.html" title="com.amap.api.services.route中的接口">RouteSearch.OnRouteSearchListener</a></dt>
<dd>
<div class="block">步行路径规划结果的回调方法。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.OnRouteSearchListener.html#onWalkRouteSearched-com.amap.api.services.route.WalkRouteResultV2-int-">onWalkRouteSearched(WalkRouteResultV2, int)</a></span> - 接口 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.OnRouteSearchListener.html" title="com.amap.api.services.route中的接口">RouteSearchV2.OnRouteSearchListener</a></dt>
<dd>
<div class="block">步行路径规划结果的回调方法。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/share/ShareSearch.OnShareSearchListener.html#onWalkRouteShareUrlSearched-java.lang.String-int-">onWalkRouteShareUrlSearched(String, int)</a></span> - 接口 中的方法com.amap.api.services.share.<a href="../com/amap/api/services/share/ShareSearch.OnShareSearchListener.html" title="com.amap.api.services.share中的接口">ShareSearch.OnShareSearchListener</a></dt>
<dd>
<div class="block">步行路径规划分享的异步处理。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/weather/WeatherSearch.OnWeatherSearchListener.html#onWeatherForecastSearched-com.amap.api.services.weather.LocalWeatherForecastResult-int-">onWeatherForecastSearched(LocalWeatherForecastResult, int)</a></span> - 接口 中的方法com.amap.api.services.weather.<a href="../com/amap/api/services/weather/WeatherSearch.OnWeatherSearchListener.html" title="com.amap.api.services.weather中的接口">WeatherSearch.OnWeatherSearchListener</a></dt>
<dd>
<div class="block">返回Weather异步处理的预报天气结果。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/weather/WeatherSearch.OnWeatherSearchListener.html#onWeatherLiveSearched-com.amap.api.services.weather.LocalWeatherLiveResult-int-">onWeatherLiveSearched(LocalWeatherLiveResult, int)</a></span> - 接口 中的方法com.amap.api.services.weather.<a href="../com/amap/api/services/weather/WeatherSearch.OnWeatherSearchListener.html" title="com.amap.api.services.weather中的接口">WeatherSearch.OnWeatherSearchListener</a></dt>
<dd>
<div class="block">返回Weather异步处理的实况天气结果。</div>
</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">K</a>&nbsp;<a href="index-11.html">L</a>&nbsp;<a href="index-12.html">M</a>&nbsp;<a href="index-13.html">N</a>&nbsp;<a href="index-14.html">O</a>&nbsp;<a href="index-15.html">P</a>&nbsp;<a href="index-16.html">Q</a>&nbsp;<a href="index-17.html">R</a>&nbsp;<a href="index-18.html">S</a>&nbsp;<a href="index-19.html">T</a>&nbsp;<a href="index-20.html">U</a>&nbsp;<a href="index-21.html">V</a>&nbsp;<a href="index-22.html">W</a>&nbsp;</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-13.html">上一个字母</a></li>
<li><a href="index-15.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-14.html" target="_top">框架</a></li>
<li><a href="index-14.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
