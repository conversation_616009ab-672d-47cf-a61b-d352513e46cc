<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>B - 索引</title>
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="B - \u7D22\u5F15";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-1.html">上一个字母</a></li>
<li><a href="index-3.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-2.html" target="_top">框架</a></li>
<li><a href="index-2.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="contentContainer"><a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">K</a>&nbsp;<a href="index-11.html">L</a>&nbsp;<a href="index-12.html">M</a>&nbsp;<a href="index-13.html">N</a>&nbsp;<a href="index-14.html">O</a>&nbsp;<a href="index-15.html">P</a>&nbsp;<a href="index-16.html">Q</a>&nbsp;<a href="index-17.html">R</a>&nbsp;<a href="index-18.html">S</a>&nbsp;<a href="index-19.html">T</a>&nbsp;<a href="index-20.html">U</a>&nbsp;<a href="index-21.html">V</a>&nbsp;<a href="index-22.html">W</a>&nbsp;<a name="I:B">
<!--   -->
</a>
<h2 class="title">B</h2>
<dl>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/cloud/CloudSearch.SearchBound.html#BOUND_SHAPE">BOUND_SHAPE</a></span> - 类 中的静态变量com.amap.api.services.cloud.<a href="../com/amap/api/services/cloud/CloudSearch.SearchBound.html" title="com.amap.api.services.cloud中的类">CloudSearch.SearchBound</a></dt>
<dd>
<div class="block">圆形区域</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearch.SearchBound.html#BOUND_SHAPE">BOUND_SHAPE</a></span> - 类 中的静态变量com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearch.SearchBound.html" title="com.amap.api.services.poisearch中的类">PoiSearch.SearchBound</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">圆形区域</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearchV2.SearchBound.html#BOUND_SHAPE">BOUND_SHAPE</a></span> - 类 中的静态变量com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearchV2.SearchBound.html" title="com.amap.api.services.poisearch中的类">PoiSearchV2.SearchBound</a></dt>
<dd>
<div class="block">圆形区域</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#BUS_COMFORTABLE">BUS_COMFORTABLE</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block">最舒适。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.BusMode.html#BUS_COMFORTABLE">BUS_COMFORTABLE</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.BusMode.html" title="com.amap.api.services.route中的类">RouteSearchV2.BusMode</a></dt>
<dd>
<div class="block">最舒适模式，尽可能乘坐空调车</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#BUS_DEFAULT">BUS_DEFAULT</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block">最快捷模式。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.BusMode.html#BUS_DEFAULT">BUS_DEFAULT</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.BusMode.html" title="com.amap.api.services.route中的类">RouteSearchV2.BusMode</a></dt>
<dd>
<div class="block">推荐模式，综合权重，同高德APP默认</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#BUS_LEASE_CHANGE">BUS_LEASE_CHANGE</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block">最少换乘。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.BusMode.html#BUS_LEASE_CHANGE">BUS_LEASE_CHANGE</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.BusMode.html" title="com.amap.api.services.route中的类">RouteSearchV2.BusMode</a></dt>
<dd>
<div class="block">最少换乘模式，换乘次数少</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#BUS_LEASE_WALK">BUS_LEASE_WALK</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block">最少步行。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.BusMode.html#BUS_LEASE_WALK">BUS_LEASE_WALK</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.BusMode.html" title="com.amap.api.services.route中的类">RouteSearchV2.BusMode</a></dt>
<dd>
<div class="block">最少步行模式，尽可能减少步行距离</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#BUS_NO_SUBWAY">BUS_NO_SUBWAY</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block">不乘地铁。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.BusMode.html#BUS_NO_SUBWAY">BUS_NO_SUBWAY</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.BusMode.html" title="com.amap.api.services.route中的类">RouteSearchV2.BusMode</a></dt>
<dd>
<div class="block">不乘地铁模式，不乘坐地铁路线</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#BUS_SAVE_MONEY">BUS_SAVE_MONEY</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block">最经济模式。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.BusMode.html#BUS_SAVE_MONEY">BUS_SAVE_MONEY</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.BusMode.html" title="com.amap.api.services.route中的类">RouteSearchV2.BusMode</a></dt>
<dd>
<div class="block">最经济模式，票价最低</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.BusMode.html#BUS_SUBWAY">BUS_SUBWAY</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.BusMode.html" title="com.amap.api.services.route中的类">RouteSearchV2.BusMode</a></dt>
<dd>
<div class="block">地铁图模式，起终点都是地铁站
 （地铁图模式下originpoi及destinationpoi为必填项）</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.BusMode.html#BUS_SUBWAY_FIRST">BUS_SUBWAY_FIRST</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.BusMode.html" title="com.amap.api.services.route中的类">RouteSearchV2.BusMode</a></dt>
<dd>
<div class="block">地铁优先模式，步行距离不超过4KM</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.BusMode.html#BUS_WASTE_LESS">BUS_WASTE_LESS</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.BusMode.html" title="com.amap.api.services.route中的类">RouteSearchV2.BusMode</a></dt>
<dd>
<div class="block">时间短模式，方案花费总时间最少</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#BusComfortable">BusComfortable</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span>
<div class="block"><span class="deprecationComment">自V4.0.0以后，请参考 <a href="../com/amap/api/services/route/RouteSearch.html#BUS_COMFORTABLE"><code>RouteSearch.BUS_COMFORTABLE</code></a></span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/share/ShareSearch.html#BusComfortable">BusComfortable</a></span> - 类 中的静态变量com.amap.api.services.share.<a href="../com/amap/api/services/share/ShareSearch.html" title="com.amap.api.services.share中的类">ShareSearch</a></dt>
<dd>
<div class="block">最舒适模式（公交路径规划）</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#BusDefault">BusDefault</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span>
<div class="block"><span class="deprecationComment">自V4.0.0以后，请参考 <a href="../com/amap/api/services/route/RouteSearch.html#BUS_DEFAULT"><code>RouteSearch.BUS_DEFAULT</code></a></span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/share/ShareSearch.html#BusDefault">BusDefault</a></span> - 类 中的静态变量com.amap.api.services.share.<a href="../com/amap/api/services/share/ShareSearch.html" title="com.amap.api.services.share中的类">ShareSearch</a></dt>
<dd>
<div class="block">最快捷模式（公交路径规划）</div>
</dd>
<dt><a href="../com/amap/api/services/poisearch/Business.html" title="com.amap.api.services.poisearch中的类"><span class="typeNameLink">Business</span></a> - <a href="../com/amap/api/services/poisearch/package-summary.html">com.amap.api.services.poisearch</a>中的类</dt>
<dd>
<div class="block">定义了一个POI商业信息类。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearchV2.ShowFields.html#BUSINESS">BUSINESS</a></span> - 类 中的静态变量com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearchV2.ShowFields.html" title="com.amap.api.services.poisearch中的类">PoiSearchV2.ShowFields</a></dt>
<dd>
<div class="block">设置后返回子POI信息business相关信息</div>
</dd>
<dt><a href="../com/amap/api/services/geocoder/BusinessArea.html" title="com.amap.api.services.geocoder中的类"><span class="typeNameLink">BusinessArea</span></a> - <a href="../com/amap/api/services/geocoder/package-summary.html">com.amap.api.services.geocoder</a>中的类</dt>
<dd>
<div class="block">逆地理编码返回的商圈信息。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#BusLeaseChange">BusLeaseChange</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span>
<div class="block"><span class="deprecationComment">自V4.0.0以后，请参考 <a href="../com/amap/api/services/route/RouteSearch.html#BUS_LEASE_CHANGE"><code>RouteSearch.BUS_LEASE_CHANGE</code></a></span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/share/ShareSearch.html#BusLeaseChange">BusLeaseChange</a></span> - 类 中的静态变量com.amap.api.services.share.<a href="../com/amap/api/services/share/ShareSearch.html" title="com.amap.api.services.share中的类">ShareSearch</a></dt>
<dd>
<div class="block">最少换乘模式（公交路径规划）</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#BusLeaseWalk">BusLeaseWalk</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span>
<div class="block"><span class="deprecationComment">自V4.0.0以后，请参考 <a href="../com/amap/api/services/route/RouteSearch.html#BUS_LEASE_WALK"><code>RouteSearch.BUS_LEASE_WALK</code></a></span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/share/ShareSearch.html#BusLeaseWalk">BusLeaseWalk</a></span> - 类 中的静态变量com.amap.api.services.share.<a href="../com/amap/api/services/share/ShareSearch.html" title="com.amap.api.services.share中的类">ShareSearch</a></dt>
<dd>
<div class="block">最少步行模式（公交路径规划）</div>
</dd>
<dt><a href="../com/amap/api/services/busline/BusLineItem.html" title="com.amap.api.services.busline中的类"><span class="typeNameLink">BusLineItem</span></a> - <a href="../com/amap/api/services/busline/package-summary.html">com.amap.api.services.busline</a>中的类</dt>
<dd>
<div class="block">此类定义了公交线路信息。</div>
</dd>
<dt><a href="../com/amap/api/services/busline/BusLineQuery.html" title="com.amap.api.services.busline中的类"><span class="typeNameLink">BusLineQuery</span></a> - <a href="../com/amap/api/services/busline/package-summary.html">com.amap.api.services.busline</a>中的类</dt>
<dd>
<div class="block">此类定义了公交线路搜索的关键字、类别及城市。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/busline/BusLineQuery.html#BusLineQuery-java.lang.String-com.amap.api.services.busline.BusLineQuery.SearchType-java.lang.String-">BusLineQuery(String, BusLineQuery.SearchType, String)</a></span> - 类 的构造器com.amap.api.services.busline.<a href="../com/amap/api/services/busline/BusLineQuery.html" title="com.amap.api.services.busline中的类">BusLineQuery</a></dt>
<dd>
<div class="block">BusLineQuery构造函数。</div>
</dd>
<dt><a href="../com/amap/api/services/busline/BusLineQuery.SearchType.html" title="com.amap.api.services.busline中的枚举"><span class="typeNameLink">BusLineQuery.SearchType</span></a> - <a href="../com/amap/api/services/busline/package-summary.html">com.amap.api.services.busline</a>中的枚举</dt>
<dd>
<div class="block">定义公交线路搜索类型。</div>
</dd>
<dt><a href="../com/amap/api/services/busline/BusLineResult.html" title="com.amap.api.services.busline中的类"><span class="typeNameLink">BusLineResult</span></a> - <a href="../com/amap/api/services/busline/package-summary.html">com.amap.api.services.busline</a>中的类</dt>
<dd>
<div class="block">公交线路搜索结果是分页显示的，从第0页开始，每页默认显示20个BusLineItem。</div>
</dd>
<dt><a href="../com/amap/api/services/busline/BusLineSearch.html" title="com.amap.api.services.busline中的类"><span class="typeNameLink">BusLineSearch</span></a> - <a href="../com/amap/api/services/busline/package-summary.html">com.amap.api.services.busline</a>中的类</dt>
<dd>
<div class="block">本类为公交线路搜索的“入口”类，定义此类，开始搜索。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/busline/BusLineSearch.html#BusLineSearch-Context-com.amap.api.services.busline.BusLineQuery-">BusLineSearch(Context, BusLineQuery)</a></span> - 类 的构造器com.amap.api.services.busline.<a href="../com/amap/api/services/busline/BusLineSearch.html" title="com.amap.api.services.busline中的类">BusLineSearch</a></dt>
<dd>
<div class="block">BusLineSearch构造函数。</div>
</dd>
<dt><a href="../com/amap/api/services/busline/BusLineSearch.OnBusLineSearchListener.html" title="com.amap.api.services.busline中的接口"><span class="typeNameLink">BusLineSearch.OnBusLineSearchListener</span></a> - <a href="../com/amap/api/services/busline/package-summary.html">com.amap.api.services.busline</a>中的接口</dt>
<dd>
<div class="block">此接口定义了公交线路查询异步处理回调接口。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.BusMode.html#BusMode--">BusMode()</a></span> - 类 的构造器com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.BusMode.html" title="com.amap.api.services.route中的类">RouteSearchV2.BusMode</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#BusNoSubway">BusNoSubway</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span>
<div class="block"><span class="deprecationComment">自V4.0.0以后，请参考 <a href="../com/amap/api/services/route/RouteSearch.html#BUS_NO_SUBWAY"><code>RouteSearch.BUS_NO_SUBWAY</code></a></span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/share/ShareSearch.html#BusNoSubway">BusNoSubway</a></span> - 类 中的静态变量com.amap.api.services.share.<a href="../com/amap/api/services/share/ShareSearch.html" title="com.amap.api.services.share中的类">ShareSearch</a></dt>
<dd>
<div class="block">不乘地铁模式（公交路径规划）</div>
</dd>
<dt><a href="../com/amap/api/services/route/BusPath.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">BusPath</span></a> - <a href="../com/amap/api/services/route/package-summary.html">com.amap.api.services.route</a>中的类</dt>
<dd>
<div class="block">定义了公交换乘路径规划的一个方案。</div>
</dd>
<dt><a href="../com/amap/api/services/route/BusPathV2.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">BusPathV2</span></a> - <a href="../com/amap/api/services/route/package-summary.html">com.amap.api.services.route</a>中的类</dt>
<dd>
<div class="block">定义了公交换乘路径规划的一个方案。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.BusRouteQuery.html#BusRouteQuery-com.amap.api.services.route.RouteSearch.FromAndTo-int-java.lang.String-int-">BusRouteQuery(RouteSearch.FromAndTo, int, String, int)</a></span> - 类 的构造器com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.BusRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearch.BusRouteQuery</a></dt>
<dd>
<div class="block">BusRouteQuery构造函数。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.BusRouteQuery.html#BusRouteQuery-com.amap.api.services.route.RouteSearchV2.FromAndTo-int-java.lang.String-int-">BusRouteQuery(RouteSearchV2.FromAndTo, int, String, int)</a></span> - 类 的构造器com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.BusRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearchV2.BusRouteQuery</a></dt>
<dd>
<div class="block">BusRouteQuery构造函数。</div>
</dd>
<dt><a href="../com/amap/api/services/route/BusRouteResult.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">BusRouteResult</span></a> - <a href="../com/amap/api/services/route/package-summary.html">com.amap.api.services.route</a>中的类</dt>
<dd>
<div class="block">定义了公交路径规划的结果集。</div>
</dd>
<dt><a href="../com/amap/api/services/route/BusRouteResultV2.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">BusRouteResultV2</span></a> - <a href="../com/amap/api/services/route/package-summary.html">com.amap.api.services.route</a>中的类</dt>
<dd>
<div class="block">定义了公交路径规划的结果集。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#BusSaveMoney">BusSaveMoney</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span>
<div class="block"><span class="deprecationComment">自V4.0.0以后，请参考 <a href="../com/amap/api/services/route/RouteSearch.html#BUS_SAVE_MONEY"><code>RouteSearch.BUS_SAVE_MONEY</code></a></span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/share/ShareSearch.html#BusSaveMoney">BusSaveMoney</a></span> - 类 中的静态变量com.amap.api.services.share.<a href="../com/amap/api/services/share/ShareSearch.html" title="com.amap.api.services.share中的类">ShareSearch</a></dt>
<dd>
<div class="block">最经济模式（公交路径规划）</div>
</dd>
<dt><a href="../com/amap/api/services/busline/BusStationItem.html" title="com.amap.api.services.busline中的类"><span class="typeNameLink">BusStationItem</span></a> - <a href="../com/amap/api/services/busline/package-summary.html">com.amap.api.services.busline</a>中的类</dt>
<dd>
<div class="block">此类定义了公交站点信息。</div>
</dd>
<dt><a href="../com/amap/api/services/busline/BusStationQuery.html" title="com.amap.api.services.busline中的类"><span class="typeNameLink">BusStationQuery</span></a> - <a href="../com/amap/api/services/busline/package-summary.html">com.amap.api.services.busline</a>中的类</dt>
<dd>
<div class="block">此类定义了公交站点搜索的关键字和城市。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/busline/BusStationQuery.html#BusStationQuery-java.lang.String-java.lang.String-">BusStationQuery(String, String)</a></span> - 类 的构造器com.amap.api.services.busline.<a href="../com/amap/api/services/busline/BusStationQuery.html" title="com.amap.api.services.busline中的类">BusStationQuery</a></dt>
<dd>
<div class="block">BusStationQuery构造函数。</div>
</dd>
<dt><a href="../com/amap/api/services/busline/BusStationResult.html" title="com.amap.api.services.busline中的类"><span class="typeNameLink">BusStationResult</span></a> - <a href="../com/amap/api/services/busline/package-summary.html">com.amap.api.services.busline</a>中的类</dt>
<dd>
<div class="block">公交站点搜索结果是分页显示的，从第0页开始，每页默认显示20个BusStationItem。</div>
</dd>
<dt><a href="../com/amap/api/services/busline/BusStationSearch.html" title="com.amap.api.services.busline中的类"><span class="typeNameLink">BusStationSearch</span></a> - <a href="../com/amap/api/services/busline/package-summary.html">com.amap.api.services.busline</a>中的类</dt>
<dd>
<div class="block">本类为公交站点搜索的“入口”类，定义此类，开始搜索。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/busline/BusStationSearch.html#BusStationSearch-Context-com.amap.api.services.busline.BusStationQuery-">BusStationSearch(Context, BusStationQuery)</a></span> - 类 的构造器com.amap.api.services.busline.<a href="../com/amap/api/services/busline/BusStationSearch.html" title="com.amap.api.services.busline中的类">BusStationSearch</a></dt>
<dd>
<div class="block">BusStationSearch构造函数。</div>
</dd>
<dt><a href="../com/amap/api/services/busline/BusStationSearch.OnBusStationSearchListener.html" title="com.amap.api.services.busline中的接口"><span class="typeNameLink">BusStationSearch.OnBusStationSearchListener</span></a> - <a href="../com/amap/api/services/busline/package-summary.html">com.amap.api.services.busline</a>中的接口</dt>
<dd>
<div class="block">此接口定义了公交站点查询异步处理回调接口。</div>
</dd>
<dt><a href="../com/amap/api/services/route/BusStep.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">BusStep</span></a> - <a href="../com/amap/api/services/route/package-summary.html">com.amap.api.services.route</a>中的类</dt>
<dd>
<div class="block">定义了公交路径规划的一个路段。</div>
</dd>
<dt><a href="../com/amap/api/services/route/BusStepV2.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">BusStepV2</span></a> - <a href="../com/amap/api/services/route/package-summary.html">com.amap.api.services.route</a>中的类</dt>
<dd>
<div class="block">定义了公交路径规划的一个路段。</div>
</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">K</a>&nbsp;<a href="index-11.html">L</a>&nbsp;<a href="index-12.html">M</a>&nbsp;<a href="index-13.html">N</a>&nbsp;<a href="index-14.html">O</a>&nbsp;<a href="index-15.html">P</a>&nbsp;<a href="index-16.html">Q</a>&nbsp;<a href="index-17.html">R</a>&nbsp;<a href="index-18.html">S</a>&nbsp;<a href="index-19.html">T</a>&nbsp;<a href="index-20.html">U</a>&nbsp;<a href="index-21.html">V</a>&nbsp;<a href="index-22.html">W</a>&nbsp;</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-1.html">上一个字母</a></li>
<li><a href="index-3.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-2.html" target="_top">框架</a></li>
<li><a href="index-2.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
