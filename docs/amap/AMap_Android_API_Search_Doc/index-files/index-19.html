<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>T - 索引</title>
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="T - \u7D22\u5F15";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-18.html">上一个字母</a></li>
<li><a href="index-20.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-19.html" target="_top">框架</a></li>
<li><a href="index-19.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="contentContainer"><a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">K</a>&nbsp;<a href="index-11.html">L</a>&nbsp;<a href="index-12.html">M</a>&nbsp;<a href="index-13.html">N</a>&nbsp;<a href="index-14.html">O</a>&nbsp;<a href="index-15.html">P</a>&nbsp;<a href="index-16.html">Q</a>&nbsp;<a href="index-17.html">R</a>&nbsp;<a href="index-18.html">S</a>&nbsp;<a href="index-19.html">T</a>&nbsp;<a href="index-20.html">U</a>&nbsp;<a href="index-21.html">V</a>&nbsp;<a href="index-22.html">W</a>&nbsp;<a name="I:T">
<!--   -->
</a>
<h2 class="title">T</h2>
<dl>
<dt><a href="../com/amap/api/services/route/TaxiItem.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">TaxiItem</span></a> - <a href="../com/amap/api/services/route/package-summary.html">com.amap.api.services.route</a>中的类</dt>
<dd>
<div class="block">此类定义了打车路段信息</div>
</dd>
<dt><a href="../com/amap/api/services/route/TaxiItemV2.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">TaxiItemV2</span></a> - <a href="../com/amap/api/services/route/package-summary.html">com.amap.api.services.route</a>中的类</dt>
<dd>
<div class="block">此类定义了打车路段信息</div>
</dd>
<dt><a href="../com/amap/api/services/route/TimeInfo.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">TimeInfo</span></a> - <a href="../com/amap/api/services/route/package-summary.html">com.amap.api.services.route</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><a href="../com/amap/api/services/route/TimeInfosElement.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">TimeInfosElement</span></a> - <a href="../com/amap/api/services/route/package-summary.html">com.amap.api.services.route</a>中的类</dt>
<dd>
<div class="block">对应的路线</div>
</dd>
<dt><a href="../com/amap/api/services/help/Tip.html" title="com.amap.api.services.help中的类"><span class="typeNameLink">Tip</span></a> - <a href="../com/amap/api/services/help/package-summary.html">com.amap.api.services.help</a>中的类</dt>
<dd>
<div class="block">定义Tip的类。</div>
</dd>
<dt><a href="../com/amap/api/services/route/TMC.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">TMC</span></a> - <a href="../com/amap/api/services/route/package-summary.html">com.amap.api.services.route</a>中的类</dt>
<dd>
<div class="block">交通信息类</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.ShowFields.html#TMCS">TMCS</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.ShowFields.html" title="com.amap.api.services.route中的类">RouteSearchV2.ShowFields</a></dt>
<dd>
<div class="block">设置后可返回分段路况详情</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/busline/BusLineItem.html#toString--">toString()</a></span> - 类 中的方法com.amap.api.services.busline.<a href="../com/amap/api/services/busline/BusLineItem.html" title="com.amap.api.services.busline中的类">BusLineItem</a></dt>
<dd>
<div class="block">将公交线路信息转换为字符串输出。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/busline/BusStationItem.html#toString--">toString()</a></span> - 类 中的方法com.amap.api.services.busline.<a href="../com/amap/api/services/busline/BusStationItem.html" title="com.amap.api.services.busline中的类">BusStationItem</a></dt>
<dd>
<div class="block">将公交站点信息转换为字符串输出。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/cloud/CloudSearch.Sortingrules.html#toString--">toString()</a></span> - 类 中的方法com.amap.api.services.cloud.<a href="../com/amap/api/services/cloud/CloudSearch.Sortingrules.html" title="com.amap.api.services.cloud中的类">CloudSearch.Sortingrules</a></dt>
<dd>
<div class="block">将查询的排序规则转换为字符串输出</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/LatLonPoint.html#toString--">toString()</a></span> - 类 中的方法com.amap.api.services.core.<a href="../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a></dt>
<dd>
<div class="block">将经纬度转换为字符串输出。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/LatLonSharePoint.html#toString--">toString()</a></span> - 类 中的方法com.amap.api.services.core.<a href="../com/amap/api/services/core/LatLonSharePoint.html" title="com.amap.api.services.core中的类">LatLonSharePoint</a></dt>
<dd>
<div class="block">将分享的位置信息转换为字符串输出</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/PoiItem.html#toString--">toString()</a></span> - 类 中的方法com.amap.api.services.core.<a href="../com/amap/api/services/core/PoiItem.html" title="com.amap.api.services.core中的类">PoiItem</a></dt>
<dd>
<div class="block">继承自Object，返回POI 的名称（name）。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/district/DistrictItem.html#toString--">toString()</a></span> - 类 中的方法com.amap.api.services.district.<a href="../com/amap/api/services/district/DistrictItem.html" title="com.amap.api.services.district中的类">DistrictItem</a></dt>
<dd>
<div class="block">将行政区信息转换成字符串输出
 返回行政区的城市编码、区域编码、行政区名称、中心点、行政区划级别</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/district/DistrictResult.html#toString--">toString()</a></span> - 类 中的方法com.amap.api.services.district.<a href="../com/amap/api/services/district/DistrictResult.html" title="com.amap.api.services.district中的类">DistrictResult</a></dt>
<dd>
<div class="block">将行政区划查询结果转换为字符串返回。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/help/Tip.html#toString--">toString()</a></span> - 类 中的方法com.amap.api.services.help.<a href="../com/amap/api/services/help/Tip.html" title="com.amap.api.services.help中的类">Tip</a></dt>
<dd>
<div class="block">将提示转换为字符串输出</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#TRUCK_AVOID_CONGESTION">TRUCK_AVOID_CONGESTION</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block">结果考虑路况，尽量躲避拥堵而规划路径，与高德地图的“躲避拥堵”策略一致</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#TRUCK_AVOID_CONGESTION__SAVE_MONEY">TRUCK_AVOID_CONGESTION__SAVE_MONEY</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block">结果会尽量的躲避拥堵，并且规划收费较低甚至免费的路径结果，与高德地图的“躲避拥堵&避免收费”策略一致</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#TRUCK_AVOID_CONGESTION__SAVE_MONEY_NO_HIGHWAY">TRUCK_AVOID_CONGESTION__SAVE_MONEY_NO_HIGHWAY</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block">结果尽量躲避拥堵，规划收费较低甚至免费的路径结果，并且尽量不走高速路，与高德地图的“避免拥堵&避免收费&不走高速”策略一致</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#TRUCK_AVOID_CONGESTION_CHOICE_HIGHWAY">TRUCK_AVOID_CONGESTION_CHOICE_HIGHWAY</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block">结果会优先考虑高速路，并且会考虑路况躲避拥堵，与高德地图的“躲避拥堵&高速优先”策略一致</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#TRUCK_AVOID_CONGESTION_NO_HIGHWAY">TRUCK_AVOID_CONGESTION_NO_HIGHWAY</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block">结果考虑路况，尽量躲避拥堵而规划路径，并且不走高速，与高德地图的“躲避拥堵&不走高速”策略一致</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#TRUCK_CHOICE_HIGHWAY">TRUCK_CHOICE_HIGHWAY</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block">结果会优先选择高速路，与高德地图的“高速优先”策略一致</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#TRUCK_NO_HIGHWAY">TRUCK_NO_HIGHWAY</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block">结果不走高速，与高德地图“不走高速”策略一致</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#TRUCK_SAVE_MONEY">TRUCK_SAVE_MONEY</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block">结果尽可能规划收费较低甚至免费的路径，与高德地图“避免收费”策略一致</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#TRUCK_SAVE_MONEY_NO_HIGHWAY">TRUCK_SAVE_MONEY_NO_HIGHWAY</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block">结果尽量不走高速，并且尽量规划收费较低甚至免费的路径结果，与高德地图的“避免收费&不走高速”策略一致</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#TRUCK_SIZE_HEAVY">TRUCK_SIZE_HEAVY</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block">重型车</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#TRUCK_SIZE_LIGHT">TRUCK_SIZE_LIGHT</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block">轻型车</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#TRUCK_SIZE_MEDIUM">TRUCK_SIZE_MEDIUM</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block">中型车</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#TRUCK_SIZE_MINI">TRUCK_SIZE_MINI</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block">微型车</div>
</dd>
<dt><a href="../com/amap/api/services/route/TruckPath.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">TruckPath</span></a> - <a href="../com/amap/api/services/route/package-summary.html">com.amap.api.services.route</a>中的类</dt>
<dd>
<div class="block">定义了货车路径规划的一个方案。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.TruckRouteQuery.html#TruckRouteQuery-com.amap.api.services.route.RouteSearch.FromAndTo-int-java.util.List-int-">TruckRouteQuery(RouteSearch.FromAndTo, int, List&lt;LatLonPoint&gt;, int)</a></span> - 类 的构造器com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.TruckRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearch.TruckRouteQuery</a></dt>
<dd>
<div class="block">货车导航请求参数构造</div>
</dd>
<dt><a href="../com/amap/api/services/route/TruckRouteRestult.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">TruckRouteRestult</span></a> - <a href="../com/amap/api/services/route/package-summary.html">com.amap.api.services.route</a>中的类</dt>
<dd>
<div class="block">定义了货车路径规划的结果集。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/TruckRouteRestult.html#TruckRouteRestult--">TruckRouteRestult()</a></span> - 类 的构造器com.amap.api.services.route.<a href="../com/amap/api/services/route/TruckRouteRestult.html" title="com.amap.api.services.route中的类">TruckRouteRestult</a></dt>
<dd>
<div class="block">定义了货车路径规划的构造函数。</div>
</dd>
<dt><a href="../com/amap/api/services/route/TruckStep.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">TruckStep</span></a> - <a href="../com/amap/api/services/route/package-summary.html">com.amap.api.services.route</a>中的类</dt>
<dd>
<div class="block">定义了货车路径规划的一个路段。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/DistanceSearch.html#TYPE_DISTANCE">TYPE_DISTANCE</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/DistanceSearch.html" title="com.amap.api.services.route中的类">DistanceSearch</a></dt>
<dd>
<div class="block">距离测量方法：直线距离</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/DistanceSearch.html#TYPE_DRIVING_DISTANCE">TYPE_DRIVING_DISTANCE</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/DistanceSearch.html" title="com.amap.api.services.route中的类">DistanceSearch</a></dt>
<dd>
<div class="block">距离测量方法：驾车导航距离（仅支持国内坐标）<br>
 会考虑路况，在不同时间请求返回结果可能不同。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/DistanceSearch.html#TYPE_WALK_DISTANCE">TYPE_WALK_DISTANCE</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/DistanceSearch.html" title="com.amap.api.services.route中的类">DistanceSearch</a></dt>
<dd>
<div class="block">距离测量方法：步行导航距离（仅支持国内坐标）<br></div>
</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">K</a>&nbsp;<a href="index-11.html">L</a>&nbsp;<a href="index-12.html">M</a>&nbsp;<a href="index-13.html">N</a>&nbsp;<a href="index-14.html">O</a>&nbsp;<a href="index-15.html">P</a>&nbsp;<a href="index-16.html">Q</a>&nbsp;<a href="index-17.html">R</a>&nbsp;<a href="index-18.html">S</a>&nbsp;<a href="index-19.html">T</a>&nbsp;<a href="index-20.html">U</a>&nbsp;<a href="index-21.html">V</a>&nbsp;<a href="index-22.html">W</a>&nbsp;</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-18.html">上一个字母</a></li>
<li><a href="index-20.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-19.html" target="_top">框架</a></li>
<li><a href="index-19.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
