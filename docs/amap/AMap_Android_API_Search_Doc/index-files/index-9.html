<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>I - 索引</title>
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="I - \u7D22\u5F15";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-8.html">上一个字母</a></li>
<li><a href="index-10.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-9.html" target="_top">框架</a></li>
<li><a href="index-9.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="contentContainer"><a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">K</a>&nbsp;<a href="index-11.html">L</a>&nbsp;<a href="index-12.html">M</a>&nbsp;<a href="index-13.html">N</a>&nbsp;<a href="index-14.html">O</a>&nbsp;<a href="index-15.html">P</a>&nbsp;<a href="index-16.html">Q</a>&nbsp;<a href="index-17.html">R</a>&nbsp;<a href="index-18.html">S</a>&nbsp;<a href="index-19.html">T</a>&nbsp;<a href="index-20.html">U</a>&nbsp;<a href="index-21.html">V</a>&nbsp;<a href="index-22.html">W</a>&nbsp;<a name="I:I">
<!--   -->
</a>
<h2 class="title">I</h2>
<dl>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearchV2.ShowFields.html#INDOOR">INDOOR</a></span> - 类 中的静态变量com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearchV2.ShowFields.html" title="com.amap.api.services.poisearch中的类">PoiSearchV2.ShowFields</a></dt>
<dd>
<div class="block">设置后返回室内相关信息</div>
</dd>
<dt><a href="../com/amap/api/services/poisearch/IndoorData.html" title="com.amap.api.services.poisearch中的类"><span class="typeNameLink">IndoorData</span></a> - <a href="../com/amap/api/services/poisearch/package-summary.html">com.amap.api.services.poisearch</a>中的类</dt>
<dd>
<div class="block">室内地图相关数据</div>
</dd>
<dt><a href="../com/amap/api/services/poisearch/IndoorDataV2.html" title="com.amap.api.services.poisearch中的类"><span class="typeNameLink">IndoorDataV2</span></a> - <a href="../com/amap/api/services/poisearch/package-summary.html">com.amap.api.services.poisearch</a>中的类</dt>
<dd>
<div class="block">室内地图相关数据</div>
</dd>
<dt><a href="../com/amap/api/services/help/Inputtips.html" title="com.amap.api.services.help中的类"><span class="typeNameLink">Inputtips</span></a> - <a href="../com/amap/api/services/help/package-summary.html">com.amap.api.services.help</a>中的类</dt>
<dd>
<div class="block">输入提示类，前提需要联网。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/help/Inputtips.html#Inputtips-Context-com.amap.api.services.help.Inputtips.InputtipsListener-">Inputtips(Context, Inputtips.InputtipsListener)</a></span> - 类 的构造器com.amap.api.services.help.<a href="../com/amap/api/services/help/Inputtips.html" title="com.amap.api.services.help中的类">Inputtips</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/help/Inputtips.html#Inputtips-Context-com.amap.api.services.help.InputtipsQuery-">Inputtips(Context, InputtipsQuery)</a></span> - 类 的构造器com.amap.api.services.help.<a href="../com/amap/api/services/help/Inputtips.html" title="com.amap.api.services.help中的类">Inputtips</a></dt>
<dd>
<div class="block">根据给定的参数构造一个Inputtips的新对象。</div>
</dd>
<dt><a href="../com/amap/api/services/help/Inputtips.InputtipsListener.html" title="com.amap.api.services.help中的接口"><span class="typeNameLink">Inputtips.InputtipsListener</span></a> - <a href="../com/amap/api/services/help/package-summary.html">com.amap.api.services.help</a>中的接口</dt>
<dd>
<div class="block">输入提示回调的监听接口。</div>
</dd>
<dt><a href="../com/amap/api/services/help/InputtipsQuery.html" title="com.amap.api.services.help中的类"><span class="typeNameLink">InputtipsQuery</span></a> - <a href="../com/amap/api/services/help/package-summary.html">com.amap.api.services.help</a>中的类</dt>
<dd>
<div class="block">此类定义了输入提示搜索的参数。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/help/InputtipsQuery.html#InputtipsQuery-java.lang.String-java.lang.String-">InputtipsQuery(String, String)</a></span> - 类 的构造器com.amap.api.services.help.<a href="../com/amap/api/services/help/InputtipsQuery.html" title="com.amap.api.services.help中的类">InputtipsQuery</a></dt>
<dd>
<div class="block">根据给定的参数来构造一个InputtipsQuery的对象</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearch.Query.html#isDistanceSort--">isDistanceSort()</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearch.Query.html" title="com.amap.api.services.poisearch中的类">PoiSearch.Query</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">返回是否按照距离排序。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearch.SearchBound.html#isDistanceSort--">isDistanceSort()</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearch.SearchBound.html" title="com.amap.api.services.poisearch中的类">PoiSearch.SearchBound</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">返回是否按照距离排序。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearchV2.Query.html#isDistanceSort--">isDistanceSort()</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearchV2.Query.html" title="com.amap.api.services.poisearch中的类">PoiSearchV2.Query</a></dt>
<dd>
<div class="block">返回是否按照距离排序。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearchV2.SearchBound.html#isDistanceSort--">isDistanceSort()</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearchV2.SearchBound.html" title="com.amap.api.services.poisearch中的类">PoiSearchV2.SearchBound</a></dt>
<dd>
<div class="block">返回是否按照距离排序。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RailwayStationItem.html#isEnd--">isEnd()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RailwayStationItem.html" title="com.amap.api.services.route中的类">RailwayStationItem</a></dt>
<dd>
<div class="block">返回是否为终点站。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.WalkRouteQuery.html#isIndoor--">isIndoor()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.WalkRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearchV2.WalkRouteQuery</a></dt>
<dd>
<div class="block">是否需要室内算路</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/PoiItem.html#isIndoorMap--">isIndoorMap()</a></span> - 类 中的方法com.amap.api.services.core.<a href="../com/amap/api/services/core/PoiItem.html" title="com.amap.api.services.core中的类">PoiItem</a></dt>
<dd>
<div class="block">返回是否支持室内地图 。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/IndoorDataV2.html#isIndoorMap--">isIndoorMap()</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/IndoorDataV2.html" title="com.amap.api.services.poisearch中的类">IndoorDataV2</a></dt>
<dd>
<div class="block">返回是否支持室内地图 。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/BusPath.html#isNightBus--">isNightBus()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/BusPath.html" title="com.amap.api.services.route中的类">BusPath</a></dt>
<dd>
<div class="block">返回是否包含夜班车。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/BusPathV2.html#isNightBus--">isNightBus()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/BusPathV2.html" title="com.amap.api.services.route中的类">BusPathV2</a></dt>
<dd>
<div class="block">返回是否包含夜班车。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearch.Query.html#isRequireSubPois--">isRequireSubPois()</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearch.Query.html" title="com.amap.api.services.poisearch中的类">PoiSearch.Query</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">返回是否按照父子关系展示POI</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/district/DistrictSearchQuery.html#isShowBoundary--">isShowBoundary()</a></span> - 类 中的方法com.amap.api.services.district.<a href="../com/amap/api/services/district/DistrictSearchQuery.html" title="com.amap.api.services.district中的类">DistrictSearchQuery</a></dt>
<dd>
<div class="block">返回行政区域查询是否返回边界值。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/district/DistrictSearchQuery.html#isShowBusinessArea--">isShowBusinessArea()</a></span> - 类 中的方法com.amap.api.services.district.<a href="../com/amap/api/services/district/DistrictSearchQuery.html" title="com.amap.api.services.district中的类">DistrictSearchQuery</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span>
<div class="block"><span class="deprecationComment">自 5.1.0 废弃，用率极低，而且会影响精准度，取消商圈（biz_area）级别数据，用街道（street）顶替商圈位置</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/district/DistrictSearchQuery.html#isShowChild--">isShowChild()</a></span> - 类 中的方法com.amap.api.services.district.<a href="../com/amap/api/services/district/DistrictSearchQuery.html" title="com.amap.api.services.district中的类">DistrictSearchQuery</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span>
<div class="block"><span class="deprecationComment">自 7.1.0 废弃，返回下级行政区划参考 <a href="../com/amap/api/services/district/DistrictSearchQuery.html#getSubDistrict--"><code>DistrictSearchQuery.getSubDistrict()</code></a></span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearch.Query.html#isSpecial--">isSpecial()</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearch.Query.html" title="com.amap.api.services.poisearch中的类">PoiSearch.Query</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">是否对结果进行人工干预，如火车站，原因为POI较为特殊，结果存在人工干预，干预结果优先，所以距离优先的排序未生效
 仅周边搜索支持</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearchV2.Query.html#isSpecial--">isSpecial()</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearchV2.Query.html" title="com.amap.api.services.poisearch中的类">PoiSearchV2.Query</a></dt>
<dd>
<div class="block">是否对结果进行人工干预，如火车站，原因为POI较为特殊，结果存在人工干预，干预结果优先，所以距离优先的排序未生效
 仅周边搜索支持</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RailwayStationItem.html#isStart--">isStart()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RailwayStationItem.html" title="com.amap.api.services.route中的类">RailwayStationItem</a></dt>
<dd>
<div class="block">返回是否为始发站。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.DriveRouteQuery.html#isUseFerry--">isUseFerry()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.DriveRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearch.DriveRouteQuery</a></dt>
<dd>
<div class="block">是否使用轮渡，默认使用</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.DriveRouteQuery.html#isUseFerry--">isUseFerry()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.DriveRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearchV2.DriveRouteQuery</a></dt>
<dd>
<div class="block">是否使用轮渡，默认使用</div>
</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">K</a>&nbsp;<a href="index-11.html">L</a>&nbsp;<a href="index-12.html">M</a>&nbsp;<a href="index-13.html">N</a>&nbsp;<a href="index-14.html">O</a>&nbsp;<a href="index-15.html">P</a>&nbsp;<a href="index-16.html">Q</a>&nbsp;<a href="index-17.html">R</a>&nbsp;<a href="index-18.html">S</a>&nbsp;<a href="index-19.html">T</a>&nbsp;<a href="index-20.html">U</a>&nbsp;<a href="index-21.html">V</a>&nbsp;<a href="index-22.html">W</a>&nbsp;</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-8.html">上一个字母</a></li>
<li><a href="index-10.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-9.html" target="_top">框架</a></li>
<li><a href="index-9.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
