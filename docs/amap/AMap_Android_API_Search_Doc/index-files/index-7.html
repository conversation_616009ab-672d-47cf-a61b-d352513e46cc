<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>G - 索引</title>
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="G - \u7D22\u5F15";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-6.html">上一个字母</a></li>
<li><a href="index-8.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-7.html" target="_top">框架</a></li>
<li><a href="index-7.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="contentContainer"><a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">K</a>&nbsp;<a href="index-11.html">L</a>&nbsp;<a href="index-12.html">M</a>&nbsp;<a href="index-13.html">N</a>&nbsp;<a href="index-14.html">O</a>&nbsp;<a href="index-15.html">P</a>&nbsp;<a href="index-16.html">Q</a>&nbsp;<a href="index-17.html">R</a>&nbsp;<a href="index-18.html">S</a>&nbsp;<a href="index-19.html">T</a>&nbsp;<a href="index-20.html">U</a>&nbsp;<a href="index-21.html">V</a>&nbsp;<a href="index-22.html">W</a>&nbsp;<a name="I:G">
<!--   -->
</a>
<h2 class="title">G</h2>
<dl>
<dt><a href="../com/amap/api/services/geocoder/GeocodeAddress.html" title="com.amap.api.services.geocoder中的类"><span class="typeNameLink">GeocodeAddress</span></a> - <a href="../com/amap/api/services/geocoder/package-summary.html">com.amap.api.services.geocoder</a>中的类</dt>
<dd>
<div class="block">地理编码返回的结果</div>
</dd>
<dt><a href="../com/amap/api/services/geocoder/GeocodeQuery.html" title="com.amap.api.services.geocoder中的类"><span class="typeNameLink">GeocodeQuery</span></a> - <a href="../com/amap/api/services/geocoder/package-summary.html">com.amap.api.services.geocoder</a>中的类</dt>
<dd>
<div class="block">此类定义了地理编码查询的关键字和查询城市。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/GeocodeQuery.html#GeocodeQuery-java.lang.String-java.lang.String-">GeocodeQuery(String, String)</a></span> - 类 的构造器com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/GeocodeQuery.html" title="com.amap.api.services.geocoder中的类">GeocodeQuery</a></dt>
<dd>
<div class="block">GeocodeQuery构造函数。</div>
</dd>
<dt><a href="../com/amap/api/services/geocoder/GeocodeResult.html" title="com.amap.api.services.geocoder中的类"><span class="typeNameLink">GeocodeResult</span></a> - <a href="../com/amap/api/services/geocoder/package-summary.html">com.amap.api.services.geocoder</a>中的类</dt>
<dd>
<div class="block">地理编码的搜索结果。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/GeocodeResult.html#GeocodeResult-com.amap.api.services.geocoder.GeocodeQuery-java.util.List-">GeocodeResult(GeocodeQuery, List&lt;GeocodeAddress&gt;)</a></span> - 类 的构造器com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/GeocodeResult.html" title="com.amap.api.services.geocoder中的类">GeocodeResult</a></dt>
<dd>
<div class="block">依据给定的参数构造地理编码的搜索结果对象。</div>
</dd>
<dt><a href="../com/amap/api/services/geocoder/GeocodeSearch.html" title="com.amap.api.services.geocoder中的类"><span class="typeNameLink">GeocodeSearch</span></a> - <a href="../com/amap/api/services/geocoder/package-summary.html">com.amap.api.services.geocoder</a>中的类</dt>
<dd>
<div class="block">地理编码与逆地理编码类。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/GeocodeSearch.html#GeocodeSearch-Context-">GeocodeSearch(Context)</a></span> - 类 的构造器com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/GeocodeSearch.html" title="com.amap.api.services.geocoder中的类">GeocodeSearch</a></dt>
<dd>
<div class="block">根据给定的参数来构造一个GeocodeSearch 新对象。</div>
</dd>
<dt><a href="../com/amap/api/services/geocoder/GeocodeSearch.OnGeocodeSearchListener.html" title="com.amap.api.services.geocoder中的接口"><span class="typeNameLink">GeocodeSearch.OnGeocodeSearchListener</span></a> - <a href="../com/amap/api/services/geocoder/package-summary.html">com.amap.api.services.geocoder</a>中的接口</dt>
<dd>
<div class="block">此接口定义了逆地理编码异步处理回调接口。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/DriveStep.html#getAction--">getAction()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/DriveStep.html" title="com.amap.api.services.route中的类">DriveStep</a></dt>
<dd>
<div class="block">返回驾车路段的导航主要操作。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/Navi.html#getAction--">getAction()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/Navi.html" title="com.amap.api.services.route中的类">Navi</a></dt>
<dd>
<div class="block">返回驾车路段的导航主要操作</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RideStep.html#getAction--">getAction()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RideStep.html" title="com.amap.api.services.route中的类">RideStep</a></dt>
<dd>
<div class="block">返回骑行路段的导航主要操作。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/TruckStep.html#getAction--">getAction()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/TruckStep.html" title="com.amap.api.services.route中的类">TruckStep</a></dt>
<dd>
<div class="block">导航主要动作<br>
  例如：左转 <br>
 目前已知会有如下动作：<br>
 左转、右转、
 向左前方行驶、向右前方行驶、向左后方行驶、向右后方行驶、
 直行、靠左、靠右、
 进入环岛、离开环岛、
 减速行驶、插入直行</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/WalkStep.html#getAction--">getAction()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/WalkStep.html" title="com.amap.api.services.route中的类">WalkStep</a></dt>
<dd>
<div class="block">返回步行路段的导航主要操作。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.BusRouteQuery.html#getAd1--">getAd1()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.BusRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearchV2.BusRouteQuery</a></dt>
<dd>
<div class="block">起点所在行政区域编码

 仅支持adcode，参考行政区域编码表</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.BusRouteQuery.html#getAd2--">getAd2()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.BusRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearchV2.BusRouteQuery</a></dt>
<dd>
<div class="block">终点所在行政区域编码

 仅支持adcode，参考行政区域编码表</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/busline/BusStationItem.html#getAdCode--">getAdCode()</a></span> - 类 中的方法com.amap.api.services.busline.<a href="../com/amap/api/services/busline/BusStationItem.html" title="com.amap.api.services.busline中的类">BusStationItem</a></dt>
<dd>
<div class="block">返回车站区域编码。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/PoiItem.html#getAdCode--">getAdCode()</a></span> - 类 中的方法com.amap.api.services.core.<a href="../com/amap/api/services/core/PoiItem.html" title="com.amap.api.services.core中的类">PoiItem</a></dt>
<dd>
<div class="block">返回POI 的行政区划代码。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/SuggestionCity.html#getAdCode--">getAdCode()</a></span> - 类 中的方法com.amap.api.services.core.<a href="../com/amap/api/services/core/SuggestionCity.html" title="com.amap.api.services.core中的类">SuggestionCity</a></dt>
<dd>
<div class="block">返回区域代码。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/district/DistrictItem.html#getAdcode--">getAdcode()</a></span> - 类 中的方法com.amap.api.services.district.<a href="../com/amap/api/services/district/DistrictItem.html" title="com.amap.api.services.district中的类">DistrictItem</a></dt>
<dd>
<div class="block">返回区域编码。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/AoiItem.html#getAdCode--">getAdCode()</a></span> - 类 中的方法com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/AoiItem.html" title="com.amap.api.services.geocoder中的类">AoiItem</a></dt>
<dd>
<div class="block">返回AOI的行政区划代码。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/GeocodeAddress.html#getAdcode--">getAdcode()</a></span> - 类 中的方法com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/GeocodeAddress.html" title="com.amap.api.services.geocoder中的类">GeocodeAddress</a></dt>
<dd>
<div class="block">地理编码返回的区域编码。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/RegeocodeAddress.html#getAdCode--">getAdCode()</a></span> - 类 中的方法com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/RegeocodeAddress.html" title="com.amap.api.services.geocoder中的类">RegeocodeAddress</a></dt>
<dd>
<div class="block">返回逆地理编码结果所在区（县）的编码。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/help/Tip.html#getAdcode--">getAdcode()</a></span> - 类 中的方法com.amap.api.services.help.<a href="../com/amap/api/services/help/Tip.html" title="com.amap.api.services.help中的类">Tip</a></dt>
<dd>
<div class="block">返回提示区域编码。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/DrivePlanStep.html#getAdCode--">getAdCode()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/DrivePlanStep.html" title="com.amap.api.services.route中的类">DrivePlanStep</a></dt>
<dd>
<div class="block">返回驾车路段的区域代码。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RailwayStationItem.html#getAdcode--">getAdcode()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RailwayStationItem.html" title="com.amap.api.services.route中的类">RailwayStationItem</a></dt>
<dd>
<div class="block">返回站点所在城市adcode。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/weather/LocalWeatherForecast.html#getAdCode--">getAdCode()</a></span> - 类 中的方法com.amap.api.services.weather.<a href="../com/amap/api/services/weather/LocalWeatherForecast.html" title="com.amap.api.services.weather中的类">LocalWeatherForecast</a></dt>
<dd>
<div class="block">返回行政区划代码。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/weather/LocalWeatherLive.html#getAdCode--">getAdCode()</a></span> - 类 中的方法com.amap.api.services.weather.<a href="../com/amap/api/services/weather/LocalWeatherLive.html" title="com.amap.api.services.weather中的类">LocalWeatherLive</a></dt>
<dd>
<div class="block">返回行政区划代码。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/help/Tip.html#getAddress--">getAddress()</a></span> - 类 中的方法com.amap.api.services.help.<a href="../com/amap/api/services/help/Tip.html" title="com.amap.api.services.help中的类">Tip</a></dt>
<dd>
<div class="block">返回详细地址。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/PoiItem.html#getAdName--">getAdName()</a></span> - 类 中的方法com.amap.api.services.core.<a href="../com/amap/api/services/core/PoiItem.html" title="com.amap.api.services.core中的类">PoiItem</a></dt>
<dd>
<div class="block">返回POI 的行政区划名称。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/Business.html#getAlias--">getAlias()</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/Business.html" title="com.amap.api.services.poisearch中的类">Business</a></dt>
<dd>
<div class="block">返回POI的别名</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.BusRouteQuery.html#getAlternativeRoute--">getAlternativeRoute()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.BusRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearchV2.BusRouteQuery</a></dt>
<dd>
<div class="block">返回方案条数

 可传入1-10的阿拉伯数字，代表返回的不同条数。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.WalkRouteQuery.html#getAlternativeRoute--">getAlternativeRoute()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.WalkRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearchV2.WalkRouteQuery</a></dt>
<dd>
<div class="block">返回路线条数</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteRailwayItem.html#getAlters--">getAlters()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteRailwayItem.html" title="com.amap.api.services.route中的类">RouteRailwayItem</a></dt>
<dd>
<div class="block">返回备选方案 。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/district/DistrictResult.html#getAMapException--">getAMapException()</a></span> - 类 中的方法com.amap.api.services.district.<a href="../com/amap/api/services/district/DistrictResult.html" title="com.amap.api.services.district中的类">DistrictResult</a></dt>
<dd>
<div class="block">返回服务异常对象。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/AoiItem.html#getAoiArea--">getAoiArea()</a></span> - 类 中的方法com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/AoiItem.html" title="com.amap.api.services.geocoder中的类">AoiItem</a></dt>
<dd>
<div class="block">返回AOI覆盖区域面积，单位平方米。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/AoiItem.html#getAoiCenterPoint--">getAoiCenterPoint()</a></span> - 类 中的方法com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/AoiItem.html" title="com.amap.api.services.geocoder中的类">AoiItem</a></dt>
<dd>
<div class="block">返回AOI的中心点坐标。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/AoiItem.html#getAoiId--">getAoiId()</a></span> - 类 中的方法com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/AoiItem.html" title="com.amap.api.services.geocoder中的类">AoiItem</a></dt>
<dd>
<div class="block">返回AOI的id，即其唯一标识。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/AoiItem.html#getAoiName--">getAoiName()</a></span> - 类 中的方法com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/AoiItem.html" title="com.amap.api.services.geocoder中的类">AoiItem</a></dt>
<dd>
<div class="block">返回AOI的名称。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/RegeocodeAddress.html#getAois--">getAois()</a></span> - 类 中的方法com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/RegeocodeAddress.html" title="com.amap.api.services.geocoder中的类">RegeocodeAddress</a></dt>
<dd>
<div class="block">返回AOI（面状数据）的数据，如POI名称、区域编码、中心点坐标、POI类型等。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteBusLineItem.html#getArrivalBusStation--">getArrivalBusStation()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteBusLineItem.html" title="com.amap.api.services.route中的类">RouteBusLineItem</a></dt>
<dd>
<div class="block">返回此公交换乘路段的到达站。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteRailwayItem.html#getArrivalstop--">getArrivalstop()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteRailwayItem.html" title="com.amap.api.services.route中的类">RouteRailwayItem</a></dt>
<dd>
<div class="block">返回火车到站信息。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/DriveStep.html#getAssistantAction--">getAssistantAction()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/DriveStep.html" title="com.amap.api.services.route中的类">DriveStep</a></dt>
<dd>
<div class="block">返回驾车路段的导航辅助操作。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/Navi.html#getAssistantAction--">getAssistantAction()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/Navi.html" title="com.amap.api.services.route中的类">Navi</a></dt>
<dd>
<div class="block">获取驾车路段的导航辅助操作</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RideStep.html#getAssistantAction--">getAssistantAction()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RideStep.html" title="com.amap.api.services.route中的类">RideStep</a></dt>
<dd>
<div class="block">返回骑车路段的导航辅助操作。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/TruckStep.html#getAssistantAction--">getAssistantAction()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/TruckStep.html" title="com.amap.api.services.route中的类">TruckStep</a></dt>
<dd>
<div class="block">列表参见 <a href="../com/amap/api/services/route/TruckStep.html#getAction--"><code>TruckStep.getAction()</code></a></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/WalkStep.html#getAssistantAction--">getAssistantAction()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/WalkStep.html" title="com.amap.api.services.route中的类">WalkStep</a></dt>
<dd>
<div class="block">返回步行路段的导航辅助操作。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.DriveRouteQuery.html#getAvoidpolygons--">getAvoidpolygons()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.DriveRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearch.DriveRouteQuery</a></dt>
<dd>
<div class="block">返回设定查询的避让区域。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.DriveRouteQuery.html#getAvoidpolygons--">getAvoidpolygons()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.DriveRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearchV2.DriveRouteQuery</a></dt>
<dd>
<div class="block">返回设定查询的避让区域。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.DriveRouteQuery.html#getAvoidpolygonsStr--">getAvoidpolygonsStr()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.DriveRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearch.DriveRouteQuery</a></dt>
<dd>
<div class="block">将避让区域转换为字符串输出。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.DriveRouteQuery.html#getAvoidpolygonsStr--">getAvoidpolygonsStr()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.DriveRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearchV2.DriveRouteQuery</a></dt>
<dd>
<div class="block">将避让区域转换为字符串输出。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.DriveRouteQuery.html#getAvoidRoad--">getAvoidRoad()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.DriveRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearch.DriveRouteQuery</a></dt>
<dd>
<div class="block">返回设定查询的避让道路。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.DriveRouteQuery.html#getAvoidRoad--">getAvoidRoad()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.DriveRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearchV2.DriveRouteQuery</a></dt>
<dd>
<div class="block">返回设定查询的避让道路。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/busline/BusLineItem.html#getBasicPrice--">getBasicPrice()</a></span> - 类 中的方法com.amap.api.services.busline.<a href="../com/amap/api/services/busline/BusLineItem.html" title="com.amap.api.services.busline中的类">BusLineItem</a></dt>
<dd>
<div class="block">返回公交线路的起步价，单位元。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/cloud/CloudResult.html#getBound--">getBound()</a></span> - 类 中的方法com.amap.api.services.cloud.<a href="../com/amap/api/services/cloud/CloudResult.html" title="com.amap.api.services.cloud中的类">CloudResult</a></dt>
<dd>
<div class="block">返回搜索结果的查询范围。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/cloud/CloudSearch.Query.html#getBound--">getBound()</a></span> - 类 中的方法com.amap.api.services.cloud.<a href="../com/amap/api/services/cloud/CloudSearch.Query.html" title="com.amap.api.services.cloud中的类">CloudSearch.Query</a></dt>
<dd>
<div class="block">返回查询的范围（本地、圆形、矩形或者多边形）。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiResult.html#getBound--">getBound()</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiResult.html" title="com.amap.api.services.poisearch中的类">PoiResult</a></dt>
<dd>
<div class="block">返回该结果对应的范围参数。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiResultV2.html#getBound--">getBound()</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiResultV2.html" title="com.amap.api.services.poisearch中的类">PoiResultV2</a></dt>
<dd>
<div class="block">返回该结果对应的范围参数。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearch.html#getBound--">getBound()</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearch.html" title="com.amap.api.services.poisearch中的类">PoiSearch</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">返回查询矩形。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearchV2.html#getBound--">getBound()</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearchV2.html" title="com.amap.api.services.poisearch中的类">PoiSearchV2</a></dt>
<dd>
<div class="block">返回查询矩形。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/busline/BusLineItem.html#getBounds--">getBounds()</a></span> - 类 中的方法com.amap.api.services.busline.<a href="../com/amap/api/services/busline/BusLineItem.html" title="com.amap.api.services.busline中的类">BusLineItem</a></dt>
<dd>
<div class="block">返回公交线路外包矩形的左下与右上顶点坐标。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/GeocodeAddress.html#getBuilding--">getBuilding()</a></span> - 类 中的方法com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/GeocodeAddress.html" title="com.amap.api.services.geocoder中的类">GeocodeAddress</a></dt>
<dd>
<div class="block">地理编码返回的建筑物名称。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/RegeocodeAddress.html#getBuilding--">getBuilding()</a></span> - 类 中的方法com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/RegeocodeAddress.html" title="com.amap.api.services.geocoder中的类">RegeocodeAddress</a></dt>
<dd>
<div class="block">逆地理编码返回的建筑物名称。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearch.Query.html#getBuilding--">getBuilding()</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearch.Query.html" title="com.amap.api.services.poisearch中的类">PoiSearch.Query</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">返回待查询建筑物的标识</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearchV2.Query.html#getBuilding--">getBuilding()</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearchV2.Query.html" title="com.amap.api.services.poisearch中的类">PoiSearchV2.Query</a></dt>
<dd>
<div class="block">返回待查询建筑物的标识</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/busline/BusLineItem.html#getBusCompany--">getBusCompany()</a></span> - 类 中的方法com.amap.api.services.busline.<a href="../com/amap/api/services/busline/BusLineItem.html" title="com.amap.api.services.busline中的类">BusLineItem</a></dt>
<dd>
<div class="block">返回公交线路所属的公交公司。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/BusPath.html#getBusDistance--">getBusDistance()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/BusPath.html" title="com.amap.api.services.route中的类">BusPath</a></dt>
<dd>
<div class="block">返回此方案的公交行驶的总距离 ，单位米。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/BusPathV2.html#getBusDistance--">getBusDistance()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/BusPathV2.html" title="com.amap.api.services.route中的类">BusPathV2</a></dt>
<dd>
<div class="block">返回此方案的公交行驶的总距离 ，单位米。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/PoiItem.html#getBusinessArea--">getBusinessArea()</a></span> - 类 中的方法com.amap.api.services.core.<a href="../com/amap/api/services/core/PoiItem.html" title="com.amap.api.services.core中的类">PoiItem</a></dt>
<dd>
<div class="block">返回POI的所在商圈。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/Business.html#getBusinessArea--">getBusinessArea()</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/Business.html" title="com.amap.api.services.poisearch中的类">Business</a></dt>
<dd>
<div class="block">返回POI的所在商圈。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/RegeocodeAddress.html#getBusinessAreas--">getBusinessAreas()</a></span> - 类 中的方法com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/RegeocodeAddress.html" title="com.amap.api.services.geocoder中的类">RegeocodeAddress</a></dt>
<dd>
<div class="block">返回商圈对象列表，若服务没有相应数据，则返回列表长度为0。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/BusStep.html#getBusLine--">getBusLine()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/BusStep.html" title="com.amap.api.services.route中的类">BusStep</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span>
<div class="block"><span class="deprecationComment">此接口废弃。</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/BusStepV2.html#getBusLine--">getBusLine()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/BusStepV2.html" title="com.amap.api.services.route中的类">BusStepV2</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span>
<div class="block"><span class="deprecationComment">此接口废弃。</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/busline/BusLineItem.html#getBusLineId--">getBusLineId()</a></span> - 类 中的方法com.amap.api.services.busline.<a href="../com/amap/api/services/busline/BusLineItem.html" title="com.amap.api.services.busline中的类">BusLineItem</a></dt>
<dd>
<div class="block">返回公交线路的唯一ID。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/busline/BusStationItem.html#getBusLineItems--">getBusLineItems()</a></span> - 类 中的方法com.amap.api.services.busline.<a href="../com/amap/api/services/busline/BusStationItem.html" title="com.amap.api.services.busline中的类">BusStationItem</a></dt>
<dd>
<div class="block">返回途径此公交站的公交路线。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/busline/BusLineItem.html#getBusLineName--">getBusLineName()</a></span> - 类 中的方法com.amap.api.services.busline.<a href="../com/amap/api/services/busline/BusLineItem.html" title="com.amap.api.services.busline中的类">BusLineItem</a></dt>
<dd>
<div class="block">返回公交线路的名称，包含线路编号和文字名称、类型、首发站、终点站。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/busline/BusLineResult.html#getBusLines--">getBusLines()</a></span> - 类 中的方法com.amap.api.services.busline.<a href="../com/amap/api/services/busline/BusLineResult.html" title="com.amap.api.services.busline中的类">BusLineResult</a></dt>
<dd>
<div class="block">返回当前页对应的结果。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/BusStep.html#getBusLines--">getBusLines()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/BusStep.html" title="com.amap.api.services.route中的类">BusStep</a></dt>
<dd>
<div class="block">返回此路段的公交导航信息。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/BusStepV2.html#getBusLines--">getBusLines()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/BusStepV2.html" title="com.amap.api.services.route中的类">BusStepV2</a></dt>
<dd>
<div class="block">返回此路段的公交导航信息。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/busline/BusLineItem.html#getBusLineType--">getBusLineType()</a></span> - 类 中的方法com.amap.api.services.busline.<a href="../com/amap/api/services/busline/BusLineItem.html" title="com.amap.api.services.busline中的类">BusLineItem</a></dt>
<dd>
<div class="block">返回公交线路的类型，类型为中文名称。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/share/ShareSearch.ShareBusRouteQuery.html#getBusMode--">getBusMode()</a></span> - 类 中的方法com.amap.api.services.share.<a href="../com/amap/api/services/share/ShareSearch.ShareBusRouteQuery.html" title="com.amap.api.services.share中的类">ShareSearch.ShareBusRouteQuery</a></dt>
<dd>
<div class="block">返回规划策略。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/BusRouteResult.html#getBusQuery--">getBusQuery()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/BusRouteResult.html" title="com.amap.api.services.route中的类">BusRouteResult</a></dt>
<dd>
<div class="block">返回该结果对应的查询参数。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/BusRouteResultV2.html#getBusQuery--">getBusQuery()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/BusRouteResultV2.html" title="com.amap.api.services.route中的类">BusRouteResultV2</a></dt>
<dd>
<div class="block">返回该结果对应的查询参数。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/busline/BusStationItem.html#getBusStationId--">getBusStationId()</a></span> - 类 中的方法com.amap.api.services.busline.<a href="../com/amap/api/services/busline/BusStationItem.html" title="com.amap.api.services.busline中的类">BusStationItem</a></dt>
<dd>
<div class="block">返回车站ID。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/busline/BusStationItem.html#getBusStationName--">getBusStationName()</a></span> - 类 中的方法com.amap.api.services.busline.<a href="../com/amap/api/services/busline/BusStationItem.html" title="com.amap.api.services.busline中的类">BusStationItem</a></dt>
<dd>
<div class="block">返回车站名称。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/busline/BusLineItem.html#getBusStations--">getBusStations()</a></span> - 类 中的方法com.amap.api.services.busline.<a href="../com/amap/api/services/busline/BusLineItem.html" title="com.amap.api.services.busline中的类">BusLineItem</a></dt>
<dd>
<div class="block">返回公交线路的站点列表。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/busline/BusStationResult.html#getBusStations--">getBusStations()</a></span> - 类 中的方法com.amap.api.services.busline.<a href="../com/amap/api/services/busline/BusStationResult.html" title="com.amap.api.services.busline中的类">BusStationResult</a></dt>
<dd>
<div class="block">返回公交站列表。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.DrivePlanQuery.html#getCarType--">getCarType()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.DrivePlanQuery.html" title="com.amap.api.services.route中的类">RouteSearch.DrivePlanQuery</a></dt>
<dd>
<div class="block">返回汽车类型</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.DriveRouteQuery.html#getCarType--">getCarType()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.DriveRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearch.DriveRouteQuery</a></dt>
<dd>
<div class="block">返回车辆类型。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.DriveRouteQuery.html#getCarType--">getCarType()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.DriveRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearchV2.DriveRouteQuery</a></dt>
<dd>
<div class="block">返回车辆类型。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/busline/BusLineQuery.html#getCategory--">getCategory()</a></span> - 类 中的方法com.amap.api.services.busline.<a href="../com/amap/api/services/busline/BusLineQuery.html" title="com.amap.api.services.busline中的类">BusLineQuery</a></dt>
<dd>
<div class="block">返回查询类型。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearch.Query.html#getCategory--">getCategory()</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearch.Query.html" title="com.amap.api.services.poisearch中的类">PoiSearch.Query</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">返回待查分类组合。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearchV2.Query.html#getCategory--">getCategory()</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearchV2.Query.html" title="com.amap.api.services.poisearch中的类">PoiSearchV2.Query</a></dt>
<dd>
<div class="block">返回待查分类组合。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/cloud/CloudSearch.SearchBound.html#getCenter--">getCenter()</a></span> - 类 中的方法com.amap.api.services.cloud.<a href="../com/amap/api/services/cloud/CloudSearch.SearchBound.html" title="com.amap.api.services.cloud中的类">CloudSearch.SearchBound</a></dt>
<dd>
<div class="block">返回查询圆形的中心点坐标。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/district/DistrictItem.html#getCenter--">getCenter()</a></span> - 类 中的方法com.amap.api.services.district.<a href="../com/amap/api/services/district/DistrictItem.html" title="com.amap.api.services.district中的类">DistrictItem</a></dt>
<dd>
<div class="block">返回行政区域规划中心点的经纬度坐标。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearch.SearchBound.html#getCenter--">getCenter()</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearch.SearchBound.html" title="com.amap.api.services.poisearch中的类">PoiSearch.SearchBound</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">返回矩形中心点坐标。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearchV2.SearchBound.html#getCenter--">getCenter()</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearchV2.SearchBound.html" title="com.amap.api.services.poisearch中的类">PoiSearchV2.SearchBound</a></dt>
<dd>
<div class="block">返回矩形中心点坐标。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/BusinessArea.html#getCenterPoint--">getCenterPoint()</a></span> - 类 中的方法com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/BusinessArea.html" title="com.amap.api.services.geocoder中的类">BusinessArea</a></dt>
<dd>
<div class="block">返回当前商圈的中心点坐标。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/nearby/NearbySearch.NearbyQuery.html#getCenterPoint--">getCenterPoint()</a></span> - 类 中的方法com.amap.api.services.nearby.<a href="../com/amap/api/services/nearby/NearbySearch.NearbyQuery.html" title="com.amap.api.services.nearby中的类">NearbySearch.NearbyQuery</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">返回检索中心点。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/road/Road.html#getCenterPoint--">getCenterPoint()</a></span> - 类 中的方法com.amap.api.services.road.<a href="../com/amap/api/services/road/Road.html" title="com.amap.api.services.road中的类">Road</a></dt>
<dd>
<div class="block">返回结果的道路中心点坐标。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/busline/BusLineQuery.html#getCity--">getCity()</a></span> - 类 中的方法com.amap.api.services.busline.<a href="../com/amap/api/services/busline/BusLineQuery.html" title="com.amap.api.services.busline中的类">BusLineQuery</a></dt>
<dd>
<div class="block">返回查询城市编码/行政区划代码/城市名称。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/busline/BusStationQuery.html#getCity--">getCity()</a></span> - 类 中的方法com.amap.api.services.busline.<a href="../com/amap/api/services/busline/BusStationQuery.html" title="com.amap.api.services.busline中的类">BusStationQuery</a></dt>
<dd>
<div class="block">返回查询城市编码/行政区划代码/城市名称。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/cloud/CloudSearch.SearchBound.html#getCity--">getCity()</a></span> - 类 中的方法com.amap.api.services.cloud.<a href="../com/amap/api/services/cloud/CloudSearch.SearchBound.html" title="com.amap.api.services.cloud中的类">CloudSearch.SearchBound</a></dt>
<dd>
<div class="block">返回查询的城市名称，适用于本地搜索。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/GeocodeAddress.html#getCity--">getCity()</a></span> - 类 中的方法com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/GeocodeAddress.html" title="com.amap.api.services.geocoder中的类">GeocodeAddress</a></dt>
<dd>
<div class="block">地理编码返回的所在城市名称。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/GeocodeQuery.html#getCity--">getCity()</a></span> - 类 中的方法com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/GeocodeQuery.html" title="com.amap.api.services.geocoder中的类">GeocodeQuery</a></dt>
<dd>
<div class="block">返回查询城市编码/城市名称/行政区划代码。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/RegeocodeAddress.html#getCity--">getCity()</a></span> - 类 中的方法com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/RegeocodeAddress.html" title="com.amap.api.services.geocoder中的类">RegeocodeAddress</a></dt>
<dd>
<div class="block">逆地理编码返回的所在城市名称。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/help/InputtipsQuery.html#getCity--">getCity()</a></span> - 类 中的方法com.amap.api.services.help.<a href="../com/amap/api/services/help/InputtipsQuery.html" title="com.amap.api.services.help中的类">InputtipsQuery</a></dt>
<dd>
<div class="block">返回查询城市。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearch.Query.html#getCity--">getCity()</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearch.Query.html" title="com.amap.api.services.poisearch中的类">PoiSearch.Query</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">返回待查城市（地区）的电话区号。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearchV2.Query.html#getCity--">getCity()</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearchV2.Query.html" title="com.amap.api.services.poisearch中的类">PoiSearchV2.Query</a></dt>
<dd>
<div class="block">返回待查城市（地区）的电话区号。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.BusRouteQuery.html#getCity--">getCity()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.BusRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearch.BusRouteQuery</a></dt>
<dd>
<div class="block">返回查询的城市。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.BusRouteQuery.html#getCity--">getCity()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.BusRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearchV2.BusRouteQuery</a></dt>
<dd>
<div class="block">返回查询的城市。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/weather/LocalWeatherForecast.html#getCity--">getCity()</a></span> - 类 中的方法com.amap.api.services.weather.<a href="../com/amap/api/services/weather/LocalWeatherForecast.html" title="com.amap.api.services.weather中的类">LocalWeatherForecast</a></dt>
<dd>
<div class="block">返回城市名称。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/weather/LocalWeatherLive.html#getCity--">getCity()</a></span> - 类 中的方法com.amap.api.services.weather.<a href="../com/amap/api/services/weather/LocalWeatherLive.html" title="com.amap.api.services.weather中的类">LocalWeatherLive</a></dt>
<dd>
<div class="block">返回城市名称。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/weather/WeatherSearchQuery.html#getCity--">getCity()</a></span> - 类 中的方法com.amap.api.services.weather.<a href="../com/amap/api/services/weather/WeatherSearchQuery.html" title="com.amap.api.services.weather中的类">WeatherSearchQuery</a></dt>
<dd>
<div class="block">返回查询的城市名称。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/busline/BusLineItem.html#getCityCode--">getCityCode()</a></span> - 类 中的方法com.amap.api.services.busline.<a href="../com/amap/api/services/busline/BusLineItem.html" title="com.amap.api.services.busline中的类">BusLineItem</a></dt>
<dd>
<div class="block">返回公交线路的城市编码。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/busline/BusStationItem.html#getCityCode--">getCityCode()</a></span> - 类 中的方法com.amap.api.services.busline.<a href="../com/amap/api/services/busline/BusStationItem.html" title="com.amap.api.services.busline中的类">BusStationItem</a></dt>
<dd>
<div class="block">返回车站城市编码。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/PoiItem.html#getCityCode--">getCityCode()</a></span> - 类 中的方法com.amap.api.services.core.<a href="../com/amap/api/services/core/PoiItem.html" title="com.amap.api.services.core中的类">PoiItem</a></dt>
<dd>
<div class="block">返回POI的城市编码</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/SuggestionCity.html#getCityCode--">getCityCode()</a></span> - 类 中的方法com.amap.api.services.core.<a href="../com/amap/api/services/core/SuggestionCity.html" title="com.amap.api.services.core中的类">SuggestionCity</a></dt>
<dd>
<div class="block">返回城市编码。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/district/DistrictItem.html#getCitycode--">getCitycode()</a></span> - 类 中的方法com.amap.api.services.district.<a href="../com/amap/api/services/district/DistrictItem.html" title="com.amap.api.services.district中的类">DistrictItem</a></dt>
<dd>
<div class="block">返回城市编码，如果行政区为省或者国家，此字段无返回值。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/RegeocodeAddress.html#getCityCode--">getCityCode()</a></span> - 类 中的方法com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/RegeocodeAddress.html" title="com.amap.api.services.geocoder中的类">RegeocodeAddress</a></dt>
<dd>
<div class="block">返回逆地理编码结果所在城市编码。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/road/Road.html#getCityCode--">getCityCode()</a></span> - 类 中的方法com.amap.api.services.road.<a href="../com/amap/api/services/road/Road.html" title="com.amap.api.services.road中的类">Road</a></dt>
<dd>
<div class="block">返回结果的城市编码。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.BusRouteQuery.html#getCityd--">getCityd()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.BusRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearch.BusRouteQuery</a></dt>
<dd>
<div class="block">返回目的地的城市（跨城公交路径规划）。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.BusRouteQuery.html#getCityd--">getCityd()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.BusRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearchV2.BusRouteQuery</a></dt>
<dd>
<div class="block">返回目的地的城市（跨城公交路径规划）。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/help/InputtipsQuery.html#getCityLimit--">getCityLimit()</a></span> - 类 中的方法com.amap.api.services.help.<a href="../com/amap/api/services/help/InputtipsQuery.html" title="com.amap.api.services.help中的类">InputtipsQuery</a></dt>
<dd>
<div class="block">返回是否严重按照设定城市返回结果。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearch.Query.html#getCityLimit--">getCityLimit()</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearch.Query.html" title="com.amap.api.services.poisearch中的类">PoiSearch.Query</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">返回是否严格按照设定城市搜索</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearchV2.Query.html#getCityLimit--">getCityLimit()</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearchV2.Query.html" title="com.amap.api.services.poisearch中的类">PoiSearchV2.Query</a></dt>
<dd>
<div class="block">返回是否严格按照设定城市搜索</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/PoiItem.html#getCityName--">getCityName()</a></span> - 类 中的方法com.amap.api.services.core.<a href="../com/amap/api/services/core/PoiItem.html" title="com.amap.api.services.core中的类">PoiItem</a></dt>
<dd>
<div class="block">返回POI的城市名称。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/SuggestionCity.html#getCityName--">getCityName()</a></span> - 类 中的方法com.amap.api.services.core.<a href="../com/amap/api/services/core/SuggestionCity.html" title="com.amap.api.services.core中的类">SuggestionCity</a></dt>
<dd>
<div class="block">返回城市名称。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/cloud/CloudItem.html#getCloudImage--">getCloudImage()</a></span> - 类 中的方法com.amap.api.services.cloud.<a href="../com/amap/api/services/cloud/CloudItem.html" title="com.amap.api.services.cloud中的类">CloudItem</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span>
<div class="block"><span class="deprecationComment">自7.4.0起不再支持企业地图图片</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/cloud/CloudResult.html#getClouds--">getClouds()</a></span> - 类 中的方法com.amap.api.services.cloud.<a href="../com/amap/api/services/cloud/CloudResult.html" title="com.amap.api.services.cloud中的类">CloudResult</a></dt>
<dd>
<div class="block">返回当前页所有 <a href="../com/amap/api/services/cloud/CloudItem.html" title="com.amap.api.services.cloud中的类"><code>CloudItem</code></a> 结果。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RailwaySpace.html#getCode--">getCode()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RailwaySpace.html" title="com.amap.api.services.route中的类">RailwaySpace</a></dt>
<dd>
<div class="block">返回舱位编码。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/ServiceSettings.html#getConnectionTimeOut--">getConnectionTimeOut()</a></span> - 类 中的方法com.amap.api.services.core.<a href="../com/amap/api/services/core/ServiceSettings.html" title="com.amap.api.services.core中的类">ServiceSettings</a></dt>
<dd>
<div class="block">返回搜索服务建立连接超时限制。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/nearby/NearbySearch.NearbyQuery.html#getCoordType--">getCoordType()</a></span> - 类 中的方法com.amap.api.services.nearby.<a href="../com/amap/api/services/nearby/NearbySearch.NearbyQuery.html" title="com.amap.api.services.nearby中的类">NearbySearch.NearbyQuery</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">返回坐标类型。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/nearby/UploadInfo.html#getCoordType--">getCoordType()</a></span> - 类 中的方法com.amap.api.services.nearby.<a href="../com/amap/api/services/nearby/UploadInfo.html" title="com.amap.api.services.nearby中的类">UploadInfo</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">返回坐标类型。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/Business.html#getCost--">getCost()</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/Business.html" title="com.amap.api.services.poisearch中的类">Business</a></dt>
<dd>
<div class="block">返回POI的人均消费。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/BusPath.html#getCost--">getCost()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/BusPath.html" title="com.amap.api.services.route中的类">BusPath</a></dt>
<dd>
<div class="block">返回公交换乘方案的花费，单位元。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/BusPathV2.html#getCost--">getCost()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/BusPathV2.html" title="com.amap.api.services.route中的类">BusPathV2</a></dt>
<dd>
<div class="block">返回公交换乘方案的花费，单位元。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/DrivePathV2.html#getCost--">getCost()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/DrivePathV2.html" title="com.amap.api.services.route中的类">DrivePathV2</a></dt>
<dd>
<div class="block">获取方案所需时间及费用成本</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RailwaySpace.html#getCost--">getCost()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RailwaySpace.html" title="com.amap.api.services.route中的类">RailwaySpace</a></dt>
<dd>
<div class="block">返回对应舱位的费用信息。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/DriveStepV2.html#getCostDetail--">getCostDetail()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/DriveStepV2.html" title="com.amap.api.services.route中的类">DriveStepV2</a></dt>
<dd>
<div class="block">获取路线消耗信息</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiResultV2.html#getCount--">getCount()</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiResultV2.html" title="com.amap.api.services.poisearch中的类">PoiResultV2</a></dt>
<dd>
<div class="block">返回当前结果集的总数。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.DrivePlanQuery.html#getCount--">getCount()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.DrivePlanQuery.html" title="com.amap.api.services.route中的类">RouteSearch.DrivePlanQuery</a></dt>
<dd>
<div class="block">返回规划的时间点个数</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/GeocodeAddress.html#getCountry--">getCountry()</a></span> - 类 中的方法com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/GeocodeAddress.html" title="com.amap.api.services.geocoder中的类">GeocodeAddress</a></dt>
<dd>
<div class="block">海外生效
 国家名称</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/GeocodeQuery.html#getCountry--">getCountry()</a></span> - 类 中的方法com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/GeocodeQuery.html" title="com.amap.api.services.geocoder中的类">GeocodeQuery</a></dt>
<dd>
<div class="block">海外生效
 国家编码
 指定查询国家，支持多个国家，用“|”分隔，可选值：国家代码ISO 3166 或 global
 https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/RegeocodeAddress.html#getCountry--">getCountry()</a></span> - 类 中的方法com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/RegeocodeAddress.html" title="com.amap.api.services.geocoder中的类">RegeocodeAddress</a></dt>
<dd>
<div class="block">获取国家名称。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/RegeocodeAddress.html#getCountryCode--">getCountryCode()</a></span> - 类 中的方法com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/RegeocodeAddress.html" title="com.amap.api.services.geocoder中的类">RegeocodeAddress</a></dt>
<dd>
<div class="block">海外生效
 国家简码</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/cloud/CloudItem.html#getCreatetime--">getCreatetime()</a></span> - 类 中的方法com.amap.api.services.cloud.<a href="../com/amap/api/services/cloud/CloudItem.html" title="com.amap.api.services.cloud中的类">CloudItem</a></dt>
<dd>
<div class="block">返回创建企业地图数据的时间。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/RegeocodeAddress.html#getCrossroads--">getCrossroads()</a></span> - 类 中的方法com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/RegeocodeAddress.html" title="com.amap.api.services.geocoder中的类">RegeocodeAddress</a></dt>
<dd>
<div class="block">逆地理编码返回的交叉路口列表。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/cloud/CloudItem.html#getCustomfield--">getCustomfield()</a></span> - 类 中的方法com.amap.api.services.cloud.<a href="../com/amap/api/services/cloud/CloudItem.html" title="com.amap.api.services.cloud中的类">CloudItem</a></dt>
<dd>
<div class="block">返回用户自定义字段。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.BusRouteQuery.html#getDate--">getDate()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.BusRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearchV2.BusRouteQuery</a></dt>
<dd>
<div class="block">请求日期

 例如:2013-10-28</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/weather/LocalDayWeatherForecast.html#getDate--">getDate()</a></span> - 类 中的方法com.amap.api.services.weather.<a href="../com/amap/api/services/weather/LocalDayWeatherForecast.html" title="com.amap.api.services.weather中的类">LocalDayWeatherForecast</a></dt>
<dd>
<div class="block">返回预报天气的年月日。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/weather/LocalDayWeatherForecast.html#getDayTemp--">getDayTemp()</a></span> - 类 中的方法com.amap.api.services.weather.<a href="../com/amap/api/services/weather/LocalDayWeatherForecast.html" title="com.amap.api.services.weather中的类">LocalDayWeatherForecast</a></dt>
<dd>
<div class="block">返回白天天气温度，单位：摄氏度。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/weather/LocalDayWeatherForecast.html#getDayWeather--">getDayWeather()</a></span> - 类 中的方法com.amap.api.services.weather.<a href="../com/amap/api/services/weather/LocalDayWeatherForecast.html" title="com.amap.api.services.weather中的类">LocalDayWeatherForecast</a></dt>
<dd>
<div class="block">返回白天天气现象，如“晴”、“多云”。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/weather/LocalDayWeatherForecast.html#getDayWindDirection--">getDayWindDirection()</a></span> - 类 中的方法com.amap.api.services.weather.<a href="../com/amap/api/services/weather/LocalDayWeatherForecast.html" title="com.amap.api.services.weather中的类">LocalDayWeatherForecast</a></dt>
<dd>
<div class="block">返回白天风向。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/weather/LocalDayWeatherForecast.html#getDayWindPower--">getDayWindPower()</a></span> - 类 中的方法com.amap.api.services.weather.<a href="../com/amap/api/services/weather/LocalDayWeatherForecast.html" title="com.amap.api.services.weather中的类">LocalDayWeatherForecast</a></dt>
<dd>
<div class="block">返回白天风力，单位：级。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteBusLineItem.html#getDepartureBusStation--">getDepartureBusStation()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteBusLineItem.html" title="com.amap.api.services.route中的类">RouteBusLineItem</a></dt>
<dd>
<div class="block">返回此公交换乘路段的出发站。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteRailwayItem.html#getDeparturestop--">getDeparturestop()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteRailwayItem.html" title="com.amap.api.services.route中的类">RouteRailwayItem</a></dt>
<dd>
<div class="block">返回火车发站信息。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/DistanceItem.html#getDestId--">getDestId()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/DistanceItem.html" title="com.amap.api.services.route中的类">DistanceItem</a></dt>
<dd>
<div class="block">终点坐标，终点坐标序列号（从１开始）<br>
     虽然终点仅支持一个坐标，但为了和起点统一也按照坐标序列号方式返回</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/DistanceSearch.DistanceQuery.html#getDestination--">getDestination()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/DistanceSearch.DistanceQuery.html" title="com.amap.api.services.route中的类">DistanceSearch.DistanceQuery</a></dt>
<dd>
<div class="block">获取距离测量终点</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteBusWalkItem.html#getDestination--">getDestination()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteBusWalkItem.html" title="com.amap.api.services.route中的类">RouteBusWalkItem</a></dt>
<dd>
<div class="block">返回此路段步行导航信息的终点坐标。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/TaxiItem.html#getDestination--">getDestination()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/TaxiItem.html" title="com.amap.api.services.route中的类">TaxiItem</a></dt>
<dd>
<div class="block">获取Taxi终点坐标</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/TaxiItemV2.html#getDestination--">getDestination()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/TaxiItemV2.html" title="com.amap.api.services.route中的类">TaxiItemV2</a></dt>
<dd>
<div class="block">获取Taxi终点坐标</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.FromAndTo.html#getDestinationPoiID--">getDestinationPoiID()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.FromAndTo.html" title="com.amap.api.services.route中的类">RouteSearch.FromAndTo</a></dt>
<dd>
<div class="block">返回路径规划目的地POI的ID。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.BusRouteQuery.html#getDestinationPoiId--">getDestinationPoiId()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.BusRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearchV2.BusRouteQuery</a></dt>
<dd>
<div class="block">目的地POI ID

 1、目的地POI ID与目的地经纬度均填写时，服务使用目的地 POI ID；

 2、该字段必须和起点 POI ID 成组使用。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.FromAndTo.html#getDestinationPoiID--">getDestinationPoiID()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.FromAndTo.html" title="com.amap.api.services.route中的类">RouteSearchV2.FromAndTo</a></dt>
<dd>
<div class="block">返回路径规划目的地POI的ID。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.FromAndTo.html#getDestinationType--">getDestinationType()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.FromAndTo.html" title="com.amap.api.services.route中的类">RouteSearch.FromAndTo</a></dt>
<dd>
<div class="block">返回路径规划目的地类型。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.FromAndTo.html#getDestinationType--">getDestinationType()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.FromAndTo.html" title="com.amap.api.services.route中的类">RouteSearchV2.FromAndTo</a></dt>
<dd>
<div class="block">返回路径规划目的地类型。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.DrivePlanQuery.html#getDestParentPoiID--">getDestParentPoiID()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.DrivePlanQuery.html" title="com.amap.api.services.route中的类">RouteSearch.DrivePlanQuery</a></dt>
<dd>
<div class="block">返回查询路径终点父POI的ID。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/PoiItem.html#getDirection--">getDirection()</a></span> - 类 中的方法com.amap.api.services.core.<a href="../com/amap/api/services/core/PoiItem.html" title="com.amap.api.services.core中的类">PoiItem</a></dt>
<dd>
<div class="block">返回逆地理编码查询时POI坐标点相对于地理坐标点的方向。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/RegeocodeRoad.html#getDirection--">getDirection()</a></span> - 类 中的方法com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/RegeocodeRoad.html" title="com.amap.api.services.geocoder中的类">RegeocodeRoad</a></dt>
<dd>
<div class="block">返回道路对象中道路中心点相对地理坐标点的方向。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/StreetNumber.html#getDirection--">getDirection()</a></span> - 类 中的方法com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/StreetNumber.html" title="com.amap.api.services.geocoder中的类">StreetNumber</a></dt>
<dd>
<div class="block">返回门牌信息中的方向 ，指结果点相对地理坐标点的方向。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/road/Crossroad.html#getDirection--">getDirection()</a></span> - 类 中的方法com.amap.api.services.road.<a href="../com/amap/api/services/road/Crossroad.html" title="com.amap.api.services.road中的类">Crossroad</a></dt>
<dd>
<div class="block">返回交叉路口相对逆地理坐标点的方向。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/busline/BusLineItem.html#getDirectionsCoordinates--">getDirectionsCoordinates()</a></span> - 类 中的方法com.amap.api.services.busline.<a href="../com/amap/api/services/busline/BusLineItem.html" title="com.amap.api.services.busline中的类">BusLineItem</a></dt>
<dd>
<div class="block">返回公交线路的沿途坐标，包含首发站和终点站坐标。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/busline/BusLineItem.html#getDistance--">getDistance()</a></span> - 类 中的方法com.amap.api.services.busline.<a href="../com/amap/api/services/busline/BusLineItem.html" title="com.amap.api.services.busline中的类">BusLineItem</a></dt>
<dd>
<div class="block">返回公交线路全程里程，单位千米。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/cloud/CloudItem.html#getDistance--">getDistance()</a></span> - 类 中的方法com.amap.api.services.cloud.<a href="../com/amap/api/services/cloud/CloudItem.html" title="com.amap.api.services.cloud中的类">CloudItem</a></dt>
<dd>
<div class="block">返回该企业地图数据距离中心点的距离，单位为米。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/PoiItem.html#getDistance--">getDistance()</a></span> - 类 中的方法com.amap.api.services.core.<a href="../com/amap/api/services/core/PoiItem.html" title="com.amap.api.services.core中的类">PoiItem</a></dt>
<dd>
<div class="block">获取 POI 距离中心点的距离。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/RegeocodeRoad.html#getDistance--">getDistance()</a></span> - 类 中的方法com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/RegeocodeRoad.html" title="com.amap.api.services.geocoder中的类">RegeocodeRoad</a></dt>
<dd>
<div class="block">返回道路对象中地理坐标点与道路的垂直距离，单位米。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/StreetNumber.html#getDistance--">getDistance()</a></span> - 类 中的方法com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/StreetNumber.html" title="com.amap.api.services.geocoder中的类">StreetNumber</a></dt>
<dd>
<div class="block">返回门牌信息中地理坐标点与结果点的垂直距离。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/nearby/NearbyInfo.html#getDistance--">getDistance()</a></span> - 类 中的方法com.amap.api.services.nearby.<a href="../com/amap/api/services/nearby/NearbyInfo.html" title="com.amap.api.services.nearby中的类">NearbyInfo</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">返回直线距离数据，单位米。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/SubPoiItem.html#getDistance--">getDistance()</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/SubPoiItem.html" title="com.amap.api.services.poisearch中的类">SubPoiItem</a></dt>
<dd>
<div class="block">子poi距离中心点的距离。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/road/Crossroad.html#getDistance--">getDistance()</a></span> - 类 中的方法com.amap.api.services.road.<a href="../com/amap/api/services/road/Crossroad.html" title="com.amap.api.services.road中的类">Crossroad</a></dt>
<dd>
<div class="block">返回逆地理坐标点与交叉路口的垂直距离，单位米。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/BusRouteResultV2.html#getDistance--">getDistance()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/BusRouteResultV2.html" title="com.amap.api.services.route中的类">BusRouteResultV2</a></dt>
<dd>
<div class="block">总距离</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/DistanceItem.html#getDistance--">getDistance()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/DistanceItem.html" title="com.amap.api.services.route中的类">DistanceItem</a></dt>
<dd>
<div class="block">路径距离，单位：米</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/DrivePlanPath.html#getDistance--">getDistance()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/DrivePlanPath.html" title="com.amap.api.services.route中的类">DrivePlanPath</a></dt>
<dd>
<div class="block">返回行驶距离，单位：米。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/DrivePlanStep.html#getDistance--">getDistance()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/DrivePlanStep.html" title="com.amap.api.services.route中的类">DrivePlanStep</a></dt>
<dd>
<div class="block">返回驾车路段的距离，单位米。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/DriveStep.html#getDistance--">getDistance()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/DriveStep.html" title="com.amap.api.services.route中的类">DriveStep</a></dt>
<dd>
<div class="block">返回驾车路段的距离，单位米。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/Path.html#getDistance--">getDistance()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/Path.html" title="com.amap.api.services.route中的类">Path</a></dt>
<dd>
<div class="block">返回此规划方案的距离，单位米。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RideStep.html#getDistance--">getDistance()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RideStep.html" title="com.amap.api.services.route中的类">RideStep</a></dt>
<dd>
<div class="block">返回骑行路段的距离，单位米。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteRailwayItem.html#getDistance--">getDistance()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteRailwayItem.html" title="com.amap.api.services.route中的类">RouteRailwayItem</a></dt>
<dd>
<div class="block">返回该换乘段的行车总距离。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/TaxiItem.html#getDistance--">getDistance()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/TaxiItem.html" title="com.amap.api.services.route中的类">TaxiItem</a></dt>
<dd>
<div class="block">获取方案总距离，单位米。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/TaxiItemV2.html#getDistance--">getDistance()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/TaxiItemV2.html" title="com.amap.api.services.route中的类">TaxiItemV2</a></dt>
<dd>
<div class="block">获取方案总距离，单位米。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/TMC.html#getDistance--">getDistance()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/TMC.html" title="com.amap.api.services.route中的类">TMC</a></dt>
<dd>
<div class="block">返回路况距离。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/TruckPath.html#getDistance--">getDistance()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/TruckPath.html" title="com.amap.api.services.route中的类">TruckPath</a></dt>
<dd>
<div class="block">此方案的行驶距离        单位：米</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/TruckStep.html#getDistance--">getDistance()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/TruckStep.html" title="com.amap.api.services.route中的类">TruckStep</a></dt>
<dd>
<div class="block">此路段距离   单位：米</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/WalkStep.html#getDistance--">getDistance()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/WalkStep.html" title="com.amap.api.services.route中的类">WalkStep</a></dt>
<dd>
<div class="block">返回步行路段的距离，单位米。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/routepoisearch/RoutePOIItem.html#getDistance--">getDistance()</a></span> - 类 中的方法com.amap.api.services.routepoisearch.<a href="../com/amap/api/services/routepoisearch/RoutePOIItem.html" title="com.amap.api.services.routepoisearch中的类">RoutePOIItem</a></dt>
<dd>
<div class="block">返回用户起点经过途经点POI再到终点的距离，单位是米。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/DistanceResult.html#getDistanceQuery--">getDistanceQuery()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/DistanceResult.html" title="com.amap.api.services.route中的类">DistanceResult</a></dt>
<dd>
<div class="block">触发该次距离测量的请求参数</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/DistanceResult.html#getDistanceResults--">getDistanceResults()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/DistanceResult.html" title="com.amap.api.services.route中的类">DistanceResult</a></dt>
<dd>
<div class="block">距离测量结果信息列表</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/district/DistrictResult.html#getDistrict--">getDistrict()</a></span> - 类 中的方法com.amap.api.services.district.<a href="../com/amap/api/services/district/DistrictResult.html" title="com.amap.api.services.district中的类">DistrictResult</a></dt>
<dd>
<div class="block">返回查询行政区的结果，只有<a href="../com/amap/api/services/core/AMapException.html#getErrorCode--"><code>AMapException.getErrorCode()</code></a>返回 <a href="../com/amap/api/services/core/AMapException.html#CODE_AMAP_SUCCESS"><code>AMapException.CODE_AMAP_SUCCESS</code></a>时，才有查询结果。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/GeocodeAddress.html#getDistrict--">getDistrict()</a></span> - 类 中的方法com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/GeocodeAddress.html" title="com.amap.api.services.geocoder中的类">GeocodeAddress</a></dt>
<dd>
<div class="block">地理编码返回的所在区（县）名称。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/RegeocodeAddress.html#getDistrict--">getDistrict()</a></span> - 类 中的方法com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/RegeocodeAddress.html" title="com.amap.api.services.geocoder中的类">RegeocodeAddress</a></dt>
<dd>
<div class="block">逆地理编码返回的所在区（县）名称。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/help/Tip.html#getDistrict--">getDistrict()</a></span> - 类 中的方法com.amap.api.services.help.<a href="../com/amap/api/services/help/Tip.html" title="com.amap.api.services.help中的类">Tip</a></dt>
<dd>
<div class="block">返回提示区域。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/District.html#getDistrictAdcode--">getDistrictAdcode()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/District.html" title="com.amap.api.services.route中的类">District</a></dt>
<dd>
<div class="block">得到行政区的编码。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/District.html#getDistrictName--">getDistrictName()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/District.html" title="com.amap.api.services.route中的类">District</a></dt>
<dd>
<div class="block">得到行政区的名称。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchCity.html#getDistricts--">getDistricts()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchCity.html" title="com.amap.api.services.route中的类">RouteSearchCity</a></dt>
<dd>
<div class="block">设置城市的名称。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/DriveRouteResult.html#getDriveQuery--">getDriveQuery()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/DriveRouteResult.html" title="com.amap.api.services.route中的类">DriveRouteResult</a></dt>
<dd>
<div class="block">返回该结果对应的查询参数。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/DriveRouteResultV2.html#getDriveQuery--">getDriveQuery()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/DriveRouteResultV2.html" title="com.amap.api.services.route中的类">DriveRouteResultV2</a></dt>
<dd>
<div class="block">返回该结果对应的查询参数。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/nearby/NearbyInfo.html#getDrivingDistance--">getDrivingDistance()</a></span> - 类 中的方法com.amap.api.services.nearby.<a href="../com/amap/api/services/nearby/NearbyInfo.html" title="com.amap.api.services.nearby中的类">NearbyInfo</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">返回行驶里程数据，单位米；里程仅在调用行驶距离检索时会返回。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/share/ShareSearch.ShareDrivingRouteQuery.html#getDrivingMode--">getDrivingMode()</a></span> - 类 中的方法com.amap.api.services.share.<a href="../com/amap/api/services/share/ShareSearch.ShareDrivingRouteQuery.html" title="com.amap.api.services.share中的类">ShareSearch.ShareDrivingRouteQuery</a></dt>
<dd>
<div class="block">返回驾车策略。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/Cost.html#getDuration--">getDuration()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/Cost.html" title="com.amap.api.services.route中的类">Cost</a></dt>
<dd>
<div class="block">获取线路耗时，包括方案总耗时及分段step中的耗时</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/DistanceItem.html#getDuration--">getDuration()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/DistanceItem.html" title="com.amap.api.services.route中的类">DistanceItem</a></dt>
<dd>
<div class="block">预计行驶时间，单位：秒</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/DriveStep.html#getDuration--">getDuration()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/DriveStep.html" title="com.amap.api.services.route中的类">DriveStep</a></dt>
<dd>
<div class="block">返回驾车路段的预计时间，单位秒。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/Path.html#getDuration--">getDuration()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/Path.html" title="com.amap.api.services.route中的类">Path</a></dt>
<dd>
<div class="block">返回方案的预计消耗时间，单位秒。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RideStep.html#getDuration--">getDuration()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RideStep.html" title="com.amap.api.services.route中的类">RideStep</a></dt>
<dd>
<div class="block">返回骑行路段的预计时间，单位为秒。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteBusLineItem.html#getDuration--">getDuration()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteBusLineItem.html" title="com.amap.api.services.route中的类">RouteBusLineItem</a></dt>
<dd>
<div class="block">返回此公交换乘路段公交预计行驶时间 。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/TaxiItem.html#getDuration--">getDuration()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/TaxiItem.html" title="com.amap.api.services.route中的类">TaxiItem</a></dt>
<dd>
<div class="block">获取方案耗时，单位秒。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/TaxiItemV2.html#getDuration--">getDuration()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/TaxiItemV2.html" title="com.amap.api.services.route中的类">TaxiItemV2</a></dt>
<dd>
<div class="block">获取方案耗时，单位秒。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/TimeInfosElement.html#getDuration--">getDuration()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/TimeInfosElement.html" title="com.amap.api.services.route中的类">TimeInfosElement</a></dt>
<dd>
<div class="block">返回驾车未来路径规划路线总时长。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/TruckPath.html#getDuration--">getDuration()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/TruckPath.html" title="com.amap.api.services.route中的类">TruckPath</a></dt>
<dd>
<div class="block">此方案的耗时  单位：秒</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/TruckStep.html#getDuration--">getDuration()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/TruckStep.html" title="com.amap.api.services.route中的类">TruckStep</a></dt>
<dd>
<div class="block">此路段预计时间</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/WalkStep.html#getDuration--">getDuration()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/WalkStep.html" title="com.amap.api.services.route中的类">WalkStep</a></dt>
<dd>
<div class="block">返回步行路段的预计时间，单位为秒。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/routepoisearch/RoutePOIItem.html#getDuration--">getDuration()</a></span> - 类 中的方法com.amap.api.services.routepoisearch.<a href="../com/amap/api/services/routepoisearch/RoutePOIItem.html" title="com.amap.api.services.routepoisearch中的类">RoutePOIItem</a></dt>
<dd>
<div class="block">返回用户起点经过途经点再到终点的时间，单位为秒。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/TimeInfo.html#getElements--">getElements()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/TimeInfo.html" title="com.amap.api.services.route中的类">TimeInfo</a></dt>
<dd>
<div class="block">返回驾车未来路径不同时间的规划以及信息列表。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/PoiItem.html#getEmail--">getEmail()</a></span> - 类 中的方法com.amap.api.services.core.<a href="../com/amap/api/services/core/PoiItem.html" title="com.amap.api.services.core中的类">PoiItem</a></dt>
<dd>
<div class="block">返回POI的电子邮件地址。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/PoiItem.html#getEnter--">getEnter()</a></span> - 类 中的方法com.amap.api.services.core.<a href="../com/amap/api/services/core/PoiItem.html" title="com.amap.api.services.core中的类">PoiItem</a></dt>
<dd>
<div class="block">返回POI入口经纬度。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiNavi.html#getEnter--">getEnter()</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiNavi.html" title="com.amap.api.services.poisearch中的类">PoiNavi</a></dt>
<dd>
<div class="block">返回POI入口经纬度。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/BusStep.html#getEntrance--">getEntrance()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/BusStep.html" title="com.amap.api.services.route中的类">BusStep</a></dt>
<dd>
<div class="block">返回此路段的入口信息。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/BusStepV2.html#getEntrance--">getEntrance()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/BusStepV2.html" title="com.amap.api.services.route中的类">BusStepV2</a></dt>
<dd>
<div class="block">返回此路段的入口信息。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#getErrorCode--">getErrorCode()</a></span> - 异常错误 中的方法com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">返回错误码。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/DistanceItem.html#getErrorCode--">getErrorCode()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/DistanceItem.html" title="com.amap.api.services.route中的类">DistanceItem</a></dt>
<dd>
<div class="block">仅在出错的时候显示此字段。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/DistanceItem.html#getErrorInfo--">getErrorInfo()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/DistanceItem.html" title="com.amap.api.services.route中的类">DistanceItem</a></dt>
<dd>
<div class="block">仅在出错的时候显示该字段。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#getErrorMessage--">getErrorMessage()</a></span> - 异常错误 中的方法com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">返回异常信息。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.DriveRouteQuery.html#getExclude--">getExclude()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.DriveRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearch.DriveRouteQuery</a></dt>
<dd>
<div class="block">海外生效
 规避道路类型，可选值：toll-收费道路；motorway-高速路；ferry-渡船，默认不规避</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.DriveRouteQuery.html#getExclude--">getExclude()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.DriveRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearchV2.DriveRouteQuery</a></dt>
<dd>
<div class="block">海外生效
 规避道路类型，可选值：toll-收费道路；motorway-高速路；ferry-渡船，默认不规避</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/PoiItem.html#getExit--">getExit()</a></span> - 类 中的方法com.amap.api.services.core.<a href="../com/amap/api/services/core/PoiItem.html" title="com.amap.api.services.core中的类">PoiItem</a></dt>
<dd>
<div class="block">返回POI出口经纬度。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiNavi.html#getExit--">getExit()</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiNavi.html" title="com.amap.api.services.poisearch中的类">PoiNavi</a></dt>
<dd>
<div class="block">返回POI出口经纬度。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/BusStep.html#getExit--">getExit()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/BusStep.html" title="com.amap.api.services.route中的类">BusStep</a></dt>
<dd>
<div class="block">返回此路段的出口信息。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/BusStepV2.html#getExit--">getExit()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/BusStepV2.html" title="com.amap.api.services.route中的类">BusStepV2</a></dt>
<dd>
<div class="block">返回此路段的出口信息。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/busline/BusLineQuery.html#getExtensions--">getExtensions()</a></span> - 类 中的方法com.amap.api.services.busline.<a href="../com/amap/api/services/busline/BusLineQuery.html" title="com.amap.api.services.busline中的类">BusLineQuery</a></dt>
<dd>
<div class="block">扩展字段 base表示只返回基础数据，all表示所有数据 ，默认 base</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/RegeocodeQuery.html#getExtensions--">getExtensions()</a></span> - 类 中的方法com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/RegeocodeQuery.html" title="com.amap.api.services.geocoder中的类">RegeocodeQuery</a></dt>
<dd>
<div class="block">扩展字段 base表示只返回基础数据，all表示所有数据 ，默认 base</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearch.Query.html#getExtensions--">getExtensions()</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearch.Query.html" title="com.amap.api.services.poisearch中的类">PoiSearch.Query</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">扩展字段 base表示只返回基础数据，all表示所有数据 ，默认 base</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/DistanceSearch.DistanceQuery.html#getExtensions--">getExtensions()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/DistanceSearch.DistanceQuery.html" title="com.amap.api.services.route中的类">DistanceSearch.DistanceQuery</a></dt>
<dd>
<div class="block">扩展字段 base表示只返回基础数据，all表示所有数据 ，默认 base</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.BusRouteQuery.html#getExtensions--">getExtensions()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.BusRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearch.BusRouteQuery</a></dt>
<dd>
<div class="block">扩展字段 base表示只返回基础数据，all表示所有数据 ，默认 base</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.DriveRouteQuery.html#getExtensions--">getExtensions()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.DriveRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearch.DriveRouteQuery</a></dt>
<dd>
<div class="block">扩展字段 base表示只返回基础数据，all表示所有数据 ，默认 base</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.RideRouteQuery.html#getExtensions--">getExtensions()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.RideRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearch.RideRouteQuery</a></dt>
<dd>
<div class="block">扩展字段 base表示只返回基础数据，all表示所有数据 ，默认 base</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.TruckRouteQuery.html#getExtensions--">getExtensions()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.TruckRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearch.TruckRouteQuery</a></dt>
<dd>
<div class="block">扩展字段 base表示只返回基础数据，all表示所有数据 ，默认 base</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.WalkRouteQuery.html#getExtensions--">getExtensions()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.WalkRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearch.WalkRouteQuery</a></dt>
<dd>
<div class="block">扩展字段 base表示只返回基础数据，all表示所有数据 ，默认 base</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/cloud/CloudSearch.Query.html#getFilterNumString--">getFilterNumString()</a></span> - 类 中的方法com.amap.api.services.cloud.<a href="../com/amap/api/services/cloud/CloudSearch.Query.html" title="com.amap.api.services.cloud中的类">CloudSearch.Query</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span>
<div class="block"><span class="deprecationComment">使用 <a href="../com/amap/api/services/cloud/CloudSearch.Query.html#addFilterString-java.lang.String-java.lang.String-"><code>CloudSearch.Query.addFilterString(String, String)</code></a>替换</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/cloud/CloudSearch.Query.html#getFilterString--">getFilterString()</a></span> - 类 中的方法com.amap.api.services.cloud.<a href="../com/amap/api/services/cloud/CloudSearch.Query.html" title="com.amap.api.services.cloud中的类">CloudSearch.Query</a></dt>
<dd>
<div class="block">将筛选字段转换为字符串返回</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/busline/BusLineItem.html#getFirstBusTime--">getFirstBusTime()</a></span> - 类 中的方法com.amap.api.services.busline.<a href="../com/amap/api/services/busline/BusLineItem.html" title="com.amap.api.services.busline中的类">BusLineItem</a></dt>
<dd>
<div class="block">返回公交线路的首班车时间。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/road/Crossroad.html#getFirstRoadId--">getFirstRoadId()</a></span> - 类 中的方法com.amap.api.services.road.<a href="../com/amap/api/services/road/Crossroad.html" title="com.amap.api.services.road中的类">Crossroad</a></dt>
<dd>
<div class="block">返回交叉路口的第一条道路ID。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/road/Crossroad.html#getFirstRoadName--">getFirstRoadName()</a></span> - 类 中的方法com.amap.api.services.road.<a href="../com/amap/api/services/road/Crossroad.html" title="com.amap.api.services.road中的类">Crossroad</a></dt>
<dd>
<div class="block">返回交叉路口的第一条道路名称。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.DrivePlanQuery.html#getFirstTime--">getFirstTime()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.DrivePlanQuery.html" title="com.amap.api.services.route中的类">RouteSearch.DrivePlanQuery</a></dt>
<dd>
<div class="block">返回第一次规划的时间</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/IndoorData.html#getFloor--">getFloor()</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/IndoorData.html" title="com.amap.api.services.poisearch中的类">IndoorData</a></dt>
<dd>
<div class="block">返回该POI在建筑物内的楼层。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/IndoorDataV2.html#getFloor--">getFloor()</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/IndoorDataV2.html" title="com.amap.api.services.poisearch中的类">IndoorDataV2</a></dt>
<dd>
<div class="block">返回该POI在建筑物内的楼层。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/IndoorData.html#getFloorName--">getFloorName()</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/IndoorData.html" title="com.amap.api.services.poisearch中的类">IndoorData</a></dt>
<dd>
<div class="block">返回该POI在建筑物内的楼层显示名称。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/IndoorDataV2.html#getFloorName--">getFloorName()</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/IndoorDataV2.html" title="com.amap.api.services.poisearch中的类">IndoorDataV2</a></dt>
<dd>
<div class="block">返回该POI在建筑物内的楼层显示名称。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/weather/LocalWeatherForecastResult.html#getForecastResult--">getForecastResult()</a></span> - 类 中的方法com.amap.api.services.weather.<a href="../com/amap/api/services/weather/LocalWeatherForecastResult.html" title="com.amap.api.services.weather中的类">LocalWeatherForecastResult</a></dt>
<dd>
<div class="block">返回预报天气的查询结果。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/GeocodeAddress.html#getFormatAddress--">getFormatAddress()</a></span> - 类 中的方法com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/GeocodeAddress.html" title="com.amap.api.services.geocoder中的类">GeocodeAddress</a></dt>
<dd>
<div class="block">地理编码返回的格式化地址。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/RegeocodeAddress.html#getFormatAddress--">getFormatAddress()</a></span> - 类 中的方法com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/RegeocodeAddress.html" title="com.amap.api.services.geocoder中的类">RegeocodeAddress</a></dt>
<dd>
<div class="block">逆地理编码返回的格式化地址。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.FromAndTo.html#getFrom--">getFrom()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.FromAndTo.html" title="com.amap.api.services.route中的类">RouteSearch.FromAndTo</a></dt>
<dd>
<div class="block">返回路径规划的起点坐标。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.FromAndTo.html#getFrom--">getFrom()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.FromAndTo.html" title="com.amap.api.services.route中的类">RouteSearchV2.FromAndTo</a></dt>
<dd>
<div class="block">返回路径规划的起点坐标。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/routepoisearch/RoutePOISearchQuery.html#getFrom--">getFrom()</a></span> - 类 中的方法com.amap.api.services.routepoisearch.<a href="../com/amap/api/services/routepoisearch/RoutePOISearchQuery.html" title="com.amap.api.services.routepoisearch中的类">RoutePOISearchQuery</a></dt>
<dd>
<div class="block">返回沿途搜索路径规划的起点坐标。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/share/ShareSearch.ShareFromAndTo.html#getFrom--">getFrom()</a></span> - 类 中的方法com.amap.api.services.share.<a href="../com/amap/api/services/share/ShareSearch.ShareFromAndTo.html" title="com.amap.api.services.share中的类">ShareSearch.ShareFromAndTo</a></dt>
<dd>
<div class="block">返回起点坐标。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.BusRouteQuery.html#getFromAndTo--">getFromAndTo()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.BusRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearch.BusRouteQuery</a></dt>
<dd>
<div class="block">返回查询路径的起终点。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.DrivePlanQuery.html#getFromAndTo--">getFromAndTo()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.DrivePlanQuery.html" title="com.amap.api.services.route中的类">RouteSearch.DrivePlanQuery</a></dt>
<dd>
<div class="block">返回查询路径的起终点。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.DriveRouteQuery.html#getFromAndTo--">getFromAndTo()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.DriveRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearch.DriveRouteQuery</a></dt>
<dd>
<div class="block">返回查询路径的起终点。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.RideRouteQuery.html#getFromAndTo--">getFromAndTo()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.RideRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearch.RideRouteQuery</a></dt>
<dd>
<div class="block">返回查询路径的起终点。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.TruckRouteQuery.html#getFromAndTo--">getFromAndTo()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.TruckRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearch.TruckRouteQuery</a></dt>
<dd>
<div class="block">获取路径的起点终点</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.WalkRouteQuery.html#getFromAndTo--">getFromAndTo()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.WalkRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearch.WalkRouteQuery</a></dt>
<dd>
<div class="block">返回查询路径的起终点。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.BusRouteQuery.html#getFromAndTo--">getFromAndTo()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.BusRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearchV2.BusRouteQuery</a></dt>
<dd>
<div class="block">返回查询路径的起终点。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.DriveRouteQuery.html#getFromAndTo--">getFromAndTo()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.DriveRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearchV2.DriveRouteQuery</a></dt>
<dd>
<div class="block">返回查询路径的起终点。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.RideRouteQuery.html#getFromAndTo--">getFromAndTo()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.RideRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearchV2.RideRouteQuery</a></dt>
<dd>
<div class="block">返回查询路径的起终点。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.WalkRouteQuery.html#getFromAndTo--">getFromAndTo()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.WalkRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearchV2.WalkRouteQuery</a></dt>
<dd>
<div class="block">返回查询路径的起终点。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/share/ShareSearch.ShareNaviQuery.html#getFromAndTo--">getFromAndTo()</a></span> - 类 中的方法com.amap.api.services.share.<a href="../com/amap/api/services/share/ShareSearch.ShareNaviQuery.html" title="com.amap.api.services.share中的类">ShareSearch.ShareNaviQuery</a></dt>
<dd>
<div class="block">返回起终点信息。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/GeocodeSearch.html#getFromLocation-com.amap.api.services.geocoder.RegeocodeQuery-">getFromLocation(RegeocodeQuery)</a></span> - 类 中的方法com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/GeocodeSearch.html" title="com.amap.api.services.geocoder中的类">GeocodeSearch</a></dt>
<dd>
<div class="block">根据给定的经纬度和最大结果数返回逆地理编码的结果列表。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/GeocodeSearch.html#getFromLocationAsyn-com.amap.api.services.geocoder.RegeocodeQuery-">getFromLocationAsyn(RegeocodeQuery)</a></span> - 类 中的方法com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/GeocodeSearch.html" title="com.amap.api.services.geocoder中的类">GeocodeSearch</a></dt>
<dd>
<div class="block">逆地理编码查询的异步处理调用。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/GeocodeSearch.html#getFromLocationName-com.amap.api.services.geocoder.GeocodeQuery-">getFromLocationName(GeocodeQuery)</a></span> - 类 中的方法com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/GeocodeSearch.html" title="com.amap.api.services.geocoder中的类">GeocodeSearch</a></dt>
<dd>
<div class="block">根据给定的地理名称和查询城市，返回地理编码的结果列表。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/GeocodeSearch.html#getFromLocationNameAsyn-com.amap.api.services.geocoder.GeocodeQuery-">getFromLocationNameAsyn(GeocodeQuery)</a></span> - 类 中的方法com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/GeocodeSearch.html" title="com.amap.api.services.geocoder中的类">GeocodeSearch</a></dt>
<dd>
<div class="block">地理编码查询的异步处理调用。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/share/ShareSearch.ShareFromAndTo.html#getFromName--">getFromName()</a></span> - 类 中的方法com.amap.api.services.share.<a href="../com/amap/api/services/share/ShareSearch.ShareFromAndTo.html" title="com.amap.api.services.share中的类">ShareSearch.ShareFromAndTo</a></dt>
<dd>
<div class="block">返回起点分享名称。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/GeocodeResult.html#getGeocodeAddressList--">getGeocodeAddressList()</a></span> - 类 中的方法com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/GeocodeResult.html" title="com.amap.api.services.geocoder中的类">GeocodeResult</a></dt>
<dd>
<div class="block">返回地理编码搜索的地理结果。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/GeocodeResult.html#getGeocodeQuery--">getGeocodeQuery()</a></span> - 类 中的方法com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/GeocodeResult.html" title="com.amap.api.services.geocoder中的类">GeocodeResult</a></dt>
<dd>
<div class="block">返回该结果对应的查询参数。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiNavi.html#getGridCode--">getGridCode()</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiNavi.html" title="com.amap.api.services.poisearch中的类">PoiNavi</a></dt>
<dd>
<div class="block">返回POI的地理格ID。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/weather/LocalWeatherLive.html#getHumidity--">getHumidity()</a></span> - 类 中的方法com.amap.api.services.weather.<a href="../com/amap/api/services/weather/LocalWeatherLive.html" title="com.amap.api.services.weather中的类">LocalWeatherLive</a></dt>
<dd>
<div class="block">返回空气湿度的百分比。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/cloud/CloudImage.html#getId--">getId()</a></span> - 类 中的方法com.amap.api.services.cloud.<a href="../com/amap/api/services/cloud/CloudImage.html" title="com.amap.api.services.cloud中的类">CloudImage</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">获取cloud数据的图片id标识。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/cloud/CloudItem.html#getID--">getID()</a></span> - 类 中的方法com.amap.api.services.cloud.<a href="../com/amap/api/services/cloud/CloudItem.html" title="com.amap.api.services.cloud中的类">CloudItem</a></dt>
<dd>
<div class="block">返回 CloudItem 的 id，即其唯一标识。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/RegeocodeRoad.html#getId--">getId()</a></span> - 类 中的方法com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/RegeocodeRoad.html" title="com.amap.api.services.geocoder中的类">RegeocodeRoad</a></dt>
<dd>
<div class="block">返回道路对象中的道路ID。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/road/Road.html#getId--">getId()</a></span> - 类 中的方法com.amap.api.services.road.<a href="../com/amap/api/services/road/Road.html" title="com.amap.api.services.road中的类">Road</a></dt>
<dd>
<div class="block">返回结果的道路ID。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/Railway.html#getID--">getID()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/Railway.html" title="com.amap.api.services.route中的类">Railway</a></dt>
<dd>
<div class="block">返回线路id编号。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RailwayStationItem.html#getID--">getID()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RailwayStationItem.html" title="com.amap.api.services.route中的类">RailwayStationItem</a></dt>
<dd>
<div class="block">返回站点ID。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/routepoisearch/RoutePOIItem.html#getID--">getID()</a></span> - 类 中的方法com.amap.api.services.routepoisearch.<a href="../com/amap/api/services/routepoisearch/RoutePOIItem.html" title="com.amap.api.services.routepoisearch中的类">RoutePOIItem</a></dt>
<dd>
<div class="block">返回沿途POI的id。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/PoiItem.html#getIndoorData--">getIndoorData()</a></span> - 类 中的方法com.amap.api.services.core.<a href="../com/amap/api/services/core/PoiItem.html" title="com.amap.api.services.core中的类">PoiItem</a></dt>
<dd>
<div class="block">返回POI的室内信息，如楼层、建筑物。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/ServiceSettings.html#getInstance--">getInstance()</a></span> - 类 中的静态方法com.amap.api.services.core.<a href="../com/amap/api/services/core/ServiceSettings.html" title="com.amap.api.services.core中的类">ServiceSettings</a></dt>
<dd>
<div class="block">初始化ServiceSettings的单例对象。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/nearby/NearbySearch.html#getInstance-Context-">getInstance(Context)</a></span> - 类 中的静态方法com.amap.api.services.nearby.<a href="../com/amap/api/services/nearby/NearbySearch.html" title="com.amap.api.services.nearby中的类">NearbySearch</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">获取NearbySearch单例对象。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/DriveStep.html#getInstruction--">getInstruction()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/DriveStep.html" title="com.amap.api.services.route中的类">DriveStep</a></dt>
<dd>
<div class="block">返回驾车路段的行驶指示。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/DriveStepV2.html#getInstruction--">getInstruction()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/DriveStepV2.html" title="com.amap.api.services.route中的类">DriveStepV2</a></dt>
<dd>
<div class="block">返回驾车路段的行驶指示。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RideStep.html#getInstruction--">getInstruction()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RideStep.html" title="com.amap.api.services.route中的类">RideStep</a></dt>
<dd>
<div class="block">返回骑行路段的行进指示。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/TruckStep.html#getInstruction--">getInstruction()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/TruckStep.html" title="com.amap.api.services.route中的类">TruckStep</a></dt>
<dd>
<div class="block">行驶指示    例如：沿火器营路向南行驶112米左转</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/WalkStep.html#getInstruction--">getInstruction()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/WalkStep.html" title="com.amap.api.services.route中的类">WalkStep</a></dt>
<dd>
<div class="block">返回步行路段的行进指示。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.DrivePlanQuery.html#getInterval--">getInterval()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.DrivePlanQuery.html" title="com.amap.api.services.route中的类">RouteSearch.DrivePlanQuery</a></dt>
<dd>
<div class="block">返回规划的时间间隔</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/help/InputtipsQuery.html#getKeyword--">getKeyword()</a></span> - 类 中的方法com.amap.api.services.help.<a href="../com/amap/api/services/help/InputtipsQuery.html" title="com.amap.api.services.help中的类">InputtipsQuery</a></dt>
<dd>
<div class="block">返回查询关键字。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/district/DistrictSearchQuery.html#getKeywords--">getKeywords()</a></span> - 类 中的方法com.amap.api.services.district.<a href="../com/amap/api/services/district/DistrictSearchQuery.html" title="com.amap.api.services.district中的类">DistrictSearchQuery</a></dt>
<dd>
<div class="block">返回查询时所用字符串关键字。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/district/DistrictSearchQuery.html#getKeywordsLevel--">getKeywordsLevel()</a></span> - 类 中的方法com.amap.api.services.district.<a href="../com/amap/api/services/district/DistrictSearchQuery.html" title="com.amap.api.services.district中的类">DistrictSearchQuery</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span>
<div class="block"><span class="deprecationComment">3.6.1 不再需要此参数限制</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/ServiceSettings.html#getLanguage--">getLanguage()</a></span> - 类 中的方法com.amap.api.services.core.<a href="../com/amap/api/services/core/ServiceSettings.html" title="com.amap.api.services.core中的类">ServiceSettings</a></dt>
<dd>
<div class="block">搜索、地理/逆地理编码、输入提示的返回结果语言类型，中英文。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearch.html#getLanguage--">getLanguage()</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearch.html" title="com.amap.api.services.poisearch中的类">PoiSearch</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span>
<div class="block"><span class="deprecationComment">自V2.5.0后废弃，请参考 <a href="../com/amap/api/services/core/ServiceSettings.html#getLanguage--"><code>ServiceSettings.getLanguage()</code></a></span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/busline/BusLineItem.html#getLastBusTime--">getLastBusTime()</a></span> - 类 中的方法com.amap.api.services.busline.<a href="../com/amap/api/services/busline/BusLineItem.html" title="com.amap.api.services.busline中的类">BusLineItem</a></dt>
<dd>
<div class="block">返回公交线路的末班车时间。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/LatLonPoint.html#getLatitude--">getLatitude()</a></span> - 类 中的方法com.amap.api.services.core.<a href="../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a></dt>
<dd>
<div class="block">获取该点纬度。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/RegeocodeRoad.html#getLatLngPoint--">getLatLngPoint()</a></span> - 类 中的方法com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/RegeocodeRoad.html" title="com.amap.api.services.geocoder中的类">RegeocodeRoad</a></dt>
<dd>
<div class="block">返回道路中心点的经纬度坐标。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/busline/BusStationItem.html#getLatLonPoint--">getLatLonPoint()</a></span> - 类 中的方法com.amap.api.services.busline.<a href="../com/amap/api/services/busline/BusStationItem.html" title="com.amap.api.services.busline中的类">BusStationItem</a></dt>
<dd>
<div class="block">返回车站经纬度坐标。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/cloud/CloudItem.html#getLatLonPoint--">getLatLonPoint()</a></span> - 类 中的方法com.amap.api.services.cloud.<a href="../com/amap/api/services/cloud/CloudItem.html" title="com.amap.api.services.cloud中的类">CloudItem</a></dt>
<dd>
<div class="block">返回 CloudItem 的经纬度坐标 。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/PoiItem.html#getLatLonPoint--">getLatLonPoint()</a></span> - 类 中的方法com.amap.api.services.core.<a href="../com/amap/api/services/core/PoiItem.html" title="com.amap.api.services.core中的类">PoiItem</a></dt>
<dd>
<div class="block">返回POI的经纬度坐标<br>

 如果使用该POI进行导航时，可以检查POI是否有<a href="../com/amap/api/services/core/PoiItem.html#getExit--"><code>PoiItem.getExit()</code></a> 和 <a href="../com/amap/api/services/core/PoiItem.html#getEnter--"><code>PoiItem.getEnter()</code></a>，如果有建议使用它们作为导航的起终点。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/GeocodeAddress.html#getLatLonPoint--">getLatLonPoint()</a></span> - 类 中的方法com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/GeocodeAddress.html" title="com.amap.api.services.geocoder中的类">GeocodeAddress</a></dt>
<dd>
<div class="block">地理编码返回的经纬度坐标。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/StreetNumber.html#getLatLonPoint--">getLatLonPoint()</a></span> - 类 中的方法com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/StreetNumber.html" title="com.amap.api.services.geocoder中的类">StreetNumber</a></dt>
<dd>
<div class="block">返回门牌信息中的经纬度坐标。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/SubPoiItem.html#getLatLonPoint--">getLatLonPoint()</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/SubPoiItem.html" title="com.amap.api.services.poisearch中的类">SubPoiItem</a></dt>
<dd>
<div class="block">返回子POI的坐标。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/SubPoiItemV2.html#getLatLonPoint--">getLatLonPoint()</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/SubPoiItemV2.html" title="com.amap.api.services.poisearch中的类">SubPoiItemV2</a></dt>
<dd>
<div class="block">返回子POI的坐标。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/Doorway.html#getLatLonPoint--">getLatLonPoint()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/Doorway.html" title="com.amap.api.services.route中的类">Doorway</a></dt>
<dd>
<div class="block">返回公交换乘中换乘点的出（入）口坐标。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/RegeocodeQuery.html#getLatLonType--">getLatLonType()</a></span> - 类 中的方法com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/RegeocodeQuery.html" title="com.amap.api.services.geocoder中的类">RegeocodeQuery</a></dt>
<dd>
<div class="block">返回参数坐标类型。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/district/DistrictItem.html#getLevel--">getLevel()</a></span> - 类 中的方法com.amap.api.services.district.<a href="../com/amap/api/services/district/DistrictItem.html" title="com.amap.api.services.district中的类">DistrictItem</a></dt>
<dd>
<div class="block">返回当前行政区划的级别。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/GeocodeAddress.html#getLevel--">getLevel()</a></span> - 类 中的方法com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/GeocodeAddress.html" title="com.amap.api.services.geocoder中的类">GeocodeAddress</a></dt>
<dd>
<div class="block">地理编码返回的匹配级别。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/weather/LocalWeatherLiveResult.html#getLiveResult--">getLiveResult()</a></span> - 类 中的方法com.amap.api.services.weather.<a href="../com/amap/api/services/weather/LocalWeatherLiveResult.html" title="com.amap.api.services.weather中的类">LocalWeatherLiveResult</a></dt>
<dd>
<div class="block">返回实况天气的查询结果。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/help/InputtipsQuery.html#getLocation--">getLocation()</a></span> - 类 中的方法com.amap.api.services.help.<a href="../com/amap/api/services/help/InputtipsQuery.html" title="com.amap.api.services.help中的类">InputtipsQuery</a></dt>
<dd>
<div class="block">返回对结果进行位置限制的经纬度点</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearch.Query.html#getLocation--">getLocation()</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearch.Query.html" title="com.amap.api.services.poisearch中的类">PoiSearch.Query</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">获取设置的经纬度</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearchV2.Query.html#getLocation--">getLocation()</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearchV2.Query.html" title="com.amap.api.services.poisearch中的类">PoiSearchV2.Query</a></dt>
<dd>
<div class="block">获取设置的经纬度</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RailwayStationItem.html#getLocation--">getLocation()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RailwayStationItem.html" title="com.amap.api.services.route中的类">RailwayStationItem</a></dt>
<dd>
<div class="block">返回站点经纬度。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/GeocodeQuery.html#getLocationName--">getLocationName()</a></span> - 类 中的方法com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/GeocodeQuery.html" title="com.amap.api.services.geocoder中的类">GeocodeQuery</a></dt>
<dd>
<div class="block">返回查询地理名称。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/LatLonPoint.html#getLongitude--">getLongitude()</a></span> - 类 中的方法com.amap.api.services.core.<a href="../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a></dt>
<dd>
<div class="block">获取该点经度。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/cloud/CloudSearch.SearchBound.html#getLowerLeft--">getLowerLeft()</a></span> - 类 中的方法com.amap.api.services.cloud.<a href="../com/amap/api/services/cloud/CloudSearch.SearchBound.html" title="com.amap.api.services.cloud中的类">CloudSearch.SearchBound</a></dt>
<dd>
<div class="block">返回查询矩形的左下角坐标。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearch.SearchBound.html#getLowerLeft--">getLowerLeft()</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearch.SearchBound.html" title="com.amap.api.services.poisearch中的类">PoiSearch.SearchBound</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">返回矩形左下角坐标。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearchV2.SearchBound.html#getLowerLeft--">getLowerLeft()</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearchV2.SearchBound.html" title="com.amap.api.services.poisearch中的类">PoiSearchV2.SearchBound</a></dt>
<dd>
<div class="block">返回矩形左下角坐标。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.BusRouteQuery.html#getMaxTrans--">getMaxTrans()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.BusRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearchV2.BusRouteQuery</a></dt>
<dd>
<div class="block">最大换乘次数

 0：直达

 1：最多换乘1次

 2：最多换乘2次

 3：最多换乘3次

 4：最多换乘4次</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/RegeocodeQuery.html#getMode--">getMode()</a></span> - 类 中的方法com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/RegeocodeQuery.html" title="com.amap.api.services.geocoder中的类">RegeocodeQuery</a></dt>
<dd>
<div class="block">海外生效
 返回策略，如传入无效mode值，则走默认策略
 distance 按距离返回
 score 按权重返回</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/DistanceSearch.DistanceQuery.html#getMode--">getMode()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/DistanceSearch.DistanceQuery.html" title="com.amap.api.services.route中的类">DistanceSearch.DistanceQuery</a></dt>
<dd>
<div class="block">驾车距离测量模式， 默认是4, 参加驾车路径规划策略模式</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.BusRouteQuery.html#getMode--">getMode()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.BusRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearch.BusRouteQuery</a></dt>
<dd>
<div class="block">返回计算路径的模式。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.DrivePlanQuery.html#getMode--">getMode()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.DrivePlanQuery.html" title="com.amap.api.services.route中的类">RouteSearch.DrivePlanQuery</a></dt>
<dd>
<div class="block">返回计算路径的模式。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.DriveRouteQuery.html#getMode--">getMode()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.DriveRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearch.DriveRouteQuery</a></dt>
<dd>
<div class="block">返回计算路径的模式。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.RideRouteQuery.html#getMode--">getMode()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.RideRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearch.RideRouteQuery</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span>
<div class="block"><span class="deprecationComment">骑行路径规划升级，废弃模式选择</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.TruckRouteQuery.html#getMode--">getMode()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.TruckRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearch.TruckRouteQuery</a></dt>
<dd>
<div class="block">计算路径的模式</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.WalkRouteQuery.html#getMode--">getMode()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.WalkRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearch.WalkRouteQuery</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span>
<div class="block"><span class="deprecationComment">步行路径规划升级，废弃模式选择</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.BusRouteQuery.html#getMode--">getMode()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.BusRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearchV2.BusRouteQuery</a></dt>
<dd>
<div class="block">返回计算路径的模式。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.DriveRouteQuery.html#getMode--">getMode()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.DriveRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearchV2.DriveRouteQuery</a></dt>
<dd>
<div class="block">返回计算路径的模式。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/routepoisearch/RoutePOISearchQuery.html#getMode--">getMode()</a></span> - 类 中的方法com.amap.api.services.routepoisearch.<a href="../com/amap/api/services/routepoisearch/RoutePOISearchQuery.html" title="com.amap.api.services.routepoisearch中的类">RoutePOISearchQuery</a></dt>
<dd>
<div class="block">返回计算路径的模式。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/Business.html#getmRating--">getmRating()</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/Business.html" title="com.amap.api.services.poisearch中的类">Business</a></dt>
<dd>
<div class="block">返回POI的评分</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiItemExtension.html#getmRating--">getmRating()</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiItemExtension.html" title="com.amap.api.services.poisearch中的类">PoiItemExtension</a></dt>
<dd>
<div class="block">返回POI的评分</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/TaxiItem.html#getmSname--">getmSname()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/TaxiItem.html" title="com.amap.api.services.route中的类">TaxiItem</a></dt>
<dd>
<div class="block">获取起点名称。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/TaxiItemV2.html#getmSname--">getmSname()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/TaxiItemV2.html" title="com.amap.api.services.route中的类">TaxiItemV2</a></dt>
<dd>
<div class="block">获取起点名称。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/TaxiItem.html#getmTname--">getmTname()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/TaxiItem.html" title="com.amap.api.services.route中的类">TaxiItem</a></dt>
<dd>
<div class="block">获取终点名称。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/TaxiItemV2.html#getmTname--">getmTname()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/TaxiItemV2.html" title="com.amap.api.services.route中的类">TaxiItemV2</a></dt>
<dd>
<div class="block">获取终点名称。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.BusRouteQuery.html#getMultiExport--">getMultiExport()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.BusRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearchV2.BusRouteQuery</a></dt>
<dd>
<div class="block">地铁出入口数量

 0：只返回一个地铁出入口

 1：返回全部地铁出入口</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/district/DistrictItem.html#getName--">getName()</a></span> - 类 中的方法com.amap.api.services.district.<a href="../com/amap/api/services/district/DistrictItem.html" title="com.amap.api.services.district中的类">DistrictItem</a></dt>
<dd>
<div class="block">返回行政区域的名称。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/BusinessArea.html#getName--">getName()</a></span> - 类 中的方法com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/BusinessArea.html" title="com.amap.api.services.geocoder中的类">BusinessArea</a></dt>
<dd>
<div class="block">获取当前商圈的名字。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/RegeocodeRoad.html#getName--">getName()</a></span> - 类 中的方法com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/RegeocodeRoad.html" title="com.amap.api.services.geocoder中的类">RegeocodeRoad</a></dt>
<dd>
<div class="block">返回道路对象中的道路名称。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/help/Tip.html#getName--">getName()</a></span> - 类 中的方法com.amap.api.services.help.<a href="../com/amap/api/services/help/Tip.html" title="com.amap.api.services.help中的类">Tip</a></dt>
<dd>
<div class="block">返回提示名称。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/road/Road.html#getName--">getName()</a></span> - 类 中的方法com.amap.api.services.road.<a href="../com/amap/api/services/road/Road.html" title="com.amap.api.services.road中的类">Road</a></dt>
<dd>
<div class="block">返回结果的道路名称。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/Doorway.html#getName--">getName()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/Doorway.html" title="com.amap.api.services.route中的类">Doorway</a></dt>
<dd>
<div class="block">返回公交换乘中换乘点的出（入）口名称 。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/Railway.html#getName--">getName()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/Railway.html" title="com.amap.api.services.route中的类">Railway</a></dt>
<dd>
<div class="block">返回线路名称。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RailwayStationItem.html#getName--">getName()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RailwayStationItem.html" title="com.amap.api.services.route中的类">RailwayStationItem</a></dt>
<dd>
<div class="block">返回站点名称。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/DriveStepV2.html#getNavi--">getNavi()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/DriveStepV2.html" title="com.amap.api.services.route中的类">DriveStepV2</a></dt>
<dd>
<div class="block">获取导航信息</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/share/ShareSearch.ShareNaviQuery.html#getNaviMode--">getNaviMode()</a></span> - 类 中的方法com.amap.api.services.share.<a href="../com/amap/api/services/share/ShareSearch.ShareNaviQuery.html" title="com.amap.api.services.share中的类">ShareSearch.ShareNaviQuery</a></dt>
<dd>
<div class="block">返回导航策略。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiNavi.html#getNaviPoiID--">getNaviPoiID()</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiNavi.html" title="com.amap.api.services.poisearch中的类">PoiNavi</a></dt>
<dd>
<div class="block">获取POI导航的ID。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/nearby/NearbySearchResult.html#getNearbyInfoList--">getNearbyInfoList()</a></span> - 类 中的方法com.amap.api.services.nearby.<a href="../com/amap/api/services/nearby/NearbySearchResult.html" title="com.amap.api.services.nearby中的类">NearbySearchResult</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">附近检索结果列表。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/GeocodeAddress.html#getNeighborhood--">getNeighborhood()</a></span> - 类 中的方法com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/GeocodeAddress.html" title="com.amap.api.services.geocoder中的类">GeocodeAddress</a></dt>
<dd>
<div class="block">地理编码返回的社区名称。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/RegeocodeAddress.html#getNeighborhood--">getNeighborhood()</a></span> - 类 中的方法com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/RegeocodeAddress.html" title="com.amap.api.services.geocoder中的类">RegeocodeAddress</a></dt>
<dd>
<div class="block">逆地理编码返回的社区名称。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.DriveRouteQuery.html#getNewEnergy--">getNewEnergy()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.DriveRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearchV2.DriveRouteQuery</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.BusRouteQuery.html#getNightFlag--">getNightFlag()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.BusRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearch.BusRouteQuery</a></dt>
<dd>
<div class="block">返回是否计算夜班车。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.BusRouteQuery.html#getNightFlag--">getNightFlag()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.BusRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearchV2.BusRouteQuery</a></dt>
<dd>
<div class="block">返回是否计算夜班车。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/weather/LocalDayWeatherForecast.html#getNightTemp--">getNightTemp()</a></span> - 类 中的方法com.amap.api.services.weather.<a href="../com/amap/api/services/weather/LocalDayWeatherForecast.html" title="com.amap.api.services.weather中的类">LocalDayWeatherForecast</a></dt>
<dd>
<div class="block">返回夜间天气温度，单位：摄氏度。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/weather/LocalDayWeatherForecast.html#getNightWeather--">getNightWeather()</a></span> - 类 中的方法com.amap.api.services.weather.<a href="../com/amap/api/services/weather/LocalDayWeatherForecast.html" title="com.amap.api.services.weather中的类">LocalDayWeatherForecast</a></dt>
<dd>
<div class="block">返回夜间天气现象，如“晴”、“多云”。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/weather/LocalDayWeatherForecast.html#getNightWindDirection--">getNightWindDirection()</a></span> - 类 中的方法com.amap.api.services.weather.<a href="../com/amap/api/services/weather/LocalDayWeatherForecast.html" title="com.amap.api.services.weather中的类">LocalDayWeatherForecast</a></dt>
<dd>
<div class="block">返回夜间风向。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/weather/LocalDayWeatherForecast.html#getNightWindPower--">getNightWindPower()</a></span> - 类 中的方法com.amap.api.services.weather.<a href="../com/amap/api/services/weather/LocalDayWeatherForecast.html" title="com.amap.api.services.weather中的类">LocalDayWeatherForecast</a></dt>
<dd>
<div class="block">返回夜间风力，单位：级。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/StreetNumber.html#getNumber--">getNumber()</a></span> - 类 中的方法com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/StreetNumber.html" title="com.amap.api.services.geocoder中的类">StreetNumber</a></dt>
<dd>
<div class="block">返回门牌信息中的门牌号码。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiItemExtension.html#getOpentime--">getOpentime()</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiItemExtension.html" title="com.amap.api.services.poisearch中的类">PoiItemExtension</a></dt>
<dd>
<div class="block">返回POI的营业时间。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/Business.html#getOpentimeToday--">getOpentimeToday()</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/Business.html" title="com.amap.api.services.poisearch中的类">Business</a></dt>
<dd>
<div class="block">返回POI的今日营业时间。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/Business.html#getOpentimeWeek--">getOpentimeWeek()</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/Business.html" title="com.amap.api.services.poisearch中的类">Business</a></dt>
<dd>
<div class="block">返回POI的营业时间描述。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/DriveStep.html#getOrientation--">getOrientation()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/DriveStep.html" title="com.amap.api.services.route中的类">DriveStep</a></dt>
<dd>
<div class="block">返回驾车路段的行驶方向。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/DriveStepV2.html#getOrientation--">getOrientation()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/DriveStepV2.html" title="com.amap.api.services.route中的类">DriveStepV2</a></dt>
<dd>
<div class="block">返回驾车路段的行驶方向。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RideStep.html#getOrientation--">getOrientation()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RideStep.html" title="com.amap.api.services.route中的类">RideStep</a></dt>
<dd>
<div class="block">返回骑行路段的行进方向，方向为中文名称，如东、西南等。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/TruckStep.html#getOrientation--">getOrientation()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/TruckStep.html" title="com.amap.api.services.route中的类">TruckStep</a></dt>
<dd>
<div class="block">方向      例如：南</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/WalkStep.html#getOrientation--">getOrientation()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/WalkStep.html" title="com.amap.api.services.route中的类">WalkStep</a></dt>
<dd>
<div class="block">返回步行路段的行进方向，方向为中文名称，如东、西南等。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteBusWalkItem.html#getOrigin--">getOrigin()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteBusWalkItem.html" title="com.amap.api.services.route中的类">RouteBusWalkItem</a></dt>
<dd>
<div class="block">返回此路段步行导航信息的起点坐标。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/TaxiItem.html#getOrigin--">getOrigin()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/TaxiItem.html" title="com.amap.api.services.route中的类">TaxiItem</a></dt>
<dd>
<div class="block">获取Taxi起点坐标</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/TaxiItemV2.html#getOrigin--">getOrigin()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/TaxiItemV2.html" title="com.amap.api.services.route中的类">TaxiItemV2</a></dt>
<dd>
<div class="block">获取Taxi起点坐标</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/busline/BusLineItem.html#getOriginatingStation--">getOriginatingStation()</a></span> - 类 中的方法com.amap.api.services.busline.<a href="../com/amap/api/services/busline/BusLineItem.html" title="com.amap.api.services.busline中的类">BusLineItem</a></dt>
<dd>
<div class="block">返回公交线路的始发站名称。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/DistanceItem.html#getOriginId--">getOriginId()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/DistanceItem.html" title="com.amap.api.services.route中的类">DistanceItem</a></dt>
<dd>
<div class="block">起点坐标，起点坐标序列号（从１开始）<br>
     如传入4个起点，originId为1则表示第一个点对应的结果</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.BusRouteQuery.html#getOriginPoiId--">getOriginPoiId()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.BusRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearchV2.BusRouteQuery</a></dt>
<dd>
<div class="block">起点POI ID

 1、起点POI ID与起点经纬度均填写时，服务使用起点 POI ID；

 2、该字段必须和目的地 POI ID 成组使用。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/DistanceSearch.DistanceQuery.html#getOrigins--">getOrigins()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/DistanceSearch.DistanceQuery.html" title="com.amap.api.services.route中的类">DistanceSearch.DistanceQuery</a></dt>
<dd>
<div class="block">获取距离测量起点集合</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.FromAndTo.html#getOriginType--">getOriginType()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.FromAndTo.html" title="com.amap.api.services.route中的类">RouteSearch.FromAndTo</a></dt>
<dd>
<div class="block">返回路径规划起点的类型。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.FromAndTo.html#getOriginType--">getOriginType()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.FromAndTo.html" title="com.amap.api.services.route中的类">RouteSearchV2.FromAndTo</a></dt>
<dd>
<div class="block">返回路径规划起点的类型。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/busline/BusLineResult.html#getPageCount--">getPageCount()</a></span> - 类 中的方法com.amap.api.services.busline.<a href="../com/amap/api/services/busline/BusLineResult.html" title="com.amap.api.services.busline中的类">BusLineResult</a></dt>
<dd>
<div class="block">返回该结果的总页数。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/busline/BusStationResult.html#getPageCount--">getPageCount()</a></span> - 类 中的方法com.amap.api.services.busline.<a href="../com/amap/api/services/busline/BusStationResult.html" title="com.amap.api.services.busline中的类">BusStationResult</a></dt>
<dd>
<div class="block">返回该结果的总页数。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/cloud/CloudResult.html#getPageCount--">getPageCount()</a></span> - 类 中的方法com.amap.api.services.cloud.<a href="../com/amap/api/services/cloud/CloudResult.html" title="com.amap.api.services.cloud中的类">CloudResult</a></dt>
<dd>
<div class="block">返回搜索结果的总页数。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/district/DistrictResult.html#getPageCount--">getPageCount()</a></span> - 类 中的方法com.amap.api.services.district.<a href="../com/amap/api/services/district/DistrictResult.html" title="com.amap.api.services.district中的类">DistrictResult</a></dt>
<dd>
<div class="block">返回行政区划查询结果的页数。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiResult.html#getPageCount--">getPageCount()</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiResult.html" title="com.amap.api.services.poisearch中的类">PoiResult</a></dt>
<dd>
<div class="block">返回该结果的总页数。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/cloud/CloudSearch.Query.html#getPageNum--">getPageNum()</a></span> - 类 中的方法com.amap.api.services.cloud.<a href="../com/amap/api/services/cloud/CloudSearch.Query.html" title="com.amap.api.services.cloud中的类">CloudSearch.Query</a></dt>
<dd>
<div class="block">返回设置查询的是第几页，从0开始。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/district/DistrictSearchQuery.html#getPageNum--">getPageNum()</a></span> - 类 中的方法com.amap.api.services.district.<a href="../com/amap/api/services/district/DistrictSearchQuery.html" title="com.amap.api.services.district中的类">DistrictSearchQuery</a></dt>
<dd>
<div class="block">获取设置查询的是第几页，从0开始。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearch.Query.html#getPageNum--">getPageNum()</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearch.Query.html" title="com.amap.api.services.poisearch中的类">PoiSearch.Query</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">获取设置查询的是第几页，从0开始。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearchV2.Query.html#getPageNum--">getPageNum()</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearchV2.Query.html" title="com.amap.api.services.poisearch中的类">PoiSearchV2.Query</a></dt>
<dd>
<div class="block">获取设置查询的是第几页，从0开始。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/busline/BusLineQuery.html#getPageNumber--">getPageNumber()</a></span> - 类 中的方法com.amap.api.services.busline.<a href="../com/amap/api/services/busline/BusLineQuery.html" title="com.amap.api.services.busline中的类">BusLineQuery</a></dt>
<dd>
<div class="block">获得查询第几页的数据。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/busline/BusStationQuery.html#getPageNumber--">getPageNumber()</a></span> - 类 中的方法com.amap.api.services.busline.<a href="../com/amap/api/services/busline/BusStationQuery.html" title="com.amap.api.services.busline中的类">BusStationQuery</a></dt>
<dd>
<div class="block">获得查询第几页的数据。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/busline/BusLineQuery.html#getPageSize--">getPageSize()</a></span> - 类 中的方法com.amap.api.services.busline.<a href="../com/amap/api/services/busline/BusLineQuery.html" title="com.amap.api.services.busline中的类">BusLineQuery</a></dt>
<dd>
<div class="block">获得查询每页的结果数目。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/busline/BusStationQuery.html#getPageSize--">getPageSize()</a></span> - 类 中的方法com.amap.api.services.busline.<a href="../com/amap/api/services/busline/BusStationQuery.html" title="com.amap.api.services.busline中的类">BusStationQuery</a></dt>
<dd>
<div class="block">获得查询每页的结果数目。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/cloud/CloudSearch.Query.html#getPageSize--">getPageSize()</a></span> - 类 中的方法com.amap.api.services.cloud.<a href="../com/amap/api/services/cloud/CloudSearch.Query.html" title="com.amap.api.services.cloud中的类">CloudSearch.Query</a></dt>
<dd>
<div class="block">返回查询每页的结果数目。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/district/DistrictSearchQuery.html#getPageSize--">getPageSize()</a></span> - 类 中的方法com.amap.api.services.district.<a href="../com/amap/api/services/district/DistrictSearchQuery.html" title="com.amap.api.services.district中的类">DistrictSearchQuery</a></dt>
<dd>
<div class="block">返回查询页面的结果数目。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearch.Query.html#getPageSize--">getPageSize()</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearch.Query.html" title="com.amap.api.services.poisearch中的类">PoiSearch.Query</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">获取设置的查询页面的结果数目。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearchV2.Query.html#getPageSize--">getPageSize()</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearchV2.Query.html" title="com.amap.api.services.poisearch中的类">PoiSearchV2.Query</a></dt>
<dd>
<div class="block">获取设置的查询页面的结果数目。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/PoiItem.html#getParkingType--">getParkingType()</a></span> - 类 中的方法com.amap.api.services.core.<a href="../com/amap/api/services/core/PoiItem.html" title="com.amap.api.services.core中的类">PoiItem</a></dt>
<dd>
<div class="block">返回POI的停车场类型</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/Business.html#getParkingType--">getParkingType()</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/Business.html" title="com.amap.api.services.poisearch中的类">Business</a></dt>
<dd>
<div class="block">返回POI的停车场类型</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.DriveRouteQuery.html#getPassedByPoints--">getPassedByPoints()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.DriveRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearch.DriveRouteQuery</a></dt>
<dd>
<div class="block">返回设定查询的途经点。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.DriveRouteQuery.html#getPassedByPoints--">getPassedByPoints()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.DriveRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearchV2.DriveRouteQuery</a></dt>
<dd>
<div class="block">返回设定查询的途经点。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.DriveRouteQuery.html#getPassedPointStr--">getPassedPointStr()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.DriveRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearch.DriveRouteQuery</a></dt>
<dd>
<div class="block">将途径点位置坐标转换为字符串输出。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.TruckRouteQuery.html#getPassedPointStr--">getPassedPointStr()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.TruckRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearch.TruckRouteQuery</a></dt>
<dd>
<div class="block">将途径点位置坐标转换为字符串输出。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.DriveRouteQuery.html#getPassedPointStr--">getPassedPointStr()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.DriveRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearchV2.DriveRouteQuery</a></dt>
<dd>
<div class="block">将途径点位置坐标转换为字符串输出。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteBusLineItem.html#getPassStationNum--">getPassStationNum()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteBusLineItem.html" title="com.amap.api.services.route中的类">RouteBusLineItem</a></dt>
<dd>
<div class="block">返回此公交换乘路段经过的站点数目（除出发站、到达站）。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteBusLineItem.html#getPassStations--">getPassStations()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteBusLineItem.html" title="com.amap.api.services.route中的类">RouteBusLineItem</a></dt>
<dd>
<div class="block">返回此公交换乘路段经过的站点名称 。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/TimeInfosElement.html#getPathindex--">getPathindex()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/TimeInfosElement.html" title="com.amap.api.services.route中的类">TimeInfosElement</a></dt>
<dd>
<div class="block">返回驾车未来路径规划路线索引。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/BusRouteResult.html#getPaths--">getPaths()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/BusRouteResult.html" title="com.amap.api.services.route中的类">BusRouteResult</a></dt>
<dd>
<div class="block">返回公交路径规划方案。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/BusRouteResultV2.html#getPaths--">getPaths()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/BusRouteResultV2.html" title="com.amap.api.services.route中的类">BusRouteResultV2</a></dt>
<dd>
<div class="block">返回公交路径规划方案。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/DriveRoutePlanResult.html#getPaths--">getPaths()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/DriveRoutePlanResult.html" title="com.amap.api.services.route中的类">DriveRoutePlanResult</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/DriveRouteResult.html#getPaths--">getPaths()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/DriveRouteResult.html" title="com.amap.api.services.route中的类">DriveRouteResult</a></dt>
<dd>
<div class="block">返回驾车路径规划方案。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/DriveRouteResultV2.html#getPaths--">getPaths()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/DriveRouteResultV2.html" title="com.amap.api.services.route中的类">DriveRouteResultV2</a></dt>
<dd>
<div class="block">返回驾车路径规划方案。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RideRouteResult.html#getPaths--">getPaths()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RideRouteResult.html" title="com.amap.api.services.route中的类">RideRouteResult</a></dt>
<dd>
<div class="block">返回骑行路径规划方案。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RideRouteResultV2.html#getPaths--">getPaths()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RideRouteResultV2.html" title="com.amap.api.services.route中的类">RideRouteResultV2</a></dt>
<dd>
<div class="block">返回骑行路径规划方案。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/TruckRouteRestult.html#getPaths--">getPaths()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/TruckRouteRestult.html" title="com.amap.api.services.route中的类">TruckRouteRestult</a></dt>
<dd>
<div class="block">里面包含具体货车路线的方案</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/WalkRouteResult.html#getPaths--">getPaths()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/WalkRouteResult.html" title="com.amap.api.services.route中的类">WalkRouteResult</a></dt>
<dd>
<div class="block">返回步行路径规划方案。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/WalkRouteResultV2.html#getPaths--">getPaths()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/WalkRouteResultV2.html" title="com.amap.api.services.route中的类">WalkRouteResultV2</a></dt>
<dd>
<div class="block">返回步行路径规划方案。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/PoiItem.html#getPhotos--">getPhotos()</a></span> - 类 中的方法com.amap.api.services.core.<a href="../com/amap/api/services/core/PoiItem.html" title="com.amap.api.services.core中的类">PoiItem</a></dt>
<dd>
<div class="block">返回POI的图片信息。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/SearchUtils.html#getPkgName-Context-">getPkgName(Context)</a></span> - 类 中的静态方法com.amap.api.services.core.<a href="../com/amap/api/services/core/SearchUtils.html" title="com.amap.api.services.core中的类">SearchUtils</a></dt>
<dd>
<div class="block">获取包名。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.FromAndTo.html#getPlateNumber--">getPlateNumber()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.FromAndTo.html" title="com.amap.api.services.route中的类">RouteSearch.FromAndTo</a></dt>
<dd>
<div class="block">获取车牌号</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.FromAndTo.html#getPlateNumber--">getPlateNumber()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.FromAndTo.html" title="com.amap.api.services.route中的类">RouteSearchV2.FromAndTo</a></dt>
<dd>
<div class="block">获取车牌号</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.FromAndTo.html#getPlateProvince--">getPlateProvince()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.FromAndTo.html" title="com.amap.api.services.route中的类">RouteSearch.FromAndTo</a></dt>
<dd>
<div class="block">获取车牌省份</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/PoiItem.html#getPoiExtension--">getPoiExtension()</a></span> - 类 中的方法com.amap.api.services.core.<a href="../com/amap/api/services/core/PoiItem.html" title="com.amap.api.services.core中的类">PoiItem</a></dt>
<dd>
<div class="block">返回POI的扩展信息。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/PoiItem.html#getPoiId--">getPoiId()</a></span> - 类 中的方法com.amap.api.services.core.<a href="../com/amap/api/services/core/PoiItem.html" title="com.amap.api.services.core中的类">PoiItem</a></dt>
<dd>
<div class="block">返回POI 的id，即其唯一标识。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/help/Tip.html#getPoiID--">getPoiID()</a></span> - 类 中的方法com.amap.api.services.help.<a href="../com/amap/api/services/help/Tip.html" title="com.amap.api.services.help中的类">Tip</a></dt>
<dd>
<div class="block">获取Poi的ID，如果不存在id则为空。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/IndoorData.html#getPoiId--">getPoiId()</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/IndoorData.html" title="com.amap.api.services.poisearch中的类">IndoorData</a></dt>
<dd>
<div class="block">返回该POI所在建筑物POI的id。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/IndoorDataV2.html#getPoiId--">getPoiId()</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/IndoorDataV2.html" title="com.amap.api.services.poisearch中的类">IndoorDataV2</a></dt>
<dd>
<div class="block">返回该POI所在建筑物POI的id。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/SubPoiItem.html#getPoiId--">getPoiId()</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/SubPoiItem.html" title="com.amap.api.services.poisearch中的类">SubPoiItem</a></dt>
<dd>
<div class="block">返回子POI 的id，即其唯一标识。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/SubPoiItemV2.html#getPoiId--">getPoiId()</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/SubPoiItemV2.html" title="com.amap.api.services.poisearch中的类">SubPoiItemV2</a></dt>
<dd>
<div class="block">返回子POI 的id，即其唯一标识。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/RegeocodeQuery.html#getPoint--">getPoint()</a></span> - 类 中的方法com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/RegeocodeQuery.html" title="com.amap.api.services.geocoder中的类">RegeocodeQuery</a></dt>
<dd>
<div class="block">返回逆地理编码的地理坐标点。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/help/Tip.html#getPoint--">getPoint()</a></span> - 类 中的方法com.amap.api.services.help.<a href="../com/amap/api/services/help/Tip.html" title="com.amap.api.services.help中的类">Tip</a></dt>
<dd>
<div class="block">获取Poi的经纬度，如果不存在经纬度则为空。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/nearby/NearbyInfo.html#getPoint--">getPoint()</a></span> - 类 中的方法com.amap.api.services.nearby.<a href="../com/amap/api/services/nearby/NearbyInfo.html" title="com.amap.api.services.nearby中的类">NearbyInfo</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">获取用户位置信息。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/nearby/UploadInfo.html#getPoint--">getPoint()</a></span> - 类 中的方法com.amap.api.services.nearby.<a href="../com/amap/api/services/nearby/UploadInfo.html" title="com.amap.api.services.nearby中的类">UploadInfo</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">获取用户位置信息。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/routepoisearch/RoutePOIItem.html#getPoint--">getPoint()</a></span> - 类 中的方法com.amap.api.services.routepoisearch.<a href="../com/amap/api/services/routepoisearch/RoutePOIItem.html" title="com.amap.api.services.routepoisearch中的类">RoutePOIItem</a></dt>
<dd>
<div class="block">返回沿途POI的位置。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/RegeocodeAddress.html#getPois--">getPois()</a></span> - 类 中的方法com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/RegeocodeAddress.html" title="com.amap.api.services.geocoder中的类">RegeocodeAddress</a></dt>
<dd>
<div class="block">逆地理编码返回的POI(兴趣点)列表。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiResult.html#getPois--">getPois()</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiResult.html" title="com.amap.api.services.poisearch中的类">PoiResult</a></dt>
<dd>
<div class="block">返回当前页所有POI结果。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiResultV2.html#getPois--">getPois()</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiResultV2.html" title="com.amap.api.services.poisearch中的类">PoiResultV2</a></dt>
<dd>
<div class="block">返回当前页所有POI结果。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/RegeocodeQuery.html#getPoiType--">getPoiType()</a></span> - 类 中的方法com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/RegeocodeQuery.html" title="com.amap.api.services.geocoder中的类">RegeocodeQuery</a></dt>
<dd>
<div class="block">返回设置的附近POI类型。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/cloud/CloudSearch.SearchBound.html#getPolyGonList--">getPolyGonList()</a></span> - 类 中的方法com.amap.api.services.cloud.<a href="../com/amap/api/services/cloud/CloudSearch.SearchBound.html" title="com.amap.api.services.cloud中的类">CloudSearch.SearchBound</a></dt>
<dd>
<div class="block">返回查询多边形的坐标点。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearch.SearchBound.html#getPolyGonList--">getPolyGonList()</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearch.SearchBound.html" title="com.amap.api.services.poisearch中的类">PoiSearch.SearchBound</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">返回首尾相接的几何点，可以组成多边形。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearchV2.SearchBound.html#getPolyGonList--">getPolyGonList()</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearchV2.SearchBound.html" title="com.amap.api.services.poisearch中的类">PoiSearchV2.SearchBound</a></dt>
<dd>
<div class="block">返回首尾相接的几何点，可以组成多边形。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/DrivePlanStep.html#getPolyline--">getPolyline()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/DrivePlanStep.html" title="com.amap.api.services.route中的类">DrivePlanStep</a></dt>
<dd>
<div class="block">返回驾车路段的坐标点集合。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/DriveStep.html#getPolyline--">getPolyline()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/DriveStep.html" title="com.amap.api.services.route中的类">DriveStep</a></dt>
<dd>
<div class="block">返回驾车路段的坐标点集合。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/DriveStepV2.html#getPolyline--">getPolyline()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/DriveStepV2.html" title="com.amap.api.services.route中的类">DriveStepV2</a></dt>
<dd>
<div class="block">返回驾车路段的坐标点集合。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/Path.html#getPolyline--">getPolyline()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/Path.html" title="com.amap.api.services.route中的类">Path</a></dt>
<dd>
<div class="block">返回路线的坐标点集合。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RideStep.html#getPolyline--">getPolyline()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RideStep.html" title="com.amap.api.services.route中的类">RideStep</a></dt>
<dd>
<div class="block">返回骑行路段的坐标点集合。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteBusLineItem.html#getPolyline--">getPolyline()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteBusLineItem.html" title="com.amap.api.services.route中的类">RouteBusLineItem</a></dt>
<dd>
<div class="block">返回此公交换乘路段（出发站-到达站）的坐标点集合。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/TaxiItemV2.html#getPolyline--">getPolyline()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/TaxiItemV2.html" title="com.amap.api.services.route中的类">TaxiItemV2</a></dt>
<dd>
<div class="block">线路点的集合</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/TMC.html#getPolyline--">getPolyline()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/TMC.html" title="com.amap.api.services.route中的类">TMC</a></dt>
<dd>
<div class="block">获取本段路况的坐标点集合。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/TruckStep.html#getPolyline--">getPolyline()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/TruckStep.html" title="com.amap.api.services.route中的类">TruckStep</a></dt>
<dd>
<div class="block">此路段坐标点串 存放到 List中</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/WalkStep.html#getPolyline--">getPolyline()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/WalkStep.html" title="com.amap.api.services.route中的类">WalkStep</a></dt>
<dd>
<div class="block">返回步行路段的坐标点集合。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/routepoisearch/RoutePOISearchQuery.html#getPolylines--">getPolylines()</a></span> - 类 中的方法com.amap.api.services.routepoisearch.<a href="../com/amap/api/services/routepoisearch/RoutePOISearchQuery.html" title="com.amap.api.services.routepoisearch中的类">RoutePOISearchQuery</a></dt>
<dd>
<div class="block">返回沿途搜索设置的坐标点集合。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/PoiItem.html#getPostcode--">getPostcode()</a></span> - 类 中的方法com.amap.api.services.core.<a href="../com/amap/api/services/core/PoiItem.html" title="com.amap.api.services.core中的类">PoiItem</a></dt>
<dd>
<div class="block">返回POI的邮编。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/GeocodeAddress.html#getPostcode--">getPostcode()</a></span> - 类 中的方法com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/GeocodeAddress.html" title="com.amap.api.services.geocoder中的类">GeocodeAddress</a></dt>
<dd>
<div class="block">海外生效
 邮政编码</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/cloud/CloudImage.html#getPreurl--">getPreurl()</a></span> - 类 中的方法com.amap.api.services.cloud.<a href="../com/amap/api/services/cloud/CloudImage.html" title="com.amap.api.services.cloud中的类">CloudImage</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">获取到cloud数据的经过压缩处理的图片地址，尺寸为400*400，若期望获取体积较小的图片文件，建议使用此地址。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/TaxiItemV2.html#getPrice--">getPrice()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/TaxiItemV2.html" title="com.amap.api.services.route中的类">TaxiItemV2</a></dt>
<dd>
<div class="block">打车花费</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/ServiceSettings.html#getProtocol--">getProtocol()</a></span> - 类 中的方法com.amap.api.services.core.<a href="../com/amap/api/services/core/ServiceSettings.html" title="com.amap.api.services.core中的类">ServiceSettings</a></dt>
<dd>
<div class="block">返回访问使用的协议类别</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/GeocodeAddress.html#getProvince--">getProvince()</a></span> - 类 中的方法com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/GeocodeAddress.html" title="com.amap.api.services.geocoder中的类">GeocodeAddress</a></dt>
<dd>
<div class="block">地理编码返回的所在省名称、直辖市的名称 。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/RegeocodeAddress.html#getProvince--">getProvince()</a></span> - 类 中的方法com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/RegeocodeAddress.html" title="com.amap.api.services.geocoder中的类">RegeocodeAddress</a></dt>
<dd>
<div class="block">逆地理编码返回的所在省名称、直辖市的名称 。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/weather/LocalWeatherForecast.html#getProvince--">getProvince()</a></span> - 类 中的方法com.amap.api.services.weather.<a href="../com/amap/api/services/weather/LocalWeatherForecast.html" title="com.amap.api.services.weather中的类">LocalWeatherForecast</a></dt>
<dd>
<div class="block">返回省份名称。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/weather/LocalWeatherLive.html#getProvince--">getProvince()</a></span> - 类 中的方法com.amap.api.services.weather.<a href="../com/amap/api/services/weather/LocalWeatherLive.html" title="com.amap.api.services.weather中的类">LocalWeatherLive</a></dt>
<dd>
<div class="block">返回省份名称。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/PoiItem.html#getProvinceCode--">getProvinceCode()</a></span> - 类 中的方法com.amap.api.services.core.<a href="../com/amap/api/services/core/PoiItem.html" title="com.amap.api.services.core中的类">PoiItem</a></dt>
<dd>
<div class="block">返回 POI 的省/自治区/直辖市/特别行政区编码。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/PoiItem.html#getProvinceName--">getProvinceName()</a></span> - 类 中的方法com.amap.api.services.core.<a href="../com/amap/api/services/core/PoiItem.html" title="com.amap.api.services.core中的类">PoiItem</a></dt>
<dd>
<div class="block">返回POI的省/自治区/直辖市/特别行政区名称 。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/busline/BusLineResult.html#getQuery--">getQuery()</a></span> - 类 中的方法com.amap.api.services.busline.<a href="../com/amap/api/services/busline/BusLineResult.html" title="com.amap.api.services.busline中的类">BusLineResult</a></dt>
<dd>
<div class="block">返回该结果对应的查询参数。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/busline/BusLineSearch.html#getQuery--">getQuery()</a></span> - 类 中的方法com.amap.api.services.busline.<a href="../com/amap/api/services/busline/BusLineSearch.html" title="com.amap.api.services.busline中的类">BusLineSearch</a></dt>
<dd>
<div class="block">返回查询条件。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/busline/BusStationResult.html#getQuery--">getQuery()</a></span> - 类 中的方法com.amap.api.services.busline.<a href="../com/amap/api/services/busline/BusStationResult.html" title="com.amap.api.services.busline中的类">BusStationResult</a></dt>
<dd>
<div class="block">返回该结果对应的查询参数。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/busline/BusStationSearch.html#getQuery--">getQuery()</a></span> - 类 中的方法com.amap.api.services.busline.<a href="../com/amap/api/services/busline/BusStationSearch.html" title="com.amap.api.services.busline中的类">BusStationSearch</a></dt>
<dd>
<div class="block">返回查询条件。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/cloud/CloudResult.html#getQuery--">getQuery()</a></span> - 类 中的方法com.amap.api.services.cloud.<a href="../com/amap/api/services/cloud/CloudResult.html" title="com.amap.api.services.cloud中的类">CloudResult</a></dt>
<dd>
<div class="block">返回搜索结果对应的查询参数。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/district/DistrictResult.html#getQuery--">getQuery()</a></span> - 类 中的方法com.amap.api.services.district.<a href="../com/amap/api/services/district/DistrictResult.html" title="com.amap.api.services.district中的类">DistrictResult</a></dt>
<dd>
<div class="block">返回查询结果对应的查询参数。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/district/DistrictSearch.html#getQuery--">getQuery()</a></span> - 类 中的方法com.amap.api.services.district.<a href="../com/amap/api/services/district/DistrictSearch.html" title="com.amap.api.services.district中的类">DistrictSearch</a></dt>
<dd>
<div class="block">返回查询条件。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/help/Inputtips.html#getQuery--">getQuery()</a></span> - 类 中的方法com.amap.api.services.help.<a href="../com/amap/api/services/help/Inputtips.html" title="com.amap.api.services.help中的类">Inputtips</a></dt>
<dd>
<div class="block">返回提示查询条件。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiResult.html#getQuery--">getQuery()</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiResult.html" title="com.amap.api.services.poisearch中的类">PoiResult</a></dt>
<dd>
<div class="block">返回该结果对应的查询参数。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiResultV2.html#getQuery--">getQuery()</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiResultV2.html" title="com.amap.api.services.poisearch中的类">PoiResultV2</a></dt>
<dd>
<div class="block">返回该结果对应的查询参数。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearch.html#getQuery--">getQuery()</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearch.html" title="com.amap.api.services.poisearch中的类">PoiSearch</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">返回查询条件。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearchV2.html#getQuery--">getQuery()</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearchV2.html" title="com.amap.api.services.poisearch中的类">PoiSearchV2</a></dt>
<dd>
<div class="block">返回查询条件。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/routepoisearch/RoutePOISearchResult.html#getQuery--">getQuery()</a></span> - 类 中的方法com.amap.api.services.routepoisearch.<a href="../com/amap/api/services/routepoisearch/RoutePOISearchResult.html" title="com.amap.api.services.routepoisearch中的类">RoutePOISearchResult</a></dt>
<dd>
<div class="block">返回查询条件。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/weather/WeatherSearch.html#getQuery--">getQuery()</a></span> - 类 中的方法com.amap.api.services.weather.<a href="../com/amap/api/services/weather/WeatherSearch.html" title="com.amap.api.services.weather中的类">WeatherSearch</a></dt>
<dd>
<div class="block">返回天气查询条件。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearch.Query.html#getQueryLanguage--">getQueryLanguage()</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearch.Query.html" title="com.amap.api.services.poisearch中的类">PoiSearch.Query</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearchV2.Query.html#getQueryLanguage--">getQueryLanguage()</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearchV2.Query.html" title="com.amap.api.services.poisearch中的类">PoiSearchV2.Query</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/busline/BusLineQuery.html#getQueryString--">getQueryString()</a></span> - 类 中的方法com.amap.api.services.busline.<a href="../com/amap/api/services/busline/BusLineQuery.html" title="com.amap.api.services.busline中的类">BusLineQuery</a></dt>
<dd>
<div class="block">返回查询关键字。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/busline/BusStationQuery.html#getQueryString--">getQueryString()</a></span> - 类 中的方法com.amap.api.services.busline.<a href="../com/amap/api/services/busline/BusStationQuery.html" title="com.amap.api.services.busline中的类">BusStationQuery</a></dt>
<dd>
<div class="block">返回查询关键字。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/cloud/CloudSearch.Query.html#getQueryString--">getQueryString()</a></span> - 类 中的方法com.amap.api.services.cloud.<a href="../com/amap/api/services/cloud/CloudSearch.Query.html" title="com.amap.api.services.cloud中的类">CloudSearch.Query</a></dt>
<dd>
<div class="block">返回搜索的关键词。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearch.Query.html#getQueryString--">getQueryString()</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearch.Query.html" title="com.amap.api.services.poisearch中的类">PoiSearch.Query</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">返回待查字符串。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearchV2.Query.html#getQueryString--">getQueryString()</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearchV2.Query.html" title="com.amap.api.services.poisearch中的类">PoiSearchV2.Query</a></dt>
<dd>
<div class="block">返回待查字符串。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/RegeocodeQuery.html#getRadius--">getRadius()</a></span> - 类 中的方法com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/RegeocodeQuery.html" title="com.amap.api.services.geocoder中的类">RegeocodeQuery</a></dt>
<dd>
<div class="block">返回查找范围。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/nearby/NearbySearch.NearbyQuery.html#getRadius--">getRadius()</a></span> - 类 中的方法com.amap.api.services.nearby.<a href="../com/amap/api/services/nearby/NearbySearch.NearbyQuery.html" title="com.amap.api.services.nearby中的类">NearbySearch.NearbyQuery</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">返回检索的半径。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/BusStep.html#getRailway--">getRailway()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/BusStep.html" title="com.amap.api.services.route中的类">BusStep</a></dt>
<dd>
<div class="block">返回此路段的火车乘坐信息。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/BusStepV2.html#getRailway--">getRailway()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/BusStepV2.html" title="com.amap.api.services.route中的类">BusStepV2</a></dt>
<dd>
<div class="block">返回此路段的火车乘坐信息。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/cloud/CloudSearch.SearchBound.html#getRange--">getRange()</a></span> - 类 中的方法com.amap.api.services.cloud.<a href="../com/amap/api/services/cloud/CloudSearch.SearchBound.html" title="com.amap.api.services.cloud中的类">CloudSearch.SearchBound</a></dt>
<dd>
<div class="block">返回查询圆形的范围（半径）。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearch.SearchBound.html#getRange--">getRange()</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearch.SearchBound.html" title="com.amap.api.services.poisearch中的类">PoiSearch.SearchBound</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">返回查询范围半径。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearchV2.SearchBound.html#getRange--">getRange()</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearchV2.SearchBound.html" title="com.amap.api.services.poisearch中的类">PoiSearchV2.SearchBound</a></dt>
<dd>
<div class="block">返回查询范围半径。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/routepoisearch/RoutePOISearchQuery.html#getRange--">getRange()</a></span> - 类 中的方法com.amap.api.services.routepoisearch.<a href="../com/amap/api/services/routepoisearch/RoutePOISearchQuery.html" title="com.amap.api.services.routepoisearch中的类">RoutePOISearchQuery</a></dt>
<dd>
<div class="block">返回道路周围搜索的范围。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/RegeocodeResult.html#getRegeocodeAddress--">getRegeocodeAddress()</a></span> - 类 中的方法com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/RegeocodeResult.html" title="com.amap.api.services.geocoder中的类">RegeocodeResult</a></dt>
<dd>
<div class="block">返回逆地理编码搜索的地理结果。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/RegeocodeResult.html#getRegeocodeQuery--">getRegeocodeQuery()</a></span> - 类 中的方法com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/RegeocodeResult.html" title="com.amap.api.services.geocoder中的类">RegeocodeResult</a></dt>
<dd>
<div class="block">返回该结果对应的查询参数。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/weather/LocalWeatherForecast.html#getReportTime--">getReportTime()</a></span> - 类 中的方法com.amap.api.services.weather.<a href="../com/amap/api/services/weather/LocalWeatherForecast.html" title="com.amap.api.services.weather中的类">LocalWeatherForecast</a></dt>
<dd>
<div class="block">返回天气预报发布时间。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/weather/LocalWeatherLive.html#getReportTime--">getReportTime()</a></span> - 类 中的方法com.amap.api.services.weather.<a href="../com/amap/api/services/weather/LocalWeatherLive.html" title="com.amap.api.services.weather中的类">LocalWeatherLive</a></dt>
<dd>
<div class="block">返回实时数据发布时间。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/DrivePath.html#getRestriction--">getRestriction()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/DrivePath.html" title="com.amap.api.services.route中的类">DrivePath</a></dt>
<dd>
<div class="block">返回驾车规划方案的限行结果</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/DrivePathV2.html#getRestriction--">getRestriction()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/DrivePathV2.html" title="com.amap.api.services.route中的类">DrivePathV2</a></dt>
<dd>
<div class="block">返回驾车规划方案的限行结果</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/TimeInfosElement.html#getRestriction--">getRestriction()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/TimeInfosElement.html" title="com.amap.api.services.route中的类">TimeInfosElement</a></dt>
<dd>
<div class="block">返回驾车未来路径规划路线是否有限行路段。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/TruckPath.html#getRestriction--">getRestriction()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/TruckPath.html" title="com.amap.api.services.route中的类">TruckPath</a></dt>
<dd>
<div class="block">限行结果
 0，未知（未输入完整/正确车牌号信息时候显示）
 1，已规避限行
 2，起点限行
 3，途径点在限行区域内（设置途径点才出现此报错）
 4，途径限行区域
 5，终点限行</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RideRouteResult.html#getRideQuery--">getRideQuery()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RideRouteResult.html" title="com.amap.api.services.route中的类">RideRouteResult</a></dt>
<dd>
<div class="block">返回该结果对应的查询参数。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RideRouteResultV2.html#getRideQuery--">getRideQuery()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RideRouteResultV2.html" title="com.amap.api.services.route中的类">RideRouteResultV2</a></dt>
<dd>
<div class="block">返回该结果对应的查询参数。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/DrivePlanStep.html#getRoad--">getRoad()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/DrivePlanStep.html" title="com.amap.api.services.route中的类">DrivePlanStep</a></dt>
<dd>
<div class="block">返回驾车路段的道路名称。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/DriveStep.html#getRoad--">getRoad()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/DriveStep.html" title="com.amap.api.services.route中的类">DriveStep</a></dt>
<dd>
<div class="block">返回驾车路段的道路名称。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/DriveStepV2.html#getRoad--">getRoad()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/DriveStepV2.html" title="com.amap.api.services.route中的类">DriveStepV2</a></dt>
<dd>
<div class="block">返回驾车路段的道路名称。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RideStep.html#getRoad--">getRoad()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RideStep.html" title="com.amap.api.services.route中的类">RideStep</a></dt>
<dd>
<div class="block">返回骑行路段的道路名称。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/TruckStep.html#getRoad--">getRoad()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/TruckStep.html" title="com.amap.api.services.route中的类">TruckStep</a></dt>
<dd>
<div class="block">道路名称    例如：火器营路</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/WalkStep.html#getRoad--">getRoad()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/WalkStep.html" title="com.amap.api.services.route中的类">WalkStep</a></dt>
<dd>
<div class="block">返回步行路段的道路名称。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/RegeocodeAddress.html#getRoads--">getRoads()</a></span> - 类 中的方法com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/RegeocodeAddress.html" title="com.amap.api.services.geocoder中的类">RegeocodeAddress</a></dt>
<dd>
<div class="block">逆地理编码返回的道路列表。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/NaviWalkType.html#getRoadType--">getRoadType()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/NaviWalkType.html" title="com.amap.api.services.route中的类">NaviWalkType</a></dt>
<dd>
<div class="block">获取道路类型</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RideStep.html#getRoadType--">getRoadType()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RideStep.html" title="com.amap.api.services.route中的类">RideStep</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/WalkStep.html#getRoadType--">getRoadType()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/WalkStep.html" title="com.amap.api.services.route中的类">WalkStep</a></dt>
<dd>
<div class="block">0，普通道路 1，人行横道 3，地下通道 4，过街天桥

 5，地铁通道 6，公园 7，广场 8，扶梯 9，直梯

 10，索道 11，空中通道 12，建筑物穿越通道

 13，行人通道 14，游船路线 15，观光车路线 16，滑道

 18，扩路 19，道路附属连接线 20，阶梯 21，斜坡

 22，桥 23，隧道 30，轮渡</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/road/Road.html#getRoadWidth--">getRoadWidth()</a></span> - 类 中的方法com.amap.api.services.road.<a href="../com/amap/api/services/road/Road.html" title="com.amap.api.services.road中的类">Road</a></dt>
<dd>
<div class="block">返回结果的道路宽度，单位米。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/routepoisearch/RoutePOISearchResult.html#getRoutePois--">getRoutePois()</a></span> - 类 中的方法com.amap.api.services.routepoisearch.<a href="../com/amap/api/services/routepoisearch/RoutePOISearchResult.html" title="com.amap.api.services.routepoisearch中的类">RoutePOISearchResult</a></dt>
<dd>
<div class="block">获取RoutePOI列表。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/DriveStep.html#getRouteSearchCityList--">getRouteSearchCityList()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/DriveStep.html" title="com.amap.api.services.route中的类">DriveStep</a></dt>
<dd>
<div class="block">得到搜索返回的路径规划途径城市和行政区。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/DriveStepV2.html#getRouteSearchCityList--">getRouteSearchCityList()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/DriveStepV2.html" title="com.amap.api.services.route中的类">DriveStepV2</a></dt>
<dd>
<div class="block">得到搜索返回的路径规划途径城市和行政区。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/TruckStep.html#getRouteSearchCityList--">getRouteSearchCityList()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/TruckStep.html" title="com.amap.api.services.route中的类">TruckStep</a></dt>
<dd>
<div class="block">途径城市列表</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/SearchCity.html#getSearchCityAdCode--">getSearchCityAdCode()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/SearchCity.html" title="com.amap.api.services.route中的类">SearchCity</a></dt>
<dd>
<div class="block">得到城市的行政编码。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/SearchCity.html#getSearchCitycode--">getSearchCitycode()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/SearchCity.html" title="com.amap.api.services.route中的类">SearchCity</a></dt>
<dd>
<div class="block">得到城市的编码。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/SearchCity.html#getSearchCityName--">getSearchCityName()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/SearchCity.html" title="com.amap.api.services.route中的类">SearchCity</a></dt>
<dd>
<div class="block">得到城市的名称。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/busline/BusLineResult.html#getSearchSuggestionCities--">getSearchSuggestionCities()</a></span> - 类 中的方法com.amap.api.services.busline.<a href="../com/amap/api/services/busline/BusLineResult.html" title="com.amap.api.services.busline中的类">BusLineResult</a></dt>
<dd>
<div class="block">返回建议的城市。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/busline/BusStationResult.html#getSearchSuggestionCities--">getSearchSuggestionCities()</a></span> - 类 中的方法com.amap.api.services.busline.<a href="../com/amap/api/services/busline/BusStationResult.html" title="com.amap.api.services.busline中的类">BusStationResult</a></dt>
<dd>
<div class="block">返回建议的城市。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiResult.html#getSearchSuggestionCitys--">getSearchSuggestionCitys()</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiResult.html" title="com.amap.api.services.poisearch中的类">PoiResult</a></dt>
<dd>
<div class="block">返回建议的城市。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/busline/BusLineResult.html#getSearchSuggestionKeywords--">getSearchSuggestionKeywords()</a></span> - 类 中的方法com.amap.api.services.busline.<a href="../com/amap/api/services/busline/BusLineResult.html" title="com.amap.api.services.busline中的类">BusLineResult</a></dt>
<dd>
<div class="block">返回搜索建议。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/busline/BusStationResult.html#getSearchSuggestionKeywords--">getSearchSuggestionKeywords()</a></span> - 类 中的方法com.amap.api.services.busline.<a href="../com/amap/api/services/busline/BusStationResult.html" title="com.amap.api.services.busline中的类">BusStationResult</a></dt>
<dd>
<div class="block">返回搜索建议。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiResult.html#getSearchSuggestionKeywords--">getSearchSuggestionKeywords()</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiResult.html" title="com.amap.api.services.poisearch中的类">PoiResult</a></dt>
<dd>
<div class="block">返回搜索建议。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/routepoisearch/RoutePOISearchQuery.html#getSearchType--">getSearchType()</a></span> - 类 中的方法com.amap.api.services.routepoisearch.<a href="../com/amap/api/services/routepoisearch/RoutePOISearchQuery.html" title="com.amap.api.services.routepoisearch中的类">RoutePOISearchQuery</a></dt>
<dd>
<div class="block">返回沿途搜索的POI类型。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/road/Crossroad.html#getSecondRoadId--">getSecondRoadId()</a></span> - 类 中的方法com.amap.api.services.road.<a href="../com/amap/api/services/road/Crossroad.html" title="com.amap.api.services.road中的类">Crossroad</a></dt>
<dd>
<div class="block">返回交叉路口的第二条道路ID。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/road/Crossroad.html#getSecondRoadName--">getSecondRoadName()</a></span> - 类 中的方法com.amap.api.services.road.<a href="../com/amap/api/services/road/Crossroad.html" title="com.amap.api.services.road中的类">Crossroad</a></dt>
<dd>
<div class="block">返回交叉路口的第二条道路名称。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/SearchUtils.html#getSHA1-Context-">getSHA1(Context)</a></span> - 类 中的静态方法com.amap.api.services.core.<a href="../com/amap/api/services/core/SearchUtils.html" title="com.amap.api.services.core中的类">SearchUtils</a></dt>
<dd>
<div class="block">返回SHA1。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/cloud/CloudSearch.SearchBound.html#getShape--">getShape()</a></span> - 类 中的方法com.amap.api.services.cloud.<a href="../com/amap/api/services/cloud/CloudSearch.SearchBound.html" title="com.amap.api.services.cloud中的类">CloudSearch.SearchBound</a></dt>
<dd>
<div class="block">返回查询的形状。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearch.SearchBound.html#getShape--">getShape()</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearch.SearchBound.html" title="com.amap.api.services.poisearch中的类">PoiSearch.SearchBound</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">返回查询范围形状。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearchV2.SearchBound.html#getShape--">getShape()</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearchV2.SearchBound.html" title="com.amap.api.services.poisearch中的类">PoiSearchV2.SearchBound</a></dt>
<dd>
<div class="block">返回查询范围形状。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/share/ShareSearch.ShareBusRouteQuery.html#getShareFromAndTo--">getShareFromAndTo()</a></span> - 类 中的方法com.amap.api.services.share.<a href="../com/amap/api/services/share/ShareSearch.ShareBusRouteQuery.html" title="com.amap.api.services.share中的类">ShareSearch.ShareBusRouteQuery</a></dt>
<dd>
<div class="block">返回规划的起点终点信息。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/share/ShareSearch.ShareDrivingRouteQuery.html#getShareFromAndTo--">getShareFromAndTo()</a></span> - 类 中的方法com.amap.api.services.share.<a href="../com/amap/api/services/share/ShareSearch.ShareDrivingRouteQuery.html" title="com.amap.api.services.share中的类">ShareSearch.ShareDrivingRouteQuery</a></dt>
<dd>
<div class="block">返回起终点信息。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/share/ShareSearch.ShareWalkRouteQuery.html#getShareFromAndTo--">getShareFromAndTo()</a></span> - 类 中的方法com.amap.api.services.share.<a href="../com/amap/api/services/share/ShareSearch.ShareWalkRouteQuery.html" title="com.amap.api.services.share中的类">ShareSearch.ShareWalkRouteQuery</a></dt>
<dd>
<div class="block">返回 路径规划的起点和终点信息。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/LatLonSharePoint.html#getSharePointName--">getSharePointName()</a></span> - 类 中的方法com.amap.api.services.core.<a href="../com/amap/api/services/core/LatLonSharePoint.html" title="com.amap.api.services.core中的类">LatLonSharePoint</a></dt>
<dd>
<div class="block">返回分享位置名称。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearchV2.Query.html#getShowFields--">getShowFields()</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearchV2.Query.html" title="com.amap.api.services.poisearch中的类">PoiSearchV2.Query</a></dt>
<dd>
<div class="block">扩展字段 base表示只返回基础数据，all表示所有数据 ，默认 base</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.BusRouteQuery.html#getShowFields--">getShowFields()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.BusRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearchV2.BusRouteQuery</a></dt>
<dd>
<div class="block">扩展字段，all表示所有数据 ，默认 cost <a href="../com/amap/api/services/route/RouteSearchV2.ShowFields.html" title="com.amap.api.services.route中的类"><code>RouteSearchV2.ShowFields</code></a></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.DriveRouteQuery.html#getShowFields--">getShowFields()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.DriveRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearchV2.DriveRouteQuery</a></dt>
<dd>
<div class="block">扩展字段</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.RideRouteQuery.html#getShowFields--">getShowFields()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.RideRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearchV2.RideRouteQuery</a></dt>
<dd>
<div class="block">扩展字段 all表示所有数据 ，默认 cost <a href="../com/amap/api/services/route/RouteSearchV2.ShowFields.html" title="com.amap.api.services.route中的类"><code>RouteSearchV2.ShowFields</code></a></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.WalkRouteQuery.html#getShowFields--">getShowFields()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.WalkRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearchV2.WalkRouteQuery</a></dt>
<dd>
<div class="block">扩展字段 <a href="../com/amap/api/services/route/RouteSearchV2.ShowFields.html" title="com.amap.api.services.route中的类"><code>RouteSearchV2.ShowFields</code></a></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/cloud/CloudItem.html#getSnippet--">getSnippet()</a></span> - 类 中的方法com.amap.api.services.cloud.<a href="../com/amap/api/services/cloud/CloudItem.html" title="com.amap.api.services.cloud中的类">CloudItem</a></dt>
<dd>
<div class="block">返回 CloudItem 的地址。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/PoiItem.html#getSnippet--">getSnippet()</a></span> - 类 中的方法com.amap.api.services.core.<a href="../com/amap/api/services/core/PoiItem.html" title="com.amap.api.services.core中的类">PoiItem</a></dt>
<dd>
<div class="block">返回POI的地址。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/SubPoiItem.html#getSnippet--">getSnippet()</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/SubPoiItem.html" title="com.amap.api.services.poisearch中的类">SubPoiItem</a></dt>
<dd>
<div class="block">返回子POI的地址。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/SubPoiItemV2.html#getSnippet--">getSnippet()</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/SubPoiItemV2.html" title="com.amap.api.services.poisearch中的类">SubPoiItemV2</a></dt>
<dd>
<div class="block">返回子POI的地址。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/cloud/CloudSearch.Query.html#getSortingrules--">getSortingrules()</a></span> - 类 中的方法com.amap.api.services.cloud.<a href="../com/amap/api/services/cloud/CloudSearch.Query.html" title="com.amap.api.services.cloud中的类">CloudSearch.Query</a></dt>
<dd>
<div class="block">返回排序规则。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/ServiceSettings.html#getSoTimeOut--">getSoTimeOut()</a></span> - 类 中的方法com.amap.api.services.core.<a href="../com/amap/api/services/core/ServiceSettings.html" title="com.amap.api.services.core中的类">ServiceSettings</a></dt>
<dd>
<div class="block">读取返回结果超时限制</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteRailwayItem.html#getSpaces--">getSpaces()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteRailwayItem.html" title="com.amap.api.services.route中的类">RouteRailwayItem</a></dt>
<dd>
<div class="block">返回舱位及价格信息。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.FromAndTo.html#getStartPoiID--">getStartPoiID()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.FromAndTo.html" title="com.amap.api.services.route中的类">RouteSearch.FromAndTo</a></dt>
<dd>
<div class="block">返回路径规划起点POI的ID。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.FromAndTo.html#getStartPoiID--">getStartPoiID()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.FromAndTo.html" title="com.amap.api.services.route中的类">RouteSearchV2.FromAndTo</a></dt>
<dd>
<div class="block">返回路径规划起点POI的ID。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RoutePlanResult.html#getStartPos--">getStartPos()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RoutePlanResult.html" title="com.amap.api.services.route中的类">RoutePlanResult</a></dt>
<dd>
<div class="block">返回路径规划起点的位置。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteResult.html#getStartPos--">getStartPos()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteResult.html" title="com.amap.api.services.route中的类">RouteResult</a></dt>
<dd>
<div class="block">返回路径规划起点的位置。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/TruckRouteRestult.html#getStartPos--">getStartPos()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/TruckRouteRestult.html" title="com.amap.api.services.route中的类">TruckRouteRestult</a></dt>
<dd>
<div class="block">路线起点坐标</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/TimeInfo.html#getStartTime--">getStartTime()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/TimeInfo.html" title="com.amap.api.services.route中的类">TimeInfo</a></dt>
<dd>
<div class="block">返回驾车未来路径规划开始时间。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/TMC.html#getStatus--">getStatus()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/TMC.html" title="com.amap.api.services.route中的类">TMC</a></dt>
<dd>
<div class="block">返回路况。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/DriveStepV2.html#getStepDistance--">getStepDistance()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/DriveStepV2.html" title="com.amap.api.services.route中的类">DriveStepV2</a></dt>
<dd>
<div class="block">获取分段距离信息</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/BusPath.html#getSteps--">getSteps()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/BusPath.html" title="com.amap.api.services.route中的类">BusPath</a></dt>
<dd>
<div class="block">返回公交路径规划方案的路段列表。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/BusPathV2.html#getSteps--">getSteps()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/BusPathV2.html" title="com.amap.api.services.route中的类">BusPathV2</a></dt>
<dd>
<div class="block">返回公交路径规划方案的路段列表。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/DrivePath.html#getSteps--">getSteps()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/DrivePath.html" title="com.amap.api.services.route中的类">DrivePath</a></dt>
<dd>
<div class="block">返回驾车规划方案的路段列表。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/DrivePathV2.html#getSteps--">getSteps()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/DrivePathV2.html" title="com.amap.api.services.route中的类">DrivePathV2</a></dt>
<dd>
<div class="block">返回驾车规划方案的路段列表。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/DrivePlanPath.html#getSteps--">getSteps()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/DrivePlanPath.html" title="com.amap.api.services.route中的类">DrivePlanPath</a></dt>
<dd>
<div class="block">返回导航路段。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RidePath.html#getSteps--">getSteps()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RidePath.html" title="com.amap.api.services.route中的类">RidePath</a></dt>
<dd>
<div class="block">返回骑行方案的路段列表。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/TruckPath.html#getSteps--">getSteps()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/TruckPath.html" title="com.amap.api.services.route中的类">TruckPath</a></dt>
<dd>
<div class="block">具体的方案细节</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/WalkPath.html#getSteps--">getSteps()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/WalkPath.html" title="com.amap.api.services.route中的类">WalkPath</a></dt>
<dd>
<div class="block">返回步行方案的路段列表。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/DrivePath.html#getStrategy--">getStrategy()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/DrivePath.html" title="com.amap.api.services.route中的类">DrivePath</a></dt>
<dd>
<div class="block">返回导航策略，显示为中文，如返回“速度最快”。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/DrivePathV2.html#getStrategy--">getStrategy()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/DrivePathV2.html" title="com.amap.api.services.route中的类">DrivePathV2</a></dt>
<dd>
<div class="block">返回导航策略，显示为中文，如返回“速度最快”。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/TruckPath.html#getStrategy--">getStrategy()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/TruckPath.html" title="com.amap.api.services.route中的类">TruckPath</a></dt>
<dd>
<div class="block">此方案的路径规划模式</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/StreetNumber.html#getStreet--">getStreet()</a></span> - 类 中的方法com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/StreetNumber.html" title="com.amap.api.services.geocoder中的类">StreetNumber</a></dt>
<dd>
<div class="block">返回门牌信息中的街道名称。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/RegeocodeAddress.html#getStreetNumber--">getStreetNumber()</a></span> - 类 中的方法com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/RegeocodeAddress.html" title="com.amap.api.services.geocoder中的类">RegeocodeAddress</a></dt>
<dd>
<div class="block">逆地理编码返回的门牌信息。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/district/DistrictItem.html#getSubDistrict--">getSubDistrict()</a></span> - 类 中的方法com.amap.api.services.district.<a href="../com/amap/api/services/district/DistrictItem.html" title="com.amap.api.services.district中的类">DistrictItem</a></dt>
<dd>
<div class="block">返回下一级行政区划的结果，如果无下级行政区划，返回null。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/district/DistrictSearchQuery.html#getSubDistrict--">getSubDistrict()</a></span> - 类 中的方法com.amap.api.services.district.<a href="../com/amap/api/services/district/DistrictSearchQuery.html" title="com.amap.api.services.district中的类">DistrictSearchQuery</a></dt>
<dd>
<div class="block">是否返回下级区划。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/SubPoiItem.html#getSubName--">getSubName()</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/SubPoiItem.html" title="com.amap.api.services.poisearch中的类">SubPoiItem</a></dt>
<dd>
<div class="block">返回子POI的子名称，如“西2门”</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/PoiItem.html#getSubPois--">getSubPois()</a></span> - 类 中的方法com.amap.api.services.core.<a href="../com/amap/api/services/core/PoiItem.html" title="com.amap.api.services.core中的类">PoiItem</a></dt>
<dd>
<div class="block">子POI信息获取。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/SubPoiItem.html#getSubTypeDes--">getSubTypeDes()</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/SubPoiItem.html" title="com.amap.api.services.poisearch中的类">SubPoiItem</a></dt>
<dd>
<div class="block">返回子POI的类型。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/SubPoiItemV2.html#getSubTypeDes--">getSubTypeDes()</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/SubPoiItemV2.html" title="com.amap.api.services.poisearch中的类">SubPoiItemV2</a></dt>
<dd>
<div class="block">返回子POI的类型。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/SuggestionCity.html#getSuggestionNum--">getSuggestionNum()</a></span> - 类 中的方法com.amap.api.services.core.<a href="../com/amap/api/services/core/SuggestionCity.html" title="com.amap.api.services.core中的类">SuggestionCity</a></dt>
<dd>
<div class="block">返回区域的建议结果数目。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/cloud/CloudSearch.Query.html#getTableID--">getTableID()</a></span> - 类 中的方法com.amap.api.services.cloud.<a href="../com/amap/api/services/cloud/CloudSearch.Query.html" title="com.amap.api.services.cloud中的类">CloudSearch.Query</a></dt>
<dd>
<div class="block">返回搜索的表tableid。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/Business.html#getTag--">getTag()</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/Business.html" title="com.amap.api.services.poisearch中的类">Business</a></dt>
<dd>
<div class="block">返回POI的特色内容。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RoutePlanResult.html#getTargetPos--">getTargetPos()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RoutePlanResult.html" title="com.amap.api.services.route中的类">RoutePlanResult</a></dt>
<dd>
<div class="block">返回路径规划终点的位置。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteResult.html#getTargetPos--">getTargetPos()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteResult.html" title="com.amap.api.services.route中的类">RouteResult</a></dt>
<dd>
<div class="block">返回路径规划终点的位置。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/TruckRouteRestult.html#getTargetPos--">getTargetPos()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/TruckRouteRestult.html" title="com.amap.api.services.route中的类">TruckRouteRestult</a></dt>
<dd>
<div class="block">路线终点坐标</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/BusStep.html#getTaxi--">getTaxi()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/BusStep.html" title="com.amap.api.services.route中的类">BusStep</a></dt>
<dd>
<div class="block">返回此路段的出租车信息。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/BusStepV2.html#getTaxi--">getTaxi()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/BusStepV2.html" title="com.amap.api.services.route中的类">BusStepV2</a></dt>
<dd>
<div class="block">返回此路段的出租车信息。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/BusRouteResult.html#getTaxiCost--">getTaxiCost()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/BusRouteResult.html" title="com.amap.api.services.route中的类">BusRouteResult</a></dt>
<dd>
<div class="block">返回规划的公交路线对应的打车费用，单位元。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/BusRouteResultV2.html#getTaxiCost--">getTaxiCost()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/BusRouteResultV2.html" title="com.amap.api.services.route中的类">BusRouteResultV2</a></dt>
<dd>
<div class="block">返回规划的公交路线对应的打车费用，单位元。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/DriveRouteResult.html#getTaxiCost--">getTaxiCost()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/DriveRouteResult.html" title="com.amap.api.services.route中的类">DriveRouteResult</a></dt>
<dd>
<div class="block">返回从起点到终点打车的费用，单位元。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/DriveRouteResultV2.html#getTaxiCost--">getTaxiCost()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/DriveRouteResultV2.html" title="com.amap.api.services.route中的类">DriveRouteResultV2</a></dt>
<dd>
<div class="block">返回从起点到终点打车的费用，单位元。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/PoiItem.html#getTel--">getTel()</a></span> - 类 中的方法com.amap.api.services.core.<a href="../com/amap/api/services/core/PoiItem.html" title="com.amap.api.services.core中的类">PoiItem</a></dt>
<dd>
<div class="block">返回POI的电话号码。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/Business.html#getTel--">getTel()</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/Business.html" title="com.amap.api.services.poisearch中的类">Business</a></dt>
<dd>
<div class="block">返回POI的电话号码。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/weather/LocalWeatherLive.html#getTemperature--">getTemperature()</a></span> - 类 中的方法com.amap.api.services.weather.<a href="../com/amap/api/services/weather/LocalWeatherLive.html" title="com.amap.api.services.weather中的类">LocalWeatherLive</a></dt>
<dd>
<div class="block">返回实时气温，单位：摄氏度。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/busline/BusLineItem.html#getTerminalStation--">getTerminalStation()</a></span> - 类 中的方法com.amap.api.services.busline.<a href="../com/amap/api/services/busline/BusLineItem.html" title="com.amap.api.services.busline中的类">BusLineItem</a></dt>
<dd>
<div class="block">返回公交线路的终点站名称。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RailwayStationItem.html#getTime--">getTime()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RailwayStationItem.html" title="com.amap.api.services.route中的类">RailwayStationItem</a></dt>
<dd>
<div class="block">返回进站时间。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteRailwayItem.html#getTime--">getTime()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteRailwayItem.html" title="com.amap.api.services.route中的类">RouteRailwayItem</a></dt>
<dd>
<div class="block">返回该线路车段耗时。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.BusRouteQuery.html#getTime--">getTime()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.BusRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearchV2.BusRouteQuery</a></dt>
<dd>
<div class="block">请求时间

 例如:9-54</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/DriveRoutePlanResult.html#getTimeInfos--">getTimeInfos()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/DriveRoutePlanResult.html" title="com.amap.api.services.route中的类">DriveRoutePlanResult</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/nearby/NearbySearch.NearbyQuery.html#getTimeRange--">getTimeRange()</a></span> - 类 中的方法com.amap.api.services.nearby.<a href="../com/amap/api/services/nearby/NearbySearch.NearbyQuery.html" title="com.amap.api.services.nearby中的类">NearbySearch.NearbyQuery</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">返回检索的时间范围。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/nearby/NearbyInfo.html#getTimeStamp--">getTimeStamp()</a></span> - 类 中的方法com.amap.api.services.nearby.<a href="../com/amap/api/services/nearby/NearbyInfo.html" title="com.amap.api.services.nearby中的类">NearbyInfo</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">获取时间戳数据。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/cloud/CloudItem.html#getTitle--">getTitle()</a></span> - 类 中的方法com.amap.api.services.cloud.<a href="../com/amap/api/services/cloud/CloudItem.html" title="com.amap.api.services.cloud中的类">CloudItem</a></dt>
<dd>
<div class="block">返回 CloudItem 的名称。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/PoiItem.html#getTitle--">getTitle()</a></span> - 类 中的方法com.amap.api.services.core.<a href="../com/amap/api/services/core/PoiItem.html" title="com.amap.api.services.core中的类">PoiItem</a></dt>
<dd>
<div class="block">返回POI的名称。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/Photo.html#getTitle--">getTitle()</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/Photo.html" title="com.amap.api.services.poisearch中的类">Photo</a></dt>
<dd>
<div class="block">获取图片的名称。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/SubPoiItem.html#getTitle--">getTitle()</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/SubPoiItem.html" title="com.amap.api.services.poisearch中的类">SubPoiItem</a></dt>
<dd>
<div class="block">返回POI全称，如“北京大学(西2门)”。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/SubPoiItemV2.html#getTitle--">getTitle()</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/SubPoiItemV2.html" title="com.amap.api.services.poisearch中的类">SubPoiItemV2</a></dt>
<dd>
<div class="block">返回POI全称，如“北京大学(西2门)”。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/routepoisearch/RoutePOIItem.html#getTitle--">getTitle()</a></span> - 类 中的方法com.amap.api.services.routepoisearch.<a href="../com/amap/api/services/routepoisearch/RoutePOIItem.html" title="com.amap.api.services.routepoisearch中的类">RoutePOIItem</a></dt>
<dd>
<div class="block">返回沿途POI的名称。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/DriveStep.html#getTMCs--">getTMCs()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/DriveStep.html" title="com.amap.api.services.route中的类">DriveStep</a></dt>
<dd>
<div class="block">获取搜索返回的路径规划交通拥堵信息。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/DriveStepV2.html#getTMCs--">getTMCs()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/DriveStepV2.html" title="com.amap.api.services.route中的类">DriveStepV2</a></dt>
<dd>
<div class="block">获取搜索返回的路径规划交通拥堵信息。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/TimeInfosElement.html#getTMCs--">getTMCs()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/TimeInfosElement.html" title="com.amap.api.services.route中的类">TimeInfosElement</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/TruckStep.html#getTMCs--">getTMCs()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/TruckStep.html" title="com.amap.api.services.route中的类">TruckStep</a></dt>
<dd>
<div class="block">获取搜索返回的路径规划交通拥堵信息。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.FromAndTo.html#getTo--">getTo()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.FromAndTo.html" title="com.amap.api.services.route中的类">RouteSearch.FromAndTo</a></dt>
<dd>
<div class="block">返回路径规划的终点坐标。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.FromAndTo.html#getTo--">getTo()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.FromAndTo.html" title="com.amap.api.services.route中的类">RouteSearchV2.FromAndTo</a></dt>
<dd>
<div class="block">返回路径规划的终点坐标。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/routepoisearch/RoutePOISearchQuery.html#getTo--">getTo()</a></span> - 类 中的方法com.amap.api.services.routepoisearch.<a href="../com/amap/api/services/routepoisearch/RoutePOISearchQuery.html" title="com.amap.api.services.routepoisearch中的类">RoutePOISearchQuery</a></dt>
<dd>
<div class="block">返回路径规划的终点坐标。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/share/ShareSearch.ShareFromAndTo.html#getTo--">getTo()</a></span> - 类 中的方法com.amap.api.services.share.<a href="../com/amap/api/services/share/ShareSearch.ShareFromAndTo.html" title="com.amap.api.services.share中的类">ShareSearch.ShareFromAndTo</a></dt>
<dd>
<div class="block">返回终点坐标。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/DrivePlanStep.html#getToll--">getToll()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/DrivePlanStep.html" title="com.amap.api.services.route中的类">DrivePlanStep</a></dt>
<dd>
<div class="block">返回驾车路段是否收费。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/Cost.html#getTollDistance--">getTollDistance()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/Cost.html" title="com.amap.api.services.route中的类">Cost</a></dt>
<dd>
<div class="block">获取收费路段里程，单位：米，包括分段信息</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/DrivePath.html#getTollDistance--">getTollDistance()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/DrivePath.html" title="com.amap.api.services.route中的类">DrivePath</a></dt>
<dd>
<div class="block">返回此方案中的收费道路的总长度，单位米。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/DriveStep.html#getTollDistance--">getTollDistance()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/DriveStep.html" title="com.amap.api.services.route中的类">DriveStep</a></dt>
<dd>
<div class="block">返回驾车收费路段的距离，单位米。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/TruckPath.html#getTollDistance--">getTollDistance()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/TruckPath.html" title="com.amap.api.services.route中的类">TruckPath</a></dt>
<dd>
<div class="block">此导航方案道路收费距离长度   单位：米</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/TruckStep.html#getTollDistance--">getTollDistance()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/TruckStep.html" title="com.amap.api.services.route中的类">TruckStep</a></dt>
<dd>
<div class="block">收费路段距离  单位：米</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/Cost.html#getTollRoad--">getTollRoad()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/Cost.html" title="com.amap.api.services.route中的类">Cost</a></dt>
<dd>
<div class="block">获取主要收费道路</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/DriveStep.html#getTollRoad--">getTollRoad()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/DriveStep.html" title="com.amap.api.services.route中的类">DriveStep</a></dt>
<dd>
<div class="block">返回驾车路段的主要收费道路。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/TruckStep.html#getTollRoad--">getTollRoad()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/TruckStep.html" title="com.amap.api.services.route中的类">TruckStep</a></dt>
<dd>
<div class="block">主要收费道路</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/Cost.html#getTolls--">getTolls()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/Cost.html" title="com.amap.api.services.route中的类">Cost</a></dt>
<dd>
<div class="block">获取此路线道路收费，单位：元，包括分段信息</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/DrivePath.html#getTolls--">getTolls()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/DrivePath.html" title="com.amap.api.services.route中的类">DrivePath</a></dt>
<dd>
<div class="block">返回此方案中的收费道路的总费用，单位元。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/DriveStep.html#getTolls--">getTolls()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/DriveStep.html" title="com.amap.api.services.route中的类">DriveStep</a></dt>
<dd>
<div class="block">返回驾车路段的收费价格，单位元。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/TimeInfosElement.html#getTolls--">getTolls()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/TimeInfosElement.html" title="com.amap.api.services.route中的类">TimeInfosElement</a></dt>
<dd>
<div class="block">返回驾车未来路径规划路线总收费。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/TruckPath.html#getTolls--">getTolls()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/TruckPath.html" title="com.amap.api.services.route中的类">TruckPath</a></dt>
<dd>
<div class="block">此导航方案道路收费金额 单位：元</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/TruckStep.html#getTolls--">getTolls()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/TruckStep.html" title="com.amap.api.services.route中的类">TruckStep</a></dt>
<dd>
<div class="block">此段收费    单位：元</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/share/ShareSearch.ShareFromAndTo.html#getToName--">getToName()</a></span> - 类 中的方法com.amap.api.services.share.<a href="../com/amap/api/services/share/ShareSearch.ShareFromAndTo.html" title="com.amap.api.services.share中的类">ShareSearch.ShareFromAndTo</a></dt>
<dd>
<div class="block">返回终点分享名称。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/cloud/CloudResult.html#getTotalCount--">getTotalCount()</a></span> - 类 中的方法com.amap.api.services.cloud.<a href="../com/amap/api/services/cloud/CloudResult.html" title="com.amap.api.services.cloud中的类">CloudResult</a></dt>
<dd>
<div class="block">返回搜索结果的总数量</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/nearby/NearbySearchResult.html#getTotalNum--">getTotalNum()</a></span> - 类 中的方法com.amap.api.services.nearby.<a href="../com/amap/api/services/nearby/NearbySearchResult.html" title="com.amap.api.services.nearby中的类">NearbySearchResult</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">结果总数。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/busline/BusLineItem.html#getTotalPrice--">getTotalPrice()</a></span> - 类 中的方法com.amap.api.services.busline.<a href="../com/amap/api/services/busline/BusLineItem.html" title="com.amap.api.services.busline中的类">BusLineItem</a></dt>
<dd>
<div class="block">返回公交线路的全程票价，单位元。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/DrivePath.html#getTotalTrafficlights--">getTotalTrafficlights()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/DrivePath.html" title="com.amap.api.services.route中的类">DrivePath</a></dt>
<dd>
<div class="block">返回此方案中的红绿灯个数。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/TruckPath.html#getTotalTrafficlights--">getTotalTrafficlights()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/TruckPath.html" title="com.amap.api.services.route中的类">TruckPath</a></dt>
<dd>
<div class="block">此方案红绿灯总数        单位：个</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/RegeocodeAddress.html#getTowncode--">getTowncode()</a></span> - 类 中的方法com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/RegeocodeAddress.html" title="com.amap.api.services.geocoder中的类">RegeocodeAddress</a></dt>
<dd>
<div class="block">返回乡镇街道编码。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/GeocodeAddress.html#getTownship--">getTownship()</a></span> - 类 中的方法com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/GeocodeAddress.html" title="com.amap.api.services.geocoder中的类">GeocodeAddress</a></dt>
<dd>
<div class="block">地理编码返回的乡镇名称。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/RegeocodeAddress.html#getTownship--">getTownship()</a></span> - 类 中的方法com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/RegeocodeAddress.html" title="com.amap.api.services.geocoder中的类">RegeocodeAddress</a></dt>
<dd>
<div class="block">逆地理编码返回的乡镇名称。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/Cost.html#getTrafficLights--">getTrafficLights()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/Cost.html" title="com.amap.api.services.route中的类">Cost</a></dt>
<dd>
<div class="block">获取方案中红绿灯个数</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/DrivePlanPath.html#getTrafficLights--">getTrafficLights()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/DrivePlanPath.html" title="com.amap.api.services.route中的类">DrivePlanPath</a></dt>
<dd>
<div class="block">返回红绿灯个数。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteRailwayItem.html#getTrip--">getTrip()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteRailwayItem.html" title="com.amap.api.services.route中的类">RouteRailwayItem</a></dt>
<dd>
<div class="block">返回线路车次号。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.TruckRouteQuery.html#getTruckAxis--">getTruckAxis()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.TruckRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearch.TruckRouteQuery</a></dt>
<dd>
<div class="block">获取设置的车辆轴数</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.TruckRouteQuery.html#getTruckHeight--">getTruckHeight()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.TruckRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearch.TruckRouteQuery</a></dt>
<dd>
<div class="block">获取设置的车辆高度</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.TruckRouteQuery.html#getTruckLoad--">getTruckLoad()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.TruckRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearch.TruckRouteQuery</a></dt>
<dd>
<div class="block">获取设置的车辆总重</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/TruckRouteRestult.html#getTruckQuery--">getTruckQuery()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/TruckRouteRestult.html" title="com.amap.api.services.route中的类">TruckRouteRestult</a></dt>
<dd>
<div class="block">请求时填写的参数配置</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.TruckRouteQuery.html#getTruckSize--">getTruckSize()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.TruckRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearch.TruckRouteQuery</a></dt>
<dd>
<div class="block">获取设置的车辆大小</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.TruckRouteQuery.html#getTruckWeight--">getTruckWeight()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.TruckRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearch.TruckRouteQuery</a></dt>
<dd>
<div class="block">获取设置的车辆核定载重</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.TruckRouteQuery.html#getTruckWidth--">getTruckWidth()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.TruckRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearch.TruckRouteQuery</a></dt>
<dd>
<div class="block">获取设置的车辆高度</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/help/InputtipsQuery.html#getType--">getType()</a></span> - 类 中的方法com.amap.api.services.help.<a href="../com/amap/api/services/help/InputtipsQuery.html" title="com.amap.api.services.help中的类">InputtipsQuery</a></dt>
<dd>
<div class="block">返回搜索的类型。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/nearby/NearbySearch.NearbyQuery.html#getType--">getType()</a></span> - 类 中的方法com.amap.api.services.nearby.<a href="../com/amap/api/services/nearby/NearbySearch.NearbyQuery.html" title="com.amap.api.services.nearby中的类">NearbySearch.NearbyQuery</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">返回检索类型。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/road/Road.html#getType--">getType()</a></span> - 类 中的方法com.amap.api.services.road.<a href="../com/amap/api/services/road/Road.html" title="com.amap.api.services.road中的类">Road</a></dt>
<dd>
<div class="block">返回结果的道路类型。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/DistanceSearch.DistanceQuery.html#getType--">getType()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/DistanceSearch.DistanceQuery.html" title="com.amap.api.services.route中的类">DistanceSearch.DistanceQuery</a></dt>
<dd>
<div class="block">获取距离测量方式</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteRailwayItem.html#getType--">getType()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteRailwayItem.html" title="com.amap.api.services.route中的类">RouteRailwayItem</a></dt>
<dd>
<div class="block">返回线路车次信息。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/weather/WeatherSearchQuery.html#getType--">getType()</a></span> - 类 中的方法com.amap.api.services.weather.<a href="../com/amap/api/services/weather/WeatherSearchQuery.html" title="com.amap.api.services.weather中的类">WeatherSearchQuery</a></dt>
<dd>
<div class="block">返回查询的天气类型。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/PoiItem.html#getTypeCode--">getTypeCode()</a></span> - 类 中的方法com.amap.api.services.core.<a href="../com/amap/api/services/core/PoiItem.html" title="com.amap.api.services.core中的类">PoiItem</a></dt>
<dd>
<div class="block">返回兴趣点类型编码。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/help/Tip.html#getTypeCode--">getTypeCode()</a></span> - 类 中的方法com.amap.api.services.help.<a href="../com/amap/api/services/help/Tip.html" title="com.amap.api.services.help中的类">Tip</a></dt>
<dd>
<div class="block">获取输入提示结果的类型编码</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/SubPoiItemV2.html#getTypeCode--">getTypeCode()</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/SubPoiItemV2.html" title="com.amap.api.services.poisearch中的类">SubPoiItemV2</a></dt>
<dd>
<div class="block">返回子POI的分类编码</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/PoiItem.html#getTypeDes--">getTypeDes()</a></span> - 类 中的方法com.amap.api.services.core.<a href="../com/amap/api/services/core/PoiItem.html" title="com.amap.api.services.core中的类">PoiItem</a></dt>
<dd>
<div class="block">返回POI 的类型描述。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/cloud/CloudItem.html#getUpdatetime--">getUpdatetime()</a></span> - 类 中的方法com.amap.api.services.cloud.<a href="../com/amap/api/services/cloud/CloudItem.html" title="com.amap.api.services.cloud中的类">CloudItem</a></dt>
<dd>
<div class="block">返回更新企业地图数据的时间。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/cloud/CloudSearch.SearchBound.html#getUpperRight--">getUpperRight()</a></span> - 类 中的方法com.amap.api.services.cloud.<a href="../com/amap/api/services/cloud/CloudSearch.SearchBound.html" title="com.amap.api.services.cloud中的类">CloudSearch.SearchBound</a></dt>
<dd>
<div class="block">返回查询矩形的右上角坐标。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearch.SearchBound.html#getUpperRight--">getUpperRight()</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearch.SearchBound.html" title="com.amap.api.services.poisearch中的类">PoiSearch.SearchBound</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">返回矩形右上角坐标。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearchV2.SearchBound.html#getUpperRight--">getUpperRight()</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearchV2.SearchBound.html" title="com.amap.api.services.poisearch中的类">PoiSearchV2.SearchBound</a></dt>
<dd>
<div class="block">返回矩形右上角坐标。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/cloud/CloudImage.html#getUrl--">getUrl()</a></span> - 类 中的方法com.amap.api.services.cloud.<a href="../com/amap/api/services/cloud/CloudImage.html" title="com.amap.api.services.cloud中的类">CloudImage</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">获取cloud数据的图片地址，最大限制获取1024*1024，若您的原始图片小于该尺寸，将返回原图。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/Photo.html#getUrl--">getUrl()</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/Photo.html" title="com.amap.api.services.poisearch中的类">Photo</a></dt>
<dd>
<div class="block">返回图片url。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/nearby/NearbyInfo.html#getUserID--">getUserID()</a></span> - 类 中的方法com.amap.api.services.nearby.<a href="../com/amap/api/services/nearby/NearbyInfo.html" title="com.amap.api.services.nearby中的类">NearbyInfo</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">返回用户ID。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/nearby/UploadInfo.html#getUserID--">getUserID()</a></span> - 类 中的方法com.amap.api.services.nearby.<a href="../com/amap/api/services/nearby/UploadInfo.html" title="com.amap.api.services.nearby中的类">UploadInfo</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">返回用户ID</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearchV2.ShowFields.html#getValue--">getValue()</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearchV2.ShowFields.html" title="com.amap.api.services.poisearch中的类">PoiSearchV2.ShowFields</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.DrivingStrategy.html#getValue--">getValue()</a></span> - 枚举 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.DrivingStrategy.html" title="com.amap.api.services.route中的枚举">RouteSearchV2.DrivingStrategy</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteRailwayItem.html#getViastops--">getViastops()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteRailwayItem.html" title="com.amap.api.services.route中的类">RouteRailwayItem</a></dt>
<dd>
<div class="block">返回途经站信息。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RailwayStationItem.html#getWait--">getWait()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RailwayStationItem.html" title="com.amap.api.services.route中的类">RailwayStationItem</a></dt>
<dd>
<div class="block">返回途经站的站点停靠时间，单位，秒（途径站才有这个值）。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/BusStep.html#getWalk--">getWalk()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/BusStep.html" title="com.amap.api.services.route中的类">BusStep</a></dt>
<dd>
<div class="block">返回此路段的步行信息。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/BusStepV2.html#getWalk--">getWalk()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/BusStepV2.html" title="com.amap.api.services.route中的类">BusStepV2</a></dt>
<dd>
<div class="block">返回此路段的步行信息。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/BusPath.html#getWalkDistance--">getWalkDistance()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/BusPath.html" title="com.amap.api.services.route中的类">BusPath</a></dt>
<dd>
<div class="block">返回此方案的总步行距离，单位米。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/BusPathV2.html#getWalkDistance--">getWalkDistance()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/BusPathV2.html" title="com.amap.api.services.route中的类">BusPathV2</a></dt>
<dd>
<div class="block">返回此方案的总步行距离，单位米。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/share/ShareSearch.ShareWalkRouteQuery.html#getWalkMode--">getWalkMode()</a></span> - 类 中的方法com.amap.api.services.share.<a href="../com/amap/api/services/share/ShareSearch.ShareWalkRouteQuery.html" title="com.amap.api.services.share中的类">ShareSearch.ShareWalkRouteQuery</a></dt>
<dd>
<div class="block">返回步行策略。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/WalkRouteResult.html#getWalkQuery--">getWalkQuery()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/WalkRouteResult.html" title="com.amap.api.services.route中的类">WalkRouteResult</a></dt>
<dd>
<div class="block">返回该结果对应的查询参数。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/WalkRouteResultV2.html#getWalkQuery--">getWalkQuery()</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/WalkRouteResultV2.html" title="com.amap.api.services.route中的类">WalkRouteResultV2</a></dt>
<dd>
<div class="block">返回该结果对应的查询参数。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/weather/LocalWeatherLive.html#getWeather--">getWeather()</a></span> - 类 中的方法com.amap.api.services.weather.<a href="../com/amap/api/services/weather/LocalWeatherLive.html" title="com.amap.api.services.weather中的类">LocalWeatherLive</a></dt>
<dd>
<div class="block">返回天气现象描述，如“晴”、“多云”等。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/weather/LocalWeatherForecast.html#getWeatherForecast--">getWeatherForecast()</a></span> - 类 中的方法com.amap.api.services.weather.<a href="../com/amap/api/services/weather/LocalWeatherForecast.html" title="com.amap.api.services.weather中的类">LocalWeatherForecast</a></dt>
<dd>
<div class="block">返回天气预报的数据的数组，数组中的对象为 LocalDayWeatherForecast。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/weather/LocalWeatherForecastResult.html#getWeatherForecastQuery--">getWeatherForecastQuery()</a></span> - 类 中的方法com.amap.api.services.weather.<a href="../com/amap/api/services/weather/LocalWeatherForecastResult.html" title="com.amap.api.services.weather中的类">LocalWeatherForecastResult</a></dt>
<dd>
<div class="block">返回该结果对应的查询参数。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/weather/LocalWeatherLiveResult.html#getWeatherLiveQuery--">getWeatherLiveQuery()</a></span> - 类 中的方法com.amap.api.services.weather.<a href="../com/amap/api/services/weather/LocalWeatherLiveResult.html" title="com.amap.api.services.weather中的类">LocalWeatherLiveResult</a></dt>
<dd>
<div class="block">返回该结果对应的查询参数。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/PoiItem.html#getWebsite--">getWebsite()</a></span> - 类 中的方法com.amap.api.services.core.<a href="../com/amap/api/services/core/PoiItem.html" title="com.amap.api.services.core中的类">PoiItem</a></dt>
<dd>
<div class="block">返回POI的网址。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/weather/LocalDayWeatherForecast.html#getWeek--">getWeek()</a></span> - 类 中的方法com.amap.api.services.weather.<a href="../com/amap/api/services/weather/LocalDayWeatherForecast.html" title="com.amap.api.services.weather中的类">LocalDayWeatherForecast</a></dt>
<dd>
<div class="block">返回预报天气的星期。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/weather/LocalWeatherLive.html#getWindDirection--">getWindDirection()</a></span> - 类 中的方法com.amap.api.services.weather.<a href="../com/amap/api/services/weather/LocalWeatherLive.html" title="com.amap.api.services.weather中的类">LocalWeatherLive</a></dt>
<dd>
<div class="block">返回风向。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/weather/LocalWeatherLive.html#getWindPower--">getWindPower()</a></span> - 类 中的方法com.amap.api.services.weather.<a href="../com/amap/api/services/weather/LocalWeatherLive.html" title="com.amap.api.services.weather中的类">LocalWeatherLive</a></dt>
<dd>
<div class="block">返回风力，单位：级。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/GeocodeSearch.html#GPS">GPS</a></span> - 类 中的静态变量com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/GeocodeSearch.html" title="com.amap.api.services.geocoder中的类">GeocodeSearch</a></dt>
<dd>
<div class="block">输入参数坐标为GPS类型。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/nearby/NearbySearch.html#GPS">GPS</a></span> - 类 中的静态变量com.amap.api.services.nearby.<a href="../com/amap/api/services/nearby/NearbySearch.html" title="com.amap.api.services.nearby中的类">NearbySearch</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">上传的坐标类型为原始GPS坐标系，应用于UploadInfo.setCoordType(int coordType)方法。</div>
</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">K</a>&nbsp;<a href="index-11.html">L</a>&nbsp;<a href="index-12.html">M</a>&nbsp;<a href="index-13.html">N</a>&nbsp;<a href="index-14.html">O</a>&nbsp;<a href="index-15.html">P</a>&nbsp;<a href="index-16.html">Q</a>&nbsp;<a href="index-17.html">R</a>&nbsp;<a href="index-18.html">S</a>&nbsp;<a href="index-19.html">T</a>&nbsp;<a href="index-20.html">U</a>&nbsp;<a href="index-21.html">V</a>&nbsp;<a href="index-22.html">W</a>&nbsp;</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-6.html">上一个字母</a></li>
<li><a href="index-8.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-7.html" target="_top">框架</a></li>
<li><a href="index-7.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
