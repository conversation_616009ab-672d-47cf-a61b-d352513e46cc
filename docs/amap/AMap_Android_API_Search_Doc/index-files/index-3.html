<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>C - 索引</title>
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="C - \u7D22\u5F15";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-2.html">上一个字母</a></li>
<li><a href="index-4.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-3.html" target="_top">框架</a></li>
<li><a href="index-3.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="contentContainer"><a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">K</a>&nbsp;<a href="index-11.html">L</a>&nbsp;<a href="index-12.html">M</a>&nbsp;<a href="index-13.html">N</a>&nbsp;<a href="index-14.html">O</a>&nbsp;<a href="index-15.html">P</a>&nbsp;<a href="index-16.html">Q</a>&nbsp;<a href="index-17.html">R</a>&nbsp;<a href="index-18.html">S</a>&nbsp;<a href="index-19.html">T</a>&nbsp;<a href="index-20.html">U</a>&nbsp;<a href="index-21.html">V</a>&nbsp;<a href="index-22.html">W</a>&nbsp;<a name="I:C">
<!--   -->
</a>
<h2 class="title">C</h2>
<dl>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#calculateBusRoute-com.amap.api.services.route.RouteSearch.BusRouteQuery-">calculateBusRoute(RouteSearch.BusRouteQuery)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span>
<div class="block"><span class="deprecationComment">自9.4.0废弃 <a href="../com/amap/api/services/route/RouteSearchV2.html#calculateBusRoute-com.amap.api.services.route.RouteSearchV2.BusRouteQuery-"><code>RouteSearchV2.calculateBusRoute(com.amap.api.services.route.RouteSearchV2.BusRouteQuery)</code></a></span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.html#calculateBusRoute-com.amap.api.services.route.RouteSearchV2.BusRouteQuery-">calculateBusRoute(RouteSearchV2.BusRouteQuery)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.html" title="com.amap.api.services.route中的类">RouteSearchV2</a></dt>
<dd>
<div class="block">根据指定的参数来计算公交路径。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#calculateBusRouteAsyn-com.amap.api.services.route.RouteSearch.BusRouteQuery-">calculateBusRouteAsyn(RouteSearch.BusRouteQuery)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span>
<div class="block"><span class="deprecationComment">自9.4.0废弃 <a href="../com/amap/api/services/route/RouteSearchV2.html#calculateBusRouteAsyn-com.amap.api.services.route.RouteSearchV2.BusRouteQuery-"><code>RouteSearchV2.calculateBusRouteAsyn(com.amap.api.services.route.RouteSearchV2.BusRouteQuery)</code></a></span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.html#calculateBusRouteAsyn-com.amap.api.services.route.RouteSearchV2.BusRouteQuery-">calculateBusRouteAsyn(RouteSearchV2.BusRouteQuery)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.html" title="com.amap.api.services.route中的类">RouteSearchV2</a></dt>
<dd>
<div class="block">异步处理。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#calculateDrivePlan-com.amap.api.services.route.RouteSearch.DrivePlanQuery-">calculateDrivePlan(RouteSearch.DrivePlanQuery)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block">根据指定的参数来计算驾车路径。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#calculateDrivePlanAsyn-com.amap.api.services.route.RouteSearch.DrivePlanQuery-">calculateDrivePlanAsyn(RouteSearch.DrivePlanQuery)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block">异步处理。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#calculateDriveRoute-com.amap.api.services.route.RouteSearch.DriveRouteQuery-">calculateDriveRoute(RouteSearch.DriveRouteQuery)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span>
<div class="block"><span class="deprecationComment">自9.4.0废弃 <a href="../com/amap/api/services/route/RouteSearchV2.html#calculateDriveRoute-com.amap.api.services.route.RouteSearchV2.DriveRouteQuery-"><code>RouteSearchV2.calculateDriveRoute(com.amap.api.services.route.RouteSearchV2.DriveRouteQuery)</code></a></span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.html#calculateDriveRoute-com.amap.api.services.route.RouteSearchV2.DriveRouteQuery-">calculateDriveRoute(RouteSearchV2.DriveRouteQuery)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.html" title="com.amap.api.services.route中的类">RouteSearchV2</a></dt>
<dd>
<div class="block">根据指定的参数来计算驾车路径。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#calculateDriveRouteAsyn-com.amap.api.services.route.RouteSearch.DriveRouteQuery-">calculateDriveRouteAsyn(RouteSearch.DriveRouteQuery)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span>
<div class="block"><span class="deprecationComment">自9.4.0废弃 <a href="../com/amap/api/services/route/RouteSearchV2.html#calculateDriveRouteAsyn-com.amap.api.services.route.RouteSearchV2.DriveRouteQuery-"><code>RouteSearchV2.calculateDriveRouteAsyn(com.amap.api.services.route.RouteSearchV2.DriveRouteQuery)</code></a></span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.html#calculateDriveRouteAsyn-com.amap.api.services.route.RouteSearchV2.DriveRouteQuery-">calculateDriveRouteAsyn(RouteSearchV2.DriveRouteQuery)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.html" title="com.amap.api.services.route中的类">RouteSearchV2</a></dt>
<dd>
<div class="block">异步处理。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#calculateRideRoute-com.amap.api.services.route.RouteSearch.RideRouteQuery-">calculateRideRoute(RouteSearch.RideRouteQuery)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span>
<div class="block"><span class="deprecationComment">自9.4.0废弃 <a href="../com/amap/api/services/route/RouteSearchV2.html#calculateRideRoute-com.amap.api.services.route.RouteSearchV2.RideRouteQuery-"><code>RouteSearchV2.calculateRideRoute(com.amap.api.services.route.RouteSearchV2.RideRouteQuery)</code></a></span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.html#calculateRideRoute-com.amap.api.services.route.RouteSearchV2.RideRouteQuery-">calculateRideRoute(RouteSearchV2.RideRouteQuery)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.html" title="com.amap.api.services.route中的类">RouteSearchV2</a></dt>
<dd>
<div class="block">根据指定的参数来计算骑行路径，同步接口。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#calculateRideRouteAsyn-com.amap.api.services.route.RouteSearch.RideRouteQuery-">calculateRideRouteAsyn(RouteSearch.RideRouteQuery)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span>
<div class="block"><span class="deprecationComment">自9.4.0废弃 <a href="../com/amap/api/services/route/RouteSearchV2.html#calculateRideRouteAsyn-com.amap.api.services.route.RouteSearchV2.RideRouteQuery-"><code>RouteSearchV2.calculateRideRouteAsyn(com.amap.api.services.route.RouteSearchV2.RideRouteQuery)</code></a></span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.html#calculateRideRouteAsyn-com.amap.api.services.route.RouteSearchV2.RideRouteQuery-">calculateRideRouteAsyn(RouteSearchV2.RideRouteQuery)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.html" title="com.amap.api.services.route中的类">RouteSearchV2</a></dt>
<dd>
<div class="block">异步处理。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/DistanceSearch.html#calculateRouteDistance-com.amap.api.services.route.DistanceSearch.DistanceQuery-">calculateRouteDistance(DistanceSearch.DistanceQuery)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/DistanceSearch.html" title="com.amap.api.services.route中的类">DistanceSearch</a></dt>
<dd>
<div class="block">测量距离请求接口，调用后会发起距离测量请求。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/DistanceSearch.html#calculateRouteDistanceAsyn-com.amap.api.services.route.DistanceSearch.DistanceQuery-">calculateRouteDistanceAsyn(DistanceSearch.DistanceQuery)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/DistanceSearch.html" title="com.amap.api.services.route中的类">DistanceSearch</a></dt>
<dd>
<div class="block">测量距离请求接口，调用后会发起距离测量请求。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#calculateTruckRoute-com.amap.api.services.route.RouteSearch.TruckRouteQuery-">calculateTruckRoute(RouteSearch.TruckRouteQuery)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block">根据指定的参数来计算货车路径。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#calculateTruckRouteAsyn-com.amap.api.services.route.RouteSearch.TruckRouteQuery-">calculateTruckRouteAsyn(RouteSearch.TruckRouteQuery)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block">异步处理。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#calculateWalkRoute-com.amap.api.services.route.RouteSearch.WalkRouteQuery-">calculateWalkRoute(RouteSearch.WalkRouteQuery)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span>
<div class="block"><span class="deprecationComment">自9.4.0废弃 <a href="../com/amap/api/services/route/RouteSearchV2.html#calculateWalkRoute-com.amap.api.services.route.RouteSearchV2.WalkRouteQuery-"><code>RouteSearchV2.calculateWalkRoute(com.amap.api.services.route.RouteSearchV2.WalkRouteQuery)</code></a></span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.html#calculateWalkRoute-com.amap.api.services.route.RouteSearchV2.WalkRouteQuery-">calculateWalkRoute(RouteSearchV2.WalkRouteQuery)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.html" title="com.amap.api.services.route中的类">RouteSearchV2</a></dt>
<dd>
<div class="block">根据指定的参数来计算步行路径。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#calculateWalkRouteAsyn-com.amap.api.services.route.RouteSearch.WalkRouteQuery-">calculateWalkRouteAsyn(RouteSearch.WalkRouteQuery)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span>
<div class="block"><span class="deprecationComment">自废弃9.4.0废弃 <a href="../com/amap/api/services/route/RouteSearchV2.html#calculateWalkRouteAsyn-com.amap.api.services.route.RouteSearchV2.WalkRouteQuery-"><code>RouteSearchV2.calculateWalkRouteAsyn(com.amap.api.services.route.RouteSearchV2.WalkRouteQuery)</code></a></span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.html#calculateWalkRouteAsyn-com.amap.api.services.route.RouteSearchV2.WalkRouteQuery-">calculateWalkRouteAsyn(RouteSearchV2.WalkRouteQuery)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.html" title="com.amap.api.services.route中的类">RouteSearchV2</a></dt>
<dd>
<div class="block">异步处理。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/district/DistrictSearchQuery.html#checkLevels--">checkLevels()</a></span> - 类 中的方法com.amap.api.services.district.<a href="../com/amap/api/services/district/DistrictSearchQuery.html" title="com.amap.api.services.district中的类">DistrictSearchQuery</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span>
<div class="block"><span class="deprecationComment">3.6.1 不再需要此参数限制</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearchV2.ShowFields.html#CHILDREN">CHILDREN</a></span> - 类 中的静态变量com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearchV2.ShowFields.html" title="com.amap.api.services.poisearch中的类">PoiSearchV2.ShowFields</a></dt>
<dd>
<div class="block">设置后返回子POI信息children相关信息</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/ServiceSettings.html#CHINESE">CHINESE</a></span> - 类 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/ServiceSettings.html" title="com.amap.api.services.core中的类">ServiceSettings</a></dt>
<dd>
<div class="block">中文</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearch.html#CHINESE">CHINESE</a></span> - 类 中的静态变量com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearch.html" title="com.amap.api.services.poisearch中的类">PoiSearch</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">语言设置常量。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearchV2.html#CHINESE">CHINESE</a></span> - 类 中的静态变量com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearchV2.html" title="com.amap.api.services.poisearch中的类">PoiSearchV2</a></dt>
<dd>
<div class="block">语言设置常量。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.ShowFields.html#CITIES">CITIES</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.ShowFields.html" title="com.amap.api.services.route中的类">RouteSearchV2.ShowFields</a></dt>
<dd>
<div class="block">设置后可返回分段途径城市信息</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/nearby/NearbySearch.html#clearUserInfoAsyn--">clearUserInfoAsyn()</a></span> - 类 中的方法com.amap.api.services.nearby.<a href="../com/amap/api/services/nearby/NearbySearch.html" title="com.amap.api.services.nearby中的类">NearbySearch</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">清除当前用户的位置信息，异步方法，与自动上传信息方法互斥。</div>
</dd>
<dt><a href="../com/amap/api/services/cloud/CloudImage.html" title="com.amap.api.services.cloud中的类"><span class="typeNameLink">CloudImage</span></a> - <a href="../com/amap/api/services/cloud/package-summary.html">com.amap.api.services.cloud</a>中的类</dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span>
<div class="block"><span class="deprecationComment">自7.4.0起不再支持云图图片</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/cloud/CloudImage.html#CloudImage-java.lang.String-java.lang.String-java.lang.String-">CloudImage(String, String, String)</a></span> - 类 的构造器com.amap.api.services.cloud.<a href="../com/amap/api/services/cloud/CloudImage.html" title="com.amap.api.services.cloud中的类">CloudImage</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">根据给定的参数构造一个 CloudImage 的新对象。</div>
</dd>
<dt><a href="../com/amap/api/services/cloud/CloudItem.html" title="com.amap.api.services.cloud中的类"><span class="typeNameLink">CloudItem</span></a> - <a href="../com/amap/api/services/cloud/package-summary.html">com.amap.api.services.cloud</a>中的类</dt>
<dd>
<div class="block">此类定义了一个企业地图数据对象。</div>
</dd>
<dt><a href="../com/amap/api/services/cloud/CloudItemDetail.html" title="com.amap.api.services.cloud中的类"><span class="typeNameLink">CloudItemDetail</span></a> - <a href="../com/amap/api/services/cloud/package-summary.html">com.amap.api.services.cloud</a>中的类</dt>
<dd>
<div class="block">该类定义了一个企业地图数据的详细信息。</div>
</dd>
<dt><a href="../com/amap/api/services/cloud/CloudResult.html" title="com.amap.api.services.cloud中的类"><span class="typeNameLink">CloudResult</span></a> - <a href="../com/amap/api/services/cloud/package-summary.html">com.amap.api.services.cloud</a>中的类</dt>
<dd>
<div class="block">企业地图搜索结果类。</div>
</dd>
<dt><a href="../com/amap/api/services/cloud/CloudSearch.html" title="com.amap.api.services.cloud中的类"><span class="typeNameLink">CloudSearch</span></a> - <a href="../com/amap/api/services/cloud/package-summary.html">com.amap.api.services.cloud</a>中的类</dt>
<dd>
<div class="block">企业地图数据进行搜索的“入口”类。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/cloud/CloudSearch.html#CloudSearch-Context-">CloudSearch(Context)</a></span> - 类 的构造器com.amap.api.services.cloud.<a href="../com/amap/api/services/cloud/CloudSearch.html" title="com.amap.api.services.cloud中的类">CloudSearch</a></dt>
<dd>
<div class="block">根据给定的参数构造一个 CloudSearch 的新对象。</div>
</dd>
<dt><a href="../com/amap/api/services/cloud/CloudSearch.OnCloudSearchListener.html" title="com.amap.api.services.cloud中的接口"><span class="typeNameLink">CloudSearch.OnCloudSearchListener</span></a> - <a href="../com/amap/api/services/cloud/package-summary.html">com.amap.api.services.cloud</a>中的接口</dt>
<dd>
<div class="block">本类为企业地图数据搜索结果的异步处理回调接口。</div>
</dd>
<dt><a href="../com/amap/api/services/cloud/CloudSearch.Query.html" title="com.amap.api.services.cloud中的类"><span class="typeNameLink">CloudSearch.Query</span></a> - <a href="../com/amap/api/services/cloud/package-summary.html">com.amap.api.services.cloud</a>中的类</dt>
<dd>
<div class="block">此类定义了搜索的关键字，区域范围。</div>
</dd>
<dt><a href="../com/amap/api/services/cloud/CloudSearch.SearchBound.html" title="com.amap.api.services.cloud中的类"><span class="typeNameLink">CloudSearch.SearchBound</span></a> - <a href="../com/amap/api/services/cloud/package-summary.html">com.amap.api.services.cloud</a>中的类</dt>
<dd>
<div class="block">此类定义了查询范围（圆形、矩形或者多边形），查询返回的企业地图数据的位置在此范围内。</div>
</dd>
<dt><a href="../com/amap/api/services/cloud/CloudSearch.Sortingrules.html" title="com.amap.api.services.cloud中的类"><span class="typeNameLink">CloudSearch.Sortingrules</span></a> - <a href="../com/amap/api/services/cloud/package-summary.html">com.amap.api.services.cloud</a>中的类</dt>
<dd>
<div class="block">此类定义了查询圆形和查询多边形返回数据的排序规则。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#CODE_AMAP_ACCESS_TOO_FREQUENT">CODE_AMAP_ACCESS_TOO_FREQUENT</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">用户访问过于频繁 ErrorCode：1005</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#CODE_AMAP_CLIENT_ERROR_PROTOCOL">CODE_AMAP_CLIENT_ERROR_PROTOCOL</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">协议解析错误 - ProtocolException ErrorCode：1801</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#CODE_AMAP_CLIENT_ERRORCODE_MISSSING">CODE_AMAP_CLIENT_ERRORCODE_MISSSING</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">没有对应的错误 ErrorCode：1800</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#CODE_AMAP_CLIENT_INVALID_PARAMETER">CODE_AMAP_CLIENT_INVALID_PARAMETER</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">无效的参数 - IllegalArgumentException ErrorCode：1901</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#CODE_AMAP_CLIENT_IO_EXCEPTION">CODE_AMAP_CLIENT_IO_EXCEPTION</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">IO 操作异常 - IOException ErrorCode：1902</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#CODE_AMAP_CLIENT_NEARBY_NULL_RESULT">CODE_AMAP_CLIENT_NEARBY_NULL_RESULT</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">NearbyInfo对象为空 ErrorCode：2202</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#CODE_AMAP_CLIENT_NETWORK_EXCEPTION">CODE_AMAP_CLIENT_NETWORK_EXCEPTION</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">http或socket连接失败 - ConnectionException ErrorCode：1806</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#CODE_AMAP_CLIENT_NULLPOINT_EXCEPTION">CODE_AMAP_CLIENT_NULLPOINT_EXCEPTION</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">空指针异常 - NullPointException ErrorCode：1903</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#CODE_AMAP_CLIENT_OVER_KEYWORD_LEN_MAX_COUNT_EXCEPTION">CODE_AMAP_CLIENT_OVER_KEYWORD_LEN_MAX_COUNT_EXCEPTION</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">搜索关键字过长</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#CODE_AMAP_CLIENT_OVER_PASSAREA_ITEM_POINT_COUNT_EXCEPTION">CODE_AMAP_CLIENT_OVER_PASSAREA_ITEM_POINT_COUNT_EXCEPTION</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">避让区域点个数超限</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#CODE_AMAP_CLIENT_OVER_PASSAREA_MAX_AREA_EXCEPTION">CODE_AMAP_CLIENT_OVER_PASSAREA_MAX_AREA_EXCEPTION</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">避让区域大小超限</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#CODE_AMAP_CLIENT_OVER_PASSAREA_MAX_COUNT_EXCEPTION">CODE_AMAP_CLIENT_OVER_PASSAREA_MAX_COUNT_EXCEPTION</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">避让区域个数超限</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#CODE_AMAP_CLIENT_OVER_PASSBY_MAX_COUNT_EXCEPTION">CODE_AMAP_CLIENT_OVER_PASSBY_MAX_COUNT_EXCEPTION</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">途经点个数超限</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#CODE_AMAP_CLIENT_SOCKET_TIMEOUT_EXCEPTION">CODE_AMAP_CLIENT_SOCKET_TIMEOUT_EXCEPTION</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">socket 连接超时 - SocketTimeoutException ErrorCode：1802</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#CODE_AMAP_CLIENT_UNKNOWHOST_EXCEPTION">CODE_AMAP_CLIENT_UNKNOWHOST_EXCEPTION</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">未知主机 - UnKnowHostException ErrorCode：1804</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#CODE_AMAP_CLIENT_UNKNOWN_ERROR">CODE_AMAP_CLIENT_UNKNOWN_ERROR</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">未知错误 ErrorCode：1900</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#CODE_AMAP_CLIENT_UPLOAD_LOCATION_ERROR">CODE_AMAP_CLIENT_UPLOAD_LOCATION_ERROR</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">Point为空，或与前次上传的相同 ErrorCode：2204</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#CODE_AMAP_CLIENT_UPLOAD_TOO_FREQUENT">CODE_AMAP_CLIENT_UPLOAD_TOO_FREQUENT</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">两次单次上传的间隔低于7秒 ErrorCode：2203</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#CODE_AMAP_CLIENT_UPLOADAUTO_STARTED_ERROR">CODE_AMAP_CLIENT_UPLOADAUTO_STARTED_ERROR</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">已开启自动上传 ErrorCode：2200</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#CODE_AMAP_CLIENT_URL_EXCEPTION">CODE_AMAP_CLIENT_URL_EXCEPTION</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">url异常 - MalformedURLException ErrorCode：1803</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#CODE_AMAP_CLIENT_USERID_ILLEGAL">CODE_AMAP_CLIENT_USERID_ILLEGAL</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">USERID非法 ErrorCode：2201</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#CODE_AMAP_DAILY_QUERY_OVER_LIMIT">CODE_AMAP_DAILY_QUERY_OVER_LIMIT</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">访问已超出日访问量 ErrorCode：1004</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#CODE_AMAP_ENGINE_CONNECT_TIMEOUT">CODE_AMAP_ENGINE_CONNECT_TIMEOUT</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">服务端请求链接超时 ErrorCode：1102</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#CODE_AMAP_ENGINE_RESPONSE_DATA_ERROR">CODE_AMAP_ENGINE_RESPONSE_DATA_ERROR</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">引擎返回数据异常 ErrorCode：1101</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#CODE_AMAP_ENGINE_RESPONSE_ERROR">CODE_AMAP_ENGINE_RESPONSE_ERROR</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">请求服务响应错误 ErrorCode：1100</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#CODE_AMAP_ENGINE_RETURN_TIMEOUT">CODE_AMAP_ENGINE_RETURN_TIMEOUT</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">读取服务结果超时 ErrorCode：1103</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#CODE_AMAP_ENGINE_TABLEID_NOT_EXIST">CODE_AMAP_ENGINE_TABLEID_NOT_EXIST</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">key对应的tableID不存在 ErrorCode：2003</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#CODE_AMAP_ID_NOT_EXIST">CODE_AMAP_ID_NOT_EXIST</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">ID不存在 ErrorCode：2001</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#CODE_AMAP_INSUFFICIENT_PRIVILEGES">CODE_AMAP_INSUFFICIENT_PRIVILEGES</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">权限不足，服务请求被拒绝 ErrorCode：1012</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#CODE_AMAP_INVALID_USER_DOMAIN">CODE_AMAP_INVALID_USER_DOMAIN</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">用户域名无效 ErrorCode：1007</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#CODE_AMAP_INVALID_USER_IP">CODE_AMAP_INVALID_USER_IP</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">用户IP无效 ErrorCode：1006</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#CODE_AMAP_INVALID_USER_KEY">CODE_AMAP_INVALID_USER_KEY</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">用户key不正确或过期 ErrorCode：1002</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#CODE_AMAP_INVALID_USER_SCODE">CODE_AMAP_INVALID_USER_SCODE</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">用户MD5安全码未通过 ErrorCode：1008</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#CODE_AMAP_IP_QUERY_OVER_LIMIT">CODE_AMAP_IP_QUERY_OVER_LIMIT</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">IP访问超限 ErrorCode：1010</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#CODE_AMAP_NEARBY_INVALID_USERID">CODE_AMAP_NEARBY_INVALID_USERID</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">找不到对应的userid信息,请检查您提供的userid是否存在 ErrorCode：2100</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#CODE_AMAP_NEARBY_KEY_NOT_BIND">CODE_AMAP_NEARBY_KEY_NOT_BIND</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">App key未开通“附近”功能,请注册附近KEY ErrorCode：2101</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#CODE_AMAP_NOT_SUPPORT_HTTPS">CODE_AMAP_NOT_SUPPORT_HTTPS</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">服务不支持https请求 ErrorCode：1011</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#CODE_AMAP_OVER_DIRECTION_RANGE">CODE_AMAP_OVER_DIRECTION_RANGE</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">起点终点距离过长 ErrorCode：3003</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#CODE_AMAP_ROUTE_FAIL">CODE_AMAP_ROUTE_FAIL</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">路线计算失败，通常是由于道路连通关系导致 ErrorCode：3002</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#CODE_AMAP_ROUTE_NO_ROADS_NEARBY">CODE_AMAP_ROUTE_NO_ROADS_NEARBY</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">规划点（起点、终点、途经点）附近搜不到路 ErrorCode：3001</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#CODE_AMAP_ROUTE_OUT_OF_SERVICE">CODE_AMAP_ROUTE_OUT_OF_SERVICE</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">规划点（包括起点、终点、途经点）不在中国陆地范围内 ErrorCode：3000</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#CODE_AMAP_SERVICE_ILLEGAL_REQUEST">CODE_AMAP_SERVICE_ILLEGAL_REQUEST</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">请求协议非法 ErrorCode：1202</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#CODE_AMAP_SERVICE_INVALID_PARAMS">CODE_AMAP_SERVICE_INVALID_PARAMS</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">请求参数非法 ErrorCode：1200</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#CODE_AMAP_SERVICE_MAINTENANCE">CODE_AMAP_SERVICE_MAINTENANCE</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">服务器维护中 ErrorCode：2002</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#CODE_AMAP_SERVICE_MISSING_REQUIRED_PARAMS">CODE_AMAP_SERVICE_MISSING_REQUIRED_PARAMS</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">缺少必填参数 ErrorCode：1201</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#CODE_AMAP_SERVICE_NOT_AVAILBALE">CODE_AMAP_SERVICE_NOT_AVAILBALE</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">请求服务不存在 ErrorCode：1003</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#CODE_AMAP_SERVICE_TABLEID_NOT_EXIST">CODE_AMAP_SERVICE_TABLEID_NOT_EXIST</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">tableID格式不正确不存在 ErrorCode：2000</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#CODE_AMAP_SERVICE_UNKNOWN_ERROR">CODE_AMAP_SERVICE_UNKNOWN_ERROR</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">其他未知错误 ErrorCode：1203</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#CODE_AMAP_SHARE_FAILURE">CODE_AMAP_SHARE_FAILURE</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">短串请求失败 ErrorCode：4001</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#CODE_AMAP_SHARE_LICENSE_IS_EXPIRED">CODE_AMAP_SHARE_LICENSE_IS_EXPIRED</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">短串分享认证失败 ErrorCode：4000</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#CODE_AMAP_SHARE_SIGNATURE_FAILURE">CODE_AMAP_SHARE_SIGNATURE_FAILURE</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">短串分享用户签名未通过 ErrorCode：4002</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#CODE_AMAP_SIGNATURE_ERROR">CODE_AMAP_SIGNATURE_ERROR</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">用户签名未通过 ErrorCode：1001</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#CODE_AMAP_SUCCESS">CODE_AMAP_SUCCESS</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">搜索成功 用 1000 表示。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#CODE_AMAP_USER_KEY_RECYCLED">CODE_AMAP_USER_KEY_RECYCLED</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">开发者删除了key，key被删除后无法正常使用 ErrorCode：1013</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#CODE_AMAP_USERKEY_PLAT_NOMATCH">CODE_AMAP_USERKEY_PLAT_NOMATCH</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">请求key与绑定平台不符 ErrorCode：1009</div>
</dd>
<dt><a href="../com/amap/api/services/busline/package-summary.html">com.amap.api.services.busline</a> - 程序包 com.amap.api.services.busline</dt>
<dd>
<div class="block">
公交线路和公交站点查询包。</div>
</dd>
<dt><a href="../com/amap/api/services/cloud/package-summary.html">com.amap.api.services.cloud</a> - 程序包 com.amap.api.services.cloud</dt>
<dd>
<div class="block">
云检索包，包含了自有数据搜索查询的功能。</div>
</dd>
<dt><a href="../com/amap/api/services/core/package-summary.html">com.amap.api.services.core</a> - 程序包 com.amap.api.services.core</dt>
<dd>
<div class="block">
工具类包。</div>
</dd>
<dt><a href="../com/amap/api/services/district/package-summary.html">com.amap.api.services.district</a> - 程序包 com.amap.api.services.district</dt>
<dd>
<div class="block">
行政区划查询包，查询某个行政级别的详细信息。</div>
</dd>
<dt><a href="../com/amap/api/services/geocoder/package-summary.html">com.amap.api.services.geocoder</a> - 程序包 com.amap.api.services.geocoder</dt>
<dd>
<div class="block">
地理编码包，用以实现通过地理编码进行查询的功能。</div>
</dd>
<dt><a href="../com/amap/api/services/help/package-summary.html">com.amap.api.services.help</a> - 程序包 com.amap.api.services.help</dt>
<dd>
<div class="block">
服务帮助包，用来实现查询过程中提供的提示帮助。</div>
</dd>
<dt><a href="../com/amap/api/services/nearby/package-summary.html">com.amap.api.services.nearby</a> - 程序包 com.amap.api.services.nearby</dt>
<dd>
<div class="block">
附近派单功能包，包含上传位置信息、检索位置信息等 自7.4.0起不再支持附件功能
</div>
</dd>
<dt><a href="../com/amap/api/services/poisearch/package-summary.html">com.amap.api.services.poisearch</a> - 程序包 com.amap.api.services.poisearch</dt>
<dd>
<div class="block">
POI查询包，包含了兴趣点的详细信息。</div>
</dd>
<dt><a href="../com/amap/api/services/road/package-summary.html">com.amap.api.services.road</a> - 程序包 com.amap.api.services.road</dt>
<dd>
<div class="block">
道路查询包，包含了道路查询结果的详细信息。</div>
</dd>
<dt><a href="../com/amap/api/services/route/package-summary.html">com.amap.api.services.route</a> - 程序包 com.amap.api.services.route</dt>
<dd>
<div class="block">
路径查询包，包含了在公交、自驾或步行路线规划中的详细信息。</div>
</dd>
<dt><a href="../com/amap/api/services/routepoisearch/package-summary.html">com.amap.api.services.routepoisearch</a> - 程序包 com.amap.api.services.routepoisearch</dt>
<dd>
<div class="block">
沿途POI搜索包，搜索驾车沿途的POI信息。</div>
</dd>
<dt><a href="../com/amap/api/services/share/package-summary.html">com.amap.api.services.share</a> - 程序包 com.amap.api.services.share</dt>
<dd>
<div class="block">
短串分享包，包含了位置/POI/路径规划/导航的分享功能。</div>
</dd>
<dt><a href="../com/amap/api/services/weather/package-summary.html">com.amap.api.services.weather</a> - 程序包 com.amap.api.services.weather</dt>
<dd>
<div class="block">
天气查询包，包含了实况天气和预报天气的详细信息。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/LatLonPoint.html#copy--">copy()</a></span> - 类 中的方法com.amap.api.services.core.<a href="../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a></dt>
<dd>
<div class="block">复制一个经纬度点对象。</div>
</dd>
<dt><a href="../com/amap/api/services/route/Cost.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">Cost</span></a> - <a href="../com/amap/api/services/route/package-summary.html">com.amap.api.services.route</a>中的类</dt>
<dd>
<div class="block">路线耗时等信息</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/Cost.html#Cost--">Cost()</a></span> - 类 的构造器com.amap.api.services.route.<a href="../com/amap/api/services/route/Cost.html" title="com.amap.api.services.route中的类">Cost</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.ShowFields.html#COST">COST</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.ShowFields.html" title="com.amap.api.services.route中的类">RouteSearchV2.ShowFields</a></dt>
<dd>
<div class="block">设置后可返回方案所需时间及费用成本</div>
</dd>
<dt><a href="../com/amap/api/services/road/Crossroad.html" title="com.amap.api.services.road中的类"><span class="typeNameLink">Crossroad</span></a> - <a href="../com/amap/api/services/road/package-summary.html">com.amap.api.services.road</a>中的类</dt>
<dd>
<div class="block">逆地理编码返回的结果的交叉路口对象。</div>
</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">K</a>&nbsp;<a href="index-11.html">L</a>&nbsp;<a href="index-12.html">M</a>&nbsp;<a href="index-13.html">N</a>&nbsp;<a href="index-14.html">O</a>&nbsp;<a href="index-15.html">P</a>&nbsp;<a href="index-16.html">Q</a>&nbsp;<a href="index-17.html">R</a>&nbsp;<a href="index-18.html">S</a>&nbsp;<a href="index-19.html">T</a>&nbsp;<a href="index-20.html">U</a>&nbsp;<a href="index-21.html">V</a>&nbsp;<a href="index-22.html">W</a>&nbsp;</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-2.html">上一个字母</a></li>
<li><a href="index-4.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-3.html" target="_top">框架</a></li>
<li><a href="index-3.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
