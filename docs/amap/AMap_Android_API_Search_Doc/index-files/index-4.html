<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>D - 索引</title>
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="D - \u7D22\u5F15";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-3.html">上一个字母</a></li>
<li><a href="index-5.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-4.html" target="_top">框架</a></li>
<li><a href="index-4.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="contentContainer"><a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">K</a>&nbsp;<a href="index-11.html">L</a>&nbsp;<a href="index-12.html">M</a>&nbsp;<a href="index-13.html">N</a>&nbsp;<a href="index-14.html">O</a>&nbsp;<a href="index-15.html">P</a>&nbsp;<a href="index-16.html">Q</a>&nbsp;<a href="index-17.html">R</a>&nbsp;<a href="index-18.html">S</a>&nbsp;<a href="index-19.html">T</a>&nbsp;<a href="index-20.html">U</a>&nbsp;<a href="index-21.html">V</a>&nbsp;<a href="index-22.html">W</a>&nbsp;<a name="I:D">
<!--   -->
</a>
<h2 class="title">D</h2>
<dl>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearchV2.ShowFields.html#DEFAULT">DEFAULT</a></span> - 类 中的静态变量com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearchV2.ShowFields.html" title="com.amap.api.services.poisearch中的类">PoiSearchV2.ShowFields</a></dt>
<dd>
<div class="block">默认值，只返回基础类</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/nearby/NearbySearch.html#destroy--">destroy()</a></span> - 类 中的静态方法com.amap.api.services.nearby.<a href="../com/amap/api/services/nearby/NearbySearch.html" title="com.amap.api.services.nearby中的类">NearbySearch</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">释放NearbySearch单例对象。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/cloud/CloudSearch.Sortingrules.html#DISTANCE">DISTANCE</a></span> - 类 中的静态变量com.amap.api.services.cloud.<a href="../com/amap/api/services/cloud/CloudSearch.Sortingrules.html" title="com.amap.api.services.cloud中的类">CloudSearch.Sortingrules</a></dt>
<dd>
<div class="block">距离排序</div>
</dd>
<dt><a href="../com/amap/api/services/route/DistanceItem.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">DistanceItem</span></a> - <a href="../com/amap/api/services/route/package-summary.html">com.amap.api.services.route</a>中的类</dt>
<dd>
<div class="block">距离测量返回结果中某一个结果的具体信息</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/DistanceSearch.DistanceQuery.html#DistanceQuery--">DistanceQuery()</a></span> - 类 的构造器com.amap.api.services.route.<a href="../com/amap/api/services/route/DistanceSearch.DistanceQuery.html" title="com.amap.api.services.route中的类">DistanceSearch.DistanceQuery</a></dt>
<dd>
<div class="block">距离测量属性设置构造函数。</div>
</dd>
<dt><a href="../com/amap/api/services/route/DistanceResult.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">DistanceResult</span></a> - <a href="../com/amap/api/services/route/package-summary.html">com.amap.api.services.route</a>中的类</dt>
<dd>
<div class="block">距离测量返回结果</div>
</dd>
<dt><a href="../com/amap/api/services/route/DistanceSearch.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">DistanceSearch</span></a> - <a href="../com/amap/api/services/route/package-summary.html">com.amap.api.services.route</a>中的类</dt>
<dd>
<div class="block">距离测量，通过两点计算直线距离或者驾车导航距离，相比路径规划返回内容较少，适合频繁调用场景。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/DistanceSearch.html#DistanceSearch-Context-">DistanceSearch(Context)</a></span> - 类 的构造器com.amap.api.services.route.<a href="../com/amap/api/services/route/DistanceSearch.html" title="com.amap.api.services.route中的类">DistanceSearch</a></dt>
<dd>
<div class="block">距离测量搜索的构造函数</div>
</dd>
<dt><a href="../com/amap/api/services/route/DistanceSearch.DistanceQuery.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">DistanceSearch.DistanceQuery</span></a> - <a href="../com/amap/api/services/route/package-summary.html">com.amap.api.services.route</a>中的类</dt>
<dd>
<div class="block">距离测量属性设置类，其中包含起终点以及测量类型的设置。</div>
</dd>
<dt><a href="../com/amap/api/services/route/DistanceSearch.OnDistanceSearchListener.html" title="com.amap.api.services.route中的接口"><span class="typeNameLink">DistanceSearch.OnDistanceSearchListener</span></a> - <a href="../com/amap/api/services/route/package-summary.html">com.amap.api.services.route</a>中的接口</dt>
<dd>
<div class="block">距离测量异步请求回调。</div>
</dd>
<dt><a href="../com/amap/api/services/route/District.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">District</span></a> - <a href="../com/amap/api/services/route/package-summary.html">com.amap.api.services.route</a>中的类</dt>
<dd>
<div class="block">此类定义搜索返回行政区的名称和编码。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/district/DistrictItem.html#districtBoundary--">districtBoundary()</a></span> - 类 中的方法com.amap.api.services.district.<a href="../com/amap/api/services/district/DistrictItem.html" title="com.amap.api.services.district中的类">DistrictItem</a></dt>
<dd>
<div class="block">以字符串数组形式返回行政区划边界值。</div>
</dd>
<dt><a href="../com/amap/api/services/district/DistrictItem.html" title="com.amap.api.services.district中的类"><span class="typeNameLink">DistrictItem</span></a> - <a href="../com/amap/api/services/district/package-summary.html">com.amap.api.services.district</a>中的类</dt>
<dd>
<div class="block">行政区信息类。</div>
</dd>
<dt><a href="../com/amap/api/services/district/DistrictResult.html" title="com.amap.api.services.district中的类"><span class="typeNameLink">DistrictResult</span></a> - <a href="../com/amap/api/services/district/package-summary.html">com.amap.api.services.district</a>中的类</dt>
<dd>
<div class="block">行政区域查询结果类。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/district/DistrictResult.html#DistrictResult-com.amap.api.services.district.DistrictSearchQuery-java.util.ArrayList-">DistrictResult(DistrictSearchQuery, ArrayList&lt;DistrictItem&gt;)</a></span> - 类 的构造器com.amap.api.services.district.<a href="../com/amap/api/services/district/DistrictResult.html" title="com.amap.api.services.district中的类">DistrictResult</a></dt>
<dd>
<div class="block">依据参数构造行政区域查询结果</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/district/DistrictResult.html#DistrictResult--">DistrictResult()</a></span> - 类 的构造器com.amap.api.services.district.<a href="../com/amap/api/services/district/DistrictResult.html" title="com.amap.api.services.district中的类">DistrictResult</a></dt>
<dd>
<div class="block">构造一个行政区划查询结果类</div>
</dd>
<dt><a href="../com/amap/api/services/district/DistrictSearch.html" title="com.amap.api.services.district中的类"><span class="typeNameLink">DistrictSearch</span></a> - <a href="../com/amap/api/services/district/package-summary.html">com.amap.api.services.district</a>中的类</dt>
<dd>
<div class="block">行政区域查询类。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/district/DistrictSearch.html#DistrictSearch-Context-">DistrictSearch(Context)</a></span> - 类 的构造器com.amap.api.services.district.<a href="../com/amap/api/services/district/DistrictSearch.html" title="com.amap.api.services.district中的类">DistrictSearch</a></dt>
<dd>
<div class="block">根据给定的参数构造一个 DistrictSearch 的新对象。</div>
</dd>
<dt><a href="../com/amap/api/services/district/DistrictSearch.OnDistrictSearchListener.html" title="com.amap.api.services.district中的接口"><span class="typeNameLink">DistrictSearch.OnDistrictSearchListener</span></a> - <a href="../com/amap/api/services/district/package-summary.html">com.amap.api.services.district</a>中的接口</dt>
<dd>
<div class="block">本类为 District（行政区域）搜索结果的异步处理回调接口。</div>
</dd>
<dt><a href="../com/amap/api/services/district/DistrictSearchQuery.html" title="com.amap.api.services.district中的类"><span class="typeNameLink">DistrictSearchQuery</span></a> - <a href="../com/amap/api/services/district/package-summary.html">com.amap.api.services.district</a>中的类</dt>
<dd>
<div class="block">此类定义了行政区划搜索的参数。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/district/DistrictSearchQuery.html#DistrictSearchQuery--">DistrictSearchQuery()</a></span> - 类 的构造器com.amap.api.services.district.<a href="../com/amap/api/services/district/DistrictSearchQuery.html" title="com.amap.api.services.district中的类">DistrictSearchQuery</a></dt>
<dd>
<div class="block">构造 DistrictSearchQuery 对象。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/district/DistrictSearchQuery.html#DistrictSearchQuery-java.lang.String-java.lang.String-int-">DistrictSearchQuery(String, String, int)</a></span> - 类 的构造器com.amap.api.services.district.<a href="../com/amap/api/services/district/DistrictSearchQuery.html" title="com.amap.api.services.district中的类">DistrictSearchQuery</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span>
<div class="block"><span class="deprecationComment">自V3.6.1废除此接口，建议用 <a href="../com/amap/api/services/district/DistrictSearchQuery.html#DistrictSearchQuery--"><code>DistrictSearchQuery.DistrictSearchQuery()</code></a>  构造。</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/district/DistrictSearchQuery.html#DistrictSearchQuery-java.lang.String-java.lang.String-int-boolean-int-">DistrictSearchQuery(String, String, int, boolean, int)</a></span> - 类 的构造器com.amap.api.services.district.<a href="../com/amap/api/services/district/DistrictSearchQuery.html" title="com.amap.api.services.district中的类">DistrictSearchQuery</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span>
<div class="block"><span class="deprecationComment">自V3.6.1废除此接口，建议用 <a href="../com/amap/api/services/district/DistrictSearchQuery.html#DistrictSearchQuery--"><code>DistrictSearchQuery.DistrictSearchQuery()</code></a> 构造。</span></div>
</div>
</dd>
<dt><a href="../com/amap/api/services/route/Doorway.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">Doorway</span></a> - <a href="../com/amap/api/services/route/package-summary.html">com.amap.api.services.route</a>中的类</dt>
<dd>
<div class="block">定义了公交换乘路径规划的一个换乘点的出入口信息。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#DRIVEING_PLAN_AVOID_CONGESTION_CHOICE_HIGHWAY">DRIVEING_PLAN_AVOID_CONGESTION_CHOICE_HIGHWAY</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block">返回的结果会优先考虑高速路，并且会考虑路况躲避拥堵，与高德地图的“躲避拥堵&高速优先”策略一致</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#DRIVEING_PLAN_AVOID_CONGESTION_FASTEST_SAVE_MONEY">DRIVEING_PLAN_AVOID_CONGESTION_FASTEST_SAVE_MONEY</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block">避让拥堵&速度优先&避免收费</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#DRIVEING_PLAN_AVOID_CONGESTION_NO_HIGHWAY">DRIVEING_PLAN_AVOID_CONGESTION_NO_HIGHWAY</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block">返回的结果考虑路况，尽量躲避拥堵而规划路径，并且不走高速，与高德地图的“躲避拥堵&不走高速”策略一致</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#DRIVEING_PLAN_AVOID_CONGESTION_SAVE_MONEY">DRIVEING_PLAN_AVOID_CONGESTION_SAVE_MONEY</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block">返回路径规划结果会尽量的躲避拥堵，并且规划收费较低甚至免费的路径结果，与高德地图的“躲避拥堵&避免收费”策略一致</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#DRIVEING_PLAN_AVOID_CONGESTION_SAVE_MONEY_NO_HIGHWAY">DRIVEING_PLAN_AVOID_CONGESTION_SAVE_MONEY_NO_HIGHWAY</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block">返回的结果尽量躲避拥堵，规划收费较低甚至免费的路径结果，并且尽量不走高速路，与高德地图的“避免拥堵&避免收费&不走高速”策略一致</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#DRIVEING_PLAN_CHOICE_HIGHWAY">DRIVEING_PLAN_CHOICE_HIGHWAY</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block">返回的结果会优先选择高速路，与高德地图的“高速优先”策略一致</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#DRIVEING_PLAN_DEFAULT">DRIVEING_PLAN_DEFAULT</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block">返回的结果考虑路况，尽量躲避拥堵而规划路径，与高德地图的“躲避拥堵”策略一致</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#DRIVEING_PLAN_FASTEST_SHORTEST">DRIVEING_PLAN_FASTEST_SHORTEST</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block">不考虑路况，返回速度最优、耗时最短的路</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#DRIVEING_PLAN_NO_HIGHWAY">DRIVEING_PLAN_NO_HIGHWAY</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block">返回的结果不走高速，与高德地图“不走高速”策略一致</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#DRIVEING_PLAN_SAVE_MONEY">DRIVEING_PLAN_SAVE_MONEY</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block">返回的结果尽可能规划收费较低甚至免费的路径，与高德地图“避免收费”策略一致</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#DRIVEING_PLAN_SAVE_MONEY_NO_HIGHWAY">DRIVEING_PLAN_SAVE_MONEY_NO_HIGHWAY</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block">返回的结果尽量不走高速，并且尽量规划收费较低甚至免费的路径结果，与高德地图的“避免收费&不走高速”策略一致</div>
</dd>
<dt><a href="../com/amap/api/services/route/DrivePath.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">DrivePath</span></a> - <a href="../com/amap/api/services/route/package-summary.html">com.amap.api.services.route</a>中的类</dt>
<dd>
<div class="block">定义了驾车路径规划的一个方案。</div>
</dd>
<dt><a href="../com/amap/api/services/route/DrivePathV2.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">DrivePathV2</span></a> - <a href="../com/amap/api/services/route/package-summary.html">com.amap.api.services.route</a>中的类</dt>
<dd>
<div class="block">定义了驾车路径规划的一个方案。</div>
</dd>
<dt><a href="../com/amap/api/services/route/DrivePlanPath.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">DrivePlanPath</span></a> - <a href="../com/amap/api/services/route/package-summary.html">com.amap.api.services.route</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.DrivePlanQuery.html#DrivePlanQuery-com.amap.api.services.route.RouteSearch.FromAndTo-int-int-int-">DrivePlanQuery(RouteSearch.FromAndTo, int, int, int)</a></span> - 类 的构造器com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.DrivePlanQuery.html" title="com.amap.api.services.route中的类">RouteSearch.DrivePlanQuery</a></dt>
<dd>
<div class="block">DriveRouteQuery构造函数。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.DrivePlanQuery.html#DrivePlanQuery--">DrivePlanQuery()</a></span> - 类 的构造器com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.DrivePlanQuery.html" title="com.amap.api.services.route中的类">RouteSearch.DrivePlanQuery</a></dt>
<dd>
<div class="block">DriveRouteQuery构造函数</div>
</dd>
<dt><a href="../com/amap/api/services/route/DrivePlanStep.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">DrivePlanStep</span></a> - <a href="../com/amap/api/services/route/package-summary.html">com.amap.api.services.route</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><a href="../com/amap/api/services/route/DriveRoutePlanResult.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">DriveRoutePlanResult</span></a> - <a href="../com/amap/api/services/route/package-summary.html">com.amap.api.services.route</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.DriveRouteQuery.html#DriveRouteQuery-com.amap.api.services.route.RouteSearch.FromAndTo-int-java.util.List-java.util.List-java.lang.String-">DriveRouteQuery(RouteSearch.FromAndTo, int, List&lt;LatLonPoint&gt;, List&lt;List&lt;LatLonPoint&gt;&gt;, String)</a></span> - 类 的构造器com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.DriveRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearch.DriveRouteQuery</a></dt>
<dd>
<div class="block">DriveRouteQuery构造函数。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.DriveRouteQuery.html#DriveRouteQuery-Parcel-">DriveRouteQuery(Parcel)</a></span> - 类 的构造器com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.DriveRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearch.DriveRouteQuery</a></dt>
<dd>
<div class="block">序列化实现</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.DriveRouteQuery.html#DriveRouteQuery--">DriveRouteQuery()</a></span> - 类 的构造器com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.DriveRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearch.DriveRouteQuery</a></dt>
<dd>
<div class="block">DriveRouteQuery构造函数</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.DriveRouteQuery.html#DriveRouteQuery-com.amap.api.services.route.RouteSearchV2.FromAndTo-com.amap.api.services.route.RouteSearchV2.DrivingStrategy-java.util.List-java.util.List-java.lang.String-">DriveRouteQuery(RouteSearchV2.FromAndTo, RouteSearchV2.DrivingStrategy, List&lt;LatLonPoint&gt;, List&lt;List&lt;LatLonPoint&gt;&gt;, String)</a></span> - 类 的构造器com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.DriveRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearchV2.DriveRouteQuery</a></dt>
<dd>
<div class="block">DriveRouteQuery构造函数。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.DriveRouteQuery.html#DriveRouteQuery-Parcel-">DriveRouteQuery(Parcel)</a></span> - 类 的构造器com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.DriveRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearchV2.DriveRouteQuery</a></dt>
<dd>
<div class="block">序列化实现</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.DriveRouteQuery.html#DriveRouteQuery--">DriveRouteQuery()</a></span> - 类 的构造器com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.DriveRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearchV2.DriveRouteQuery</a></dt>
<dd>
<div class="block">DriveRouteQuery构造函数</div>
</dd>
<dt><a href="../com/amap/api/services/route/DriveRouteResult.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">DriveRouteResult</span></a> - <a href="../com/amap/api/services/route/package-summary.html">com.amap.api.services.route</a>中的类</dt>
<dd>
<div class="block">定义了驾车路径规划的结果集。</div>
</dd>
<dt><a href="../com/amap/api/services/route/DriveRouteResultV2.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">DriveRouteResultV2</span></a> - <a href="../com/amap/api/services/route/package-summary.html">com.amap.api.services.route</a>中的类</dt>
<dd>
<div class="block">定义了驾车路径规划的结果集。</div>
</dd>
<dt><a href="../com/amap/api/services/route/DriveStep.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">DriveStep</span></a> - <a href="../com/amap/api/services/route/package-summary.html">com.amap.api.services.route</a>中的类</dt>
<dd>
<div class="block">定义了驾车路径规划的一个路段。</div>
</dd>
<dt><a href="../com/amap/api/services/route/DriveStepV2.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">DriveStepV2</span></a> - <a href="../com/amap/api/services/route/package-summary.html">com.amap.api.services.route</a>中的类</dt>
<dd>
<div class="block">定义了驾车路径规划的一个路段。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#DRIVING_EXCLUDE_FERRY">DRIVING_EXCLUDE_FERRY</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block">海外生效 规避道路类型-渡船</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#DRIVING_EXCLUDE_MOTORWAY">DRIVING_EXCLUDE_MOTORWAY</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block">海外生效 规避道路类型-高速路</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#DRIVING_EXCLUDE_TOLL">DRIVING_EXCLUDE_TOLL</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block">海外生效 规避道路类型-收费道路</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#DRIVING_MULTI_CHOICE_AVOID_CONGESTION">DRIVING_MULTI_CHOICE_AVOID_CONGESTION</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block">多备选，躲避拥堵（考虑路况）</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#DRIVING_MULTI_CHOICE_AVOID_CONGESTION_NO_HIGHWAY">DRIVING_MULTI_CHOICE_AVOID_CONGESTION_NO_HIGHWAY</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block">多备选，躲避拥堵，不走高速（考虑路况）</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#DRIVING_MULTI_CHOICE_AVOID_CONGESTION_NO_HIGHWAY_SAVE_MONEY">DRIVING_MULTI_CHOICE_AVOID_CONGESTION_NO_HIGHWAY_SAVE_MONEY</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block">多备选，躲避拥堵，不走高速，费用优先（考虑路况）</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#DRIVING_MULTI_CHOICE_AVOID_CONGESTION_SAVE_MONEY">DRIVING_MULTI_CHOICE_AVOID_CONGESTION_SAVE_MONEY</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block">多备选，躲避拥堵，费用优先（考虑路况）</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#DRIVING_MULTI_CHOICE_HIGHWAY">DRIVING_MULTI_CHOICE_HIGHWAY</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block">多备选，高速优先</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#DRIVING_MULTI_CHOICE_HIGHWAY_AVOID_CONGESTION">DRIVING_MULTI_CHOICE_HIGHWAY_AVOID_CONGESTION</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block">多备选，高速优先，躲避拥堵（考虑路况）</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#DRIVING_MULTI_CHOICE_NO_HIGHWAY">DRIVING_MULTI_CHOICE_NO_HIGHWAY</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block">多备选，不走高速</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#DRIVING_MULTI_CHOICE_SAVE_MONEY">DRIVING_MULTI_CHOICE_SAVE_MONEY</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block">多备选，费用优先</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#DRIVING_MULTI_CHOICE_SAVE_MONEY_NO_HIGHWAY">DRIVING_MULTI_CHOICE_SAVE_MONEY_NO_HIGHWAY</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block">多备选，费用有限，不走高速</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#DRIVING_MULTI_STRATEGY_FASTEST_SAVE_MONEY_SHORTEST">DRIVING_MULTI_STRATEGY_FASTEST_SAVE_MONEY_SHORTEST</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block">同时使用速度优先、费用优先、距离优先三个策略计算路径。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#DRIVING_MULTI_STRATEGY_FASTEST_SHORTEST">DRIVING_MULTI_STRATEGY_FASTEST_SHORTEST</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block">多备选，时间最短，距离最短</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#DRIVING_MULTI_STRATEGY_FASTEST_SHORTEST_AVOID_CONGESTION">DRIVING_MULTI_STRATEGY_FASTEST_SHORTEST_AVOID_CONGESTION</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block">多备选，时间最短，距离最短，躲避拥堵（考虑路况）</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#DRIVING_NORMAL_CAR">DRIVING_NORMAL_CAR</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block">普通汽车</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#DRIVING_PLUGIN_HYBRID_CAR">DRIVING_PLUGIN_HYBRID_CAR</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block">插电混动车</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#DRIVING_PURE_ELECTRIC_VEHICLE">DRIVING_PURE_ELECTRIC_VEHICLE</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block">纯电动车</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#DRIVING_SINGLE_AVOID_CONGESTION">DRIVING_SINGLE_AVOID_CONGESTION</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block">避免拥堵。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#DRIVING_SINGLE_DEFAULT">DRIVING_SINGLE_DEFAULT</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block">速度优先</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#DRIVING_SINGLE_NO_EXPRESSWAYS">DRIVING_SINGLE_NO_EXPRESSWAYS</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block">不走快速路。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#DRIVING_SINGLE_NO_HIGHWAY">DRIVING_SINGLE_NO_HIGHWAY</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block">不走高速。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#DRIVING_SINGLE_NO_HIGHWAY_SAVE_MONEY">DRIVING_SINGLE_NO_HIGHWAY_SAVE_MONEY</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block">不走高速且避免收费。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#DRIVING_SINGLE_NO_HIGHWAY_SAVE_MONEY_AVOID_CONGESTION">DRIVING_SINGLE_NO_HIGHWAY_SAVE_MONEY_AVOID_CONGESTION</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block">不走高速且躲避收费和拥堵。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#DRIVING_SINGLE_SAVE_MONEY">DRIVING_SINGLE_SAVE_MONEY</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block">费用优先（不走收费路的最快道路）。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#DRIVING_SINGLE_SAVE_MONEY_AVOID_CONGESTION">DRIVING_SINGLE_SAVE_MONEY_AVOID_CONGESTION</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block">避免收费与拥堵。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#DRIVING_SINGLE_SHORTEST">DRIVING_SINGLE_SHORTEST</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block">距离优先。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#DrivingAvoidCongestion">DrivingAvoidCongestion</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span>
<div class="block"><span class="deprecationComment">自V4.0.0以后，请参考 <a href="../com/amap/api/services/route/RouteSearch.html#DRIVING_SINGLE_AVOID_CONGESTION"><code>RouteSearch.DRIVING_SINGLE_AVOID_CONGESTION</code></a></span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/routepoisearch/RoutePOISearch.html#DrivingAvoidCongestion">DrivingAvoidCongestion</a></span> - 类 中的静态变量com.amap.api.services.routepoisearch.<a href="../com/amap/api/services/routepoisearch/RoutePOISearch.html" title="com.amap.api.services.routepoisearch中的类">RoutePOISearch</a></dt>
<dd>
<div class="block">避免拥堵。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/share/ShareSearch.html#DrivingAvoidCongestion">DrivingAvoidCongestion</a></span> - 类 中的静态变量com.amap.api.services.share.<a href="../com/amap/api/services/share/ShareSearch.html" title="com.amap.api.services.share中的类">ShareSearch</a></dt>
<dd>
<div class="block">躲避拥堵（驾车路径规划）</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#DrivingDefault">DrivingDefault</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span>
<div class="block"><span class="deprecationComment">自V4.0.0以后，请参考 <a href="../com/amap/api/services/route/RouteSearch.html#DRIVING_SINGLE_DEFAULT"><code>RouteSearch.DRIVING_SINGLE_DEFAULT</code></a></span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/routepoisearch/RoutePOISearch.html#DrivingDefault">DrivingDefault</a></span> - 类 中的静态变量com.amap.api.services.routepoisearch.<a href="../com/amap/api/services/routepoisearch/RoutePOISearch.html" title="com.amap.api.services.routepoisearch中的类">RoutePOISearch</a></dt>
<dd>
<div class="block">速度优先。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/share/ShareSearch.html#DrivingDefault">DrivingDefault</a></span> - 类 中的静态变量com.amap.api.services.share.<a href="../com/amap/api/services/share/ShareSearch.html" title="com.amap.api.services.share中的类">ShareSearch</a></dt>
<dd>
<div class="block">速度最快（驾车路径规划）</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#DrivingMultiStrategy">DrivingMultiStrategy</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span>
<div class="block"><span class="deprecationComment">自V4.0.0以后，请参考 <a href="../com/amap/api/services/route/RouteSearch.html#DRIVING_MULTI_STRATEGY_FASTEST_SAVE_MONEY_SHORTEST"><code>RouteSearch.DRIVING_MULTI_STRATEGY_FASTEST_SAVE_MONEY_SHORTEST</code></a></span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#DrivingNoExpressways">DrivingNoExpressways</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span>
<div class="block"><span class="deprecationComment">自V4.0.0以后，请参考 <a href="../com/amap/api/services/route/RouteSearch.html#DRIVING_SINGLE_NO_EXPRESSWAYS"><code>RouteSearch.DRIVING_SINGLE_NO_EXPRESSWAYS</code></a></span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/routepoisearch/RoutePOISearch.html#DrivingNoExpressways">DrivingNoExpressways</a></span> - 类 中的静态变量com.amap.api.services.routepoisearch.<a href="../com/amap/api/services/routepoisearch/RoutePOISearch.html" title="com.amap.api.services.routepoisearch中的类">RoutePOISearch</a></dt>
<dd>
<div class="block">不走快速路。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#DrivingNoHighAvoidCongestionSaveMoney">DrivingNoHighAvoidCongestionSaveMoney</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span>
<div class="block"><span class="deprecationComment">自V4.0.0以后，请参考 <a href="../com/amap/api/services/route/RouteSearch.html#DRIVING_SINGLE_NO_HIGHWAY_SAVE_MONEY_AVOID_CONGESTION"><code>RouteSearch.DRIVING_SINGLE_NO_HIGHWAY_SAVE_MONEY_AVOID_CONGESTION</code></a></span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/routepoisearch/RoutePOISearch.html#DrivingNoHighAvoidCongestionSaveMoney">DrivingNoHighAvoidCongestionSaveMoney</a></span> - 类 中的静态变量com.amap.api.services.routepoisearch.<a href="../com/amap/api/services/routepoisearch/RoutePOISearch.html" title="com.amap.api.services.routepoisearch中的类">RoutePOISearch</a></dt>
<dd>
<div class="block">不走高速且躲避收费和拥堵。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#DrivingNoHighWay">DrivingNoHighWay</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span>
<div class="block"><span class="deprecationComment">自V4.0.0以后，请参考 <a href="../com/amap/api/services/route/RouteSearch.html#DRIVING_SINGLE_NO_HIGHWAY"><code>RouteSearch.DRIVING_SINGLE_NO_HIGHWAY</code></a></span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/routepoisearch/RoutePOISearch.html#DrivingNoHighWay">DrivingNoHighWay</a></span> - 类 中的静态变量com.amap.api.services.routepoisearch.<a href="../com/amap/api/services/routepoisearch/RoutePOISearch.html" title="com.amap.api.services.routepoisearch中的类">RoutePOISearch</a></dt>
<dd>
<div class="block">不走高速。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/share/ShareSearch.html#DrivingNoHighWay">DrivingNoHighWay</a></span> - 类 中的静态变量com.amap.api.services.share.<a href="../com/amap/api/services/share/ShareSearch.html" title="com.amap.api.services.share中的类">ShareSearch</a></dt>
<dd>
<div class="block">不走高速（驾车路径规划）</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/share/ShareSearch.html#DrivingNoHighWayAvoidCongestion">DrivingNoHighWayAvoidCongestion</a></span> - 类 中的静态变量com.amap.api.services.share.<a href="../com/amap/api/services/share/ShareSearch.html" title="com.amap.api.services.share中的类">ShareSearch</a></dt>
<dd>
<div class="block">不走高速且躲避拥堵（驾车路径规划）</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#DrivingNoHighWaySaveMoney">DrivingNoHighWaySaveMoney</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span>
<div class="block"><span class="deprecationComment">自V4.0.0以后，请参考 <a href="../com/amap/api/services/route/RouteSearch.html#DRIVING_SINGLE_NO_HIGHWAY_SAVE_MONEY"><code>RouteSearch.DRIVING_SINGLE_NO_HIGHWAY_SAVE_MONEY</code></a></span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/routepoisearch/RoutePOISearch.html#DrivingNoHighWaySaveMoney">DrivingNoHighWaySaveMoney</a></span> - 类 中的静态变量com.amap.api.services.routepoisearch.<a href="../com/amap/api/services/routepoisearch/RoutePOISearch.html" title="com.amap.api.services.routepoisearch中的类">RoutePOISearch</a></dt>
<dd>
<div class="block">不走高速且避免收费。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/share/ShareSearch.html#DrivingNoHighWaySaveMoney">DrivingNoHighWaySaveMoney</a></span> - 类 中的静态变量com.amap.api.services.share.<a href="../com/amap/api/services/share/ShareSearch.html" title="com.amap.api.services.share中的类">ShareSearch</a></dt>
<dd>
<div class="block">不走高速且避免收费（驾车路径规划）</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/share/ShareSearch.html#DrivingNoHighWaySaveMoneyAvoidCongestion">DrivingNoHighWaySaveMoneyAvoidCongestion</a></span> - 类 中的静态变量com.amap.api.services.share.<a href="../com/amap/api/services/share/ShareSearch.html" title="com.amap.api.services.share中的类">ShareSearch</a></dt>
<dd>
<div class="block">不走高速躲避收费和拥堵（驾车路径规划）</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#DrivingSaveMoney">DrivingSaveMoney</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span>
<div class="block"><span class="deprecationComment">自V4.0.0以后，请参考 <a href="../com/amap/api/services/route/RouteSearch.html#DRIVING_SINGLE_SAVE_MONEY"><code>RouteSearch.DRIVING_SINGLE_SAVE_MONEY</code></a></span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/routepoisearch/RoutePOISearch.html#DrivingSaveMoney">DrivingSaveMoney</a></span> - 类 中的静态变量com.amap.api.services.routepoisearch.<a href="../com/amap/api/services/routepoisearch/RoutePOISearch.html" title="com.amap.api.services.routepoisearch中的类">RoutePOISearch</a></dt>
<dd>
<div class="block">费用优先（不走收费路的最快道路）。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/share/ShareSearch.html#DrivingSaveMoney">DrivingSaveMoney</a></span> - 类 中的静态变量com.amap.api.services.share.<a href="../com/amap/api/services/share/ShareSearch.html" title="com.amap.api.services.share中的类">ShareSearch</a></dt>
<dd>
<div class="block">费用少（驾车路径规划）</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#DrivingSaveMoneyAvoidCongestion">DrivingSaveMoneyAvoidCongestion</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span>
<div class="block"><span class="deprecationComment">自V4.0.0以后，请参考 <a href="../com/amap/api/services/route/RouteSearch.html#DRIVING_SINGLE_SAVE_MONEY_AVOID_CONGESTION"><code>RouteSearch.DRIVING_SINGLE_SAVE_MONEY_AVOID_CONGESTION</code></a></span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/routepoisearch/RoutePOISearch.html#DrivingSaveMoneyAvoidCongestion">DrivingSaveMoneyAvoidCongestion</a></span> - 类 中的静态变量com.amap.api.services.routepoisearch.<a href="../com/amap/api/services/routepoisearch/RoutePOISearch.html" title="com.amap.api.services.routepoisearch中的类">RoutePOISearch</a></dt>
<dd>
<div class="block">避免收费与拥堵。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/share/ShareSearch.html#DrivingSaveMoneyAvoidCongestion">DrivingSaveMoneyAvoidCongestion</a></span> - 类 中的静态变量com.amap.api.services.share.<a href="../com/amap/api/services/share/ShareSearch.html" title="com.amap.api.services.share中的类">ShareSearch</a></dt>
<dd>
<div class="block">躲避收费和拥堵（驾车路径规划）</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#DrivingShortDistance">DrivingShortDistance</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span>
<div class="block"><span class="deprecationComment">自V4.0.0以后，请参考 <a href="../com/amap/api/services/route/RouteSearch.html#DRIVING_SINGLE_SHORTEST"><code>RouteSearch.DRIVING_SINGLE_SHORTEST</code></a></span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/routepoisearch/RoutePOISearch.html#DrivingShortDistance">DrivingShortDistance</a></span> - 类 中的静态变量com.amap.api.services.routepoisearch.<a href="../com/amap/api/services/routepoisearch/RoutePOISearch.html" title="com.amap.api.services.routepoisearch中的类">RoutePOISearch</a></dt>
<dd>
<div class="block">距离优先。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/share/ShareSearch.html#DrivingShortDistance">DrivingShortDistance</a></span> - 类 中的静态变量com.amap.api.services.share.<a href="../com/amap/api/services/share/ShareSearch.html" title="com.amap.api.services.share中的类">ShareSearch</a></dt>
<dd>
<div class="block">距离优先（驾车路径规划）</div>
</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">K</a>&nbsp;<a href="index-11.html">L</a>&nbsp;<a href="index-12.html">M</a>&nbsp;<a href="index-13.html">N</a>&nbsp;<a href="index-14.html">O</a>&nbsp;<a href="index-15.html">P</a>&nbsp;<a href="index-16.html">Q</a>&nbsp;<a href="index-17.html">R</a>&nbsp;<a href="index-18.html">S</a>&nbsp;<a href="index-19.html">T</a>&nbsp;<a href="index-20.html">U</a>&nbsp;<a href="index-21.html">V</a>&nbsp;<a href="index-22.html">W</a>&nbsp;</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-3.html">上一个字母</a></li>
<li><a href="index-5.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-4.html" target="_top">框架</a></li>
<li><a href="index-4.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
