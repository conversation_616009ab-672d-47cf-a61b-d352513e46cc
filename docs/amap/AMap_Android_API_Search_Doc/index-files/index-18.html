<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>S - 索引</title>
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="S - \u7D22\u5F15";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-17.html">上一个字母</a></li>
<li><a href="index-19.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-18.html" target="_top">框架</a></li>
<li><a href="index-18.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="contentContainer"><a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">K</a>&nbsp;<a href="index-11.html">L</a>&nbsp;<a href="index-12.html">M</a>&nbsp;<a href="index-13.html">N</a>&nbsp;<a href="index-14.html">O</a>&nbsp;<a href="index-15.html">P</a>&nbsp;<a href="index-16.html">Q</a>&nbsp;<a href="index-17.html">R</a>&nbsp;<a href="index-18.html">S</a>&nbsp;<a href="index-19.html">T</a>&nbsp;<a href="index-20.html">U</a>&nbsp;<a href="index-21.html">V</a>&nbsp;<a href="index-22.html">W</a>&nbsp;<a name="I:S">
<!--   -->
</a>
<h2 class="title">S</h2>
<dl>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/cloud/CloudSearch.SearchBound.html#SearchBound-com.amap.api.services.core.LatLonPoint-int-">SearchBound(LatLonPoint, int)</a></span> - 类 的构造器com.amap.api.services.cloud.<a href="../com/amap/api/services/cloud/CloudSearch.SearchBound.html" title="com.amap.api.services.cloud中的类">CloudSearch.SearchBound</a></dt>
<dd>
<div class="block">根据给定的参数来构造一个圆形查询范围对象。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/cloud/CloudSearch.SearchBound.html#SearchBound-com.amap.api.services.core.LatLonPoint-com.amap.api.services.core.LatLonPoint-">SearchBound(LatLonPoint, LatLonPoint)</a></span> - 类 的构造器com.amap.api.services.cloud.<a href="../com/amap/api/services/cloud/CloudSearch.SearchBound.html" title="com.amap.api.services.cloud中的类">CloudSearch.SearchBound</a></dt>
<dd>
<div class="block">根据给定的参数来构造一个矩形查询范围对象。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/cloud/CloudSearch.SearchBound.html#SearchBound-java.util.List-">SearchBound(List&lt;LatLonPoint&gt;)</a></span> - 类 的构造器com.amap.api.services.cloud.<a href="../com/amap/api/services/cloud/CloudSearch.SearchBound.html" title="com.amap.api.services.cloud中的类">CloudSearch.SearchBound</a></dt>
<dd>
<div class="block">根据给定的参数来构造一个多边形查询范围对象。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/cloud/CloudSearch.SearchBound.html#SearchBound-java.lang.String-">SearchBound(String)</a></span> - 类 的构造器com.amap.api.services.cloud.<a href="../com/amap/api/services/cloud/CloudSearch.SearchBound.html" title="com.amap.api.services.cloud中的类">CloudSearch.SearchBound</a></dt>
<dd>
<div class="block">根据城市名称构造查询范围对象，适用于本地搜索。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearch.SearchBound.html#SearchBound-com.amap.api.services.core.LatLonPoint-int-">SearchBound(LatLonPoint, int)</a></span> - 类 的构造器com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearch.SearchBound.html" title="com.amap.api.services.poisearch中的类">PoiSearch.SearchBound</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">根据给定的参数来构造PoiSearch.SearchBound 的新对象，默认由近到远排序。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearch.SearchBound.html#SearchBound-com.amap.api.services.core.LatLonPoint-int-boolean-">SearchBound(LatLonPoint, int, boolean)</a></span> - 类 的构造器com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearch.SearchBound.html" title="com.amap.api.services.poisearch中的类">PoiSearch.SearchBound</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">根据给定的参数来构造PoiSearch.SearchBound 的新对象，默认由近到远排序。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearch.SearchBound.html#SearchBound-com.amap.api.services.core.LatLonPoint-com.amap.api.services.core.LatLonPoint-">SearchBound(LatLonPoint, LatLonPoint)</a></span> - 类 的构造器com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearch.SearchBound.html" title="com.amap.api.services.poisearch中的类">PoiSearch.SearchBound</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">根据给定的参数来构造PoiSearch.SearchBound 的新对象。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearch.SearchBound.html#SearchBound-java.util.List-">SearchBound(List&lt;LatLonPoint&gt;)</a></span> - 类 的构造器com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearch.SearchBound.html" title="com.amap.api.services.poisearch中的类">PoiSearch.SearchBound</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">根据给定的参数来构造PoiSearch.SearchBound 的新对象。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearchV2.SearchBound.html#SearchBound-com.amap.api.services.core.LatLonPoint-int-">SearchBound(LatLonPoint, int)</a></span> - 类 的构造器com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearchV2.SearchBound.html" title="com.amap.api.services.poisearch中的类">PoiSearchV2.SearchBound</a></dt>
<dd>
<div class="block">根据给定的参数来构造PoiSearchV2.SearchBound 的新对象，默认由近到远排序。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearchV2.SearchBound.html#SearchBound-com.amap.api.services.core.LatLonPoint-int-boolean-">SearchBound(LatLonPoint, int, boolean)</a></span> - 类 的构造器com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearchV2.SearchBound.html" title="com.amap.api.services.poisearch中的类">PoiSearchV2.SearchBound</a></dt>
<dd>
<div class="block">根据给定的参数来构造PoiSearchV2.SearchBound 的新对象，默认由近到远排序。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearchV2.SearchBound.html#SearchBound-com.amap.api.services.core.LatLonPoint-com.amap.api.services.core.LatLonPoint-">SearchBound(LatLonPoint, LatLonPoint)</a></span> - 类 的构造器com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearchV2.SearchBound.html" title="com.amap.api.services.poisearch中的类">PoiSearchV2.SearchBound</a></dt>
<dd>
<div class="block">根据给定的参数来构造PoiSearchV2.SearchBound 的新对象。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearchV2.SearchBound.html#SearchBound-java.util.List-">SearchBound(List&lt;LatLonPoint&gt;)</a></span> - 类 的构造器com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearchV2.SearchBound.html" title="com.amap.api.services.poisearch中的类">PoiSearchV2.SearchBound</a></dt>
<dd>
<div class="block">根据给定的参数来构造PoiSearchV2.SearchBound 的新对象。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/busline/BusLineSearch.html#searchBusLine--">searchBusLine()</a></span> - 类 中的方法com.amap.api.services.busline.<a href="../com/amap/api/services/busline/BusLineSearch.html" title="com.amap.api.services.busline中的类">BusLineSearch</a></dt>
<dd>
<div class="block">搜索公交线路。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/busline/BusLineSearch.html#searchBusLineAsyn--">searchBusLineAsyn()</a></span> - 类 中的方法com.amap.api.services.busline.<a href="../com/amap/api/services/busline/BusLineSearch.html" title="com.amap.api.services.busline中的类">BusLineSearch</a></dt>
<dd>
<div class="block">搜索公交线路的异步处理调用。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/share/ShareSearch.html#searchBusRouteShareUrl-com.amap.api.services.share.ShareSearch.ShareBusRouteQuery-">searchBusRouteShareUrl(ShareSearch.ShareBusRouteQuery)</a></span> - 类 中的方法com.amap.api.services.share.<a href="../com/amap/api/services/share/ShareSearch.html" title="com.amap.api.services.share中的类">ShareSearch</a></dt>
<dd>
<div class="block">根据指定的参数获取公交路径规划分享的短串地址。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/share/ShareSearch.html#searchBusRouteShareUrlAsyn-com.amap.api.services.share.ShareSearch.ShareBusRouteQuery-">searchBusRouteShareUrlAsyn(ShareSearch.ShareBusRouteQuery)</a></span> - 类 中的方法com.amap.api.services.share.<a href="../com/amap/api/services/share/ShareSearch.html" title="com.amap.api.services.share中的类">ShareSearch</a></dt>
<dd>
<div class="block">（异步处理）根据指定的参数来进行公交路径规划分享的异步处理。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/busline/BusStationSearch.html#searchBusStation--">searchBusStation()</a></span> - 类 中的方法com.amap.api.services.busline.<a href="../com/amap/api/services/busline/BusStationSearch.html" title="com.amap.api.services.busline中的类">BusStationSearch</a></dt>
<dd>
<div class="block">搜索公交站点。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/busline/BusStationSearch.html#searchBusStationAsyn--">searchBusStationAsyn()</a></span> - 类 中的方法com.amap.api.services.busline.<a href="../com/amap/api/services/busline/BusStationSearch.html" title="com.amap.api.services.busline中的类">BusStationSearch</a></dt>
<dd>
<div class="block">搜索公交站点的异步处理调用。</div>
</dd>
<dt><a href="../com/amap/api/services/route/SearchCity.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">SearchCity</span></a> - <a href="../com/amap/api/services/route/package-summary.html">com.amap.api.services.route</a>中的类</dt>
<dd>
<div class="block">此类定义搜索返回城市的名称和编码。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/cloud/CloudSearch.html#searchCloudAsyn-com.amap.api.services.cloud.CloudSearch.Query-">searchCloudAsyn(CloudSearch.Query)</a></span> - 类 中的方法com.amap.api.services.cloud.<a href="../com/amap/api/services/cloud/CloudSearch.html" title="com.amap.api.services.cloud中的类">CloudSearch</a></dt>
<dd>
<div class="block">企业地图数据搜索的异步接口。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/cloud/CloudSearch.html#searchCloudDetailAsyn-java.lang.String-java.lang.String-">searchCloudDetailAsyn(String, String)</a></span> - 类 中的方法com.amap.api.services.cloud.<a href="../com/amap/api/services/cloud/CloudSearch.html" title="com.amap.api.services.cloud中的类">CloudSearch</a></dt>
<dd>
<div class="block">根据CloudItem 的 ID 查找Cloud数据的详细信息。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/district/DistrictSearch.html#searchDistrict--">searchDistrict()</a></span> - 类 中的方法com.amap.api.services.district.<a href="../com/amap/api/services/district/DistrictSearch.html" title="com.amap.api.services.district中的类">DistrictSearch</a></dt>
<dd>
<div class="block">查询行政区的同步接口。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/district/DistrictSearch.html#searchDistrictAnsy--">searchDistrictAnsy()</a></span> - 类 中的方法com.amap.api.services.district.<a href="../com/amap/api/services/district/DistrictSearch.html" title="com.amap.api.services.district中的类">DistrictSearch</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span>
<div class="block"><span class="deprecationComment">请参考 <a href="../com/amap/api/services/district/DistrictSearch.html#searchDistrictAsyn--"><code>DistrictSearch.searchDistrictAsyn()</code></a></span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/district/DistrictSearch.html#searchDistrictAsyn--">searchDistrictAsyn()</a></span> - 类 中的方法com.amap.api.services.district.<a href="../com/amap/api/services/district/DistrictSearch.html" title="com.amap.api.services.district中的类">DistrictSearch</a></dt>
<dd>
<div class="block">查询行政区的异步接口。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/share/ShareSearch.html#searchDrivingRouteShareUrl-com.amap.api.services.share.ShareSearch.ShareDrivingRouteQuery-">searchDrivingRouteShareUrl(ShareSearch.ShareDrivingRouteQuery)</a></span> - 类 中的方法com.amap.api.services.share.<a href="../com/amap/api/services/share/ShareSearch.html" title="com.amap.api.services.share中的类">ShareSearch</a></dt>
<dd>
<div class="block">根据指定的参数获取驾车路径规划分享的短串地址。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/share/ShareSearch.html#searchDrivingRouteShareUrlAsyn-com.amap.api.services.share.ShareSearch.ShareDrivingRouteQuery-">searchDrivingRouteShareUrlAsyn(ShareSearch.ShareDrivingRouteQuery)</a></span> - 类 中的方法com.amap.api.services.share.<a href="../com/amap/api/services/share/ShareSearch.html" title="com.amap.api.services.share中的类">ShareSearch</a></dt>
<dd>
<div class="block">（异步处理）根据指定的参数来进行驾车路径规划分享的异步处理。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/share/ShareSearch.html#searchLocationShareUrl-com.amap.api.services.core.LatLonSharePoint-">searchLocationShareUrl(LatLonSharePoint)</a></span> - 类 中的方法com.amap.api.services.share.<a href="../com/amap/api/services/share/ShareSearch.html" title="com.amap.api.services.share中的类">ShareSearch</a></dt>
<dd>
<div class="block">根据指定的参数获取位置分享的短串地址。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/share/ShareSearch.html#searchLocationShareUrlAsyn-com.amap.api.services.core.LatLonSharePoint-">searchLocationShareUrlAsyn(LatLonSharePoint)</a></span> - 类 中的方法com.amap.api.services.share.<a href="../com/amap/api/services/share/ShareSearch.html" title="com.amap.api.services.share中的类">ShareSearch</a></dt>
<dd>
<div class="block">（异步处理）根据指定的参数来进行定位位置分享的异步处理。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/share/ShareSearch.html#searchNaviShareUrl-com.amap.api.services.share.ShareSearch.ShareNaviQuery-">searchNaviShareUrl(ShareSearch.ShareNaviQuery)</a></span> - 类 中的方法com.amap.api.services.share.<a href="../com/amap/api/services/share/ShareSearch.html" title="com.amap.api.services.share中的类">ShareSearch</a></dt>
<dd>
<div class="block">根据指定的参数获取导航分享的短串地址。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/share/ShareSearch.html#searchNaviShareUrlAsyn-com.amap.api.services.share.ShareSearch.ShareNaviQuery-">searchNaviShareUrlAsyn(ShareSearch.ShareNaviQuery)</a></span> - 类 中的方法com.amap.api.services.share.<a href="../com/amap/api/services/share/ShareSearch.html" title="com.amap.api.services.share中的类">ShareSearch</a></dt>
<dd>
<div class="block">（异步处理）根据指定的参数来进行导航分享的异步处理。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/nearby/NearbySearch.html#searchNearbyInfo-com.amap.api.services.nearby.NearbySearch.NearbyQuery-">searchNearbyInfo(NearbySearch.NearbyQuery)</a></span> - 类 中的方法com.amap.api.services.nearby.<a href="../com/amap/api/services/nearby/NearbySearch.html" title="com.amap.api.services.nearby中的类">NearbySearch</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">检索附近的用户信息，同步方法。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/nearby/NearbySearch.html#searchNearbyInfoAsyn-com.amap.api.services.nearby.NearbySearch.NearbyQuery-">searchNearbyInfoAsyn(NearbySearch.NearbyQuery)</a></span> - 类 中的方法com.amap.api.services.nearby.<a href="../com/amap/api/services/nearby/NearbySearch.html" title="com.amap.api.services.nearby中的类">NearbySearch</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">检索附近的用户信息，异步方法。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearch.html#searchPOI--">searchPOI()</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearch.html" title="com.amap.api.services.poisearch中的类">PoiSearch</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">查询POI。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearchV2.html#searchPOI--">searchPOI()</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearchV2.html" title="com.amap.api.services.poisearch中的类">PoiSearchV2</a></dt>
<dd>
<div class="block">查询POI。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearch.html#searchPOIAsyn--">searchPOIAsyn()</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearch.html" title="com.amap.api.services.poisearch中的类">PoiSearch</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">查询POI异步接口。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearchV2.html#searchPOIAsyn--">searchPOIAsyn()</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearchV2.html" title="com.amap.api.services.poisearch中的类">PoiSearchV2</a></dt>
<dd>
<div class="block">查询POI异步接口。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearch.html#searchPOIId-java.lang.String-">searchPOIId(String)</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearch.html" title="com.amap.api.services.poisearch中的类">PoiSearch</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">已知poiid信息（点击地图底图），搜索POI的详细信息，同步</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearchV2.html#searchPOIId-java.lang.String-">searchPOIId(String)</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearchV2.html" title="com.amap.api.services.poisearch中的类">PoiSearchV2</a></dt>
<dd>
<div class="block">已知poiid信息（点击地图底图），搜索POI的详细信息，同步</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearch.html#searchPOIIdAsyn-java.lang.String-">searchPOIIdAsyn(String)</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearch.html" title="com.amap.api.services.poisearch中的类">PoiSearch</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">已知poiid信息（点击地图底图），搜索POI的详细信息，异步</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearchV2.html#searchPOIIdAsyn-java.lang.String-">searchPOIIdAsyn(String)</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearchV2.html" title="com.amap.api.services.poisearch中的类">PoiSearchV2</a></dt>
<dd>
<div class="block">已知poiid信息（点击地图底图），搜索POI的详细信息，异步</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/share/ShareSearch.html#searchPoiShareUrl-com.amap.api.services.core.PoiItem-">searchPoiShareUrl(PoiItem)</a></span> - 类 中的方法com.amap.api.services.share.<a href="../com/amap/api/services/share/ShareSearch.html" title="com.amap.api.services.share中的类">ShareSearch</a></dt>
<dd>
<div class="block">根据指定的参数获取POI分享的短串地址。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/share/ShareSearch.html#searchPoiShareUrlAsyn-com.amap.api.services.core.PoiItem-">searchPoiShareUrlAsyn(PoiItem)</a></span> - 类 中的方法com.amap.api.services.share.<a href="../com/amap/api/services/share/ShareSearch.html" title="com.amap.api.services.share中的类">ShareSearch</a></dt>
<dd>
<div class="block">（异步处理）根据指定的参数来进行POI分享的异步处理。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/routepoisearch/RoutePOISearch.html#searchRoutePOI--">searchRoutePOI()</a></span> - 类 中的方法com.amap.api.services.routepoisearch.<a href="../com/amap/api/services/routepoisearch/RoutePOISearch.html" title="com.amap.api.services.routepoisearch中的类">RoutePOISearch</a></dt>
<dd>
<div class="block">沿途搜索同步查询接口。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/routepoisearch/RoutePOISearch.html#searchRoutePOIAsyn--">searchRoutePOIAsyn()</a></span> - 类 中的方法com.amap.api.services.routepoisearch.<a href="../com/amap/api/services/routepoisearch/RoutePOISearch.html" title="com.amap.api.services.routepoisearch中的类">RoutePOISearch</a></dt>
<dd>
<div class="block">沿途搜索POI异步查询接口。</div>
</dd>
<dt><a href="../com/amap/api/services/core/SearchUtils.html" title="com.amap.api.services.core中的类"><span class="typeNameLink">SearchUtils</span></a> - <a href="../com/amap/api/services/core/package-summary.html">com.amap.api.services.core</a>中的类</dt>
<dd>
<div class="block">定义了一个实现搜索包其他功能的类。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/SearchUtils.html#SearchUtils--">SearchUtils()</a></span> - 类 的构造器com.amap.api.services.core.<a href="../com/amap/api/services/core/SearchUtils.html" title="com.amap.api.services.core中的类">SearchUtils</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/share/ShareSearch.html#searchWalkRouteShareUrl-com.amap.api.services.share.ShareSearch.ShareWalkRouteQuery-">searchWalkRouteShareUrl(ShareSearch.ShareWalkRouteQuery)</a></span> - 类 中的方法com.amap.api.services.share.<a href="../com/amap/api/services/share/ShareSearch.html" title="com.amap.api.services.share中的类">ShareSearch</a></dt>
<dd>
<div class="block">根据指定的参数获取步行路径规划分享的短串地址。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/share/ShareSearch.html#searchWalkRouteShareUrlAsyn-com.amap.api.services.share.ShareSearch.ShareWalkRouteQuery-">searchWalkRouteShareUrlAsyn(ShareSearch.ShareWalkRouteQuery)</a></span> - 类 中的方法com.amap.api.services.share.<a href="../com/amap/api/services/share/ShareSearch.html" title="com.amap.api.services.share中的类">ShareSearch</a></dt>
<dd>
<div class="block">（异步处理）根据指定的参数来进行步行路径规划分享的异步处理。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/weather/WeatherSearch.html#searchWeatherAsyn--">searchWeatherAsyn()</a></span> - 类 中的方法com.amap.api.services.weather.<a href="../com/amap/api/services/weather/WeatherSearch.html" title="com.amap.api.services.weather中的类">WeatherSearch</a></dt>
<dd>
<div class="block">查询天气信息的异步接口。</div>
</dd>
<dt><a href="../com/amap/api/services/core/ServiceSettings.html" title="com.amap.api.services.core中的类"><span class="typeNameLink">ServiceSettings</span></a> - <a href="../com/amap/api/services/core/package-summary.html">com.amap.api.services.core</a>中的类</dt>
<dd>
<div class="block">设置API接口访问协议的单例。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/Navi.html#setAction-java.lang.String-">setAction(String)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/Navi.html" title="com.amap.api.services.route中的类">Navi</a></dt>
<dd>
<div class="block">设置驾车路段的导航主要操作</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.BusRouteQuery.html#setAd1-java.lang.String-">setAd1(String)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.BusRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearchV2.BusRouteQuery</a></dt>
<dd>
<div class="block">起点所在行政区域编码

 仅支持adcode，参考行政区域编码表</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.BusRouteQuery.html#setAd2-java.lang.String-">setAd2(String)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.BusRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearchV2.BusRouteQuery</a></dt>
<dd>
<div class="block">终点所在行政区域编码

 仅支持adcode，参考行政区域编码表</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/SuggestionCity.html#setAdCode-java.lang.String-">setAdCode(String)</a></span> - 类 中的方法com.amap.api.services.core.<a href="../com/amap/api/services/core/SuggestionCity.html" title="com.amap.api.services.core中的类">SuggestionCity</a></dt>
<dd>
<div class="block">设置区域代码。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/weather/LocalWeatherForecast.html#setAdCode-java.lang.String-">setAdCode(String)</a></span> - 类 中的方法com.amap.api.services.weather.<a href="../com/amap/api/services/weather/LocalWeatherForecast.html" title="com.amap.api.services.weather中的类">LocalWeatherForecast</a></dt>
<dd>
<div class="block">设置行政区划代码。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.BusRouteQuery.html#setAlternativeRoute-int-">setAlternativeRoute(int)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.BusRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearchV2.BusRouteQuery</a></dt>
<dd>
<div class="block">返回方案条数

 可传入1-10的阿拉伯数字，代表返回的不同条数。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.WalkRouteQuery.html#setAlternativeRoute-int-">setAlternativeRoute(int)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.WalkRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearchV2.WalkRouteQuery</a></dt>
<dd>
<div class="block">设置返回路线条数</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/ServiceSettings.html#setApiKey-java.lang.String-">setApiKey(String)</a></span> - 类 中的方法com.amap.api.services.core.<a href="../com/amap/api/services/core/ServiceSettings.html" title="com.amap.api.services.core中的类">ServiceSettings</a></dt>
<dd>
<div class="block">动态设置apiKey。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/Navi.html#setAssistantAction-java.lang.String-">setAssistantAction(String)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/Navi.html" title="com.amap.api.services.route中的类">Navi</a></dt>
<dd>
<div class="block">设置驾车路段的导航辅助操作</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/cloud/CloudSearch.Query.html#setBound-com.amap.api.services.cloud.CloudSearch.SearchBound-">setBound(CloudSearch.SearchBound)</a></span> - 类 中的方法com.amap.api.services.cloud.<a href="../com/amap/api/services/cloud/CloudSearch.Query.html" title="com.amap.api.services.cloud中的类">CloudSearch.Query</a></dt>
<dd>
<div class="block">设置查询的范围（本地、圆形、矩形或者多边形）。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearch.html#setBound-com.amap.api.services.poisearch.PoiSearch.SearchBound-">setBound(PoiSearch.SearchBound)</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearch.html" title="com.amap.api.services.poisearch中的类">PoiSearch</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">设置查询矩形。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearchV2.html#setBound-com.amap.api.services.poisearch.PoiSearchV2.SearchBound-">setBound(PoiSearchV2.SearchBound)</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearchV2.html" title="com.amap.api.services.poisearch中的类">PoiSearchV2</a></dt>
<dd>
<div class="block">设置查询矩形。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearch.Query.html#setBuilding-java.lang.String-">setBuilding(String)</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearch.Query.html" title="com.amap.api.services.poisearch中的类">PoiSearch.Query</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">如果要进行室内搜索，需要添加该字段</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearchV2.Query.html#setBuilding-java.lang.String-">setBuilding(String)</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearchV2.Query.html" title="com.amap.api.services.poisearch中的类">PoiSearchV2.Query</a></dt>
<dd>
<div class="block">如果要进行室内搜索，需要添加该字段</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.DrivePlanQuery.html#setCarType-int-">setCarType(int)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.DrivePlanQuery.html" title="com.amap.api.services.route中的类">RouteSearch.DrivePlanQuery</a></dt>
<dd>
<div class="block">设置车辆类型，默认为普通汽车</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.DriveRouteQuery.html#setCarType-int-">setCarType(int)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.DriveRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearch.DriveRouteQuery</a></dt>
<dd>
<div class="block">设置车辆类型，默认为普通汽车</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.DriveRouteQuery.html#setCarType-int-">setCarType(int)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.DriveRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearchV2.DriveRouteQuery</a></dt>
<dd>
<div class="block">设置车辆类型，默认为普通汽车</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/busline/BusLineQuery.html#setCategory-com.amap.api.services.busline.BusLineQuery.SearchType-">setCategory(BusLineQuery.SearchType)</a></span> - 类 中的方法com.amap.api.services.busline.<a href="../com/amap/api/services/busline/BusLineQuery.html" title="com.amap.api.services.busline中的类">BusLineQuery</a></dt>
<dd>
<div class="block">设置查询类型。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/nearby/NearbySearch.NearbyQuery.html#setCenterPoint-com.amap.api.services.core.LatLonPoint-">setCenterPoint(LatLonPoint)</a></span> - 类 中的方法com.amap.api.services.nearby.<a href="../com/amap/api/services/nearby/NearbySearch.NearbyQuery.html" title="com.amap.api.services.nearby中的类">NearbySearch.NearbyQuery</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">检索的中心点，必选参数。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/road/Road.html#setCenterPoint-com.amap.api.services.core.LatLonPoint-">setCenterPoint(LatLonPoint)</a></span> - 类 中的方法com.amap.api.services.road.<a href="../com/amap/api/services/road/Road.html" title="com.amap.api.services.road中的类">Road</a></dt>
<dd>
<div class="block">设置道路中心点的坐标。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/busline/BusLineQuery.html#setCity-java.lang.String-">setCity(String)</a></span> - 类 中的方法com.amap.api.services.busline.<a href="../com/amap/api/services/busline/BusLineQuery.html" title="com.amap.api.services.busline中的类">BusLineQuery</a></dt>
<dd>
<div class="block">设置查询城市参数，参数可以为城市编码/行政区划代码/城市名称。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/busline/BusStationQuery.html#setCity-java.lang.String-">setCity(String)</a></span> - 类 中的方法com.amap.api.services.busline.<a href="../com/amap/api/services/busline/BusStationQuery.html" title="com.amap.api.services.busline中的类">BusStationQuery</a></dt>
<dd>
<div class="block">设置查询城市参数。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/GeocodeQuery.html#setCity-java.lang.String-">setCity(String)</a></span> - 类 中的方法com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/GeocodeQuery.html" title="com.amap.api.services.geocoder中的类">GeocodeQuery</a></dt>
<dd>
<div class="block">设置查询城市名称、城市编码或行政区划代码。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/weather/LocalWeatherForecast.html#setCity-java.lang.String-">setCity(String)</a></span> - 类 中的方法com.amap.api.services.weather.<a href="../com/amap/api/services/weather/LocalWeatherForecast.html" title="com.amap.api.services.weather中的类">LocalWeatherForecast</a></dt>
<dd>
<div class="block">设置城市名称。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/SuggestionCity.html#setCityCode-java.lang.String-">setCityCode(String)</a></span> - 类 中的方法com.amap.api.services.core.<a href="../com/amap/api/services/core/SuggestionCity.html" title="com.amap.api.services.core中的类">SuggestionCity</a></dt>
<dd>
<div class="block">设置城市编码。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/road/Road.html#setCityCode-java.lang.String-">setCityCode(String)</a></span> - 类 中的方法com.amap.api.services.road.<a href="../com/amap/api/services/road/Road.html" title="com.amap.api.services.road中的类">Road</a></dt>
<dd>
<div class="block">设置结果的城市编码。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.BusRouteQuery.html#setCityd-java.lang.String-">setCityd(String)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.BusRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearch.BusRouteQuery</a></dt>
<dd>
<div class="block">设置跨城公交规划的目的地城市。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.BusRouteQuery.html#setCityd-java.lang.String-">setCityd(String)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.BusRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearchV2.BusRouteQuery</a></dt>
<dd>
<div class="block">设置跨城公交规划的目的地城市。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/help/InputtipsQuery.html#setCityLimit-boolean-">setCityLimit(boolean)</a></span> - 类 中的方法com.amap.api.services.help.<a href="../com/amap/api/services/help/InputtipsQuery.html" title="com.amap.api.services.help中的类">InputtipsQuery</a></dt>
<dd>
<div class="block">对获取结果进行严格城市限制。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearch.Query.html#setCityLimit-boolean-">setCityLimit(boolean)</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearch.Query.html" title="com.amap.api.services.poisearch中的类">PoiSearch.Query</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">仅在通过关键字搜索时进行限制严格按照设置城市搜索</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearchV2.Query.html#setCityLimit-boolean-">setCityLimit(boolean)</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearchV2.Query.html" title="com.amap.api.services.poisearch中的类">PoiSearchV2.Query</a></dt>
<dd>
<div class="block">仅在通过关键字搜索时进行限制严格按照设置城市搜索</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/SuggestionCity.html#setCityName-java.lang.String-">setCityName(String)</a></span> - 类 中的方法com.amap.api.services.core.<a href="../com/amap/api/services/core/SuggestionCity.html" title="com.amap.api.services.core中的类">SuggestionCity</a></dt>
<dd>
<div class="block">设置城市名称。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/ServiceSettings.html#setConnectionTimeOut-int-">setConnectionTimeOut(int)</a></span> - 类 中的方法com.amap.api.services.core.<a href="../com/amap/api/services/core/ServiceSettings.html" title="com.amap.api.services.core中的类">ServiceSettings</a></dt>
<dd>
<div class="block">设置搜索服务建立连接超时。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/nearby/NearbySearch.NearbyQuery.html#setCoordType-int-">setCoordType(int)</a></span> - 类 中的方法com.amap.api.services.nearby.<a href="../com/amap/api/services/nearby/NearbySearch.NearbyQuery.html" title="com.amap.api.services.nearby中的类">NearbySearch.NearbyQuery</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">设置坐标类型，NearbySearch.GPS代表GPS坐标系，NearbySearch.AMAP代表高德坐标系，默认为高德坐标系。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/nearby/UploadInfo.html#setCoordType-int-">setCoordType(int)</a></span> - 类 中的方法com.amap.api.services.nearby.<a href="../com/amap/api/services/nearby/UploadInfo.html" title="com.amap.api.services.nearby中的类">UploadInfo</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">设置坐标类型，NearbySearch.GPS代表GPS坐标系，NearbySearch.AMAP代表高德坐标系，默认为高德坐标系。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/DrivePathV2.html#setCost-com.amap.api.services.route.Cost-">setCost(Cost)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/DrivePathV2.html" title="com.amap.api.services.route中的类">DrivePathV2</a></dt>
<dd>
<div class="block">设置方案所需时间及费用成本</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/DriveStepV2.html#setCostDetail-com.amap.api.services.route.Cost-">setCostDetail(Cost)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/DriveStepV2.html" title="com.amap.api.services.route中的类">DriveStepV2</a></dt>
<dd>
<div class="block">设置路线消耗信息</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/GeocodeAddress.html#setCountry-java.lang.String-">setCountry(String)</a></span> - 类 中的方法com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/GeocodeAddress.html" title="com.amap.api.services.geocoder中的类">GeocodeAddress</a></dt>
<dd>
<div class="block">海外生效
 国家名称</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/GeocodeQuery.html#setCountry-java.lang.String-">setCountry(String)</a></span> - 类 中的方法com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/GeocodeQuery.html" title="com.amap.api.services.geocoder中的类">GeocodeQuery</a></dt>
<dd>
<div class="block">海外生效
 指定查询国家，支持多个国家，用“|”分隔，可选值：国家代码ISO 3166 或 global
 https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/RegeocodeAddress.html#setCountryCode-java.lang.String-">setCountryCode(String)</a></span> - 类 中的方法com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/RegeocodeAddress.html" title="com.amap.api.services.geocoder中的类">RegeocodeAddress</a></dt>
<dd>
<div class="block">海外生效
 国家简码</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.BusRouteQuery.html#setDate-java.lang.String-">setDate(String)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.BusRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearchV2.BusRouteQuery</a></dt>
<dd>
<div class="block">请求日期

 例如:2013-10-28</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/weather/LocalDayWeatherForecast.html#setDate-java.lang.String-">setDate(String)</a></span> - 类 中的方法com.amap.api.services.weather.<a href="../com/amap/api/services/weather/LocalDayWeatherForecast.html" title="com.amap.api.services.weather中的类">LocalDayWeatherForecast</a></dt>
<dd>
<div class="block">设置预报天气的年月日。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/weather/LocalDayWeatherForecast.html#setDayTemp-java.lang.String-">setDayTemp(String)</a></span> - 类 中的方法com.amap.api.services.weather.<a href="../com/amap/api/services/weather/LocalDayWeatherForecast.html" title="com.amap.api.services.weather中的类">LocalDayWeatherForecast</a></dt>
<dd>
<div class="block">设置白天天气温度</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/weather/LocalDayWeatherForecast.html#setDayWeather-java.lang.String-">setDayWeather(String)</a></span> - 类 中的方法com.amap.api.services.weather.<a href="../com/amap/api/services/weather/LocalDayWeatherForecast.html" title="com.amap.api.services.weather中的类">LocalDayWeatherForecast</a></dt>
<dd>
<div class="block">设置白天的天气现象。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/weather/LocalDayWeatherForecast.html#setDayWindDirection-java.lang.String-">setDayWindDirection(String)</a></span> - 类 中的方法com.amap.api.services.weather.<a href="../com/amap/api/services/weather/LocalDayWeatherForecast.html" title="com.amap.api.services.weather中的类">LocalDayWeatherForecast</a></dt>
<dd>
<div class="block">设置白天风向。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/weather/LocalDayWeatherForecast.html#setDayWindPower-java.lang.String-">setDayWindPower(String)</a></span> - 类 中的方法com.amap.api.services.weather.<a href="../com/amap/api/services/weather/LocalDayWeatherForecast.html" title="com.amap.api.services.weather中的类">LocalDayWeatherForecast</a></dt>
<dd>
<div class="block">设置白天风力。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/DistanceSearch.DistanceQuery.html#setDestination-com.amap.api.services.core.LatLonPoint-">setDestination(LatLonPoint)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/DistanceSearch.DistanceQuery.html" title="com.amap.api.services.route中的类">DistanceSearch.DistanceQuery</a></dt>
<dd>
<div class="block">设置距离测量终点，和起点不同终点仅支持一个坐标</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.FromAndTo.html#setDestinationPoiID-java.lang.String-">setDestinationPoiID(String)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.FromAndTo.html" title="com.amap.api.services.route中的类">RouteSearch.FromAndTo</a></dt>
<dd>
<div class="block">设置路径规划目的地POI的ID。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.BusRouteQuery.html#setDestinationPoiId-java.lang.String-">setDestinationPoiId(String)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.BusRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearchV2.BusRouteQuery</a></dt>
<dd>
<div class="block">目的地POI ID

 1、目的地POI ID与目的地经纬度均填写时，服务使用目的地 POI ID；

 2、该字段必须和起点 POI ID 成组使用。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.FromAndTo.html#setDestinationPoiID-java.lang.String-">setDestinationPoiID(String)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.FromAndTo.html" title="com.amap.api.services.route中的类">RouteSearchV2.FromAndTo</a></dt>
<dd>
<div class="block">设置路径规划目的地POI的ID。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.FromAndTo.html#setDestinationType-java.lang.String-">setDestinationType(String)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.FromAndTo.html" title="com.amap.api.services.route中的类">RouteSearch.FromAndTo</a></dt>
<dd>
<div class="block">设置路径规划目的地类型。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.FromAndTo.html#setDestinationType-java.lang.String-">setDestinationType(String)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.FromAndTo.html" title="com.amap.api.services.route中的类">RouteSearchV2.FromAndTo</a></dt>
<dd>
<div class="block">设置路径规划目的地类型。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.DrivePlanQuery.html#setDestParentPoiID-java.lang.String-">setDestParentPoiID(String)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.DrivePlanQuery.html" title="com.amap.api.services.route中的类">RouteSearch.DrivePlanQuery</a></dt>
<dd>
<div class="block">设置终点的父POIID，无父POI的情况留空即可</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/StreetNumber.html#setDirection-java.lang.String-">setDirection(String)</a></span> - 类 中的方法com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/StreetNumber.html" title="com.amap.api.services.geocoder中的类">StreetNumber</a></dt>
<dd>
<div class="block">设置门牌信息中的方向 ，指结果点相对地理坐标点的方向。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/road/Crossroad.html#setDirection-java.lang.String-">setDirection(String)</a></span> - 类 中的方法com.amap.api.services.road.<a href="../com/amap/api/services/road/Crossroad.html" title="com.amap.api.services.road中的类">Crossroad</a></dt>
<dd>
<div class="block">设置交叉路口相对逆地理坐标点的方向。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/StreetNumber.html#setDistance-float-">setDistance(float)</a></span> - 类 中的方法com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/StreetNumber.html" title="com.amap.api.services.geocoder中的类">StreetNumber</a></dt>
<dd>
<div class="block">设置门牌信息中地理坐标点与结果点的垂直距离。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/road/Crossroad.html#setDistance-float-">setDistance(float)</a></span> - 类 中的方法com.amap.api.services.road.<a href="../com/amap/api/services/road/Crossroad.html" title="com.amap.api.services.road中的类">Crossroad</a></dt>
<dd>
<div class="block">设置逆地理坐标点与交叉路口的垂直距离，单位米。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/BusRouteResultV2.html#setDistance-float-">setDistance(float)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/BusRouteResultV2.html" title="com.amap.api.services.route中的类">BusRouteResultV2</a></dt>
<dd>
<div class="block">总距离</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/DistanceSearch.html#setDistanceSearchListener-com.amap.api.services.route.DistanceSearch.OnDistanceSearchListener-">setDistanceSearchListener(DistanceSearch.OnDistanceSearchListener)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/DistanceSearch.html" title="com.amap.api.services.route中的类">DistanceSearch</a></dt>
<dd>
<div class="block">设置距离测量异步请求监听回调</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearch.Query.html#setDistanceSort-boolean-">setDistanceSort(boolean)</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearch.Query.html" title="com.amap.api.services.poisearch中的类">PoiSearch.Query</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">设置是否按距离排序</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearchV2.Query.html#setDistanceSort-boolean-">setDistanceSort(boolean)</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearchV2.Query.html" title="com.amap.api.services.poisearch中的类">PoiSearchV2.Query</a></dt>
<dd>
<div class="block">设置是否按距离排序</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchCity.html#setDistricts-java.util.List-">setDistricts(List&lt;District&gt;)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchCity.html" title="com.amap.api.services.route中的类">RouteSearchCity</a></dt>
<dd>
<div class="block">设置行政区对象。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/Cost.html#setDuration-float-">setDuration(float)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/Cost.html" title="com.amap.api.services.route中的类">Cost</a></dt>
<dd>
<div class="block">设置线路耗时，包括方案总耗时及分段step中的耗时</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.DriveRouteQuery.html#setExclude-java.lang.String-">setExclude(String)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.DriveRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearch.DriveRouteQuery</a></dt>
<dd>
<div class="block">海外生效
 规避道路类型，默认不规避
 可选值：
 <a href="../com/amap/api/services/route/RouteSearch.html#DRIVING_EXCLUDE_TOLL"><code>RouteSearch.DRIVING_EXCLUDE_TOLL</code></a> toll-收费道路； <br>
 <a href="../com/amap/api/services/route/RouteSearch.html#DRIVING_EXCLUDE_MOTORWAY"><code>RouteSearch.DRIVING_EXCLUDE_MOTORWAY</code></a> motorway-高速路；<br>
 <a href="../com/amap/api/services/route/RouteSearch.html#DRIVING_EXCLUDE_FERRY"><code>RouteSearch.DRIVING_EXCLUDE_FERRY</code></a> ferry-渡船;<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.DriveRouteQuery.html#setExclude-java.lang.String-">setExclude(String)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.DriveRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearchV2.DriveRouteQuery</a></dt>
<dd>
<div class="block">海外生效
 规避道路类型，默认不规避
 可选值：
 <code>#DRIVING_EXCLUDE_TOLL</code> toll-收费道路； <br>
 <code>#DRIVING_EXCLUDE_MOTORWAY</code> motorway-高速路；<br>
 <code>#DRIVING_EXCLUDE_FERRY</code> ferry-渡船;<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/busline/BusLineQuery.html#setExtensions-java.lang.String-">setExtensions(String)</a></span> - 类 中的方法com.amap.api.services.busline.<a href="../com/amap/api/services/busline/BusLineQuery.html" title="com.amap.api.services.busline中的类">BusLineQuery</a></dt>
<dd>
<div class="block">扩展字段 base表示只返回基础数据，all表示所有数据 ，默认 base</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/RegeocodeQuery.html#setExtensions-java.lang.String-">setExtensions(String)</a></span> - 类 中的方法com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/RegeocodeQuery.html" title="com.amap.api.services.geocoder中的类">RegeocodeQuery</a></dt>
<dd>
<div class="block">扩展字段 base表示只返回基础数据，all表示所有数据 ，默认 base</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearch.Query.html#setExtensions-java.lang.String-">setExtensions(String)</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearch.Query.html" title="com.amap.api.services.poisearch中的类">PoiSearch.Query</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">扩展字段 base表示只返回基础数据，all表示所有数据 ，默认 base</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/DistanceSearch.DistanceQuery.html#setExtensions-java.lang.String-">setExtensions(String)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/DistanceSearch.DistanceQuery.html" title="com.amap.api.services.route中的类">DistanceSearch.DistanceQuery</a></dt>
<dd>
<div class="block">扩展字段 base表示只返回基础数据，all表示所有数据 ，默认 base</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.BusRouteQuery.html#setExtensions-java.lang.String-">setExtensions(String)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.BusRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearch.BusRouteQuery</a></dt>
<dd>
<div class="block">扩展字段 base表示只返回基础数据，all表示所有数据 ，默认 base</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.DriveRouteQuery.html#setExtensions-java.lang.String-">setExtensions(String)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.DriveRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearch.DriveRouteQuery</a></dt>
<dd>
<div class="block">扩展字段 base表示只返回基础数据，all表示所有数据 ，默认 base</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.RideRouteQuery.html#setExtensions-java.lang.String-">setExtensions(String)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.RideRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearch.RideRouteQuery</a></dt>
<dd>
<div class="block">扩展字段 base表示只返回基础数据，all表示所有数据 ，默认 base</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.TruckRouteQuery.html#setExtensions-java.lang.String-">setExtensions(String)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.TruckRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearch.TruckRouteQuery</a></dt>
<dd>
<div class="block">扩展字段 base表示只返回基础数据，all表示所有数据 ，默认 base</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.WalkRouteQuery.html#setExtensions-java.lang.String-">setExtensions(String)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.WalkRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearch.WalkRouteQuery</a></dt>
<dd>
<div class="block">扩展字段 base表示只返回基础数据，all表示所有数据 ，默认 base</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/road/Crossroad.html#setFirstRoadId-java.lang.String-">setFirstRoadId(String)</a></span> - 类 中的方法com.amap.api.services.road.<a href="../com/amap/api/services/road/Crossroad.html" title="com.amap.api.services.road中的类">Crossroad</a></dt>
<dd>
<div class="block">设置交叉路口的第一条道路ID。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/road/Crossroad.html#setFirstRoadName-java.lang.String-">setFirstRoadName(String)</a></span> - 类 中的方法com.amap.api.services.road.<a href="../com/amap/api/services/road/Crossroad.html" title="com.amap.api.services.road中的类">Crossroad</a></dt>
<dd>
<div class="block">设置交叉路口的第一条道路名称。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/share/ShareSearch.ShareFromAndTo.html#setFromName-java.lang.String-">setFromName(String)</a></span> - 类 中的方法com.amap.api.services.share.<a href="../com/amap/api/services/share/ShareSearch.ShareFromAndTo.html" title="com.amap.api.services.share中的类">ShareSearch.ShareFromAndTo</a></dt>
<dd>
<div class="block">设置起点分享名称。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/GeocodeResult.html#setGeocodeAddressList-java.util.List-">setGeocodeAddressList(List&lt;GeocodeAddress&gt;)</a></span> - 类 中的方法com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/GeocodeResult.html" title="com.amap.api.services.geocoder中的类">GeocodeResult</a></dt>
<dd>
<div class="block">设置地理编码搜索的结果</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/GeocodeResult.html#setGeocodeQuery-com.amap.api.services.geocoder.GeocodeQuery-">setGeocodeQuery(GeocodeQuery)</a></span> - 类 中的方法com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/GeocodeResult.html" title="com.amap.api.services.geocoder中的类">GeocodeResult</a></dt>
<dd>
<div class="block">设置查询结果对应的查询参数</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/road/Road.html#setId-java.lang.String-">setId(String)</a></span> - 类 中的方法com.amap.api.services.road.<a href="../com/amap/api/services/road/Road.html" title="com.amap.api.services.road中的类">Road</a></dt>
<dd>
<div class="block">设置道路ID。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.WalkRouteQuery.html#setIndoor-boolean-">setIndoor(boolean)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.WalkRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearchV2.WalkRouteQuery</a></dt>
<dd>
<div class="block">设置是否需要室内算路</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/help/Inputtips.html#setInputtipsListener-com.amap.api.services.help.Inputtips.InputtipsListener-">setInputtipsListener(Inputtips.InputtipsListener)</a></span> - 类 中的方法com.amap.api.services.help.<a href="../com/amap/api/services/help/Inputtips.html" title="com.amap.api.services.help中的类">Inputtips</a></dt>
<dd>
<div class="block">设置提示查询监听。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/district/DistrictSearchQuery.html#setKeywords-java.lang.String-">setKeywords(String)</a></span> - 类 中的方法com.amap.api.services.district.<a href="../com/amap/api/services/district/DistrictSearchQuery.html" title="com.amap.api.services.district中的类">DistrictSearchQuery</a></dt>
<dd>
<div class="block">设置查询字符串关键字。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/district/DistrictSearchQuery.html#setKeywordsLevel-java.lang.String-">setKeywordsLevel(String)</a></span> - 类 中的方法com.amap.api.services.district.<a href="../com/amap/api/services/district/DistrictSearchQuery.html" title="com.amap.api.services.district中的类">DistrictSearchQuery</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span>
<div class="block"><span class="deprecationComment">3.6.1 不再需要此参数限制</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/ServiceSettings.html#setLanguage-java.lang.String-">setLanguage(String)</a></span> - 类 中的方法com.amap.api.services.core.<a href="../com/amap/api/services/core/ServiceSettings.html" title="com.amap.api.services.core中的类">ServiceSettings</a></dt>
<dd>
<div class="block">设置搜索、地理/逆地理编码、输入提示中英文切换的接口。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearch.html#setLanguage-java.lang.String-">setLanguage(String)</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearch.html" title="com.amap.api.services.poisearch中的类">PoiSearch</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span>
<div class="block"><span class="deprecationComment">自V2.5.0后废弃，请参考 <a href="../com/amap/api/services/core/ServiceSettings.html#setLanguage-java.lang.String-"><code>ServiceSettings.setLanguage(String)</code></a></span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/LatLonPoint.html#setLatitude-double-">setLatitude(double)</a></span> - 类 中的方法com.amap.api.services.core.<a href="../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a></dt>
<dd>
<div class="block">设置该点纬度。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/StreetNumber.html#setLatLonPoint-com.amap.api.services.core.LatLonPoint-">setLatLonPoint(LatLonPoint)</a></span> - 类 中的方法com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/StreetNumber.html" title="com.amap.api.services.geocoder中的类">StreetNumber</a></dt>
<dd>
<div class="block">设置门牌信息中的经纬度坐标。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/RegeocodeQuery.html#setLatLonType-java.lang.String-">setLatLonType(String)</a></span> - 类 中的方法com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/RegeocodeQuery.html" title="com.amap.api.services.geocoder中的类">RegeocodeQuery</a></dt>
<dd>
<div class="block">设置参数坐标类型。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/help/InputtipsQuery.html#setLocation-com.amap.api.services.core.LatLonPoint-">setLocation(LatLonPoint)</a></span> - 类 中的方法com.amap.api.services.help.<a href="../com/amap/api/services/help/InputtipsQuery.html" title="com.amap.api.services.help中的类">InputtipsQuery</a></dt>
<dd>
<div class="block">对获取结果进行经纬度位置限制</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearch.Query.html#setLocation-com.amap.api.services.core.LatLonPoint-">setLocation(LatLonPoint)</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearch.Query.html" title="com.amap.api.services.poisearch中的类">PoiSearch.Query</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">设置经纬度</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearchV2.Query.html#setLocation-com.amap.api.services.core.LatLonPoint-">setLocation(LatLonPoint)</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearchV2.Query.html" title="com.amap.api.services.poisearch中的类">PoiSearchV2.Query</a></dt>
<dd>
<div class="block">设置经纬度</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/GeocodeQuery.html#setLocationName-java.lang.String-">setLocationName(String)</a></span> - 类 中的方法com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/GeocodeQuery.html" title="com.amap.api.services.geocoder中的类">GeocodeQuery</a></dt>
<dd>
<div class="block">设置查询的地理名称。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/LatLonPoint.html#setLongitude-double-">setLongitude(double)</a></span> - 类 中的方法com.amap.api.services.core.<a href="../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a></dt>
<dd>
<div class="block">设置该点经度。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.BusRouteQuery.html#setMaxTrans-int-">setMaxTrans(int)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.BusRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearchV2.BusRouteQuery</a></dt>
<dd>
<div class="block">最大换乘次数

 0：直达

 1：最多换乘1次

 2：最多换乘2次

 3：最多换乘3次

 4：最多换乘4次</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/RegeocodeQuery.html#setMode-java.lang.String-">setMode(String)</a></span> - 类 中的方法com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/RegeocodeQuery.html" title="com.amap.api.services.geocoder中的类">RegeocodeQuery</a></dt>
<dd>
<div class="block">海外生效
 返回策略，如传入无效mode值，则走默认策略
 distance 按距离返回
 score 按权重返回</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/DistanceSearch.DistanceQuery.html#setMode-int-">setMode(int)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/DistanceSearch.DistanceQuery.html" title="com.amap.api.services.route中的类">DistanceSearch.DistanceQuery</a></dt>
<dd>
<div class="block">驾车距离测量模式， 默认是4， 参加驾车路径规划策略模式</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.DrivePlanQuery.html#setMode-int-">setMode(int)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.DrivePlanQuery.html" title="com.amap.api.services.route中的类">RouteSearch.DrivePlanQuery</a></dt>
<dd>
<div class="block">设置规划策略模式，默认为普通汽车</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.TruckRouteQuery.html#setMode-int-">setMode(int)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.TruckRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearch.TruckRouteQuery</a></dt>
<dd>
<div class="block">货车导航计算路径的模式设置</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.BusRouteQuery.html#setMultiExport-int-">setMultiExport(int)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.BusRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearchV2.BusRouteQuery</a></dt>
<dd>
<div class="block">地铁出入口数量

 0：只返回一个地铁出入口

 1：返回全部地铁出入口</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/road/Road.html#setName-java.lang.String-">setName(String)</a></span> - 类 中的方法com.amap.api.services.road.<a href="../com/amap/api/services/road/Road.html" title="com.amap.api.services.road中的类">Road</a></dt>
<dd>
<div class="block">设置道路名称。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/DriveStepV2.html#setNavi-com.amap.api.services.route.Navi-">setNavi(Navi)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/DriveStepV2.html" title="com.amap.api.services.route中的类">DriveStepV2</a></dt>
<dd>
<div class="block">设置导航信息</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.DriveRouteQuery.html#setNewEnergy-com.amap.api.services.route.RouteSearchV2.NewEnergy-">setNewEnergy(RouteSearchV2.NewEnergy)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.DriveRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearchV2.DriveRouteQuery</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/weather/LocalDayWeatherForecast.html#setNightTemp-java.lang.String-">setNightTemp(String)</a></span> - 类 中的方法com.amap.api.services.weather.<a href="../com/amap/api/services/weather/LocalDayWeatherForecast.html" title="com.amap.api.services.weather中的类">LocalDayWeatherForecast</a></dt>
<dd>
<div class="block">设置夜间天气温度。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/weather/LocalDayWeatherForecast.html#setNightWeather-java.lang.String-">setNightWeather(String)</a></span> - 类 中的方法com.amap.api.services.weather.<a href="../com/amap/api/services/weather/LocalDayWeatherForecast.html" title="com.amap.api.services.weather中的类">LocalDayWeatherForecast</a></dt>
<dd>
<div class="block">设置夜间天气现象。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/weather/LocalDayWeatherForecast.html#setNightWindDirection-java.lang.String-">setNightWindDirection(String)</a></span> - 类 中的方法com.amap.api.services.weather.<a href="../com/amap/api/services/weather/LocalDayWeatherForecast.html" title="com.amap.api.services.weather中的类">LocalDayWeatherForecast</a></dt>
<dd>
<div class="block">设置夜间风向。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/weather/LocalDayWeatherForecast.html#setNightWindPower-java.lang.String-">setNightWindPower(String)</a></span> - 类 中的方法com.amap.api.services.weather.<a href="../com/amap/api/services/weather/LocalDayWeatherForecast.html" title="com.amap.api.services.weather中的类">LocalDayWeatherForecast</a></dt>
<dd>
<div class="block">设置夜间风力。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/StreetNumber.html#setNumber-java.lang.String-">setNumber(String)</a></span> - 类 中的方法com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/StreetNumber.html" title="com.amap.api.services.geocoder中的类">StreetNumber</a></dt>
<dd>
<div class="block">设置门牌信息中的门牌号码。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/busline/BusLineSearch.html#setOnBusLineSearchListener-com.amap.api.services.busline.BusLineSearch.OnBusLineSearchListener-">setOnBusLineSearchListener(BusLineSearch.OnBusLineSearchListener)</a></span> - 类 中的方法com.amap.api.services.busline.<a href="../com/amap/api/services/busline/BusLineSearch.html" title="com.amap.api.services.busline中的类">BusLineSearch</a></dt>
<dd>
<div class="block">设置查询监听接口。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/busline/BusStationSearch.html#setOnBusStationSearchListener-com.amap.api.services.busline.BusStationSearch.OnBusStationSearchListener-">setOnBusStationSearchListener(BusStationSearch.OnBusStationSearchListener)</a></span> - 类 中的方法com.amap.api.services.busline.<a href="../com/amap/api/services/busline/BusStationSearch.html" title="com.amap.api.services.busline中的类">BusStationSearch</a></dt>
<dd>
<div class="block">公交站点搜索结果监听接口设置。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/cloud/CloudSearch.html#setOnCloudSearchListener-com.amap.api.services.cloud.CloudSearch.OnCloudSearchListener-">setOnCloudSearchListener(CloudSearch.OnCloudSearchListener)</a></span> - 类 中的方法com.amap.api.services.cloud.<a href="../com/amap/api/services/cloud/CloudSearch.html" title="com.amap.api.services.cloud中的类">CloudSearch</a></dt>
<dd>
<div class="block">设置查询监听接口。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/district/DistrictSearch.html#setOnDistrictSearchListener-com.amap.api.services.district.DistrictSearch.OnDistrictSearchListener-">setOnDistrictSearchListener(DistrictSearch.OnDistrictSearchListener)</a></span> - 类 中的方法com.amap.api.services.district.<a href="../com/amap/api/services/district/DistrictSearch.html" title="com.amap.api.services.district中的类">DistrictSearch</a></dt>
<dd>
<div class="block">设置行政区划查询监听。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/GeocodeSearch.html#setOnGeocodeSearchListener-com.amap.api.services.geocoder.GeocodeSearch.OnGeocodeSearchListener-">setOnGeocodeSearchListener(GeocodeSearch.OnGeocodeSearchListener)</a></span> - 类 中的方法com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/GeocodeSearch.html" title="com.amap.api.services.geocoder中的类">GeocodeSearch</a></dt>
<dd>
<div class="block">地理编码查询结果监听接口设置。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearch.html#setOnPoiSearchListener-com.amap.api.services.poisearch.PoiSearch.OnPoiSearchListener-">setOnPoiSearchListener(PoiSearch.OnPoiSearchListener)</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearch.html" title="com.amap.api.services.poisearch中的类">PoiSearch</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">设置查询监听接口。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearchV2.html#setOnPoiSearchListener-com.amap.api.services.poisearch.PoiSearchV2.OnPoiSearchListener-">setOnPoiSearchListener(PoiSearchV2.OnPoiSearchListener)</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearchV2.html" title="com.amap.api.services.poisearch中的类">PoiSearchV2</a></dt>
<dd>
<div class="block">设置查询监听接口。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#setOnRoutePlanSearchListener-com.amap.api.services.route.RouteSearch.OnRoutePlanSearchListener-">setOnRoutePlanSearchListener(RouteSearch.OnRoutePlanSearchListener)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block">未来路径规划搜索结果监听接口设置。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/share/ShareSearch.html#setOnShareSearchListener-com.amap.api.services.share.ShareSearch.OnShareSearchListener-">setOnShareSearchListener(ShareSearch.OnShareSearchListener)</a></span> - 类 中的方法com.amap.api.services.share.<a href="../com/amap/api/services/share/ShareSearch.html" title="com.amap.api.services.share中的类">ShareSearch</a></dt>
<dd>
<div class="block">短串分享结果监听接口设置。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#setOnTruckRouteSearchListener-com.amap.api.services.route.RouteSearch.OnTruckRouteSearchListener-">setOnTruckRouteSearchListener(RouteSearch.OnTruckRouteSearchListener)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block">路径货车搜索结果监听接口设置。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/weather/WeatherSearch.html#setOnWeatherSearchListener-com.amap.api.services.weather.WeatherSearch.OnWeatherSearchListener-">setOnWeatherSearchListener(WeatherSearch.OnWeatherSearchListener)</a></span> - 类 中的方法com.amap.api.services.weather.<a href="../com/amap/api/services/weather/WeatherSearch.html" title="com.amap.api.services.weather中的类">WeatherSearch</a></dt>
<dd>
<div class="block">设置天气信息查询监听。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.BusRouteQuery.html#setOriginPoiId-java.lang.String-">setOriginPoiId(String)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.BusRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearchV2.BusRouteQuery</a></dt>
<dd>
<div class="block">起点POI ID

 1、起点POI ID与起点经纬度均填写时，服务使用起点 POI ID；

 2、该字段必须和目的地 POI ID 成组使用。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/DistanceSearch.DistanceQuery.html#setOrigins-java.util.List-">setOrigins(List&lt;LatLonPoint&gt;)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/DistanceSearch.DistanceQuery.html" title="com.amap.api.services.route中的类">DistanceSearch.DistanceQuery</a></dt>
<dd>
<div class="block">设置距离测量起点数据集合,建议不超过100个坐标</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.FromAndTo.html#setOriginType-java.lang.String-">setOriginType(String)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.FromAndTo.html" title="com.amap.api.services.route中的类">RouteSearch.FromAndTo</a></dt>
<dd>
<div class="block">设置路径规划起点类型。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.FromAndTo.html#setOriginType-java.lang.String-">setOriginType(String)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.FromAndTo.html" title="com.amap.api.services.route中的类">RouteSearchV2.FromAndTo</a></dt>
<dd>
<div class="block">设置路径规划起点类型。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/cloud/CloudSearch.Query.html#setPageNum-int-">setPageNum(int)</a></span> - 类 中的方法com.amap.api.services.cloud.<a href="../com/amap/api/services/cloud/CloudSearch.Query.html" title="com.amap.api.services.cloud中的类">CloudSearch.Query</a></dt>
<dd>
<div class="block">设置查询第几页的结果数目。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/district/DistrictSearchQuery.html#setPageNum-int-">setPageNum(int)</a></span> - 类 中的方法com.amap.api.services.district.<a href="../com/amap/api/services/district/DistrictSearchQuery.html" title="com.amap.api.services.district中的类">DistrictSearchQuery</a></dt>
<dd>
<div class="block">设置查询第几页的结果数目。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearch.Query.html#setPageNum-int-">setPageNum(int)</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearch.Query.html" title="com.amap.api.services.poisearch中的类">PoiSearch.Query</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">设置查询第几页的结果数目。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearchV2.Query.html#setPageNum-int-">setPageNum(int)</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearchV2.Query.html" title="com.amap.api.services.poisearch中的类">PoiSearchV2.Query</a></dt>
<dd>
<div class="block">设置查询第几页的结果数目。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/busline/BusLineQuery.html#setPageNumber-int-">setPageNumber(int)</a></span> - 类 中的方法com.amap.api.services.busline.<a href="../com/amap/api/services/busline/BusLineQuery.html" title="com.amap.api.services.busline中的类">BusLineQuery</a></dt>
<dd>
<div class="block">设置查询第几页。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/busline/BusStationQuery.html#setPageNumber-int-">setPageNumber(int)</a></span> - 类 中的方法com.amap.api.services.busline.<a href="../com/amap/api/services/busline/BusStationQuery.html" title="com.amap.api.services.busline中的类">BusStationQuery</a></dt>
<dd>
<div class="block">设置查询第几页。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/busline/BusLineQuery.html#setPageSize-int-">setPageSize(int)</a></span> - 类 中的方法com.amap.api.services.busline.<a href="../com/amap/api/services/busline/BusLineQuery.html" title="com.amap.api.services.busline中的类">BusLineQuery</a></dt>
<dd>
<div class="block">设置查询每页的结果数目。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/busline/BusStationQuery.html#setPageSize-int-">setPageSize(int)</a></span> - 类 中的方法com.amap.api.services.busline.<a href="../com/amap/api/services/busline/BusStationQuery.html" title="com.amap.api.services.busline中的类">BusStationQuery</a></dt>
<dd>
<div class="block">设置查询每页的结果数目。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/cloud/CloudSearch.Query.html#setPageSize-int-">setPageSize(int)</a></span> - 类 中的方法com.amap.api.services.cloud.<a href="../com/amap/api/services/cloud/CloudSearch.Query.html" title="com.amap.api.services.cloud中的类">CloudSearch.Query</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/district/DistrictSearchQuery.html#setPageSize-int-">setPageSize(int)</a></span> - 类 中的方法com.amap.api.services.district.<a href="../com/amap/api/services/district/DistrictSearchQuery.html" title="com.amap.api.services.district中的类">DistrictSearchQuery</a></dt>
<dd>
<div class="block">设置查询每页的结果数目， 默认值是20 条，取值范围在[1-50]。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearch.Query.html#setPageSize-int-">setPageSize(int)</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearch.Query.html" title="com.amap.api.services.poisearch中的类">PoiSearch.Query</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">设置查询每页的结果数目。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearchV2.Query.html#setPageSize-int-">setPageSize(int)</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearchV2.Query.html" title="com.amap.api.services.poisearch中的类">PoiSearchV2.Query</a></dt>
<dd>
<div class="block">设置查询每页的结果数目。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/DriveRoutePlanResult.html#setPaths-java.util.List-">setPaths(List&lt;DrivePlanPath&gt;)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/DriveRoutePlanResult.html" title="com.amap.api.services.route中的类">DriveRoutePlanResult</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.FromAndTo.html#setPlateNumber-java.lang.String-">setPlateNumber(String)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.FromAndTo.html" title="com.amap.api.services.route中的类">RouteSearch.FromAndTo</a></dt>
<dd>
<div class="block">设置车牌号</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.FromAndTo.html#setPlateNumber-java.lang.String-">setPlateNumber(String)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.FromAndTo.html" title="com.amap.api.services.route中的类">RouteSearchV2.FromAndTo</a></dt>
<dd>
<div class="block">设置车牌号</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.FromAndTo.html#setPlateProvince-java.lang.String-">setPlateProvince(String)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.FromAndTo.html" title="com.amap.api.services.route中的类">RouteSearch.FromAndTo</a></dt>
<dd>
<div class="block">设置车牌省份</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/RegeocodeQuery.html#setPoint-com.amap.api.services.core.LatLonPoint-">setPoint(LatLonPoint)</a></span> - 类 中的方法com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/RegeocodeQuery.html" title="com.amap.api.services.geocoder中的类">RegeocodeQuery</a></dt>
<dd>
<div class="block">设置逆地理编码的地理坐标点。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/nearby/UploadInfo.html#setPoint-com.amap.api.services.core.LatLonPoint-">setPoint(LatLonPoint)</a></span> - 类 中的方法com.amap.api.services.nearby.<a href="../com/amap/api/services/nearby/UploadInfo.html" title="com.amap.api.services.nearby中的类">UploadInfo</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">设置用户位置信息。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/routepoisearch/RoutePOISearch.html#setPoiSearchListener-com.amap.api.services.routepoisearch.RoutePOISearch.OnRoutePOISearchListener-">setPoiSearchListener(RoutePOISearch.OnRoutePOISearchListener)</a></span> - 类 中的方法com.amap.api.services.routepoisearch.<a href="../com/amap/api/services/routepoisearch/RoutePOISearch.html" title="com.amap.api.services.routepoisearch中的类">RoutePOISearch</a></dt>
<dd>
<div class="block">设置查询监听接口。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/RegeocodeQuery.html#setPoiType-java.lang.String-">setPoiType(String)</a></span> - 类 中的方法com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/RegeocodeQuery.html" title="com.amap.api.services.geocoder中的类">RegeocodeQuery</a></dt>
<dd>
<div class="block">设置附近POI类型，结果将会围绕这些类型进行返回。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/TaxiItemV2.html#setPolyline-java.util.List-">setPolyline(List&lt;LatLonPoint&gt;)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/TaxiItemV2.html" title="com.amap.api.services.route中的类">TaxiItemV2</a></dt>
<dd>
<div class="block">线路点的集合</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/GeocodeAddress.html#setPostcode-java.lang.String-">setPostcode(String)</a></span> - 类 中的方法com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/GeocodeAddress.html" title="com.amap.api.services.geocoder中的类">GeocodeAddress</a></dt>
<dd>
<div class="block">海外生效
 邮政编码</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/TaxiItemV2.html#setPrice-float-">setPrice(float)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/TaxiItemV2.html" title="com.amap.api.services.route中的类">TaxiItemV2</a></dt>
<dd>
<div class="block">打车花费</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/ServiceSettings.html#setProtocol-int-">setProtocol(int)</a></span> - 类 中的方法com.amap.api.services.core.<a href="../com/amap/api/services/core/ServiceSettings.html" title="com.amap.api.services.core中的类">ServiceSettings</a></dt>
<dd>
<div class="block">设置访问使用的协议类别。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/weather/LocalWeatherForecast.html#setProvince-java.lang.String-">setProvince(String)</a></span> - 类 中的方法com.amap.api.services.weather.<a href="../com/amap/api/services/weather/LocalWeatherForecast.html" title="com.amap.api.services.weather中的类">LocalWeatherForecast</a></dt>
<dd>
<div class="block">设置省份名称。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/busline/BusLineSearch.html#setQuery-com.amap.api.services.busline.BusLineQuery-">setQuery(BusLineQuery)</a></span> - 类 中的方法com.amap.api.services.busline.<a href="../com/amap/api/services/busline/BusLineSearch.html" title="com.amap.api.services.busline中的类">BusLineSearch</a></dt>
<dd>
<div class="block">设置查询条件。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/busline/BusStationSearch.html#setQuery-com.amap.api.services.busline.BusStationQuery-">setQuery(BusStationQuery)</a></span> - 类 中的方法com.amap.api.services.busline.<a href="../com/amap/api/services/busline/BusStationSearch.html" title="com.amap.api.services.busline中的类">BusStationSearch</a></dt>
<dd>
<div class="block">设置查询条件。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/district/DistrictSearch.html#setQuery-com.amap.api.services.district.DistrictSearchQuery-">setQuery(DistrictSearchQuery)</a></span> - 类 中的方法com.amap.api.services.district.<a href="../com/amap/api/services/district/DistrictSearch.html" title="com.amap.api.services.district中的类">DistrictSearch</a></dt>
<dd>
<div class="block">设置查询条件。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/help/Inputtips.html#setQuery-com.amap.api.services.help.InputtipsQuery-">setQuery(InputtipsQuery)</a></span> - 类 中的方法com.amap.api.services.help.<a href="../com/amap/api/services/help/Inputtips.html" title="com.amap.api.services.help中的类">Inputtips</a></dt>
<dd>
<div class="block">设置提示查询条件。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearch.html#setQuery-com.amap.api.services.poisearch.PoiSearch.Query-">setQuery(PoiSearch.Query)</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearch.html" title="com.amap.api.services.poisearch中的类">PoiSearch</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">设置查询条件。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearchV2.html#setQuery-com.amap.api.services.poisearch.PoiSearchV2.Query-">setQuery(PoiSearchV2.Query)</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearchV2.html" title="com.amap.api.services.poisearch中的类">PoiSearchV2</a></dt>
<dd>
<div class="block">设置查询条件。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/routepoisearch/RoutePOISearch.html#setQuery-com.amap.api.services.routepoisearch.RoutePOISearchQuery-">setQuery(RoutePOISearchQuery)</a></span> - 类 中的方法com.amap.api.services.routepoisearch.<a href="../com/amap/api/services/routepoisearch/RoutePOISearch.html" title="com.amap.api.services.routepoisearch中的类">RoutePOISearch</a></dt>
<dd>
<div class="block">设置查询条件。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/weather/WeatherSearch.html#setQuery-com.amap.api.services.weather.WeatherSearchQuery-">setQuery(WeatherSearchQuery)</a></span> - 类 中的方法com.amap.api.services.weather.<a href="../com/amap/api/services/weather/WeatherSearch.html" title="com.amap.api.services.weather中的类">WeatherSearch</a></dt>
<dd>
<div class="block">设置天气查询条件。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearch.Query.html#setQueryLanguage-java.lang.String-">setQueryLanguage(String)</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearch.Query.html" title="com.amap.api.services.poisearch中的类">PoiSearch.Query</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span>
<div class="block"><span class="deprecationComment">请参考 <a href="../com/amap/api/services/core/ServiceSettings.html#setLanguage-java.lang.String-"><code>ServiceSettings.setLanguage(String)</code></a></span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearchV2.Query.html#setQueryLanguage-java.lang.String-">setQueryLanguage(String)</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearchV2.Query.html" title="com.amap.api.services.poisearch中的类">PoiSearchV2.Query</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span>
<div class="block"><span class="deprecationComment">请参考 <a href="../com/amap/api/services/core/ServiceSettings.html#setLanguage-java.lang.String-"><code>ServiceSettings.setLanguage(String)</code></a></span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/busline/BusLineQuery.html#setQueryString-java.lang.String-">setQueryString(String)</a></span> - 类 中的方法com.amap.api.services.busline.<a href="../com/amap/api/services/busline/BusLineQuery.html" title="com.amap.api.services.busline中的类">BusLineQuery</a></dt>
<dd>
<div class="block">设置查询关键字。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/busline/BusStationQuery.html#setQueryString-java.lang.String-">setQueryString(String)</a></span> - 类 中的方法com.amap.api.services.busline.<a href="../com/amap/api/services/busline/BusStationQuery.html" title="com.amap.api.services.busline中的类">BusStationQuery</a></dt>
<dd>
<div class="block">设置查询关键字。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/RegeocodeQuery.html#setRadius-float-">setRadius(float)</a></span> - 类 中的方法com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/RegeocodeQuery.html" title="com.amap.api.services.geocoder中的类">RegeocodeQuery</a></dt>
<dd>
<div class="block">设置查找范围。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/nearby/NearbySearch.NearbyQuery.html#setRadius-int-">setRadius(int)</a></span> - 类 中的方法com.amap.api.services.nearby.<a href="../com/amap/api/services/nearby/NearbySearch.NearbyQuery.html" title="com.amap.api.services.nearby中的类">NearbySearch.NearbyQuery</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">设置检索的半径，下限为0米，默认为3000米，上限为10000米，可选参数。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/RegeocodeResult.html#setRegeocodeAddress-com.amap.api.services.geocoder.RegeocodeAddress-">setRegeocodeAddress(RegeocodeAddress)</a></span> - 类 中的方法com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/RegeocodeResult.html" title="com.amap.api.services.geocoder中的类">RegeocodeResult</a></dt>
<dd>
<div class="block">设置逆地理编码搜索的结果</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/RegeocodeResult.html#setRegeocodeQuery-com.amap.api.services.geocoder.RegeocodeQuery-">setRegeocodeQuery(RegeocodeQuery)</a></span> - 类 中的方法com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/RegeocodeResult.html" title="com.amap.api.services.geocoder中的类">RegeocodeResult</a></dt>
<dd>
<div class="block">设置该结果对应的查询参数</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/weather/LocalWeatherForecast.html#setReportTime-java.lang.String-">setReportTime(String)</a></span> - 类 中的方法com.amap.api.services.weather.<a href="../com/amap/api/services/weather/LocalWeatherForecast.html" title="com.amap.api.services.weather中的类">LocalWeatherForecast</a></dt>
<dd>
<div class="block">设置天气预报发布时间。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/DrivePath.html#setRestriction-int-">setRestriction(int)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/DrivePath.html" title="com.amap.api.services.route中的类">DrivePath</a></dt>
<dd>
<div class="block">设置驾车规划方案限行结果</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/DrivePathV2.html#setRestriction-int-">setRestriction(int)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/DrivePathV2.html" title="com.amap.api.services.route中的类">DrivePathV2</a></dt>
<dd>
<div class="block">设置驾车规划方案限行结果</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/TimeInfosElement.html#setRestriction-int-">setRestriction(int)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/TimeInfosElement.html" title="com.amap.api.services.route中的类">TimeInfosElement</a></dt>
<dd>
<div class="block">设置驾车未来路径规划路线是否有限行路段。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/NaviWalkType.html#setRoadType-int-">setRoadType(int)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/NaviWalkType.html" title="com.amap.api.services.route中的类">NaviWalkType</a></dt>
<dd>
<div class="block">设置道路类型</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RideStep.html#setRoadType-int-">setRoadType(int)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RideStep.html" title="com.amap.api.services.route中的类">RideStep</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/WalkStep.html#setRoadType-int-">setRoadType(int)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/WalkStep.html" title="com.amap.api.services.route中的类">WalkStep</a></dt>
<dd>
<div class="block">0，普通道路 1，人行横道 3，地下通道 4，过街天桥

 5，地铁通道 6，公园 7，广场 8，扶梯 9，直梯

 10，索道 11，空中通道 12，建筑物穿越通道

 13，行人通道 14，游船路线 15，观光车路线 16，滑道

 18，扩路 19，道路附属连接线 20，阶梯 21，斜坡

 22，桥 23，隧道 30，轮渡</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/road/Road.html#setRoadWidth-float-">setRoadWidth(float)</a></span> - 类 中的方法com.amap.api.services.road.<a href="../com/amap/api/services/road/Road.html" title="com.amap.api.services.road中的类">Road</a></dt>
<dd>
<div class="block">设置道路宽度。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#setRouteSearchListener-com.amap.api.services.route.RouteSearch.OnRouteSearchListener-">setRouteSearchListener(RouteSearch.OnRouteSearchListener)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block">路径搜索结果监听接口设置。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.html#setRouteSearchListener-com.amap.api.services.route.RouteSearchV2.OnRouteSearchListener-">setRouteSearchListener(RouteSearchV2.OnRouteSearchListener)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.html" title="com.amap.api.services.route中的类">RouteSearchV2</a></dt>
<dd>
<div class="block">路径搜索结果监听接口设置。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/SearchCity.html#setSearchCitycode-java.lang.String-">setSearchCitycode(String)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/SearchCity.html" title="com.amap.api.services.route中的类">SearchCity</a></dt>
<dd>
<div class="block">设置城市的编码。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/SearchCity.html#setSearchCityhAdCode-java.lang.String-">setSearchCityhAdCode(String)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/SearchCity.html" title="com.amap.api.services.route中的类">SearchCity</a></dt>
<dd>
<div class="block">设置城市的行政编码。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/SearchCity.html#setSearchCityName-java.lang.String-">setSearchCityName(String)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/SearchCity.html" title="com.amap.api.services.route中的类">SearchCity</a></dt>
<dd>
<div class="block">设置城市的名称。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/road/Crossroad.html#setSecondRoadId-java.lang.String-">setSecondRoadId(String)</a></span> - 类 中的方法com.amap.api.services.road.<a href="../com/amap/api/services/road/Crossroad.html" title="com.amap.api.services.road中的类">Crossroad</a></dt>
<dd>
<div class="block">设置交叉路口的第二条道路ID。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/road/Crossroad.html#setSecondRoadName-java.lang.String-">setSecondRoadName(String)</a></span> - 类 中的方法com.amap.api.services.road.<a href="../com/amap/api/services/road/Crossroad.html" title="com.amap.api.services.road中的类">Crossroad</a></dt>
<dd>
<div class="block">设置交叉路口的第二条道路名称。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/LatLonSharePoint.html#setSharePointName-java.lang.String-">setSharePointName(String)</a></span> - 类 中的方法com.amap.api.services.core.<a href="../com/amap/api/services/core/LatLonSharePoint.html" title="com.amap.api.services.core中的类">LatLonSharePoint</a></dt>
<dd>
<div class="block">设置位置名称。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/district/DistrictSearchQuery.html#setShowBoundary-boolean-">setShowBoundary(boolean)</a></span> - 类 中的方法com.amap.api.services.district.<a href="../com/amap/api/services/district/DistrictSearchQuery.html" title="com.amap.api.services.district中的类">DistrictSearchQuery</a></dt>
<dd>
<div class="block">设置行政区域搜索是否返回边界值，true为返回，false为不返回。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/district/DistrictSearchQuery.html#setShowBusinessArea-boolean-">setShowBusinessArea(boolean)</a></span> - 类 中的方法com.amap.api.services.district.<a href="../com/amap/api/services/district/DistrictSearchQuery.html" title="com.amap.api.services.district中的类">DistrictSearchQuery</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span>
<div class="block"><span class="deprecationComment">自 5.1.0 废弃，使用率极低，而且会影响精准度，取消商圈（biz_area）级别数据，用街道（street）顶替商圈位置</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/district/DistrictSearchQuery.html#setShowChild-boolean-">setShowChild(boolean)</a></span> - 类 中的方法com.amap.api.services.district.<a href="../com/amap/api/services/district/DistrictSearchQuery.html" title="com.amap.api.services.district中的类">DistrictSearchQuery</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span>
<div class="block"><span class="deprecationComment">自 7.1.0 废弃，设置下级行政区划参考 <a href="../com/amap/api/services/district/DistrictSearchQuery.html#setSubDistrict-int-"><code>DistrictSearchQuery.setSubDistrict(int)</code></a> ()}</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearchV2.Query.html#setShowFields-com.amap.api.services.poisearch.PoiSearchV2.ShowFields-">setShowFields(PoiSearchV2.ShowFields)</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearchV2.Query.html" title="com.amap.api.services.poisearch中的类">PoiSearchV2.Query</a></dt>
<dd>
<div class="block">扩展字段 base表示只返回基础数据，all表示所有数据 ，默认 base</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.BusRouteQuery.html#setShowFields-int-">setShowFields(int)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.BusRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearchV2.BusRouteQuery</a></dt>
<dd>
<div class="block">扩展字段，all表示所有数据 ，默认 cost <a href="../com/amap/api/services/route/RouteSearchV2.ShowFields.html" title="com.amap.api.services.route中的类"><code>RouteSearchV2.ShowFields</code></a></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.DriveRouteQuery.html#setShowFields-int-">setShowFields(int)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.DriveRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearchV2.DriveRouteQuery</a></dt>
<dd>
<div class="block">扩展字段 <a href="../com/amap/api/services/route/RouteSearchV2.ShowFields.html" title="com.amap.api.services.route中的类"><code>RouteSearchV2.ShowFields</code></a> 说明</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.RideRouteQuery.html#setShowFields-int-">setShowFields(int)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.RideRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearchV2.RideRouteQuery</a></dt>
<dd>
<div class="block">扩展字段 all表示所有数据 ，默认 cost <a href="../com/amap/api/services/route/RouteSearchV2.ShowFields.html" title="com.amap.api.services.route中的类"><code>RouteSearchV2.ShowFields</code></a></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.WalkRouteQuery.html#setShowFields-int-">setShowFields(int)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.WalkRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearchV2.WalkRouteQuery</a></dt>
<dd>
<div class="block">扩展字段 <a href="../com/amap/api/services/route/RouteSearchV2.ShowFields.html" title="com.amap.api.services.route中的类"><code>RouteSearchV2.ShowFields</code></a></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/cloud/CloudSearch.Query.html#setSortingrules-com.amap.api.services.cloud.CloudSearch.Sortingrules-">setSortingrules(CloudSearch.Sortingrules)</a></span> - 类 中的方法com.amap.api.services.cloud.<a href="../com/amap/api/services/cloud/CloudSearch.Query.html" title="com.amap.api.services.cloud中的类">CloudSearch.Query</a></dt>
<dd>
<div class="block">设置排序规则。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/ServiceSettings.html#setSoTimeOut-int-">setSoTimeOut(int)</a></span> - 类 中的方法com.amap.api.services.core.<a href="../com/amap/api/services/core/ServiceSettings.html" title="com.amap.api.services.core中的类">ServiceSettings</a></dt>
<dd>
<div class="block">设置搜索读取返回结果超时。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearch.Query.html#setSpecial-boolean-">setSpecial(boolean)</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearch.Query.html" title="com.amap.api.services.poisearch中的类">PoiSearch.Query</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">是否对结果进行人工干预，如火车站，原因为POI较为特殊，结果存在人工干预，干预结果优先，所以距离优先的排序未生效
 仅周边搜索支持
 默认为 true</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearchV2.Query.html#setSpecial-boolean-">setSpecial(boolean)</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearchV2.Query.html" title="com.amap.api.services.poisearch中的类">PoiSearchV2.Query</a></dt>
<dd>
<div class="block">是否对结果进行人工干预，如火车站，原因为POI较为特殊，结果存在人工干预，干预结果优先，所以距离优先的排序未生效
 仅周边搜索支持
 默认为 true</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.FromAndTo.html#setStartPoiID-java.lang.String-">setStartPoiID(String)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.FromAndTo.html" title="com.amap.api.services.route中的类">RouteSearch.FromAndTo</a></dt>
<dd>
<div class="block">设置路径规划起点POI的ID。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.FromAndTo.html#setStartPoiID-java.lang.String-">setStartPoiID(String)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.FromAndTo.html" title="com.amap.api.services.route中的类">RouteSearchV2.FromAndTo</a></dt>
<dd>
<div class="block">设置路径规划起点POI的ID。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/DriveStepV2.html#setStepDistance-int-">setStepDistance(int)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/DriveStepV2.html" title="com.amap.api.services.route中的类">DriveStepV2</a></dt>
<dd>
<div class="block">设置分段距离信息</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/BusPath.html#setSteps-java.util.List-">setSteps(List&lt;BusStep&gt;)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/BusPath.html" title="com.amap.api.services.route中的类">BusPath</a></dt>
<dd>
<div class="block">设置公交路径规划方案的路段列表。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/BusPathV2.html#setSteps-java.util.List-">setSteps(List&lt;BusStepV2&gt;)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/BusPathV2.html" title="com.amap.api.services.route中的类">BusPathV2</a></dt>
<dd>
<div class="block">设置公交路径规划方案的路段列表。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/DrivePlanPath.html#setSteps-java.util.List-">setSteps(List&lt;DrivePlanStep&gt;)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/DrivePlanPath.html" title="com.amap.api.services.route中的类">DrivePlanPath</a></dt>
<dd>
<div class="block">设置导航路段。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/StreetNumber.html#setStreet-java.lang.String-">setStreet(String)</a></span> - 类 中的方法com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/StreetNumber.html" title="com.amap.api.services.geocoder中的类">StreetNumber</a></dt>
<dd>
<div class="block">设置门牌信息中的街道名称。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/district/DistrictSearchQuery.html#setSubDistrict-int-">setSubDistrict(int)</a></span> - 类 中的方法com.amap.api.services.district.<a href="../com/amap/api/services/district/DistrictSearchQuery.html" title="com.amap.api.services.district中的类">DistrictSearchQuery</a></dt>
<dd>
<div class="block">设置显示下级行政区级数（行政区级别包括：国家、省/直辖市、市、区/县、乡镇/街道多级数据）</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/SuggestionCity.html#setSuggestionNum-int-">setSuggestionNum(int)</a></span> - 类 中的方法com.amap.api.services.core.<a href="../com/amap/api/services/core/SuggestionCity.html" title="com.amap.api.services.core中的类">SuggestionCity</a></dt>
<dd>
<div class="block">设置建议的结果数目。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/cloud/CloudSearch.Query.html#setTableID-java.lang.String-">setTableID(String)</a></span> - 类 中的方法com.amap.api.services.cloud.<a href="../com/amap/api/services/cloud/CloudSearch.Query.html" title="com.amap.api.services.cloud中的类">CloudSearch.Query</a></dt>
<dd>
<div class="block">设置搜索的表tableid。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.BusRouteQuery.html#setTime-java.lang.String-">setTime(String)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.BusRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearchV2.BusRouteQuery</a></dt>
<dd>
<div class="block">请求时间

 例如:9-54</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/DriveRoutePlanResult.html#setTimeInfos-java.util.List-">setTimeInfos(List&lt;TimeInfo&gt;)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/DriveRoutePlanResult.html" title="com.amap.api.services.route中的类">DriveRoutePlanResult</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/nearby/NearbySearch.NearbyQuery.html#setTimeRange-int-">setTimeRange(int)</a></span> - 类 中的方法com.amap.api.services.nearby.<a href="../com/amap/api/services/nearby/NearbySearch.NearbyQuery.html" title="com.amap.api.services.nearby中的类">NearbySearch.NearbyQuery</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">设置检索的时间范围，默认从当前范围向前推移period长度的时间，单位秒级。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/TimeInfosElement.html#setTMCs-java.util.List-">setTMCs(List&lt;TMC&gt;)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/TimeInfosElement.html" title="com.amap.api.services.route中的类">TimeInfosElement</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/Cost.html#setTollDistance-float-">setTollDistance(float)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/Cost.html" title="com.amap.api.services.route中的类">Cost</a></dt>
<dd>
<div class="block">设置收费路段里程，单位：米，包括分段信息</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/Cost.html#setTollRoad-java.lang.String-">setTollRoad(String)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/Cost.html" title="com.amap.api.services.route中的类">Cost</a></dt>
<dd>
<div class="block">设置主要收费道路</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/Cost.html#setTolls-float-">setTolls(float)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/Cost.html" title="com.amap.api.services.route中的类">Cost</a></dt>
<dd>
<div class="block">设置此路线道路收费，单位：元，包括分段信息</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/share/ShareSearch.ShareFromAndTo.html#setToName-java.lang.String-">setToName(String)</a></span> - 类 中的方法com.amap.api.services.share.<a href="../com/amap/api/services/share/ShareSearch.ShareFromAndTo.html" title="com.amap.api.services.share中的类">ShareSearch.ShareFromAndTo</a></dt>
<dd>
<div class="block">设置终点分享名称。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/Cost.html#setTrafficLights-int-">setTrafficLights(int)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/Cost.html" title="com.amap.api.services.route中的类">Cost</a></dt>
<dd>
<div class="block">设置方案中红绿灯个数</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.TruckRouteQuery.html#setTruckAxis-float-">setTruckAxis(float)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.TruckRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearch.TruckRouteQuery</a></dt>
<dd>
<div class="block">车辆轴数</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.TruckRouteQuery.html#setTruckHeight-float-">setTruckHeight(float)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.TruckRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearch.TruckRouteQuery</a></dt>
<dd>
<div class="block">车辆高度</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.TruckRouteQuery.html#setTruckLoad-float-">setTruckLoad(float)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.TruckRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearch.TruckRouteQuery</a></dt>
<dd>
<div class="block">车辆总重</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.TruckRouteQuery.html#setTruckSize-int-">setTruckSize(int)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.TruckRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearch.TruckRouteQuery</a></dt>
<dd>
<div class="block">高德此分类依据国标
 1：微型车，2：轻型车（默认值），3：中型车，4：重型车<br>
 根据新的汽车分类国家标准(GB9417-89)就可方便地区分车型。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.TruckRouteQuery.html#setTruckWeight-float-">setTruckWeight(float)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.TruckRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearch.TruckRouteQuery</a></dt>
<dd>
<div class="block">货车核定载重</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.TruckRouteQuery.html#setTruckWidth-float-">setTruckWidth(float)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.TruckRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearch.TruckRouteQuery</a></dt>
<dd>
<div class="block">车辆宽度</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/help/InputtipsQuery.html#setType-java.lang.String-">setType(String)</a></span> - 类 中的方法com.amap.api.services.help.<a href="../com/amap/api/services/help/InputtipsQuery.html" title="com.amap.api.services.help中的类">InputtipsQuery</a></dt>
<dd>
<div class="block">限定搜索类型。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/nearby/NearbySearch.NearbyQuery.html#setType-com.amap.api.services.nearby.NearbySearchFunctionType-">setType(NearbySearchFunctionType)</a></span> - 类 中的方法com.amap.api.services.nearby.<a href="../com/amap/api/services/nearby/NearbySearch.NearbyQuery.html" title="com.amap.api.services.nearby中的类">NearbySearch.NearbyQuery</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">设置检索的类型。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/road/Road.html#setType-java.lang.String-">setType(String)</a></span> - 类 中的方法com.amap.api.services.road.<a href="../com/amap/api/services/road/Road.html" title="com.amap.api.services.road中的类">Road</a></dt>
<dd>
<div class="block">设置道路类型。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/DistanceSearch.DistanceQuery.html#setType-int-">setType(int)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/DistanceSearch.DistanceQuery.html" title="com.amap.api.services.route中的类">DistanceSearch.DistanceQuery</a></dt>
<dd>
<div class="block">设置距离测量方式</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.DriveRouteQuery.html#setUseFerry-boolean-">setUseFerry(boolean)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.DriveRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearch.DriveRouteQuery</a></dt>
<dd>
<div class="block">是否使用轮渡，默认使用</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.DriveRouteQuery.html#setUseFerry-boolean-">setUseFerry(boolean)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.DriveRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearchV2.DriveRouteQuery</a></dt>
<dd>
<div class="block">是否使用轮渡，默认使用</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/nearby/NearbySearch.html#setUserID-java.lang.String-">setUserID(String)</a></span> - 类 中的方法com.amap.api.services.nearby.<a href="../com/amap/api/services/nearby/NearbySearch.html" title="com.amap.api.services.nearby中的类">NearbySearch</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">设置用户id，此id可以是用户在业务逻辑中的私有id，设置的id用于检索结果区分用户，长度不超过32个字符，只能包含英文、数字、下划线、短横杠，反之在上传时会返回13。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/nearby/UploadInfo.html#setUserID-java.lang.String-">setUserID(String)</a></span> - 类 中的方法com.amap.api.services.nearby.<a href="../com/amap/api/services/nearby/UploadInfo.html" title="com.amap.api.services.nearby中的类">UploadInfo</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">设置用户ID。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearchV2.ShowFields.html#setValue-int-">setValue(int)</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearchV2.ShowFields.html" title="com.amap.api.services.poisearch中的类">PoiSearchV2.ShowFields</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/weather/LocalWeatherForecast.html#setWeatherForecast-java.util.List-">setWeatherForecast(List&lt;LocalDayWeatherForecast&gt;)</a></span> - 类 中的方法com.amap.api.services.weather.<a href="../com/amap/api/services/weather/LocalWeatherForecast.html" title="com.amap.api.services.weather中的类">LocalWeatherForecast</a></dt>
<dd>
<div class="block">设置天气预报的结果。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/weather/LocalDayWeatherForecast.html#setWeek-java.lang.String-">setWeek(String)</a></span> - 类 中的方法com.amap.api.services.weather.<a href="../com/amap/api/services/weather/LocalDayWeatherForecast.html" title="com.amap.api.services.weather中的类">LocalDayWeatherForecast</a></dt>
<dd>
<div class="block">设置预报天气的星期。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/share/ShareSearch.ShareBusRouteQuery.html#ShareBusRouteQuery-com.amap.api.services.share.ShareSearch.ShareFromAndTo-int-">ShareBusRouteQuery(ShareSearch.ShareFromAndTo, int)</a></span> - 类 的构造器com.amap.api.services.share.<a href="../com/amap/api/services/share/ShareSearch.ShareBusRouteQuery.html" title="com.amap.api.services.share中的类">ShareSearch.ShareBusRouteQuery</a></dt>
<dd>
<div class="block">根据给定的参数构造一个ShareBusRouteQuery的新对象。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/share/ShareSearch.ShareDrivingRouteQuery.html#ShareDrivingRouteQuery-com.amap.api.services.share.ShareSearch.ShareFromAndTo-int-">ShareDrivingRouteQuery(ShareSearch.ShareFromAndTo, int)</a></span> - 类 的构造器com.amap.api.services.share.<a href="../com/amap/api/services/share/ShareSearch.ShareDrivingRouteQuery.html" title="com.amap.api.services.share中的类">ShareSearch.ShareDrivingRouteQuery</a></dt>
<dd>
<div class="block">根据给定的参数构造一个ShareDrivingRouteQuery的新对象。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/share/ShareSearch.ShareFromAndTo.html#ShareFromAndTo-com.amap.api.services.core.LatLonPoint-com.amap.api.services.core.LatLonPoint-">ShareFromAndTo(LatLonPoint, LatLonPoint)</a></span> - 类 的构造器com.amap.api.services.share.<a href="../com/amap/api/services/share/ShareSearch.ShareFromAndTo.html" title="com.amap.api.services.share中的类">ShareSearch.ShareFromAndTo</a></dt>
<dd>
<div class="block">根据给定的参数来构造一个ShareFromAndTo的新对象。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/share/ShareSearch.ShareNaviQuery.html#ShareNaviQuery-com.amap.api.services.share.ShareSearch.ShareFromAndTo-int-">ShareNaviQuery(ShareSearch.ShareFromAndTo, int)</a></span> - 类 的构造器com.amap.api.services.share.<a href="../com/amap/api/services/share/ShareSearch.ShareNaviQuery.html" title="com.amap.api.services.share中的类">ShareSearch.ShareNaviQuery</a></dt>
<dd>
<div class="block">根据给定的参数构造一个ShareNaviQuery 的新对象。</div>
</dd>
<dt><a href="../com/amap/api/services/share/ShareSearch.html" title="com.amap.api.services.share中的类"><span class="typeNameLink">ShareSearch</span></a> - <a href="../com/amap/api/services/share/package-summary.html">com.amap.api.services.share</a>中的类</dt>
<dd>
<div class="block">本类为短串分享的入口类。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/share/ShareSearch.html#ShareSearch-Context-">ShareSearch(Context)</a></span> - 类 的构造器com.amap.api.services.share.<a href="../com/amap/api/services/share/ShareSearch.html" title="com.amap.api.services.share中的类">ShareSearch</a></dt>
<dd>
<div class="block">根据给定的参数来构造一个ShareSearch的新对象。</div>
</dd>
<dt><a href="../com/amap/api/services/share/ShareSearch.OnShareSearchListener.html" title="com.amap.api.services.share中的接口"><span class="typeNameLink">ShareSearch.OnShareSearchListener</span></a> - <a href="../com/amap/api/services/share/package-summary.html">com.amap.api.services.share</a>中的接口</dt>
<dd>
<div class="block">此接口定义了短串分享的异步处理回调接口。</div>
</dd>
<dt><a href="../com/amap/api/services/share/ShareSearch.ShareBusRouteQuery.html" title="com.amap.api.services.share中的类"><span class="typeNameLink">ShareSearch.ShareBusRouteQuery</span></a> - <a href="../com/amap/api/services/share/package-summary.html">com.amap.api.services.share</a>中的类</dt>
<dd>
<div class="block">此类定义了公交路径规划的起点、终点、公交路径规划策略。</div>
</dd>
<dt><a href="../com/amap/api/services/share/ShareSearch.ShareDrivingRouteQuery.html" title="com.amap.api.services.share中的类"><span class="typeNameLink">ShareSearch.ShareDrivingRouteQuery</span></a> - <a href="../com/amap/api/services/share/package-summary.html">com.amap.api.services.share</a>中的类</dt>
<dd>
<div class="block">此类定义了驾车路径规划的起点、终点、路径规划策略。</div>
</dd>
<dt><a href="../com/amap/api/services/share/ShareSearch.ShareFromAndTo.html" title="com.amap.api.services.share中的类"><span class="typeNameLink">ShareSearch.ShareFromAndTo</span></a> - <a href="../com/amap/api/services/share/package-summary.html">com.amap.api.services.share</a>中的类</dt>
<dd>
<div class="block">此类定义了短串分享的坐标、分享名称。</div>
</dd>
<dt><a href="../com/amap/api/services/share/ShareSearch.ShareNaviQuery.html" title="com.amap.api.services.share中的类"><span class="typeNameLink">ShareSearch.ShareNaviQuery</span></a> - <a href="../com/amap/api/services/share/package-summary.html">com.amap.api.services.share</a>中的类</dt>
<dd>
<div class="block">此类定义了导航分享的起点、终点、驾车策略。</div>
</dd>
<dt><a href="../com/amap/api/services/share/ShareSearch.ShareWalkRouteQuery.html" title="com.amap.api.services.share中的类"><span class="typeNameLink">ShareSearch.ShareWalkRouteQuery</span></a> - <a href="../com/amap/api/services/share/package-summary.html">com.amap.api.services.share</a>中的类</dt>
<dd>
<div class="block">此类定义了步行路径规划的起点、终点、步行路径规划策略。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/share/ShareSearch.ShareWalkRouteQuery.html#ShareWalkRouteQuery-com.amap.api.services.share.ShareSearch.ShareFromAndTo-int-">ShareWalkRouteQuery(ShareSearch.ShareFromAndTo, int)</a></span> - 类 的构造器com.amap.api.services.share.<a href="../com/amap/api/services/share/ShareSearch.ShareWalkRouteQuery.html" title="com.amap.api.services.share中的类">ShareSearch.ShareWalkRouteQuery</a></dt>
<dd>
<div class="block">根据给定的参数构造一个ShareWalkRouteQuery的新对象。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearchV2.ShowFields.html#ShowFields-int-">ShowFields(int)</a></span> - 类 的构造器com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearchV2.ShowFields.html" title="com.amap.api.services.poisearch中的类">PoiSearchV2.ShowFields</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.ShowFields.html#ShowFields--">ShowFields()</a></span> - 类 的构造器com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.ShowFields.html" title="com.amap.api.services.route中的类">RouteSearchV2.ShowFields</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/cloud/CloudSearch.Sortingrules.html#Sortingrules-java.lang.String-boolean-">Sortingrules(String, boolean)</a></span> - 类 的构造器com.amap.api.services.cloud.<a href="../com/amap/api/services/cloud/CloudSearch.Sortingrules.html" title="com.amap.api.services.cloud中的类">CloudSearch.Sortingrules</a></dt>
<dd>
<div class="block">根据给定的参数构造一个 Sortingrules 新对象。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/cloud/CloudSearch.Sortingrules.html#Sortingrules-int-">Sortingrules(int)</a></span> - 类 的构造器com.amap.api.services.cloud.<a href="../com/amap/api/services/cloud/CloudSearch.Sortingrules.html" title="com.amap.api.services.cloud中的类">CloudSearch.Sortingrules</a></dt>
<dd>
<div class="block">根据给定的参数确定 Sortingrules 对象。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/nearby/NearbySearch.html#startUploadNearbyInfoAuto-com.amap.api.services.nearby.UploadInfoCallback-int-">startUploadNearbyInfoAuto(UploadInfoCallback, int)</a></span> - 类 中的方法com.amap.api.services.nearby.<a href="../com/amap/api/services/nearby/NearbySearch.html" title="com.amap.api.services.nearby中的类">NearbySearch</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">启动自动上传信息。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/nearby/NearbySearch.html#stopUploadNearbyInfoAuto--">stopUploadNearbyInfoAuto()</a></span> - 类 中的方法com.amap.api.services.nearby.<a href="../com/amap/api/services/nearby/NearbySearch.html" title="com.amap.api.services.nearby中的类">NearbySearch</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">停止自动上传信息接口。</div>
</dd>
<dt><a href="../com/amap/api/services/geocoder/StreetNumber.html" title="com.amap.api.services.geocoder中的类"><span class="typeNameLink">StreetNumber</span></a> - <a href="../com/amap/api/services/geocoder/package-summary.html">com.amap.api.services.geocoder</a>中的类</dt>
<dd>
<div class="block">逆地理编码返回结果的门牌信息对象。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/StreetNumber.html#StreetNumber--">StreetNumber()</a></span> - 类 的构造器com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/StreetNumber.html" title="com.amap.api.services.geocoder中的类">StreetNumber</a></dt>
<dd>
<div class="block">StreetNumber的构造方法。</div>
</dd>
<dt><a href="../com/amap/api/services/poisearch/SubPoiItem.html" title="com.amap.api.services.poisearch中的类"><span class="typeNameLink">SubPoiItem</span></a> - <a href="../com/amap/api/services/poisearch/package-summary.html">com.amap.api.services.poisearch</a>中的类</dt>
<dd>
<div class="block">定义了一个子POI类</div>
</dd>
<dt><a href="../com/amap/api/services/poisearch/SubPoiItemV2.html" title="com.amap.api.services.poisearch中的类"><span class="typeNameLink">SubPoiItemV2</span></a> - <a href="../com/amap/api/services/poisearch/package-summary.html">com.amap.api.services.poisearch</a>中的类</dt>
<dd>
<div class="block">定义了一个子POI类</div>
</dd>
<dt><a href="../com/amap/api/services/core/SuggestionCity.html" title="com.amap.api.services.core中的类"><span class="typeNameLink">SuggestionCity</span></a> - <a href="../com/amap/api/services/core/package-summary.html">com.amap.api.services.core</a>中的类</dt>
<dd>
<div class="block">定义了当POI搜索无结果时，引擎对于搜索的城市建议内容。</div>
</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">K</a>&nbsp;<a href="index-11.html">L</a>&nbsp;<a href="index-12.html">M</a>&nbsp;<a href="index-13.html">N</a>&nbsp;<a href="index-14.html">O</a>&nbsp;<a href="index-15.html">P</a>&nbsp;<a href="index-16.html">Q</a>&nbsp;<a href="index-17.html">R</a>&nbsp;<a href="index-18.html">S</a>&nbsp;<a href="index-19.html">T</a>&nbsp;<a href="index-20.html">U</a>&nbsp;<a href="index-21.html">V</a>&nbsp;<a href="index-22.html">W</a>&nbsp;</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-17.html">上一个字母</a></li>
<li><a href="index-19.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-18.html" target="_top">框架</a></li>
<li><a href="index-18.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
