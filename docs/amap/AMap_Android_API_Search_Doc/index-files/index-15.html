<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>P - 索引</title>
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="P - \u7D22\u5F15";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-14.html">上一个字母</a></li>
<li><a href="index-16.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-15.html" target="_top">框架</a></li>
<li><a href="index-15.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="contentContainer"><a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">K</a>&nbsp;<a href="index-11.html">L</a>&nbsp;<a href="index-12.html">M</a>&nbsp;<a href="index-13.html">N</a>&nbsp;<a href="index-14.html">O</a>&nbsp;<a href="index-15.html">P</a>&nbsp;<a href="index-16.html">Q</a>&nbsp;<a href="index-17.html">R</a>&nbsp;<a href="index-18.html">S</a>&nbsp;<a href="index-19.html">T</a>&nbsp;<a href="index-20.html">U</a>&nbsp;<a href="index-21.html">V</a>&nbsp;<a href="index-22.html">W</a>&nbsp;<a name="I:P">
<!--   -->
</a>
<h2 class="title">P</h2>
<dl>
<dt><a href="../com/amap/api/services/route/Path.html" title="com.amap.api.services.route中的类"><span class="typeNameLink">Path</span></a> - <a href="../com/amap/api/services/route/package-summary.html">com.amap.api.services.route</a>中的类</dt>
<dd>
<div class="block">定义了路径规划的一个方案。</div>
</dd>
<dt><a href="../com/amap/api/services/poisearch/Photo.html" title="com.amap.api.services.poisearch中的类"><span class="typeNameLink">Photo</span></a> - <a href="../com/amap/api/services/poisearch/package-summary.html">com.amap.api.services.poisearch</a>中的类</dt>
<dd>
<div class="block">POI图片类</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/Photo.html#Photo-Parcel-">Photo(Parcel)</a></span> - 类 的构造器com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/Photo.html" title="com.amap.api.services.poisearch中的类">Photo</a></dt>
<dd>
<div class="block">序列化实现。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearchV2.ShowFields.html#PHOTOS">PHOTOS</a></span> - 类 中的静态变量com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearchV2.ShowFields.html" title="com.amap.api.services.poisearch中的类">PoiSearchV2.ShowFields</a></dt>
<dd>
<div class="block">设置后返回poi图片相关信息</div>
</dd>
<dt><a href="../com/amap/api/services/core/PoiItem.html" title="com.amap.api.services.core中的类"><span class="typeNameLink">PoiItem</span></a> - <a href="../com/amap/api/services/core/package-summary.html">com.amap.api.services.core</a>中的类</dt>
<dd>
<div class="block">定义一个POI（Point Of Interest，兴趣点）。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/PoiItem.html#PoiItem-java.lang.String-com.amap.api.services.core.LatLonPoint-java.lang.String-java.lang.String-">PoiItem(String, LatLonPoint, String, String)</a></span> - 类 的构造器com.amap.api.services.core.<a href="../com/amap/api/services/core/PoiItem.html" title="com.amap.api.services.core中的类">PoiItem</a></dt>
<dd>
<div class="block">根据给定的参数构造一个PoiItem 的新对象。</div>
</dd>
<dt><a href="../com/amap/api/services/poisearch/PoiItemExtension.html" title="com.amap.api.services.poisearch中的类"><span class="typeNameLink">PoiItemExtension</span></a> - <a href="../com/amap/api/services/poisearch/package-summary.html">com.amap.api.services.poisearch</a>中的类</dt>
<dd>
<div class="block">定义了一个POI深度信息类。</div>
</dd>
<dt><a href="../com/amap/api/services/poisearch/PoiNavi.html" title="com.amap.api.services.poisearch中的类"><span class="typeNameLink">PoiNavi</span></a> - <a href="../com/amap/api/services/poisearch/package-summary.html">com.amap.api.services.poisearch</a>中的类</dt>
<dd>
<div class="block">POI导航类</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiNavi.html#PoiNavi-Parcel-">PoiNavi(Parcel)</a></span> - 类 的构造器com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiNavi.html" title="com.amap.api.services.poisearch中的类">PoiNavi</a></dt>
<dd>
<div class="block">序列化实现。</div>
</dd>
<dt><a href="../com/amap/api/services/poisearch/PoiResult.html" title="com.amap.api.services.poisearch中的类"><span class="typeNameLink">PoiResult</span></a> - <a href="../com/amap/api/services/poisearch/package-summary.html">com.amap.api.services.poisearch</a>中的类</dt>
<dd>
<div class="block">POI（Point Of Interest，兴趣点）搜索结果分页显示。</div>
</dd>
<dt><a href="../com/amap/api/services/poisearch/PoiResultV2.html" title="com.amap.api.services.poisearch中的类"><span class="typeNameLink">PoiResultV2</span></a> - <a href="../com/amap/api/services/poisearch/package-summary.html">com.amap.api.services.poisearch</a>中的类</dt>
<dd>
<div class="block">POI（Point Of Interest，兴趣点）搜索结果分页显示。</div>
</dd>
<dt><a href="../com/amap/api/services/poisearch/PoiSearch.html" title="com.amap.api.services.poisearch中的类"><span class="typeNameLink">PoiSearch</span></a> - <a href="../com/amap/api/services/poisearch/package-summary.html">com.amap.api.services.poisearch</a>中的类</dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span>
<div class="block"><span class="deprecationComment">自v9.4.0废弃,推荐使用 <a href="../com/amap/api/services/poisearch/PoiSearchV2.html" title="com.amap.api.services.poisearch中的类"><code>PoiSearchV2</code></a></span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearch.html#PoiSearch-Context-com.amap.api.services.poisearch.PoiSearch.Query-">PoiSearch(Context, PoiSearch.Query)</a></span> - 类 的构造器com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearch.html" title="com.amap.api.services.poisearch中的类">PoiSearch</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">根据给定的参数构造一个PoiSearch 的新对象。</div>
</dd>
<dt><a href="../com/amap/api/services/poisearch/PoiSearch.OnPoiSearchListener.html" title="com.amap.api.services.poisearch中的接口"><span class="typeNameLink">PoiSearch.OnPoiSearchListener</span></a> - <a href="../com/amap/api/services/poisearch/package-summary.html">com.amap.api.services.poisearch</a>中的接口</dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">本类为POI（Point Of Interest，兴趣点）搜索结果的异步处理回调接口。</div>
</dd>
<dt><a href="../com/amap/api/services/poisearch/PoiSearch.Query.html" title="com.amap.api.services.poisearch中的类"><span class="typeNameLink">PoiSearch.Query</span></a> - <a href="../com/amap/api/services/poisearch/package-summary.html">com.amap.api.services.poisearch</a>中的类</dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">此类定义了搜索的关键字，类别及城市。</div>
</dd>
<dt><a href="../com/amap/api/services/poisearch/PoiSearch.SearchBound.html" title="com.amap.api.services.poisearch中的类"><span class="typeNameLink">PoiSearch.SearchBound</span></a> - <a href="../com/amap/api/services/poisearch/package-summary.html">com.amap.api.services.poisearch</a>中的类</dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">此类定义了查询圆形和查询矩形，查询返回的POI的位置在此圆形或矩形内。</div>
</dd>
<dt><a href="../com/amap/api/services/poisearch/PoiSearchV2.html" title="com.amap.api.services.poisearch中的类"><span class="typeNameLink">PoiSearchV2</span></a> - <a href="../com/amap/api/services/poisearch/package-summary.html">com.amap.api.services.poisearch</a>中的类</dt>
<dd>
<div class="block">本类为POI（Point Of Interest，兴趣点）搜索的“入口”类，定义此类，开始搜索。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearchV2.html#PoiSearchV2-Context-com.amap.api.services.poisearch.PoiSearchV2.Query-">PoiSearchV2(Context, PoiSearchV2.Query)</a></span> - 类 的构造器com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearchV2.html" title="com.amap.api.services.poisearch中的类">PoiSearchV2</a></dt>
<dd>
<div class="block">根据给定的参数构造一个PoiSearch 的新对象。</div>
</dd>
<dt><a href="../com/amap/api/services/poisearch/PoiSearchV2.OnPoiSearchListener.html" title="com.amap.api.services.poisearch中的接口"><span class="typeNameLink">PoiSearchV2.OnPoiSearchListener</span></a> - <a href="../com/amap/api/services/poisearch/package-summary.html">com.amap.api.services.poisearch</a>中的接口</dt>
<dd>
<div class="block">本类为POI（Point Of Interest，兴趣点）搜索结果的异步处理回调接口。</div>
</dd>
<dt><a href="../com/amap/api/services/poisearch/PoiSearchV2.Query.html" title="com.amap.api.services.poisearch中的类"><span class="typeNameLink">PoiSearchV2.Query</span></a> - <a href="../com/amap/api/services/poisearch/package-summary.html">com.amap.api.services.poisearch</a>中的类</dt>
<dd>
<div class="block">此类定义了搜索的关键字，类别及城市。</div>
</dd>
<dt><a href="../com/amap/api/services/poisearch/PoiSearchV2.SearchBound.html" title="com.amap.api.services.poisearch中的类"><span class="typeNameLink">PoiSearchV2.SearchBound</span></a> - <a href="../com/amap/api/services/poisearch/package-summary.html">com.amap.api.services.poisearch</a>中的类</dt>
<dd>
<div class="block">此类定义了查询圆形和查询矩形，查询返回的POI的位置在此圆形或矩形内。</div>
</dd>
<dt><a href="../com/amap/api/services/poisearch/PoiSearchV2.ShowFields.html" title="com.amap.api.services.poisearch中的类"><span class="typeNameLink">PoiSearchV2.ShowFields</span></a> - <a href="../com/amap/api/services/poisearch/package-summary.html">com.amap.api.services.poisearch</a>中的类</dt>
<dd>
<div class="block">扩展字段</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.ShowFields.html#POLINE">POLINE</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.ShowFields.html" title="com.amap.api.services.route中的类">RouteSearchV2.ShowFields</a></dt>
<dd>
<div class="block">设置后可返回分路段坐标点串，两点间用“,”分隔</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/cloud/CloudSearch.SearchBound.html#POLYGON_SHAPE">POLYGON_SHAPE</a></span> - 类 中的静态变量com.amap.api.services.cloud.<a href="../com/amap/api/services/cloud/CloudSearch.SearchBound.html" title="com.amap.api.services.cloud中的类">CloudSearch.SearchBound</a></dt>
<dd>
<div class="block">多边形区域</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearch.SearchBound.html#POLYGON_SHAPE">POLYGON_SHAPE</a></span> - 类 中的静态变量com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearch.SearchBound.html" title="com.amap.api.services.poisearch中的类">PoiSearch.SearchBound</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">多边形区域</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearchV2.SearchBound.html#POLYGON_SHAPE">POLYGON_SHAPE</a></span> - 类 中的静态变量com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearchV2.SearchBound.html" title="com.amap.api.services.poisearch中的类">PoiSearchV2.SearchBound</a></dt>
<dd>
<div class="block">多边形区域</div>
</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">K</a>&nbsp;<a href="index-11.html">L</a>&nbsp;<a href="index-12.html">M</a>&nbsp;<a href="index-13.html">N</a>&nbsp;<a href="index-14.html">O</a>&nbsp;<a href="index-15.html">P</a>&nbsp;<a href="index-16.html">Q</a>&nbsp;<a href="index-17.html">R</a>&nbsp;<a href="index-18.html">S</a>&nbsp;<a href="index-19.html">T</a>&nbsp;<a href="index-20.html">U</a>&nbsp;<a href="index-21.html">V</a>&nbsp;<a href="index-22.html">W</a>&nbsp;</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-14.html">上一个字母</a></li>
<li><a href="index-16.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-15.html" target="_top">框架</a></li>
<li><a href="index-15.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
