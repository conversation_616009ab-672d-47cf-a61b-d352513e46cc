<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>A - 索引</title>
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="A - \u7D22\u5F15";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个字母</li>
<li><a href="index-2.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-1.html" target="_top">框架</a></li>
<li><a href="index-1.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="contentContainer"><a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">K</a>&nbsp;<a href="index-11.html">L</a>&nbsp;<a href="index-12.html">M</a>&nbsp;<a href="index-13.html">N</a>&nbsp;<a href="index-14.html">O</a>&nbsp;<a href="index-15.html">P</a>&nbsp;<a href="index-16.html">Q</a>&nbsp;<a href="index-17.html">R</a>&nbsp;<a href="index-18.html">S</a>&nbsp;<a href="index-19.html">T</a>&nbsp;<a href="index-20.html">U</a>&nbsp;<a href="index-21.html">V</a>&nbsp;<a href="index-22.html">W</a>&nbsp;<a name="I:A">
<!--   -->
</a>
<h2 class="title">A</h2>
<dl>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/cloud/CloudSearch.Query.html#addFilterNum-java.lang.String-java.lang.String-java.lang.String-">addFilterNum(String, String, String)</a></span> - 类 中的方法com.amap.api.services.cloud.<a href="../com/amap/api/services/cloud/CloudSearch.Query.html" title="com.amap.api.services.cloud中的类">CloudSearch.Query</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span>
<div class="block"><span class="deprecationComment">使用 <a href="../com/amap/api/services/cloud/CloudSearch.Query.html#addFilterString-java.lang.String-java.lang.String-"><code>CloudSearch.Query.addFilterString(String, String)</code></a> 替换</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/cloud/CloudSearch.Query.html#addFilterString-java.lang.String-java.lang.String-">addFilterString(String, String)</a></span> - 类 中的方法com.amap.api.services.cloud.<a href="../com/amap/api/services/cloud/CloudSearch.Query.html" title="com.amap.api.services.cloud中的类">CloudSearch.Query</a></dt>
<dd>
<div class="block">1：支持建立索引的字段根据多个条件筛选，多个条件用&&符号连接。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/nearby/NearbySearch.html#addNearbyListener-com.amap.api.services.nearby.NearbySearch.NearbyListener-">addNearbyListener(NearbySearch.NearbyListener)</a></span> - 类 中的方法com.amap.api.services.nearby.<a href="../com/amap/api/services/nearby/NearbySearch.html" title="com.amap.api.services.nearby中的类">NearbySearch</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">设置NearbySearch.NearbyListener监听器对象，用作调用查询周边信息的监听回调函数，可以添加多个。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/DistanceSearch.DistanceQuery.html#addOrigins-com.amap.api.services.core.LatLonPoint...-">addOrigins(LatLonPoint...)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/DistanceSearch.DistanceQuery.html" title="com.amap.api.services.route中的类">DistanceSearch.DistanceQuery</a></dt>
<dd>
<div class="block">设置距离测量起点数据集合,建议不超过s100个坐标
 和<a href="../com/amap/api/services/route/DistanceSearch.DistanceQuery.html#setOrigins-java.util.List-"><code>DistanceSearch.DistanceQuery.setOrigins(List)</code></a> 不同，不需要自行创建集合对象
 如果已经调用了<a href="../com/amap/api/services/route/DistanceSearch.DistanceQuery.html#setOrigins-java.util.List-"><code>DistanceSearch.DistanceQuery.setOrigins(List)</code></a>，再次调用此方法，之前的内容也会被保留</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearchV2.ShowFields.html#ALL">ALL</a></span> - 类 中的静态变量com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearchV2.ShowFields.html" title="com.amap.api.services.poisearch中的类">PoiSearchV2.ShowFields</a></dt>
<dd>
<div class="block">设置后添加所有字段</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.ShowFields.html#ALL">ALL</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.ShowFields.html" title="com.amap.api.services.route中的类">RouteSearchV2.ShowFields</a></dt>
<dd>
<div class="block">设置后添加所有字段</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.AlternativeRoute.html#ALTERNATIVE_ROUTE_ONE">ALTERNATIVE_ROUTE_ONE</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.AlternativeRoute.html" title="com.amap.api.services.route中的类">RouteSearchV2.AlternativeRoute</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.AlternativeRoute.html#ALTERNATIVE_ROUTE_THREE">ALTERNATIVE_ROUTE_THREE</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.AlternativeRoute.html" title="com.amap.api.services.route中的类">RouteSearchV2.AlternativeRoute</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.AlternativeRoute.html#ALTERNATIVE_ROUTE_TWO">ALTERNATIVE_ROUTE_TWO</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.AlternativeRoute.html" title="com.amap.api.services.route中的类">RouteSearchV2.AlternativeRoute</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.AlternativeRoute.html#AlternativeRoute--">AlternativeRoute()</a></span> - 类 的构造器com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.AlternativeRoute.html" title="com.amap.api.services.route中的类">RouteSearchV2.AlternativeRoute</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/GeocodeSearch.html#AMAP">AMAP</a></span> - 类 中的静态变量com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/GeocodeSearch.html" title="com.amap.api.services.geocoder中的类">GeocodeSearch</a></dt>
<dd>
<div class="block">输入参数坐标为高德类型。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/nearby/NearbySearch.html#AMAP">AMAP</a></span> - 类 中的静态变量com.amap.api.services.nearby.<a href="../com/amap/api/services/nearby/NearbySearch.html" title="com.amap.api.services.nearby中的类">NearbySearch</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">上传的坐标类型为gcj02坐标系，应用于UploadInfo.setCoordType(int coordType)方法。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#AMAP_ACCESS_TOO_FREQUENT">AMAP_ACCESS_TOO_FREQUENT</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">用户访问过于频繁 ErrorCode：1005</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#AMAP_CLIENT_ERROR_PROTOCOL">AMAP_CLIENT_ERROR_PROTOCOL</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">协议解析错误 - ProtocolException ErrorCode：1801</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#AMAP_CLIENT_ERRORCODE_MISSSING">AMAP_CLIENT_ERRORCODE_MISSSING</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">没有对应的错误 ErrorCode：1800</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#AMAP_CLIENT_ERRORCODE_MISSSING_TPPE">AMAP_CLIENT_ERRORCODE_MISSSING_TPPE</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#AMAP_CLIENT_INVALID_PARAMETER">AMAP_CLIENT_INVALID_PARAMETER</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">无效的参数 - IllegalArgumentException ErrorCode：1901</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#AMAP_CLIENT_IO_EXCEPTION">AMAP_CLIENT_IO_EXCEPTION</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">IO 操作异常 - IOException ErrorCode：1902</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#AMAP_CLIENT_NEARBY_NULL_RESULT">AMAP_CLIENT_NEARBY_NULL_RESULT</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">NearbyInfo对象为空 ErrorCode：2202</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#AMAP_CLIENT_NETWORK_EXCEPTION">AMAP_CLIENT_NETWORK_EXCEPTION</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">http或socket连接失败 - ConnectionException ErrorCode：1806</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#AMAP_CLIENT_NULLPOINT_EXCEPTION">AMAP_CLIENT_NULLPOINT_EXCEPTION</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">空指针异常 - NullPointException ErrorCode：1903</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#AMAP_CLIENT_OVER_KEYWORD_LEN_MAX_COUNT_EXCEPTION">AMAP_CLIENT_OVER_KEYWORD_LEN_MAX_COUNT_EXCEPTION</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">搜索关键字过长</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#AMAP_CLIENT_OVER_PASSAREA_ITEM_POINT_COUNT_EXCEPTION">AMAP_CLIENT_OVER_PASSAREA_ITEM_POINT_COUNT_EXCEPTION</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">避让区域点个数超限</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#AMAP_CLIENT_OVER_PASSAREA_MAX_AREA_EXCEPTION">AMAP_CLIENT_OVER_PASSAREA_MAX_AREA_EXCEPTION</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">避让区域大小超限</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#AMAP_CLIENT_OVER_PASSAREA_MAX_COUNT_EXCEPTION">AMAP_CLIENT_OVER_PASSAREA_MAX_COUNT_EXCEPTION</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">避让区域个数超限</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#AMAP_CLIENT_OVER_PASSBY_MAX_COUNT_EXCEPTION">AMAP_CLIENT_OVER_PASSBY_MAX_COUNT_EXCEPTION</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">途经点个数超限</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#AMAP_CLIENT_SOCKET_TIMEOUT_EXCEPTION">AMAP_CLIENT_SOCKET_TIMEOUT_EXCEPTION</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">socket 连接超时 - SocketTimeoutException ErrorCode：1802</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#AMAP_CLIENT_UNKNOWHOST_EXCEPTION">AMAP_CLIENT_UNKNOWHOST_EXCEPTION</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">未知主机 - UnKnowHostException ErrorCode：1804</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#AMAP_CLIENT_UNKNOWN_ERROR">AMAP_CLIENT_UNKNOWN_ERROR</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">未知错误 ErrorCode：1900</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#AMAP_CLIENT_UNKNOWN_ERROR_TYPE">AMAP_CLIENT_UNKNOWN_ERROR_TYPE</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#AMAP_CLIENT_UPLOAD_LOCATION_ERROR">AMAP_CLIENT_UPLOAD_LOCATION_ERROR</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">Point为空，或与前次上传的相同 ErrorCode：2204</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#AMAP_CLIENT_UPLOAD_TOO_FREQUENT">AMAP_CLIENT_UPLOAD_TOO_FREQUENT</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">两次单次上传的间隔低于7秒 ErrorCode：2203</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#AMAP_CLIENT_UPLOADAUTO_STARTED_ERROR">AMAP_CLIENT_UPLOADAUTO_STARTED_ERROR</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">已开启自动上传 ErrorCode：2200</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#AMAP_CLIENT_URL_EXCEPTION">AMAP_CLIENT_URL_EXCEPTION</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">url异常 - MalformedURLException ErrorCode：1803</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#AMAP_CLIENT_USERID_ILLEGAL">AMAP_CLIENT_USERID_ILLEGAL</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">USERID非法 ErrorCode：2201</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#AMAP_DAILY_QUERY_OVER_LIMIT">AMAP_DAILY_QUERY_OVER_LIMIT</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">访问已超出日访问量 ErrorCode：1004</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#AMAP_ENGINE_CONNECT_TIMEOUT">AMAP_ENGINE_CONNECT_TIMEOUT</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">服务端请求链接超时 ErrorCode：1102</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#AMAP_ENGINE_RESPONSE_DATA_ERROR">AMAP_ENGINE_RESPONSE_DATA_ERROR</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">引擎返回数据异常 ErrorCode：1101</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#AMAP_ENGINE_RESPONSE_ERROR">AMAP_ENGINE_RESPONSE_ERROR</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">请求服务响应错误 ErrorCode：1100</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#AMAP_ENGINE_RETURN_TIMEOUT">AMAP_ENGINE_RETURN_TIMEOUT</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">读取服务结果超时 ErrorCode：1103</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#AMAP_ENGINE_TABLEID_NOT_EXIST">AMAP_ENGINE_TABLEID_NOT_EXIST</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">key对应的tableID不存在 ErrorCode：2003</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#AMAP_ID_NOT_EXIST">AMAP_ID_NOT_EXIST</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">ID不存在 ErrorCode：2001</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#AMAP_INSUFFICIENT_PRIVILEGES">AMAP_INSUFFICIENT_PRIVILEGES</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">权限不足，服务请求被拒绝 ErrorCode：1012</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#AMAP_INVALID_USER_DOMAIN">AMAP_INVALID_USER_DOMAIN</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">用户域名无效 ErrorCode：1007</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#AMAP_INVALID_USER_IP">AMAP_INVALID_USER_IP</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">用户IP无效 ErrorCode：1006</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#AMAP_INVALID_USER_KEY">AMAP_INVALID_USER_KEY</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">用户key不正确或过期 ErrorCode：1002</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#AMAP_INVALID_USER_SCODE">AMAP_INVALID_USER_SCODE</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">用户MD5安全码未通过 ErrorCode：1008</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#AMAP_IP_QUERY_OVER_LIMIT">AMAP_IP_QUERY_OVER_LIMIT</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">IP访问超限 ErrorCode：1010</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#AMAP_NEARBY_INVALID_USERID">AMAP_NEARBY_INVALID_USERID</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">找不到对应的userid信息,请检查您提供的userid是否存在 ErrorCode：2100</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#AMAP_NEARBY_KEY_NOT_BIND">AMAP_NEARBY_KEY_NOT_BIND</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">App key未开通“附近”功能,请注册附近KEY ErrorCode：2101</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#AMAP_NOT_SUPPORT_HTTPS">AMAP_NOT_SUPPORT_HTTPS</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">服务不支持https请求 ErrorCode：1011</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#AMAP_OVER_DIRECTION_RANGE">AMAP_OVER_DIRECTION_RANGE</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">起点终点距离过长 ErrorCode：3003</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#AMAP_ROUTE_FAIL">AMAP_ROUTE_FAIL</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">路线计算失败，通常是由于道路连通关系导致 ErrorCode：3002</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#AMAP_ROUTE_NO_ROADS_NEARBY">AMAP_ROUTE_NO_ROADS_NEARBY</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">规划点（起点、终点、途经点）附近搜不到路 ErrorCode：3001</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#AMAP_ROUTE_OUT_OF_SERVICE">AMAP_ROUTE_OUT_OF_SERVICE</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">规划点（包括起点、终点、途经点）不在中国陆地范围内 ErrorCode：3000</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#AMAP_SERVICE_ILLEGAL_REQUEST">AMAP_SERVICE_ILLEGAL_REQUEST</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">请求协议非法 ErrorCode：1202</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#AMAP_SERVICE_INVALID_PARAMS">AMAP_SERVICE_INVALID_PARAMS</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">请求参数非法 ErrorCode：1200</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#AMAP_SERVICE_MAINTENANCE">AMAP_SERVICE_MAINTENANCE</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">服务器维护中 ErrorCode：2002</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#AMAP_SERVICE_MISSING_REQUIRED_PARAMS">AMAP_SERVICE_MISSING_REQUIRED_PARAMS</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">缺少必填参数 ErrorCode：1201</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#AMAP_SERVICE_NOT_AVAILBALE">AMAP_SERVICE_NOT_AVAILBALE</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">请求服务不存在 ErrorCode：1003</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#AMAP_SERVICE_TABLEID_NOT_EXIST">AMAP_SERVICE_TABLEID_NOT_EXIST</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">tableID格式不正确不存在 ErrorCode：2000</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#AMAP_SERVICE_UNKNOWN_ERROR">AMAP_SERVICE_UNKNOWN_ERROR</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">其他未知错误 ErrorCode：1203</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#AMAP_SHARE_FAILURE">AMAP_SHARE_FAILURE</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">短串请求失败 ErrorCode：4001</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#AMAP_SHARE_LICENSE_IS_EXPIRED">AMAP_SHARE_LICENSE_IS_EXPIRED</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">短串分享认证失败 ErrorCode：4000</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#AMAP_SHARE_SIGNATURE_FAILURE">AMAP_SHARE_SIGNATURE_FAILURE</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">短串分享用户签名未通过 ErrorCode：4002</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#AMAP_SIGNATURE_ERROR">AMAP_SIGNATURE_ERROR</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">用户签名未通过 ErrorCode：1001</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#AMAP_USER_KEY_RECYCLED">AMAP_USER_KEY_RECYCLED</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">开发者删除了key，key被删除后无法正常使用 ErrorCode：1013</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#AMAP_USERKEY_PLAT_NOMATCH">AMAP_USERKEY_PLAT_NOMATCH</a></span> - 异常错误 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">请求key与绑定平台不符 ErrorCode：1009</div>
</dd>
<dt><a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类"><span class="typeNameLink">AMapException</span></a> - <a href="../com/amap/api/services/core/package-summary.html">com.amap.api.services.core</a>中的异常错误</dt>
<dd>
<div class="block">服务异常信息类。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#AMapException-java.lang.String-">AMapException(String)</a></span> - 异常错误 的构造器com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">构造服务异常对象。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/AMapException.html#AMapException--">AMapException()</a></span> - 异常错误 的构造器com.amap.api.services.core.<a href="../com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></dt>
<dd>
<div class="block">构造服务异常对象。</div>
</dd>
<dt><a href="../com/amap/api/services/geocoder/AoiItem.html" title="com.amap.api.services.geocoder中的类"><span class="typeNameLink">AoiItem</span></a> - <a href="../com/amap/api/services/geocoder/package-summary.html">com.amap.api.services.geocoder</a>中的类</dt>
<dd>
<div class="block">定义了逆地理编码返回的一个面状数据信息。</div>
</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">K</a>&nbsp;<a href="index-11.html">L</a>&nbsp;<a href="index-12.html">M</a>&nbsp;<a href="index-13.html">N</a>&nbsp;<a href="index-14.html">O</a>&nbsp;<a href="index-15.html">P</a>&nbsp;<a href="index-16.html">Q</a>&nbsp;<a href="index-17.html">R</a>&nbsp;<a href="index-18.html">S</a>&nbsp;<a href="index-19.html">T</a>&nbsp;<a href="index-20.html">U</a>&nbsp;<a href="index-21.html">V</a>&nbsp;<a href="index-22.html">W</a>&nbsp;</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个字母</li>
<li><a href="index-2.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-1.html" target="_top">框架</a></li>
<li><a href="index-1.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
