<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>E - 索引</title>
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="E - \u7D22\u5F15";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-4.html">上一个字母</a></li>
<li><a href="index-6.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-5.html" target="_top">框架</a></li>
<li><a href="index-5.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="contentContainer"><a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">K</a>&nbsp;<a href="index-11.html">L</a>&nbsp;<a href="index-12.html">M</a>&nbsp;<a href="index-13.html">N</a>&nbsp;<a href="index-14.html">O</a>&nbsp;<a href="index-15.html">P</a>&nbsp;<a href="index-16.html">Q</a>&nbsp;<a href="index-17.html">R</a>&nbsp;<a href="index-18.html">S</a>&nbsp;<a href="index-19.html">T</a>&nbsp;<a href="index-20.html">U</a>&nbsp;<a href="index-21.html">V</a>&nbsp;<a href="index-22.html">W</a>&nbsp;<a name="I:E">
<!--   -->
</a>
<h2 class="title">E</h2>
<dl>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearch.SearchBound.html#ELLIPSE_SHAPE">ELLIPSE_SHAPE</a></span> - 类 中的静态变量com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearch.SearchBound.html" title="com.amap.api.services.poisearch中的类">PoiSearch.SearchBound</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">椭圆区域</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearchV2.SearchBound.html#ELLIPSE_SHAPE">ELLIPSE_SHAPE</a></span> - 类 中的静态变量com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearchV2.SearchBound.html" title="com.amap.api.services.poisearch中的类">PoiSearchV2.SearchBound</a></dt>
<dd>
<div class="block">椭圆区域</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/ServiceSettings.html#ENGLISH">ENGLISH</a></span> - 类 中的静态变量com.amap.api.services.core.<a href="../com/amap/api/services/core/ServiceSettings.html" title="com.amap.api.services.core中的类">ServiceSettings</a></dt>
<dd>
<div class="block">英文</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearch.html#ENGLISH">ENGLISH</a></span> - 类 中的静态变量com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearch.html" title="com.amap.api.services.poisearch中的类">PoiSearch</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">语言设置常量。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearchV2.html#ENGLISH">ENGLISH</a></span> - 类 中的静态变量com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearchV2.html" title="com.amap.api.services.poisearch中的类">PoiSearchV2</a></dt>
<dd>
<div class="block">语言设置常量。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/busline/BusLineItem.html#equals-java.lang.Object-">equals(Object)</a></span> - 类 中的方法com.amap.api.services.busline.<a href="../com/amap/api/services/busline/BusLineItem.html" title="com.amap.api.services.busline中的类">BusLineItem</a></dt>
<dd>
<div class="block">比较两个公交线路ID是否相同。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/busline/BusLineQuery.html#equals-java.lang.Object-">equals(Object)</a></span> - 类 中的方法com.amap.api.services.busline.<a href="../com/amap/api/services/busline/BusLineQuery.html" title="com.amap.api.services.busline中的类">BusLineQuery</a></dt>
<dd>
<div class="block">比较两个查询条件是否相同。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/busline/BusStationItem.html#equals-java.lang.Object-">equals(Object)</a></span> - 类 中的方法com.amap.api.services.busline.<a href="../com/amap/api/services/busline/BusStationItem.html" title="com.amap.api.services.busline中的类">BusStationItem</a></dt>
<dd>
<div class="block">比较两个公交站点ID是否相同。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/busline/BusStationQuery.html#equals-java.lang.Object-">equals(Object)</a></span> - 类 中的方法com.amap.api.services.busline.<a href="../com/amap/api/services/busline/BusStationQuery.html" title="com.amap.api.services.busline中的类">BusStationQuery</a></dt>
<dd>
<div class="block">比较两个查询条件是否相同。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/cloud/CloudSearch.Query.html#equals-java.lang.Object-">equals(Object)</a></span> - 类 中的方法com.amap.api.services.cloud.<a href="../com/amap/api/services/cloud/CloudSearch.Query.html" title="com.amap.api.services.cloud中的类">CloudSearch.Query</a></dt>
<dd>
<div class="block">比较两个查询条件是否相同(包括查询第几页)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/cloud/CloudSearch.SearchBound.html#equals-java.lang.Object-">equals(Object)</a></span> - 类 中的方法com.amap.api.services.cloud.<a href="../com/amap/api/services/cloud/CloudSearch.SearchBound.html" title="com.amap.api.services.cloud中的类">CloudSearch.SearchBound</a></dt>
<dd>
<div class="block">比较两个企业地图查询范围是否相同</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/cloud/CloudSearch.Sortingrules.html#equals-java.lang.Object-">equals(Object)</a></span> - 类 中的方法com.amap.api.services.cloud.<a href="../com/amap/api/services/cloud/CloudSearch.Sortingrules.html" title="com.amap.api.services.cloud中的类">CloudSearch.Sortingrules</a></dt>
<dd>
<div class="block">比较两个排序规则是否相同</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/LatLonPoint.html#equals-java.lang.Object-">equals(Object)</a></span> - 类 中的方法com.amap.api.services.core.<a href="../com/amap/api/services/core/LatLonPoint.html" title="com.amap.api.services.core中的类">LatLonPoint</a></dt>
<dd>
<div class="block">判断经纬度是否和原对象相等。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/LatLonSharePoint.html#equals-java.lang.Object-">equals(Object)</a></span> - 类 中的方法com.amap.api.services.core.<a href="../com/amap/api/services/core/LatLonSharePoint.html" title="com.amap.api.services.core中的类">LatLonSharePoint</a></dt>
<dd>
<div class="block">比较两个分享位置是否相同</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/core/PoiItem.html#equals-java.lang.Object-">equals(Object)</a></span> - 类 中的方法com.amap.api.services.core.<a href="../com/amap/api/services/core/PoiItem.html" title="com.amap.api.services.core中的类">PoiItem</a></dt>
<dd>
<div class="block">比较两个POI 的id 是否相等。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/district/DistrictItem.html#equals-java.lang.Object-">equals(Object)</a></span> - 类 中的方法com.amap.api.services.district.<a href="../com/amap/api/services/district/DistrictItem.html" title="com.amap.api.services.district中的类">DistrictItem</a></dt>
<dd>
<div class="block">比较两个行政区划是否相同</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/district/DistrictResult.html#equals-java.lang.Object-">equals(Object)</a></span> - 类 中的方法com.amap.api.services.district.<a href="../com/amap/api/services/district/DistrictResult.html" title="com.amap.api.services.district中的类">DistrictResult</a></dt>
<dd>
<div class="block">比较两个行政区划的查询结果是否相同</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/district/DistrictSearchQuery.html#equals-java.lang.Object-">equals(Object)</a></span> - 类 中的方法com.amap.api.services.district.<a href="../com/amap/api/services/district/DistrictSearchQuery.html" title="com.amap.api.services.district中的类">DistrictSearchQuery</a></dt>
<dd>
<div class="block">比较两个查询条件是否相同。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/GeocodeQuery.html#equals-java.lang.Object-">equals(Object)</a></span> - 类 中的方法com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/GeocodeQuery.html" title="com.amap.api.services.geocoder中的类">GeocodeQuery</a></dt>
<dd>
<div class="block">比较两个查询条件是否相同。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/RegeocodeQuery.html#equals-java.lang.Object-">equals(Object)</a></span> - 类 中的方法com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/RegeocodeQuery.html" title="com.amap.api.services.geocoder中的类">RegeocodeQuery</a></dt>
<dd>
<div class="block">比较两个查询条件是否相同。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearch.Query.html#equals-java.lang.Object-">equals(Object)</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearch.Query.html" title="com.amap.api.services.poisearch中的类">PoiSearch.Query</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">比较两个查询条件是否相同（包括查询第几页）。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearchV2.Query.html#equals-java.lang.Object-">equals(Object)</a></span> - 类 中的方法com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearchV2.Query.html" title="com.amap.api.services.poisearch中的类">PoiSearchV2.Query</a></dt>
<dd>
<div class="block">比较两个查询条件是否相同（包括查询第几页）。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteBusLineItem.html#equals-java.lang.Object-">equals(Object)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteBusLineItem.html" title="com.amap.api.services.route中的类">RouteBusLineItem</a></dt>
<dd>
<div class="block">比较两个公交换乘规划是否相同。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.BusRouteQuery.html#equals-java.lang.Object-">equals(Object)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.BusRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearch.BusRouteQuery</a></dt>
<dd>
<div class="block">比较两个查询条件是否相同。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.DrivePlanQuery.html#equals-java.lang.Object-">equals(Object)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.DrivePlanQuery.html" title="com.amap.api.services.route中的类">RouteSearch.DrivePlanQuery</a></dt>
<dd>
<div class="block">比较两个查询条件是否相同。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.DriveRouteQuery.html#equals-java.lang.Object-">equals(Object)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.DriveRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearch.DriveRouteQuery</a></dt>
<dd>
<div class="block">比较两个查询条件是否相同。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.FromAndTo.html#equals-java.lang.Object-">equals(Object)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.FromAndTo.html" title="com.amap.api.services.route中的类">RouteSearch.FromAndTo</a></dt>
<dd>
<div class="block">比较路径规划的起终点是否相同。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.RideRouteQuery.html#equals-java.lang.Object-">equals(Object)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.RideRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearch.RideRouteQuery</a></dt>
<dd>
<div class="block">比较两个查询条件是否相同。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.WalkRouteQuery.html#equals-java.lang.Object-">equals(Object)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.WalkRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearch.WalkRouteQuery</a></dt>
<dd>
<div class="block">比较两个查询条件是否相同。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.BusRouteQuery.html#equals-java.lang.Object-">equals(Object)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.BusRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearchV2.BusRouteQuery</a></dt>
<dd>
<div class="block">比较两个查询条件是否相同。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.DriveRouteQuery.html#equals-java.lang.Object-">equals(Object)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.DriveRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearchV2.DriveRouteQuery</a></dt>
<dd>
<div class="block">比较两个查询条件是否相同。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.FromAndTo.html#equals-java.lang.Object-">equals(Object)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.FromAndTo.html" title="com.amap.api.services.route中的类">RouteSearchV2.FromAndTo</a></dt>
<dd>
<div class="block">比较路径规划的起终点是否相同。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.RideRouteQuery.html#equals-java.lang.Object-">equals(Object)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.RideRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearchV2.RideRouteQuery</a></dt>
<dd>
<div class="block">比较两个查询条件是否相同。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearchV2.WalkRouteQuery.html#equals-java.lang.Object-">equals(Object)</a></span> - 类 中的方法com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearchV2.WalkRouteQuery.html" title="com.amap.api.services.route中的类">RouteSearchV2.WalkRouteQuery</a></dt>
<dd>
<div class="block">比较两个查询条件是否相同。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/DistanceItem.html#ERROR_CODE_NO_DRIVE">ERROR_CODE_NO_DRIVE</a></span> - 类 中的变量com.amap.api.services.route.<a href="../com/amap/api/services/route/DistanceItem.html" title="com.amap.api.services.route中的类">DistanceItem</a></dt>
<dd>
<div class="block">指定地点之间没有可以行车的道路</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/DistanceItem.html#ERROR_CODE_NOT_IN_CHINA">ERROR_CODE_NOT_IN_CHINA</a></span> - 类 中的变量com.amap.api.services.route.<a href="../com/amap/api/services/route/DistanceItem.html" title="com.amap.api.services.route中的类">DistanceItem</a></dt>
<dd>
<div class="block">起点/终点不在中国境内</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/DistanceItem.html#ERROR_CODE_TOO_FAR">ERROR_CODE_TOO_FAR</a></span> - 类 中的变量com.amap.api.services.route.<a href="../com/amap/api/services/route/DistanceItem.html" title="com.amap.api.services.route中的类">DistanceItem</a></dt>
<dd>
<div class="block">起点/终点 距离所有道路均距离过远（例如在海洋/矿业）</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/busline/BusLineSearch.html#EXTENSIONS_ALL">EXTENSIONS_ALL</a></span> - 类 中的静态变量com.amap.api.services.busline.<a href="../com/amap/api/services/busline/BusLineSearch.html" title="com.amap.api.services.busline中的类">BusLineSearch</a></dt>
<dd>
<div class="block">扩展字段all，会返回完整参数</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/GeocodeSearch.html#EXTENSIONS_ALL">EXTENSIONS_ALL</a></span> - 类 中的静态变量com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/GeocodeSearch.html" title="com.amap.api.services.geocoder中的类">GeocodeSearch</a></dt>
<dd>
<div class="block">扩展字段all，会返回完整参数</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearch.html#EXTENSIONS_ALL">EXTENSIONS_ALL</a></span> - 类 中的静态变量com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearch.html" title="com.amap.api.services.poisearch中的类">PoiSearch</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">扩展字段all，会返回完整参数</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/DistanceSearch.html#EXTENSIONS_ALL">EXTENSIONS_ALL</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/DistanceSearch.html" title="com.amap.api.services.route中的类">DistanceSearch</a></dt>
<dd>
<div class="block">扩展字段all，会返回完整参数</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#EXTENSIONS_ALL">EXTENSIONS_ALL</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block">扩展字段all，会返回完整参数</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/busline/BusLineSearch.html#EXTENSIONS_BASE">EXTENSIONS_BASE</a></span> - 类 中的静态变量com.amap.api.services.busline.<a href="../com/amap/api/services/busline/BusLineSearch.html" title="com.amap.api.services.busline中的类">BusLineSearch</a></dt>
<dd>
<div class="block">扩展字段base，会返回部分参数</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/geocoder/GeocodeSearch.html#EXTENSIONS_BASE">EXTENSIONS_BASE</a></span> - 类 中的静态变量com.amap.api.services.geocoder.<a href="../com/amap/api/services/geocoder/GeocodeSearch.html" title="com.amap.api.services.geocoder中的类">GeocodeSearch</a></dt>
<dd>
<div class="block">扩展字段base，会返回部分参数</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/poisearch/PoiSearch.html#EXTENSIONS_BASE">EXTENSIONS_BASE</a></span> - 类 中的静态变量com.amap.api.services.poisearch.<a href="../com/amap/api/services/poisearch/PoiSearch.html" title="com.amap.api.services.poisearch中的类">PoiSearch</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">扩展字段base，会返回部分参数</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/DistanceSearch.html#EXTENSIONS_BASE">EXTENSIONS_BASE</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/DistanceSearch.html" title="com.amap.api.services.route中的类">DistanceSearch</a></dt>
<dd>
<div class="block">扩展字段base，会返回部分参数</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/services/route/RouteSearch.html#EXTENSIONS_BASE">EXTENSIONS_BASE</a></span> - 类 中的静态变量com.amap.api.services.route.<a href="../com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></dt>
<dd>
<div class="block">扩展字段base，会返回部分参数</div>
</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">K</a>&nbsp;<a href="index-11.html">L</a>&nbsp;<a href="index-12.html">M</a>&nbsp;<a href="index-13.html">N</a>&nbsp;<a href="index-14.html">O</a>&nbsp;<a href="index-15.html">P</a>&nbsp;<a href="index-16.html">Q</a>&nbsp;<a href="index-17.html">R</a>&nbsp;<a href="index-18.html">S</a>&nbsp;<a href="index-19.html">T</a>&nbsp;<a href="index-20.html">U</a>&nbsp;<a href="index-21.html">V</a>&nbsp;<a href="index-22.html">W</a>&nbsp;</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-4.html">上一个字母</a></li>
<li><a href="index-6.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-5.html" target="_top">框架</a></li>
<li><a href="index-5.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
