<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>已过时的列表</title>
<link rel="stylesheet" type="text/css" href="stylesheet.css" title="Style">
<script type="text/javascript" src="script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="\u5DF2\u8FC7\u65F6\u7684\u5217\u8868";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="overview-tree.html">树</a></li>
<li class="navBarCell1Rev">已过时</li>
<li><a href="index-files/index-1.html">索引</a></li>
<li><a href="help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个</li>
<li>下一个</li>
</ul>
<ul class="navList">
<li><a href="index.html?deprecated-list.html" target="_top">框架</a></li>
<li><a href="deprecated-list.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 title="已过时的 API" class="title">已过时的 API</h1>
<h2 title="目录">目录</h2>
<ul>
<li><a href="#interface">已过时的接口</a></li>
<li><a href="#class">已过时的类</a></li>
<li><a href="#enum">已过时的枚举</a></li>
<li><a href="#field">已过时的字段</a></li>
<li><a href="#method">已过时的方法</a></li>
<li><a href="#constructor">已过时的构造器</a></li>
</ul>
</div>
<div class="contentContainer"><a name="interface">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<table class="deprecatedSummary" border="0" cellpadding="3" cellspacing="0" summary="已过时的接口表, 列表已过时的接口和解释">
<caption><span>已过时的接口</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">接口和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="com/amap/api/services/nearby/UploadInfoCallback.html" title="com.amap.api.services.nearby中的接口">com.amap.api.services.nearby.UploadInfoCallback</a>
<div class="block"><span class="deprecationComment">自7.4.0起不再支持附件功能</span></div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
<a name="class">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<table class="deprecatedSummary" border="0" cellpadding="3" cellspacing="0" summary="已过时的类表, 列表已过时的类和解释">
<caption><span>已过时的类</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">类和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="com/amap/api/services/cloud/CloudImage.html" title="com.amap.api.services.cloud中的类">com.amap.api.services.cloud.CloudImage</a>
<div class="block"><span class="deprecationComment">自7.4.0起不再支持云图图片</span></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="com/amap/api/services/nearby/NearbyInfo.html" title="com.amap.api.services.nearby中的类">com.amap.api.services.nearby.NearbyInfo</a>
<div class="block"><span class="deprecationComment">自7.4.0起不再支持附件功能</span></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="com/amap/api/services/nearby/NearbySearch.html" title="com.amap.api.services.nearby中的类">com.amap.api.services.nearby.NearbySearch</a>
<div class="block"><span class="deprecationComment">自7.4.0起不再支持附件功能</span></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="com/amap/api/services/nearby/NearbySearchResult.html" title="com.amap.api.services.nearby中的类">com.amap.api.services.nearby.NearbySearchResult</a>
<div class="block"><span class="deprecationComment">自7.4.0起不再支持附件功能</span></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="com/amap/api/services/poisearch/PoiSearch.html" title="com.amap.api.services.poisearch中的类">com.amap.api.services.poisearch.PoiSearch</a>
<div class="block"><span class="deprecationComment">自v9.4.0废弃,推荐使用 <a href="com/amap/api/services/poisearch/PoiSearchV2.html" title="com.amap.api.services.poisearch中的类"><code>PoiSearchV2</code></a></span></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="com/amap/api/services/nearby/UploadInfo.html" title="com.amap.api.services.nearby中的类">com.amap.api.services.nearby.UploadInfo</a>
<div class="block"><span class="deprecationComment">自7.4.0起不再支持附件功能</span></div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
<a name="enum">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<table class="deprecatedSummary" border="0" cellpadding="3" cellspacing="0" summary="已过时的枚举表, 列表已过时的枚举和解释">
<caption><span>已过时的枚举</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">枚举和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="com/amap/api/services/nearby/NearbySearchFunctionType.html" title="com.amap.api.services.nearby中的枚举">com.amap.api.services.nearby.NearbySearchFunctionType</a>
<div class="block"><span class="deprecationComment">自7.4.0起不再支持附件功能</span></div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
<a name="field">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<table class="deprecatedSummary" border="0" cellpadding="3" cellspacing="0" summary="已过时的字段表, 列表已过时的字段和解释">
<caption><span>已过时的字段</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">字段和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="com/amap/api/services/route/RouteSearch.html#BusComfortable">com.amap.api.services.route.RouteSearch.BusComfortable</a>
<div class="block"><span class="deprecationComment">自V4.0.0以后，请参考 <a href="com/amap/api/services/route/RouteSearch.html#BUS_COMFORTABLE"><code>RouteSearch.BUS_COMFORTABLE</code></a></span></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="com/amap/api/services/route/RouteSearch.html#BusDefault">com.amap.api.services.route.RouteSearch.BusDefault</a>
<div class="block"><span class="deprecationComment">自V4.0.0以后，请参考 <a href="com/amap/api/services/route/RouteSearch.html#BUS_DEFAULT"><code>RouteSearch.BUS_DEFAULT</code></a></span></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="com/amap/api/services/route/RouteSearch.html#BusLeaseChange">com.amap.api.services.route.RouteSearch.BusLeaseChange</a>
<div class="block"><span class="deprecationComment">自V4.0.0以后，请参考 <a href="com/amap/api/services/route/RouteSearch.html#BUS_LEASE_CHANGE"><code>RouteSearch.BUS_LEASE_CHANGE</code></a></span></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="com/amap/api/services/route/RouteSearch.html#BusLeaseWalk">com.amap.api.services.route.RouteSearch.BusLeaseWalk</a>
<div class="block"><span class="deprecationComment">自V4.0.0以后，请参考 <a href="com/amap/api/services/route/RouteSearch.html#BUS_LEASE_WALK"><code>RouteSearch.BUS_LEASE_WALK</code></a></span></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="com/amap/api/services/route/RouteSearch.html#BusNoSubway">com.amap.api.services.route.RouteSearch.BusNoSubway</a>
<div class="block"><span class="deprecationComment">自V4.0.0以后，请参考 <a href="com/amap/api/services/route/RouteSearch.html#BUS_NO_SUBWAY"><code>RouteSearch.BUS_NO_SUBWAY</code></a></span></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="com/amap/api/services/route/RouteSearch.html#BusSaveMoney">com.amap.api.services.route.RouteSearch.BusSaveMoney</a>
<div class="block"><span class="deprecationComment">自V4.0.0以后，请参考 <a href="com/amap/api/services/route/RouteSearch.html#BUS_SAVE_MONEY"><code>RouteSearch.BUS_SAVE_MONEY</code></a></span></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="com/amap/api/services/route/RouteSearch.html#DrivingAvoidCongestion">com.amap.api.services.route.RouteSearch.DrivingAvoidCongestion</a>
<div class="block"><span class="deprecationComment">自V4.0.0以后，请参考 <a href="com/amap/api/services/route/RouteSearch.html#DRIVING_SINGLE_AVOID_CONGESTION"><code>RouteSearch.DRIVING_SINGLE_AVOID_CONGESTION</code></a></span></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="com/amap/api/services/route/RouteSearch.html#DrivingDefault">com.amap.api.services.route.RouteSearch.DrivingDefault</a>
<div class="block"><span class="deprecationComment">自V4.0.0以后，请参考 <a href="com/amap/api/services/route/RouteSearch.html#DRIVING_SINGLE_DEFAULT"><code>RouteSearch.DRIVING_SINGLE_DEFAULT</code></a></span></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="com/amap/api/services/route/RouteSearch.html#DrivingMultiStrategy">com.amap.api.services.route.RouteSearch.DrivingMultiStrategy</a>
<div class="block"><span class="deprecationComment">自V4.0.0以后，请参考 <a href="com/amap/api/services/route/RouteSearch.html#DRIVING_MULTI_STRATEGY_FASTEST_SAVE_MONEY_SHORTEST"><code>RouteSearch.DRIVING_MULTI_STRATEGY_FASTEST_SAVE_MONEY_SHORTEST</code></a></span></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="com/amap/api/services/route/RouteSearch.html#DrivingNoExpressways">com.amap.api.services.route.RouteSearch.DrivingNoExpressways</a>
<div class="block"><span class="deprecationComment">自V4.0.0以后，请参考 <a href="com/amap/api/services/route/RouteSearch.html#DRIVING_SINGLE_NO_EXPRESSWAYS"><code>RouteSearch.DRIVING_SINGLE_NO_EXPRESSWAYS</code></a></span></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="com/amap/api/services/route/RouteSearch.html#DrivingNoHighAvoidCongestionSaveMoney">com.amap.api.services.route.RouteSearch.DrivingNoHighAvoidCongestionSaveMoney</a>
<div class="block"><span class="deprecationComment">自V4.0.0以后，请参考 <a href="com/amap/api/services/route/RouteSearch.html#DRIVING_SINGLE_NO_HIGHWAY_SAVE_MONEY_AVOID_CONGESTION"><code>RouteSearch.DRIVING_SINGLE_NO_HIGHWAY_SAVE_MONEY_AVOID_CONGESTION</code></a></span></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="com/amap/api/services/route/RouteSearch.html#DrivingNoHighWay">com.amap.api.services.route.RouteSearch.DrivingNoHighWay</a>
<div class="block"><span class="deprecationComment">自V4.0.0以后，请参考 <a href="com/amap/api/services/route/RouteSearch.html#DRIVING_SINGLE_NO_HIGHWAY"><code>RouteSearch.DRIVING_SINGLE_NO_HIGHWAY</code></a></span></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="com/amap/api/services/route/RouteSearch.html#DrivingNoHighWaySaveMoney">com.amap.api.services.route.RouteSearch.DrivingNoHighWaySaveMoney</a>
<div class="block"><span class="deprecationComment">自V4.0.0以后，请参考 <a href="com/amap/api/services/route/RouteSearch.html#DRIVING_SINGLE_NO_HIGHWAY_SAVE_MONEY"><code>RouteSearch.DRIVING_SINGLE_NO_HIGHWAY_SAVE_MONEY</code></a></span></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="com/amap/api/services/route/RouteSearch.html#DrivingSaveMoney">com.amap.api.services.route.RouteSearch.DrivingSaveMoney</a>
<div class="block"><span class="deprecationComment">自V4.0.0以后，请参考 <a href="com/amap/api/services/route/RouteSearch.html#DRIVING_SINGLE_SAVE_MONEY"><code>RouteSearch.DRIVING_SINGLE_SAVE_MONEY</code></a></span></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="com/amap/api/services/route/RouteSearch.html#DrivingSaveMoneyAvoidCongestion">com.amap.api.services.route.RouteSearch.DrivingSaveMoneyAvoidCongestion</a>
<div class="block"><span class="deprecationComment">自V4.0.0以后，请参考 <a href="com/amap/api/services/route/RouteSearch.html#DRIVING_SINGLE_SAVE_MONEY_AVOID_CONGESTION"><code>RouteSearch.DRIVING_SINGLE_SAVE_MONEY_AVOID_CONGESTION</code></a></span></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="com/amap/api/services/route/RouteSearch.html#DrivingShortDistance">com.amap.api.services.route.RouteSearch.DrivingShortDistance</a>
<div class="block"><span class="deprecationComment">自V4.0.0以后，请参考 <a href="com/amap/api/services/route/RouteSearch.html#DRIVING_SINGLE_SHORTEST"><code>RouteSearch.DRIVING_SINGLE_SHORTEST</code></a></span></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="com/amap/api/services/district/DistrictSearchQuery.html#KEYWORDS_BUSINESS">com.amap.api.services.district.DistrictSearchQuery.KEYWORDS_BUSINESS</a>
<div class="block"><span class="deprecationComment">3.6.1 不再需要此参数限制</span></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="com/amap/api/services/district/DistrictSearchQuery.html#KEYWORDS_CITY">com.amap.api.services.district.DistrictSearchQuery.KEYWORDS_CITY</a>
<div class="block"><span class="deprecationComment">3.6.1 不再需要此参数限制</span></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="com/amap/api/services/district/DistrictSearchQuery.html#KEYWORDS_COUNTRY">com.amap.api.services.district.DistrictSearchQuery.KEYWORDS_COUNTRY</a>
<div class="block"><span class="deprecationComment">3.6.1 不再需要此参数限制</span></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="com/amap/api/services/district/DistrictSearchQuery.html#KEYWORDS_DISTRICT">com.amap.api.services.district.DistrictSearchQuery.KEYWORDS_DISTRICT</a>
<div class="block"><span class="deprecationComment">3.6.1 不再需要此参数限制</span></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="com/amap/api/services/district/DistrictSearchQuery.html#KEYWORDS_PROVINCE">com.amap.api.services.district.DistrictSearchQuery.KEYWORDS_PROVINCE</a>
<div class="block"><span class="deprecationComment">3.6.1 不再需要此参数限制</span></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="com/amap/api/services/route/RouteSearch.html#RIDING_DEFAULT">com.amap.api.services.route.RouteSearch.RIDING_DEFAULT</a>
<div class="block"><span class="deprecationComment">骑行不再提供模式相关设置</span></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="com/amap/api/services/route/RouteSearch.html#RIDING_FAST">com.amap.api.services.route.RouteSearch.RIDING_FAST</a>
<div class="block"><span class="deprecationComment">骑行不再提供模式相关设置</span></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="com/amap/api/services/route/RouteSearch.html#RIDING_RECOMMEND">com.amap.api.services.route.RouteSearch.RIDING_RECOMMEND</a>
<div class="block"><span class="deprecationComment">骑行不再提供骑行相关设置</span></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="com/amap/api/services/route/RouteSearch.html#RidingDefault">com.amap.api.services.route.RouteSearch.RidingDefault</a>
<div class="block"><span class="deprecationComment">自V4.0.0以后，请参考 <a href="com/amap/api/services/route/RouteSearch.html#RIDING_DEFAULT"><code>RouteSearch.RIDING_DEFAULT</code></a></span></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="com/amap/api/services/route/RouteSearch.html#RidingFast">com.amap.api.services.route.RouteSearch.RidingFast</a>
<div class="block"><span class="deprecationComment">自V4.0.0以后，请参考 <a href="com/amap/api/services/route/RouteSearch.html#RIDING_FAST"><code>RouteSearch.RIDING_FAST</code></a></span></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="com/amap/api/services/route/RouteSearch.html#RidingRecommend">com.amap.api.services.route.RouteSearch.RidingRecommend</a>
<div class="block"><span class="deprecationComment">自V4.0.0以后，请参考 <a href="com/amap/api/services/route/RouteSearch.html#RIDING_RECOMMEND"><code>RouteSearch.RIDING_RECOMMEND</code></a></span></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="com/amap/api/services/route/RouteSearch.html#WALK_DEFAULT">com.amap.api.services.route.RouteSearch.WALK_DEFAULT</a>
<div class="block"><span class="deprecationComment">步行不再提供模式相关设置</span></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="com/amap/api/services/route/RouteSearch.html#WALK_MULTI_PATH">com.amap.api.services.route.RouteSearch.WALK_MULTI_PATH</a>
<div class="block"><span class="deprecationComment">步行不再提供模式相关设置</span></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="com/amap/api/services/route/RouteSearch.html#WalkDefault">com.amap.api.services.route.RouteSearch.WalkDefault</a>
<div class="block"><span class="deprecationComment">自V4.0.0以后，请参考 <a href="com/amap/api/services/route/RouteSearch.html#WALK_DEFAULT"><code>RouteSearch.WALK_DEFAULT</code></a></span></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="com/amap/api/services/route/RouteSearch.html#WalkMultipath">com.amap.api.services.route.RouteSearch.WalkMultipath</a>
<div class="block"><span class="deprecationComment">自V4.0.0以后，请参考 <a href="com/amap/api/services/route/RouteSearch.html#WALK_MULTI_PATH"><code>RouteSearch.WALK_MULTI_PATH</code></a></span></div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
<a name="method">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<table class="deprecatedSummary" border="0" cellpadding="3" cellspacing="0" summary="已过时的方法表, 列表已过时的方法和解释">
<caption><span>已过时的方法</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">方法和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="com/amap/api/services/cloud/CloudSearch.Query.html#addFilterNum-java.lang.String-java.lang.String-java.lang.String-">com.amap.api.services.cloud.CloudSearch.Query.addFilterNum(String, String, String)</a>
<div class="block"><span class="deprecationComment">使用 <a href="com/amap/api/services/cloud/CloudSearch.Query.html#addFilterString-java.lang.String-java.lang.String-"><code>CloudSearch.Query.addFilterString(String, String)</code></a> 替换</span></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="com/amap/api/services/route/RouteSearch.html#calculateBusRoute-com.amap.api.services.route.RouteSearch.BusRouteQuery-">com.amap.api.services.route.RouteSearch.calculateBusRoute(RouteSearch.BusRouteQuery)</a>
<div class="block"><span class="deprecationComment">自9.4.0废弃 <a href="com/amap/api/services/route/RouteSearchV2.html#calculateBusRoute-com.amap.api.services.route.RouteSearchV2.BusRouteQuery-"><code>RouteSearchV2.calculateBusRoute(com.amap.api.services.route.RouteSearchV2.BusRouteQuery)</code></a></span></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="com/amap/api/services/route/RouteSearch.html#calculateBusRouteAsyn-com.amap.api.services.route.RouteSearch.BusRouteQuery-">com.amap.api.services.route.RouteSearch.calculateBusRouteAsyn(RouteSearch.BusRouteQuery)</a>
<div class="block"><span class="deprecationComment">自9.4.0废弃 <a href="com/amap/api/services/route/RouteSearchV2.html#calculateBusRouteAsyn-com.amap.api.services.route.RouteSearchV2.BusRouteQuery-"><code>RouteSearchV2.calculateBusRouteAsyn(com.amap.api.services.route.RouteSearchV2.BusRouteQuery)</code></a></span></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="com/amap/api/services/route/RouteSearch.html#calculateDriveRoute-com.amap.api.services.route.RouteSearch.DriveRouteQuery-">com.amap.api.services.route.RouteSearch.calculateDriveRoute(RouteSearch.DriveRouteQuery)</a>
<div class="block"><span class="deprecationComment">自9.4.0废弃 <a href="com/amap/api/services/route/RouteSearchV2.html#calculateDriveRoute-com.amap.api.services.route.RouteSearchV2.DriveRouteQuery-"><code>RouteSearchV2.calculateDriveRoute(com.amap.api.services.route.RouteSearchV2.DriveRouteQuery)</code></a></span></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="com/amap/api/services/route/RouteSearch.html#calculateDriveRouteAsyn-com.amap.api.services.route.RouteSearch.DriveRouteQuery-">com.amap.api.services.route.RouteSearch.calculateDriveRouteAsyn(RouteSearch.DriveRouteQuery)</a>
<div class="block"><span class="deprecationComment">自9.4.0废弃 <a href="com/amap/api/services/route/RouteSearchV2.html#calculateDriveRouteAsyn-com.amap.api.services.route.RouteSearchV2.DriveRouteQuery-"><code>RouteSearchV2.calculateDriveRouteAsyn(com.amap.api.services.route.RouteSearchV2.DriveRouteQuery)</code></a></span></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="com/amap/api/services/route/RouteSearch.html#calculateRideRoute-com.amap.api.services.route.RouteSearch.RideRouteQuery-">com.amap.api.services.route.RouteSearch.calculateRideRoute(RouteSearch.RideRouteQuery)</a>
<div class="block"><span class="deprecationComment">自9.4.0废弃 <a href="com/amap/api/services/route/RouteSearchV2.html#calculateRideRoute-com.amap.api.services.route.RouteSearchV2.RideRouteQuery-"><code>RouteSearchV2.calculateRideRoute(com.amap.api.services.route.RouteSearchV2.RideRouteQuery)</code></a></span></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="com/amap/api/services/route/RouteSearch.html#calculateRideRouteAsyn-com.amap.api.services.route.RouteSearch.RideRouteQuery-">com.amap.api.services.route.RouteSearch.calculateRideRouteAsyn(RouteSearch.RideRouteQuery)</a>
<div class="block"><span class="deprecationComment">自9.4.0废弃 <a href="com/amap/api/services/route/RouteSearchV2.html#calculateRideRouteAsyn-com.amap.api.services.route.RouteSearchV2.RideRouteQuery-"><code>RouteSearchV2.calculateRideRouteAsyn(com.amap.api.services.route.RouteSearchV2.RideRouteQuery)</code></a></span></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="com/amap/api/services/route/RouteSearch.html#calculateWalkRoute-com.amap.api.services.route.RouteSearch.WalkRouteQuery-">com.amap.api.services.route.RouteSearch.calculateWalkRoute(RouteSearch.WalkRouteQuery)</a>
<div class="block"><span class="deprecationComment">自9.4.0废弃 <a href="com/amap/api/services/route/RouteSearchV2.html#calculateWalkRoute-com.amap.api.services.route.RouteSearchV2.WalkRouteQuery-"><code>RouteSearchV2.calculateWalkRoute(com.amap.api.services.route.RouteSearchV2.WalkRouteQuery)</code></a></span></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="com/amap/api/services/route/RouteSearch.html#calculateWalkRouteAsyn-com.amap.api.services.route.RouteSearch.WalkRouteQuery-">com.amap.api.services.route.RouteSearch.calculateWalkRouteAsyn(RouteSearch.WalkRouteQuery)</a>
<div class="block"><span class="deprecationComment">自废弃9.4.0废弃 <a href="com/amap/api/services/route/RouteSearchV2.html#calculateWalkRouteAsyn-com.amap.api.services.route.RouteSearchV2.WalkRouteQuery-"><code>RouteSearchV2.calculateWalkRouteAsyn(com.amap.api.services.route.RouteSearchV2.WalkRouteQuery)</code></a></span></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="com/amap/api/services/district/DistrictSearchQuery.html#checkLevels--">com.amap.api.services.district.DistrictSearchQuery.checkLevels()</a>
<div class="block"><span class="deprecationComment">3.6.1 不再需要此参数限制</span></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="com/amap/api/services/route/BusStep.html#getBusLine--">com.amap.api.services.route.BusStep.getBusLine()</a>
<div class="block"><span class="deprecationComment">此接口废弃。</span></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="com/amap/api/services/route/BusStepV2.html#getBusLine--">com.amap.api.services.route.BusStepV2.getBusLine()</a>
<div class="block"><span class="deprecationComment">此接口废弃。</span></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="com/amap/api/services/cloud/CloudItem.html#getCloudImage--">com.amap.api.services.cloud.CloudItem.getCloudImage()</a>
<div class="block"><span class="deprecationComment">自7.4.0起不再支持企业地图图片</span></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="com/amap/api/services/cloud/CloudSearch.Query.html#getFilterNumString--">com.amap.api.services.cloud.CloudSearch.Query.getFilterNumString()</a>
<div class="block"><span class="deprecationComment">使用 <a href="com/amap/api/services/cloud/CloudSearch.Query.html#addFilterString-java.lang.String-java.lang.String-"><code>CloudSearch.Query.addFilterString(String, String)</code></a>替换</span></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="com/amap/api/services/district/DistrictSearchQuery.html#getKeywordsLevel--">com.amap.api.services.district.DistrictSearchQuery.getKeywordsLevel()</a>
<div class="block"><span class="deprecationComment">3.6.1 不再需要此参数限制</span></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="com/amap/api/services/poisearch/PoiSearch.html#getLanguage--">com.amap.api.services.poisearch.PoiSearch.getLanguage()</a>
<div class="block"><span class="deprecationComment">自V2.5.0后废弃，请参考 <a href="com/amap/api/services/core/ServiceSettings.html#getLanguage--"><code>ServiceSettings.getLanguage()</code></a></span></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="com/amap/api/services/route/RouteSearch.WalkRouteQuery.html#getMode--">com.amap.api.services.route.RouteSearch.WalkRouteQuery.getMode()</a>
<div class="block"><span class="deprecationComment">步行路径规划升级，废弃模式选择</span></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="com/amap/api/services/route/RouteSearch.RideRouteQuery.html#getMode--">com.amap.api.services.route.RouteSearch.RideRouteQuery.getMode()</a>
<div class="block"><span class="deprecationComment">骑行路径规划升级，废弃模式选择</span></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="com/amap/api/services/district/DistrictSearchQuery.html#isShowBusinessArea--">com.amap.api.services.district.DistrictSearchQuery.isShowBusinessArea()</a>
<div class="block"><span class="deprecationComment">自 5.1.0 废弃，用率极低，而且会影响精准度，取消商圈（biz_area）级别数据，用街道（street）顶替商圈位置</span></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="com/amap/api/services/district/DistrictSearchQuery.html#isShowChild--">com.amap.api.services.district.DistrictSearchQuery.isShowChild()</a>
<div class="block"><span class="deprecationComment">自 7.1.0 废弃，返回下级行政区划参考 <a href="com/amap/api/services/district/DistrictSearchQuery.html#getSubDistrict--"><code>DistrictSearchQuery.getSubDistrict()</code></a></span></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="com/amap/api/services/help/Inputtips.html#requestInputtips-java.lang.String-java.lang.String-">com.amap.api.services.help.Inputtips.requestInputtips(String, String)</a>
<div class="block"><span class="deprecationComment">请参考 <a href="com/amap/api/services/help/Inputtips.html#requestInputtips--"><code>Inputtips.requestInputtips()</code></a></span></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="com/amap/api/services/help/Inputtips.html#requestInputtips-java.lang.String-java.lang.String-java.lang.String-">com.amap.api.services.help.Inputtips.requestInputtips(String, String, String)</a>
<div class="block"><span class="deprecationComment">请参考 <a href="com/amap/api/services/help/Inputtips.html#requestInputtips--"><code>Inputtips.requestInputtips()</code></a></span></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="com/amap/api/services/district/DistrictSearch.html#searchDistrictAnsy--">com.amap.api.services.district.DistrictSearch.searchDistrictAnsy()</a>
<div class="block"><span class="deprecationComment">请参考 <a href="com/amap/api/services/district/DistrictSearch.html#searchDistrictAsyn--"><code>DistrictSearch.searchDistrictAsyn()</code></a></span></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="com/amap/api/services/district/DistrictSearchQuery.html#setKeywordsLevel-java.lang.String-">com.amap.api.services.district.DistrictSearchQuery.setKeywordsLevel(String)</a>
<div class="block"><span class="deprecationComment">3.6.1 不再需要此参数限制</span></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="com/amap/api/services/poisearch/PoiSearch.html#setLanguage-java.lang.String-">com.amap.api.services.poisearch.PoiSearch.setLanguage(String)</a>
<div class="block"><span class="deprecationComment">自V2.5.0后废弃，请参考 <a href="com/amap/api/services/core/ServiceSettings.html#setLanguage-java.lang.String-"><code>ServiceSettings.setLanguage(String)</code></a></span></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="com/amap/api/services/poisearch/PoiSearch.Query.html#setQueryLanguage-java.lang.String-">com.amap.api.services.poisearch.PoiSearch.Query.setQueryLanguage(String)</a>
<div class="block"><span class="deprecationComment">请参考 <a href="com/amap/api/services/core/ServiceSettings.html#setLanguage-java.lang.String-"><code>ServiceSettings.setLanguage(String)</code></a></span></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="com/amap/api/services/poisearch/PoiSearchV2.Query.html#setQueryLanguage-java.lang.String-">com.amap.api.services.poisearch.PoiSearchV2.Query.setQueryLanguage(String)</a>
<div class="block"><span class="deprecationComment">请参考 <a href="com/amap/api/services/core/ServiceSettings.html#setLanguage-java.lang.String-"><code>ServiceSettings.setLanguage(String)</code></a></span></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="com/amap/api/services/district/DistrictSearchQuery.html#setShowBusinessArea-boolean-">com.amap.api.services.district.DistrictSearchQuery.setShowBusinessArea(boolean)</a>
<div class="block"><span class="deprecationComment">自 5.1.0 废弃，使用率极低，而且会影响精准度，取消商圈（biz_area）级别数据，用街道（street）顶替商圈位置</span></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="com/amap/api/services/district/DistrictSearchQuery.html#setShowChild-boolean-">com.amap.api.services.district.DistrictSearchQuery.setShowChild(boolean)</a>
<div class="block"><span class="deprecationComment">自 7.1.0 废弃，设置下级行政区划参考 <a href="com/amap/api/services/district/DistrictSearchQuery.html#setSubDistrict-int-"><code>DistrictSearchQuery.setSubDistrict(int)</code></a> ()}</span></div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
<a name="constructor">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<table class="deprecatedSummary" border="0" cellpadding="3" cellspacing="0" summary="已过时的构造器表, 列表已过时的构造器和解释">
<caption><span>已过时的构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="com/amap/api/services/district/DistrictSearchQuery.html#DistrictSearchQuery-java.lang.String-java.lang.String-int-">com.amap.api.services.district.DistrictSearchQuery(String, String, int)</a>
<div class="block"><span class="deprecationComment">自V3.6.1废除此接口，建议用 <a href="com/amap/api/services/district/DistrictSearchQuery.html#DistrictSearchQuery--"><code>DistrictSearchQuery.DistrictSearchQuery()</code></a>  构造。</span></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="com/amap/api/services/district/DistrictSearchQuery.html#DistrictSearchQuery-java.lang.String-java.lang.String-int-boolean-int-">com.amap.api.services.district.DistrictSearchQuery(String, String, int, boolean, int)</a>
<div class="block"><span class="deprecationComment">自V3.6.1废除此接口，建议用 <a href="com/amap/api/services/district/DistrictSearchQuery.html#DistrictSearchQuery--"><code>DistrictSearchQuery.DistrictSearchQuery()</code></a> 构造。</span></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="com/amap/api/services/help/Inputtips.html#Inputtips-Context-com.amap.api.services.help.Inputtips.InputtipsListener-">com.amap.api.services.help.Inputtips(Context, Inputtips.InputtipsListener)</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="com/amap/api/services/route/RouteSearch.RideRouteQuery.html#RideRouteQuery-com.amap.api.services.route.RouteSearch.FromAndTo-int-">com.amap.api.services.route.RouteSearch.RideRouteQuery(RouteSearch.FromAndTo, int)</a>
<div class="block"><span class="deprecationComment">因取消mode参数而废弃</span></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="com/amap/api/services/route/RouteSearch.WalkRouteQuery.html#WalkRouteQuery-com.amap.api.services.route.RouteSearch.FromAndTo-int-">com.amap.api.services.route.RouteSearch.WalkRouteQuery(RouteSearch.FromAndTo, int)</a>
<div class="block"><span class="deprecationComment">因取消mode参数而废弃</span></div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="overview-tree.html">树</a></li>
<li class="navBarCell1Rev">已过时</li>
<li><a href="index-files/index-1.html">索引</a></li>
<li><a href="help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个</li>
<li>下一个</li>
</ul>
<ul class="navList">
<li><a href="index.html?deprecated-list.html" target="_top">框架</a></li>
<li><a href="deprecated-list.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
