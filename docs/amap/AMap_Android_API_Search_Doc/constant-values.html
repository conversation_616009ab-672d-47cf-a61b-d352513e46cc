<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>常量字段值</title>
<link rel="stylesheet" type="text/css" href="stylesheet.css" title="Style">
<script type="text/javascript" src="script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="\u5E38\u91CF\u5B57\u6BB5\u503C";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="overview-tree.html">树</a></li>
<li><a href="deprecated-list.html">已过时</a></li>
<li><a href="index-files/index-1.html">索引</a></li>
<li><a href="help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个</li>
<li>下一个</li>
</ul>
<ul class="navList">
<li><a href="index.html?constant-values.html" target="_top">框架</a></li>
<li><a href="constant-values.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 title="常量字段值" class="title">常量字段值</h1>
<h2 title="目录">目录</h2>
<ul>
<li><a href="#com.amap">com.amap.*</a></li>
</ul>
</div>
<div class="constantValuesContainer"><a name="com.amap">
<!--   -->
</a>
<h2 title="com.amap">com.amap.*</h2>
<ul class="blockList">
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="常量字段值表, 列表常量字段和值">
<caption><span>com.amap.api.services.busline.<a href="com/amap/api/services/busline/BusLineSearch.html" title="com.amap.api.services.busline中的类">BusLineSearch</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th scope="col">常量字段</th>
<th class="colLast" scope="col">值</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.busline.BusLineSearch.EXTENSIONS_ALL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/services/busline/BusLineSearch.html#EXTENSIONS_ALL">EXTENSIONS_ALL</a></code></td>
<td class="colLast"><code>"all"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.busline.BusLineSearch.EXTENSIONS_BASE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/services/busline/BusLineSearch.html#EXTENSIONS_BASE">EXTENSIONS_BASE</a></code></td>
<td class="colLast"><code>"base"</code></td>
</tr>
</tbody>
</table>
</li>
</ul>
<ul class="blockList">
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="常量字段值表, 列表常量字段和值">
<caption><span>com.amap.api.services.cloud.<a href="com/amap/api/services/cloud/CloudSearch.SearchBound.html" title="com.amap.api.services.cloud中的类">CloudSearch.SearchBound</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th scope="col">常量字段</th>
<th class="colLast" scope="col">值</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.cloud.CloudSearch.SearchBound.BOUND_SHAPE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/services/cloud/CloudSearch.SearchBound.html#BOUND_SHAPE">BOUND_SHAPE</a></code></td>
<td class="colLast"><code>"Bound"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.cloud.CloudSearch.SearchBound.LOCAL_SHAPE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/services/cloud/CloudSearch.SearchBound.html#LOCAL_SHAPE">LOCAL_SHAPE</a></code></td>
<td class="colLast"><code>"Local"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.cloud.CloudSearch.SearchBound.POLYGON_SHAPE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/services/cloud/CloudSearch.SearchBound.html#POLYGON_SHAPE">POLYGON_SHAPE</a></code></td>
<td class="colLast"><code>"Polygon"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.cloud.CloudSearch.SearchBound.RECTANGLE_SHAPE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/services/cloud/CloudSearch.SearchBound.html#RECTANGLE_SHAPE">RECTANGLE_SHAPE</a></code></td>
<td class="colLast"><code>"Rectangle"</code></td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="常量字段值表, 列表常量字段和值">
<caption><span>com.amap.api.services.cloud.<a href="com/amap/api/services/cloud/CloudSearch.Sortingrules.html" title="com.amap.api.services.cloud中的类">CloudSearch.Sortingrules</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th scope="col">常量字段</th>
<th class="colLast" scope="col">值</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.cloud.CloudSearch.Sortingrules.DISTANCE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/cloud/CloudSearch.Sortingrules.html#DISTANCE">DISTANCE</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.cloud.CloudSearch.Sortingrules.WEIGHT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/cloud/CloudSearch.Sortingrules.html#WEIGHT">WEIGHT</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
</tbody>
</table>
</li>
</ul>
<ul class="blockList">
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="常量字段值表, 列表常量字段和值">
<caption><span>com.amap.api.services.core.<a href="com/amap/api/services/core/AMapException.html" title="com.amap.api.services.core中的类">AMapException</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th scope="col">常量字段</th>
<th class="colLast" scope="col">值</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.AMAP_ACCESS_TOO_FREQUENT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#AMAP_ACCESS_TOO_FREQUENT">AMAP_ACCESS_TOO_FREQUENT</a></code></td>
<td class="colLast"><code>"\u7528\u6237\u8bbf\u95ee\u8fc7\u4e8e\u9891\u7e41"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.AMAP_CLIENT_ERROR_PROTOCOL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#AMAP_CLIENT_ERROR_PROTOCOL">AMAP_CLIENT_ERROR_PROTOCOL</a></code></td>
<td class="colLast"><code>"\u534f\u8bae\u89e3\u6790\u9519\u8bef - ProtocolException"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.AMAP_CLIENT_ERRORCODE_MISSSING">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#AMAP_CLIENT_ERRORCODE_MISSSING">AMAP_CLIENT_ERRORCODE_MISSSING</a></code></td>
<td class="colLast"><code>"\u6ca1\u6709\u5bf9\u5e94\u7684\u9519\u8bef"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.AMAP_CLIENT_ERRORCODE_MISSSING_TPPE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#AMAP_CLIENT_ERRORCODE_MISSSING_TPPE">AMAP_CLIENT_ERRORCODE_MISSSING_TPPE</a></code></td>
<td class="colLast"><code>"AMAP_CLIENT_ERRORCODE_MISSSING"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.AMAP_CLIENT_INVALID_PARAMETER">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#AMAP_CLIENT_INVALID_PARAMETER">AMAP_CLIENT_INVALID_PARAMETER</a></code></td>
<td class="colLast"><code>"\u65e0\u6548\u7684\u53c2\u6570 - IllegalArgumentException"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.AMAP_CLIENT_IO_EXCEPTION">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#AMAP_CLIENT_IO_EXCEPTION">AMAP_CLIENT_IO_EXCEPTION</a></code></td>
<td class="colLast"><code>"IO \u64cd\u4f5c\u5f02\u5e38 - IOException"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.AMAP_CLIENT_NEARBY_NULL_RESULT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#AMAP_CLIENT_NEARBY_NULL_RESULT">AMAP_CLIENT_NEARBY_NULL_RESULT</a></code></td>
<td class="colLast"><code>"NearbyInfo\u5bf9\u8c61\u4e3a\u7a7a"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.AMAP_CLIENT_NETWORK_EXCEPTION">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#AMAP_CLIENT_NETWORK_EXCEPTION">AMAP_CLIENT_NETWORK_EXCEPTION</a></code></td>
<td class="colLast"><code>"http\u6216socket\u8fde\u63a5\u5931\u8d25 - ConnectionException"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.AMAP_CLIENT_NULLPOINT_EXCEPTION">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#AMAP_CLIENT_NULLPOINT_EXCEPTION">AMAP_CLIENT_NULLPOINT_EXCEPTION</a></code></td>
<td class="colLast"><code>"\u7a7a\u6307\u9488\u5f02\u5e38 - NullPointException"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.AMAP_CLIENT_OVER_KEYWORD_LEN_MAX_COUNT_EXCEPTION">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#AMAP_CLIENT_OVER_KEYWORD_LEN_MAX_COUNT_EXCEPTION">AMAP_CLIENT_OVER_KEYWORD_LEN_MAX_COUNT_EXCEPTION</a></code></td>
<td class="colLast"><code>"\u5173\u952e\u5b57\u8fc7\u957f"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.AMAP_CLIENT_OVER_PASSAREA_ITEM_POINT_COUNT_EXCEPTION">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#AMAP_CLIENT_OVER_PASSAREA_ITEM_POINT_COUNT_EXCEPTION">AMAP_CLIENT_OVER_PASSAREA_ITEM_POINT_COUNT_EXCEPTION</a></code></td>
<td class="colLast"><code>"\u907f\u8ba9\u533a\u57df\u70b9\u4e2a\u6570\u8d85\u9650"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.AMAP_CLIENT_OVER_PASSAREA_MAX_AREA_EXCEPTION">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#AMAP_CLIENT_OVER_PASSAREA_MAX_AREA_EXCEPTION">AMAP_CLIENT_OVER_PASSAREA_MAX_AREA_EXCEPTION</a></code></td>
<td class="colLast"><code>"\u907f\u8ba9\u533a\u57df\u5927\u5c0f\u8d85\u9650"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.AMAP_CLIENT_OVER_PASSAREA_MAX_COUNT_EXCEPTION">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#AMAP_CLIENT_OVER_PASSAREA_MAX_COUNT_EXCEPTION">AMAP_CLIENT_OVER_PASSAREA_MAX_COUNT_EXCEPTION</a></code></td>
<td class="colLast"><code>"\u907f\u8ba9\u533a\u57df\u4e2a\u6570\u8d85\u9650"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.AMAP_CLIENT_OVER_PASSBY_MAX_COUNT_EXCEPTION">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#AMAP_CLIENT_OVER_PASSBY_MAX_COUNT_EXCEPTION">AMAP_CLIENT_OVER_PASSBY_MAX_COUNT_EXCEPTION</a></code></td>
<td class="colLast"><code>"\u9014\u7ecf\u70b9\u4e2a\u6570\u8d85\u9650"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.AMAP_CLIENT_SOCKET_TIMEOUT_EXCEPTION">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#AMAP_CLIENT_SOCKET_TIMEOUT_EXCEPTION">AMAP_CLIENT_SOCKET_TIMEOUT_EXCEPTION</a></code></td>
<td class="colLast"><code>"socket \u8fde\u63a5\u8d85\u65f6 - SocketTimeoutException"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.AMAP_CLIENT_UNKNOWHOST_EXCEPTION">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#AMAP_CLIENT_UNKNOWHOST_EXCEPTION">AMAP_CLIENT_UNKNOWHOST_EXCEPTION</a></code></td>
<td class="colLast"><code>"\u672a\u77e5\u4e3b\u673a - UnKnowHostException"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.AMAP_CLIENT_UNKNOWN_ERROR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#AMAP_CLIENT_UNKNOWN_ERROR">AMAP_CLIENT_UNKNOWN_ERROR</a></code></td>
<td class="colLast"><code>"\u672a\u77e5\u9519\u8bef"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.AMAP_CLIENT_UNKNOWN_ERROR_TYPE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#AMAP_CLIENT_UNKNOWN_ERROR_TYPE">AMAP_CLIENT_UNKNOWN_ERROR_TYPE</a></code></td>
<td class="colLast"><code>"CLIENT_UNKNOWN_ERROR"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.AMAP_CLIENT_UPLOAD_LOCATION_ERROR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#AMAP_CLIENT_UPLOAD_LOCATION_ERROR">AMAP_CLIENT_UPLOAD_LOCATION_ERROR</a></code></td>
<td class="colLast"><code>"Point\u4e3a\u7a7a\uff0c\u6216\u4e0e\u524d\u6b21\u4e0a\u4f20\u7684\u76f8\u540c"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.AMAP_CLIENT_UPLOAD_TOO_FREQUENT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#AMAP_CLIENT_UPLOAD_TOO_FREQUENT">AMAP_CLIENT_UPLOAD_TOO_FREQUENT</a></code></td>
<td class="colLast"><code>"\u4e24\u6b21\u5355\u6b21\u4e0a\u4f20\u7684\u95f4\u9694\u4f4e\u4e8e7\u79d2"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.AMAP_CLIENT_UPLOADAUTO_STARTED_ERROR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#AMAP_CLIENT_UPLOADAUTO_STARTED_ERROR">AMAP_CLIENT_UPLOADAUTO_STARTED_ERROR</a></code></td>
<td class="colLast"><code>"\u5df2\u5f00\u542f\u81ea\u52a8\u4e0a\u4f20"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.AMAP_CLIENT_URL_EXCEPTION">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#AMAP_CLIENT_URL_EXCEPTION">AMAP_CLIENT_URL_EXCEPTION</a></code></td>
<td class="colLast"><code>"url\u5f02\u5e38 - MalformedURLException"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.AMAP_CLIENT_USERID_ILLEGAL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#AMAP_CLIENT_USERID_ILLEGAL">AMAP_CLIENT_USERID_ILLEGAL</a></code></td>
<td class="colLast"><code>"USERID\u975e\u6cd5"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.AMAP_DAILY_QUERY_OVER_LIMIT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#AMAP_DAILY_QUERY_OVER_LIMIT">AMAP_DAILY_QUERY_OVER_LIMIT</a></code></td>
<td class="colLast"><code>"\u8bbf\u95ee\u5df2\u8d85\u51fa\u65e5\u8bbf\u95ee\u91cf"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.AMAP_ENGINE_CONNECT_TIMEOUT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#AMAP_ENGINE_CONNECT_TIMEOUT">AMAP_ENGINE_CONNECT_TIMEOUT</a></code></td>
<td class="colLast"><code>"\u670d\u52a1\u7aef\u8bf7\u6c42\u94fe\u63a5\u8d85\u65f6"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.AMAP_ENGINE_RESPONSE_DATA_ERROR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#AMAP_ENGINE_RESPONSE_DATA_ERROR">AMAP_ENGINE_RESPONSE_DATA_ERROR</a></code></td>
<td class="colLast"><code>"\u5f15\u64ce\u8fd4\u56de\u6570\u636e\u5f02\u5e38"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.AMAP_ENGINE_RESPONSE_ERROR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#AMAP_ENGINE_RESPONSE_ERROR">AMAP_ENGINE_RESPONSE_ERROR</a></code></td>
<td class="colLast"><code>"\u8bf7\u6c42\u670d\u52a1\u54cd\u5e94\u9519\u8bef"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.AMAP_ENGINE_RETURN_TIMEOUT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#AMAP_ENGINE_RETURN_TIMEOUT">AMAP_ENGINE_RETURN_TIMEOUT</a></code></td>
<td class="colLast"><code>"\u8bfb\u53d6\u670d\u52a1\u7ed3\u679c\u8d85\u65f6"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.AMAP_ENGINE_TABLEID_NOT_EXIST">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#AMAP_ENGINE_TABLEID_NOT_EXIST">AMAP_ENGINE_TABLEID_NOT_EXIST</a></code></td>
<td class="colLast"><code>"key\u5bf9\u5e94\u7684tableID\u4e0d\u5b58\u5728"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.AMAP_ID_NOT_EXIST">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#AMAP_ID_NOT_EXIST">AMAP_ID_NOT_EXIST</a></code></td>
<td class="colLast"><code>"ID\u4e0d\u5b58\u5728"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.AMAP_INSUFFICIENT_PRIVILEGES">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#AMAP_INSUFFICIENT_PRIVILEGES">AMAP_INSUFFICIENT_PRIVILEGES</a></code></td>
<td class="colLast"><code>"\u6743\u9650\u4e0d\u8db3\uff0c\u670d\u52a1\u8bf7\u6c42\u88ab\u62d2\u7edd"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.AMAP_INVALID_USER_DOMAIN">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#AMAP_INVALID_USER_DOMAIN">AMAP_INVALID_USER_DOMAIN</a></code></td>
<td class="colLast"><code>"\u7528\u6237\u57df\u540d\u65e0\u6548"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.AMAP_INVALID_USER_IP">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#AMAP_INVALID_USER_IP">AMAP_INVALID_USER_IP</a></code></td>
<td class="colLast"><code>"\u7528\u6237IP\u65e0\u6548"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.AMAP_INVALID_USER_KEY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#AMAP_INVALID_USER_KEY">AMAP_INVALID_USER_KEY</a></code></td>
<td class="colLast"><code>"\u7528\u6237key\u4e0d\u6b63\u786e\u6216\u8fc7\u671f"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.AMAP_INVALID_USER_SCODE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#AMAP_INVALID_USER_SCODE">AMAP_INVALID_USER_SCODE</a></code></td>
<td class="colLast"><code>"\u7528\u6237MD5\u5b89\u5168\u7801\u672a\u901a\u8fc7"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.AMAP_IP_QUERY_OVER_LIMIT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#AMAP_IP_QUERY_OVER_LIMIT">AMAP_IP_QUERY_OVER_LIMIT</a></code></td>
<td class="colLast"><code>"IP\u8bbf\u95ee\u8d85\u9650"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.AMAP_NEARBY_INVALID_USERID">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#AMAP_NEARBY_INVALID_USERID">AMAP_NEARBY_INVALID_USERID</a></code></td>
<td class="colLast"><code>"\u627e\u4e0d\u5230\u5bf9\u5e94\u7684userid\u4fe1\u606f,\u8bf7\u68c0\u67e5\u60a8\u63d0\u4f9b\u7684userid\u662f\u5426\u5b58\u5728"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.AMAP_NEARBY_KEY_NOT_BIND">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#AMAP_NEARBY_KEY_NOT_BIND">AMAP_NEARBY_KEY_NOT_BIND</a></code></td>
<td class="colLast"><code>"App key\u672a\u5f00\u901a\u201c\u9644\u8fd1\u201d\u529f\u80fd,\u8bf7\u6ce8\u518c\u9644\u8fd1KEY"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.AMAP_NOT_SUPPORT_HTTPS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#AMAP_NOT_SUPPORT_HTTPS">AMAP_NOT_SUPPORT_HTTPS</a></code></td>
<td class="colLast"><code>"\u670d\u52a1\u4e0d\u652f\u6301https\u8bf7\u6c42"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.AMAP_OVER_DIRECTION_RANGE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#AMAP_OVER_DIRECTION_RANGE">AMAP_OVER_DIRECTION_RANGE</a></code></td>
<td class="colLast"><code>"\u8d77\u70b9\u7ec8\u70b9\u8ddd\u79bb\u8fc7\u957f"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.AMAP_ROUTE_FAIL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#AMAP_ROUTE_FAIL">AMAP_ROUTE_FAIL</a></code></td>
<td class="colLast"><code>"\u8def\u7ebf\u8ba1\u7b97\u5931\u8d25\uff0c\u901a\u5e38\u662f\u7531\u4e8e\u9053\u8def\u8fde\u901a\u5173\u7cfb\u5bfc\u81f4"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.AMAP_ROUTE_NO_ROADS_NEARBY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#AMAP_ROUTE_NO_ROADS_NEARBY">AMAP_ROUTE_NO_ROADS_NEARBY</a></code></td>
<td class="colLast"><code>"\u89c4\u5212\u70b9\uff08\u8d77\u70b9\u3001\u7ec8\u70b9\u3001\u9014\u7ecf\u70b9\uff09\u9644\u8fd1\u641c\u4e0d\u5230\u8def"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.AMAP_ROUTE_OUT_OF_SERVICE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#AMAP_ROUTE_OUT_OF_SERVICE">AMAP_ROUTE_OUT_OF_SERVICE</a></code></td>
<td class="colLast"><code>"\u89c4\u5212\u70b9\uff08\u5305\u62ec\u8d77\u70b9\u3001\u7ec8\u70b9\u3001\u9014\u7ecf\u70b9\uff09\u4e0d\u5728\u4e2d\u56fd\u9646\u5730\u8303\u56f4\u5185"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.AMAP_SERVICE_ILLEGAL_REQUEST">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#AMAP_SERVICE_ILLEGAL_REQUEST">AMAP_SERVICE_ILLEGAL_REQUEST</a></code></td>
<td class="colLast"><code>"\u8bf7\u6c42\u534f\u8bae\u975e\u6cd5"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.AMAP_SERVICE_INVALID_PARAMS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#AMAP_SERVICE_INVALID_PARAMS">AMAP_SERVICE_INVALID_PARAMS</a></code></td>
<td class="colLast"><code>"\u8bf7\u6c42\u53c2\u6570\u975e\u6cd5"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.AMAP_SERVICE_MAINTENANCE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#AMAP_SERVICE_MAINTENANCE">AMAP_SERVICE_MAINTENANCE</a></code></td>
<td class="colLast"><code>"\u670d\u52a1\u5668\u7ef4\u62a4\u4e2d"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.AMAP_SERVICE_MISSING_REQUIRED_PARAMS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#AMAP_SERVICE_MISSING_REQUIRED_PARAMS">AMAP_SERVICE_MISSING_REQUIRED_PARAMS</a></code></td>
<td class="colLast"><code>"\u7f3a\u5c11\u5fc5\u586b\u53c2\u6570"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.AMAP_SERVICE_NOT_AVAILBALE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#AMAP_SERVICE_NOT_AVAILBALE">AMAP_SERVICE_NOT_AVAILBALE</a></code></td>
<td class="colLast"><code>"\u8bf7\u6c42\u670d\u52a1\u4e0d\u5b58\u5728"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.AMAP_SERVICE_TABLEID_NOT_EXIST">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#AMAP_SERVICE_TABLEID_NOT_EXIST">AMAP_SERVICE_TABLEID_NOT_EXIST</a></code></td>
<td class="colLast"><code>"tableID\u683c\u5f0f\u4e0d\u6b63\u786e\u4e0d\u5b58\u5728"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.AMAP_SERVICE_UNKNOWN_ERROR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#AMAP_SERVICE_UNKNOWN_ERROR">AMAP_SERVICE_UNKNOWN_ERROR</a></code></td>
<td class="colLast"><code>"\u5176\u4ed6\u672a\u77e5\u9519\u8bef"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.AMAP_SHARE_FAILURE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#AMAP_SHARE_FAILURE">AMAP_SHARE_FAILURE</a></code></td>
<td class="colLast"><code>"\u77ed\u4e32\u8bf7\u6c42\u5931\u8d25"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.AMAP_SHARE_LICENSE_IS_EXPIRED">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#AMAP_SHARE_LICENSE_IS_EXPIRED">AMAP_SHARE_LICENSE_IS_EXPIRED</a></code></td>
<td class="colLast"><code>"\u77ed\u4e32\u5206\u4eab\u8ba4\u8bc1\u5931\u8d25"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.AMAP_SHARE_SIGNATURE_FAILURE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#AMAP_SHARE_SIGNATURE_FAILURE">AMAP_SHARE_SIGNATURE_FAILURE</a></code></td>
<td class="colLast"><code>"\u7528\u6237\u7b7e\u540d\u672a\u901a\u8fc7"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.AMAP_SIGNATURE_ERROR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#AMAP_SIGNATURE_ERROR">AMAP_SIGNATURE_ERROR</a></code></td>
<td class="colLast"><code>"\u7528\u6237\u7b7e\u540d\u672a\u901a\u8fc7"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.AMAP_USER_KEY_RECYCLED">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#AMAP_USER_KEY_RECYCLED">AMAP_USER_KEY_RECYCLED</a></code></td>
<td class="colLast"><code>"\u5f00\u53d1\u8005\u5220\u9664\u4e86key\uff0ckey\u88ab\u5220\u9664\u540e\u65e0\u6cd5\u6b63\u5e38\u4f7f\u7528"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.AMAP_USERKEY_PLAT_NOMATCH">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#AMAP_USERKEY_PLAT_NOMATCH">AMAP_USERKEY_PLAT_NOMATCH</a></code></td>
<td class="colLast"><code>"\u8bf7\u6c42key\u4e0e\u7ed1\u5b9a\u5e73\u53f0\u4e0d\u7b26"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.CODE_AMAP_ACCESS_TOO_FREQUENT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#CODE_AMAP_ACCESS_TOO_FREQUENT">CODE_AMAP_ACCESS_TOO_FREQUENT</a></code></td>
<td class="colLast"><code>1005</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.CODE_AMAP_CLIENT_ERROR_PROTOCOL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#CODE_AMAP_CLIENT_ERROR_PROTOCOL">CODE_AMAP_CLIENT_ERROR_PROTOCOL</a></code></td>
<td class="colLast"><code>1801</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.CODE_AMAP_CLIENT_ERRORCODE_MISSSING">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#CODE_AMAP_CLIENT_ERRORCODE_MISSSING">CODE_AMAP_CLIENT_ERRORCODE_MISSSING</a></code></td>
<td class="colLast"><code>1800</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.CODE_AMAP_CLIENT_INVALID_PARAMETER">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#CODE_AMAP_CLIENT_INVALID_PARAMETER">CODE_AMAP_CLIENT_INVALID_PARAMETER</a></code></td>
<td class="colLast"><code>1901</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.CODE_AMAP_CLIENT_IO_EXCEPTION">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#CODE_AMAP_CLIENT_IO_EXCEPTION">CODE_AMAP_CLIENT_IO_EXCEPTION</a></code></td>
<td class="colLast"><code>1902</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.CODE_AMAP_CLIENT_NEARBY_NULL_RESULT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#CODE_AMAP_CLIENT_NEARBY_NULL_RESULT">CODE_AMAP_CLIENT_NEARBY_NULL_RESULT</a></code></td>
<td class="colLast"><code>2202</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.CODE_AMAP_CLIENT_NETWORK_EXCEPTION">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#CODE_AMAP_CLIENT_NETWORK_EXCEPTION">CODE_AMAP_CLIENT_NETWORK_EXCEPTION</a></code></td>
<td class="colLast"><code>1806</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.CODE_AMAP_CLIENT_NULLPOINT_EXCEPTION">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#CODE_AMAP_CLIENT_NULLPOINT_EXCEPTION">CODE_AMAP_CLIENT_NULLPOINT_EXCEPTION</a></code></td>
<td class="colLast"><code>1903</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.CODE_AMAP_CLIENT_OVER_KEYWORD_LEN_MAX_COUNT_EXCEPTION">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#CODE_AMAP_CLIENT_OVER_KEYWORD_LEN_MAX_COUNT_EXCEPTION">CODE_AMAP_CLIENT_OVER_KEYWORD_LEN_MAX_COUNT_EXCEPTION</a></code></td>
<td class="colLast"><code>1813</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.CODE_AMAP_CLIENT_OVER_PASSAREA_ITEM_POINT_COUNT_EXCEPTION">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#CODE_AMAP_CLIENT_OVER_PASSAREA_ITEM_POINT_COUNT_EXCEPTION">CODE_AMAP_CLIENT_OVER_PASSAREA_ITEM_POINT_COUNT_EXCEPTION</a></code></td>
<td class="colLast"><code>1812</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.CODE_AMAP_CLIENT_OVER_PASSAREA_MAX_AREA_EXCEPTION">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#CODE_AMAP_CLIENT_OVER_PASSAREA_MAX_AREA_EXCEPTION">CODE_AMAP_CLIENT_OVER_PASSAREA_MAX_AREA_EXCEPTION</a></code></td>
<td class="colLast"><code>1811</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.CODE_AMAP_CLIENT_OVER_PASSAREA_MAX_COUNT_EXCEPTION">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#CODE_AMAP_CLIENT_OVER_PASSAREA_MAX_COUNT_EXCEPTION">CODE_AMAP_CLIENT_OVER_PASSAREA_MAX_COUNT_EXCEPTION</a></code></td>
<td class="colLast"><code>1810</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.CODE_AMAP_CLIENT_OVER_PASSBY_MAX_COUNT_EXCEPTION">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#CODE_AMAP_CLIENT_OVER_PASSBY_MAX_COUNT_EXCEPTION">CODE_AMAP_CLIENT_OVER_PASSBY_MAX_COUNT_EXCEPTION</a></code></td>
<td class="colLast"><code>1809</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.CODE_AMAP_CLIENT_SOCKET_TIMEOUT_EXCEPTION">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#CODE_AMAP_CLIENT_SOCKET_TIMEOUT_EXCEPTION">CODE_AMAP_CLIENT_SOCKET_TIMEOUT_EXCEPTION</a></code></td>
<td class="colLast"><code>1802</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.CODE_AMAP_CLIENT_UNKNOWHOST_EXCEPTION">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#CODE_AMAP_CLIENT_UNKNOWHOST_EXCEPTION">CODE_AMAP_CLIENT_UNKNOWHOST_EXCEPTION</a></code></td>
<td class="colLast"><code>1804</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.CODE_AMAP_CLIENT_UNKNOWN_ERROR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#CODE_AMAP_CLIENT_UNKNOWN_ERROR">CODE_AMAP_CLIENT_UNKNOWN_ERROR</a></code></td>
<td class="colLast"><code>1900</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.CODE_AMAP_CLIENT_UPLOAD_LOCATION_ERROR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#CODE_AMAP_CLIENT_UPLOAD_LOCATION_ERROR">CODE_AMAP_CLIENT_UPLOAD_LOCATION_ERROR</a></code></td>
<td class="colLast"><code>2204</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.CODE_AMAP_CLIENT_UPLOAD_TOO_FREQUENT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#CODE_AMAP_CLIENT_UPLOAD_TOO_FREQUENT">CODE_AMAP_CLIENT_UPLOAD_TOO_FREQUENT</a></code></td>
<td class="colLast"><code>2203</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.CODE_AMAP_CLIENT_UPLOADAUTO_STARTED_ERROR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#CODE_AMAP_CLIENT_UPLOADAUTO_STARTED_ERROR">CODE_AMAP_CLIENT_UPLOADAUTO_STARTED_ERROR</a></code></td>
<td class="colLast"><code>2200</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.CODE_AMAP_CLIENT_URL_EXCEPTION">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#CODE_AMAP_CLIENT_URL_EXCEPTION">CODE_AMAP_CLIENT_URL_EXCEPTION</a></code></td>
<td class="colLast"><code>1803</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.CODE_AMAP_CLIENT_USERID_ILLEGAL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#CODE_AMAP_CLIENT_USERID_ILLEGAL">CODE_AMAP_CLIENT_USERID_ILLEGAL</a></code></td>
<td class="colLast"><code>2201</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.CODE_AMAP_DAILY_QUERY_OVER_LIMIT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#CODE_AMAP_DAILY_QUERY_OVER_LIMIT">CODE_AMAP_DAILY_QUERY_OVER_LIMIT</a></code></td>
<td class="colLast"><code>1004</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.CODE_AMAP_ENGINE_CONNECT_TIMEOUT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#CODE_AMAP_ENGINE_CONNECT_TIMEOUT">CODE_AMAP_ENGINE_CONNECT_TIMEOUT</a></code></td>
<td class="colLast"><code>1102</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.CODE_AMAP_ENGINE_RESPONSE_DATA_ERROR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#CODE_AMAP_ENGINE_RESPONSE_DATA_ERROR">CODE_AMAP_ENGINE_RESPONSE_DATA_ERROR</a></code></td>
<td class="colLast"><code>1101</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.CODE_AMAP_ENGINE_RESPONSE_ERROR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#CODE_AMAP_ENGINE_RESPONSE_ERROR">CODE_AMAP_ENGINE_RESPONSE_ERROR</a></code></td>
<td class="colLast"><code>1100</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.CODE_AMAP_ENGINE_RETURN_TIMEOUT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#CODE_AMAP_ENGINE_RETURN_TIMEOUT">CODE_AMAP_ENGINE_RETURN_TIMEOUT</a></code></td>
<td class="colLast"><code>1103</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.CODE_AMAP_ENGINE_TABLEID_NOT_EXIST">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#CODE_AMAP_ENGINE_TABLEID_NOT_EXIST">CODE_AMAP_ENGINE_TABLEID_NOT_EXIST</a></code></td>
<td class="colLast"><code>2003</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.CODE_AMAP_ID_NOT_EXIST">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#CODE_AMAP_ID_NOT_EXIST">CODE_AMAP_ID_NOT_EXIST</a></code></td>
<td class="colLast"><code>2001</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.CODE_AMAP_INSUFFICIENT_PRIVILEGES">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#CODE_AMAP_INSUFFICIENT_PRIVILEGES">CODE_AMAP_INSUFFICIENT_PRIVILEGES</a></code></td>
<td class="colLast"><code>1012</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.CODE_AMAP_INVALID_USER_DOMAIN">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#CODE_AMAP_INVALID_USER_DOMAIN">CODE_AMAP_INVALID_USER_DOMAIN</a></code></td>
<td class="colLast"><code>1007</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.CODE_AMAP_INVALID_USER_IP">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#CODE_AMAP_INVALID_USER_IP">CODE_AMAP_INVALID_USER_IP</a></code></td>
<td class="colLast"><code>1006</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.CODE_AMAP_INVALID_USER_KEY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#CODE_AMAP_INVALID_USER_KEY">CODE_AMAP_INVALID_USER_KEY</a></code></td>
<td class="colLast"><code>1002</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.CODE_AMAP_INVALID_USER_SCODE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#CODE_AMAP_INVALID_USER_SCODE">CODE_AMAP_INVALID_USER_SCODE</a></code></td>
<td class="colLast"><code>1008</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.CODE_AMAP_IP_QUERY_OVER_LIMIT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#CODE_AMAP_IP_QUERY_OVER_LIMIT">CODE_AMAP_IP_QUERY_OVER_LIMIT</a></code></td>
<td class="colLast"><code>1010</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.CODE_AMAP_NEARBY_INVALID_USERID">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#CODE_AMAP_NEARBY_INVALID_USERID">CODE_AMAP_NEARBY_INVALID_USERID</a></code></td>
<td class="colLast"><code>2100</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.CODE_AMAP_NEARBY_KEY_NOT_BIND">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#CODE_AMAP_NEARBY_KEY_NOT_BIND">CODE_AMAP_NEARBY_KEY_NOT_BIND</a></code></td>
<td class="colLast"><code>2101</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.CODE_AMAP_NOT_SUPPORT_HTTPS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#CODE_AMAP_NOT_SUPPORT_HTTPS">CODE_AMAP_NOT_SUPPORT_HTTPS</a></code></td>
<td class="colLast"><code>1011</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.CODE_AMAP_OVER_DIRECTION_RANGE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#CODE_AMAP_OVER_DIRECTION_RANGE">CODE_AMAP_OVER_DIRECTION_RANGE</a></code></td>
<td class="colLast"><code>3003</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.CODE_AMAP_ROUTE_FAIL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#CODE_AMAP_ROUTE_FAIL">CODE_AMAP_ROUTE_FAIL</a></code></td>
<td class="colLast"><code>3002</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.CODE_AMAP_ROUTE_NO_ROADS_NEARBY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#CODE_AMAP_ROUTE_NO_ROADS_NEARBY">CODE_AMAP_ROUTE_NO_ROADS_NEARBY</a></code></td>
<td class="colLast"><code>3001</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.CODE_AMAP_ROUTE_OUT_OF_SERVICE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#CODE_AMAP_ROUTE_OUT_OF_SERVICE">CODE_AMAP_ROUTE_OUT_OF_SERVICE</a></code></td>
<td class="colLast"><code>3000</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.CODE_AMAP_SERVICE_ILLEGAL_REQUEST">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#CODE_AMAP_SERVICE_ILLEGAL_REQUEST">CODE_AMAP_SERVICE_ILLEGAL_REQUEST</a></code></td>
<td class="colLast"><code>1202</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.CODE_AMAP_SERVICE_INVALID_PARAMS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#CODE_AMAP_SERVICE_INVALID_PARAMS">CODE_AMAP_SERVICE_INVALID_PARAMS</a></code></td>
<td class="colLast"><code>1200</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.CODE_AMAP_SERVICE_MAINTENANCE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#CODE_AMAP_SERVICE_MAINTENANCE">CODE_AMAP_SERVICE_MAINTENANCE</a></code></td>
<td class="colLast"><code>2002</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.CODE_AMAP_SERVICE_MISSING_REQUIRED_PARAMS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#CODE_AMAP_SERVICE_MISSING_REQUIRED_PARAMS">CODE_AMAP_SERVICE_MISSING_REQUIRED_PARAMS</a></code></td>
<td class="colLast"><code>1201</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.CODE_AMAP_SERVICE_NOT_AVAILBALE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#CODE_AMAP_SERVICE_NOT_AVAILBALE">CODE_AMAP_SERVICE_NOT_AVAILBALE</a></code></td>
<td class="colLast"><code>1003</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.CODE_AMAP_SERVICE_TABLEID_NOT_EXIST">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#CODE_AMAP_SERVICE_TABLEID_NOT_EXIST">CODE_AMAP_SERVICE_TABLEID_NOT_EXIST</a></code></td>
<td class="colLast"><code>2000</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.CODE_AMAP_SERVICE_UNKNOWN_ERROR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#CODE_AMAP_SERVICE_UNKNOWN_ERROR">CODE_AMAP_SERVICE_UNKNOWN_ERROR</a></code></td>
<td class="colLast"><code>1203</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.CODE_AMAP_SHARE_FAILURE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#CODE_AMAP_SHARE_FAILURE">CODE_AMAP_SHARE_FAILURE</a></code></td>
<td class="colLast"><code>4001</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.CODE_AMAP_SHARE_LICENSE_IS_EXPIRED">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#CODE_AMAP_SHARE_LICENSE_IS_EXPIRED">CODE_AMAP_SHARE_LICENSE_IS_EXPIRED</a></code></td>
<td class="colLast"><code>4000</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.CODE_AMAP_SHARE_SIGNATURE_FAILURE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#CODE_AMAP_SHARE_SIGNATURE_FAILURE">CODE_AMAP_SHARE_SIGNATURE_FAILURE</a></code></td>
<td class="colLast"><code>4002</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.CODE_AMAP_SIGNATURE_ERROR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#CODE_AMAP_SIGNATURE_ERROR">CODE_AMAP_SIGNATURE_ERROR</a></code></td>
<td class="colLast"><code>1001</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.CODE_AMAP_SUCCESS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#CODE_AMAP_SUCCESS">CODE_AMAP_SUCCESS</a></code></td>
<td class="colLast"><code>1000</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.CODE_AMAP_USER_KEY_RECYCLED">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#CODE_AMAP_USER_KEY_RECYCLED">CODE_AMAP_USER_KEY_RECYCLED</a></code></td>
<td class="colLast"><code>1013</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.core.AMapException.CODE_AMAP_USERKEY_PLAT_NOMATCH">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/core/AMapException.html#CODE_AMAP_USERKEY_PLAT_NOMATCH">CODE_AMAP_USERKEY_PLAT_NOMATCH</a></code></td>
<td class="colLast"><code>1009</code></td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="常量字段值表, 列表常量字段和值">
<caption><span>com.amap.api.services.core.<a href="com/amap/api/services/core/ServiceSettings.html" title="com.amap.api.services.core中的类">ServiceSettings</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th scope="col">常量字段</th>
<th class="colLast" scope="col">值</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.core.ServiceSettings.CHINESE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/services/core/ServiceSettings.html#CHINESE">CHINESE</a></code></td>
<td class="colLast"><code>"zh-CN"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.core.ServiceSettings.ENGLISH">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/services/core/ServiceSettings.html#ENGLISH">ENGLISH</a></code></td>
<td class="colLast"><code>"en"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.core.ServiceSettings.HTTP">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/core/ServiceSettings.html#HTTP">HTTP</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.core.ServiceSettings.HTTPS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/core/ServiceSettings.html#HTTPS">HTTPS</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
</tbody>
</table>
</li>
</ul>
<ul class="blockList">
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="常量字段值表, 列表常量字段和值">
<caption><span>com.amap.api.services.district.<a href="com/amap/api/services/district/DistrictSearchQuery.html" title="com.amap.api.services.district中的类">DistrictSearchQuery</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th scope="col">常量字段</th>
<th class="colLast" scope="col">值</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.district.DistrictSearchQuery.KEYWORDS_BUSINESS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/services/district/DistrictSearchQuery.html#KEYWORDS_BUSINESS">KEYWORDS_BUSINESS</a></code></td>
<td class="colLast"><code>"biz_area"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.district.DistrictSearchQuery.KEYWORDS_CITY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/services/district/DistrictSearchQuery.html#KEYWORDS_CITY">KEYWORDS_CITY</a></code></td>
<td class="colLast"><code>"city"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.district.DistrictSearchQuery.KEYWORDS_COUNTRY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/services/district/DistrictSearchQuery.html#KEYWORDS_COUNTRY">KEYWORDS_COUNTRY</a></code></td>
<td class="colLast"><code>"country"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.district.DistrictSearchQuery.KEYWORDS_DISTRICT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/services/district/DistrictSearchQuery.html#KEYWORDS_DISTRICT">KEYWORDS_DISTRICT</a></code></td>
<td class="colLast"><code>"district"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.district.DistrictSearchQuery.KEYWORDS_PROVINCE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/services/district/DistrictSearchQuery.html#KEYWORDS_PROVINCE">KEYWORDS_PROVINCE</a></code></td>
<td class="colLast"><code>"province"</code></td>
</tr>
</tbody>
</table>
</li>
</ul>
<ul class="blockList">
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="常量字段值表, 列表常量字段和值">
<caption><span>com.amap.api.services.geocoder.<a href="com/amap/api/services/geocoder/GeocodeSearch.html" title="com.amap.api.services.geocoder中的类">GeocodeSearch</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th scope="col">常量字段</th>
<th class="colLast" scope="col">值</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.geocoder.GeocodeSearch.AMAP">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/services/geocoder/GeocodeSearch.html#AMAP">AMAP</a></code></td>
<td class="colLast"><code>"autonavi"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.geocoder.GeocodeSearch.EXTENSIONS_ALL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/services/geocoder/GeocodeSearch.html#EXTENSIONS_ALL">EXTENSIONS_ALL</a></code></td>
<td class="colLast"><code>"all"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.geocoder.GeocodeSearch.EXTENSIONS_BASE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/services/geocoder/GeocodeSearch.html#EXTENSIONS_BASE">EXTENSIONS_BASE</a></code></td>
<td class="colLast"><code>"base"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.geocoder.GeocodeSearch.GPS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/services/geocoder/GeocodeSearch.html#GPS">GPS</a></code></td>
<td class="colLast"><code>"gps"</code></td>
</tr>
</tbody>
</table>
</li>
</ul>
<ul class="blockList">
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="常量字段值表, 列表常量字段和值">
<caption><span>com.amap.api.services.nearby.<a href="com/amap/api/services/nearby/NearbySearch.html" title="com.amap.api.services.nearby中的类">NearbySearch</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th scope="col">常量字段</th>
<th class="colLast" scope="col">值</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.nearby.NearbySearch.AMAP">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/nearby/NearbySearch.html#AMAP">AMAP</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.nearby.NearbySearch.GPS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/nearby/NearbySearch.html#GPS">GPS</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
</tbody>
</table>
</li>
</ul>
<ul class="blockList">
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="常量字段值表, 列表常量字段和值">
<caption><span>com.amap.api.services.poisearch.<a href="com/amap/api/services/poisearch/PoiSearch.html" title="com.amap.api.services.poisearch中的类">PoiSearch</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th scope="col">常量字段</th>
<th class="colLast" scope="col">值</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.poisearch.PoiSearch.CHINESE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/services/poisearch/PoiSearch.html#CHINESE">CHINESE</a></code></td>
<td class="colLast"><code>"zh-CN"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.poisearch.PoiSearch.ENGLISH">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/services/poisearch/PoiSearch.html#ENGLISH">ENGLISH</a></code></td>
<td class="colLast"><code>"en"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.poisearch.PoiSearch.EXTENSIONS_ALL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/services/poisearch/PoiSearch.html#EXTENSIONS_ALL">EXTENSIONS_ALL</a></code></td>
<td class="colLast"><code>"all"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.poisearch.PoiSearch.EXTENSIONS_BASE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/services/poisearch/PoiSearch.html#EXTENSIONS_BASE">EXTENSIONS_BASE</a></code></td>
<td class="colLast"><code>"base"</code></td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="常量字段值表, 列表常量字段和值">
<caption><span>com.amap.api.services.poisearch.<a href="com/amap/api/services/poisearch/PoiSearch.SearchBound.html" title="com.amap.api.services.poisearch中的类">PoiSearch.SearchBound</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th scope="col">常量字段</th>
<th class="colLast" scope="col">值</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.poisearch.PoiSearch.SearchBound.BOUND_SHAPE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/services/poisearch/PoiSearch.SearchBound.html#BOUND_SHAPE">BOUND_SHAPE</a></code></td>
<td class="colLast"><code>"Bound"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.poisearch.PoiSearch.SearchBound.ELLIPSE_SHAPE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/services/poisearch/PoiSearch.SearchBound.html#ELLIPSE_SHAPE">ELLIPSE_SHAPE</a></code></td>
<td class="colLast"><code>"Ellipse"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.poisearch.PoiSearch.SearchBound.POLYGON_SHAPE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/services/poisearch/PoiSearch.SearchBound.html#POLYGON_SHAPE">POLYGON_SHAPE</a></code></td>
<td class="colLast"><code>"Polygon"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.poisearch.PoiSearch.SearchBound.RECTANGLE_SHAPE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/services/poisearch/PoiSearch.SearchBound.html#RECTANGLE_SHAPE">RECTANGLE_SHAPE</a></code></td>
<td class="colLast"><code>"Rectangle"</code></td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="常量字段值表, 列表常量字段和值">
<caption><span>com.amap.api.services.poisearch.<a href="com/amap/api/services/poisearch/PoiSearchV2.html" title="com.amap.api.services.poisearch中的类">PoiSearchV2</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th scope="col">常量字段</th>
<th class="colLast" scope="col">值</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.poisearch.PoiSearchV2.CHINESE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/services/poisearch/PoiSearchV2.html#CHINESE">CHINESE</a></code></td>
<td class="colLast"><code>"zh-CN"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.poisearch.PoiSearchV2.ENGLISH">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/services/poisearch/PoiSearchV2.html#ENGLISH">ENGLISH</a></code></td>
<td class="colLast"><code>"en"</code></td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="常量字段值表, 列表常量字段和值">
<caption><span>com.amap.api.services.poisearch.<a href="com/amap/api/services/poisearch/PoiSearchV2.SearchBound.html" title="com.amap.api.services.poisearch中的类">PoiSearchV2.SearchBound</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th scope="col">常量字段</th>
<th class="colLast" scope="col">值</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.poisearch.PoiSearchV2.SearchBound.BOUND_SHAPE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/services/poisearch/PoiSearchV2.SearchBound.html#BOUND_SHAPE">BOUND_SHAPE</a></code></td>
<td class="colLast"><code>"Bound"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.poisearch.PoiSearchV2.SearchBound.ELLIPSE_SHAPE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/services/poisearch/PoiSearchV2.SearchBound.html#ELLIPSE_SHAPE">ELLIPSE_SHAPE</a></code></td>
<td class="colLast"><code>"Ellipse"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.poisearch.PoiSearchV2.SearchBound.POLYGON_SHAPE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/services/poisearch/PoiSearchV2.SearchBound.html#POLYGON_SHAPE">POLYGON_SHAPE</a></code></td>
<td class="colLast"><code>"Polygon"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.poisearch.PoiSearchV2.SearchBound.RECTANGLE_SHAPE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/services/poisearch/PoiSearchV2.SearchBound.html#RECTANGLE_SHAPE">RECTANGLE_SHAPE</a></code></td>
<td class="colLast"><code>"Rectangle"</code></td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="常量字段值表, 列表常量字段和值">
<caption><span>com.amap.api.services.poisearch.<a href="com/amap/api/services/poisearch/PoiSearchV2.ShowFields.html" title="com.amap.api.services.poisearch中的类">PoiSearchV2.ShowFields</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th scope="col">常量字段</th>
<th class="colLast" scope="col">值</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.poisearch.PoiSearchV2.ShowFields.ALL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/poisearch/PoiSearchV2.ShowFields.html#ALL">ALL</a></code></td>
<td class="colLast"><code>-1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.poisearch.PoiSearchV2.ShowFields.BUSINESS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/poisearch/PoiSearchV2.ShowFields.html#BUSINESS">BUSINESS</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.poisearch.PoiSearchV2.ShowFields.CHILDREN">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/poisearch/PoiSearchV2.ShowFields.html#CHILDREN">CHILDREN</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.poisearch.PoiSearchV2.ShowFields.DEFAULT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/poisearch/PoiSearchV2.ShowFields.html#DEFAULT">DEFAULT</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.poisearch.PoiSearchV2.ShowFields.INDOOR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/poisearch/PoiSearchV2.ShowFields.html#INDOOR">INDOOR</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.poisearch.PoiSearchV2.ShowFields.NAVI">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/poisearch/PoiSearchV2.ShowFields.html#NAVI">NAVI</a></code></td>
<td class="colLast"><code>8</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.poisearch.PoiSearchV2.ShowFields.PHOTOS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/poisearch/PoiSearchV2.ShowFields.html#PHOTOS">PHOTOS</a></code></td>
<td class="colLast"><code>16</code></td>
</tr>
</tbody>
</table>
</li>
</ul>
<ul class="blockList">
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="常量字段值表, 列表常量字段和值">
<caption><span>com.amap.api.services.route.<a href="com/amap/api/services/route/DistanceItem.html" title="com.amap.api.services.route中的类">DistanceItem</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th scope="col">常量字段</th>
<th class="colLast" scope="col">值</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.route.DistanceItem.ERROR_CODE_NO_DRIVE">
<!--   -->
</a><code>public&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/DistanceItem.html#ERROR_CODE_NO_DRIVE">ERROR_CODE_NO_DRIVE</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.route.DistanceItem.ERROR_CODE_NOT_IN_CHINA">
<!--   -->
</a><code>public&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/DistanceItem.html#ERROR_CODE_NOT_IN_CHINA">ERROR_CODE_NOT_IN_CHINA</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.route.DistanceItem.ERROR_CODE_TOO_FAR">
<!--   -->
</a><code>public&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/DistanceItem.html#ERROR_CODE_TOO_FAR">ERROR_CODE_TOO_FAR</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="常量字段值表, 列表常量字段和值">
<caption><span>com.amap.api.services.route.<a href="com/amap/api/services/route/DistanceSearch.html" title="com.amap.api.services.route中的类">DistanceSearch</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th scope="col">常量字段</th>
<th class="colLast" scope="col">值</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.route.DistanceSearch.EXTENSIONS_ALL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/services/route/DistanceSearch.html#EXTENSIONS_ALL">EXTENSIONS_ALL</a></code></td>
<td class="colLast"><code>"all"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.route.DistanceSearch.EXTENSIONS_BASE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/services/route/DistanceSearch.html#EXTENSIONS_BASE">EXTENSIONS_BASE</a></code></td>
<td class="colLast"><code>"base"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.route.DistanceSearch.TYPE_DISTANCE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/DistanceSearch.html#TYPE_DISTANCE">TYPE_DISTANCE</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.route.DistanceSearch.TYPE_DRIVING_DISTANCE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/DistanceSearch.html#TYPE_DRIVING_DISTANCE">TYPE_DRIVING_DISTANCE</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.route.DistanceSearch.TYPE_WALK_DISTANCE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/DistanceSearch.html#TYPE_WALK_DISTANCE">TYPE_WALK_DISTANCE</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="常量字段值表, 列表常量字段和值">
<caption><span>com.amap.api.services.route.<a href="com/amap/api/services/route/RouteSearch.html" title="com.amap.api.services.route中的类">RouteSearch</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th scope="col">常量字段</th>
<th class="colLast" scope="col">值</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearch.BUS_COMFORTABLE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearch.html#BUS_COMFORTABLE">BUS_COMFORTABLE</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearch.BUS_DEFAULT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearch.html#BUS_DEFAULT">BUS_DEFAULT</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearch.BUS_LEASE_CHANGE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearch.html#BUS_LEASE_CHANGE">BUS_LEASE_CHANGE</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearch.BUS_LEASE_WALK">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearch.html#BUS_LEASE_WALK">BUS_LEASE_WALK</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearch.BUS_NO_SUBWAY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearch.html#BUS_NO_SUBWAY">BUS_NO_SUBWAY</a></code></td>
<td class="colLast"><code>5</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearch.BUS_SAVE_MONEY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearch.html#BUS_SAVE_MONEY">BUS_SAVE_MONEY</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearch.BusComfortable">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearch.html#BusComfortable">BusComfortable</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearch.BusDefault">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearch.html#BusDefault">BusDefault</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearch.BusLeaseChange">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearch.html#BusLeaseChange">BusLeaseChange</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearch.BusLeaseWalk">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearch.html#BusLeaseWalk">BusLeaseWalk</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearch.BusNoSubway">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearch.html#BusNoSubway">BusNoSubway</a></code></td>
<td class="colLast"><code>5</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearch.BusSaveMoney">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearch.html#BusSaveMoney">BusSaveMoney</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearch.DRIVEING_PLAN_AVOID_CONGESTION_CHOICE_HIGHWAY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearch.html#DRIVEING_PLAN_AVOID_CONGESTION_CHOICE_HIGHWAY">DRIVEING_PLAN_AVOID_CONGESTION_CHOICE_HIGHWAY</a></code></td>
<td class="colLast"><code>9</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearch.DRIVEING_PLAN_AVOID_CONGESTION_FASTEST_SAVE_MONEY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearch.html#DRIVEING_PLAN_AVOID_CONGESTION_FASTEST_SAVE_MONEY">DRIVEING_PLAN_AVOID_CONGESTION_FASTEST_SAVE_MONEY</a></code></td>
<td class="colLast"><code>11</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearch.DRIVEING_PLAN_AVOID_CONGESTION_NO_HIGHWAY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearch.html#DRIVEING_PLAN_AVOID_CONGESTION_NO_HIGHWAY">DRIVEING_PLAN_AVOID_CONGESTION_NO_HIGHWAY</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearch.DRIVEING_PLAN_AVOID_CONGESTION_SAVE_MONEY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearch.html#DRIVEING_PLAN_AVOID_CONGESTION_SAVE_MONEY">DRIVEING_PLAN_AVOID_CONGESTION_SAVE_MONEY</a></code></td>
<td class="colLast"><code>6</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearch.DRIVEING_PLAN_AVOID_CONGESTION_SAVE_MONEY_NO_HIGHWAY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearch.html#DRIVEING_PLAN_AVOID_CONGESTION_SAVE_MONEY_NO_HIGHWAY">DRIVEING_PLAN_AVOID_CONGESTION_SAVE_MONEY_NO_HIGHWAY</a></code></td>
<td class="colLast"><code>7</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearch.DRIVEING_PLAN_CHOICE_HIGHWAY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearch.html#DRIVEING_PLAN_CHOICE_HIGHWAY">DRIVEING_PLAN_CHOICE_HIGHWAY</a></code></td>
<td class="colLast"><code>8</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearch.DRIVEING_PLAN_DEFAULT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearch.html#DRIVEING_PLAN_DEFAULT">DRIVEING_PLAN_DEFAULT</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearch.DRIVEING_PLAN_FASTEST_SHORTEST">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearch.html#DRIVEING_PLAN_FASTEST_SHORTEST">DRIVEING_PLAN_FASTEST_SHORTEST</a></code></td>
<td class="colLast"><code>10</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearch.DRIVEING_PLAN_NO_HIGHWAY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearch.html#DRIVEING_PLAN_NO_HIGHWAY">DRIVEING_PLAN_NO_HIGHWAY</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearch.DRIVEING_PLAN_SAVE_MONEY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearch.html#DRIVEING_PLAN_SAVE_MONEY">DRIVEING_PLAN_SAVE_MONEY</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearch.DRIVEING_PLAN_SAVE_MONEY_NO_HIGHWAY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearch.html#DRIVEING_PLAN_SAVE_MONEY_NO_HIGHWAY">DRIVEING_PLAN_SAVE_MONEY_NO_HIGHWAY</a></code></td>
<td class="colLast"><code>5</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearch.DRIVING_EXCLUDE_FERRY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearch.html#DRIVING_EXCLUDE_FERRY">DRIVING_EXCLUDE_FERRY</a></code></td>
<td class="colLast"><code>"ferry"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearch.DRIVING_EXCLUDE_MOTORWAY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearch.html#DRIVING_EXCLUDE_MOTORWAY">DRIVING_EXCLUDE_MOTORWAY</a></code></td>
<td class="colLast"><code>"motorway"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearch.DRIVING_EXCLUDE_TOLL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearch.html#DRIVING_EXCLUDE_TOLL">DRIVING_EXCLUDE_TOLL</a></code></td>
<td class="colLast"><code>"toll"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearch.DRIVING_MULTI_CHOICE_AVOID_CONGESTION">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearch.html#DRIVING_MULTI_CHOICE_AVOID_CONGESTION">DRIVING_MULTI_CHOICE_AVOID_CONGESTION</a></code></td>
<td class="colLast"><code>12</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearch.DRIVING_MULTI_CHOICE_AVOID_CONGESTION_NO_HIGHWAY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearch.html#DRIVING_MULTI_CHOICE_AVOID_CONGESTION_NO_HIGHWAY">DRIVING_MULTI_CHOICE_AVOID_CONGESTION_NO_HIGHWAY</a></code></td>
<td class="colLast"><code>15</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearch.DRIVING_MULTI_CHOICE_AVOID_CONGESTION_NO_HIGHWAY_SAVE_MONEY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearch.html#DRIVING_MULTI_CHOICE_AVOID_CONGESTION_NO_HIGHWAY_SAVE_MONEY">DRIVING_MULTI_CHOICE_AVOID_CONGESTION_NO_HIGHWAY_SAVE_MONEY</a></code></td>
<td class="colLast"><code>18</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearch.DRIVING_MULTI_CHOICE_AVOID_CONGESTION_SAVE_MONEY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearch.html#DRIVING_MULTI_CHOICE_AVOID_CONGESTION_SAVE_MONEY">DRIVING_MULTI_CHOICE_AVOID_CONGESTION_SAVE_MONEY</a></code></td>
<td class="colLast"><code>17</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearch.DRIVING_MULTI_CHOICE_HIGHWAY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearch.html#DRIVING_MULTI_CHOICE_HIGHWAY">DRIVING_MULTI_CHOICE_HIGHWAY</a></code></td>
<td class="colLast"><code>19</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearch.DRIVING_MULTI_CHOICE_HIGHWAY_AVOID_CONGESTION">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearch.html#DRIVING_MULTI_CHOICE_HIGHWAY_AVOID_CONGESTION">DRIVING_MULTI_CHOICE_HIGHWAY_AVOID_CONGESTION</a></code></td>
<td class="colLast"><code>20</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearch.DRIVING_MULTI_CHOICE_NO_HIGHWAY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearch.html#DRIVING_MULTI_CHOICE_NO_HIGHWAY">DRIVING_MULTI_CHOICE_NO_HIGHWAY</a></code></td>
<td class="colLast"><code>13</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearch.DRIVING_MULTI_CHOICE_SAVE_MONEY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearch.html#DRIVING_MULTI_CHOICE_SAVE_MONEY">DRIVING_MULTI_CHOICE_SAVE_MONEY</a></code></td>
<td class="colLast"><code>14</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearch.DRIVING_MULTI_CHOICE_SAVE_MONEY_NO_HIGHWAY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearch.html#DRIVING_MULTI_CHOICE_SAVE_MONEY_NO_HIGHWAY">DRIVING_MULTI_CHOICE_SAVE_MONEY_NO_HIGHWAY</a></code></td>
<td class="colLast"><code>16</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearch.DRIVING_MULTI_STRATEGY_FASTEST_SAVE_MONEY_SHORTEST">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearch.html#DRIVING_MULTI_STRATEGY_FASTEST_SAVE_MONEY_SHORTEST">DRIVING_MULTI_STRATEGY_FASTEST_SAVE_MONEY_SHORTEST</a></code></td>
<td class="colLast"><code>5</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearch.DRIVING_MULTI_STRATEGY_FASTEST_SHORTEST">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearch.html#DRIVING_MULTI_STRATEGY_FASTEST_SHORTEST">DRIVING_MULTI_STRATEGY_FASTEST_SHORTEST</a></code></td>
<td class="colLast"><code>11</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearch.DRIVING_MULTI_STRATEGY_FASTEST_SHORTEST_AVOID_CONGESTION">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearch.html#DRIVING_MULTI_STRATEGY_FASTEST_SHORTEST_AVOID_CONGESTION">DRIVING_MULTI_STRATEGY_FASTEST_SHORTEST_AVOID_CONGESTION</a></code></td>
<td class="colLast"><code>10</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearch.DRIVING_NORMAL_CAR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearch.html#DRIVING_NORMAL_CAR">DRIVING_NORMAL_CAR</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearch.DRIVING_PLUGIN_HYBRID_CAR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearch.html#DRIVING_PLUGIN_HYBRID_CAR">DRIVING_PLUGIN_HYBRID_CAR</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearch.DRIVING_PURE_ELECTRIC_VEHICLE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearch.html#DRIVING_PURE_ELECTRIC_VEHICLE">DRIVING_PURE_ELECTRIC_VEHICLE</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearch.DRIVING_SINGLE_AVOID_CONGESTION">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearch.html#DRIVING_SINGLE_AVOID_CONGESTION">DRIVING_SINGLE_AVOID_CONGESTION</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearch.DRIVING_SINGLE_DEFAULT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearch.html#DRIVING_SINGLE_DEFAULT">DRIVING_SINGLE_DEFAULT</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearch.DRIVING_SINGLE_NO_EXPRESSWAYS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearch.html#DRIVING_SINGLE_NO_EXPRESSWAYS">DRIVING_SINGLE_NO_EXPRESSWAYS</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearch.DRIVING_SINGLE_NO_HIGHWAY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearch.html#DRIVING_SINGLE_NO_HIGHWAY">DRIVING_SINGLE_NO_HIGHWAY</a></code></td>
<td class="colLast"><code>6</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearch.DRIVING_SINGLE_NO_HIGHWAY_SAVE_MONEY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearch.html#DRIVING_SINGLE_NO_HIGHWAY_SAVE_MONEY">DRIVING_SINGLE_NO_HIGHWAY_SAVE_MONEY</a></code></td>
<td class="colLast"><code>7</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearch.DRIVING_SINGLE_NO_HIGHWAY_SAVE_MONEY_AVOID_CONGESTION">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearch.html#DRIVING_SINGLE_NO_HIGHWAY_SAVE_MONEY_AVOID_CONGESTION">DRIVING_SINGLE_NO_HIGHWAY_SAVE_MONEY_AVOID_CONGESTION</a></code></td>
<td class="colLast"><code>9</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearch.DRIVING_SINGLE_SAVE_MONEY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearch.html#DRIVING_SINGLE_SAVE_MONEY">DRIVING_SINGLE_SAVE_MONEY</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearch.DRIVING_SINGLE_SAVE_MONEY_AVOID_CONGESTION">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearch.html#DRIVING_SINGLE_SAVE_MONEY_AVOID_CONGESTION">DRIVING_SINGLE_SAVE_MONEY_AVOID_CONGESTION</a></code></td>
<td class="colLast"><code>8</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearch.DRIVING_SINGLE_SHORTEST">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearch.html#DRIVING_SINGLE_SHORTEST">DRIVING_SINGLE_SHORTEST</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearch.DrivingAvoidCongestion">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearch.html#DrivingAvoidCongestion">DrivingAvoidCongestion</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearch.DrivingDefault">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearch.html#DrivingDefault">DrivingDefault</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearch.DrivingMultiStrategy">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearch.html#DrivingMultiStrategy">DrivingMultiStrategy</a></code></td>
<td class="colLast"><code>5</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearch.DrivingNoExpressways">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearch.html#DrivingNoExpressways">DrivingNoExpressways</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearch.DrivingNoHighAvoidCongestionSaveMoney">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearch.html#DrivingNoHighAvoidCongestionSaveMoney">DrivingNoHighAvoidCongestionSaveMoney</a></code></td>
<td class="colLast"><code>9</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearch.DrivingNoHighWay">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearch.html#DrivingNoHighWay">DrivingNoHighWay</a></code></td>
<td class="colLast"><code>6</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearch.DrivingNoHighWaySaveMoney">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearch.html#DrivingNoHighWaySaveMoney">DrivingNoHighWaySaveMoney</a></code></td>
<td class="colLast"><code>7</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearch.DrivingSaveMoney">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearch.html#DrivingSaveMoney">DrivingSaveMoney</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearch.DrivingSaveMoneyAvoidCongestion">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearch.html#DrivingSaveMoneyAvoidCongestion">DrivingSaveMoneyAvoidCongestion</a></code></td>
<td class="colLast"><code>8</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearch.DrivingShortDistance">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearch.html#DrivingShortDistance">DrivingShortDistance</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearch.EXTENSIONS_ALL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearch.html#EXTENSIONS_ALL">EXTENSIONS_ALL</a></code></td>
<td class="colLast"><code>"all"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearch.EXTENSIONS_BASE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearch.html#EXTENSIONS_BASE">EXTENSIONS_BASE</a></code></td>
<td class="colLast"><code>"base"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearch.RIDING_DEFAULT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearch.html#RIDING_DEFAULT">RIDING_DEFAULT</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearch.RIDING_FAST">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearch.html#RIDING_FAST">RIDING_FAST</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearch.RIDING_RECOMMEND">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearch.html#RIDING_RECOMMEND">RIDING_RECOMMEND</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearch.RidingDefault">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearch.html#RidingDefault">RidingDefault</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearch.RidingFast">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearch.html#RidingFast">RidingFast</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearch.RidingRecommend">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearch.html#RidingRecommend">RidingRecommend</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearch.TRUCK_AVOID_CONGESTION">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearch.html#TRUCK_AVOID_CONGESTION">TRUCK_AVOID_CONGESTION</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearch.TRUCK_AVOID_CONGESTION__SAVE_MONEY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearch.html#TRUCK_AVOID_CONGESTION__SAVE_MONEY">TRUCK_AVOID_CONGESTION__SAVE_MONEY</a></code></td>
<td class="colLast"><code>6</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearch.TRUCK_AVOID_CONGESTION__SAVE_MONEY_NO_HIGHWAY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearch.html#TRUCK_AVOID_CONGESTION__SAVE_MONEY_NO_HIGHWAY">TRUCK_AVOID_CONGESTION__SAVE_MONEY_NO_HIGHWAY</a></code></td>
<td class="colLast"><code>7</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearch.TRUCK_AVOID_CONGESTION_CHOICE_HIGHWAY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearch.html#TRUCK_AVOID_CONGESTION_CHOICE_HIGHWAY">TRUCK_AVOID_CONGESTION_CHOICE_HIGHWAY</a></code></td>
<td class="colLast"><code>9</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearch.TRUCK_AVOID_CONGESTION_NO_HIGHWAY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearch.html#TRUCK_AVOID_CONGESTION_NO_HIGHWAY">TRUCK_AVOID_CONGESTION_NO_HIGHWAY</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearch.TRUCK_CHOICE_HIGHWAY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearch.html#TRUCK_CHOICE_HIGHWAY">TRUCK_CHOICE_HIGHWAY</a></code></td>
<td class="colLast"><code>8</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearch.TRUCK_NO_HIGHWAY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearch.html#TRUCK_NO_HIGHWAY">TRUCK_NO_HIGHWAY</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearch.TRUCK_SAVE_MONEY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearch.html#TRUCK_SAVE_MONEY">TRUCK_SAVE_MONEY</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearch.TRUCK_SAVE_MONEY_NO_HIGHWAY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearch.html#TRUCK_SAVE_MONEY_NO_HIGHWAY">TRUCK_SAVE_MONEY_NO_HIGHWAY</a></code></td>
<td class="colLast"><code>5</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearch.TRUCK_SIZE_HEAVY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearch.html#TRUCK_SIZE_HEAVY">TRUCK_SIZE_HEAVY</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearch.TRUCK_SIZE_LIGHT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearch.html#TRUCK_SIZE_LIGHT">TRUCK_SIZE_LIGHT</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearch.TRUCK_SIZE_MEDIUM">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearch.html#TRUCK_SIZE_MEDIUM">TRUCK_SIZE_MEDIUM</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearch.TRUCK_SIZE_MINI">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearch.html#TRUCK_SIZE_MINI">TRUCK_SIZE_MINI</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearch.WALK_DEFAULT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearch.html#WALK_DEFAULT">WALK_DEFAULT</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearch.WALK_MULTI_PATH">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearch.html#WALK_MULTI_PATH">WALK_MULTI_PATH</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearch.WalkDefault">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearch.html#WalkDefault">WalkDefault</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearch.WalkMultipath">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearch.html#WalkMultipath">WalkMultipath</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="常量字段值表, 列表常量字段和值">
<caption><span>com.amap.api.services.route.<a href="com/amap/api/services/route/RouteSearchV2.AlternativeRoute.html" title="com.amap.api.services.route中的类">RouteSearchV2.AlternativeRoute</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th scope="col">常量字段</th>
<th class="colLast" scope="col">值</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearchV2.AlternativeRoute.ALTERNATIVE_ROUTE_ONE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearchV2.AlternativeRoute.html#ALTERNATIVE_ROUTE_ONE">ALTERNATIVE_ROUTE_ONE</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearchV2.AlternativeRoute.ALTERNATIVE_ROUTE_THREE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearchV2.AlternativeRoute.html#ALTERNATIVE_ROUTE_THREE">ALTERNATIVE_ROUTE_THREE</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearchV2.AlternativeRoute.ALTERNATIVE_ROUTE_TWO">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearchV2.AlternativeRoute.html#ALTERNATIVE_ROUTE_TWO">ALTERNATIVE_ROUTE_TWO</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="常量字段值表, 列表常量字段和值">
<caption><span>com.amap.api.services.route.<a href="com/amap/api/services/route/RouteSearchV2.BusMode.html" title="com.amap.api.services.route中的类">RouteSearchV2.BusMode</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th scope="col">常量字段</th>
<th class="colLast" scope="col">值</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearchV2.BusMode.BUS_COMFORTABLE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearchV2.BusMode.html#BUS_COMFORTABLE">BUS_COMFORTABLE</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearchV2.BusMode.BUS_DEFAULT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearchV2.BusMode.html#BUS_DEFAULT">BUS_DEFAULT</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearchV2.BusMode.BUS_LEASE_CHANGE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearchV2.BusMode.html#BUS_LEASE_CHANGE">BUS_LEASE_CHANGE</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearchV2.BusMode.BUS_LEASE_WALK">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearchV2.BusMode.html#BUS_LEASE_WALK">BUS_LEASE_WALK</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearchV2.BusMode.BUS_NO_SUBWAY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearchV2.BusMode.html#BUS_NO_SUBWAY">BUS_NO_SUBWAY</a></code></td>
<td class="colLast"><code>5</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearchV2.BusMode.BUS_SAVE_MONEY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearchV2.BusMode.html#BUS_SAVE_MONEY">BUS_SAVE_MONEY</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearchV2.BusMode.BUS_SUBWAY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearchV2.BusMode.html#BUS_SUBWAY">BUS_SUBWAY</a></code></td>
<td class="colLast"><code>6</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearchV2.BusMode.BUS_SUBWAY_FIRST">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearchV2.BusMode.html#BUS_SUBWAY_FIRST">BUS_SUBWAY_FIRST</a></code></td>
<td class="colLast"><code>7</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearchV2.BusMode.BUS_WASTE_LESS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearchV2.BusMode.html#BUS_WASTE_LESS">BUS_WASTE_LESS</a></code></td>
<td class="colLast"><code>8</code></td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="常量字段值表, 列表常量字段和值">
<caption><span>com.amap.api.services.route.<a href="com/amap/api/services/route/RouteSearchV2.ShowFields.html" title="com.amap.api.services.route中的类">RouteSearchV2.ShowFields</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th scope="col">常量字段</th>
<th class="colLast" scope="col">值</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearchV2.ShowFields.ALL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearchV2.ShowFields.html#ALL">ALL</a></code></td>
<td class="colLast"><code>-1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearchV2.ShowFields.CITIES">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearchV2.ShowFields.html#CITIES">CITIES</a></code></td>
<td class="colLast"><code>8</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearchV2.ShowFields.COST">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearchV2.ShowFields.html#COST">COST</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearchV2.ShowFields.NAVI">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearchV2.ShowFields.html#NAVI">NAVI</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearchV2.ShowFields.POLINE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearchV2.ShowFields.html#POLINE">POLINE</a></code></td>
<td class="colLast"><code>16</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.route.RouteSearchV2.ShowFields.TMCS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/route/RouteSearchV2.ShowFields.html#TMCS">TMCS</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
</tbody>
</table>
</li>
</ul>
<ul class="blockList">
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="常量字段值表, 列表常量字段和值">
<caption><span>com.amap.api.services.routepoisearch.<a href="com/amap/api/services/routepoisearch/RoutePOISearch.html" title="com.amap.api.services.routepoisearch中的类">RoutePOISearch</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th scope="col">常量字段</th>
<th class="colLast" scope="col">值</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.routepoisearch.RoutePOISearch.DrivingAvoidCongestion">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/routepoisearch/RoutePOISearch.html#DrivingAvoidCongestion">DrivingAvoidCongestion</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.routepoisearch.RoutePOISearch.DrivingDefault">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/routepoisearch/RoutePOISearch.html#DrivingDefault">DrivingDefault</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.routepoisearch.RoutePOISearch.DrivingNoExpressways">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/routepoisearch/RoutePOISearch.html#DrivingNoExpressways">DrivingNoExpressways</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.routepoisearch.RoutePOISearch.DrivingNoHighAvoidCongestionSaveMoney">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/routepoisearch/RoutePOISearch.html#DrivingNoHighAvoidCongestionSaveMoney">DrivingNoHighAvoidCongestionSaveMoney</a></code></td>
<td class="colLast"><code>9</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.routepoisearch.RoutePOISearch.DrivingNoHighWay">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/routepoisearch/RoutePOISearch.html#DrivingNoHighWay">DrivingNoHighWay</a></code></td>
<td class="colLast"><code>6</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.routepoisearch.RoutePOISearch.DrivingNoHighWaySaveMoney">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/routepoisearch/RoutePOISearch.html#DrivingNoHighWaySaveMoney">DrivingNoHighWaySaveMoney</a></code></td>
<td class="colLast"><code>7</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.routepoisearch.RoutePOISearch.DrivingSaveMoney">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/routepoisearch/RoutePOISearch.html#DrivingSaveMoney">DrivingSaveMoney</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.routepoisearch.RoutePOISearch.DrivingSaveMoneyAvoidCongestion">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/routepoisearch/RoutePOISearch.html#DrivingSaveMoneyAvoidCongestion">DrivingSaveMoneyAvoidCongestion</a></code></td>
<td class="colLast"><code>8</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.routepoisearch.RoutePOISearch.DrivingShortDistance">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/routepoisearch/RoutePOISearch.html#DrivingShortDistance">DrivingShortDistance</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
</tbody>
</table>
</li>
</ul>
<ul class="blockList">
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="常量字段值表, 列表常量字段和值">
<caption><span>com.amap.api.services.share.<a href="com/amap/api/services/share/ShareSearch.html" title="com.amap.api.services.share中的类">ShareSearch</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th scope="col">常量字段</th>
<th class="colLast" scope="col">值</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.share.ShareSearch.BusComfortable">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/share/ShareSearch.html#BusComfortable">BusComfortable</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.share.ShareSearch.BusDefault">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/share/ShareSearch.html#BusDefault">BusDefault</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.share.ShareSearch.BusLeaseChange">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/share/ShareSearch.html#BusLeaseChange">BusLeaseChange</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.share.ShareSearch.BusLeaseWalk">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/share/ShareSearch.html#BusLeaseWalk">BusLeaseWalk</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.share.ShareSearch.BusNoSubway">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/share/ShareSearch.html#BusNoSubway">BusNoSubway</a></code></td>
<td class="colLast"><code>5</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.share.ShareSearch.BusSaveMoney">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/share/ShareSearch.html#BusSaveMoney">BusSaveMoney</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.share.ShareSearch.DrivingAvoidCongestion">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/share/ShareSearch.html#DrivingAvoidCongestion">DrivingAvoidCongestion</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.share.ShareSearch.DrivingDefault">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/share/ShareSearch.html#DrivingDefault">DrivingDefault</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.share.ShareSearch.DrivingNoHighWay">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/share/ShareSearch.html#DrivingNoHighWay">DrivingNoHighWay</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.share.ShareSearch.DrivingNoHighWayAvoidCongestion">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/share/ShareSearch.html#DrivingNoHighWayAvoidCongestion">DrivingNoHighWayAvoidCongestion</a></code></td>
<td class="colLast"><code>6</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.share.ShareSearch.DrivingNoHighWaySaveMoney">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/share/ShareSearch.html#DrivingNoHighWaySaveMoney">DrivingNoHighWaySaveMoney</a></code></td>
<td class="colLast"><code>5</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.share.ShareSearch.DrivingNoHighWaySaveMoneyAvoidCongestion">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/share/ShareSearch.html#DrivingNoHighWaySaveMoneyAvoidCongestion">DrivingNoHighWaySaveMoneyAvoidCongestion</a></code></td>
<td class="colLast"><code>8</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.share.ShareSearch.DrivingSaveMoney">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/share/ShareSearch.html#DrivingSaveMoney">DrivingSaveMoney</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.share.ShareSearch.DrivingSaveMoneyAvoidCongestion">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/share/ShareSearch.html#DrivingSaveMoneyAvoidCongestion">DrivingSaveMoneyAvoidCongestion</a></code></td>
<td class="colLast"><code>7</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.share.ShareSearch.DrivingShortDistance">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/share/ShareSearch.html#DrivingShortDistance">DrivingShortDistance</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.share.ShareSearch.NaviAvoidCongestion">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/share/ShareSearch.html#NaviAvoidCongestion">NaviAvoidCongestion</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.share.ShareSearch.NaviDefault">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/share/ShareSearch.html#NaviDefault">NaviDefault</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.share.ShareSearch.NaviNoHighWay">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/share/ShareSearch.html#NaviNoHighWay">NaviNoHighWay</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.share.ShareSearch.NaviNoHighWayAvoidCongestion">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/share/ShareSearch.html#NaviNoHighWayAvoidCongestion">NaviNoHighWayAvoidCongestion</a></code></td>
<td class="colLast"><code>6</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.share.ShareSearch.NaviNoHighWaySaveMoney">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/share/ShareSearch.html#NaviNoHighWaySaveMoney">NaviNoHighWaySaveMoney</a></code></td>
<td class="colLast"><code>5</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.share.ShareSearch.NaviNoHighWaySaveMoneyAvoidCongestion">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/share/ShareSearch.html#NaviNoHighWaySaveMoneyAvoidCongestion">NaviNoHighWaySaveMoneyAvoidCongestion</a></code></td>
<td class="colLast"><code>8</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.share.ShareSearch.NaviSaveMoney">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/share/ShareSearch.html#NaviSaveMoney">NaviSaveMoney</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.share.ShareSearch.NaviSaveMoneyAvoidCongestion">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/share/ShareSearch.html#NaviSaveMoneyAvoidCongestion">NaviSaveMoneyAvoidCongestion</a></code></td>
<td class="colLast"><code>7</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.share.ShareSearch.NaviShortDistance">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/share/ShareSearch.html#NaviShortDistance">NaviShortDistance</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
</tbody>
</table>
</li>
</ul>
<ul class="blockList">
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="常量字段值表, 列表常量字段和值">
<caption><span>com.amap.api.services.weather.<a href="com/amap/api/services/weather/WeatherSearchQuery.html" title="com.amap.api.services.weather中的类">WeatherSearchQuery</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th scope="col">常量字段</th>
<th class="colLast" scope="col">值</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.services.weather.WeatherSearchQuery.WEATHER_TYPE_FORECAST">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/weather/WeatherSearchQuery.html#WEATHER_TYPE_FORECAST">WEATHER_TYPE_FORECAST</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.services.weather.WeatherSearchQuery.WEATHER_TYPE_LIVE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/services/weather/WeatherSearchQuery.html#WEATHER_TYPE_LIVE">WEATHER_TYPE_LIVE</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
</tbody>
</table>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="overview-tree.html">树</a></li>
<li><a href="deprecated-list.html">已过时</a></li>
<li><a href="index-files/index-1.html">索引</a></li>
<li><a href="help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个</li>
<li>下一个</li>
</ul>
<ul class="navList">
<li><a href="index.html?constant-values.html" target="_top">框架</a></li>
<li><a href="constant-values.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
