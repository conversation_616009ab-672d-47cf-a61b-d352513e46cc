<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>O - 索引</title>
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="O - \u7D22\u5F15";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-13.html">上一个字母</a></li>
<li><a href="index-15.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-14.html" target="_top">框架</a></li>
<li><a href="index-14.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="contentContainer"><a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">J</a>&nbsp;<a href="index-11.html">L</a>&nbsp;<a href="index-12.html">M</a>&nbsp;<a href="index-13.html">N</a>&nbsp;<a href="index-14.html">O</a>&nbsp;<a href="index-15.html">P</a>&nbsp;<a href="index-16.html">R</a>&nbsp;<a href="index-17.html">S</a>&nbsp;<a href="index-18.html">T</a>&nbsp;<a href="index-19.html">U</a>&nbsp;<a href="index-20.html">V</a>&nbsp;<a href="index-21.html">W</a>&nbsp;<a href="index-22.html">Z</a>&nbsp;<a name="I:O">
<!--   -->
</a>
<h2 class="title">O</h2>
<dl>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/AMapNaviListener.html#onArriveDestination--">onArriveDestination()</a></span> - 接口 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/AMapNaviListener.html" title="com.amap.api.navi中的接口">AMapNaviListener</a></dt>
<dd>
<div class="block">到达目的地后回调函数。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/INaviInfoCallback.html#onArriveDestination-boolean-">onArriveDestination(boolean)</a></span> - 接口 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/INaviInfoCallback.html" title="com.amap.api.navi中的接口">INaviInfoCallback</a></dt>
<dd>
<div class="block">到达目的地后回调函数。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/SimpleNaviListener.html#onArriveDestination--">onArriveDestination()</a></span> - 类 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/SimpleNaviListener.html" title="com.amap.api.navi中的类">SimpleNaviListener</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/view/RouteOverLay.html#onArriveDestination--">onArriveDestination()</a></span> - 类 中的方法com.amap.api.navi.view.<a href="../com/amap/api/navi/view/RouteOverLay.html" title="com.amap.api.navi.view中的类">RouteOverLay</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/AMapNaviTravelListener.html#onArrivedTravelWayPoint-int-">onArrivedTravelWayPoint(int)</a></span> - 接口 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/AMapNaviTravelListener.html" title="com.amap.api.navi中的接口">AMapNaviTravelListener</a></dt>
<dd>
<div class="block">骑步行导航到达途径点的回调函数</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/SimpleNaviListener.html#onArrivedTravelWayPoint-int-">onArrivedTravelWayPoint(int)</a></span> - 类 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/SimpleNaviListener.html" title="com.amap.api.navi中的类">SimpleNaviListener</a></dt>
<dd>
<div class="block">骑步行导航到达途径点的回调函数</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/AMapNaviListener.html#onArrivedWayPoint-int-">onArrivedWayPoint(int)</a></span> - 接口 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/AMapNaviListener.html" title="com.amap.api.navi中的接口">AMapNaviListener</a></dt>
<dd>
<div class="block">驾车路径导航到达某个途经点的回调函数。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/INaviInfoCallback.html#onArrivedWayPoint-int-">onArrivedWayPoint(int)</a></span> - 接口 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/INaviInfoCallback.html" title="com.amap.api.navi中的接口">INaviInfoCallback</a></dt>
<dd>
<div class="block">驾车路径导航到达某个途经点的回调函数。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/SimpleNaviListener.html#onArrivedWayPoint-int-">onArrivedWayPoint(int)</a></span> - 类 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/SimpleNaviListener.html" title="com.amap.api.navi中的类">SimpleNaviListener</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/INaviInfoCallback.html#onBroadcastModeChanged-int-">onBroadcastModeChanged(int)</a></span> - 接口 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/INaviInfoCallback.html" title="com.amap.api.navi中的接口">INaviInfoCallback</a></dt>
<dd>
<div class="block">播报模式变化回调</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/AMapHudView.html#onCalculateRouteFailure-com.amap.api.navi.model.AMapCalcRouteResult-">onCalculateRouteFailure(AMapCalcRouteResult)</a></span> - 类 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/AMapHudView.html" title="com.amap.api.navi中的类">AMapHudView</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/AMapNaviListener.html#onCalculateRouteFailure-int-">onCalculateRouteFailure(int)</a></span> - 接口 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/AMapNaviListener.html" title="com.amap.api.navi中的接口">AMapNaviListener</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span>
<div class="block"><span class="deprecationComment">该方法在6.1.0版本废弃，但是还会正常回调，建议使用<a href="../com/amap/api/navi/AMapNaviListener.html#onCalculateRouteFailure-com.amap.api.navi.model.AMapCalcRouteResult-"><code>AMapNaviListener.onCalculateRouteFailure(AMapCalcRouteResult)</code></a> 方法替换</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/AMapNaviListener.html#onCalculateRouteFailure-com.amap.api.navi.model.AMapCalcRouteResult-">onCalculateRouteFailure(AMapCalcRouteResult)</a></span> - 接口 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/AMapNaviListener.html" title="com.amap.api.navi中的接口">AMapNaviListener</a></dt>
<dd>
<div class="block">路线规划失败回调，包括算路、导航中偏航、用户改变算路策略、行程点等触发的重算，具体算路结果可以通过<a href="../com/amap/api/navi/model/AMapCalcRouteResult.html" title="com.amap.api.navi.model中的类"><code>AMapCalcRouteResult</code></a>获取
 可以通过CalcRouteResult获取算路错误码、算路类型以及路线id</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/INaviInfoCallback.html#onCalculateRouteFailure-int-">onCalculateRouteFailure(int)</a></span> - 接口 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/INaviInfoCallback.html" title="com.amap.api.navi中的接口">INaviInfoCallback</a></dt>
<dd>
<div class="block">驾车路径规划失败后的回调函数。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/SimpleNaviListener.html#onCalculateRouteFailure-int-">onCalculateRouteFailure(int)</a></span> - 类 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/SimpleNaviListener.html" title="com.amap.api.navi中的类">SimpleNaviListener</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/SimpleNaviListener.html#onCalculateRouteFailure-com.amap.api.navi.model.AMapCalcRouteResult-">onCalculateRouteFailure(AMapCalcRouteResult)</a></span> - 类 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/SimpleNaviListener.html" title="com.amap.api.navi中的类">SimpleNaviListener</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/AMapHudView.html#onCalculateRouteSuccess-int:A-">onCalculateRouteSuccess(int[])</a></span> - 类 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/AMapHudView.html" title="com.amap.api.navi中的类">AMapHudView</a></dt>
<dd>
<div class="block">算路成功回调</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/AMapHudView.html#onCalculateRouteSuccess-com.amap.api.navi.model.AMapCalcRouteResult-">onCalculateRouteSuccess(AMapCalcRouteResult)</a></span> - 类 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/AMapHudView.html" title="com.amap.api.navi中的类">AMapHudView</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/AMapNaviListener.html#onCalculateRouteSuccess-int:A-">onCalculateRouteSuccess(int[])</a></span> - 接口 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/AMapNaviListener.html" title="com.amap.api.navi中的接口">AMapNaviListener</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span>
<div class="block"><span class="deprecationComment">该方法在6.1.0版本废弃，但是还会正常回调，建议使用<a href="../com/amap/api/navi/AMapNaviListener.html#onCalculateRouteSuccess-com.amap.api.navi.model.AMapCalcRouteResult-"><code>AMapNaviListener.onCalculateRouteSuccess(AMapCalcRouteResult)</code></a> 方法替换</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/AMapNaviListener.html#onCalculateRouteSuccess-com.amap.api.navi.model.AMapCalcRouteResult-">onCalculateRouteSuccess(AMapCalcRouteResult)</a></span> - 接口 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/AMapNaviListener.html" title="com.amap.api.navi中的接口">AMapNaviListener</a></dt>
<dd>
<div class="block">路线规划成功回调，包括算路、导航中偏航、用户改变算路策略、行程点等触发的重算，具体算路结果可以通过<a href="../com/amap/api/navi/model/AMapCalcRouteResult.html" title="com.amap.api.navi.model中的类"><code>AMapCalcRouteResult</code></a>获取
 可以通过CalcRouteResult获取算路错误码、算路类型以及路线id</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/INaviInfoCallback.html#onCalculateRouteSuccess-int:A-">onCalculateRouteSuccess(int[])</a></span> - 接口 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/INaviInfoCallback.html" title="com.amap.api.navi中的接口">INaviInfoCallback</a></dt>
<dd>
<div class="block">算路成功回调</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/SimpleNaviListener.html#onCalculateRouteSuccess-int:A-">onCalculateRouteSuccess(int[])</a></span> - 类 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/SimpleNaviListener.html" title="com.amap.api.navi中的类">SimpleNaviListener</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/SimpleNaviListener.html#onCalculateRouteSuccess-com.amap.api.navi.model.AMapCalcRouteResult-">onCalculateRouteSuccess(AMapCalcRouteResult)</a></span> - 类 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/SimpleNaviListener.html" title="com.amap.api.navi中的类">SimpleNaviListener</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/AMapNaviView.html#onCreate-Bundle-">onCreate(Bundle)</a></span> - 类 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/AMapNaviView.html" title="com.amap.api.navi中的类">AMapNaviView</a></dt>
<dd>
<div class="block">与Activity onCreate同步</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/INaviInfoCallback.html#onDayAndNightModeChanged-int-">onDayAndNightModeChanged(int)</a></span> - 接口 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/INaviInfoCallback.html" title="com.amap.api.navi中的接口">INaviInfoCallback</a></dt>
<dd>
<div class="block">昼夜模式设置变化回调</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/AMapHudView.html#onDestroy--">onDestroy()</a></span> - 类 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/AMapHudView.html" title="com.amap.api.navi中的类">AMapHudView</a></dt>
<dd>
<div class="block">销毁AMapHudView</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/AMapNaviView.html#onDestroy--">onDestroy()</a></span> - 类 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/AMapNaviView.html" title="com.amap.api.navi中的类">AMapNaviView</a></dt>
<dd>
<div class="block">与Activity onDestroy同步
 <p/>
 在1.6.0之前，此方法会自动执行AMapNavi.stopNavi(); 在1.6.0之后（包括1.6.0），请用户自己根据需要选择执行AMapNavi.stopNavi();</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/AMapNaviListener.html#onEndEmulatorNavi--">onEndEmulatorNavi()</a></span> - 接口 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/AMapNaviListener.html" title="com.amap.api.navi中的接口">AMapNaviListener</a></dt>
<dd>
<div class="block">模拟导航停止后回调函数。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/SimpleNaviListener.html#onEndEmulatorNavi--">onEndEmulatorNavi()</a></span> - 类 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/SimpleNaviListener.html" title="com.amap.api.navi中的类">SimpleNaviListener</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/INaviInfoCallback.html#onExitPage-int-">onExitPage(int)</a></span> - 接口 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/INaviInfoCallback.html" title="com.amap.api.navi中的接口">INaviInfoCallback</a></dt>
<dd>
<div class="block">退出组件或退出组件导航的回调函数</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/view/AMapModeCrossOverlay.OnCreateBitmapFinish.html#onGenerateComplete-Bitmap-int-">onGenerateComplete(Bitmap, int)</a></span> - 接口 中的方法com.amap.api.navi.view.<a href="../com/amap/api/navi/view/AMapModeCrossOverlay.OnCreateBitmapFinish.html" title="com.amap.api.navi.view中的接口">AMapModeCrossOverlay.OnCreateBitmapFinish</a></dt>
<dd>
<div class="block">返回模型路口放大图的bitmap</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/AMapHudView.html#onGetNavigationText-java.lang.String-">onGetNavigationText(String)</a></span> - 类 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/AMapHudView.html" title="com.amap.api.navi中的类">AMapHudView</a></dt>
<dd>
<div class="block">导航播报信息回调函数。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/AMapNaviListener.html#onGetNavigationText-int-java.lang.String-">onGetNavigationText(int, String)</a></span> - 接口 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/AMapNaviListener.html" title="com.amap.api.navi中的接口">AMapNaviListener</a></dt>
<dd>
<div class="block">导航播报信息回调函数。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/AMapNaviListener.html#onGetNavigationText-java.lang.String-">onGetNavigationText(String)</a></span> - 接口 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/AMapNaviListener.html" title="com.amap.api.navi中的接口">AMapNaviListener</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/INaviInfoCallback.html#onGetNavigationText-java.lang.String-">onGetNavigationText(String)</a></span> - 接口 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/INaviInfoCallback.html" title="com.amap.api.navi中的接口">INaviInfoCallback</a></dt>
<dd>
<div class="block">导航播报信息回调函数。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/SimpleNaviListener.html#onGetNavigationText-int-java.lang.String-">onGetNavigationText(int, String)</a></span> - 类 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/SimpleNaviListener.html" title="com.amap.api.navi中的类">SimpleNaviListener</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/SimpleNaviListener.html#onGetNavigationText-java.lang.String-">onGetNavigationText(String)</a></span> - 类 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/SimpleNaviListener.html" title="com.amap.api.navi中的类">SimpleNaviListener</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/AMapNaviListener.html#onGpsOpenStatus-boolean-">onGpsOpenStatus(boolean)</a></span> - 接口 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/AMapNaviListener.html" title="com.amap.api.navi中的接口">AMapNaviListener</a></dt>
<dd>
<div class="block">用户手机位置信息设置是否开启的回调函数。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/SimpleNaviListener.html#onGpsOpenStatus-boolean-">onGpsOpenStatus(boolean)</a></span> - 类 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/SimpleNaviListener.html" title="com.amap.api.navi中的类">SimpleNaviListener</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/AMapNaviListener.html#onGpsSignalWeak-boolean-">onGpsSignalWeak(boolean)</a></span> - 接口 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/AMapNaviListener.html" title="com.amap.api.navi中的接口">AMapNaviListener</a></dt>
<dd>
<div class="block">手机卫星定位信号强弱变化的回调</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/SimpleNaviListener.html#onGpsSignalWeak-boolean-">onGpsSignalWeak(boolean)</a></span> - 类 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/SimpleNaviListener.html" title="com.amap.api.navi中的类">SimpleNaviListener</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/AMapHudViewListener.html#onHudViewCancel--">onHudViewCancel()</a></span> - 接口 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/AMapHudViewListener.html" title="com.amap.api.navi中的接口">AMapHudViewListener</a></dt>
<dd>
<div class="block">点击<a href="../com/amap/api/navi/AMapHudView.html" title="com.amap.api.navi中的类"><code>AMapHudView</code></a>中的返回按钮将回调此接口</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/AMapNaviIndependentRouteListener.html#onIndependentCalculateFail-com.amap.api.navi.model.AMapCalcRouteResult-">onIndependentCalculateFail(AMapCalcRouteResult)</a></span> - 接口 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/AMapNaviIndependentRouteListener.html" title="com.amap.api.navi中的接口">AMapNaviIndependentRouteListener</a></dt>
<dd>
<div class="block">独立算路失败回调</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/AMapNaviIndependentRouteListener.html#onIndependentCalculateSuccess-com.amap.api.navi.model.AMapNaviPathGroup-">onIndependentCalculateSuccess(AMapNaviPathGroup)</a></span> - 接口 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/AMapNaviIndependentRouteListener.html" title="com.amap.api.navi中的接口">AMapNaviIndependentRouteListener</a></dt>
<dd>
<div class="block">独立算路成功回调</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/AMapNaviListener.html#onInitNaviFailure--">onInitNaviFailure()</a></span> - 接口 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/AMapNaviListener.html" title="com.amap.api.navi中的接口">AMapNaviListener</a></dt>
<dd>
<div class="block">导航初始化失败时的回调函数。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/INaviInfoCallback.html#onInitNaviFailure--">onInitNaviFailure()</a></span> - 接口 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/INaviInfoCallback.html" title="com.amap.api.navi中的接口">INaviInfoCallback</a></dt>
<dd>
<div class="block">导航初始化失败时的回调函数。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/SimpleNaviListener.html#onInitNaviFailure--">onInitNaviFailure()</a></span> - 类 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/SimpleNaviListener.html" title="com.amap.api.navi中的类">SimpleNaviListener</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/AMapNaviListener.html#onInitNaviSuccess--">onInitNaviSuccess()</a></span> - 接口 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/AMapNaviListener.html" title="com.amap.api.navi中的接口">AMapNaviListener</a></dt>
<dd>
<div class="block">导航初始化成功时的回调函数。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/SimpleNaviListener.html#onInitNaviSuccess--">onInitNaviSuccess()</a></span> - 类 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/SimpleNaviListener.html" title="com.amap.api.navi中的类">SimpleNaviListener</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/SimpleNaviListener.html#onInnerNaviInfoUpdate-InnerNaviInfo-">onInnerNaviInfoUpdate(InnerNaviInfo)</a></span> - 类 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/SimpleNaviListener.html" title="com.amap.api.navi中的类">SimpleNaviListener</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/SimpleNaviListener.html#onInnerNaviInfoUpdate-InnerNaviInfo:A-">onInnerNaviInfoUpdate(InnerNaviInfo[])</a></span> - 类 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/SimpleNaviListener.html" title="com.amap.api.navi中的类">SimpleNaviListener</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/AMapNaviListener.html#onLocationChange-com.amap.api.navi.model.AMapNaviLocation-">onLocationChange(AMapNaviLocation)</a></span> - 接口 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/AMapNaviListener.html" title="com.amap.api.navi中的接口">AMapNaviListener</a></dt>
<dd>
<div class="block">当位置信息有更新时的回调函数。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/INaviInfoCallback.html#onLocationChange-com.amap.api.navi.model.AMapNaviLocation-">onLocationChange(AMapNaviLocation)</a></span> - 接口 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/INaviInfoCallback.html" title="com.amap.api.navi中的接口">INaviInfoCallback</a></dt>
<dd>
<div class="block">当GPS位置有更新时的回调函数。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/SimpleNaviListener.html#onLocationChange-com.amap.api.navi.model.AMapNaviLocation-">onLocationChange(AMapNaviLocation)</a></span> - 类 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/SimpleNaviListener.html" title="com.amap.api.navi中的类">SimpleNaviListener</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/AMapNaviViewListener.html#onLockMap-boolean-">onLockMap(boolean)</a></span> - 接口 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/AMapNaviViewListener.html" title="com.amap.api.navi中的接口">AMapNaviViewListener</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span>
<div class="block"><span class="deprecationComment">请使用 <a href="../com/amap/api/navi/AMapNaviViewListener.html#onNaviViewShowMode-int-"><code>AMapNaviViewListener.onNaviViewShowMode(int)</code></a></span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/FullLinkLogCallback.html#onLogCallback-java.lang.String-long-">onLogCallback(String, long)</a></span> - 接口 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/FullLinkLogCallback.html" title="com.amap.api.navi中的接口">FullLinkLogCallback</a></dt>
<dd>
<div class="block">日志记录回调</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/AMapNaviViewListener.html#onMapTypeChanged-int-">onMapTypeChanged(int)</a></span> - 接口 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/AMapNaviViewListener.html" title="com.amap.api.navi中的接口">AMapNaviViewListener</a></dt>
<dd>
<div class="block">AMapNaviView地图白天黑夜模式切换回调</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/INaviInfoCallback.html#onMapTypeChanged-int-">onMapTypeChanged(int)</a></span> - 接口 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/INaviInfoCallback.html" title="com.amap.api.navi中的接口">INaviInfoCallback</a></dt>
<dd>
<div class="block">组件地图白天黑夜模式切换回调</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/AMapNaviViewListener.html#onNaviBackClick--">onNaviBackClick()</a></span> - 接口 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/AMapNaviViewListener.html" title="com.amap.api.navi中的接口">AMapNaviViewListener</a></dt>
<dd>
<div class="block">导航页面左下角"退出"按钮的点击回调</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/AMapNaviViewListener.html#onNaviCancel--">onNaviCancel()</a></span> - 接口 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/AMapNaviViewListener.html" title="com.amap.api.navi中的接口">AMapNaviViewListener</a></dt>
<dd>
<div class="block">导航页面左下角返回按钮点击后弹出的『退出导航』对话框中选择『确定』后的回调接口。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/INaviInfoCallback.html#onNaviDirectionChanged-int-">onNaviDirectionChanged(int)</a></span> - 接口 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/INaviInfoCallback.html" title="com.amap.api.navi中的接口">INaviInfoCallback</a></dt>
<dd>
<div class="block">导航视角变化回调</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/AMapNaviListener.html#onNaviInfoUpdate-com.amap.api.navi.model.NaviInfo-">onNaviInfoUpdate(NaviInfo)</a></span> - 接口 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/AMapNaviListener.html" title="com.amap.api.navi中的接口">AMapNaviListener</a></dt>
<dd>
<div class="block">导航引导信息回调。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/SimpleNaviListener.html#onNaviInfoUpdate-com.amap.api.navi.model.NaviInfo-">onNaviInfoUpdate(NaviInfo)</a></span> - 类 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/SimpleNaviListener.html" title="com.amap.api.navi中的类">SimpleNaviListener</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/AMapNaviViewListener.html#onNaviMapMode-int-">onNaviMapMode(int)</a></span> - 接口 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/AMapNaviViewListener.html" title="com.amap.api.navi中的接口">AMapNaviViewListener</a></dt>
<dd>
<div class="block">导航视角变化回调</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/AMapHudView.html#onNaviRouteNotify-com.amap.api.navi.model.AMapNaviRouteNotifyData-">onNaviRouteNotify(AMapNaviRouteNotifyData)</a></span> - 类 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/AMapHudView.html" title="com.amap.api.navi中的类">AMapHudView</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/AMapNaviListener.html#onNaviRouteNotify-com.amap.api.navi.model.AMapNaviRouteNotifyData-">onNaviRouteNotify(AMapNaviRouteNotifyData)</a></span> - 接口 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/AMapNaviListener.html" title="com.amap.api.navi中的接口">AMapNaviListener</a></dt>
<dd>
<div class="block">导航过程中道路信息通知
 注意：<font color='red'>该接口仅驾车模式有效</font>
 
     导航过程中针对拥堵区域、限行区域、禁行区域、道路封闭等情况的躲避通知。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/SimpleNaviListener.html#onNaviRouteNotify-com.amap.api.navi.model.AMapNaviRouteNotifyData-">onNaviRouteNotify(AMapNaviRouteNotifyData)</a></span> - 类 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/SimpleNaviListener.html" title="com.amap.api.navi中的类">SimpleNaviListener</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/AMapNaviViewListener.html#onNaviSetting--">onNaviSetting()</a></span> - 接口 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/AMapNaviViewListener.html" title="com.amap.api.navi中的接口">AMapNaviViewListener</a></dt>
<dd>
<div class="block">界面右下角设置按钮的点击回调</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/AMapNaviViewListener.html#onNaviTurnClick--">onNaviTurnClick()</a></span> - 接口 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/AMapNaviViewListener.html" title="com.amap.api.navi中的接口">AMapNaviViewListener</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/AMapNaviViewListener.html#onNaviViewLoaded--">onNaviViewLoaded()</a></span> - 接口 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/AMapNaviViewListener.html" title="com.amap.api.navi中的接口">AMapNaviViewListener</a></dt>
<dd>
<div class="block">导航view加载完成回调。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/AMapNaviViewListener.html#onNaviViewShowMode-int-">onNaviViewShowMode(int)</a></span> - 接口 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/AMapNaviViewListener.html" title="com.amap.api.navi中的接口">AMapNaviViewListener</a></dt>
<dd>
<div class="block">导航视图展示模式变化回调</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/AMapNaviViewListener.html#onNextRoadClick--">onNextRoadClick()</a></span> - 接口 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/AMapNaviViewListener.html" title="com.amap.api.navi中的接口">AMapNaviViewListener</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/AMapNaviView.html#onPause--">onPause()</a></span> - 类 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/AMapNaviView.html" title="com.amap.api.navi中的类">AMapNaviView</a></dt>
<dd>
<div class="block">与Activity onPause同步</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/TTSPlayListener.html#onPlayEnd-java.lang.String-">onPlayEnd(String)</a></span> - 接口 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/TTSPlayListener.html" title="com.amap.api.navi中的接口">TTSPlayListener</a></dt>
<dd>
<div class="block">播报结束</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/AMapNaviListener.html#onPlayRing-int-">onPlayRing(int)</a></span> - 接口 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/AMapNaviListener.html" title="com.amap.api.navi中的接口">AMapNaviListener</a></dt>
<dd>
<div class="block">回调各种类型的提示音，类似高德导航"叮".</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/SimpleNaviListener.html#onPlayRing-int-">onPlayRing(int)</a></span> - 类 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/SimpleNaviListener.html" title="com.amap.api.navi中的类">SimpleNaviListener</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/TTSPlayListener.html#onPlayStart-java.lang.String-">onPlayStart(String)</a></span> - 接口 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/TTSPlayListener.html" title="com.amap.api.navi中的接口">TTSPlayListener</a></dt>
<dd>
<div class="block">播报开始</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/INaviInfoCallback.html#onReCalculateRoute-int-">onReCalculateRoute(int)</a></span> - 接口 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/INaviInfoCallback.html" title="com.amap.api.navi中的接口">INaviInfoCallback</a></dt>
<dd>
<div class="block">重新规划的回调</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/AMapNaviListener.html#onReCalculateRouteForTrafficJam--">onReCalculateRouteForTrafficJam()</a></span> - 接口 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/AMapNaviListener.html" title="com.amap.api.navi中的接口">AMapNaviListener</a></dt>
<dd>
<div class="block">驾车导航时，当前方遇到拥堵时准备重新规划路线前的通知回调。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/SimpleNaviListener.html#onReCalculateRouteForTrafficJam--">onReCalculateRouteForTrafficJam()</a></span> - 类 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/SimpleNaviListener.html" title="com.amap.api.navi中的类">SimpleNaviListener</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/AMapNaviListener.html#onReCalculateRouteForYaw--">onReCalculateRouteForYaw()</a></span> - 接口 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/AMapNaviListener.html" title="com.amap.api.navi中的接口">AMapNaviListener</a></dt>
<dd>
<div class="block">偏航后准备重新规划路线前的通知回调。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/SimpleNaviListener.html#onReCalculateRouteForYaw--">onReCalculateRouteForYaw()</a></span> - 类 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/SimpleNaviListener.html" title="com.amap.api.navi中的类">SimpleNaviListener</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/AMapNaviNetWorkProxy.html#onResponseExtParam-java.lang.String-java.lang.String-">onResponseExtParam(String, String)</a></span> - 接口 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/AMapNaviNetWorkProxy.html" title="com.amap.api.navi中的接口">AMapNaviNetWorkProxy</a></dt>
<dd>
<div class="block">设置对应path服务返回的扩展数据</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/AMapNaviRestrictAreaInfoListener.html#onRestrictAreaInfoResult-boolean-java.lang.String-java.lang.String-">onRestrictAreaInfoResult(boolean, String, String)</a></span> - 接口 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/AMapNaviRestrictAreaInfoListener.html" title="com.amap.api.navi中的接口">AMapNaviRestrictAreaInfoListener</a></dt>
<dd>
<div class="block">独立算路成功回调</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/AMapNaviView.html#onResume--">onResume()</a></span> - 类 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/AMapNaviView.html" title="com.amap.api.navi中的类">AMapNaviView</a></dt>
<dd>
<div class="block">与Activity onResume同步</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/AMapNaviView.html#onSaveInstanceState-Bundle-">onSaveInstanceState(Bundle)</a></span> - 类 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/AMapNaviView.html" title="com.amap.api.navi中的类">AMapNaviView</a></dt>
<dd>
<div class="block">与Activity onSaveInstanceState同步</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/INaviInfoCallback.html#onScaleAutoChanged-boolean-">onScaleAutoChanged(boolean)</a></span> - 接口 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/INaviInfoCallback.html" title="com.amap.api.navi中的接口">INaviInfoCallback</a></dt>
<dd>
<div class="block">比例尺智能缩放设置变化回调</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/AMapNaviViewListener.html#onScanViewButtonClick--">onScanViewButtonClick()</a></span> - 接口 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/AMapNaviViewListener.html" title="com.amap.api.navi中的接口">AMapNaviViewListener</a></dt>
<dd>
<div class="block">界面全览按钮的点击回调。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/SimpleNaviListener.html#onSelectRouteId-int-">onSelectRouteId(int)</a></span> - 类 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/SimpleNaviListener.html" title="com.amap.api.navi中的类">SimpleNaviListener</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/AMapNaviListener.html#onServiceAreaUpdate-com.amap.api.navi.model.AMapServiceAreaInfo:A-">onServiceAreaUpdate(AMapServiceAreaInfo[])</a></span> - 接口 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/AMapNaviListener.html" title="com.amap.api.navi中的接口">AMapNaviListener</a></dt>
<dd>
<div class="block">服务区信息回调函数
 注意：<font color='red'>该接口仅驾车模式有效</font></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/SimpleNaviListener.html#onServiceAreaUpdate-com.amap.api.navi.model.AMapServiceAreaInfo:A-">onServiceAreaUpdate(AMapServiceAreaInfo[])</a></span> - 类 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/SimpleNaviListener.html" title="com.amap.api.navi中的类">SimpleNaviListener</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/AMapNaviListener.html#onStartNavi-int-">onStartNavi(int)</a></span> - 接口 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/AMapNaviListener.html" title="com.amap.api.navi中的接口">AMapNaviListener</a></dt>
<dd>
<div class="block">启动导航后的回调函数</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/INaviInfoCallback.html#onStartNavi-int-">onStartNavi(int)</a></span> - 接口 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/INaviInfoCallback.html" title="com.amap.api.navi中的接口">INaviInfoCallback</a></dt>
<dd>
<div class="block">启动导航后的回调函数</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/SimpleNaviListener.html#onStartNavi-int-">onStartNavi(int)</a></span> - 类 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/SimpleNaviListener.html" title="com.amap.api.navi中的类">SimpleNaviListener</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/SimpleNaviListener.html#onStopNavi--">onStopNavi()</a></span> - 类 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/SimpleNaviListener.html" title="com.amap.api.navi中的类">SimpleNaviListener</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/INaviInfoCallback.html#onStopSpeaking--">onStopSpeaking()</a></span> - 接口 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/INaviInfoCallback.html" title="com.amap.api.navi中的接口">INaviInfoCallback</a></dt>
<dd>
<div class="block">停止播报回调
 <br>
 当退出组件导航页，或切换组件的播报模式为静音的时候，会触发该回调。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/INaviInfoCallback.html#onStrategyChanged-int-">onStrategyChanged(int)</a></span> - 接口 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/INaviInfoCallback.html" title="com.amap.api.navi中的接口">INaviInfoCallback</a></dt>
<dd>
<div class="block">切换算路偏好回调</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/SimpleNaviListener.html#onSuggestChangePath-long-long-int-java.lang.String-">onSuggestChangePath(long, long, int, String)</a></span> - 类 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/SimpleNaviListener.html" title="com.amap.api.navi中的类">SimpleNaviListener</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/AMapNaviListener.html#onTrafficStatusUpdate--">onTrafficStatusUpdate()</a></span> - 接口 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/AMapNaviListener.html" title="com.amap.api.navi中的接口">AMapNaviListener</a></dt>
<dd>
<div class="block">当前方路况光柱信息有更新时回调函数。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/SimpleNaviListener.html#onTrafficStatusUpdate--">onTrafficStatusUpdate()</a></span> - 类 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/SimpleNaviListener.html" title="com.amap.api.navi中的类">SimpleNaviListener</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/AimlessModeListener.html#onUpdateAimlessModeElecCameraInfo-com.amap.api.navi.model.AMapNaviTrafficFacilityInfo:A-">onUpdateAimlessModeElecCameraInfo(AMapNaviTrafficFacilityInfo[])</a></span> - 接口 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/AimlessModeListener.html" title="com.amap.api.navi中的接口">AimlessModeListener</a></dt>
<dd>
<div class="block">巡航模式（无路线规划）下, 电子眼信息更新回调。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/AMapNaviDriveListener.html#onUpdateDriveComfort-com.amap.api.navi.model.AMapNaviDriveComfort-">onUpdateDriveComfort(AMapNaviDriveComfort)</a></span> - 接口 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/AMapNaviDriveListener.html" title="com.amap.api.navi中的接口">AMapNaviDriveListener</a></dt>
<dd>
<div class="block">驾车导航道路舒适度回调
 注意：<font color='red'>该接口仅驾车模式有效</font>
 特别注意：<font color='red'>当前接口为收费接口，您如果申请试用或者正式应用都请通过工单系统提交商务合作类工单进行沟通 https://lbs.amap.com/</font></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/SimpleNaviListener.html#onUpdateDriveComfort-com.amap.api.navi.model.AMapNaviDriveComfort-">onUpdateDriveComfort(AMapNaviDriveComfort)</a></span> - 类 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/SimpleNaviListener.html" title="com.amap.api.navi中的类">SimpleNaviListener</a></dt>
<dd>
<div class="block">驾车导航道路舒适度回调
 注意：<font color='red'>该接口仅驾车模式有效</font></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/AMapNaviDriveListener.html#onUpdateDriveEvent-com.amap.api.navi.model.AMapNaviDriveEvent-">onUpdateDriveEvent(AMapNaviDriveEvent)</a></span> - 接口 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/AMapNaviDriveListener.html" title="com.amap.api.navi中的接口">AMapNaviDriveListener</a></dt>
<dd>
<div class="block">驾车导航三急（急加速/急减速/急转弯）事件回调
 注意：<font color='red'>该接口仅驾车模式有效</font>
 特别注意：<font color='red'>当前接口为收费接口，您如果申请试用或者正式应用都请通过工单系统提交商务合作类工单进行沟通 https://lbs.amap.com/</font></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/SimpleNaviListener.html#onUpdateDriveEvent-com.amap.api.navi.model.AMapNaviDriveEvent-">onUpdateDriveEvent(AMapNaviDriveEvent)</a></span> - 类 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/SimpleNaviListener.html" title="com.amap.api.navi中的类">SimpleNaviListener</a></dt>
<dd>
<div class="block">驾车导航三急（急加速/急减速/急转弯）事件回调
 注意：<font color='red'>该接口仅驾车模式有效</font></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/SimpleNaviListener.html#onUpdateGpsSignalStrength-int-">onUpdateGpsSignalStrength(int)</a></span> - 类 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/SimpleNaviListener.html" title="com.amap.api.navi中的类">SimpleNaviListener</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/SimpleNaviListener.html#onUpdateNaviPath--">onUpdateNaviPath()</a></span> - 类 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/SimpleNaviListener.html" title="com.amap.api.navi中的类">SimpleNaviListener</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/AMapNaviDriveListener.html#onUpdateNaviSpeedLimitSection-int-">onUpdateNaviSpeedLimitSection(int)</a></span> - 接口 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/AMapNaviDriveListener.html" title="com.amap.api.navi中的接口">AMapNaviDriveListener</a></dt>
<dd>
<div class="block">路段限速事件更新</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/SimpleNaviListener.html#onUpdateNaviSpeedLimitSection-int-">onUpdateNaviSpeedLimitSection(int)</a></span> - 类 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/SimpleNaviListener.html" title="com.amap.api.navi中的类">SimpleNaviListener</a></dt>
<dd>
<div class="block">路段限速事件更新</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/SimpleNaviListener.html#onUpdateTmcStatus-com.amap.api.navi.model.NaviCongestionInfo-">onUpdateTmcStatus(NaviCongestionInfo)</a></span> - 类 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/SimpleNaviListener.html" title="com.amap.api.navi中的类">SimpleNaviListener</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/AimlessModeListener.html#onUpdateTrafficFacility-com.amap.api.navi.model.AMapNaviTrafficFacilityInfo:A-">onUpdateTrafficFacility(AMapNaviTrafficFacilityInfo[])</a></span> - 接口 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/AimlessModeListener.html" title="com.amap.api.navi中的接口">AimlessModeListener</a></dt>
<dd>
<div class="block">巡航模式（无路线规划）下，道路设施信息更新回调。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/AMapNaviListener.html#OnUpdateTrafficFacility-com.amap.api.navi.model.AMapNaviTrafficFacilityInfo:A-">OnUpdateTrafficFacility(AMapNaviTrafficFacilityInfo[])</a></span> - 接口 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/AMapNaviListener.html" title="com.amap.api.navi中的接口">AMapNaviListener</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span>
<div class="block"><span class="deprecationComment">已过期，建议使用<a href="../com/amap/api/navi/AimlessModeListener.html#onUpdateTrafficFacility-com.amap.api.navi.model.AMapNaviTrafficFacilityInfo:A-"><code>AimlessModeListener.onUpdateTrafficFacility(AMapNaviTrafficFacilityInfo[])</code></a></span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/AMapNaviListener.html#OnUpdateTrafficFacility-com.amap.api.navi.model.AMapNaviTrafficFacilityInfo-">OnUpdateTrafficFacility(AMapNaviTrafficFacilityInfo)</a></span> - 接口 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/AMapNaviListener.html" title="com.amap.api.navi中的接口">AMapNaviListener</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/SimpleNaviListener.html#OnUpdateTrafficFacility-com.amap.api.navi.model.AMapNaviTrafficFacilityInfo:A-">OnUpdateTrafficFacility(AMapNaviTrafficFacilityInfo[])</a></span> - 类 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/SimpleNaviListener.html" title="com.amap.api.navi中的类">SimpleNaviListener</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/SimpleNaviListener.html#OnUpdateTrafficFacility-com.amap.api.navi.model.AMapNaviTrafficFacilityInfo-">OnUpdateTrafficFacility(AMapNaviTrafficFacilityInfo)</a></span> - 类 中的方法com.amap.api.navi.<a href="../com/amap/api/navi/SimpleNaviListener.html" title="com.amap.api.navi中的类">SimpleNaviListener</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/enums/PathPlanningErrCode.html#OUT_OF_SERVICE">OUT_OF_SERVICE</a></span> - 类 中的静态变量com.amap.api.navi.enums.<a href="../com/amap/api/navi/enums/PathPlanningErrCode.html" title="com.amap.api.navi.enums中的类">PathPlanningErrCode</a></dt>
<dd>
<div class="block">使用路径规划服务接口时可能出现该问题，规划点（包括起点、终点、途经点）不在中国陆地范围内</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/enums/IconType.html#OUT_ROUNDABOUT">OUT_ROUNDABOUT</a></span> - 类 中的静态变量com.amap.api.navi.enums.<a href="../com/amap/api/navi/enums/IconType.html" title="com.amap.api.navi.enums中的类">IconType</a></dt>
<dd>
<div class="block">驶出环岛图标（数值：12）</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/enums/PathPlanningErrCode.html#OVER_DIRECTION_RANGE">OVER_DIRECTION_RANGE</a></span> - 类 中的静态变量com.amap.api.navi.enums.<a href="../com/amap/api/navi/enums/PathPlanningErrCode.html" title="com.amap.api.navi.enums中的类">PathPlanningErrCode</a></dt>
<dd>
<div class="block">使用路径规划服务接口时可能出现该问题，路线计算失败，通常是由于道路起点和终点距离过长导致</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/enums/RoadType.html#OVER_HEAD">OVER_HEAD</a></span> - 类 中的静态变量com.amap.api.navi.enums.<a href="../com/amap/api/navi/enums/RoadType.html" title="com.amap.api.navi.enums中的类">RoadType</a></dt>
<dd>
<div class="block">3-高架</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/enums/PathPlanningErrCode.html#OVER_QUOTA">OVER_QUOTA</a></span> - 类 中的静态变量com.amap.api.navi.enums.<a href="../com/amap/api/navi/enums/PathPlanningErrCode.html" title="com.amap.api.navi.enums中的类">PathPlanningErrCode</a></dt>
<dd>
<div class="block">请求超出配额。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/navi/enums/IconType.html#OVERPASS">OVERPASS</a></span> - 类 中的静态变量com.amap.api.navi.enums.<a href="../com/amap/api/navi/enums/IconType.html" title="com.amap.api.navi.enums中的类">IconType</a></dt>
<dd>
<div class="block">通过过街天桥图标（数值：30）骑行、步行专有图标</div>
</dd>
<dt><a href="../com/amap/api/navi/view/OverviewButtonView.html" title="com.amap.api.navi.view中的类"><span class="typeNameLink">OverviewButtonView</span></a> - <a href="../com/amap/api/navi/view/package-summary.html">com.amap.api.navi.view</a>中的类</dt>
<dd>
<div class="block">自定义全览按钮类</div>
</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">J</a>&nbsp;<a href="index-11.html">L</a>&nbsp;<a href="index-12.html">M</a>&nbsp;<a href="index-13.html">N</a>&nbsp;<a href="index-14.html">O</a>&nbsp;<a href="index-15.html">P</a>&nbsp;<a href="index-16.html">R</a>&nbsp;<a href="index-17.html">S</a>&nbsp;<a href="index-18.html">T</a>&nbsp;<a href="index-19.html">U</a>&nbsp;<a href="index-20.html">V</a>&nbsp;<a href="index-21.html">W</a>&nbsp;<a href="index-22.html">Z</a>&nbsp;</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-13.html">上一个字母</a></li>
<li><a href="index-15.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-14.html" target="_top">框架</a></li>
<li><a href="index-14.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
