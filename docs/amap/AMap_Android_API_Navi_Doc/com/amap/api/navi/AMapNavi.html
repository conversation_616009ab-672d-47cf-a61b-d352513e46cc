<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>AMapNavi</title>
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="AMapNavi";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":9,"i21":10,"i22":9,"i23":10,"i24":9,"i25":10,"i26":10,"i27":10,"i28":10,"i29":42,"i30":10,"i31":10,"i32":10,"i33":10,"i34":10,"i35":10,"i36":42,"i37":9,"i38":10,"i39":10,"i40":9,"i41":10,"i42":10,"i43":10,"i44":10,"i45":10,"i46":10,"i47":10,"i48":10,"i49":10,"i50":10,"i51":10,"i52":10,"i53":9,"i54":10,"i55":42,"i56":10,"i57":10,"i58":10,"i59":10,"i60":10,"i61":42,"i62":10,"i63":10,"i64":10,"i65":10,"i66":10,"i67":10,"i68":10,"i69":9,"i70":10,"i71":10,"i72":10,"i73":10,"i74":10,"i75":10,"i76":9,"i77":42,"i78":10,"i79":10,"i80":10,"i81":10,"i82":42,"i83":10,"i84":10,"i85":10,"i86":10,"i87":10,"i88":10,"i89":10,"i90":10,"i91":42,"i92":10};
var tabs = {65535:["t0","所有方法"],1:["t1","静态方法"],2:["t2","实例方法"],8:["t4","具体方法"],32:["t6","已过时的方法"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/AMapNavi.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/amap/api/navi/AMapHudViewListener.html" title="com.amap.api.navi中的接口"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../com/amap/api/navi/AMapNaviDriveListener.html" title="com.amap.api.navi中的接口"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/amap/api/navi/AMapNavi.html" target="_top">框架</a></li>
<li><a href="AMapNavi.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.amap.api.navi</div>
<h2 title="类 AMapNavi" class="title">类 AMapNavi</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.amap.api.navi.AMapNavi</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">AMapNavi</span>
extends java.lang.Object</pre>
<div class="block">导航管理类</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>所有方法</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">静态方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">实例方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">具体方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t6" class="tableTab"><span><a href="javascript:show(32);">已过时的方法</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/navi/AMapNavi.html#addAimlessModeListener-com.amap.api.navi.AimlessModeListener-">addAimlessModeListener</a></span>(<a href="../../../../com/amap/api/navi/AimlessModeListener.html" title="com.amap.api.navi中的接口">AimlessModeListener</a>&nbsp;listener)</code>
<div class="block">注册巡航模式信息回调监听
 注意：<font color='red'>该接口仅驾车模式有效</font></div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/navi/AMapNavi.html#addAMapNaviListener-com.amap.api.navi.AMapNaviListener-">addAMapNaviListener</a></span>(<a href="../../../../com/amap/api/navi/AMapNaviListener.html" title="com.amap.api.navi中的接口">AMapNaviListener</a>&nbsp;naviListener)</code>
<div class="block">添加导航事件回调监听。</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/navi/AMapNavi.html#addParallelRoadListener-com.amap.api.navi.ParallelRoadListener-">addParallelRoadListener</a></span>(<a href="../../../../com/amap/api/navi/ParallelRoadListener.html" title="com.amap.api.navi中的接口">ParallelRoadListener</a>&nbsp;listener)</code>
<div class="block">添加平行路切换监听，包括主辅路切换和高架上下切换
 注意：<font color='red'>该接口仅驾车模式有效</font></div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/navi/AMapNavi.html#addTTSPlayListener-com.amap.api.navi.TTSPlayListener-">addTTSPlayListener</a></span>(<a href="../../../../com/amap/api/navi/TTSPlayListener.html" title="com.amap.api.navi中的接口">TTSPlayListener</a>&nbsp;listener)</code>
<div class="block">添加内置语音播报监听</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/navi/AMapNavi.html#calculateDriveRoute-java.util.List-java.util.List-int-">calculateDriveRoute</a></span>(java.util.List&lt;<a href="../../../../com/amap/api/navi/model/NaviLatLng.html" title="com.amap.api.navi.model中的类">NaviLatLng</a>&gt;&nbsp;to,
                   java.util.List&lt;<a href="../../../../com/amap/api/navi/model/NaviLatLng.html" title="com.amap.api.navi.model中的类">NaviLatLng</a>&gt;&nbsp;wayPoints,
                   int&nbsp;strategy)</code>
<div class="block">计算驾车路径(不带起点，起点默认为当前位置)。</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/navi/AMapNavi.html#calculateDriveRoute-java.util.List-java.util.List-java.util.List-int-">calculateDriveRoute</a></span>(java.util.List&lt;<a href="../../../../com/amap/api/navi/model/NaviLatLng.html" title="com.amap.api.navi.model中的类">NaviLatLng</a>&gt;&nbsp;from,
                   java.util.List&lt;<a href="../../../../com/amap/api/navi/model/NaviLatLng.html" title="com.amap.api.navi.model中的类">NaviLatLng</a>&gt;&nbsp;to,
                   java.util.List&lt;<a href="../../../../com/amap/api/navi/model/NaviLatLng.html" title="com.amap.api.navi.model中的类">NaviLatLng</a>&gt;&nbsp;wayPoints,
                   int&nbsp;strategy)</code>
<div class="block">计算驾车路径(包含起点)。</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/navi/AMapNavi.html#calculateDriveRoute-com.amap.api.navi.model.NaviPoi-com.amap.api.navi.model.NaviPoi-java.util.List-int-">calculateDriveRoute</a></span>(<a href="../../../../com/amap/api/navi/model/NaviPoi.html" title="com.amap.api.navi.model中的类">NaviPoi</a>&nbsp;fromPoi,
                   <a href="../../../../com/amap/api/navi/model/NaviPoi.html" title="com.amap.api.navi.model中的类">NaviPoi</a>&nbsp;toPoi,
                   java.util.List&lt;<a href="../../../../com/amap/api/navi/model/NaviPoi.html" title="com.amap.api.navi.model中的类">NaviPoi</a>&gt;&nbsp;wayPoints,
                   int&nbsp;strategy)</code>
<div class="block">驾车POI算路
 
 该方法可以避免到达点问题导致的绕路，地铁等多出口类POI，也会导航到多个出口。</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/navi/AMapNavi.html#calculateDriveRoute-java.lang.String-java.util.List-int-">calculateDriveRoute</a></span>(java.lang.String&nbsp;toPoiId,
                   java.util.List&lt;java.lang.String&gt;&nbsp;wayPointIds,
                   int&nbsp;strategy)</code>
<div class="block">计算驾车路径(不带起点，起点默认为当前位置)。</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/navi/AMapNavi.html#calculateDriveRoute-java.lang.String-java.lang.String-java.util.List-int-">calculateDriveRoute</a></span>(java.lang.String&nbsp;fromPoiId,
                   java.lang.String&nbsp;toPoiId,
                   java.util.List&lt;java.lang.String&gt;&nbsp;wayPoints,
                   int&nbsp;strategy)</code>
<div class="block">计算驾车路径(包含起点)。</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/navi/AMapNavi.html#calculateEleBikeRoute-com.amap.api.navi.model.NaviLatLng-">calculateEleBikeRoute</a></span>(<a href="../../../../com/amap/api/navi/model/NaviLatLng.html" title="com.amap.api.navi.model中的类">NaviLatLng</a>&nbsp;to)</code>
<div class="block">骑行（电动车）经纬度算路 (不带起点，默认为当前位置)。</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/navi/AMapNavi.html#calculateEleBikeRoute-com.amap.api.navi.model.NaviLatLng-com.amap.api.navi.model.NaviLatLng-">calculateEleBikeRoute</a></span>(<a href="../../../../com/amap/api/navi/model/NaviLatLng.html" title="com.amap.api.navi.model中的类">NaviLatLng</a>&nbsp;from,
                     <a href="../../../../com/amap/api/navi/model/NaviLatLng.html" title="com.amap.api.navi.model中的类">NaviLatLng</a>&nbsp;to)</code>
<div class="block">骑行（电动车）经纬度算路</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/navi/AMapNavi.html#calculateEleBikeRoute-com.amap.api.navi.model.NaviPoi-com.amap.api.navi.model.NaviPoi-com.amap.api.navi.enums.TravelStrategy-">calculateEleBikeRoute</a></span>(<a href="../../../../com/amap/api/navi/model/NaviPoi.html" title="com.amap.api.navi.model中的类">NaviPoi</a>&nbsp;fromPoi,
                     <a href="../../../../com/amap/api/navi/model/NaviPoi.html" title="com.amap.api.navi.model中的类">NaviPoi</a>&nbsp;toPoi,
                     <a href="../../../../com/amap/api/navi/enums/TravelStrategy.html" title="com.amap.api.navi.enums中的枚举">TravelStrategy</a>&nbsp;strategy)</code>
<div class="block">骑行（电动车）POI算路</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/navi/AMapNavi.html#calculateRideRoute-com.amap.api.navi.model.NaviLatLng-">calculateRideRoute</a></span>(<a href="../../../../com/amap/api/navi/model/NaviLatLng.html" title="com.amap.api.navi.model中的类">NaviLatLng</a>&nbsp;to)</code>
<div class="block">骑行（自行车）经纬度算路 (不带起点，默认为当前位置)。</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/navi/AMapNavi.html#calculateRideRoute-com.amap.api.navi.model.NaviLatLng-com.amap.api.navi.model.NaviLatLng-">calculateRideRoute</a></span>(<a href="../../../../com/amap/api/navi/model/NaviLatLng.html" title="com.amap.api.navi.model中的类">NaviLatLng</a>&nbsp;from,
                  <a href="../../../../com/amap/api/navi/model/NaviLatLng.html" title="com.amap.api.navi.model中的类">NaviLatLng</a>&nbsp;to)</code>
<div class="block">骑行（自行车）经纬度算路 (包含起点)。</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/navi/AMapNavi.html#calculateRideRoute-com.amap.api.navi.model.NaviPoi-java.util.List-com.amap.api.navi.model.NaviPoi-com.amap.api.navi.enums.TravelStrategy-">calculateRideRoute</a></span>(<a href="../../../../com/amap/api/navi/model/NaviPoi.html" title="com.amap.api.navi.model中的类">NaviPoi</a>&nbsp;fromPoi,
                  java.util.List&lt;<a href="../../../../com/amap/api/navi/model/NaviPoi.html" title="com.amap.api.navi.model中的类">NaviPoi</a>&gt;&nbsp;wayPoints,
                  <a href="../../../../com/amap/api/navi/model/NaviPoi.html" title="com.amap.api.navi.model中的类">NaviPoi</a>&nbsp;toPoi,
                  <a href="../../../../com/amap/api/navi/enums/TravelStrategy.html" title="com.amap.api.navi.enums中的枚举">TravelStrategy</a>&nbsp;strategy)</code>
<div class="block">骑行（自行车）POI算路 特别注意：当前接口为收费接口，您如果申请试用或者正式应用都请通过工单系统提交商务合作类工单进行沟通 https://lbs.amap.com/</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/navi/AMapNavi.html#calculateRideRoute-com.amap.api.navi.model.NaviPoi-com.amap.api.navi.model.NaviPoi-com.amap.api.navi.enums.TravelStrategy-">calculateRideRoute</a></span>(<a href="../../../../com/amap/api/navi/model/NaviPoi.html" title="com.amap.api.navi.model中的类">NaviPoi</a>&nbsp;fromPoi,
                  <a href="../../../../com/amap/api/navi/model/NaviPoi.html" title="com.amap.api.navi.model中的类">NaviPoi</a>&nbsp;toPoi,
                  <a href="../../../../com/amap/api/navi/enums/TravelStrategy.html" title="com.amap.api.navi.enums中的枚举">TravelStrategy</a>&nbsp;strategy)</code>
<div class="block">骑行（自行车）POI算路</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/navi/AMapNavi.html#calculateWalkRoute-com.amap.api.navi.model.NaviLatLng-">calculateWalkRoute</a></span>(<a href="../../../../com/amap/api/navi/model/NaviLatLng.html" title="com.amap.api.navi.model中的类">NaviLatLng</a>&nbsp;to)</code>
<div class="block">计算步行路径(不带起点，默认为当前位置)。</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/navi/AMapNavi.html#calculateWalkRoute-com.amap.api.navi.model.NaviLatLng-com.amap.api.navi.model.NaviLatLng-">calculateWalkRoute</a></span>(<a href="../../../../com/amap/api/navi/model/NaviLatLng.html" title="com.amap.api.navi.model中的类">NaviLatLng</a>&nbsp;from,
                  <a href="../../../../com/amap/api/navi/model/NaviLatLng.html" title="com.amap.api.navi.model中的类">NaviLatLng</a>&nbsp;to)</code>
<div class="block">计算步行路径(包含起点)。</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/navi/AMapNavi.html#calculateWalkRoute-com.amap.api.navi.model.NaviPoi-java.util.List-com.amap.api.navi.model.NaviPoi-com.amap.api.navi.enums.TravelStrategy-">calculateWalkRoute</a></span>(<a href="../../../../com/amap/api/navi/model/NaviPoi.html" title="com.amap.api.navi.model中的类">NaviPoi</a>&nbsp;fromPoi,
                  java.util.List&lt;<a href="../../../../com/amap/api/navi/model/NaviPoi.html" title="com.amap.api.navi.model中的类">NaviPoi</a>&gt;&nbsp;wayPoints,
                  <a href="../../../../com/amap/api/navi/model/NaviPoi.html" title="com.amap.api.navi.model中的类">NaviPoi</a>&nbsp;toPoi,
                  <a href="../../../../com/amap/api/navi/enums/TravelStrategy.html" title="com.amap.api.navi.enums中的枚举">TravelStrategy</a>&nbsp;strategy)</code>
<div class="block">步行POI算路 特别注意：当前接口为收费接口，您如果申请试用或者正式应用都请通过工单系统提交商务合作类工单进行沟通 https://lbs.amap.com/。</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/navi/AMapNavi.html#calculateWalkRoute-com.amap.api.navi.model.NaviPoi-com.amap.api.navi.model.NaviPoi-com.amap.api.navi.enums.TravelStrategy-">calculateWalkRoute</a></span>(<a href="../../../../com/amap/api/navi/model/NaviPoi.html" title="com.amap.api.navi.model中的类">NaviPoi</a>&nbsp;fromPoi,
                  <a href="../../../../com/amap/api/navi/model/NaviPoi.html" title="com.amap.api.navi.model中的类">NaviPoi</a>&nbsp;toPoi,
                  <a href="../../../../com/amap/api/navi/enums/TravelStrategy.html" title="com.amap.api.navi.enums中的枚举">TravelStrategy</a>&nbsp;strategy)</code>
<div class="block">步行POI算路</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/navi/AMapNavi.html#destroy--">destroy</a></span>()</code>
<div class="block">释放导航对象资源
 
 退出时调用此接口释放导航资源，在调用此接口后不能再调用AMapNavi类里的其它接口。</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/navi/AMapNavi.html#getControlMusicVolumeMode--">getControlMusicVolumeMode</a></span>()</code>
<div class="block">获取导航语音播报混合音状态</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/navi/AMapNavi.html#getDeviceId-Context-">getDeviceId</a></span>(Context&nbsp;context)</code>
<div class="block">获取设备唯一ID，使用导航SDK的时候建议使用这个作为设备唯一ID</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/navi/AMapNavi.html#getEngineType--">getEngineType</a></span>()</code>
<div class="block">获得导航引擎类型</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/amap/api/navi/AMapNavi.html" title="com.amap.api.navi中的类">AMapNavi</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/navi/AMapNavi.html#getInstance-Context-">getInstance</a></span>(Context&nbsp;context)</code>
<div class="block">创建一个AMapNavi导航对象。</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/navi/AMapNavi.html#getIsNaviTravelView--">getIsNaviTravelView</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/navi/AMapNavi.html#getIsUseExtraGPSData--">getIsUseExtraGPSData</a></span>()</code>
<div class="block">获取是否使用外部定位数据</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/navi/AMapNavi.html#getIsUseInnerVoice--">getIsUseInnerVoice</a></span>()</code>
<div class="block">获取是否使用内部语音播报</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/navi/AMapNavi.html#getListenToVoiceDuringCall--">getListenToVoiceDuringCall</a></span>()</code>
<div class="block">获取当前通话过程中是否进行导航播报(只在使用内置语音时生效)</div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../com/amap/api/navi/model/AMapNaviGuide.html" title="com.amap.api.navi.model中的类">AMapNaviGuide</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/navi/AMapNavi.html#getNaviGuideList--">getNaviGuideList</a></span>()</code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;
<div class="block"><span class="deprecationComment">请使用 <a href="../../../../com/amap/api/navi/model/AMapNaviPath.html#getNaviGuideList--"><code>AMapNaviPath.getNaviGuideList()</code></a></span></div>
</div>
</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code><a href="../../../../com/amap/api/navi/model/AMapNaviPath.html" title="com.amap.api.navi.model中的类">AMapNaviPath</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/navi/AMapNavi.html#getNaviPath--">getNaviPath</a></span>()</code>
<div class="block">获取当前规划的路线方案（路线结果），驾车、骑行与步行共用这一个对象</div>
</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code>java.util.HashMap&lt;java.lang.Integer,<a href="../../../../com/amap/api/navi/model/AMapNaviPath.html" title="com.amap.api.navi.model中的类">AMapNaviPath</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/navi/AMapNavi.html#getNaviPaths--">getNaviPaths</a></span>()</code>
<div class="block">获取计算的多条路径</div>
</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code><a href="../../../../com/amap/api/navi/NaviSetting.html" title="com.amap.api.navi中的类">NaviSetting</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/navi/AMapNavi.html#getNaviSetting--">getNaviSetting</a></span>()</code>
<div class="block">获取导航配置类</div>
</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/navi/AMapNavi.html#getNaviType--">getNaviType</a></span>()</code>
<div class="block">获取导航位置变化驱动类型</div>
</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/navi/AMapNavi.html#getRestrictareaInfo-com.amap.api.navi.model.AMapNaviPath-com.amap.api.navi.AMapNaviRestrictAreaInfoListener-">getRestrictareaInfo</a></span>(<a href="../../../../com/amap/api/navi/model/AMapNaviPath.html" title="com.amap.api.navi.model中的类">AMapNaviPath</a>&nbsp;path,
                   <a href="../../../../com/amap/api/navi/AMapNaviRestrictAreaInfoListener.html" title="com.amap.api.navi中的接口">AMapNaviRestrictAreaInfoListener</a>&nbsp;observer)</code>
<div class="block">获取现行信息数据</div>
</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/navi/AMapNavi.html#getServiceAreaDetailsEnable--">getServiceAreaDetailsEnable</a></span>()</code>
<div class="block">是否开启服务区详情开关</div>
</td>
</tr>
<tr id="i36" class="altColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../com/amap/api/navi/model/AMapTrafficStatus.html" title="com.amap.api.navi.model中的类">AMapTrafficStatus</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/navi/AMapNavi.html#getTrafficStatuses-int-int-">getTrafficStatuses</a></span>(int&nbsp;startPos,
                  int&nbsp;distance)</code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;
<div class="block"><span class="deprecationComment">已废弃，请使用 <a href="../../../../com/amap/api/navi/model/AMapNaviPath.html#getTrafficStatuses--"><code>AMapNaviPath.getTrafficStatuses()</code></a></span></div>
</div>
</td>
</tr>
<tr id="i37" class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/navi/AMapNavi.html#getVersion--">getVersion</a></span>()</code>
<div class="block">获取版本号</div>
</td>
</tr>
<tr id="i38" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/navi/AMapNavi.html#independentCalculateRoute-com.amap.api.navi.model.NaviPoi-com.amap.api.navi.model.NaviPoi-java.util.List-int-int-com.amap.api.navi.AMapNaviIndependentRouteListener-">independentCalculateRoute</a></span>(<a href="../../../../com/amap/api/navi/model/NaviPoi.html" title="com.amap.api.navi.model中的类">NaviPoi</a>&nbsp;fromPoi,
                         <a href="../../../../com/amap/api/navi/model/NaviPoi.html" title="com.amap.api.navi.model中的类">NaviPoi</a>&nbsp;toPoi,
                         java.util.List&lt;<a href="../../../../com/amap/api/navi/model/NaviPoi.html" title="com.amap.api.navi.model中的类">NaviPoi</a>&gt;&nbsp;wayPoints,
                         int&nbsp;strategy,
                         int&nbsp;transportType,
                         <a href="../../../../com/amap/api/navi/AMapNaviIndependentRouteListener.html" title="com.amap.api.navi中的接口">AMapNaviIndependentRouteListener</a>&nbsp;observer)</code>
<div class="block">POI独立算路 SDK9.4.0版本开始支持骑步行途径点算路，骑步行添加途径点算路为付费接口。</div>
</td>
</tr>
<tr id="i39" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/navi/AMapNavi.html#isGpsReady--">isGpsReady</a></span>()</code>
<div class="block">获取手机卫星定位是否准备就绪</div>
</td>
</tr>
<tr id="i40" class="altColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/navi/AMapNavi.html#isTtsPlaying--">isTtsPlaying</a></span>()</code>
<div class="block">当前SDK内置语音的是否正在播报
 
 注意：正在播报的时候请不要使用外部播放器播报语音，会导致与导航内置的播报冲突。</div>
</td>
</tr>
<tr id="i41" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/navi/AMapNavi.html#pauseNavi--">pauseNavi</a></span>()</code>
<div class="block">暂停导航，仅支持模拟导航。</div>
</td>
</tr>
<tr id="i42" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/navi/AMapNavi.html#playTTS-java.lang.String-boolean-">playTTS</a></span>(java.lang.String&nbsp;tts,
       boolean&nbsp;forcePlay)</code>
<div class="block">播放自定义文字，注意如果当前正在播放导航语音，可能导致播放失败，只在内置语音下生效</div>
</td>
</tr>
<tr id="i43" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/navi/AMapNavi.html#readNaviInfo--">readNaviInfo</a></span>()</code>
<div class="block">触发一次导航播报提示。</div>
</td>
</tr>
<tr id="i44" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/navi/AMapNavi.html#readTrafficInfo-int-">readTrafficInfo</a></span>(int&nbsp;frontDistance)</code>
<div class="block">触发一次前方路况播报。</div>
</td>
</tr>
<tr id="i45" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/navi/AMapNavi.html#reCalculateRoute-int-">reCalculateRoute</a></span>(int&nbsp;strategy)</code>
<div class="block">导航过程中重新规划路线（起点为当前位置，途经点、终点位置不变）</div>
</td>
</tr>
<tr id="i46" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/navi/AMapNavi.html#removeAimlessModeListener-com.amap.api.navi.AimlessModeListener-">removeAimlessModeListener</a></span>(<a href="../../../../com/amap/api/navi/AimlessModeListener.html" title="com.amap.api.navi中的接口">AimlessModeListener</a>&nbsp;listener)</code>
<div class="block">移除巡航模式信息回调监听
 注意：<font color='red'>该接口仅驾车模式有效</font></div>
</td>
</tr>
<tr id="i47" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/navi/AMapNavi.html#removeAMapNaviListener-com.amap.api.navi.AMapNaviListener-">removeAMapNaviListener</a></span>(<a href="../../../../com/amap/api/navi/AMapNaviListener.html" title="com.amap.api.navi中的接口">AMapNaviListener</a>&nbsp;naviListener)</code>
<div class="block">移除导航对象的监听。</div>
</td>
</tr>
<tr id="i48" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/navi/AMapNavi.html#removeParallelRoadListener-com.amap.api.navi.ParallelRoadListener-">removeParallelRoadListener</a></span>(<a href="../../../../com/amap/api/navi/ParallelRoadListener.html" title="com.amap.api.navi中的接口">ParallelRoadListener</a>&nbsp;listener)</code>
<div class="block">移除平行路切换监听，包括主辅路切换和高架上下切换
 注意：<font color='red'>该接口仅驾车模式有效</font></div>
</td>
</tr>
<tr id="i49" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/navi/AMapNavi.html#removeTTSPlayListener-com.amap.api.navi.TTSPlayListener-">removeTTSPlayListener</a></span>(<a href="../../../../com/amap/api/navi/TTSPlayListener.html" title="com.amap.api.navi中的接口">TTSPlayListener</a>&nbsp;listener)</code>
<div class="block">移除内置语音播报监听</div>
</td>
</tr>
<tr id="i50" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/navi/AMapNavi.html#resumeNavi--">resumeNavi</a></span>()</code>
<div class="block">继续导航，仅支持模拟导航。</div>
</td>
</tr>
<tr id="i51" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/navi/AMapNavi.html#selectRouteId-int-">selectRouteId</a></span>(int&nbsp;id)</code>
<div class="block">导航前选主路线
 注意：<font color='red'>该接口仅驾车模式有效</font></div>
</td>
</tr>
<tr id="i52" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/navi/AMapNavi.html#setAMapNaviOnlineCarHailingType-com.amap.api.navi.enums.AMapNaviOnlineCarHailingType-">setAMapNaviOnlineCarHailingType</a></span>(<a href="../../../../com/amap/api/navi/enums/AMapNaviOnlineCarHailingType.html" title="com.amap.api.navi.enums中的枚举">AMapNaviOnlineCarHailingType</a>&nbsp;type)</code>
<div class="block">设置网约车导航模式，包括非网约车模式，网约车接驾模式、网约车送驾模式，默认为非网约车模式
 注意：<font color='red'>该接口仅驾车模式有效</font></div>
</td>
</tr>
<tr id="i53" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/navi/AMapNavi.html#setApiKey-Context-java.lang.String-">setApiKey</a></span>(Context&nbsp;context,
         java.lang.String&nbsp;apiKey)</code>
<div class="block">手动设置key,这是个静态方法，需要在一切导航接口前调用，建议在applicaiton中调用</div>
</td>
</tr>
<tr id="i54" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/navi/AMapNavi.html#setBroadcastMode-int-">setBroadcastMode</a></span>(int&nbsp;mode)</code>
<div class="block">设置语音播报模式为"专家模式"还是"新手模式"【驾车有效】,导航中设置需要下次算路才生效，如偏航或刷新重算.</div>
</td>
</tr>
<tr id="i55" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/navi/AMapNavi.html#setCameraInfoUpdateEnabled-boolean-">setCameraInfoUpdateEnabled</a></span>(boolean&nbsp;enable)</code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;
<div class="block"><span class="deprecationComment">已废弃，新引擎机制不再支持单播报模块控制</span></div>
</div>
</td>
</tr>
<tr id="i56" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/navi/AMapNavi.html#setCarInfo-com.amap.api.navi.model.AMapCarInfo-">setCarInfo</a></span>(<a href="../../../../com/amap/api/navi/model/AMapCarInfo.html" title="com.amap.api.navi.model中的类">AMapCarInfo</a>&nbsp;carInfo)</code>
<div class="block">设置车辆信息(包括车型，车牌，车高，车重等)，路径规划时会躲避车辆限行区域和路线。</div>
</td>
</tr>
<tr id="i57" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/navi/AMapNavi.html#setConnectionTimeout-int-">setConnectionTimeout</a></span>(int&nbsp;connectionTimeOut)</code>
<div class="block">设置建立连接超时时间，单位毫秒级，最低3000，默认10000.</div>
</td>
</tr>
<tr id="i58" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/navi/AMapNavi.html#setControlMusicVolumeMode-int-">setControlMusicVolumeMode</a></span>(int&nbsp;controlMusicVolumeMode)</code>
<div class="block">设置导航播报时压低音乐、暂停音乐，只有在使用内置语音时生效（Android8.0以下系统不支持暂停音乐，设置暂停音乐无效）</div>
</td>
</tr>
<tr id="i59" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/navi/AMapNavi.html#setEmulatorNaviSpeed-int-">setEmulatorNaviSpeed</a></span>(int&nbsp;speed)</code>
<div class="block">设置模拟导航的速度。</div>
</td>
</tr>
<tr id="i60" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/navi/AMapNavi.html#setExtraGPSData-int-Location-">setExtraGPSData</a></span>(int&nbsp;type,
               Location&nbsp;location)</code>
<div class="block">此方法用于设置外部定位数据，并使用外部定位数据进行导航
 使用此方法前需要先调用<a href="../../../../com/amap/api/navi/AMapNavi.html#setIsUseExtraGPSData-boolean-"><code>AMapNavi.setIsUseExtraGPSData(boolean)</code></a>将开关打开.</div>
</td>
</tr>
<tr id="i61" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/navi/AMapNavi.html#setGpsWeakDetecedInterval-long-">setGpsWeakDetecedInterval</a></span>(long&nbsp;delayTime)</code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;
<div class="block"><span class="deprecationComment">此接口已失效</span></div>
</div>
</td>
</tr>
<tr id="i62" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/navi/AMapNavi.html#setInnerOptRecordEnable-java.lang.String-">setInnerOptRecordEnable</a></span>(java.lang.String&nbsp;key)</code>
<div class="block">【二环专用】设置是否启用OPT日志记录
  * @param key</div>
</td>
</tr>
<tr id="i63" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/navi/AMapNavi.html#setIsNaviTravelView-boolean-">setIsNaviTravelView</a></span>(boolean&nbsp;isTravel)</code>
<div class="block">设置是否为骑步行视图</div>
</td>
</tr>
<tr id="i64" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/navi/AMapNavi.html#setIsOpenTrafficLight-java.lang.String-">setIsOpenTrafficLight</a></span>(java.lang.String&nbsp;key)</code>
<div class="block">设置是否显示红绿灯倒计时
  * @param key</div>
</td>
</tr>
<tr id="i65" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/navi/AMapNavi.html#setIsUseExtraGPSData-boolean-">setIsUseExtraGPSData</a></span>(boolean&nbsp;isUseExtraData)</code>
<div class="block">设置是否使用外部定位数据.</div>
</td>
</tr>
<tr id="i66" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/navi/AMapNavi.html#setListenToVoiceDuringCall-boolean-">setListenToVoiceDuringCall</a></span>(boolean&nbsp;isListenToVoiceDuringCall)</code>
<div class="block">设置在通话过程中是否进行导航播报，只有在使用内置语音时生效</div>
</td>
</tr>
<tr id="i67" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/navi/AMapNavi.html#setMultipleRouteNaviMode-boolean-">setMultipleRouteNaviMode</a></span>(boolean&nbsp;multipleRouteNaviMode)</code>
<div class="block">设置多路线导航模式(导航中拥有若干条备选路线供用户选择), 或单路线导航模式(默认模式).</div>
</td>
</tr>
<tr id="i68" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/navi/AMapNavi.html#setNaviNetworkProxy-com.amap.api.navi.AMapNaviNetWorkProxy-">setNaviNetworkProxy</a></span>(<a href="../../../../com/amap/api/navi/AMapNaviNetWorkProxy.html" title="com.amap.api.navi中的接口">AMapNaviNetWorkProxy</a>&nbsp;proxy)</code>
<div class="block">设置导航外部网络代理类</div>
</td>
</tr>
<tr id="i69" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/navi/AMapNavi.html#setNetWorkingProtocol-com.amap.api.navi.enums.NetWorkingProtocol-">setNetWorkingProtocol</a></span>(<a href="../../../../com/amap/api/navi/enums/NetWorkingProtocol.html" title="com.amap.api.navi.enums中的枚举">NetWorkingProtocol</a>&nbsp;protocol)</code>
<div class="block">设置网络请求协议
 
 注意，该接口仅控制导航业务相关的网络请求，不影响定位、地图sdk内部的请求。</div>
</td>
</tr>
<tr id="i70" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/navi/AMapNavi.html#setServiceAreaDetailsEnable-boolean-">setServiceAreaDetailsEnable</a></span>(boolean&nbsp;enable)</code>
<div class="block">设置服务区详情是否打开，打开后组件会显示服务区UI，API会返回服务区类型信息</div>
</td>
</tr>
<tr id="i71" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/navi/AMapNavi.html#setSoTimeout-int-">setSoTimeout</a></span>(int&nbsp;soTimeOut)</code>
<div class="block">设置服务器返回超时时间，单位毫秒级，最低3000，默认10000.</div>
</td>
</tr>
<tr id="i72" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/navi/AMapNavi.html#setTrafficInfoUpdateEnabled-boolean-">setTrafficInfoUpdateEnabled</a></span>(boolean&nbsp;enable)</code>
<div class="block">设置是否打开移动交通台功能
 <p/>
 默认是开启的，打开此功能后将有整体路况概览与前方路况播报功能，打开此功能后TBT会基本1分钟请求
 一次前方路况（此时间间隔不是固定的）。</div>
</td>
</tr>
<tr id="i73" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/navi/AMapNavi.html#setTrafficStatusUpdateEnabled-boolean-">setTrafficStatusUpdateEnabled</a></span>(boolean&nbsp;enable)</code>
<div class="block">设置交通状态是否更新
 <p/>
 需要动态交通信息相关功能时需要调用此接口打开交通信息功能，默认是开启的，
 打开后会每1分钟请求一次新的交通信息来更新交通信息。</div>
</td>
</tr>
<tr id="i74" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/navi/AMapNavi.html#setTravelInfo-com.amap.api.navi.model.AMapTravelInfo-">setTravelInfo</a></span>(<a href="../../../../com/amap/api/navi/model/AMapTravelInfo.html" title="com.amap.api.navi.model中的类">AMapTravelInfo</a>&nbsp;travelInfo)</code>
<div class="block">设置骑步行信息（骑行、步行）。</div>
</td>
</tr>
<tr id="i75" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/navi/AMapNavi.html#setTruckMultipleRouteNaviMode-boolean-">setTruckMultipleRouteNaviMode</a></span>(boolean&nbsp;truckMultipleRouteNaviMode)</code>
<div class="block">设置货车多路线导航模式(导航中拥有若干条备选路线供用户选择)， 或单路线导航模式（默认模式）

 注意：此方法仅限于在开始导航前调用有效，步行与骑行导航无效，以下情况不会出现多备选路线：
 模拟导航、路线存在途经点、路线长度超过80KM。</div>
</td>
</tr>
<tr id="i76" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/navi/AMapNavi.html#setTtsPlaying-boolean-">setTtsPlaying</a></span>(boolean&nbsp;playing)</code>
<div class="block">设置外部播放器是否正在播报
 <br>
 如果正在播报语音，请设置为true，如果没有在播报语音，请设置false；如一直设置true，SDK内部会认为外界一直在播报，
 那么<a href="../../../../com/amap/api/navi/AMapNaviListener.html#onGetNavigationText-int-java.lang.String-"><code>AMapNaviListener.onGetNavigationText(int, String)</code></a>，就会一直不触发，导致无文字吐出;
 如一直设置false，文字吐出的频率可能会过快，会出现语句打断情况，所以请根据实际情况设置。</div>
</td>
</tr>
<tr id="i77" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/navi/AMapNavi.html#setUseInnerVoice-boolean-">setUseInnerVoice</a></span>(boolean&nbsp;isUseInnerVoice)</code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;&nbsp;</div>
</td>
</tr>
<tr id="i78" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/navi/AMapNavi.html#setUseInnerVoice-boolean-boolean-">setUseInnerVoice</a></span>(boolean&nbsp;isUseInnerVoice,
                boolean&nbsp;isCallBackText)</code>
<div class="block">设置使用内部语音播报</div>
</td>
</tr>
<tr id="i79" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/navi/AMapNavi.html#startAimlessMode-int-">startAimlessMode</a></span>(int&nbsp;aimlessMode)</code>
<div class="block">设置在巡航模式（无路线规划）的状态下，智能播报的类型
 <br>
 用户一旦设置，在巡航模式（无路线规划）的状态下，会获得以下回调:
 <br>
 <a href="../../../../com/amap/api/navi/AimlessModeListener.html#onUpdateTrafficFacility-com.amap.api.navi.model.AMapNaviTrafficFacilityInfo:A-"><code>AimlessModeListener.onUpdateTrafficFacility(AMapNaviTrafficFacilityInfo[])</code></a>
 可以用于获得道路设施（包括转弯提示等）
 <br>
 <a href="../../../../com/amap/api/navi/AimlessModeListener.html#onUpdateAimlessModeElecCameraInfo-com.amap.api.navi.model.AMapNaviTrafficFacilityInfo:A-"><code>AimlessModeListener.onUpdateAimlessModeElecCameraInfo(AMapNaviTrafficFacilityInfo[])</code></a>
 可以用于获得电子眼信息更新
 <br>
 <a href="../../../../com/amap/api/navi/AimlessModeListener.html#updateAimlessModeCongestionInfo-com.amap.api.navi.model.AimLessModeCongestionInfo-"><code>AimlessModeListener.updateAimlessModeCongestionInfo(AimLessModeCongestionInfo)</code></a>
 可以用于获得周边道路拥堵信息
 <br>
 <a href="../../../../com/amap/api/navi/AimlessModeListener.html#updateAimlessModeStatistics-com.amap.api.navi.model.AimLessModeStat-"><code>AimlessModeListener.updateAimlessModeStatistics(AimLessModeStat)</code></a>
 可以用于巡航模式的统计信息，巡航开启时间，巡航移动距离
 <br>
 <a href="../../../../com/amap/api/navi/AMapNaviListener.html#onGetNavigationText-int-java.lang.String-"><code>AMapNaviListener.onGetNavigationText(int, String)</code></a>
 可以用于获得巡航语音播报文字
 <br>
 注意：<font color='red'>该接口仅驾车模式有效</font></div>
</td>
</tr>
<tr id="i80" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/navi/AMapNavi.html#startGPS--">startGPS</a></span>()</code>
<div class="block">启动手机卫星定位。</div>
</td>
</tr>
<tr id="i81" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/navi/AMapNavi.html#startGPS-long-">startGPS</a></span>(long&nbsp;interval)</code>
<div class="block">启动手机卫星定位, 带定位时间间隔参数。</div>
</td>
</tr>
<tr id="i82" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/navi/AMapNavi.html#startGPS-long-int-">startGPS</a></span>(long&nbsp;time,
        int&nbsp;dis)</code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;
<div class="block"><span class="deprecationComment">请使用 <a href="../../../../com/amap/api/navi/AMapNavi.html#startGPS-long-"><code>AMapNavi.startGPS(long)</code></a></span></div>
</div>
</td>
</tr>
<tr id="i83" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/navi/AMapNavi.html#startNavi-int-">startNavi</a></span>(int&nbsp;naviType)</code>
<div class="block">开始导航。</div>
</td>
</tr>
<tr id="i84" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/navi/AMapNavi.html#startNaviWithPath-int-com.amap.api.navi.model.AMapNaviPathGroup-">startNaviWithPath</a></span>(int&nbsp;naviType,
                 <a href="../../../../com/amap/api/navi/model/AMapNaviPathGroup.html" title="com.amap.api.navi.model中的类">AMapNaviPathGroup</a>&nbsp;pathGroup)</code>
<div class="block">使用指定路线开始导航</div>
</td>
</tr>
<tr id="i85" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/navi/AMapNavi.html#startSpeak--">startSpeak</a></span>()</code>
<div class="block">开始内置语音播报，只有在使用内置语音的情况下才有效</div>
</td>
</tr>
<tr id="i86" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/navi/AMapNavi.html#stopAimlessMode--">stopAimlessMode</a></span>()</code>
<div class="block">停止巡航模式
 注意：<font color='red'>该接口仅驾车模式有效</font></div>
</td>
</tr>
<tr id="i87" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/navi/AMapNavi.html#stopGPS--">stopGPS</a></span>()</code>
<div class="block">停止手机卫星定位。</div>
</td>
</tr>
<tr id="i88" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/navi/AMapNavi.html#stopNavi--">stopNavi</a></span>()</code>
<div class="block">停止导航，包含实时导航和模拟导航。</div>
</td>
</tr>
<tr id="i89" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/navi/AMapNavi.html#stopSpeak--">stopSpeak</a></span>()</code>
<div class="block">停止内置语音播报，只有在使用内置语音的情况下有效
 注意：7.1.0版本以后，调用该接口，会停止播放导航语音，但是仍然可以播放自定义语音</div>
</td>
</tr>
<tr id="i90" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/navi/AMapNavi.html#strategyConvert-boolean-boolean-boolean-boolean-boolean-">strategyConvert</a></span>(boolean&nbsp;avoidCongestion,
               boolean&nbsp;avoidHighway,
               boolean&nbsp;avoidCost,
               boolean&nbsp;prioritiseHighway,
               boolean&nbsp;multipleRoute)</code>
<div class="block">进行算路策略转换，将传入的特定规则转换成PathPlanningStrategy的枚举值。</div>
</td>
</tr>
<tr id="i91" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/navi/AMapNavi.html#switchParallelRoad--">switchParallelRoad</a></span>()</code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;&nbsp;</div>
</td>
</tr>
<tr id="i92" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/navi/AMapNavi.html#switchParallelRoad-int-">switchParallelRoad</a></span>(int&nbsp;parallelType)</code>
<div class="block">切换平行路，目前支持主辅路和高架上下切换。</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="getInstance-Context-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getInstance</h4>
<pre>public static&nbsp;<a href="../../../../com/amap/api/navi/AMapNavi.html" title="com.amap.api.navi中的类">AMapNavi</a>&nbsp;getInstance(Context&nbsp;context)
                            throws com.amap.api.maps.AMapException</pre>
<div class="block">创建一个AMapNavi导航对象。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>context</code> - 对应的Context</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>导航对象(单例)。</dd>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code>com.amap.api.maps.AMapException</code></dd>
</dl>
</li>
</ul>
<a name="getVersion--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVersion</h4>
<pre>public static&nbsp;java.lang.String&nbsp;getVersion()</pre>
<div class="block">获取版本号</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>SDK版本号</dd>
</dl>
</li>
</ul>
<a name="setApiKey-Context-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setApiKey</h4>
<pre>public static&nbsp;void&nbsp;setApiKey(Context&nbsp;context,
                             java.lang.String&nbsp;apiKey)</pre>
<div class="block">手动设置key,这是个静态方法，需要在一切导航接口前调用，建议在applicaiton中调用</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>context</code> - 对应的Context</dd>
<dd><code>apiKey</code> - 用户在高德官网申请的KEY</dd>
</dl>
</li>
</ul>
<a name="setTtsPlaying-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTtsPlaying</h4>
<pre>public static&nbsp;void&nbsp;setTtsPlaying(boolean&nbsp;playing)</pre>
<div class="block">设置外部播放器是否正在播报
 <br>
 如果正在播报语音，请设置为true，如果没有在播报语音，请设置false；如一直设置true，SDK内部会认为外界一直在播报，
 那么<a href="../../../../com/amap/api/navi/AMapNaviListener.html#onGetNavigationText-int-java.lang.String-"><code>AMapNaviListener.onGetNavigationText(int, String)</code></a>，就会一直不触发，导致无文字吐出;
 如一直设置false，文字吐出的频率可能会过快，会出现语句打断情况，所以请根据实际情况设置。
 <p>
 注意：7.1.0版本以后，若该接口设置为true，使用playTTS播放的自定义语音也不会播报。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>playing</code> - true-外部播放器正在播报中，false-外部播放器空闲中</dd>
</dl>
</li>
</ul>
<a name="isTtsPlaying--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isTtsPlaying</h4>
<pre>public static&nbsp;boolean&nbsp;isTtsPlaying()</pre>
<div class="block">当前SDK内置语音的是否正在播报
 <p>
 注意：正在播报的时候请不要使用外部播放器播报语音，会导致与导航内置的播报冲突。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>true-内置语音正在播报，false-内置语音空闲中</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.4.0</dd>
</dl>
</li>
</ul>
<a name="strategyConvert-boolean-boolean-boolean-boolean-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>strategyConvert</h4>
<pre>public&nbsp;int&nbsp;strategyConvert(boolean&nbsp;avoidCongestion,
                           boolean&nbsp;avoidHighway,
                           boolean&nbsp;avoidCost,
                           boolean&nbsp;prioritiseHighway,
                           boolean&nbsp;multipleRoute)</pre>
<div class="block">进行算路策略转换，将传入的特定规则转换成PathPlanningStrategy的枚举值。
 注意：<font color='red'>该接口仅驾车模式有效</font></div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>avoidCongestion</code> - 是否躲避拥堵</dd>
<dd><code>avoidHighway</code> - 是否不走高速</dd>
<dd><code>avoidCost</code> - 是否避免收费</dd>
<dd><code>prioritiseHighway</code> - 是否高速优先</dd>
<dd><code>multipleRoute</code> - 单路径or多路径，6.3版本以后该参数已经无效，统一使用多路线算路。</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>策略ID，可以将此策略ID传入AmapNavi类calculateDriveRoute方法进行相应的算路。</dd>
</dl>
</li>
</ul>
<a name="destroy--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>destroy</h4>
<pre>public static&nbsp;void&nbsp;destroy()</pre>
<div class="block">释放导航对象资源
 <p>
 退出时调用此接口释放导航资源，在调用此接口后不能再调用AMapNavi类里的其它接口。
 </p></div>
</li>
</ul>
<a name="startNavi-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>startNavi</h4>
<pre>public&nbsp;boolean&nbsp;startNavi(int&nbsp;naviType)</pre>
<div class="block">开始导航。实时导航会自动打开手机卫星定位功能。模拟导航则不需要使用定位功能。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>naviType</code> - 导航类型，1:实时导航 2:模拟导航。</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>启动成功或者失败。true是成功，false是失败。</dd>
</dl>
</li>
</ul>
<a name="startNaviWithPath-int-com.amap.api.navi.model.AMapNaviPathGroup-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>startNaviWithPath</h4>
<pre>public&nbsp;boolean&nbsp;startNaviWithPath(int&nbsp;naviType,
                                 <a href="../../../../com/amap/api/navi/model/AMapNaviPathGroup.html" title="com.amap.api.navi.model中的类">AMapNaviPathGroup</a>&nbsp;pathGroup)</pre>
<div class="block">使用指定路线开始导航</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>naviType</code> - 导航类型 <a href="../../../../com/amap/api/navi/enums/NaviType.html" title="com.amap.api.navi.enums中的类"><code>NaviType</code></a></dd>
<dd><code>pathGroup</code> - 路线组</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>true-成功 false-失败</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>7.7.0</dd>
</dl>
</li>
</ul>
<a name="pauseNavi--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>pauseNavi</h4>
<pre>public&nbsp;void&nbsp;pauseNavi()</pre>
<div class="block">暂停导航，仅支持模拟导航。</div>
</li>
</ul>
<a name="stopNavi--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>stopNavi</h4>
<pre>public&nbsp;void&nbsp;stopNavi()</pre>
<div class="block">停止导航，包含实时导航和模拟导航。</div>
</li>
</ul>
<a name="resumeNavi--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>resumeNavi</h4>
<pre>public&nbsp;void&nbsp;resumeNavi()</pre>
<div class="block">继续导航，仅支持模拟导航。</div>
</li>
</ul>
<a name="readNaviInfo--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readNaviInfo</h4>
<pre>public&nbsp;boolean&nbsp;readNaviInfo()</pre>
<div class="block">触发一次导航播报提示。
 注意：<font color='red'>该接口仅驾车和步行模式有效</font></div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>是否请求成功。true是成功，false是失败。</dd>
</dl>
</li>
</ul>
<a name="readTrafficInfo-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readTrafficInfo</h4>
<pre>public&nbsp;boolean&nbsp;readTrafficInfo(int&nbsp;frontDistance)</pre>
<div class="block">触发一次前方路况播报。
 注意：<font color='red'>该接口仅驾车模式有效</font></div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>frontDistance</code> - 要播报的前方路径距离，0为SDK默认距离，-1为整条路径，一般情况下前方路况播报设置为0。</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>返回请求是否成功。true是成功，false是失败。</dd>
</dl>
</li>
</ul>
<a name="calculateDriveRoute-java.util.List-java.util.List-java.util.List-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>calculateDriveRoute</h4>
<pre>public&nbsp;boolean&nbsp;calculateDriveRoute(java.util.List&lt;<a href="../../../../com/amap/api/navi/model/NaviLatLng.html" title="com.amap.api.navi.model中的类">NaviLatLng</a>&gt;&nbsp;from,
                                   java.util.List&lt;<a href="../../../../com/amap/api/navi/model/NaviLatLng.html" title="com.amap.api.navi.model中的类">NaviLatLng</a>&gt;&nbsp;to,
                                   java.util.List&lt;<a href="../../../../com/amap/api/navi/model/NaviLatLng.html" title="com.amap.api.navi.model中的类">NaviLatLng</a>&gt;&nbsp;wayPoints,
                                   int&nbsp;strategy)</pre>
<div class="block">计算驾车路径(包含起点)。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>from</code> - 指定的导航起点。支持多个起点，起点列表的尾点为实际导航起点，其他坐标点为辅助信息，带有方向性，可有效避免算路到马路的另一侧；</dd>
<dd><code>to</code> - 指定的导航终点。支持多个终点，终点列表的尾点为实际导航终点，其他坐标点为辅助信息，带有方向性，可有效避免算路到马路的另一侧。</dd>
<dd><code>wayPoints</code> - 途经点，同时支持最多16个途经点的路径规划；</dd>
<dd><code>strategy</code> - 规划策略，详情参见 <a href="../../../../com/amap/api/navi/enums/PathPlanningStrategy.html" title="com.amap.api.navi.enums中的类"><code>PathPlanningStrategy</code></a>。普通客车、货车请使用"DRIVING_"开头的策略，驾车推荐使用默认策略 <a href="../../../../com/amap/api/navi/enums/PathPlanningStrategy.html#DRIVING_MULTIPLE_ROUTES_DEFAULT"><code>PathPlanningStrategy.DRIVING_MULTIPLE_ROUTES_DEFAULT</code></a>，
                  摩托车请使用"MOTOR_"开头的策略。</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>算路是否成功，true-成功；false-失败。注意：返回true，只表示路径计算方法执行，但是否返回规划的路线，请参见AMapNaviListener的回调。</dd>
</dl>
</li>
</ul>
<a name="calculateDriveRoute-java.util.List-java.util.List-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>calculateDriveRoute</h4>
<pre>public&nbsp;boolean&nbsp;calculateDriveRoute(java.util.List&lt;<a href="../../../../com/amap/api/navi/model/NaviLatLng.html" title="com.amap.api.navi.model中的类">NaviLatLng</a>&gt;&nbsp;to,
                                   java.util.List&lt;<a href="../../../../com/amap/api/navi/model/NaviLatLng.html" title="com.amap.api.navi.model中的类">NaviLatLng</a>&gt;&nbsp;wayPoints,
                                   int&nbsp;strategy)</pre>
<div class="block">计算驾车路径(不带起点，起点默认为当前位置)。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>to</code> - 指定的导航终点。支持多个终点，终点列表的尾点为实际导航终点，其他坐标点为辅助信息，带有方向性，可有效避免算路到马路的另一侧。</dd>
<dd><code>wayPoints</code> - 途经点，同时支持最多16个途经点的路径规划。</dd>
<dd><code>strategy</code> - 规划策略，详情参见 <a href="../../../../com/amap/api/navi/enums/PathPlanningStrategy.html" title="com.amap.api.navi.enums中的类"><code>PathPlanningStrategy</code></a>。普通客车、货车请使用"DRIVING_"开头的策略，驾车推荐使用默认策略 <a href="../../../../com/amap/api/navi/enums/PathPlanningStrategy.html#DRIVING_MULTIPLE_ROUTES_DEFAULT"><code>PathPlanningStrategy.DRIVING_MULTIPLE_ROUTES_DEFAULT</code></a>，
                  摩托车请使用"MOTOR_"开头的策略。</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>算路是否成功，true-成功；false-失败。说明：返回true，只表示路径计算方法执行，但是否返回规划的路线，请参见AMapNaviListener的回调。</dd>
</dl>
</li>
</ul>
<a name="calculateDriveRoute-java.lang.String-java.lang.String-java.util.List-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>calculateDriveRoute</h4>
<pre>public&nbsp;boolean&nbsp;calculateDriveRoute(java.lang.String&nbsp;fromPoiId,
                                   java.lang.String&nbsp;toPoiId,
                                   java.util.List&lt;java.lang.String&gt;&nbsp;wayPoints,
                                   int&nbsp;strategy)</pre>
<div class="block">计算驾车路径(包含起点)。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>fromPoiId</code> - 指定的导航起点Poi的Id;</dd>
<dd><code>toPoiId</code> - 指定的导航终点Poi的Id;</dd>
<dd><code>wayPoints</code> - 途经点Poi，同时支持最多16个途经点的路径规划；</dd>
<dd><code>strategy</code> - 规划策略，详情参见 <a href="../../../../com/amap/api/navi/enums/PathPlanningStrategy.html" title="com.amap.api.navi.enums中的类"><code>PathPlanningStrategy</code></a>。普通客车、货车请使用"DRIVING_"开头的策略，驾车推荐使用默认策略 <a href="../../../../com/amap/api/navi/enums/PathPlanningStrategy.html#DRIVING_MULTIPLE_ROUTES_DEFAULT"><code>PathPlanningStrategy.DRIVING_MULTIPLE_ROUTES_DEFAULT</code></a>，
                  摩托车请使用"MOTOR_"开头的策略。</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>返回请求是否成功, true-成功;false-失败。返回true，只表示路径计算方法执行，但是否返回规划的路线，请参见<a href="../../../../com/amap/api/navi/AMapNaviListener.html" title="com.amap.api.navi中的接口"><code>AMapNaviListener</code></a>算路成功回调。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.1.0</dd>
</dl>
</li>
</ul>
<a name="calculateDriveRoute-java.lang.String-java.util.List-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>calculateDriveRoute</h4>
<pre>public&nbsp;boolean&nbsp;calculateDriveRoute(java.lang.String&nbsp;toPoiId,
                                   java.util.List&lt;java.lang.String&gt;&nbsp;wayPointIds,
                                   int&nbsp;strategy)</pre>
<div class="block">计算驾车路径(不带起点，起点默认为当前位置)。调用此接口前需要调用<a href="../../../../com/amap/api/navi/AMapNavi.html#startGPS--"><code>AMapNavi.startGPS()</code></a>开启定位，获取算路的起点。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>toPoiId</code> - 指定的导航终点Poi的Id;</dd>
<dd><code>wayPointIds</code> - 途经点Poi id，同时支持最多16个途经点的路径规划；</dd>
<dd><code>strategy</code> - 规划策略，详情参见 <a href="../../../../com/amap/api/navi/enums/PathPlanningStrategy.html" title="com.amap.api.navi.enums中的类"><code>PathPlanningStrategy</code></a>。普通客车、货车请使用"DRIVING_"开头的策略，驾车推荐使用默认策略 <a href="../../../../com/amap/api/navi/enums/PathPlanningStrategy.html#DRIVING_MULTIPLE_ROUTES_DEFAULT"><code>PathPlanningStrategy.DRIVING_MULTIPLE_ROUTES_DEFAULT</code></a>，
                    摩托车请使用"MOTOR_"开头的策略。</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>返回请求是否成功, true-成功;false-失败。返回true，只表示路径计算方法执行，但是否返回规划的路线，请参见<a href="../../../../com/amap/api/navi/AMapNaviListener.html" title="com.amap.api.navi中的接口"><code>AMapNaviListener</code></a>算路成功回调。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.1.0</dd>
</dl>
</li>
</ul>
<a name="calculateDriveRoute-com.amap.api.navi.model.NaviPoi-com.amap.api.navi.model.NaviPoi-java.util.List-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>calculateDriveRoute</h4>
<pre>public&nbsp;boolean&nbsp;calculateDriveRoute(<a href="../../../../com/amap/api/navi/model/NaviPoi.html" title="com.amap.api.navi.model中的类">NaviPoi</a>&nbsp;fromPoi,
                                   <a href="../../../../com/amap/api/navi/model/NaviPoi.html" title="com.amap.api.navi.model中的类">NaviPoi</a>&nbsp;toPoi,
                                   java.util.List&lt;<a href="../../../../com/amap/api/navi/model/NaviPoi.html" title="com.amap.api.navi.model中的类">NaviPoi</a>&gt;&nbsp;wayPoints,
                                   int&nbsp;strategy)</pre>
<div class="block">驾车POI算路
 <p>
 该方法可以避免到达点问题导致的绕路，地铁等多出口类POI，也会导航到多个出口。
 注意：NaviPoi对象中，优先使用Poi id进行算路，失败则使用经纬度进行算路。
 </p></div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>fromPoi</code> - 起点poi <a href="../../../../com/amap/api/navi/model/NaviPoi.html" title="com.amap.api.navi.model中的类"><code>NaviPoi</code></a>，若传入无效起点则使用当前位置</dd>
<dd><code>toPoi</code> - 终点poi <a href="../../../../com/amap/api/navi/model/NaviPoi.html" title="com.amap.api.navi.model中的类"><code>NaviPoi</code></a></dd>
<dd><code>wayPoints</code> - 途经点poi <a href="../../../../com/amap/api/navi/model/NaviPoi.html" title="com.amap.api.navi.model中的类"><code>NaviPoi</code></a></dd>
<dd><code>strategy</code> - 规划策略，详情参见 <a href="../../../../com/amap/api/navi/enums/PathPlanningStrategy.html" title="com.amap.api.navi.enums中的类"><code>PathPlanningStrategy</code></a>。普通客车、货车请使用"DRIVING_"开头的策略，驾车推荐使用默认策略 <a href="../../../../com/amap/api/navi/enums/PathPlanningStrategy.html#DRIVING_MULTIPLE_ROUTES_DEFAULT"><code>PathPlanningStrategy.DRIVING_MULTIPLE_ROUTES_DEFAULT</code></a>，
                  摩托车请使用"MOTOR_"开头的策略。</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>发起算路是否成功</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.6.0</dd>
</dl>
</li>
</ul>
<a name="setCarInfo-com.amap.api.navi.model.AMapCarInfo-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCarInfo</h4>
<pre>public&nbsp;void&nbsp;setCarInfo(<a href="../../../../com/amap/api/navi/model/AMapCarInfo.html" title="com.amap.api.navi.model中的类">AMapCarInfo</a>&nbsp;carInfo)</pre>
<div class="block">设置车辆信息(包括车型，车牌，车高，车重等)，路径规划时会躲避车辆限行区域和路线。<br/>
 目前小客车算路只考虑车牌因素，货车算路会考虑车高、车重等信息，
 其中<a href="../../../../com/amap/api/navi/model/AMapCarInfo.html#setRestriction-boolean-"><code>AMapCarInfo.setRestriction(boolean)</code></a>}用于控制是否规避限行路段。<br/>
 <a href="../../../../com/amap/api/navi/model/AMapCarInfo.html#setVehicleLoadSwitch-boolean-"><code>AMapCarInfo.setVehicleLoadSwitch(boolean)</code></a>用于控制算路时是否需要考虑车重。<br/>
 注意：<font color='red'>该接口仅驾车模式有效</font></div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>carInfo</code> - 车辆信息类</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>5.0.0</dd>
</dl>
</li>
</ul>
<a name="setTravelInfo-com.amap.api.navi.model.AMapTravelInfo-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTravelInfo</h4>
<pre>public&nbsp;void&nbsp;setTravelInfo(<a href="../../../../com/amap/api/navi/model/AMapTravelInfo.html" title="com.amap.api.navi.model中的类">AMapTravelInfo</a>&nbsp;travelInfo)</pre>
<div class="block">设置骑步行信息（骑行、步行）。<br/></div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>travelInfo</code> - 车辆信息类</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>10.0.x</dd>
</dl>
</li>
</ul>
<a name="reCalculateRoute-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>reCalculateRoute</h4>
<pre>public&nbsp;boolean&nbsp;reCalculateRoute(int&nbsp;strategy)</pre>
<div class="block">导航过程中重新规划路线（起点为当前位置，途经点、终点位置不变）</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>strategy</code> - 指定的驾车策略常量值，参见<a href="../../../../com/amap/api/navi/enums/PathPlanningStrategy.html" title="com.amap.api.navi.enums中的类"><code>PathPlanningStrategy</code></a>；
                 骑步行忽略此参数，传0即可。</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>算路是否成功。true-成功；false-失败。</dd>
</dl>
</li>
</ul>
<a name="getTrafficStatuses-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTrafficStatuses</h4>
<pre>public&nbsp;java.util.List&lt;<a href="../../../../com/amap/api/navi/model/AMapTrafficStatus.html" title="com.amap.api.navi.model中的类">AMapTrafficStatus</a>&gt;&nbsp;getTrafficStatuses(int&nbsp;startPos,
                                                            int&nbsp;distance)</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;<span class="deprecationComment">已废弃，请使用 <a href="../../../../com/amap/api/navi/model/AMapNaviPath.html#getTrafficStatuses--"><code>AMapNaviPath.getTrafficStatuses()</code></a></span></div>
<div class="block">获取当前导航主路线的路况信息。<br/>
 注意：<font color='red'>该接口仅驾车模式有效</font></div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>startPos</code> - <font color='red'>参数已无效,传入0即可</font></dd>
<dd><code>distance</code> - <font color='red'>参数已无效,传入0即可</font></dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>返回当前导航主路线的路况信息数组，用于绘制光柱。</dd>
</dl>
</li>
</ul>
<a name="getNaviPath--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNaviPath</h4>
<pre>public&nbsp;<a href="../../../../com/amap/api/navi/model/AMapNaviPath.html" title="com.amap.api.navi.model中的类">AMapNaviPath</a>&nbsp;getNaviPath()</pre>
<div class="block">获取当前规划的路线方案（路线结果），驾车、骑行与步行共用这一个对象</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>当前规划的路线信息。</dd>
</dl>
</li>
</ul>
<a name="getNaviPaths--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNaviPaths</h4>
<pre>public&nbsp;java.util.HashMap&lt;java.lang.Integer,<a href="../../../../com/amap/api/navi/model/AMapNaviPath.html" title="com.amap.api.navi.model中的类">AMapNaviPath</a>&gt;&nbsp;getNaviPaths()</pre>
<div class="block">获取计算的多条路径</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>路线Map对象，key存储的是路线id,value存储的是<a href="../../../../com/amap/api/navi/model/AMapNaviPath.html" title="com.amap.api.navi.model中的类"><code>AMapNaviPath</code></a>对象</dd>
</dl>
</li>
</ul>
<a name="getNaviGuideList--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNaviGuideList</h4>
<pre>public&nbsp;java.util.List&lt;<a href="../../../../com/amap/api/navi/model/AMapNaviGuide.html" title="com.amap.api.navi.model中的类">AMapNaviGuide</a>&gt;&nbsp;getNaviGuideList()</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;<span class="deprecationComment">请使用 <a href="../../../../com/amap/api/navi/model/AMapNaviPath.html#getNaviGuideList--"><code>AMapNaviPath.getNaviGuideList()</code></a></span></div>
<div class="block">获取主路线路段概览</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>路段概览集合</dd>
</dl>
</li>
</ul>
<a name="getNaviSetting--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNaviSetting</h4>
<pre>public&nbsp;<a href="../../../../com/amap/api/navi/NaviSetting.html" title="com.amap.api.navi中的类">NaviSetting</a>&nbsp;getNaviSetting()</pre>
<div class="block">获取导航配置类</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>导航配置类</dd>
</dl>
</li>
</ul>
<a name="setEmulatorNaviSpeed-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEmulatorNaviSpeed</h4>
<pre>public&nbsp;void&nbsp;setEmulatorNaviSpeed(int&nbsp;speed)</pre>
<div class="block">设置模拟导航的速度。<br/>
 驾车默认速度为60km/h,设置的模拟值区间应该在10-120之间.<br/>
 步行默认速度为20km/h,设置的模拟值区间应该在10-30之间.<br/>
 骑行默认速度为35km/h,设置的模拟值区间应该在10-50之间.</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>speed</code> - 模拟导航的速度，单位：km/h。</dd>
</dl>
</li>
</ul>
<a name="addAMapNaviListener-com.amap.api.navi.AMapNaviListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addAMapNaviListener</h4>
<pre>public&nbsp;void&nbsp;addAMapNaviListener(<a href="../../../../com/amap/api/navi/AMapNaviListener.html" title="com.amap.api.navi中的接口">AMapNaviListener</a>&nbsp;naviListener)</pre>
<div class="block">添加导航事件回调监听。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>naviListener</code> - 导航回调监听</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>1.7.0</dd>
</dl>
</li>
</ul>
<a name="removeAMapNaviListener-com.amap.api.navi.AMapNaviListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removeAMapNaviListener</h4>
<pre>public&nbsp;void&nbsp;removeAMapNaviListener(<a href="../../../../com/amap/api/navi/AMapNaviListener.html" title="com.amap.api.navi中的接口">AMapNaviListener</a>&nbsp;naviListener)</pre>
<div class="block">移除导航对象的监听。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>naviListener</code> - 监听listener。</dd>
</dl>
</li>
</ul>
<a name="startGPS--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>startGPS</h4>
<pre>public&nbsp;boolean&nbsp;startGPS()</pre>
<div class="block">启动手机卫星定位。
 <p>
 用户可以手动启动卫星定位功能，如果没有启动，在导航启动时（startNavi）会自动启动。默认定位时间间隔为1秒，变化距离为0。
 </p></div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>返回手机卫星定位启动是否成功。true代表成功，false代表失败。</dd>
</dl>
</li>
</ul>
<a name="startGPS-long-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>startGPS</h4>
<pre>public&nbsp;boolean&nbsp;startGPS(long&nbsp;time,
                        int&nbsp;dis)</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;<span class="deprecationComment">请使用 <a href="../../../../com/amap/api/navi/AMapNavi.html#startGPS-long-"><code>AMapNavi.startGPS(long)</code></a></span></div>
<div class="block">启动手机卫星定位, 带距离和时间参数。
 <p>
 用户可以手动启动手机卫星定位，如果没有启动，在导航启动时（startNavi）会自动启动。
 </p></div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>time</code> - 位置更新的时间间隔, 单位：毫秒。</dd>
<dd><code>dis</code> - 位置更新的距离间隔, 单位：米。此参数已无效。</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>返回手机卫星定位启动是否成功。true代表成功，false代表失败。</dd>
</dl>
</li>
</ul>
<a name="startGPS-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>startGPS</h4>
<pre>public&nbsp;boolean&nbsp;startGPS(long&nbsp;interval)</pre>
<div class="block">启动手机卫星定位, 带定位时间间隔参数。
 <p>
 用户可以手动启动手机卫星定位，如果没有启动，在导航启动时（startNavi）内部会自动启动。
 </p></div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>interval</code> - 导航内部定位的时间间隔, 单位：毫秒。注意，该参数仅影响导航内部定位塞点的频率，与导航位置回调 <a href="../../../../com/amap/api/navi/AMapNaviListener.html#onLocationChange-com.amap.api.navi.model.AMapNaviLocation-"><code>AMapNaviListener.onLocationChange(AMapNaviLocation)</code></a> 的频率无关。</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>返回手机卫星定位启动是否成功。true代表成功，false代表失败。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>7.9.0</dd>
</dl>
</li>
</ul>
<a name="stopGPS--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>stopGPS</h4>
<pre>public&nbsp;boolean&nbsp;stopGPS()</pre>
<div class="block">停止手机卫星定位。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>返回是否停止手机卫星定位成功。true，成功；false，失败。</dd>
</dl>
</li>
</ul>
<a name="calculateWalkRoute-com.amap.api.navi.model.NaviLatLng-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>calculateWalkRoute</h4>
<pre>public&nbsp;boolean&nbsp;calculateWalkRoute(<a href="../../../../com/amap/api/navi/model/NaviLatLng.html" title="com.amap.api.navi.model中的类">NaviLatLng</a>&nbsp;to)</pre>
<div class="block">计算步行路径(不带起点，默认为当前位置)。超过100km会直接返回false</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>to</code> - 导航终点。调用此接口前需要调用<a href="../../../../com/amap/api/navi/AMapNavi.html#startGPS--"><code>AMapNavi.startGPS()</code></a>开启定位，获取算路的起点。</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>算路是否成功，true-成功；false-失败。说明：返回true，只表示路径计算方法执行，但是否返回规划的路线，请参见AMapNaviListener的回调。</dd>
</dl>
</li>
</ul>
<a name="calculateWalkRoute-com.amap.api.navi.model.NaviLatLng-com.amap.api.navi.model.NaviLatLng-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>calculateWalkRoute</h4>
<pre>public&nbsp;boolean&nbsp;calculateWalkRoute(<a href="../../../../com/amap/api/navi/model/NaviLatLng.html" title="com.amap.api.navi.model中的类">NaviLatLng</a>&nbsp;from,
                                  <a href="../../../../com/amap/api/navi/model/NaviLatLng.html" title="com.amap.api.navi.model中的类">NaviLatLng</a>&nbsp;to)</pre>
<div class="block">计算步行路径(包含起点)。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>from</code> - 导航起点。</dd>
<dd><code>to</code> - 导航终点。</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>算路是否成功，true-成功；false-失败。说明：返回true，只表示路径计算方法执行，但是否返回规划的路线，请参见AMapNaviListener的回调。</dd>
</dl>
</li>
</ul>
<a name="calculateWalkRoute-com.amap.api.navi.model.NaviPoi-com.amap.api.navi.model.NaviPoi-com.amap.api.navi.enums.TravelStrategy-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>calculateWalkRoute</h4>
<pre>public&nbsp;boolean&nbsp;calculateWalkRoute(<a href="../../../../com/amap/api/navi/model/NaviPoi.html" title="com.amap.api.navi.model中的类">NaviPoi</a>&nbsp;fromPoi,
                                  <a href="../../../../com/amap/api/navi/model/NaviPoi.html" title="com.amap.api.navi.model中的类">NaviPoi</a>&nbsp;toPoi,
                                  <a href="../../../../com/amap/api/navi/enums/TravelStrategy.html" title="com.amap.api.navi.enums中的枚举">TravelStrategy</a>&nbsp;strategy)</pre>
<div class="block">步行POI算路</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>fromPoi</code> - 起点POI</dd>
<dd><code>toPoi</code> - 终点POI</dd>
<dd><code>strategy</code> - 算路策略 <a href="../../../../com/amap/api/navi/enums/TravelStrategy.html" title="com.amap.api.navi.enums中的枚举"><code>TravelStrategy</code></a></dd>
<dt><span class="returnLabel">返回:</span></dt>
</dl>
</li>
</ul>
<a name="calculateWalkRoute-com.amap.api.navi.model.NaviPoi-java.util.List-com.amap.api.navi.model.NaviPoi-com.amap.api.navi.enums.TravelStrategy-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>calculateWalkRoute</h4>
<pre>public&nbsp;boolean&nbsp;calculateWalkRoute(<a href="../../../../com/amap/api/navi/model/NaviPoi.html" title="com.amap.api.navi.model中的类">NaviPoi</a>&nbsp;fromPoi,
                                  java.util.List&lt;<a href="../../../../com/amap/api/navi/model/NaviPoi.html" title="com.amap.api.navi.model中的类">NaviPoi</a>&gt;&nbsp;wayPoints,
                                  <a href="../../../../com/amap/api/navi/model/NaviPoi.html" title="com.amap.api.navi.model中的类">NaviPoi</a>&nbsp;toPoi,
                                  <a href="../../../../com/amap/api/navi/enums/TravelStrategy.html" title="com.amap.api.navi.enums中的枚举">TravelStrategy</a>&nbsp;strategy)</pre>
<div class="block">步行POI算路 特别注意：当前接口为收费接口，您如果申请试用或者正式应用都请通过工单系统提交商务合作类工单进行沟通 https://lbs.amap.com/。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>fromPoi</code> - 起点POI，如果以“我的位置”作为起点，请传null。 如果fromPoi不为null，那么POIID合法，优先使用ID参与算路，否则使用坐标点。</dd>
<dd><code>wayPoints</code> - 途径点POI，最多支持16个途经点，超过16个会取前16个。如果POIID合法，优先使用ID参与算路。否则使用坐标点。注意:POIID和坐标点不能同时为空。</dd>
<dd><code>toPoi</code> - 终点POI，如果POIID合法，优先使用ID参与算路，否则使用坐标点。注意：POIID和坐标点不能同时为空。</dd>
<dd><code>strategy</code> - 路径的计算策略</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>规划路径所需条件和参数校验是否成功，不代表算路成功与否。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.3.5</dd>
</dl>
</li>
</ul>
<a name="calculateRideRoute-com.amap.api.navi.model.NaviLatLng-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>calculateRideRoute</h4>
<pre>public&nbsp;boolean&nbsp;calculateRideRoute(<a href="../../../../com/amap/api/navi/model/NaviLatLng.html" title="com.amap.api.navi.model中的类">NaviLatLng</a>&nbsp;to)</pre>
<div class="block">骑行（自行车）经纬度算路 (不带起点，默认为当前位置)。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>to</code> - 骑行终点。调用此接口前需要调用<a href="../../../../com/amap/api/navi/AMapNavi.html#startGPS--"><code>AMapNavi.startGPS()</code></a>开启定位，获取算路的起点。</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>算路是否成功，true-成功；false-失败。说明：返回true，只表示路径计算方法执行，但是否返回规划的路线，请参见AMapNaviListener的回调。</dd>
</dl>
</li>
</ul>
<a name="calculateRideRoute-com.amap.api.navi.model.NaviLatLng-com.amap.api.navi.model.NaviLatLng-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>calculateRideRoute</h4>
<pre>public&nbsp;boolean&nbsp;calculateRideRoute(<a href="../../../../com/amap/api/navi/model/NaviLatLng.html" title="com.amap.api.navi.model中的类">NaviLatLng</a>&nbsp;from,
                                  <a href="../../../../com/amap/api/navi/model/NaviLatLng.html" title="com.amap.api.navi.model中的类">NaviLatLng</a>&nbsp;to)</pre>
<div class="block">骑行（自行车）经纬度算路 (包含起点)。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>from</code> - 骑行起点。</dd>
<dd><code>to</code> - 导航终点。</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>算路是否成功，true-成功；false-失败。说明：返回true，只表示路径计算方法执行，但是否返回规划的路线，请参见AMapNaviListener的回调。</dd>
</dl>
</li>
</ul>
<a name="calculateRideRoute-com.amap.api.navi.model.NaviPoi-com.amap.api.navi.model.NaviPoi-com.amap.api.navi.enums.TravelStrategy-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>calculateRideRoute</h4>
<pre>public&nbsp;boolean&nbsp;calculateRideRoute(<a href="../../../../com/amap/api/navi/model/NaviPoi.html" title="com.amap.api.navi.model中的类">NaviPoi</a>&nbsp;fromPoi,
                                  <a href="../../../../com/amap/api/navi/model/NaviPoi.html" title="com.amap.api.navi.model中的类">NaviPoi</a>&nbsp;toPoi,
                                  <a href="../../../../com/amap/api/navi/enums/TravelStrategy.html" title="com.amap.api.navi.enums中的枚举">TravelStrategy</a>&nbsp;strategy)</pre>
<div class="block">骑行（自行车）POI算路</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>fromPoi</code> - 起点POI</dd>
<dd><code>toPoi</code> - 终点POI</dd>
<dd><code>strategy</code> - 算路策略 <a href="../../../../com/amap/api/navi/enums/TravelStrategy.html" title="com.amap.api.navi.enums中的枚举"><code>TravelStrategy</code></a></dd>
<dt><span class="returnLabel">返回:</span></dt>
</dl>
</li>
</ul>
<a name="calculateRideRoute-com.amap.api.navi.model.NaviPoi-java.util.List-com.amap.api.navi.model.NaviPoi-com.amap.api.navi.enums.TravelStrategy-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>calculateRideRoute</h4>
<pre>public&nbsp;boolean&nbsp;calculateRideRoute(<a href="../../../../com/amap/api/navi/model/NaviPoi.html" title="com.amap.api.navi.model中的类">NaviPoi</a>&nbsp;fromPoi,
                                  java.util.List&lt;<a href="../../../../com/amap/api/navi/model/NaviPoi.html" title="com.amap.api.navi.model中的类">NaviPoi</a>&gt;&nbsp;wayPoints,
                                  <a href="../../../../com/amap/api/navi/model/NaviPoi.html" title="com.amap.api.navi.model中的类">NaviPoi</a>&nbsp;toPoi,
                                  <a href="../../../../com/amap/api/navi/enums/TravelStrategy.html" title="com.amap.api.navi.enums中的枚举">TravelStrategy</a>&nbsp;strategy)</pre>
<div class="block">骑行（自行车）POI算路 特别注意：当前接口为收费接口，您如果申请试用或者正式应用都请通过工单系统提交商务合作类工单进行沟通 https://lbs.amap.com/</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>fromPoi</code> - 起点POI，如果以“我的位置”作为起点，请传null. 如果fromPoi不为null，那么POIID合法，优先使用ID参与算路，否则使用坐标点</dd>
<dd><code>wayPoints</code> - 途径点POI，最多支持16个途经点，超过16个会取前16个。如果POIID合法。优先使用ID参与算路。否则使用坐标点。注意:POIID和坐标点不能同时为空</dd>
<dd><code>toPoi</code> - 终点POI，如果POIID合法，优先使用ID参与算路，否则使用坐标点。注意:POIID和坐标点不能同时为空</dd>
<dd><code>strategy</code> - 路径的计算策略</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>规划路径所需条件和参数校验是否成功，不代表算路成功与否</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.3.5</dd>
</dl>
</li>
</ul>
<a name="calculateEleBikeRoute-com.amap.api.navi.model.NaviLatLng-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>calculateEleBikeRoute</h4>
<pre>public&nbsp;boolean&nbsp;calculateEleBikeRoute(<a href="../../../../com/amap/api/navi/model/NaviLatLng.html" title="com.amap.api.navi.model中的类">NaviLatLng</a>&nbsp;to)</pre>
<div class="block">骑行（电动车）经纬度算路 (不带起点，默认为当前位置)。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>to</code> - 导航终点。</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>算路是否成功，true-成功；false-失败。说明：返回true，只表示路径计算方法执行，但是否返回规划的路线，请参见AMapNaviListener的回调。</dd>
</dl>
</li>
</ul>
<a name="calculateEleBikeRoute-com.amap.api.navi.model.NaviLatLng-com.amap.api.navi.model.NaviLatLng-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>calculateEleBikeRoute</h4>
<pre>public&nbsp;boolean&nbsp;calculateEleBikeRoute(<a href="../../../../com/amap/api/navi/model/NaviLatLng.html" title="com.amap.api.navi.model中的类">NaviLatLng</a>&nbsp;from,
                                     <a href="../../../../com/amap/api/navi/model/NaviLatLng.html" title="com.amap.api.navi.model中的类">NaviLatLng</a>&nbsp;to)</pre>
<div class="block">骑行（电动车）经纬度算路</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>from</code> - 骑行起点。起点传null，或经纬度为 0 时，使用当前位置作为起点。</dd>
<dd><code>to</code> - 导航终点。</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>算路是否成功，true-成功；false-失败。说明：返回true，只表示路径计算方法执行，但是否返回规划的路线，请参见AMapNaviListener的回调。</dd>
</dl>
</li>
</ul>
<a name="calculateEleBikeRoute-com.amap.api.navi.model.NaviPoi-com.amap.api.navi.model.NaviPoi-com.amap.api.navi.enums.TravelStrategy-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>calculateEleBikeRoute</h4>
<pre>public&nbsp;boolean&nbsp;calculateEleBikeRoute(<a href="../../../../com/amap/api/navi/model/NaviPoi.html" title="com.amap.api.navi.model中的类">NaviPoi</a>&nbsp;fromPoi,
                                     <a href="../../../../com/amap/api/navi/model/NaviPoi.html" title="com.amap.api.navi.model中的类">NaviPoi</a>&nbsp;toPoi,
                                     <a href="../../../../com/amap/api/navi/enums/TravelStrategy.html" title="com.amap.api.navi.enums中的枚举">TravelStrategy</a>&nbsp;strategy)</pre>
<div class="block">骑行（电动车）POI算路</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>fromPoi</code> - 起点POI</dd>
<dd><code>toPoi</code> - 终点POI</dd>
<dd><code>strategy</code> - 算路策略 <a href="../../../../com/amap/api/navi/enums/TravelStrategy.html" title="com.amap.api.navi.enums中的枚举"><code>TravelStrategy</code></a></dd>
<dt><span class="returnLabel">返回:</span></dt>
</dl>
</li>
</ul>
<a name="getEngineType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEngineType</h4>
<pre>public&nbsp;int&nbsp;getEngineType()</pre>
<div class="block">获得导航引擎类型</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>参见 <a href="../../../../com/amap/api/navi/enums/EngineType.html" title="com.amap.api.navi.enums中的类"><code>EngineType</code></a></dd>
</dl>
</li>
</ul>
<a name="getNaviType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNaviType</h4>
<pre>public&nbsp;int&nbsp;getNaviType()</pre>
<div class="block">获取导航位置变化驱动类型</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>参见 <a href="../../../../com/amap/api/navi/enums/NaviType.html" title="com.amap.api.navi.enums中的类"><code>NaviType</code></a></dd>
</dl>
</li>
</ul>
<a name="startAimlessMode-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>startAimlessMode</h4>
<pre>public&nbsp;void&nbsp;startAimlessMode(int&nbsp;aimlessMode)</pre>
<div class="block">设置在巡航模式（无路线规划）的状态下，智能播报的类型
 <br>
 用户一旦设置，在巡航模式（无路线规划）的状态下，会获得以下回调:
 <br>
 <a href="../../../../com/amap/api/navi/AimlessModeListener.html#onUpdateTrafficFacility-com.amap.api.navi.model.AMapNaviTrafficFacilityInfo:A-"><code>AimlessModeListener.onUpdateTrafficFacility(AMapNaviTrafficFacilityInfo[])</code></a>
 可以用于获得道路设施（包括转弯提示等）
 <br>
 <a href="../../../../com/amap/api/navi/AimlessModeListener.html#onUpdateAimlessModeElecCameraInfo-com.amap.api.navi.model.AMapNaviTrafficFacilityInfo:A-"><code>AimlessModeListener.onUpdateAimlessModeElecCameraInfo(AMapNaviTrafficFacilityInfo[])</code></a>
 可以用于获得电子眼信息更新
 <br>
 <a href="../../../../com/amap/api/navi/AimlessModeListener.html#updateAimlessModeCongestionInfo-com.amap.api.navi.model.AimLessModeCongestionInfo-"><code>AimlessModeListener.updateAimlessModeCongestionInfo(AimLessModeCongestionInfo)</code></a>
 可以用于获得周边道路拥堵信息
 <br>
 <a href="../../../../com/amap/api/navi/AimlessModeListener.html#updateAimlessModeStatistics-com.amap.api.navi.model.AimLessModeStat-"><code>AimlessModeListener.updateAimlessModeStatistics(AimLessModeStat)</code></a>
 可以用于巡航模式的统计信息，巡航开启时间，巡航移动距离
 <br>
 <a href="../../../../com/amap/api/navi/AMapNaviListener.html#onGetNavigationText-int-java.lang.String-"><code>AMapNaviListener.onGetNavigationText(int, String)</code></a>
 可以用于获得巡航语音播报文字
 <br>
 注意：<font color='red'>该接口仅驾车模式有效</font></div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>aimlessMode</code> - 巡航播报模式，详情见<a href="../../../../com/amap/api/navi/enums/AimLessMode.html" title="com.amap.api.navi.enums中的类"><code>AimLessMode</code></a></dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>1.8.0</dd>
</dl>
</li>
</ul>
<a name="stopAimlessMode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>stopAimlessMode</h4>
<pre>public&nbsp;void&nbsp;stopAimlessMode()</pre>
<div class="block">停止巡航模式
 注意：<font color='red'>该接口仅驾车模式有效</font></div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>1.8.0</dd>
</dl>
</li>
</ul>
<a name="setConnectionTimeout-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setConnectionTimeout</h4>
<pre>public&nbsp;void&nbsp;setConnectionTimeout(int&nbsp;connectionTimeOut)</pre>
<div class="block">设置建立连接超时时间，单位毫秒级，最低3000，默认10000.</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>connectionTimeOut</code> - 超时时间</dd>
</dl>
</li>
</ul>
<a name="setSoTimeout-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSoTimeout</h4>
<pre>public&nbsp;void&nbsp;setSoTimeout(int&nbsp;soTimeOut)</pre>
<div class="block">设置服务器返回超时时间，单位毫秒级，最低3000，默认10000.</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>soTimeOut</code> - 超时时间</dd>
</dl>
</li>
</ul>
<a name="selectRouteId-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>selectRouteId</h4>
<pre>public&nbsp;boolean&nbsp;selectRouteId(int&nbsp;id)</pre>
<div class="block">导航前选主路线
 注意：<font color='red'>该接口仅驾车模式有效</font></div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>id</code> - 路线的routeId</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>是否选择此路线成功</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>1.6.0</dd>
</dl>
</li>
</ul>
<a name="isGpsReady--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isGpsReady</h4>
<pre>public&nbsp;boolean&nbsp;isGpsReady()</pre>
<div class="block">获取手机卫星定位是否准备就绪</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>准备就绪，返回true；否则，返回false</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>1.6.0</dd>
</dl>
</li>
</ul>
<a name="setBroadcastMode-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBroadcastMode</h4>
<pre>public&nbsp;boolean&nbsp;setBroadcastMode(int&nbsp;mode)</pre>
<div class="block">设置语音播报模式为"专家模式"还是"新手模式"【驾车有效】,导航中设置需要下次算路才生效，如偏航或刷新重算.
 注意：<font color='red'>该接口仅驾车模式有效</font></div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>mode</code> - 参见<a href="../../../../com/amap/api/navi/enums/BroadcastMode.html" title="com.amap.api.navi.enums中的类"><code>BroadcastMode</code></a></dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>设置成功，返回true；否则，返回false</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>1.7.0</dd>
</dl>
</li>
</ul>
<a name="switchParallelRoad--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>switchParallelRoad</h4>
<pre>public&nbsp;void&nbsp;switchParallelRoad()</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>1.7.0 请使用<a href="../../../../com/amap/api/navi/AMapNavi.html#switchParallelRoad-int-"><code>AMapNavi.switchParallelRoad(int)</code></a>
 注意：<font color='red'>该接口仅驾车模式有效</font></dd>
</dl>
</li>
</ul>
<a name="switchParallelRoad-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>switchParallelRoad</h4>
<pre>public&nbsp;void&nbsp;switchParallelRoad(int&nbsp;parallelType)</pre>
<div class="block">切换平行路，目前支持主辅路和高架上下切换。
 <p>
 此函数只有在实时导航开始后才能使用，用来将路径的起点切换到当前导航路径平行的其它路径上
 <p/>
 例如: 当前路径在主路上，调用此接口将把路径切换到辅路上，如果当前道路周围没有平行道路，则路径不变，切换成功后将自动开始导航。
 注意：<font color='red'>该接口仅驾车模式有效</font></div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>parallelType</code> - 1-主辅路切换，2-高架上下切换</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.6.0</dd>
</dl>
</li>
</ul>
<a name="getIsUseExtraGPSData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getIsUseExtraGPSData</h4>
<pre>public&nbsp;boolean&nbsp;getIsUseExtraGPSData()</pre>
<div class="block">获取是否使用外部定位数据</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>是否使用外部定位数据</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>1.7.0</dd>
</dl>
</li>
</ul>
<a name="setIsUseExtraGPSData-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setIsUseExtraGPSData</h4>
<pre>public&nbsp;void&nbsp;setIsUseExtraGPSData(boolean&nbsp;isUseExtraData)</pre>
<div class="block">设置是否使用外部定位数据.
 只有将此开关打开后，<a href="../../../../com/amap/api/navi/AMapNavi.html#setExtraGPSData-int-Location-"><code>AMapNavi.setExtraGPSData(int, Location)</code></a>方法才会生效。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>isUseExtraData</code> - 是否使用外部定位数据</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>1.7.0</dd>
</dl>
</li>
</ul>
<a name="setExtraGPSData-int-Location-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setExtraGPSData</h4>
<pre>public&nbsp;void&nbsp;setExtraGPSData(int&nbsp;type,
                            Location&nbsp;location)</pre>
<div class="block">此方法用于设置外部定位数据，并使用外部定位数据进行导航
 使用此方法前需要先调用<a href="../../../../com/amap/api/navi/AMapNavi.html#setIsUseExtraGPSData-boolean-"><code>AMapNavi.setIsUseExtraGPSData(boolean)</code></a>将开关打开.</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>type</code> - 坐标类型。如果使用系统默认返回的定位坐标，type值为1。使用高德坐标，type值传2</dd>
<dd><code>location</code> - 外部定位数据。Longitude、Latitude、Accuracy、Speed、Bearing、Time 缺一不可</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>1.9.3</dd>
</dl>
</li>
</ul>
<a name="setUseInnerVoice-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setUseInnerVoice</h4>
<pre>public&nbsp;void&nbsp;setUseInnerVoice(boolean&nbsp;isUseInnerVoice)</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;</div>
<div class="block">设置使用内部语音播报, 默认为false, 为true时，用户设置<a href="../../../../com/amap/api/navi/AMapNaviListener.html#onGetNavigationText-int-java.lang.String-"><code>AMapNaviListener.onGetNavigationText(int, java.lang.String)</code></a> 方法将不再回调</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>isUseInnerVoice</code> - 是否使用内部语音播报</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>5.6.0</dd>
</dl>
</li>
</ul>
<a name="setUseInnerVoice-boolean-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setUseInnerVoice</h4>
<pre>public&nbsp;void&nbsp;setUseInnerVoice(boolean&nbsp;isUseInnerVoice,
                             boolean&nbsp;isCallBackText)</pre>
<div class="block">设置使用内部语音播报</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>isUseInnerVoice</code> - 是否使用内部语音播报， 默认为false</dd>
<dd><code>isCallBackText</code> - isUseInnerVoice设置为true以后，<a href="../../../../com/amap/api/navi/AMapNaviListener.html#onGetNavigationText-int-java.lang.String-"><code>AMapNaviListener.onGetNavigationText(int, java.lang.String)</code></a>接口会继续返回文字，默认为false</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.1.0</dd>
</dl>
</li>
</ul>
<a name="getIsUseInnerVoice--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getIsUseInnerVoice</h4>
<pre>public&nbsp;boolean&nbsp;getIsUseInnerVoice()</pre>
<div class="block">获取是否使用内部语音播报</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>true 使用内部语音; false 不适用内部语音</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>5.6.0</dd>
</dl>
</li>
</ul>
<a name="setMultipleRouteNaviMode-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMultipleRouteNaviMode</h4>
<pre>public&nbsp;void&nbsp;setMultipleRouteNaviMode(boolean&nbsp;multipleRouteNaviMode)</pre>
<div class="block">设置多路线导航模式(导航中拥有若干条备选路线供用户选择), 或单路线导航模式(默认模式).

 注意:此方法仅限于在开始导航前调用有效,步行与骑行导航无效,以下情况不会出现多备选路线：模拟导航、货车导航、路线存在途经点、路线长度超过80KM。
 注意：<font color='red'>该接口仅驾车模式有效</font></div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>multipleRouteNaviMode</code> - true:多路线导航模式, false:单路线导航模式(默认)</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.3.0</dd>
</dl>
</li>
</ul>
<a name="setTruckMultipleRouteNaviMode-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTruckMultipleRouteNaviMode</h4>
<pre>public&nbsp;void&nbsp;setTruckMultipleRouteNaviMode(boolean&nbsp;truckMultipleRouteNaviMode)</pre>
<div class="block">设置货车多路线导航模式(导航中拥有若干条备选路线供用户选择)， 或单路线导航模式（默认模式）

 注意：此方法仅限于在开始导航前调用有效，步行与骑行导航无效，以下情况不会出现多备选路线：
 模拟导航、路线存在途经点、路线长度超过80KM。
 注意：<font color='red'>该接口仅货车导航有效</font>
 特别注意：<font color='red'>当前接口为收费接口，您如果申请试用或者正式应用都请通过工单系统提交商务合作类工单进行沟通 https://lbs.amap.com/</font></div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>truckMultipleRouteNaviMode</code> - true:多路线导航模式, false:单路线导航模式（默认）</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.3.5</dd>
</dl>
</li>
</ul>
<a name="addParallelRoadListener-com.amap.api.navi.ParallelRoadListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addParallelRoadListener</h4>
<pre>public&nbsp;void&nbsp;addParallelRoadListener(<a href="../../../../com/amap/api/navi/ParallelRoadListener.html" title="com.amap.api.navi中的接口">ParallelRoadListener</a>&nbsp;listener)</pre>
<div class="block">添加平行路切换监听，包括主辅路切换和高架上下切换
 注意：<font color='red'>该接口仅驾车模式有效</font></div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>listener</code> - </dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.6.0</dd>
</dl>
</li>
</ul>
<a name="removeParallelRoadListener-com.amap.api.navi.ParallelRoadListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removeParallelRoadListener</h4>
<pre>public&nbsp;void&nbsp;removeParallelRoadListener(<a href="../../../../com/amap/api/navi/ParallelRoadListener.html" title="com.amap.api.navi中的接口">ParallelRoadListener</a>&nbsp;listener)</pre>
<div class="block">移除平行路切换监听，包括主辅路切换和高架上下切换
 注意：<font color='red'>该接口仅驾车模式有效</font></div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>listener</code> - </dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.6.0</dd>
</dl>
</li>
</ul>
<a name="addAimlessModeListener-com.amap.api.navi.AimlessModeListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addAimlessModeListener</h4>
<pre>public&nbsp;void&nbsp;addAimlessModeListener(<a href="../../../../com/amap/api/navi/AimlessModeListener.html" title="com.amap.api.navi中的接口">AimlessModeListener</a>&nbsp;listener)</pre>
<div class="block">注册巡航模式信息回调监听
 注意：<font color='red'>该接口仅驾车模式有效</font></div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>listener</code> - </dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>7.0.0</dd>
</dl>
</li>
</ul>
<a name="removeAimlessModeListener-com.amap.api.navi.AimlessModeListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removeAimlessModeListener</h4>
<pre>public&nbsp;void&nbsp;removeAimlessModeListener(<a href="../../../../com/amap/api/navi/AimlessModeListener.html" title="com.amap.api.navi中的接口">AimlessModeListener</a>&nbsp;listener)</pre>
<div class="block">移除巡航模式信息回调监听
 注意：<font color='red'>该接口仅驾车模式有效</font></div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>listener</code> - </dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>7.0.0</dd>
</dl>
</li>
</ul>
<a name="addTTSPlayListener-com.amap.api.navi.TTSPlayListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addTTSPlayListener</h4>
<pre>public&nbsp;void&nbsp;addTTSPlayListener(<a href="../../../../com/amap/api/navi/TTSPlayListener.html" title="com.amap.api.navi中的接口">TTSPlayListener</a>&nbsp;listener)</pre>
<div class="block">添加内置语音播报监听</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>listener</code> - </dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>7.9.0</dd>
</dl>
</li>
</ul>
<a name="removeTTSPlayListener-com.amap.api.navi.TTSPlayListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removeTTSPlayListener</h4>
<pre>public&nbsp;void&nbsp;removeTTSPlayListener(<a href="../../../../com/amap/api/navi/TTSPlayListener.html" title="com.amap.api.navi中的接口">TTSPlayListener</a>&nbsp;listener)</pre>
<div class="block">移除内置语音播报监听</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>listener</code> - </dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>7.9.0</dd>
</dl>
</li>
</ul>
<a name="setGpsWeakDetecedInterval-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setGpsWeakDetecedInterval</h4>
<pre>public&nbsp;void&nbsp;setGpsWeakDetecedInterval(long&nbsp;delayTime)</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;<span class="deprecationComment">此接口已失效</span></div>
<div class="block">设置触发手机卫星定位信号弱的时间间隔，默认为10秒。
 表示有连续的10s，卫星定位信号质量都比较差，就会触发手机卫星定位信号弱的回调。值越小，就越容易触发，
 支持的时间间隔范围为5～15秒</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>delayTime</code> - 时间间隔，单位为毫秒</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.6.0</dd>
</dl>
</li>
</ul>
<a name="stopSpeak--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>stopSpeak</h4>
<pre>public&nbsp;void&nbsp;stopSpeak()</pre>
<div class="block">停止内置语音播报，只有在使用内置语音的情况下有效
 注意：7.1.0版本以后，调用该接口，会停止播放导航语音，但是仍然可以播放自定义语音</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.4.0</dd>
</dl>
</li>
</ul>
<a name="startSpeak--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>startSpeak</h4>
<pre>public&nbsp;void&nbsp;startSpeak()</pre>
<div class="block">开始内置语音播报，只有在使用内置语音的情况下才有效</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.4.0</dd>
</dl>
</li>
</ul>
<a name="setAMapNaviOnlineCarHailingType-com.amap.api.navi.enums.AMapNaviOnlineCarHailingType-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAMapNaviOnlineCarHailingType</h4>
<pre>public&nbsp;void&nbsp;setAMapNaviOnlineCarHailingType(<a href="../../../../com/amap/api/navi/enums/AMapNaviOnlineCarHailingType.html" title="com.amap.api.navi.enums中的枚举">AMapNaviOnlineCarHailingType</a>&nbsp;type)</pre>
<div class="block">设置网约车导航模式，包括非网约车模式，网约车接驾模式、网约车送驾模式，默认为非网约车模式
 注意：<font color='red'>该接口仅驾车模式有效</font></div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>type</code> - <a href="../../../../com/amap/api/navi/enums/AMapNaviOnlineCarHailingType.html" title="com.amap.api.navi.enums中的枚举"><code>AMapNaviOnlineCarHailingType</code></a></dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.4.0</dd>
</dl>
</li>
</ul>
<a name="setNetWorkingProtocol-com.amap.api.navi.enums.NetWorkingProtocol-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setNetWorkingProtocol</h4>
<pre>public static&nbsp;void&nbsp;setNetWorkingProtocol(<a href="../../../../com/amap/api/navi/enums/NetWorkingProtocol.html" title="com.amap.api.navi.enums中的枚举">NetWorkingProtocol</a>&nbsp;protocol)</pre>
<div class="block">设置网络请求协议
 <p>
 注意，该接口仅控制导航业务相关的网络请求，不影响定位、地图sdk内部的请求。
 </p></div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>protocol</code> - 请求协议枚举，详情见 <a href="../../../../com/amap/api/navi/enums/NetWorkingProtocol.html" title="com.amap.api.navi.enums中的枚举"><code>NetWorkingProtocol</code></a></dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.4.0</dd>
</dl>
</li>
</ul>
<a name="playTTS-java.lang.String-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>playTTS</h4>
<pre>public&nbsp;boolean&nbsp;playTTS(java.lang.String&nbsp;tts,
                       boolean&nbsp;forcePlay)</pre>
<div class="block">播放自定义文字，注意如果当前正在播放导航语音，可能导致播放失败，只在内置语音下生效</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>tts</code> - 要播放的文字</dd>
<dd><code>forcePlay</code> - 是否强制进行播报
                  true  如果当前有其他导航语音，会等导航语音播放完毕后播放该文字，但是可能导致导航丢失掉关键引导信息
                  false 如果当前有其他导航语音，则不播报</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>播放是否成功 true 成功，false 失败</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.7.0</dd>
</dl>
</li>
</ul>
<a name="getDeviceId-Context-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDeviceId</h4>
<pre>public static&nbsp;java.lang.String&nbsp;getDeviceId(Context&nbsp;context)</pre>
<div class="block">获取设备唯一ID，使用导航SDK的时候建议使用这个作为设备唯一ID</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>context</code> - </dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>设备唯一ID</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.8.0</dd>
</dl>
</li>
</ul>
<a name="independentCalculateRoute-com.amap.api.navi.model.NaviPoi-com.amap.api.navi.model.NaviPoi-java.util.List-int-int-com.amap.api.navi.AMapNaviIndependentRouteListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>independentCalculateRoute</h4>
<pre>public&nbsp;boolean&nbsp;independentCalculateRoute(<a href="../../../../com/amap/api/navi/model/NaviPoi.html" title="com.amap.api.navi.model中的类">NaviPoi</a>&nbsp;fromPoi,
                                         <a href="../../../../com/amap/api/navi/model/NaviPoi.html" title="com.amap.api.navi.model中的类">NaviPoi</a>&nbsp;toPoi,
                                         java.util.List&lt;<a href="../../../../com/amap/api/navi/model/NaviPoi.html" title="com.amap.api.navi.model中的类">NaviPoi</a>&gt;&nbsp;wayPoints,
                                         int&nbsp;strategy,
                                         int&nbsp;transportType,
                                         <a href="../../../../com/amap/api/navi/AMapNaviIndependentRouteListener.html" title="com.amap.api.navi中的接口">AMapNaviIndependentRouteListener</a>&nbsp;observer)</pre>
<div class="block">POI独立算路 SDK9.4.0版本开始支持骑步行途径点算路，骑步行添加途径点算路为付费接口。您如果申请试用或者正式应用都请通过工单系统提交商务合作类工单进行沟通 https://lbs.amap.com/。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>fromPoi</code> - 起点信息</dd>
<dd><code>toPoi</code> - 终点信息</dd>
<dd><code>wayPoints</code> - 途经点信息，最多支持16个途经点，超过16个会取前16个。如果POIID合法，优先使用ID参与算路，否则使用坐标点。注意:POIID和坐标点不能同时为空</dd>
<dd><code>strategy</code> - 算路策略: 驾车参考 <a href="../../../../com/amap/api/navi/enums/PathPlanningStrategy.html" title="com.amap.api.navi.enums中的类"><code>PathPlanningStrategy</code></a>， 骑步行参考 <a href="../../../../com/amap/api/navi/enums/TravelStrategy.html" title="com.amap.api.navi.enums中的枚举"><code>TravelStrategy</code></a></dd>
<dd><code>transportType</code> - 交通类型 1-驾车 2-骑行 3-步行</dd>
<dd><code>observer</code> - 算路结果监听器</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>规划路径所需条件和参数校验是否成功，不代表算路成功与否</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>7.7.0</dd>
</dl>
</li>
</ul>
<a name="getRestrictareaInfo-com.amap.api.navi.model.AMapNaviPath-com.amap.api.navi.AMapNaviRestrictAreaInfoListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRestrictareaInfo</h4>
<pre>public&nbsp;boolean&nbsp;getRestrictareaInfo(<a href="../../../../com/amap/api/navi/model/AMapNaviPath.html" title="com.amap.api.navi.model中的类">AMapNaviPath</a>&nbsp;path,
                                   <a href="../../../../com/amap/api/navi/AMapNaviRestrictAreaInfoListener.html" title="com.amap.api.navi中的接口">AMapNaviRestrictAreaInfoListener</a>&nbsp;observer)</pre>
<div class="block">获取现行信息数据</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>path</code> - 路线信息</dd>
<dd><code>observer</code> - 路线现行信息结果监听器</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>获取限行条件是否满足；成功发起数据请求 true，否则的话则返回false；不代表获取限行数据成功与否</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.0.1</dd>
</dl>
</li>
</ul>
<a name="setTrafficStatusUpdateEnabled-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTrafficStatusUpdateEnabled</h4>
<pre>public&nbsp;void&nbsp;setTrafficStatusUpdateEnabled(boolean&nbsp;enable)</pre>
<div class="block">设置交通状态是否更新
 <p/>
 需要动态交通信息相关功能时需要调用此接口打开交通信息功能，默认是开启的，
 打开后会每1分钟请求一次新的交通信息来更新交通信息。关闭动态交通信息后使用交通信息的相关信息的功能将无法正常使用，包括路况刷新、避免拥堵重算等
 <p/></div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>enable</code> - true-更新路况，false-不更新路况</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>7.9.0</dd>
</dl>
</li>
</ul>
<a name="setTrafficInfoUpdateEnabled-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTrafficInfoUpdateEnabled</h4>
<pre>public&nbsp;void&nbsp;setTrafficInfoUpdateEnabled(boolean&nbsp;enable)</pre>
<div class="block">设置是否打开移动交通台功能
 <p/>
 默认是开启的，打开此功能后将有整体路况概览与前方路况播报功能，打开此功能后TBT会基本1分钟请求
 一次前方路况（此时间间隔不是固定的）。关闭交通台后将没有路况播报功能
 <p/></div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>enable</code> - true-播报前方路况，false-不播报前方路况</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>7.9.0</dd>
</dl>
</li>
</ul>
<a name="setCameraInfoUpdateEnabled-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCameraInfoUpdateEnabled</h4>
<pre>public&nbsp;void&nbsp;setCameraInfoUpdateEnabled(boolean&nbsp;enable)</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;<span class="deprecationComment">已废弃，新引擎机制不再支持单播报模块控制</span></div>
<div class="block">设置电子眼播报是否开启
 <p/>
 初始电子眼播报是打开的，当电子眼播报打开时，电子眼数据中有限速信息时，同时会播报限速信息
 <p/></div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>enable</code> - true-播报前方电子眼，false-不播报前方电子眼</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>7.9.0</dd>
</dl>
</li>
</ul>
<a name="setServiceAreaDetailsEnable-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setServiceAreaDetailsEnable</h4>
<pre>public&nbsp;void&nbsp;setServiceAreaDetailsEnable(boolean&nbsp;enable)</pre>
<div class="block">设置服务区详情是否打开，打开后组件会显示服务区UI，API会返回服务区类型信息</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>enable</code> - </dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>8.0.0</dd>
</dl>
</li>
</ul>
<a name="getServiceAreaDetailsEnable--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getServiceAreaDetailsEnable</h4>
<pre>public&nbsp;boolean&nbsp;getServiceAreaDetailsEnable()</pre>
<div class="block">是否开启服务区详情开关</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>8.0.0</dd>
</dl>
</li>
</ul>
<a name="setListenToVoiceDuringCall-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setListenToVoiceDuringCall</h4>
<pre>public&nbsp;void&nbsp;setListenToVoiceDuringCall(boolean&nbsp;isListenToVoiceDuringCall)</pre>
<div class="block">设置在通话过程中是否进行导航播报，只有在使用内置语音时生效</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>isListenToVoiceDuringCall</code> - </dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>8.0.1</dd>
</dl>
</li>
</ul>
<a name="getListenToVoiceDuringCall--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getListenToVoiceDuringCall</h4>
<pre>public&nbsp;boolean&nbsp;getListenToVoiceDuringCall()</pre>
<div class="block">获取当前通话过程中是否进行导航播报(只在使用内置语音时生效)</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>true：通话过程中播报导航语音；false：通话过程中不播报导航语音</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>8.0.1</dd>
</dl>
</li>
</ul>
<a name="setControlMusicVolumeMode-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setControlMusicVolumeMode</h4>
<pre>public&nbsp;void&nbsp;setControlMusicVolumeMode(int&nbsp;controlMusicVolumeMode)</pre>
<div class="block">设置导航播报时压低音乐、暂停音乐，只有在使用内置语音时生效（Android8.0以下系统不支持暂停音乐，设置暂停音乐无效）</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>controlMusicVolumeMode</code> - 0：压低音乐； 1：暂停音乐 <a href="../../../../com/amap/api/navi/enums/AMapNaviControlMusicVolumeMode.html" title="com.amap.api.navi.enums中的类"><code>AMapNaviControlMusicVolumeMode</code></a></dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>8.0.1</dd>
</dl>
</li>
</ul>
<a name="getControlMusicVolumeMode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getControlMusicVolumeMode</h4>
<pre>public&nbsp;int&nbsp;getControlMusicVolumeMode()</pre>
<div class="block">获取导航语音播报混合音状态</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>0：压低音乐； 1：暂停音乐 <a href="../../../../com/amap/api/navi/enums/AMapNaviControlMusicVolumeMode.html" title="com.amap.api.navi.enums中的类"><code>AMapNaviControlMusicVolumeMode</code></a></dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>8.0.1</dd>
</dl>
</li>
</ul>
<a name="setNaviNetworkProxy-com.amap.api.navi.AMapNaviNetWorkProxy-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setNaviNetworkProxy</h4>
<pre>public&nbsp;void&nbsp;setNaviNetworkProxy(<a href="../../../../com/amap/api/navi/AMapNaviNetWorkProxy.html" title="com.amap.api.navi中的接口">AMapNaviNetWorkProxy</a>&nbsp;proxy)</pre>
<div class="block">设置导航外部网络代理类</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>10.0.1</dd>
</dl>
</li>
</ul>
<a name="setIsNaviTravelView-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setIsNaviTravelView</h4>
<pre>public&nbsp;void&nbsp;setIsNaviTravelView(boolean&nbsp;isTravel)</pre>
<div class="block">设置是否为骑步行视图</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>10.0.1</dd>
</dl>
</li>
</ul>
<a name="getIsNaviTravelView--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getIsNaviTravelView</h4>
<pre>public&nbsp;boolean&nbsp;getIsNaviTravelView()</pre>
</li>
</ul>
<a name="setInnerOptRecordEnable-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setInnerOptRecordEnable</h4>
<pre>public&nbsp;void&nbsp;setInnerOptRecordEnable(java.lang.String&nbsp;key)</pre>
<div class="block">【二环专用】设置是否启用OPT日志记录
  * @param key</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>10.0.1</dd>
</dl>
</li>
</ul>
<a name="setIsOpenTrafficLight-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setIsOpenTrafficLight</h4>
<pre>public&nbsp;void&nbsp;setIsOpenTrafficLight(java.lang.String&nbsp;key)</pre>
<div class="block">设置是否显示红绿灯倒计时
  * @param key</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>10.0.700</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/AMapNavi.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/amap/api/navi/AMapHudViewListener.html" title="com.amap.api.navi中的接口"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../com/amap/api/navi/AMapNaviDriveListener.html" title="com.amap.api.navi中的接口"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/amap/api/navi/AMapNavi.html" target="_top">框架</a></li>
<li><a href="AMapNavi.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
