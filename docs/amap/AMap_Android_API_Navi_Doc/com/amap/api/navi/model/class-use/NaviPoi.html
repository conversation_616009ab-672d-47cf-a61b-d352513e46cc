<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>类 com.amap.api.navi.model.NaviPoi的使用</title>
<link rel="stylesheet" type="text/css" href="../../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="\u7C7B com.amap.api.navi.model.NaviPoi\u7684\u4F7F\u7528";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../../overview-summary.html">概览</a></li>
<li><a href="../package-summary.html">程序包</a></li>
<li><a href="../../../../../../com/amap/api/navi/model/NaviPoi.html" title="com.amap.api.navi.model中的类">类</a></li>
<li class="navBarCell1Rev">使用</li>
<li><a href="../../../../../../overview-tree.html">树</a></li>
<li><a href="../../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个</li>
<li>下一个</li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?com/amap/api/navi/model/class-use/NaviPoi.html" target="_top">框架</a></li>
<li><a href="NaviPoi.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h2 title="类的使用 com.amap.api.navi.model.NaviPoi" class="title">类的使用<br>com.amap.api.navi.model.NaviPoi</h2>
</div>
<div class="classUseContainer">
<ul class="blockList">
<li class="blockList">
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表程序包和解释">
<caption><span>使用<a href="../../../../../../com/amap/api/navi/model/NaviPoi.html" title="com.amap.api.navi.model中的类">NaviPoi</a>的程序包</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">程序包</th>
<th class="colLast" scope="col">说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="#com.amap.api.navi">com.amap.api.navi</a></td>
<td class="colLast">
<div class="block">
    导航基础包，提供路径规划、导航等基础功能。</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<ul class="blockList">
<li class="blockList"><a name="com.amap.api.navi">
<!--   -->
</a>
<h3><a href="../../../../../../com/amap/api/navi/package-summary.html">com.amap.api.navi</a>中<a href="../../../../../../com/amap/api/navi/model/NaviPoi.html" title="com.amap.api.navi.model中的类">NaviPoi</a>的使用</h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表方法和解释">
<caption><span>参数类型为<a href="../../../../../../com/amap/api/navi/model/NaviPoi.html" title="com.amap.api.navi.model中的类">NaviPoi</a>的<a href="../../../../../../com/amap/api/navi/package-summary.html">com.amap.api.navi</a>中的方法</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="typeNameLabel">AMapNavi.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/navi/AMapNavi.html#calculateDriveRoute-com.amap.api.navi.model.NaviPoi-com.amap.api.navi.model.NaviPoi-java.util.List-int-">calculateDriveRoute</a></span>(<a href="../../../../../../com/amap/api/navi/model/NaviPoi.html" title="com.amap.api.navi.model中的类">NaviPoi</a>&nbsp;fromPoi,
                   <a href="../../../../../../com/amap/api/navi/model/NaviPoi.html" title="com.amap.api.navi.model中的类">NaviPoi</a>&nbsp;toPoi,
                   java.util.List&lt;<a href="../../../../../../com/amap/api/navi/model/NaviPoi.html" title="com.amap.api.navi.model中的类">NaviPoi</a>&gt;&nbsp;wayPoints,
                   int&nbsp;strategy)</code>
<div class="block">驾车POI算路
 
 该方法可以避免到达点问题导致的绕路，地铁等多出口类POI，也会导航到多个出口。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="typeNameLabel">AMapNavi.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/navi/AMapNavi.html#calculateEleBikeRoute-com.amap.api.navi.model.NaviPoi-com.amap.api.navi.model.NaviPoi-com.amap.api.navi.enums.TravelStrategy-">calculateEleBikeRoute</a></span>(<a href="../../../../../../com/amap/api/navi/model/NaviPoi.html" title="com.amap.api.navi.model中的类">NaviPoi</a>&nbsp;fromPoi,
                     <a href="../../../../../../com/amap/api/navi/model/NaviPoi.html" title="com.amap.api.navi.model中的类">NaviPoi</a>&nbsp;toPoi,
                     <a href="../../../../../../com/amap/api/navi/enums/TravelStrategy.html" title="com.amap.api.navi.enums中的枚举">TravelStrategy</a>&nbsp;strategy)</code>
<div class="block">骑行（电动车）POI算路</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="typeNameLabel">AMapNavi.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/navi/AMapNavi.html#calculateRideRoute-com.amap.api.navi.model.NaviPoi-java.util.List-com.amap.api.navi.model.NaviPoi-com.amap.api.navi.enums.TravelStrategy-">calculateRideRoute</a></span>(<a href="../../../../../../com/amap/api/navi/model/NaviPoi.html" title="com.amap.api.navi.model中的类">NaviPoi</a>&nbsp;fromPoi,
                  java.util.List&lt;<a href="../../../../../../com/amap/api/navi/model/NaviPoi.html" title="com.amap.api.navi.model中的类">NaviPoi</a>&gt;&nbsp;wayPoints,
                  <a href="../../../../../../com/amap/api/navi/model/NaviPoi.html" title="com.amap.api.navi.model中的类">NaviPoi</a>&nbsp;toPoi,
                  <a href="../../../../../../com/amap/api/navi/enums/TravelStrategy.html" title="com.amap.api.navi.enums中的枚举">TravelStrategy</a>&nbsp;strategy)</code>
<div class="block">骑行（自行车）POI算路 特别注意：当前接口为收费接口，您如果申请试用或者正式应用都请通过工单系统提交商务合作类工单进行沟通 https://lbs.amap.com/</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="typeNameLabel">AMapNavi.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/navi/AMapNavi.html#calculateRideRoute-com.amap.api.navi.model.NaviPoi-com.amap.api.navi.model.NaviPoi-com.amap.api.navi.enums.TravelStrategy-">calculateRideRoute</a></span>(<a href="../../../../../../com/amap/api/navi/model/NaviPoi.html" title="com.amap.api.navi.model中的类">NaviPoi</a>&nbsp;fromPoi,
                  <a href="../../../../../../com/amap/api/navi/model/NaviPoi.html" title="com.amap.api.navi.model中的类">NaviPoi</a>&nbsp;toPoi,
                  <a href="../../../../../../com/amap/api/navi/enums/TravelStrategy.html" title="com.amap.api.navi.enums中的枚举">TravelStrategy</a>&nbsp;strategy)</code>
<div class="block">骑行（自行车）POI算路</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="typeNameLabel">AMapNavi.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/navi/AMapNavi.html#calculateWalkRoute-com.amap.api.navi.model.NaviPoi-java.util.List-com.amap.api.navi.model.NaviPoi-com.amap.api.navi.enums.TravelStrategy-">calculateWalkRoute</a></span>(<a href="../../../../../../com/amap/api/navi/model/NaviPoi.html" title="com.amap.api.navi.model中的类">NaviPoi</a>&nbsp;fromPoi,
                  java.util.List&lt;<a href="../../../../../../com/amap/api/navi/model/NaviPoi.html" title="com.amap.api.navi.model中的类">NaviPoi</a>&gt;&nbsp;wayPoints,
                  <a href="../../../../../../com/amap/api/navi/model/NaviPoi.html" title="com.amap.api.navi.model中的类">NaviPoi</a>&nbsp;toPoi,
                  <a href="../../../../../../com/amap/api/navi/enums/TravelStrategy.html" title="com.amap.api.navi.enums中的枚举">TravelStrategy</a>&nbsp;strategy)</code>
<div class="block">步行POI算路 特别注意：当前接口为收费接口，您如果申请试用或者正式应用都请通过工单系统提交商务合作类工单进行沟通 https://lbs.amap.com/。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="typeNameLabel">AMapNavi.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/navi/AMapNavi.html#calculateWalkRoute-com.amap.api.navi.model.NaviPoi-com.amap.api.navi.model.NaviPoi-com.amap.api.navi.enums.TravelStrategy-">calculateWalkRoute</a></span>(<a href="../../../../../../com/amap/api/navi/model/NaviPoi.html" title="com.amap.api.navi.model中的类">NaviPoi</a>&nbsp;fromPoi,
                  <a href="../../../../../../com/amap/api/navi/model/NaviPoi.html" title="com.amap.api.navi.model中的类">NaviPoi</a>&nbsp;toPoi,
                  <a href="../../../../../../com/amap/api/navi/enums/TravelStrategy.html" title="com.amap.api.navi.enums中的枚举">TravelStrategy</a>&nbsp;strategy)</code>
<div class="block">步行POI算路</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="typeNameLabel">AMapNavi.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/navi/AMapNavi.html#independentCalculateRoute-com.amap.api.navi.model.NaviPoi-com.amap.api.navi.model.NaviPoi-java.util.List-int-int-com.amap.api.navi.AMapNaviIndependentRouteListener-">independentCalculateRoute</a></span>(<a href="../../../../../../com/amap/api/navi/model/NaviPoi.html" title="com.amap.api.navi.model中的类">NaviPoi</a>&nbsp;fromPoi,
                         <a href="../../../../../../com/amap/api/navi/model/NaviPoi.html" title="com.amap.api.navi.model中的类">NaviPoi</a>&nbsp;toPoi,
                         java.util.List&lt;<a href="../../../../../../com/amap/api/navi/model/NaviPoi.html" title="com.amap.api.navi.model中的类">NaviPoi</a>&gt;&nbsp;wayPoints,
                         int&nbsp;strategy,
                         int&nbsp;transportType,
                         <a href="../../../../../../com/amap/api/navi/AMapNaviIndependentRouteListener.html" title="com.amap.api.navi中的接口">AMapNaviIndependentRouteListener</a>&nbsp;observer)</code>
<div class="block">POI独立算路 SDK9.4.0版本开始支持骑步行途径点算路，骑步行添加途径点算路为付费接口。</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表方法和解释">
<caption><span>类型变量类型为<a href="../../../../../../com/amap/api/navi/model/NaviPoi.html" title="com.amap.api.navi.model中的类">NaviPoi</a>的<a href="../../../../../../com/amap/api/navi/package-summary.html">com.amap.api.navi</a>中的方法参数</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="typeNameLabel">AMapNavi.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/navi/AMapNavi.html#calculateDriveRoute-com.amap.api.navi.model.NaviPoi-com.amap.api.navi.model.NaviPoi-java.util.List-int-">calculateDriveRoute</a></span>(<a href="../../../../../../com/amap/api/navi/model/NaviPoi.html" title="com.amap.api.navi.model中的类">NaviPoi</a>&nbsp;fromPoi,
                   <a href="../../../../../../com/amap/api/navi/model/NaviPoi.html" title="com.amap.api.navi.model中的类">NaviPoi</a>&nbsp;toPoi,
                   java.util.List&lt;<a href="../../../../../../com/amap/api/navi/model/NaviPoi.html" title="com.amap.api.navi.model中的类">NaviPoi</a>&gt;&nbsp;wayPoints,
                   int&nbsp;strategy)</code>
<div class="block">驾车POI算路
 
 该方法可以避免到达点问题导致的绕路，地铁等多出口类POI，也会导航到多个出口。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="typeNameLabel">AMapNavi.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/navi/AMapNavi.html#calculateRideRoute-com.amap.api.navi.model.NaviPoi-java.util.List-com.amap.api.navi.model.NaviPoi-com.amap.api.navi.enums.TravelStrategy-">calculateRideRoute</a></span>(<a href="../../../../../../com/amap/api/navi/model/NaviPoi.html" title="com.amap.api.navi.model中的类">NaviPoi</a>&nbsp;fromPoi,
                  java.util.List&lt;<a href="../../../../../../com/amap/api/navi/model/NaviPoi.html" title="com.amap.api.navi.model中的类">NaviPoi</a>&gt;&nbsp;wayPoints,
                  <a href="../../../../../../com/amap/api/navi/model/NaviPoi.html" title="com.amap.api.navi.model中的类">NaviPoi</a>&nbsp;toPoi,
                  <a href="../../../../../../com/amap/api/navi/enums/TravelStrategy.html" title="com.amap.api.navi.enums中的枚举">TravelStrategy</a>&nbsp;strategy)</code>
<div class="block">骑行（自行车）POI算路 特别注意：当前接口为收费接口，您如果申请试用或者正式应用都请通过工单系统提交商务合作类工单进行沟通 https://lbs.amap.com/</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="typeNameLabel">AMapNavi.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/navi/AMapNavi.html#calculateWalkRoute-com.amap.api.navi.model.NaviPoi-java.util.List-com.amap.api.navi.model.NaviPoi-com.amap.api.navi.enums.TravelStrategy-">calculateWalkRoute</a></span>(<a href="../../../../../../com/amap/api/navi/model/NaviPoi.html" title="com.amap.api.navi.model中的类">NaviPoi</a>&nbsp;fromPoi,
                  java.util.List&lt;<a href="../../../../../../com/amap/api/navi/model/NaviPoi.html" title="com.amap.api.navi.model中的类">NaviPoi</a>&gt;&nbsp;wayPoints,
                  <a href="../../../../../../com/amap/api/navi/model/NaviPoi.html" title="com.amap.api.navi.model中的类">NaviPoi</a>&nbsp;toPoi,
                  <a href="../../../../../../com/amap/api/navi/enums/TravelStrategy.html" title="com.amap.api.navi.enums中的枚举">TravelStrategy</a>&nbsp;strategy)</code>
<div class="block">步行POI算路 特别注意：当前接口为收费接口，您如果申请试用或者正式应用都请通过工单系统提交商务合作类工单进行沟通 https://lbs.amap.com/。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="typeNameLabel">AMapNavi.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/navi/AMapNavi.html#independentCalculateRoute-com.amap.api.navi.model.NaviPoi-com.amap.api.navi.model.NaviPoi-java.util.List-int-int-com.amap.api.navi.AMapNaviIndependentRouteListener-">independentCalculateRoute</a></span>(<a href="../../../../../../com/amap/api/navi/model/NaviPoi.html" title="com.amap.api.navi.model中的类">NaviPoi</a>&nbsp;fromPoi,
                         <a href="../../../../../../com/amap/api/navi/model/NaviPoi.html" title="com.amap.api.navi.model中的类">NaviPoi</a>&nbsp;toPoi,
                         java.util.List&lt;<a href="../../../../../../com/amap/api/navi/model/NaviPoi.html" title="com.amap.api.navi.model中的类">NaviPoi</a>&gt;&nbsp;wayPoints,
                         int&nbsp;strategy,
                         int&nbsp;transportType,
                         <a href="../../../../../../com/amap/api/navi/AMapNaviIndependentRouteListener.html" title="com.amap.api.navi中的接口">AMapNaviIndependentRouteListener</a>&nbsp;observer)</code>
<div class="block">POI独立算路 SDK9.4.0版本开始支持骑步行途径点算路，骑步行添加途径点算路为付费接口。</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../../overview-summary.html">概览</a></li>
<li><a href="../package-summary.html">程序包</a></li>
<li><a href="../../../../../../com/amap/api/navi/model/NaviPoi.html" title="com.amap.api.navi.model中的类">类</a></li>
<li class="navBarCell1Rev">使用</li>
<li><a href="../../../../../../overview-tree.html">树</a></li>
<li><a href="../../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个</li>
<li>下一个</li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?com/amap/api/navi/model/class-use/NaviPoi.html" target="_top">框架</a></li>
<li><a href="NaviPoi.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
