<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>com.amap.api.navi.model 类分层结构</title>
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="com.amap.api.navi.model \u7C7B\u5206\u5C42\u7ED3\u6784";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li>类</li>
<li>使用</li>
<li class="navBarCell1Rev">树</li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/navi/enums/package-tree.html">上一个</a></li>
<li><a href="../../../../../com/amap/api/navi/view/package-tree.html">下一个</a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/navi/model/package-tree.html" target="_top">框架</a></li>
<li><a href="package-tree.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 class="title">程序包com.amap.api.navi.model的分层结构</h1>
<span class="packageHierarchyLabel">程序包分层结构:</span>
<ul class="horizontal">
<li><a href="../../../../../overview-tree.html">所有程序包</a></li>
</ul>
</div>
<div class="contentContainer">
<h2 title="类分层结构">类分层结构</h2>
<ul>
<li type="circle">java.lang.Object
<ul>
<li type="circle">com.amap.api.navi.model.<a href="../../../../../com/amap/api/navi/model/AimLessModeCongestionInfo.html" title="com.amap.api.navi.model中的类"><span class="typeNameLink">AimLessModeCongestionInfo</span></a></li>
<li type="circle">com.amap.api.navi.model.<a href="../../../../../com/amap/api/navi/model/AimLessModeStat.html" title="com.amap.api.navi.model中的类"><span class="typeNameLink">AimLessModeStat</span></a></li>
<li type="circle">com.amap.api.navi.model.<a href="../../../../../com/amap/api/navi/model/AMapCalcRouteResult.html" title="com.amap.api.navi.model中的类"><span class="typeNameLink">AMapCalcRouteResult</span></a></li>
<li type="circle">com.amap.api.navi.model.<a href="../../../../../com/amap/api/navi/model/AMapCarInfo.html" title="com.amap.api.navi.model中的类"><span class="typeNameLink">AMapCarInfo</span></a></li>
<li type="circle">com.amap.api.navi.model.<a href="../../../../../com/amap/api/navi/model/AMapCongestionLink.html" title="com.amap.api.navi.model中的类"><span class="typeNameLink">AMapCongestionLink</span></a></li>
<li type="circle">com.amap.api.navi.model.<a href="../../../../../com/amap/api/navi/model/AMapExitDirectionInfo.html" title="com.amap.api.navi.model中的类"><span class="typeNameLink">AMapExitDirectionInfo</span></a></li>
<li type="circle">com.amap.api.navi.model.<a href="../../../../../com/amap/api/navi/model/AMapLaneInfo.html" title="com.amap.api.navi.model中的类"><span class="typeNameLink">AMapLaneInfo</span></a></li>
<li type="circle">com.amap.api.navi.model.<a href="../../../../../com/amap/api/navi/model/AMapModelCross.html" title="com.amap.api.navi.model中的类"><span class="typeNameLink">AMapModelCross</span></a></li>
<li type="circle">com.amap.api.navi.model.<a href="../../../../../com/amap/api/navi/model/AMapNaviCameraInfo.html" title="com.amap.api.navi.model中的类"><span class="typeNameLink">AMapNaviCameraInfo</span></a></li>
<li type="circle">com.amap.api.navi.model.<a href="../../../../../com/amap/api/navi/model/AMapNaviCross.html" title="com.amap.api.navi.model中的类"><span class="typeNameLink">AMapNaviCross</span></a></li>
<li type="circle">com.amap.api.navi.model.<a href="../../../../../com/amap/api/navi/model/AMapNaviDriveComfort.html" title="com.amap.api.navi.model中的类"><span class="typeNameLink">AMapNaviDriveComfort</span></a></li>
<li type="circle">com.amap.api.navi.model.<a href="../../../../../com/amap/api/navi/model/AMapNaviDriveEvent.html" title="com.amap.api.navi.model中的类"><span class="typeNameLink">AMapNaviDriveEvent</span></a></li>
<li type="circle">com.amap.api.navi.model.<a href="../../../../../com/amap/api/navi/model/AMapNaviForbiddenInfo.html" title="com.amap.api.navi.model中的类"><span class="typeNameLink">AMapNaviForbiddenInfo</span></a></li>
<li type="circle">com.amap.api.navi.model.<a href="../../../../../com/amap/api/navi/model/AMapNaviGuide.html" title="com.amap.api.navi.model中的类"><span class="typeNameLink">AMapNaviGuide</span></a></li>
<li type="circle">com.amap.api.navi.model.<a href="../../../../../com/amap/api/navi/model/AMapNaviLimitInfo.html" title="com.amap.api.navi.model中的类"><span class="typeNameLink">AMapNaviLimitInfo</span></a></li>
<li type="circle">com.amap.api.navi.model.<a href="../../../../../com/amap/api/navi/model/AMapNaviLink.html" title="com.amap.api.navi.model中的类"><span class="typeNameLink">AMapNaviLink</span></a></li>
<li type="circle">com.amap.api.navi.model.<a href="../../../../../com/amap/api/navi/model/AMapNaviLocation.html" title="com.amap.api.navi.model中的类"><span class="typeNameLink">AMapNaviLocation</span></a></li>
<li type="circle">com.amap.api.navi.model.<a href="../../../../../com/amap/api/navi/model/AMapNaviPath.html" title="com.amap.api.navi.model中的类"><span class="typeNameLink">AMapNaviPath</span></a></li>
<li type="circle">com.amap.api.navi.model.<a href="../../../../../com/amap/api/navi/model/AMapNaviPathGroup.html" title="com.amap.api.navi.model中的类"><span class="typeNameLink">AMapNaviPathGroup</span></a></li>
<li type="circle">com.amap.api.navi.model.<a href="../../../../../com/amap/api/navi/model/AMapNaviRouteGuideGroup.html" title="com.amap.api.navi.model中的类"><span class="typeNameLink">AMapNaviRouteGuideGroup</span></a></li>
<li type="circle">com.amap.api.navi.model.<a href="../../../../../com/amap/api/navi/model/AMapNaviRouteGuideSegment.html" title="com.amap.api.navi.model中的类"><span class="typeNameLink">AMapNaviRouteGuideSegment</span></a></li>
<li type="circle">com.amap.api.navi.model.<a href="../../../../../com/amap/api/navi/model/AMapNaviRouteNotifyData.html" title="com.amap.api.navi.model中的类"><span class="typeNameLink">AMapNaviRouteNotifyData</span></a></li>
<li type="circle">com.amap.api.navi.model.<a href="../../../../../com/amap/api/navi/model/AMapNaviStep.html" title="com.amap.api.navi.model中的类"><span class="typeNameLink">AMapNaviStep</span></a></li>
<li type="circle">com.amap.api.navi.model.<a href="../../../../../com/amap/api/navi/model/AMapNaviToViaInfo.html" title="com.amap.api.navi.model中的类"><span class="typeNameLink">AMapNaviToViaInfo</span></a></li>
<li type="circle">com.amap.api.navi.model.<a href="../../../../../com/amap/api/navi/model/AMapNaviTrafficFacilityInfo.html" title="com.amap.api.navi.model中的类"><span class="typeNameLink">AMapNaviTrafficFacilityInfo</span></a></li>
<li type="circle">com.amap.api.navi.model.<a href="../../../../../com/amap/api/navi/model/AMapNotAvoidInfo.html" title="com.amap.api.navi.model中的类"><span class="typeNameLink">AMapNotAvoidInfo</span></a></li>
<li type="circle">com.amap.api.navi.model.<a href="../../../../../com/amap/api/navi/model/AMapRestrictionInfo.html" title="com.amap.api.navi.model中的类"><span class="typeNameLink">AMapRestrictionInfo</span></a></li>
<li type="circle">com.amap.api.navi.model.<a href="../../../../../com/amap/api/navi/model/AMapServiceAreaInfo.html" title="com.amap.api.navi.model中的类"><span class="typeNameLink">AMapServiceAreaInfo</span></a></li>
<li type="circle">com.amap.api.navi.model.<a href="../../../../../com/amap/api/navi/model/AMapTrafficIncidentInfo.html" title="com.amap.api.navi.model中的类"><span class="typeNameLink">AMapTrafficIncidentInfo</span></a></li>
<li type="circle">com.amap.api.navi.model.<a href="../../../../../com/amap/api/navi/model/AMapTrafficStatus.html" title="com.amap.api.navi.model中的类"><span class="typeNameLink">AMapTrafficStatus</span></a></li>
<li type="circle">com.amap.api.navi.model.<a href="../../../../../com/amap/api/navi/model/AMapTravelInfo.html" title="com.amap.api.navi.model中的类"><span class="typeNameLink">AMapTravelInfo</span></a></li>
<li type="circle">com.amap.api.navi.model.<a href="../../../../../com/amap/api/navi/model/CustomEyrieRouteConfig.html" title="com.amap.api.navi.model中的类"><span class="typeNameLink">CustomEyrieRouteConfig</span></a></li>
<li type="circle">com.amap.api.navi.model.<a href="../../../../../com/amap/api/navi/model/IndependInfo.html" title="com.amap.api.navi.model中的类"><span class="typeNameLink">IndependInfo</span></a></li>
<li type="circle">com.amap.api.navi.model.<a href="../../../../../com/amap/api/navi/model/NaviCongestionInfo.html" title="com.amap.api.navi.model中的类"><span class="typeNameLink">NaviCongestionInfo</span></a></li>
<li type="circle">com.amap.api.navi.model.<a href="../../../../../com/amap/api/navi/model/NaviInfo.html" title="com.amap.api.navi.model中的类"><span class="typeNameLink">NaviInfo</span></a></li>
<li type="circle">com.amap.api.navi.model.<a href="../../../../../com/amap/api/navi/model/NaviLatLng.html" title="com.amap.api.navi.model中的类"><span class="typeNameLink">NaviLatLng</span></a></li>
<li type="circle">com.amap.api.navi.model.<a href="../../../../../com/amap/api/navi/model/NaviPoi.html" title="com.amap.api.navi.model中的类"><span class="typeNameLink">NaviPoi</span></a></li>
<li type="circle">com.amap.api.navi.model.<a href="../../../../../com/amap/api/navi/model/RouteOverlayOptions.html" title="com.amap.api.navi.model中的类"><span class="typeNameLink">RouteOverlayOptions</span></a></li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li>类</li>
<li>使用</li>
<li class="navBarCell1Rev">树</li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/navi/enums/package-tree.html">上一个</a></li>
<li><a href="../../../../../com/amap/api/navi/view/package-tree.html">下一个</a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/navi/model/package-tree.html" target="_top">框架</a></li>
<li><a href="package-tree.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
