<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>已过时的列表</title>
<link rel="stylesheet" type="text/css" href="stylesheet.css" title="Style">
<script type="text/javascript" src="script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="\u5DF2\u8FC7\u65F6\u7684\u5217\u8868";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="overview-tree.html">树</a></li>
<li class="navBarCell1Rev">已过时</li>
<li><a href="index-files/index-1.html">索引</a></li>
<li><a href="help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个</li>
<li>下一个</li>
</ul>
<ul class="navList">
<li><a href="index.html?deprecated-list.html" target="_top">框架</a></li>
<li><a href="deprecated-list.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 title="已过时的 API" class="title">已过时的 API</h1>
<h2 title="目录">目录</h2>
<ul>
<li><a href="#field">已过时的字段</a></li>
<li><a href="#method">已过时的方法</a></li>
</ul>
</div>
<div class="contentContainer"><a name="field">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<table class="deprecatedSummary" border="0" cellpadding="3" cellspacing="0" summary="已过时的字段表, 列表已过时的字段和解释">
<caption><span>已过时的字段</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">字段和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="com/amap/api/location/AMapLocation.html#LOCATION_TYPE_FAST">com.amap.api.location.AMapLocation.LOCATION_TYPE_FAST</a>
<div class="block"><span class="deprecationComment">已合并到<a href="com/amap/api/location/AMapLocation.html#LOCATION_TYPE_SAME_REQ"><code>AMapLocation.LOCATION_TYPE_SAME_REQ</code></a></span></div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
<a name="method">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<table class="deprecatedSummary" border="0" cellpadding="3" cellspacing="0" summary="已过时的方法表, 列表已过时的方法和解释">
<caption><span>已过时的方法</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">方法和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="com/amap/api/location/AMapLocation.html#getRoad--">com.amap.api.location.AMapLocation.getRoad()</a>
<div class="block"><span class="deprecationComment">使用<a href="com/amap/api/location/AMapLocation.html#getStreet--"><code>AMapLocation.getStreet()</code></a>方法代替</span></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="com/amap/api/location/AMapLocationClientOption.html#isWifiActiveScan--">com.amap.api.location.AMapLocationClientOption.isWifiActiveScan()</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="com/amap/api/location/AMapLocationClientOption.html#setWifiActiveScan-boolean-">com.amap.api.location.AMapLocationClientOption.setWifiActiveScan(boolean)</a>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="overview-tree.html">树</a></li>
<li class="navBarCell1Rev">已过时</li>
<li><a href="index-files/index-1.html">索引</a></li>
<li><a href="help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个</li>
<li>下一个</li>
</ul>
<ul class="navList">
<li><a href="index.html?deprecated-list.html" target="_top">框架</a></li>
<li><a href="deprecated-list.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
