<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>AMapLocation</title>
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="AMapLocation";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10,"i27":42,"i28":10,"i29":10,"i30":10,"i31":10,"i32":10,"i33":10,"i34":10,"i35":10};
var tabs = {65535:["t0","所有方法"],2:["t2","实例方法"],8:["t4","具体方法"],32:["t6","已过时的方法"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/AMapLocation.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个类</li>
<li><a href="../../../../com/amap/api/location/AMapLocationClient.html" title="com.amap.api.location中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/amap/api/location/AMapLocation.html" target="_top">框架</a></li>
<li><a href="AMapLocation.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li><a href="#field.summary">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li><a href="#field.detail">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.amap.api.location</div>
<h2 title="类 AMapLocation" class="title">类 AMapLocation</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>Location</li>
<li>
<ul class="inheritance">
<li>com.amap.api.location.AMapLocation</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>所有已实现的接口:</dt>
<dd>java.lang.Cloneable</dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">AMapLocation</span>
extends Location
implements java.lang.Cloneable</pre>
<div class="block">定位信息类。定位完成后的位置信息。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.0</dd>
<dt><span class="simpleTagLabel">版本:</span></dt>
<dd>2.0.0</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>字段概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="字段概要表, 列表字段和解释">
<caption><span>字段</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">字段和说明</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocation.html#COORD_TYPE_GCJ02">COORD_TYPE_GCJ02</a></span></code>
<div class="block">GCJ02坐标系</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocation.html#COORD_TYPE_WGS84">COORD_TYPE_WGS84</a></span></code>
<div class="block">WGS84坐标系</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocation.html#ERROR_CODE_AIRPLANEMODE_WIFIOFF">ERROR_CODE_AIRPLANEMODE_WIFIOFF</a></span></code>
<div class="block">定位错误码：定位失败，飞行模式下关闭了WIFI开关，请关闭飞行模式或者打开WIFI开关</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocation.html#ERROR_CODE_FAILURE_AUTH">ERROR_CODE_FAILURE_AUTH</a></span></code>
<div class="block">定位错误码：KEY错误,可以通过<a href="../../../../com/amap/api/location/AMapLocation.html#getLocationDetail--"><code>AMapLocation.getLocationDetail()</code></a>获取详细信息来跟注册的KEY信息进行对照</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocation.html#ERROR_CODE_FAILURE_CELL">ERROR_CODE_FAILURE_CELL</a></span></code>
<div class="block">定位错误码：错误的基站信息，请检查是否安装SIM卡</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocation.html#ERROR_CODE_FAILURE_COARSE_LOCATION">ERROR_CODE_FAILURE_COARSE_LOCATION</a></span></code>
<div class="block">定位错误码：定位失败，模糊权限下定位异常，可以通过<a href="../../../../com/amap/api/location/AMapLocation.html#getErrorInfo--"><code>AMapLocation.getErrorInfo()</code></a> 获取详细信息</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocation.html#ERROR_CODE_FAILURE_CONNECTION">ERROR_CODE_FAILURE_CONNECTION</a></span></code>
<div class="block">定位错误码：网络连接异常,可以通过<a href="../../../../com/amap/api/location/AMapLocation.html#getLocationDetail--"><code>AMapLocation.getLocationDetail()</code></a>获取详细信息</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocation.html#ERROR_CODE_FAILURE_INIT">ERROR_CODE_FAILURE_INIT</a></span></code>
<div class="block">定位错误码：初始化异常,可以通过<a href="../../../../com/amap/api/location/AMapLocation.html#getLocationDetail--"><code>AMapLocation.getLocationDetail()</code></a>获取详细信息</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocation.html#ERROR_CODE_FAILURE_LOCATION">ERROR_CODE_FAILURE_LOCATION</a></span></code>
<div class="block">定位错误码：定位结果错误,可以通过<a href="../../../../com/amap/api/location/AMapLocation.html#getLocationDetail--"><code>AMapLocation.getLocationDetail()</code></a>获取详细信息</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocation.html#ERROR_CODE_FAILURE_LOCATION_PARAMETER">ERROR_CODE_FAILURE_LOCATION_PARAMETER</a></span></code>
<div class="block">定位错误码：获取到的请求参数为空，可能获取过程中出现异常,可以通过<a href="../../../../com/amap/api/location/AMapLocation.html#getLocationDetail--"><code>AMapLocation.getLocationDetail()</code></a>获取详细信息</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocation.html#ERROR_CODE_FAILURE_LOCATION_PERMISSION">ERROR_CODE_FAILURE_LOCATION_PERMISSION</a></span></code>
<div class="block">定位错误码：缺少定位权限,请检查是否配置定位权限,并在安全软件和设置中给应用打开定位权限</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocation.html#ERROR_CODE_FAILURE_NOENOUGHSATELLITES">ERROR_CODE_FAILURE_NOENOUGHSATELLITES</a></span></code>
<div class="block">定位错误码：卫星定位失败，可用卫星数不足</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocation.html#ERROR_CODE_FAILURE_NOWIFIANDAP">ERROR_CODE_FAILURE_NOWIFIANDAP</a></span></code>
<div class="block">定位错误码：网络定位失败，请检查设备是否插入sim卡、开启移动网络或开启了wifi模块</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocation.html#ERROR_CODE_FAILURE_PARSER">ERROR_CODE_FAILURE_PARSER</a></span></code>
<div class="block">定位错误码：解析XML出错,可以通过<a href="../../../../com/amap/api/location/AMapLocation.html#getLocationDetail--"><code>AMapLocation.getLocationDetail()</code></a>获取详细信息</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocation.html#ERROR_CODE_FAILURE_SIMULATION_LOCATION">ERROR_CODE_FAILURE_SIMULATION_LOCATION</a></span></code>
<div class="block">定位错误码：定位位置可能被模拟</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocation.html#ERROR_CODE_FAILURE_WIFI_INFO">ERROR_CODE_FAILURE_WIFI_INFO</a></span></code>
<div class="block">定位错误码：定位失败，由于设备仅扫描到单个wifi，不能精准的计算出位置信息。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocation.html#ERROR_CODE_INVALID_PARAMETER">ERROR_CODE_INVALID_PARAMETER</a></span></code>
<div class="block">定位错误码：一些重要参数为空,如context,可以通过<a href="../../../../com/amap/api/location/AMapLocation.html#getLocationDetail--"><code>AMapLocation.getLocationDetail()</code></a>获取详细信息</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocation.html#ERROR_CODE_NOCGI_WIFIOFF">ERROR_CODE_NOCGI_WIFIOFF</a></span></code>
<div class="block">定位错误码：定位失败，没有检查到SIM卡，并且关闭了WIFI开关，请打开WIFI开关或者插入SIM卡</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocation.html#ERROR_CODE_SERVICE_FAIL">ERROR_CODE_SERVICE_FAIL</a></span></code>
<div class="block">定位错误码：定位服务启动失败，请检查是否配置service并且manifest中service标签是否配置在application标签内</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocation.html#ERROR_CODE_UNKNOWN">ERROR_CODE_UNKNOWN</a></span></code>
<div class="block">定位错误码：其他错误,可以通过<a href="../../../../com/amap/api/location/AMapLocation.html#getLocationDetail--"><code>AMapLocation.getLocationDetail()</code></a>获取详细信息</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocation.html#GPS_ACCURACY_BAD">GPS_ACCURACY_BAD</a></span></code>
<div class="block">卫星信号弱</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocation.html#GPS_ACCURACY_GOOD">GPS_ACCURACY_GOOD</a></span></code>
<div class="block">卫星信号强</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocation.html#GPS_ACCURACY_UNKNOWN">GPS_ACCURACY_UNKNOWN</a></span></code>
<div class="block">卫星状态未知</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocation.html#LOCATION_COMPENSATION">LOCATION_COMPENSATION</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocation.html#LOCATION_SUCCESS">LOCATION_SUCCESS</a></span></code>
<div class="block">定位错误码：定位成功</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_CELL">LOCATION_TYPE_CELL</a></span></code>
<div class="block">定位结果类型：基站定位结果
 
 属于网络定位</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_COARSE_LOCATION">LOCATION_TYPE_COARSE_LOCATION</a></span></code>
<div class="block">模糊定位类型</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_FAST">LOCATION_TYPE_FAST</a></span></code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;
<div class="block"><span class="deprecationComment">已合并到<a href="../../../../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_SAME_REQ"><code>AMapLocation.LOCATION_TYPE_SAME_REQ</code></a></span></div>
</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_FIX_CACHE">LOCATION_TYPE_FIX_CACHE</a></span></code>
<div class="block">定位结果类型：缓存定位结果
 
 返回一段时间前设备在相同的环境中缓存下来的网络定位结果，节省无必要的设备定位消耗</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS">LOCATION_TYPE_GPS</a></span></code>
<div class="block">定位结果类型：卫星定位结果
 
 通过设备卫星定位模块返回的定位结果</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_LAST_LOCATION_CACHE">LOCATION_TYPE_LAST_LOCATION_CACHE</a></span></code>
<div class="block">定位结果类型： 最后位置缓存</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_OFFLINE">LOCATION_TYPE_OFFLINE</a></span></code>
<div class="block">定位结果类型： 离线定位结果</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_SAME_REQ">LOCATION_TYPE_SAME_REQ</a></span></code>
<div class="block">定位结果类型：前次定位结果
 
 网络定位请求低于1秒、或两次定位之间设备位置变化非常小时返回，设备位移通过传感器感知</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_WIFI">LOCATION_TYPE_WIFI</a></span></code>
<div class="block">定位结果类型：Wifi定位结果
 
 属于网络定位，定位精度相对基站定位会更好</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocation.html#TRUSTED_LEVEL_BAD">TRUSTED_LEVEL_BAD</a></span></code>
<div class="block">定位结果的可信度-非常不可信
 
     周边信息的新鲜度超过10分钟
     模拟定位结果
 </div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocation.html#TRUSTED_LEVEL_HIGH">TRUSTED_LEVEL_HIGH</a></span></code>
<div class="block">定位结果的可信度-非常可信
 
     周边信息的新鲜度在15s之内
     实时GPS定位结果
 </div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocation.html#TRUSTED_LEVEL_LOW">TRUSTED_LEVEL_LOW</a></span></code>
<div class="block">定位结果的可信度-可信度较低
 
     周边信息的新鲜度在2-10分钟之间
 </div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocation.html#TRUSTED_LEVEL_NORMAL">TRUSTED_LEVEL_NORMAL</a></span></code>
<div class="block">定位结果的可信度-可信度一般
 
     周边信息的新鲜度在15秒-2分钟之间
     缓存、离线定位、最后位置
 </div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocation.html#AMapLocation-Location-">AMapLocation</a></span>(Location&nbsp;location)</code>
<div class="block">根据android.location.Location对象构造</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocation.html#AMapLocation-java.lang.String-">AMapLocation</a></span>(java.lang.String&nbsp;provider)</code>
<div class="block">根据定位提供者构造</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>所有方法</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">实例方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">具体方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t6" class="tableTab"><span><a href="javascript:show(32);">已过时的方法</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocation.html#getAccuracy--">getAccuracy</a></span>()</code>
<div class="block">获取定位精度
 
 单位:米</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocation.html#getAdCode--">getAdCode</a></span>()</code>
<div class="block">获取区域编码
 
 <b>2.9.0</b>之前的版本定位类型为<a href="../../../../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS"><code>AMapLocation.LOCATION_TYPE_GPS</code></a>时不会返回区域编码<br>
 自<b>2.9.0</b>版本开始，当<a href="../../../../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS"><code>AMapLocation.LOCATION_TYPE_GPS</code></a>时也可以返回区域编码
 </div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocation.html#getAddress--">getAddress</a></span>()</code>
<div class="block">获取地址
 
 <b>2.9.0</b>之前的版本定位类型为<a href="../../../../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS"><code>AMapLocation.LOCATION_TYPE_GPS</code></a>时不会返回地址信息<br>
 自<b>2.9.0</b>版本开始，当类型为<a href="../../../../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS"><code>AMapLocation.LOCATION_TYPE_GPS</code></a>时也可以返回地址信息(需要网络通畅，第一次有可能没有地址信息返回）
 </div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocation.html#getAltitude--">getAltitude</a></span>()</code>
<div class="block">获取海拔高度(单位：米)
 
 默认值：0.0<br>
 3.1.0之前的版本只有定位类型为 <a href="../../../../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS"><code>AMapLocation.LOCATION_TYPE_GPS</code></a>时才有值<br>
 自3.1.0版本开始，不限定定位类型，当定位类型不是<a href="../../../../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS"><code>AMapLocation.LOCATION_TYPE_GPS</code></a>时，可以通过
 <a href="../../../../com/amap/api/location/AMapLocationClientOption.html#setSensorEnable-boolean-"><code>AMapLocationClientOption.setSensorEnable(boolean)</code></a>
 控制是否返回海拔高度，当设置为true时会通过手机传感器获取海拔高度,如果手机没有对应的传感器会返回0.0
 </div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocation.html#getAoiName--">getAoiName</a></span>()</code>
<div class="block">获取兴趣面名称
 
 <b>2.9.0</b>之前的版本定位类型为<a href="../../../../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS"><code>AMapLocation.LOCATION_TYPE_GPS</code></a>时不会返回兴趣面名称<br>
 自<b>2.9.0</b>版本开始，当<a href="../../../../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS"><code>AMapLocation.LOCATION_TYPE_GPS</code></a>时也可以返回兴趣面名称
 </div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocation.html#getBearing--">getBearing</a></span>()</code>
<div class="block">获取方向角(单位：度）
 
 默认值：0.0<br>
 取值范围：【0，360】，其中0度表示正北方向，90度表示正东，180度表示正南，270度表示正西<br>
 3.1.0之前的版本只有定位类型为 <a href="../../../../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS"><code>AMapLocation.LOCATION_TYPE_GPS</code></a>时才有值<br>
 自3.1.0版本开始，不限定定位类型，当定位类型不是<a href="../../../../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS"><code>AMapLocation.LOCATION_TYPE_GPS</code></a>时，可以通过
 <a href="../../../../com/amap/api/location/AMapLocationClientOption.html#setSensorEnable-boolean-"><code>AMapLocationClientOption.setSensorEnable(boolean)</code></a>
 控制是否返回方向角，当设置为true时会通过手机传感器获取方向角,如果手机没有对应的传感器会返回0.0
 
 
     注意：<br>
     定位类型为<a href="../../../../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS"><code>AMapLocation.LOCATION_TYPE_GPS</code></a>时，方向角指的是运动方向<br>
     定位类型不是<a href="../../../../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS"><code>AMapLocation.LOCATION_TYPE_GPS</code></a>时，方向角指的是手机朝向<br>
 </div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocation.html#getBuildingId--">getBuildingId</a></span>()</code>
<div class="block">返回支持室内定位的建筑物ID信息</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocation.html#getCity--">getCity</a></span>()</code>
<div class="block">获取城市名称
 
 <b>2.9.0</b>之前的版本定位类型为<a href="../../../../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS"><code>AMapLocation.LOCATION_TYPE_GPS</code></a>时不会返回城市名称<br>
 自<b>2.9.0</b>版本开始，当<a href="../../../../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS"><code>AMapLocation.LOCATION_TYPE_GPS</code></a>时也可以返回省城市名称
 </div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocation.html#getCityCode--">getCityCode</a></span>()</code>
<div class="block">获取城市编码
 
 <b>2.9.0</b>之前的版本定位类型为<a href="../../../../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS"><code>AMapLocation.LOCATION_TYPE_GPS</code></a>时不会返回城市编码<br>
 自<b>2.9.0</b>版本开始，当<a href="../../../../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS"><code>AMapLocation.LOCATION_TYPE_GPS</code></a>时也可以返回城市编码
 </div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocation.html#getConScenario--">getConScenario</a></span>()</code>
<div class="block">室内外置信度
 
        室内：且置信度取值在[1 ～ 100]，值越大在室内的可能性越大
        室外：且置信度取值在[-100 ～ -1] ,值越小在室外的可能性越大
        无法识别室内外：置信度返回值为 0
 </div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocation.html#getCoordType--">getCoordType</a></span>()</code>
<div class="block">获取坐标系类型
 
     高德定位sdk会返回两种坐标系
     
         <a href="../../../../com/amap/api/location/AMapLocation.html#COORD_TYPE_GCJ02"><code>AMapLocation.COORD_TYPE_GCJ02</code></a> -- GCJ02坐标系
     
     
         <a href="../../../../com/amap/api/location/AMapLocation.html#COORD_TYPE_WGS84"><code>AMapLocation.COORD_TYPE_WGS84</code></a> -- WGS84坐标系,国外定位时返回的是WGS84坐标系
     
 </div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocation.html#getCountry--">getCountry</a></span>()</code>
<div class="block">获取国家名称</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocation.html#getDescription--">getDescription</a></span>()</code>
<div class="block">获取位置语义信息</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocation.html#getDistrict--">getDistrict</a></span>()</code>
<div class="block">获取区的名称
 
 <b>2.9.0</b>之前的版本定位类型为<a href="../../../../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS"><code>AMapLocation.LOCATION_TYPE_GPS</code></a>时不会返回区的名称<br>
 自<b>2.9.0</b>版本开始，当<a href="../../../../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS"><code>AMapLocation.LOCATION_TYPE_GPS</code></a>时也可以返回区的名称
 </div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocation.html#getErrorCode--">getErrorCode</a></span>()</code>
<div class="block">获取错误码</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocation.html#getErrorInfo--">getErrorInfo</a></span>()</code>
<div class="block">获取错误信息</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>Bundle</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocation.html#getExtras--">getExtras</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocation.html#getFloor--">getFloor</a></span>()</code>
<div class="block">获取室内定位的楼层信息</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocation.html#getGpsAccuracyStatus--">getGpsAccuracyStatus</a></span>()</code>
<div class="block">获取卫星信号强度，仅在卫星定位时有效,值为
 <code>#GPS_ACCURACY_BAD，#GPS_ACCURACY_GOOD，#GPS_ACCURACY_UNKNOWN</code></div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocation.html#getLatitude--">getLatitude</a></span>()</code>
<div class="block">获取纬度</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocation.html#getLocationDetail--">getLocationDetail</a></span>()</code>
<div class="block">获取定位信息描述</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/amap/api/location/AMapLocationQualityReport.html" title="com.amap.api.location中的类">AMapLocationQualityReport</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocation.html#getLocationQualityReport--">getLocationQualityReport</a></span>()</code>
<div class="block">获取定位质量</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocation.html#getLocationType--">getLocationType</a></span>()</code>
<div class="block">获取定位结果来源</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocation.html#getLongitude--">getLongitude</a></span>()</code>
<div class="block">获取经度</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocation.html#getPoiName--">getPoiName</a></span>()</code>
<div class="block">获取兴趣点名称
 
 <b>2.9.0</b>之前的版本定位类型为<a href="../../../../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS"><code>AMapLocation.LOCATION_TYPE_GPS</code></a>时不会返回兴趣点名称<br>
 自<b>2.9.0</b>版本开始，当<a href="../../../../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS"><code>AMapLocation.LOCATION_TYPE_GPS</code></a>时也可以返回兴趣点名称
 </div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocation.html#getProvider--">getProvider</a></span>()</code>
<div class="block">获取定位提供者</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocation.html#getProvince--">getProvince</a></span>()</code>
<div class="block">获取省的名称
 
 <b>2.9.0</b>之前的版本定位类型为<a href="../../../../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS"><code>AMapLocation.LOCATION_TYPE_GPS</code></a>时不会返回省份名称<br>
 自<b>2.9.0</b>版本开始，当<a href="../../../../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS"><code>AMapLocation.LOCATION_TYPE_GPS</code></a>时也可以返回省份名称
 </div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocation.html#getRoad--">getRoad</a></span>()</code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;
<div class="block"><span class="deprecationComment">使用<a href="../../../../com/amap/api/location/AMapLocation.html#getStreet--"><code>AMapLocation.getStreet()</code></a>方法代替</span></div>
</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocation.html#getSatellites--">getSatellites</a></span>()</code>
<div class="block">获取当前可用卫星数量, 仅在卫星定位时有效,</div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocation.html#getSpeed--">getSpeed</a></span>()</code>
<div class="block">获取当前速度(单位：米/秒)
 
 默认值：0.0<br>
 3.1.0之前的版本只有定位类型为 <a href="../../../../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS"><code>AMapLocation.LOCATION_TYPE_GPS</code></a>时才有值<br>
 自3.1.0版本开始，不限定定位类型，当定位类型不是<a href="../../../../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS"><code>AMapLocation.LOCATION_TYPE_GPS</code></a>时，可以通过
 <a href="../../../../com/amap/api/location/AMapLocationClientOption.html#setSensorEnable-boolean-"><code>AMapLocationClientOption.setSensorEnable(boolean)</code></a>
 控制是否返回速度值，当设置为true时会通过手机传感器获取速度,如果手机没有对应的传感器会返回0.0
 </div>
</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocation.html#getStreet--">getStreet</a></span>()</code>
<div class="block">获取街道名称
 
 <b>2.9.0</b>之前的版本定位类型为<a href="../../../../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS"><code>AMapLocation.LOCATION_TYPE_GPS</code></a>时不会返回街道名称<br>
 自<b>2.9.0</b>版本开始，当<a href="../../../../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS"><code>AMapLocation.LOCATION_TYPE_GPS</code></a>时也可以返回街道名称
 </div>
</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocation.html#getStreetNum--">getStreetNum</a></span>()</code>
<div class="block">获取门牌号
 
 <b>2.9.0</b>之前的版本定位类型为<a href="../../../../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS"><code>AMapLocation.LOCATION_TYPE_GPS</code></a>时不会返回门牌号<br>
 自<b>2.9.0</b>版本开始，当<a href="../../../../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS"><code>AMapLocation.LOCATION_TYPE_GPS</code></a>时也可以返回门牌号
 </div>
</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocation.html#getTrustedLevel--">getTrustedLevel</a></span>()</code>
<div class="block">获取定位结果的可信度
 
     只有在定位成功时才有意义
     非常可信 <a href="../../../../com/amap/api/location/AMapLocation.html#TRUSTED_LEVEL_HIGH"><code>AMapLocation.TRUSTED_LEVEL_HIGH</code></a>
     可信度一般<a href="../../../../com/amap/api/location/AMapLocation.html#TRUSTED_LEVEL_NORMAL"><code>AMapLocation.TRUSTED_LEVEL_NORMAL</code></a>
     可信度较低 <a href="../../../../com/amap/api/location/AMapLocation.html#TRUSTED_LEVEL_LOW"><code>AMapLocation.TRUSTED_LEVEL_LOW</code></a>
     非常不可信 <a href="../../../../com/amap/api/location/AMapLocation.html#TRUSTED_LEVEL_BAD"><code>AMapLocation.TRUSTED_LEVEL_BAD</code></a>
 </div>
</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocation.html#toStr--">toStr</a></span>()</code>
<div class="block">将定位结果转换成字符串</div>
</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocation.html#toStr-int-">toStr</a></span>(int&nbsp;iT)</code>
<div class="block">将定位结果转化为字符串</div>
</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocation.html#toString--">toString</a></span>()</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>字段详细资料</h3>
<a name="LOCATION_SUCCESS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LOCATION_SUCCESS</h4>
<pre>public static final&nbsp;int LOCATION_SUCCESS</pre>
<div class="block">定位错误码：定位成功</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.location.AMapLocation.LOCATION_SUCCESS">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="ERROR_CODE_INVALID_PARAMETER">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ERROR_CODE_INVALID_PARAMETER</h4>
<pre>public static final&nbsp;int ERROR_CODE_INVALID_PARAMETER</pre>
<div class="block">定位错误码：一些重要参数为空,如context,可以通过<a href="../../../../com/amap/api/location/AMapLocation.html#getLocationDetail--"><code>AMapLocation.getLocationDetail()</code></a>获取详细信息</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.location.AMapLocation.ERROR_CODE_INVALID_PARAMETER">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="ERROR_CODE_FAILURE_WIFI_INFO">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ERROR_CODE_FAILURE_WIFI_INFO</h4>
<pre>public static final&nbsp;int ERROR_CODE_FAILURE_WIFI_INFO</pre>
<div class="block">定位错误码：定位失败，由于设备仅扫描到单个wifi，不能精准的计算出位置信息。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.location.AMapLocation.ERROR_CODE_FAILURE_WIFI_INFO">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="ERROR_CODE_FAILURE_LOCATION_PARAMETER">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ERROR_CODE_FAILURE_LOCATION_PARAMETER</h4>
<pre>public static final&nbsp;int ERROR_CODE_FAILURE_LOCATION_PARAMETER</pre>
<div class="block">定位错误码：获取到的请求参数为空，可能获取过程中出现异常,可以通过<a href="../../../../com/amap/api/location/AMapLocation.html#getLocationDetail--"><code>AMapLocation.getLocationDetail()</code></a>获取详细信息</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.location.AMapLocation.ERROR_CODE_FAILURE_LOCATION_PARAMETER">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="ERROR_CODE_FAILURE_CONNECTION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ERROR_CODE_FAILURE_CONNECTION</h4>
<pre>public static final&nbsp;int ERROR_CODE_FAILURE_CONNECTION</pre>
<div class="block">定位错误码：网络连接异常,可以通过<a href="../../../../com/amap/api/location/AMapLocation.html#getLocationDetail--"><code>AMapLocation.getLocationDetail()</code></a>获取详细信息</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.location.AMapLocation.ERROR_CODE_FAILURE_CONNECTION">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="ERROR_CODE_FAILURE_PARSER">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ERROR_CODE_FAILURE_PARSER</h4>
<pre>public static final&nbsp;int ERROR_CODE_FAILURE_PARSER</pre>
<div class="block">定位错误码：解析XML出错,可以通过<a href="../../../../com/amap/api/location/AMapLocation.html#getLocationDetail--"><code>AMapLocation.getLocationDetail()</code></a>获取详细信息</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.location.AMapLocation.ERROR_CODE_FAILURE_PARSER">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="ERROR_CODE_FAILURE_LOCATION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ERROR_CODE_FAILURE_LOCATION</h4>
<pre>public static final&nbsp;int ERROR_CODE_FAILURE_LOCATION</pre>
<div class="block">定位错误码：定位结果错误,可以通过<a href="../../../../com/amap/api/location/AMapLocation.html#getLocationDetail--"><code>AMapLocation.getLocationDetail()</code></a>获取详细信息</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.location.AMapLocation.ERROR_CODE_FAILURE_LOCATION">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="ERROR_CODE_FAILURE_AUTH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ERROR_CODE_FAILURE_AUTH</h4>
<pre>public static final&nbsp;int ERROR_CODE_FAILURE_AUTH</pre>
<div class="block">定位错误码：KEY错误,可以通过<a href="../../../../com/amap/api/location/AMapLocation.html#getLocationDetail--"><code>AMapLocation.getLocationDetail()</code></a>获取详细信息来跟注册的KEY信息进行对照</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.location.AMapLocation.ERROR_CODE_FAILURE_AUTH">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="ERROR_CODE_UNKNOWN">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ERROR_CODE_UNKNOWN</h4>
<pre>public static final&nbsp;int ERROR_CODE_UNKNOWN</pre>
<div class="block">定位错误码：其他错误,可以通过<a href="../../../../com/amap/api/location/AMapLocation.html#getLocationDetail--"><code>AMapLocation.getLocationDetail()</code></a>获取详细信息</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.location.AMapLocation.ERROR_CODE_UNKNOWN">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="ERROR_CODE_FAILURE_INIT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ERROR_CODE_FAILURE_INIT</h4>
<pre>public static final&nbsp;int ERROR_CODE_FAILURE_INIT</pre>
<div class="block">定位错误码：初始化异常,可以通过<a href="../../../../com/amap/api/location/AMapLocation.html#getLocationDetail--"><code>AMapLocation.getLocationDetail()</code></a>获取详细信息</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.location.AMapLocation.ERROR_CODE_FAILURE_INIT">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="ERROR_CODE_SERVICE_FAIL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ERROR_CODE_SERVICE_FAIL</h4>
<pre>public static final&nbsp;int ERROR_CODE_SERVICE_FAIL</pre>
<div class="block">定位错误码：定位服务启动失败，请检查是否配置service并且manifest中service标签是否配置在application标签内</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.location.AMapLocation.ERROR_CODE_SERVICE_FAIL">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="ERROR_CODE_FAILURE_CELL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ERROR_CODE_FAILURE_CELL</h4>
<pre>public static final&nbsp;int ERROR_CODE_FAILURE_CELL</pre>
<div class="block">定位错误码：错误的基站信息，请检查是否安装SIM卡</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.location.AMapLocation.ERROR_CODE_FAILURE_CELL">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="ERROR_CODE_FAILURE_LOCATION_PERMISSION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ERROR_CODE_FAILURE_LOCATION_PERMISSION</h4>
<pre>public static final&nbsp;int ERROR_CODE_FAILURE_LOCATION_PERMISSION</pre>
<div class="block">定位错误码：缺少定位权限,请检查是否配置定位权限,并在安全软件和设置中给应用打开定位权限</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.location.AMapLocation.ERROR_CODE_FAILURE_LOCATION_PERMISSION">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="ERROR_CODE_FAILURE_NOWIFIANDAP">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ERROR_CODE_FAILURE_NOWIFIANDAP</h4>
<pre>public static final&nbsp;int ERROR_CODE_FAILURE_NOWIFIANDAP</pre>
<div class="block">定位错误码：网络定位失败，请检查设备是否插入sim卡、开启移动网络或开启了wifi模块</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.0.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.location.AMapLocation.ERROR_CODE_FAILURE_NOWIFIANDAP">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="ERROR_CODE_FAILURE_NOENOUGHSATELLITES">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ERROR_CODE_FAILURE_NOENOUGHSATELLITES</h4>
<pre>public static final&nbsp;int ERROR_CODE_FAILURE_NOENOUGHSATELLITES</pre>
<div class="block">定位错误码：卫星定位失败，可用卫星数不足</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.1.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.location.AMapLocation.ERROR_CODE_FAILURE_NOENOUGHSATELLITES">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="ERROR_CODE_FAILURE_SIMULATION_LOCATION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ERROR_CODE_FAILURE_SIMULATION_LOCATION</h4>
<pre>public static final&nbsp;int ERROR_CODE_FAILURE_SIMULATION_LOCATION</pre>
<div class="block">定位错误码：定位位置可能被模拟</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.location.AMapLocation.ERROR_CODE_FAILURE_SIMULATION_LOCATION">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="ERROR_CODE_AIRPLANEMODE_WIFIOFF">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ERROR_CODE_AIRPLANEMODE_WIFIOFF</h4>
<pre>public static final&nbsp;int ERROR_CODE_AIRPLANEMODE_WIFIOFF</pre>
<div class="block">定位错误码：定位失败，飞行模式下关闭了WIFI开关，请关闭飞行模式或者打开WIFI开关</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.4.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.location.AMapLocation.ERROR_CODE_AIRPLANEMODE_WIFIOFF">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="ERROR_CODE_NOCGI_WIFIOFF">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ERROR_CODE_NOCGI_WIFIOFF</h4>
<pre>public static final&nbsp;int ERROR_CODE_NOCGI_WIFIOFF</pre>
<div class="block">定位错误码：定位失败，没有检查到SIM卡，并且关闭了WIFI开关，请打开WIFI开关或者插入SIM卡</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.4.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.location.AMapLocation.ERROR_CODE_NOCGI_WIFIOFF">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="ERROR_CODE_FAILURE_COARSE_LOCATION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ERROR_CODE_FAILURE_COARSE_LOCATION</h4>
<pre>public static final&nbsp;int ERROR_CODE_FAILURE_COARSE_LOCATION</pre>
<div class="block">定位错误码：定位失败，模糊权限下定位异常，可以通过<a href="../../../../com/amap/api/location/AMapLocation.html#getErrorInfo--"><code>AMapLocation.getErrorInfo()</code></a> 获取详细信息</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.0.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.location.AMapLocation.ERROR_CODE_FAILURE_COARSE_LOCATION">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="LOCATION_TYPE_GPS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LOCATION_TYPE_GPS</h4>
<pre>public static final&nbsp;int LOCATION_TYPE_GPS</pre>
<div class="block">定位结果类型：卫星定位结果
 <p>
 通过设备卫星定位模块返回的定位结果</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.location.AMapLocation.LOCATION_TYPE_GPS">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="LOCATION_TYPE_SAME_REQ">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LOCATION_TYPE_SAME_REQ</h4>
<pre>public static final&nbsp;int LOCATION_TYPE_SAME_REQ</pre>
<div class="block">定位结果类型：前次定位结果
 <P>
 网络定位请求低于1秒、或两次定位之间设备位置变化非常小时返回，设备位移通过传感器感知</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.location.AMapLocation.LOCATION_TYPE_SAME_REQ">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="LOCATION_TYPE_FAST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LOCATION_TYPE_FAST</h4>
<pre>public static final&nbsp;int LOCATION_TYPE_FAST</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;<span class="deprecationComment">已合并到<a href="../../../../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_SAME_REQ"><code>AMapLocation.LOCATION_TYPE_SAME_REQ</code></a></span></div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.location.AMapLocation.LOCATION_TYPE_FAST">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="LOCATION_TYPE_FIX_CACHE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LOCATION_TYPE_FIX_CACHE</h4>
<pre>public static final&nbsp;int LOCATION_TYPE_FIX_CACHE</pre>
<div class="block">定位结果类型：缓存定位结果
 <p>
 返回一段时间前设备在相同的环境中缓存下来的网络定位结果，节省无必要的设备定位消耗</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.location.AMapLocation.LOCATION_TYPE_FIX_CACHE">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="LOCATION_TYPE_WIFI">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LOCATION_TYPE_WIFI</h4>
<pre>public static final&nbsp;int LOCATION_TYPE_WIFI</pre>
<div class="block">定位结果类型：Wifi定位结果
 <P>
 属于网络定位，定位精度相对基站定位会更好</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.location.AMapLocation.LOCATION_TYPE_WIFI">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="LOCATION_TYPE_CELL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LOCATION_TYPE_CELL</h4>
<pre>public static final&nbsp;int LOCATION_TYPE_CELL</pre>
<div class="block">定位结果类型：基站定位结果
 <P>
 属于网络定位</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.location.AMapLocation.LOCATION_TYPE_CELL">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="LOCATION_TYPE_OFFLINE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LOCATION_TYPE_OFFLINE</h4>
<pre>public static final&nbsp;int LOCATION_TYPE_OFFLINE</pre>
<div class="block">定位结果类型： 离线定位结果</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.2.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.location.AMapLocation.LOCATION_TYPE_OFFLINE">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="LOCATION_TYPE_LAST_LOCATION_CACHE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LOCATION_TYPE_LAST_LOCATION_CACHE</h4>
<pre>public static final&nbsp;int LOCATION_TYPE_LAST_LOCATION_CACHE</pre>
<div class="block">定位结果类型： 最后位置缓存</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.4.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.location.AMapLocation.LOCATION_TYPE_LAST_LOCATION_CACHE">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="LOCATION_COMPENSATION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LOCATION_COMPENSATION</h4>
<pre>public static final&nbsp;int LOCATION_COMPENSATION</pre>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.location.AMapLocation.LOCATION_COMPENSATION">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="LOCATION_TYPE_COARSE_LOCATION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LOCATION_TYPE_COARSE_LOCATION</h4>
<pre>public static final&nbsp;int LOCATION_TYPE_COARSE_LOCATION</pre>
<div class="block">模糊定位类型</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.0.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.location.AMapLocation.LOCATION_TYPE_COARSE_LOCATION">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="COORD_TYPE_WGS84">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>COORD_TYPE_WGS84</h4>
<pre>public static final&nbsp;java.lang.String COORD_TYPE_WGS84</pre>
<div class="block">WGS84坐标系</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>4.3.0</dd>
</dl>
</li>
</ul>
<a name="COORD_TYPE_GCJ02">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>COORD_TYPE_GCJ02</h4>
<pre>public static final&nbsp;java.lang.String COORD_TYPE_GCJ02</pre>
<div class="block">GCJ02坐标系</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>4.3.0</dd>
</dl>
</li>
</ul>
<a name="GPS_ACCURACY_GOOD">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>GPS_ACCURACY_GOOD</h4>
<pre>public static final&nbsp;int GPS_ACCURACY_GOOD</pre>
<div class="block">卫星信号强</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.1.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.location.AMapLocation.GPS_ACCURACY_GOOD">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="GPS_ACCURACY_BAD">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>GPS_ACCURACY_BAD</h4>
<pre>public static final&nbsp;int GPS_ACCURACY_BAD</pre>
<div class="block">卫星信号弱</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.1.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.location.AMapLocation.GPS_ACCURACY_BAD">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="GPS_ACCURACY_UNKNOWN">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>GPS_ACCURACY_UNKNOWN</h4>
<pre>public static final&nbsp;int GPS_ACCURACY_UNKNOWN</pre>
<div class="block">卫星状态未知</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.1.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.location.AMapLocation.GPS_ACCURACY_UNKNOWN">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="TRUSTED_LEVEL_HIGH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TRUSTED_LEVEL_HIGH</h4>
<pre>public static final&nbsp;int TRUSTED_LEVEL_HIGH</pre>
<div class="block">定位结果的可信度-非常可信
 <p>
     <li>周边信息的新鲜度在15s之内</li>
     <li>实时GPS定位结果</li>
 </p></div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>4.4.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.location.AMapLocation.TRUSTED_LEVEL_HIGH">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="TRUSTED_LEVEL_NORMAL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TRUSTED_LEVEL_NORMAL</h4>
<pre>public static final&nbsp;int TRUSTED_LEVEL_NORMAL</pre>
<div class="block">定位结果的可信度-可信度一般
 <p>
     <li>周边信息的新鲜度在15秒-2分钟之间</li>
     <li>缓存、离线定位、最后位置</li>
 </p></div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>4.4.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.location.AMapLocation.TRUSTED_LEVEL_NORMAL">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="TRUSTED_LEVEL_LOW">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TRUSTED_LEVEL_LOW</h4>
<pre>public static final&nbsp;int TRUSTED_LEVEL_LOW</pre>
<div class="block">定位结果的可信度-可信度较低
 <p>
     <li>周边信息的新鲜度在2-10分钟之间</li>
 </p></div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>4.4.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.location.AMapLocation.TRUSTED_LEVEL_LOW">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="TRUSTED_LEVEL_BAD">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>TRUSTED_LEVEL_BAD</h4>
<pre>public static final&nbsp;int TRUSTED_LEVEL_BAD</pre>
<div class="block">定位结果的可信度-非常不可信
 <p>
     <li>周边信息的新鲜度超过10分钟</li>
     <li>模拟定位结果</li>
 </p></div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>4.4.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.location.AMapLocation.TRUSTED_LEVEL_BAD">常量字段值</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="AMapLocation-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>AMapLocation</h4>
<pre>public&nbsp;AMapLocation(java.lang.String&nbsp;provider)</pre>
<div class="block">根据定位提供者构造</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>provider</code> - 定位提供者名称</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.0</dd>
</dl>
</li>
</ul>
<a name="AMapLocation-Location-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>AMapLocation</h4>
<pre>public&nbsp;AMapLocation(Location&nbsp;location)</pre>
<div class="block">根据android.location.Location对象构造</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>location</code> - android。location.Location对象</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="getGpsAccuracyStatus--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getGpsAccuracyStatus</h4>
<pre>public&nbsp;int&nbsp;getGpsAccuracyStatus()</pre>
<div class="block">获取卫星信号强度，仅在卫星定位时有效,值为
 <code>#GPS_ACCURACY_BAD，#GPS_ACCURACY_GOOD，#GPS_ACCURACY_UNKNOWN</code></div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.1.0</dd>
</dl>
</li>
</ul>
<a name="getLocationType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLocationType</h4>
<pre>public&nbsp;int&nbsp;getLocationType()</pre>
<div class="block">获取定位结果来源</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.0</dd>
</dl>
</li>
</ul>
<a name="getLocationDetail--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLocationDetail</h4>
<pre>public&nbsp;java.lang.String&nbsp;getLocationDetail()</pre>
<div class="block">获取定位信息描述</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>定位信息描述</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.0</dd>
</dl>
</li>
</ul>
<a name="getErrorCode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getErrorCode</h4>
<pre>public&nbsp;int&nbsp;getErrorCode()</pre>
<div class="block">获取错误码</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>错误码</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.0</dd>
</dl>
</li>
</ul>
<a name="getErrorInfo--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getErrorInfo</h4>
<pre>public&nbsp;java.lang.String&nbsp;getErrorInfo()</pre>
<div class="block">获取错误信息</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>错误信息</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.0</dd>
</dl>
</li>
</ul>
<a name="getCountry--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCountry</h4>
<pre>public&nbsp;java.lang.String&nbsp;getCountry()</pre>
<div class="block">获取国家名称</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.0</dd>
</dl>
</li>
</ul>
<a name="getRoad--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRoad</h4>
<pre>public&nbsp;java.lang.String&nbsp;getRoad()</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;<span class="deprecationComment">使用<a href="../../../../com/amap/api/location/AMapLocation.html#getStreet--"><code>AMapLocation.getStreet()</code></a>方法代替</span></div>
<div class="block">获取街道或者道路名称</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>街道或者道路名称</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.0</dd>
</dl>
</li>
</ul>
<a name="getAddress--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAddress</h4>
<pre>public&nbsp;java.lang.String&nbsp;getAddress()</pre>
<div class="block">获取地址
 <p>
 <b>2.9.0</b>之前的版本定位类型为<a href="../../../../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS"><code>AMapLocation.LOCATION_TYPE_GPS</code></a>时不会返回地址信息<br>
 自<b>2.9.0</b>版本开始，当类型为<a href="../../../../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS"><code>AMapLocation.LOCATION_TYPE_GPS</code></a>时也可以返回地址信息(需要网络通畅，第一次有可能没有地址信息返回）
 </p></div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>地址</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.0</dd>
</dl>
</li>
</ul>
<a name="getProvince--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProvince</h4>
<pre>public&nbsp;java.lang.String&nbsp;getProvince()</pre>
<div class="block">获取省的名称
 <p>
 <b>2.9.0</b>之前的版本定位类型为<a href="../../../../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS"><code>AMapLocation.LOCATION_TYPE_GPS</code></a>时不会返回省份名称<br>
 自<b>2.9.0</b>版本开始，当<a href="../../../../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS"><code>AMapLocation.LOCATION_TYPE_GPS</code></a>时也可以返回省份名称
 </p></div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>省的名称</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.0</dd>
</dl>
</li>
</ul>
<a name="getCity--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCity</h4>
<pre>public&nbsp;java.lang.String&nbsp;getCity()</pre>
<div class="block">获取城市名称
 <p>
 <b>2.9.0</b>之前的版本定位类型为<a href="../../../../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS"><code>AMapLocation.LOCATION_TYPE_GPS</code></a>时不会返回城市名称<br>
 自<b>2.9.0</b>版本开始，当<a href="../../../../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS"><code>AMapLocation.LOCATION_TYPE_GPS</code></a>时也可以返回省城市名称
 </p></div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>城市名称</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.0</dd>
</dl>
</li>
</ul>
<a name="getDistrict--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDistrict</h4>
<pre>public&nbsp;java.lang.String&nbsp;getDistrict()</pre>
<div class="block">获取区的名称
 <p>
 <b>2.9.0</b>之前的版本定位类型为<a href="../../../../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS"><code>AMapLocation.LOCATION_TYPE_GPS</code></a>时不会返回区的名称<br>
 自<b>2.9.0</b>版本开始，当<a href="../../../../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS"><code>AMapLocation.LOCATION_TYPE_GPS</code></a>时也可以返回区的名称
 </p></div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>区的名称</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.0</dd>
</dl>
</li>
</ul>
<a name="getCityCode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCityCode</h4>
<pre>public&nbsp;java.lang.String&nbsp;getCityCode()</pre>
<div class="block">获取城市编码
 <p>
 <b>2.9.0</b>之前的版本定位类型为<a href="../../../../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS"><code>AMapLocation.LOCATION_TYPE_GPS</code></a>时不会返回城市编码<br>
 自<b>2.9.0</b>版本开始，当<a href="../../../../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS"><code>AMapLocation.LOCATION_TYPE_GPS</code></a>时也可以返回城市编码
 </p></div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>城市编码</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.0</dd>
</dl>
</li>
</ul>
<a name="getAdCode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAdCode</h4>
<pre>public&nbsp;java.lang.String&nbsp;getAdCode()</pre>
<div class="block">获取区域编码
 <p>
 <b>2.9.0</b>之前的版本定位类型为<a href="../../../../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS"><code>AMapLocation.LOCATION_TYPE_GPS</code></a>时不会返回区域编码<br>
 自<b>2.9.0</b>版本开始，当<a href="../../../../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS"><code>AMapLocation.LOCATION_TYPE_GPS</code></a>时也可以返回区域编码
 </p></div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>区域编码</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.0</dd>
</dl>
</li>
</ul>
<a name="getPoiName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPoiName</h4>
<pre>public&nbsp;java.lang.String&nbsp;getPoiName()</pre>
<div class="block">获取兴趣点名称
 <p>
 <b>2.9.0</b>之前的版本定位类型为<a href="../../../../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS"><code>AMapLocation.LOCATION_TYPE_GPS</code></a>时不会返回兴趣点名称<br>
 自<b>2.9.0</b>版本开始，当<a href="../../../../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS"><code>AMapLocation.LOCATION_TYPE_GPS</code></a>时也可以返回兴趣点名称
 </p></div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>兴趣点名称</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.2.0</dd>
</dl>
</li>
</ul>
<a name="getLatitude--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLatitude</h4>
<pre>public&nbsp;double&nbsp;getLatitude()</pre>
<div class="block">获取纬度</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>纬度</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.0</dd>
</dl>
</li>
</ul>
<a name="getLongitude--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLongitude</h4>
<pre>public&nbsp;double&nbsp;getLongitude()</pre>
<div class="block">获取经度</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>经度</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.0</dd>
</dl>
</li>
</ul>
<a name="getSatellites--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSatellites</h4>
<pre>public&nbsp;int&nbsp;getSatellites()</pre>
<div class="block">获取当前可用卫星数量, 仅在卫星定位时有效,</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>卫星数量</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.3.0</dd>
</dl>
</li>
</ul>
<a name="getStreet--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStreet</h4>
<pre>public&nbsp;java.lang.String&nbsp;getStreet()</pre>
<div class="block">获取街道名称
 <p>
 <b>2.9.0</b>之前的版本定位类型为<a href="../../../../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS"><code>AMapLocation.LOCATION_TYPE_GPS</code></a>时不会返回街道名称<br>
 自<b>2.9.0</b>版本开始，当<a href="../../../../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS"><code>AMapLocation.LOCATION_TYPE_GPS</code></a>时也可以返回街道名称
 </p></div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>街道名称</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.3.0</dd>
</dl>
</li>
</ul>
<a name="getStreetNum--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStreetNum</h4>
<pre>public&nbsp;java.lang.String&nbsp;getStreetNum()</pre>
<div class="block">获取门牌号
 <p>
 <b>2.9.0</b>之前的版本定位类型为<a href="../../../../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS"><code>AMapLocation.LOCATION_TYPE_GPS</code></a>时不会返回门牌号<br>
 自<b>2.9.0</b>版本开始，当<a href="../../../../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS"><code>AMapLocation.LOCATION_TYPE_GPS</code></a>时也可以返回门牌号
 </p></div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>门牌号</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.3.0</dd>
</dl>
</li>
</ul>
<a name="getAoiName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAoiName</h4>
<pre>public&nbsp;java.lang.String&nbsp;getAoiName()</pre>
<div class="block">获取兴趣面名称
 <p>
 <b>2.9.0</b>之前的版本定位类型为<a href="../../../../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS"><code>AMapLocation.LOCATION_TYPE_GPS</code></a>时不会返回兴趣面名称<br>
 自<b>2.9.0</b>版本开始，当<a href="../../../../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS"><code>AMapLocation.LOCATION_TYPE_GPS</code></a>时也可以返回兴趣面名称
 </p></div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>兴趣面名称</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.4.0</dd>
</dl>
</li>
</ul>
<a name="getBuildingId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBuildingId</h4>
<pre>public&nbsp;java.lang.String&nbsp;getBuildingId()</pre>
<div class="block">返回支持室内定位的建筑物ID信息</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>建筑物的POIID，</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.0</dd>
</dl>
</li>
</ul>
<a name="getFloor--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFloor</h4>
<pre>public&nbsp;java.lang.String&nbsp;getFloor()</pre>
<div class="block">获取室内定位的楼层信息</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>楼层信息</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.0</dd>
</dl>
</li>
</ul>
<a name="getDescription--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDescription</h4>
<pre>public&nbsp;java.lang.String&nbsp;getDescription()</pre>
<div class="block">获取位置语义信息</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>位置语义信息</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.4.0</dd>
</dl>
</li>
</ul>
<a name="toString--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toString</h4>
<pre>public&nbsp;java.lang.String&nbsp;toString()</pre>
</li>
</ul>
<a name="toStr--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toStr</h4>
<pre>public&nbsp;java.lang.String&nbsp;toStr()</pre>
<div class="block">将定位结果转换成字符串</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>定位结果字符串</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.0</dd>
</dl>
</li>
</ul>
<a name="toStr-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toStr</h4>
<pre>public&nbsp;java.lang.String&nbsp;toStr(int&nbsp;iT)</pre>
<div class="block">将定位结果转化为字符串</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>iT</code> - 【1代表完整描述，2代表精简描述，3代表极简描述】</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>定位结果字符串</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.0</dd>
</dl>
</li>
</ul>
<a name="getAccuracy--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAccuracy</h4>
<pre>public&nbsp;float&nbsp;getAccuracy()</pre>
<div class="block">获取定位精度
 <p>
 单位:米</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>定位精度</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.0</dd>
</dl>
</li>
</ul>
<a name="getBearing--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBearing</h4>
<pre>public&nbsp;float&nbsp;getBearing()</pre>
<div class="block">获取方向角(单位：度）
 <p>
 默认值：0.0<br>
 取值范围：【0，360】，其中0度表示正北方向，90度表示正东，180度表示正南，270度表示正西<br>
 3.1.0之前的版本只有定位类型为 <a href="../../../../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS"><code>AMapLocation.LOCATION_TYPE_GPS</code></a>时才有值<br>
 自3.1.0版本开始，不限定定位类型，当定位类型不是<a href="../../../../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS"><code>AMapLocation.LOCATION_TYPE_GPS</code></a>时，可以通过
 <a href="../../../../com/amap/api/location/AMapLocationClientOption.html#setSensorEnable-boolean-"><code>AMapLocationClientOption.setSensorEnable(boolean)</code></a>
 控制是否返回方向角，当设置为true时会通过手机传感器获取方向角,如果手机没有对应的传感器会返回0.0
 </p>
 <p>
     注意：<br>
     定位类型为<a href="../../../../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS"><code>AMapLocation.LOCATION_TYPE_GPS</code></a>时，方向角指的是运动方向<br>
     定位类型不是<a href="../../../../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS"><code>AMapLocation.LOCATION_TYPE_GPS</code></a>时，方向角指的是手机朝向<br>
 </p></div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>方向角</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.0</dd>
</dl>
</li>
</ul>
<a name="getAltitude--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAltitude</h4>
<pre>public&nbsp;double&nbsp;getAltitude()</pre>
<div class="block">获取海拔高度(单位：米)
 <p>
 默认值：0.0<br>
 3.1.0之前的版本只有定位类型为 <a href="../../../../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS"><code>AMapLocation.LOCATION_TYPE_GPS</code></a>时才有值<br>
 自3.1.0版本开始，不限定定位类型，当定位类型不是<a href="../../../../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS"><code>AMapLocation.LOCATION_TYPE_GPS</code></a>时，可以通过
 <a href="../../../../com/amap/api/location/AMapLocationClientOption.html#setSensorEnable-boolean-"><code>AMapLocationClientOption.setSensorEnable(boolean)</code></a>
 控制是否返回海拔高度，当设置为true时会通过手机传感器获取海拔高度,如果手机没有对应的传感器会返回0.0
 </p></div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>海拔高度</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.0</dd>
</dl>
</li>
</ul>
<a name="getSpeed--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSpeed</h4>
<pre>public&nbsp;float&nbsp;getSpeed()</pre>
<div class="block">获取当前速度(单位：米/秒)
 <p>
 默认值：0.0<br>
 3.1.0之前的版本只有定位类型为 <a href="../../../../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS"><code>AMapLocation.LOCATION_TYPE_GPS</code></a>时才有值<br>
 自3.1.0版本开始，不限定定位类型，当定位类型不是<a href="../../../../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS"><code>AMapLocation.LOCATION_TYPE_GPS</code></a>时，可以通过
 <a href="../../../../com/amap/api/location/AMapLocationClientOption.html#setSensorEnable-boolean-"><code>AMapLocationClientOption.setSensorEnable(boolean)</code></a>
 控制是否返回速度值，当设置为true时会通过手机传感器获取速度,如果手机没有对应的传感器会返回0.0
 </p></div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>当前速度</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.0</dd>
</dl>
</li>
</ul>
<a name="getProvider--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProvider</h4>
<pre>public&nbsp;java.lang.String&nbsp;getProvider()</pre>
<div class="block">获取定位提供者</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>定位提供者
         <P>
         lbs:高德网络定位
         <p>
         gps:卫星定位</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.0</dd>
</dl>
</li>
</ul>
<a name="getExtras--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getExtras</h4>
<pre>public&nbsp;Bundle&nbsp;getExtras()</pre>
</li>
</ul>
<a name="getLocationQualityReport--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLocationQualityReport</h4>
<pre>public&nbsp;<a href="../../../../com/amap/api/location/AMapLocationQualityReport.html" title="com.amap.api.location中的类">AMapLocationQualityReport</a>&nbsp;getLocationQualityReport()</pre>
<div class="block">获取定位质量</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.6.0</dd>
</dl>
</li>
</ul>
<a name="getCoordType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCoordType</h4>
<pre>public&nbsp;java.lang.String&nbsp;getCoordType()</pre>
<div class="block">获取坐标系类型
 <p>
     高德定位sdk会返回两种坐标系
     <li>
         <a href="../../../../com/amap/api/location/AMapLocation.html#COORD_TYPE_GCJ02"><code>AMapLocation.COORD_TYPE_GCJ02</code></a> -- GCJ02坐标系
     </li>
     <li>
         <a href="../../../../com/amap/api/location/AMapLocation.html#COORD_TYPE_WGS84"><code>AMapLocation.COORD_TYPE_WGS84</code></a> -- WGS84坐标系,国外定位时返回的是WGS84坐标系
     </li>
 </p></div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>4.3.0</dd>
</dl>
</li>
</ul>
<a name="getTrustedLevel--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTrustedLevel</h4>
<pre>public&nbsp;int&nbsp;getTrustedLevel()</pre>
<div class="block">获取定位结果的可信度
 <p>
     <li>只有在定位成功时才有意义</li>
     <li>非常可信 <a href="../../../../com/amap/api/location/AMapLocation.html#TRUSTED_LEVEL_HIGH"><code>AMapLocation.TRUSTED_LEVEL_HIGH</code></a></li>
     <li>可信度一般<a href="../../../../com/amap/api/location/AMapLocation.html#TRUSTED_LEVEL_NORMAL"><code>AMapLocation.TRUSTED_LEVEL_NORMAL</code></a></li>
     <li>可信度较低 <a href="../../../../com/amap/api/location/AMapLocation.html#TRUSTED_LEVEL_LOW"><code>AMapLocation.TRUSTED_LEVEL_LOW</code></a></li>
     <li>非常不可信 <a href="../../../../com/amap/api/location/AMapLocation.html#TRUSTED_LEVEL_BAD"><code>AMapLocation.TRUSTED_LEVEL_BAD</code></a></li>
 </p></div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>4.4.0</dd>
</dl>
</li>
</ul>
<a name="getConScenario--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getConScenario</h4>
<pre>public&nbsp;int&nbsp;getConScenario()</pre>
<div class="block">室内外置信度
 <p>
        <li>室内：且置信度取值在[1 ～ 100]，值越大在室内的可能性越大</li>
        <li>室外：且置信度取值在[-100 ～ -1] ,值越小在室外的可能性越大</li>
        <li>无法识别室内外：置信度返回值为 0</li>
 </p></div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>室内外置信度</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>4.6</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/AMapLocation.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个类</li>
<li><a href="../../../../com/amap/api/location/AMapLocationClient.html" title="com.amap.api.location中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/amap/api/location/AMapLocation.html" target="_top">框架</a></li>
<li><a href="AMapLocation.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li><a href="#field.summary">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li><a href="#field.detail">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
