<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>AMapLocationClient</title>
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="AMapLocationClient";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":9,"i3":10,"i4":10,"i5":10,"i6":10,"i7":9,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":9,"i16":9};
var tabs = {65535:["t0","所有方法"],1:["t1","静态方法"],2:["t2","实例方法"],8:["t4","具体方法"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/AMapLocationClient.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/amap/api/location/AMapLocation.html" title="com.amap.api.location中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/amap/api/location/AMapLocationClient.html" target="_top">框架</a></li>
<li><a href="AMapLocationClient.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.amap.api.location</div>
<h2 title="类 AMapLocationClient" class="title">类 AMapLocationClient</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.amap.api.location.AMapLocationClient</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">AMapLocationClient</span>
extends java.lang.Object</pre>
<div class="block">定位服务类。此类提供单次定位、持续定位、最后位置相关功能。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.0</dd>
<dt><span class="simpleTagLabel">版本:</span></dt>
<dd>2.0.0</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocationClient.html#AMapLocationClient-Context-">AMapLocationClient</a></span>(Context&nbsp;context)</code>
<div class="block">构造方法</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>所有方法</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">静态方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">实例方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">具体方法</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocationClient.html#disableBackgroundLocation-boolean-">disableBackgroundLocation</a></span>(boolean&nbsp;removeNotification)</code>
<div class="block">关闭后台定位功能
 
 与<a href="../../../../com/amap/api/location/AMapLocationClient.html#enableBackgroundLocation-int-Notification-"><code>AMapLocationClient.enableBackgroundLocation(int, Notification)</code></a>配对使用<br>
 <font color="red"><b>注意:</b></font>关闭后台定位功能只是代表不再提供后台定位的能力，并不是停止定位，停止定位请调用<a href="../../../../com/amap/api/location/AMapLocationClient.html#stopLocation--"><code>AMapLocationClient.stopLocation()</code></a>。</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocationClient.html#enableBackgroundLocation-int-Notification-">enableBackgroundLocation</a></span>(int&nbsp;notificationId,
                        Notification&nbsp;notification)</code>
<div class="block">开启后台定位功能
 <font color="red"><b>注意:</b></font>
 如果您设置了target>=28,需要增加android.permission.FOREGROUND_SERVICE权限,<br/>
 如果您的app需要运行在Android Q版本的手机上，需要为ApsService增加android:foregroundServiceType="location"属性，
 例：&lt;service
             android:name="com.amap.api.location.APSService"
             android:foregroundServiceType="location"&#x2F;&gt;
 
 主要是为了解决Android 8.0以上版本对后台定位的限制，开启后会显示通知栏,如果您的应用本身已经存在一个前台服务通知，则无需再开启此接口<br>
 <font color="red"><b>注意:</b></font>启动后台定位只是代表开启了后台定位的能力，并不代表已经开始定位，开始定位请调用<a href="../../../../com/amap/api/location/AMapLocationClient.html#startLocation--"><code>AMapLocationClient.startLocation()</code></a>。</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocationClient.html#getDeviceId-Context-">getDeviceId</a></span>(Context&nbsp;context)</code>
<div class="block">获取设备号</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/amap/api/location/AMapLocation.html" title="com.amap.api.location中的类">AMapLocation</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocationClient.html#getLastKnownLocation--">getLastKnownLocation</a></span>()</code>
<div class="block">获取最后位置</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocationClient.html#getVersion--">getVersion</a></span>()</code>
<div class="block">获取定位sdk版本信息</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocationClient.html#isStarted--">isStarted</a></span>()</code>
<div class="block">本地定位服务是否已经启动，用于用户检查服务是否已经启动</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocationClient.html#onDestroy--">onDestroy</a></span>()</code>
<div class="block">销毁定位,释放定位资源, 当不再需要进行定位时调用此方法
 
 <b>该方法会释放所有定位资源，调用后再进行定位需要重新实例化AMapLocationClient</b>
 </div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocationClient.html#setApiKey-java.lang.String-">setApiKey</a></span>(java.lang.String&nbsp;apiKey)</code>
<div class="block">设置apikey <b>必须在AmapLocationClient实例化之前调用</b></div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocationClient.html#setLocationListener-com.amap.api.location.AMapLocationListener-">setLocationListener</a></span>(<a href="../../../../com/amap/api/location/AMapLocationListener.html" title="com.amap.api.location中的接口">AMapLocationListener</a>&nbsp;listener)</code>
<div class="block">设置定位回调监听</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocationClient.html#setLocationOption-com.amap.api.location.AMapLocationClientOption-">setLocationOption</a></span>(<a href="../../../../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a>&nbsp;option)</code>
<div class="block">设置定位参数</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocationClient.html#startAssistantLocation-WebView-">startAssistantLocation</a></span>(WebView&nbsp;webView)</code>
<div class="block">启动H5辅助定位
 
     只适用于Android 4.2及以后版本
     该接口只用于配合Web JS API的H5辅助定位，开启后并没有开始定位，开始定位由JS API触发。</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocationClient.html#startLocation--">startLocation</a></span>()</code>
<div class="block">开始定位</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocationClient.html#stopAssistantLocation--">stopAssistantLocation</a></span>()</code>
<div class="block">停止辅助定位
 
     如果已经调用了startAssistantLocation接口，在destroy时请调用该接口
 </div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocationClient.html#stopLocation--">stopLocation</a></span>()</code>
<div class="block">停止定位</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocationClient.html#unRegisterLocationListener-com.amap.api.location.AMapLocationListener-">unRegisterLocationListener</a></span>(<a href="../../../../com/amap/api/location/AMapLocationListener.html" title="com.amap.api.location中的接口">AMapLocationListener</a>&nbsp;listener)</code>
<div class="block">移除定位监听</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocationClient.html#updatePrivacyAgree-Context-boolean-">updatePrivacyAgree</a></span>(Context&nbsp;context,
                  boolean&nbsp;isAgree)</code>
<div class="block">设置是否同意用户授权政策 <b>必须在AmapLocationClient实例化之前调用</b></div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocationClient.html#updatePrivacyShow-Context-boolean-boolean-">updatePrivacyShow</a></span>(Context&nbsp;context,
                 boolean&nbsp;isContains,
                 boolean&nbsp;isShow)</code>
<div class="block">设置包含隐私政策，并展示用户授权弹窗 <b>必须在AmapLocationClient实例化之前调用</b></div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="AMapLocationClient-Context-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>AMapLocationClient</h4>
<pre>public&nbsp;AMapLocationClient(Context&nbsp;context)
                   throws java.lang.Exception</pre>
<div class="block">构造方法</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>context</code> - Android上下文</dd>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code>java.lang.Exception</code></dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="setLocationOption-com.amap.api.location.AMapLocationClientOption-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLocationOption</h4>
<pre>public&nbsp;void&nbsp;setLocationOption(<a href="../../../../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a>&nbsp;option)</pre>
<div class="block">设置定位参数</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>option</code> - 定位参数</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.0</dd>
</dl>
</li>
</ul>
<a name="setLocationListener-com.amap.api.location.AMapLocationListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLocationListener</h4>
<pre>public&nbsp;void&nbsp;setLocationListener(<a href="../../../../com/amap/api/location/AMapLocationListener.html" title="com.amap.api.location中的接口">AMapLocationListener</a>&nbsp;listener)</pre>
<div class="block">设置定位回调监听</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>listener</code> - 定位完成后的回调接口</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.0</dd>
</dl>
</li>
</ul>
<a name="startLocation--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>startLocation</h4>
<pre>public&nbsp;void&nbsp;startLocation()</pre>
<div class="block">开始定位</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.0</dd>
</dl>
</li>
</ul>
<a name="stopLocation--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>stopLocation</h4>
<pre>public&nbsp;void&nbsp;stopLocation()</pre>
<div class="block">停止定位</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.0</dd>
</dl>
</li>
</ul>
<a name="getLastKnownLocation--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLastKnownLocation</h4>
<pre>public&nbsp;<a href="../../../../com/amap/api/location/AMapLocation.html" title="com.amap.api.location中的类">AMapLocation</a>&nbsp;getLastKnownLocation()</pre>
<div class="block">获取最后位置</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>最后一次定位的位置信息</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.0</dd>
</dl>
</li>
</ul>
<a name="startAssistantLocation-WebView-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>startAssistantLocation</h4>
<pre>public&nbsp;void&nbsp;startAssistantLocation(WebView&nbsp;webView)</pre>
<div class="block">启动H5辅助定位
 <p>
     <li>只适用于Android 4.2及以后版本</li>
     <li>该接口只用于配合Web JS API的H5辅助定位，开启后并没有开始定位，开始定位由JS API触发。</li>
     <li>建议在WebView控件初始化之后就调用该接口，尽量不要在WebView执行loadUrl之后调用</li>
 </p></div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>webView</code> - 自定义的webView</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.8.0</dd>
</dl>
</li>
</ul>
<a name="stopAssistantLocation--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>stopAssistantLocation</h4>
<pre>public&nbsp;void&nbsp;stopAssistantLocation()</pre>
<div class="block">停止辅助定位
 <p>
     如果已经调用了startAssistantLocation接口，在destroy时请调用该接口
 </p></div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.0</dd>
</dl>
</li>
</ul>
<a name="getVersion--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVersion</h4>
<pre>public&nbsp;java.lang.String&nbsp;getVersion()</pre>
<div class="block">获取定位sdk版本信息</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>sdk版本信息</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.0</dd>
</dl>
</li>
</ul>
<a name="updatePrivacyShow-Context-boolean-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>updatePrivacyShow</h4>
<pre>public static&nbsp;void&nbsp;updatePrivacyShow(Context&nbsp;context,
                                     boolean&nbsp;isContains,
                                     boolean&nbsp;isShow)</pre>
<div class="block">设置包含隐私政策，并展示用户授权弹窗 <b>必须在AmapLocationClient实例化之前调用</b></div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>context</code> - </dd>
<dd><code>isContains:</code> - 是隐私权政策是否包含高德开平隐私权政策  true是包含</dd>
<dd><code>isShow:</code> - 隐私权政策是否弹窗展示告知用户 true是展示</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>5.6.0</dd>
</dl>
</li>
</ul>
<a name="updatePrivacyAgree-Context-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>updatePrivacyAgree</h4>
<pre>public static&nbsp;void&nbsp;updatePrivacyAgree(Context&nbsp;context,
                                      boolean&nbsp;isAgree)</pre>
<div class="block">设置是否同意用户授权政策 <b>必须在AmapLocationClient实例化之前调用</b></div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>context</code> - </dd>
<dd><code>isAgree:隐私权政策是否取得用户同意</code> -  true是用户同意</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>5.6.0</dd>
</dl>
</li>
</ul>
<a name="setApiKey-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setApiKey</h4>
<pre>public static&nbsp;void&nbsp;setApiKey(java.lang.String&nbsp;apiKey)</pre>
<div class="block">设置apikey <b>必须在AmapLocationClient实例化之前调用</b></div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>apiKey</code> - 要设置的apikey</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.0</dd>
</dl>
</li>
</ul>
<a name="isStarted--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isStarted</h4>
<pre>public&nbsp;boolean&nbsp;isStarted()</pre>
<div class="block">本地定位服务是否已经启动，用于用户检查服务是否已经启动</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>true:已经启动</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.0</dd>
</dl>
</li>
</ul>
<a name="unRegisterLocationListener-com.amap.api.location.AMapLocationListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>unRegisterLocationListener</h4>
<pre>public&nbsp;void&nbsp;unRegisterLocationListener(<a href="../../../../com/amap/api/location/AMapLocationListener.html" title="com.amap.api.location中的接口">AMapLocationListener</a>&nbsp;listener)</pre>
<div class="block">移除定位监听</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>listener</code> - 要移除的定位监听</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.0</dd>
</dl>
</li>
</ul>
<a name="onDestroy--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onDestroy</h4>
<pre>public&nbsp;void&nbsp;onDestroy()</pre>
<div class="block">销毁定位,释放定位资源, 当不再需要进行定位时调用此方法
 <p>
 <b>该方法会释放所有定位资源，调用后再进行定位需要重新实例化AMapLocationClient</b>
 </p></div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.0</dd>
</dl>
</li>
</ul>
<a name="enableBackgroundLocation-int-Notification-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>enableBackgroundLocation</h4>
<pre>public&nbsp;void&nbsp;enableBackgroundLocation(int&nbsp;notificationId,
                                     Notification&nbsp;notification)</pre>
<div class="block">开启后台定位功能
 <font color="red"><b>注意:</b></font>
 如果您设置了target>=28,需要增加android.permission.FOREGROUND_SERVICE权限,<br/>
 如果您的app需要运行在Android Q版本的手机上，需要为ApsService增加android:foregroundServiceType="location"属性，
 例：&lt;service
             android:name="com.amap.api.location.APSService"
             android:foregroundServiceType="location"&#x2F;&gt;
 <p>
 主要是为了解决Android 8.0以上版本对后台定位的限制，开启后会显示通知栏,如果您的应用本身已经存在一个前台服务通知，则无需再开启此接口<br>
 <font color="red"><b>注意:</b></font>启动后台定位只是代表开启了后台定位的能力，并不代表已经开始定位，开始定位请调用<a href="../../../../com/amap/api/location/AMapLocationClient.html#startLocation--"><code>AMapLocationClient.startLocation()</code></a>。<br>
 建议：在整个APP中如果存在多个AMapLocationClient，只需要其中一个开启就可以了，无需重复启动，重复启动也只会显示一个通知栏。
 </p></div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>notificationId</code> - 通知栏ID,建议这个app唯一; 不能为0</dd>
<dd><code>notification</code> - 自定义通知栏，使用者可以根据自己业务需求自定义Notification，将创建好的Notification对象传入；不能为null</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.8.0</dd>
</dl>
</li>
</ul>
<a name="disableBackgroundLocation-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>disableBackgroundLocation</h4>
<pre>public&nbsp;void&nbsp;disableBackgroundLocation(boolean&nbsp;removeNotification)</pre>
<div class="block">关闭后台定位功能
 <p>
 与<a href="../../../../com/amap/api/location/AMapLocationClient.html#enableBackgroundLocation-int-Notification-"><code>AMapLocationClient.enableBackgroundLocation(int, Notification)</code></a>配对使用<br>
 <font color="red"><b>注意:</b></font>关闭后台定位功能只是代表不再提供后台定位的能力，并不是停止定位，停止定位请调用<a href="../../../../com/amap/api/location/AMapLocationClient.html#stopLocation--"><code>AMapLocationClient.stopLocation()</code></a>。<br>
 如果在整个APP中如果有调用<a href="../../../../com/amap/api/location/AMapLocationClient.html#enableBackgroundLocation-int-Notification-"><code>AMapLocationClient.enableBackgroundLocation(int, Notification)</code></a>，必须调用该方法。<br>
 如果同时有多个AMapLocationClient调用了<a href="../../../../com/amap/api/location/AMapLocationClient.html#enableBackgroundLocation-int-Notification-"><code>AMapLocationClient.enableBackgroundLocation(int, Notification)</code></a>，只有全部的client
 都调用了disableBackgroundLocation（true),通知栏才会消失。
 <p>
 注意：如果您设置了target>=28,需要增加android.permission.FOREGROUND_SERVICE权限</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>removeNotification</code> - 是否移除通知栏， true：移除通知栏，false：不移除通知栏，可以手动移除</dd>
</dl>
</li>
</ul>
<a name="getDeviceId-Context-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getDeviceId</h4>
<pre>public static&nbsp;java.lang.String&nbsp;getDeviceId(Context&nbsp;context)</pre>
<div class="block">获取设备号</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>context</code> - </dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>设备号, 如果获取不到返回null</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>4.5.1</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/AMapLocationClient.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/amap/api/location/AMapLocation.html" title="com.amap.api.location中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/amap/api/location/AMapLocationClient.html" target="_top">框架</a></li>
<li><a href="AMapLocationClient.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
