<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>程序包 com.amap.api.location的使用</title>
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="\u7A0B\u5E8F\u5305 com.amap.api.location\u7684\u4F7F\u7528";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li>类</li>
<li class="navBarCell1Rev">使用</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个</li>
<li>下一个</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/amap/api/location/package-use.html" target="_top">框架</a></li>
<li><a href="package-use.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 title="程序包的使用 com.amap.api.location" class="title">程序包的使用<br>com.amap.api.location</h1>
</div>
<div class="contentContainer">
<ul class="blockList">
<li class="blockList">
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表程序包和解释">
<caption><span>使用<a href="../../../../com/amap/api/location/package-summary.html">com.amap.api.location</a>的程序包</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">程序包</th>
<th class="colLast" scope="col">说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="#com.amap.api.fence">com.amap.api.fence</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.amap.api.location">com.amap.api.location</a></td>
<td class="colLast">&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.amap.api.fence">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表类和解释">
<caption><span><a href="../../../../com/amap/api/fence/package-summary.html">com.amap.api.fence</a>使用的<a href="../../../../com/amap/api/location/package-summary.html">com.amap.api.location</a>中的类</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">类和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/amap/api/location/class-use/AMapLocation.html#com.amap.api.fence">AMapLocation</a>
<div class="block">定位信息类。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/amap/api/location/class-use/DPoint.html#com.amap.api.fence">DPoint</a>
<div class="block">坐标点对象，包含经度和纬度</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.amap.api.location">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表类和解释">
<caption><span><a href="../../../../com/amap/api/location/package-summary.html">com.amap.api.location</a>使用的<a href="../../../../com/amap/api/location/package-summary.html">com.amap.api.location</a>中的类</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">类和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/amap/api/location/class-use/AMapLocation.html#com.amap.api.location">AMapLocation</a>
<div class="block">定位信息类。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/amap/api/location/class-use/AMapLocationClientOption.html#com.amap.api.location">AMapLocationClientOption</a>
<div class="block">定位参数设置，通过这个类可以对定位的相关参数进行设置 <br>
 在<link>AMapLocationClient</link>进行定位时需要这些参数</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/amap/api/location/class-use/AMapLocationClientOption.AMapLocationMode.html#com.amap.api.location">AMapLocationClientOption.AMapLocationMode</a>
<div class="block">定位模式，目前支持三种定位模式<br>
 
 
 高精度定位模式：
 
 在这种定位模式下，将同时使用高德网络定位和卫星定位,优先返回精度高的定位
 
 
 低功耗定位模式：
 
 在这种模式下，将只使用高德网络定位
 
 
 仅设备定位模式：
 
 在这种模式下，将只使用卫星定位。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/amap/api/location/class-use/AMapLocationClientOption.AMapLocationProtocol.html#com.amap.api.location">AMapLocationClientOption.AMapLocationProtocol</a>
<div class="block">定位协议，目前支持二种定位协议<br>
 
 
 http协议：
 
 在这种定位协议下，会使用http请求定位
 
 
 https协议：
 
 在这种定位协议下，会使用https请求定位</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/amap/api/location/class-use/AMapLocationClientOption.AMapLocationPurpose.html#com.amap.api.location">AMapLocationClientOption.AMapLocationPurpose</a>
<div class="block">定位场景</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/amap/api/location/class-use/AMapLocationClientOption.GeoLanguage.html#com.amap.api.location">AMapLocationClientOption.GeoLanguage</a>
<div class="block">设置返回逆地理使用的语言，目前有三种选择<br>
 
 
 默认：
 
 选择这种模式，会根据位置按照相应的语言返回逆地理信息，在国外按英语返回，在国内按中文返回 
 
 中文：
 
 设置只中文后，无论在国外还是国内都为返回中文的逆地理信息 
 
 英文：
 
 设置英文后，无论在国外还是国内都为返回英文的逆地理信息</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/amap/api/location/class-use/AMapLocationListener.html#com.amap.api.location">AMapLocationListener</a>
<div class="block">定位回调接口</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/amap/api/location/class-use/AMapLocationQualityReport.html#com.amap.api.location">AMapLocationQualityReport</a>
<div class="block">高德定位质量报告，随定位结果一起返回
 Created by hongming.wang on 2017/8/29.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/amap/api/location/class-use/CoordinateConverter.html#com.amap.api.location">CoordinateConverter</a>
<div class="block">坐标转换类</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/amap/api/location/class-use/CoordinateConverter.CoordType.html#com.amap.api.location">CoordinateConverter.CoordType</a>
<div class="block">坐标类型</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/amap/api/location/class-use/DPoint.html#com.amap.api.location">DPoint</a>
<div class="block">坐标点对象，包含经度和纬度</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li>类</li>
<li class="navBarCell1Rev">使用</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个</li>
<li>下一个</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/amap/api/location/package-use.html" target="_top">框架</a></li>
<li><a href="package-use.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
