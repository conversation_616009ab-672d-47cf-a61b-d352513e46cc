<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>CoordinateConverter</title>
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="CoordinateConverter";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":10,"i2":10,"i3":10,"i4":9};
var tabs = {65535:["t0","所有方法"],1:["t1","静态方法"],2:["t2","实例方法"],8:["t4","具体方法"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/CoordinateConverter.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/amap/api/location/AMapLocationQualityReport.html" title="com.amap.api.location中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../com/amap/api/location/CoordinateConverter.CoordType.html" title="com.amap.api.location中的枚举"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/amap/api/location/CoordinateConverter.html" target="_top">框架</a></li>
<li><a href="CoordinateConverter.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li><a href="#nested.class.summary">嵌套</a>&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.amap.api.location</div>
<h2 title="类 CoordinateConverter" class="title">类 CoordinateConverter</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.amap.api.location.CoordinateConverter</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">CoordinateConverter</span>
extends java.lang.Object</pre>
<div class="block">坐标转换类</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
<dt><span class="simpleTagLabel">版本:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>嵌套类概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="嵌套类概要表, 列表嵌套类和解释">
<caption><span>嵌套类</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">类和说明</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/CoordinateConverter.CoordType.html" title="com.amap.api.location中的枚举">CoordinateConverter.CoordType</a></span></code>
<div class="block">坐标类型</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/CoordinateConverter.html#CoordinateConverter-Context-">CoordinateConverter</a></span>(Context&nbsp;context)</code>
<div class="block">构造方法</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>所有方法</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">静态方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">实例方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">具体方法</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/CoordinateConverter.html#calculateLineDistance-com.amap.api.location.DPoint-com.amap.api.location.DPoint-">calculateLineDistance</a></span>(<a href="../../../../com/amap/api/location/DPoint.html" title="com.amap.api.location中的类">DPoint</a>&nbsp;startLatlng,
                     <a href="../../../../com/amap/api/location/DPoint.html" title="com.amap.api.location中的类">DPoint</a>&nbsp;endLatlng)</code>
<div class="block">计算两点间距离 单位：米</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/amap/api/location/DPoint.html" title="com.amap.api.location中的类">DPoint</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/CoordinateConverter.html#convert--">convert</a></span>()</code>
<div class="block">进行坐标转换</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../../../com/amap/api/location/CoordinateConverter.html" title="com.amap.api.location中的类">CoordinateConverter</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/CoordinateConverter.html#coord-com.amap.api.location.DPoint-">coord</a></span>(<a href="../../../../com/amap/api/location/DPoint.html" title="com.amap.api.location中的类">DPoint</a>&nbsp;latLng)</code>
<div class="block">设置偏转数据源</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/amap/api/location/CoordinateConverter.html" title="com.amap.api.location中的类">CoordinateConverter</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/CoordinateConverter.html#from-com.amap.api.location.CoordinateConverter.CoordType-">from</a></span>(<a href="../../../../com/amap/api/location/CoordinateConverter.CoordType.html" title="com.amap.api.location中的枚举">CoordinateConverter.CoordType</a>&nbsp;type)</code>
<div class="block">设置偏转源类型</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/CoordinateConverter.html#isAMapDataAvailable-double-double-">isAMapDataAvailable</a></span>(double&nbsp;latitude,
                   double&nbsp;longitude)</code>
<div class="block">是否是高德地图可用数据</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="CoordinateConverter-Context-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>CoordinateConverter</h4>
<pre>public&nbsp;CoordinateConverter(Context&nbsp;context)</pre>
<div class="block">构造方法</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>context</code> - </dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="from-com.amap.api.location.CoordinateConverter.CoordType-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>from</h4>
<pre>public&nbsp;<a href="../../../../com/amap/api/location/CoordinateConverter.html" title="com.amap.api.location中的类">CoordinateConverter</a>&nbsp;from(<a href="../../../../com/amap/api/location/CoordinateConverter.CoordType.html" title="com.amap.api.location中的枚举">CoordinateConverter.CoordType</a>&nbsp;type)</pre>
<div class="block">设置偏转源类型</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>type</code> - 类型见CoordType</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="coord-com.amap.api.location.DPoint-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>coord</h4>
<pre>public&nbsp;<a href="../../../../com/amap/api/location/CoordinateConverter.html" title="com.amap.api.location中的类">CoordinateConverter</a>&nbsp;coord(<a href="../../../../com/amap/api/location/DPoint.html" title="com.amap.api.location中的类">DPoint</a>&nbsp;latLng)
                          throws java.lang.Exception</pre>
<div class="block">设置偏转数据源</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>latLng</code> - 需要转换的坐标点</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>包含需要转换的坐标点的CoordinateConverter对象</dd>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code>java.lang.Exception</code></dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="convert--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>convert</h4>
<pre>public&nbsp;<a href="../../../../com/amap/api/location/DPoint.html" title="com.amap.api.location中的类">DPoint</a>&nbsp;convert()
               throws java.lang.Exception</pre>
<div class="block">进行坐标转换</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>根据相应的偏转类型处理后的坐标点</dd>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code>java.lang.Exception</code></dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="isAMapDataAvailable-double-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isAMapDataAvailable</h4>
<pre>public static&nbsp;boolean&nbsp;isAMapDataAvailable(double&nbsp;latitude,
                                          double&nbsp;longitude)</pre>
<div class="block">是否是高德地图可用数据</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>latitude</code> - 纬度</dd>
<dd><code>longitude</code> - 经度</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>是否是高德地图可用数据</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="calculateLineDistance-com.amap.api.location.DPoint-com.amap.api.location.DPoint-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>calculateLineDistance</h4>
<pre>public static&nbsp;float&nbsp;calculateLineDistance(<a href="../../../../com/amap/api/location/DPoint.html" title="com.amap.api.location中的类">DPoint</a>&nbsp;startLatlng,
                                          <a href="../../../../com/amap/api/location/DPoint.html" title="com.amap.api.location中的类">DPoint</a>&nbsp;endLatlng)</pre>
<div class="block">计算两点间距离 单位：米</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>startLatlng</code> - 起点</dd>
<dd><code>endLatlng</code> - 终点</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>两点间距离 单位：米</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.3.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/CoordinateConverter.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/amap/api/location/AMapLocationQualityReport.html" title="com.amap.api.location中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../com/amap/api/location/CoordinateConverter.CoordType.html" title="com.amap.api.location中的枚举"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/amap/api/location/CoordinateConverter.html" target="_top">框架</a></li>
<li><a href="CoordinateConverter.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li><a href="#nested.class.summary">嵌套</a>&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
