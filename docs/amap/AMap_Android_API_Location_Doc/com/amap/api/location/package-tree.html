<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>com.amap.api.location 类分层结构</title>
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="com.amap.api.location \u7C7B\u5206\u5C42\u7ED3\u6784";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li>类</li>
<li>使用</li>
<li class="navBarCell1Rev">树</li>
<li><a href="../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/amap/api/fence/package-tree.html">上一个</a></li>
<li>下一个</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/amap/api/location/package-tree.html" target="_top">框架</a></li>
<li><a href="package-tree.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 class="title">程序包com.amap.api.location的分层结构</h1>
<span class="packageHierarchyLabel">程序包分层结构:</span>
<ul class="horizontal">
<li><a href="../../../../overview-tree.html">所有程序包</a></li>
</ul>
</div>
<div class="contentContainer">
<h2 title="类分层结构">类分层结构</h2>
<ul>
<li type="circle">java.lang.Object
<ul>
<li type="circle">com.amap.api.location.<a href="../../../../com/amap/api/location/AMapLocationClient.html" title="com.amap.api.location中的类"><span class="typeNameLink">AMapLocationClient</span></a></li>
<li type="circle">com.amap.api.location.<a href="../../../../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类"><span class="typeNameLink">AMapLocationClientOption</span></a> (implements java.lang.Cloneable)</li>
<li type="circle">com.amap.api.location.<a href="../../../../com/amap/api/location/AMapLocationQualityReport.html" title="com.amap.api.location中的类"><span class="typeNameLink">AMapLocationQualityReport</span></a> (implements java.lang.Cloneable)</li>
<li type="circle">com.amap.api.location.<a href="../../../../com/amap/api/location/CoordinateConverter.html" title="com.amap.api.location中的类"><span class="typeNameLink">CoordinateConverter</span></a></li>
<li type="circle">com.amap.api.location.<a href="../../../../com/amap/api/location/DPoint.html" title="com.amap.api.location中的类"><span class="typeNameLink">DPoint</span></a></li>
<li type="circle">Location
<ul>
<li type="circle">com.amap.api.location.<a href="../../../../com/amap/api/location/AMapLocation.html" title="com.amap.api.location中的类"><span class="typeNameLink">AMapLocation</span></a> (implements java.lang.Cloneable)</li>
</ul>
</li>
</ul>
</li>
</ul>
<h2 title="接口分层结构">接口分层结构</h2>
<ul>
<li type="circle">com.amap.api.location.<a href="../../../../com/amap/api/location/AMapLocationListener.html" title="com.amap.api.location中的接口"><span class="typeNameLink">AMapLocationListener</span></a></li>
</ul>
<h2 title="枚举分层结构">枚举分层结构</h2>
<ul>
<li type="circle">java.lang.Object
<ul>
<li type="circle">java.lang.Enum&lt;E&gt; (implements java.lang.Comparable&lt;T&gt;, java.io.Serializable)
<ul>
<li type="circle">com.amap.api.location.<a href="../../../../com/amap/api/location/AMapLocationClientOption.AMapLocationProtocol.html" title="com.amap.api.location中的枚举"><span class="typeNameLink">AMapLocationClientOption.AMapLocationProtocol</span></a></li>
<li type="circle">com.amap.api.location.<a href="../../../../com/amap/api/location/AMapLocationClientOption.GeoLanguage.html" title="com.amap.api.location中的枚举"><span class="typeNameLink">AMapLocationClientOption.GeoLanguage</span></a></li>
<li type="circle">com.amap.api.location.<a href="../../../../com/amap/api/location/AMapLocationClientOption.AMapLocationPurpose.html" title="com.amap.api.location中的枚举"><span class="typeNameLink">AMapLocationClientOption.AMapLocationPurpose</span></a></li>
<li type="circle">com.amap.api.location.<a href="../../../../com/amap/api/location/AMapLocationClientOption.AMapLocationMode.html" title="com.amap.api.location中的枚举"><span class="typeNameLink">AMapLocationClientOption.AMapLocationMode</span></a></li>
<li type="circle">com.amap.api.location.<a href="../../../../com/amap/api/location/CoordinateConverter.CoordType.html" title="com.amap.api.location中的枚举"><span class="typeNameLink">CoordinateConverter.CoordType</span></a></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li>类</li>
<li>使用</li>
<li class="navBarCell1Rev">树</li>
<li><a href="../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/amap/api/fence/package-tree.html">上一个</a></li>
<li>下一个</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/amap/api/location/package-tree.html" target="_top">框架</a></li>
<li><a href="package-tree.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
