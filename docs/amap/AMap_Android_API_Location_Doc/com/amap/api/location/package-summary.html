<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>com.amap.api.location</title>
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="com.amap.api.location";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../overview-summary.html">概览</a></li>
<li class="navBarCell1Rev">程序包</li>
<li>类</li>
<li><a href="package-use.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/amap/api/fence/package-summary.html">上一个程序包</a></li>
<li>下一个程序包</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/amap/api/location/package-summary.html" target="_top">框架</a></li>
<li><a href="package-summary.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 title="程序包" class="title">程序包&nbsp;com.amap.api.location</h1>
</div>
<div class="contentContainer">
<ul class="blockList">
<li class="blockList">
<table class="typeSummary" border="0" cellpadding="3" cellspacing="0" summary="接口概要表, 列表接口和解释">
<caption><span>接口概要</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">接口</th>
<th class="colLast" scope="col">说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/amap/api/location/AMapLocationListener.html" title="com.amap.api.location中的接口">AMapLocationListener</a></td>
<td class="colLast">
<div class="block">定位回调接口</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="typeSummary" border="0" cellpadding="3" cellspacing="0" summary="类概要表, 列表类和解释">
<caption><span>类概要</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">类</th>
<th class="colLast" scope="col">说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/amap/api/location/AMapLocation.html" title="com.amap.api.location中的类">AMapLocation</a></td>
<td class="colLast">
<div class="block">定位信息类。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/amap/api/location/AMapLocationClient.html" title="com.amap.api.location中的类">AMapLocationClient</a></td>
<td class="colLast">
<div class="block">定位服务类。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a></td>
<td class="colLast">
<div class="block">定位参数设置，通过这个类可以对定位的相关参数进行设置 <br>
 在<link>AMapLocationClient</link>进行定位时需要这些参数</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/amap/api/location/AMapLocationQualityReport.html" title="com.amap.api.location中的类">AMapLocationQualityReport</a></td>
<td class="colLast">
<div class="block">高德定位质量报告，随定位结果一起返回
 Created by hongming.wang on 2017/8/29.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/amap/api/location/CoordinateConverter.html" title="com.amap.api.location中的类">CoordinateConverter</a></td>
<td class="colLast">
<div class="block">坐标转换类</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/amap/api/location/DPoint.html" title="com.amap.api.location中的类">DPoint</a></td>
<td class="colLast">
<div class="block">坐标点对象，包含经度和纬度</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="typeSummary" border="0" cellpadding="3" cellspacing="0" summary="枚举概要表, 列表枚举和解释">
<caption><span>枚举概要</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">枚举</th>
<th class="colLast" scope="col">说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/amap/api/location/AMapLocationClientOption.AMapLocationMode.html" title="com.amap.api.location中的枚举">AMapLocationClientOption.AMapLocationMode</a></td>
<td class="colLast">
<div class="block">定位模式，目前支持三种定位模式<br>
 
 
 高精度定位模式：
 
 在这种定位模式下，将同时使用高德网络定位和卫星定位,优先返回精度高的定位
 
 
 低功耗定位模式：
 
 在这种模式下，将只使用高德网络定位
 
 
 仅设备定位模式：
 
 在这种模式下，将只使用卫星定位。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/amap/api/location/AMapLocationClientOption.AMapLocationProtocol.html" title="com.amap.api.location中的枚举">AMapLocationClientOption.AMapLocationProtocol</a></td>
<td class="colLast">
<div class="block">定位协议，目前支持二种定位协议<br>
 
 
 http协议：
 
 在这种定位协议下，会使用http请求定位
 
 
 https协议：
 
 在这种定位协议下，会使用https请求定位</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/amap/api/location/AMapLocationClientOption.AMapLocationPurpose.html" title="com.amap.api.location中的枚举">AMapLocationClientOption.AMapLocationPurpose</a></td>
<td class="colLast">
<div class="block">定位场景</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/amap/api/location/AMapLocationClientOption.GeoLanguage.html" title="com.amap.api.location中的枚举">AMapLocationClientOption.GeoLanguage</a></td>
<td class="colLast">
<div class="block">设置返回逆地理使用的语言，目前有三种选择<br>
 
 
 默认：
 
 选择这种模式，会根据位置按照相应的语言返回逆地理信息，在国外按英语返回，在国内按中文返回 
 
 中文：
 
 设置只中文后，无论在国外还是国内都为返回中文的逆地理信息 
 
 英文：
 
 设置英文后，无论在国外还是国内都为返回英文的逆地理信息</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/amap/api/location/CoordinateConverter.CoordType.html" title="com.amap.api.location中的枚举">CoordinateConverter.CoordType</a></td>
<td class="colLast">
<div class="block">坐标类型</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../overview-summary.html">概览</a></li>
<li class="navBarCell1Rev">程序包</li>
<li>类</li>
<li><a href="package-use.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/amap/api/fence/package-summary.html">上一个程序包</a></li>
<li>下一个程序包</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/amap/api/location/package-summary.html" target="_top">框架</a></li>
<li><a href="package-summary.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
