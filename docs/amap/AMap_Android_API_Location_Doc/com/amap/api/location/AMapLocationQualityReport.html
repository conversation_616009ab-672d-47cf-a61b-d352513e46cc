<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>AMapLocationQualityReport</title>
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="AMapLocationQualityReport";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10};
var tabs = {65535:["t0","所有方法"],2:["t2","实例方法"],8:["t4","具体方法"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/AMapLocationQualityReport.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/amap/api/location/AMapLocationListener.html" title="com.amap.api.location中的接口"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../com/amap/api/location/CoordinateConverter.html" title="com.amap.api.location中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/amap/api/location/AMapLocationQualityReport.html" target="_top">框架</a></li>
<li><a href="AMapLocationQualityReport.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li><a href="#field.summary">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li><a href="#field.detail">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.amap.api.location</div>
<h2 title="类 AMapLocationQualityReport" class="title">类 AMapLocationQualityReport</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.amap.api.location.AMapLocationQualityReport</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>所有已实现的接口:</dt>
<dd>java.lang.Cloneable</dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">AMapLocationQualityReport</span>
extends java.lang.Object
implements java.lang.Cloneable</pre>
<div class="block">高德定位质量报告，随定位结果一起返回
 Created by hongming.wang on 2017/8/29.</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.6.0</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>字段概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="字段概要表, 列表字段和解释">
<caption><span>字段</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">字段和说明</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocationQualityReport.html#GPS_STATUS_MODE_SAVING">GPS_STATUS_MODE_SAVING</a></span></code>
<div class="block">卫星定位状态--选择的定位模式中不包含卫星定位
 
     Android 4.4以上的手机设置中开启了定位（位置）服务，但是选择的模式为省电模式，不包含卫星定位<br>
     建议选择包含gps定位的模式（例如：高精度、仅设备）
 </div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocationQualityReport.html#GPS_STATUS_NOGPSPERMISSION">GPS_STATUS_NOGPSPERMISSION</a></span></code>
<div class="block">卫星定位状态--没有GPS定位权限
 
  如果没有GPS定位权限无法进行卫星定位, 建议在安全软件中授予GPS定位权限
 </div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocationQualityReport.html#GPS_STATUS_NOGPSPROVIDER">GPS_STATUS_NOGPSPROVIDER</a></span></code>
<div class="block">卫星定位状态--手机中没有GPS Provider，无法进行卫星定位</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocationQualityReport.html#GPS_STATUS_OFF">GPS_STATUS_OFF</a></span></code>
<div class="block">卫星定位状态--GPS开关关闭
 
     建议开启GPS开关，提高定位质量<br>
     Android 4.4以下的手机是gps开关关闭-建议开启gps开关<br>
     Android 4.4以上的手机设置中关闭了定位（位置）服务-建议开启定位服务，并选择包含gps的定位模式<br>

 </div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocationQualityReport.html#GPS_STATUS_OK">GPS_STATUS_OK</a></span></code>
<div class="block">卫星定位状态--正常</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocationQualityReport.html#AMapLocationQualityReport--">AMapLocationQualityReport</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>所有方法</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">实例方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">具体方法</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocationQualityReport.html#getAdviseMessage--">getAdviseMessage</a></span>()</code>
<div class="block">获取提示语义,状态良好时，返回的是内容为空
 
     根据当前的质量报告，给出相应的建议
 </div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocationQualityReport.html#getGPSSatellites--">getGPSSatellites</a></span>()</code>
<div class="block">获取当前的卫星数， 只有在非低功耗模式下此值才有效</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocationQualityReport.html#getGPSStatus--">getGPSStatus</a></span>()</code>
<div class="block">获取卫星状态信息，只有在非低功耗模式下此值才有效</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocationQualityReport.html#getNetUseTime--">getNetUseTime</a></span>()</code>
<div class="block">获取网络定位时的网络耗时 单位：毫秒</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocationQualityReport.html#getNetworkType--">getNetworkType</a></span>()</code>
<div class="block">获取网络连接类型（2G、3G、4G、WIFI)</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocationQualityReport.html#isInstalledHighDangerMockApp--">isInstalledHighDangerMockApp</a></span>()</code>
<div class="block">是否安装了高危位置模拟软件
 首次定位可能没有结果</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocationQualityReport.html#isWifiAble--">isWifiAble</a></span>()</code>
<div class="block">wifi开关是否打开
 
     如果wifi关闭建议打开wifi开关，提高定位质量
 </div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>字段详细资料</h3>
<a name="GPS_STATUS_OK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>GPS_STATUS_OK</h4>
<pre>public static final&nbsp;int GPS_STATUS_OK</pre>
<div class="block">卫星定位状态--正常</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.6.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.location.AMapLocationQualityReport.GPS_STATUS_OK">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="GPS_STATUS_NOGPSPROVIDER">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>GPS_STATUS_NOGPSPROVIDER</h4>
<pre>public static final&nbsp;int GPS_STATUS_NOGPSPROVIDER</pre>
<div class="block">卫星定位状态--手机中没有GPS Provider，无法进行卫星定位</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.6.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.location.AMapLocationQualityReport.GPS_STATUS_NOGPSPROVIDER">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="GPS_STATUS_OFF">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>GPS_STATUS_OFF</h4>
<pre>public static final&nbsp;int GPS_STATUS_OFF</pre>
<div class="block">卫星定位状态--GPS开关关闭
 <p>
     建议开启GPS开关，提高定位质量<br>
     Android 4.4以下的手机是gps开关关闭-建议开启gps开关<br>
     Android 4.4以上的手机设置中关闭了定位（位置）服务-建议开启定位服务，并选择包含gps的定位模式<br>

 </p></div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.6.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.location.AMapLocationQualityReport.GPS_STATUS_OFF">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="GPS_STATUS_MODE_SAVING">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>GPS_STATUS_MODE_SAVING</h4>
<pre>public static final&nbsp;int GPS_STATUS_MODE_SAVING</pre>
<div class="block">卫星定位状态--选择的定位模式中不包含卫星定位
 <p>
     Android 4.4以上的手机设置中开启了定位（位置）服务，但是选择的模式为省电模式，不包含卫星定位<br>
     建议选择包含gps定位的模式（例如：高精度、仅设备）
 </p></div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.6.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.location.AMapLocationQualityReport.GPS_STATUS_MODE_SAVING">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="GPS_STATUS_NOGPSPERMISSION">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>GPS_STATUS_NOGPSPERMISSION</h4>
<pre>public static final&nbsp;int GPS_STATUS_NOGPSPERMISSION</pre>
<div class="block">卫星定位状态--没有GPS定位权限
 <p>
  如果没有GPS定位权限无法进行卫星定位, 建议在安全软件中授予GPS定位权限
 </p></div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.6.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.location.AMapLocationQualityReport.GPS_STATUS_NOGPSPERMISSION">常量字段值</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="AMapLocationQualityReport--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>AMapLocationQualityReport</h4>
<pre>public&nbsp;AMapLocationQualityReport()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="isWifiAble--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isWifiAble</h4>
<pre>public&nbsp;boolean&nbsp;isWifiAble()</pre>
<div class="block">wifi开关是否打开
 <P>
     如果wifi关闭建议打开wifi开关，提高定位质量
 </P></div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>wifi开关是否打开，true:开; false:关闭</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.6.0</dd>
</dl>
</li>
</ul>
<a name="getGPSStatus--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getGPSStatus</h4>
<pre>public&nbsp;int&nbsp;getGPSStatus()</pre>
<div class="block">获取卫星状态信息，只有在非低功耗模式下此值才有效</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>卫星状态信息</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.6.0</dd>
</dl>
</li>
</ul>
<a name="getGPSSatellites--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getGPSSatellites</h4>
<pre>public&nbsp;int&nbsp;getGPSSatellites()</pre>
<div class="block">获取当前的卫星数， 只有在非低功耗模式下此值才有效</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>当前的星数</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.6.0, 3.6.0</dd>
</dl>
</li>
</ul>
<a name="getNetworkType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNetworkType</h4>
<pre>public&nbsp;java.lang.String&nbsp;getNetworkType()</pre>
<div class="block">获取网络连接类型（2G、3G、4G、WIFI)</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>网络连接类型</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.7.0</dd>
</dl>
</li>
</ul>
<a name="getNetUseTime--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNetUseTime</h4>
<pre>public&nbsp;long&nbsp;getNetUseTime()</pre>
<div class="block">获取网络定位时的网络耗时 单位：毫秒</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.7.0</dd>
</dl>
</li>
</ul>
<a name="isInstalledHighDangerMockApp--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isInstalledHighDangerMockApp</h4>
<pre>public&nbsp;boolean&nbsp;isInstalledHighDangerMockApp()</pre>
<div class="block">是否安装了高危位置模拟软件
 首次定位可能没有结果</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>true:有安装; false:没有安装</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>4.6.0</dd>
</dl>
</li>
</ul>
<a name="getAdviseMessage--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getAdviseMessage</h4>
<pre>public&nbsp;java.lang.String&nbsp;getAdviseMessage()</pre>
<div class="block">获取提示语义,状态良好时，返回的是内容为空
 <p>
     根据当前的质量报告，给出相应的建议
 </p></div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>定位语义</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.7.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/AMapLocationQualityReport.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/amap/api/location/AMapLocationListener.html" title="com.amap.api.location中的接口"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../com/amap/api/location/CoordinateConverter.html" title="com.amap.api.location中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/amap/api/location/AMapLocationQualityReport.html" target="_top">框架</a></li>
<li><a href="AMapLocationQualityReport.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li><a href="#field.summary">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li><a href="#field.detail">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
