<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>AMapLocationClientOption</title>
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="AMapLocationClientOption";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":9,"i15":10,"i16":42,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10,"i27":10,"i28":9,"i29":10,"i30":10,"i31":10,"i32":10,"i33":10,"i34":9,"i35":10,"i36":10,"i37":42,"i38":10,"i39":10};
var tabs = {65535:["t0","所有方法"],1:["t1","静态方法"],2:["t2","实例方法"],8:["t4","具体方法"],32:["t6","已过时的方法"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/AMapLocationClientOption.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/amap/api/location/AMapLocationClient.html" title="com.amap.api.location中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../com/amap/api/location/AMapLocationClientOption.AMapLocationMode.html" title="com.amap.api.location中的枚举"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/amap/api/location/AMapLocationClientOption.html" target="_top">框架</a></li>
<li><a href="AMapLocationClientOption.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li><a href="#nested.class.summary">嵌套</a>&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.amap.api.location</div>
<h2 title="类 AMapLocationClientOption" class="title">类 AMapLocationClientOption</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.amap.api.location.AMapLocationClientOption</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>所有已实现的接口:</dt>
<dd>java.lang.Cloneable</dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">AMapLocationClientOption</span>
extends java.lang.Object
implements java.lang.Cloneable</pre>
<div class="block">定位参数设置，通过这个类可以对定位的相关参数进行设置 <br>
 在<link>AMapLocationClient</link>进行定位时需要这些参数</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.0</dd>
<dt><span class="simpleTagLabel">版本:</span></dt>
<dd>2.0.0</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>嵌套类概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="嵌套类概要表, 列表嵌套类和解释">
<caption><span>嵌套类</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">类和说明</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocationClientOption.AMapLocationMode.html" title="com.amap.api.location中的枚举">AMapLocationClientOption.AMapLocationMode</a></span></code>
<div class="block">定位模式，目前支持三种定位模式<br>
 
 
 高精度定位模式：
 
 在这种定位模式下，将同时使用高德网络定位和卫星定位,优先返回精度高的定位
 
 
 低功耗定位模式：
 
 在这种模式下，将只使用高德网络定位
 
 
 仅设备定位模式：
 
 在这种模式下，将只使用卫星定位。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocationClientOption.AMapLocationProtocol.html" title="com.amap.api.location中的枚举">AMapLocationClientOption.AMapLocationProtocol</a></span></code>
<div class="block">定位协议，目前支持二种定位协议<br>
 
 
 http协议：
 
 在这种定位协议下，会使用http请求定位
 
 
 https协议：
 
 在这种定位协议下，会使用https请求定位</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocationClientOption.AMapLocationPurpose.html" title="com.amap.api.location中的枚举">AMapLocationClientOption.AMapLocationPurpose</a></span></code>
<div class="block">定位场景</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocationClientOption.GeoLanguage.html" title="com.amap.api.location中的枚举">AMapLocationClientOption.GeoLanguage</a></span></code>
<div class="block">设置返回逆地理使用的语言，目前有三种选择<br>
 
 
 默认：
 
 选择这种模式，会根据位置按照相应的语言返回逆地理信息，在国外按英语返回，在国内按中文返回 
 
 中文：
 
 设置只中文后，无论在国外还是国内都为返回中文的逆地理信息 
 
 英文：
 
 设置英文后，无论在国外还是国内都为返回英文的逆地理信息</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocationClientOption.html#AMapLocationClientOption--">AMapLocationClientOption</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>所有方法</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">静态方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">实例方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">具体方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t6" class="tableTab"><span><a href="javascript:show(32);">已过时的方法</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../../../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocationClientOption.html#clone--">clone</a></span>()</code>
<div class="block">获取AMapLocationClientOption对象的拷贝</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocationClientOption.html#getDeviceModeDistanceFilter--">getDeviceModeDistanceFilter</a></span>()</code>
<div class="block">获取仅设备模式/高精度模式的系统定位自动回调最少间隔距离值 <br>
 默认值：0米
 
     只有当定位模式为<a href="../../../../com/amap/api/location/AMapLocationClientOption.AMapLocationMode.html#Device_Sensors"><code>AMapLocationClientOption.AMapLocationMode.Device_Sensors</code></a>（仅设备模式）或 <a href="../../../../com/amap/api/location/AMapLocationClientOption.AMapLocationMode.html#Hight_Accuracy"><code>AMapLocationClientOption.AMapLocationMode.Hight_Accuracy</code></a>（高精度模式）有效
 </div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocationClientOption.html#getGpsFirstTimeout--">getGpsFirstTimeout</a></span>()</code>
<div class="block">获取优先返回定位信息时等待GPS结果的超时时间</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocationClientOption.html#getHttpTimeOut--">getHttpTimeOut</a></span>()</code>
<div class="block">获取联网超时时间<br>
 单位：毫秒<br>
 默认值：30000毫秒 <br></div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocationClientOption.html#getInterval--">getInterval</a></span>()</code>
<div class="block">获取发起定位请求的时间间隔 <br>
 默认值：2000毫秒</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/amap/api/location/AMapLocationClientOption.AMapLocationMode.html" title="com.amap.api.location中的枚举">AMapLocationClientOption.AMapLocationMode</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocationClientOption.html#getLocationMode--">getLocationMode</a></span>()</code>
<div class="block">获取定位模式 默认值：Hight_Accuracy 高精度模式<br></div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="../../../../com/amap/api/location/AMapLocationClientOption.AMapLocationProtocol.html" title="com.amap.api.location中的枚举">AMapLocationClientOption.AMapLocationProtocol</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocationClientOption.html#getLocationProtocol--">getLocationProtocol</a></span>()</code>
<div class="block">获取定位协议 默认值：HTTP http协议<br></div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/amap/api/location/AMapLocationClientOption.AMapLocationPurpose.html" title="com.amap.api.location中的枚举">AMapLocationClientOption.AMapLocationPurpose</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocationClientOption.html#getLocationPurpose--">getLocationPurpose</a></span>()</code>
<div class="block">获取定位场景</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocationClientOption.html#isBeidouFirst--">isBeidouFirst</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocationClientOption.html#isGpsFirst--">isGpsFirst</a></span>()</code>
<div class="block">获取高精度模式下单次定位是否优先返回卫星定位信息<br>
 默认值：false <br>
 
 <b>只有在单次定位高精度定位模式下有效<br>
 为true时，会等待卫星定位结果返回，最多等待30秒，若30秒后仍无卫星定位结果返回，返回网络定位结果</b></div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocationClientOption.html#isKillProcess--">isKillProcess</a></span>()</code>
<div class="block">获取退出时是否杀死进程<br>
 默认值:false, 不杀死 <br>
 
 <font color="red"><b>注意：如果设置为true，并且配置的service不是remote的则会杀死当前页面进程，请慎重使用
 </b> </font>
 </div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocationClientOption.html#isMockEnable--">isMockEnable</a></span>()</code>
<div class="block">获取是否允许模拟位置</br>
 
     从<b>3.4.0</b>开始，默认值为true，允许模拟;<br>
     <b>3.4.0</b>之前的版本，默认值为false，不允许模拟
 </div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocationClientOption.html#isNeedAddress--">isNeedAddress</a></span>()</code>
<div class="block">获取是否需要地址信息 <br>
 默认值：true 返回地址信息<br>
 <b>2.9.0</b>之前的版本定位类型为<a href="../../../../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS"><code>AMapLocation.LOCATION_TYPE_GPS</code></a>时不会返回地址信息<br>
 自<b>2.9.0</b>版本开始，当类型为<a href="../../../../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS"><code>AMapLocation.LOCATION_TYPE_GPS</code></a>时也可以返回地址信息(需要网络通畅，第一次有可能没有地址信息返回）</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocationClientOption.html#isOnceLocation--">isOnceLocation</a></span>()</code>
<div class="block">是否单次单次定位<br>
 默认值：false</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocationClientOption.html#isOpenAlwaysScanWifi--">isOpenAlwaysScanWifi</a></span>()</code>
<div class="block">是否开启始终wifi扫描
 
     只有设置了android.permission.WRITE_SECURE_SETTINGS权限后才会开启
     开启后，即使关闭wifi开关的情况下也会扫描wifi
     默认值为：true, 开启wifi始终扫描
 </div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocationClientOption.html#isSelfStartServiceEnable--">isSelfStartServiceEnable</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocationClientOption.html#isWifiActiveScan--">isWifiActiveScan</a></span>()</code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;&nbsp;</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocationClientOption.html#isWifiScan--">isWifiScan</a></span>()</code>
<div class="block">获取是否允许主动调用WIFI刷新</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code><a href="../../../../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocationClientOption.html#setBeidouFirst-boolean-">setBeidouFirst</a></span>(boolean&nbsp;isBeidouFirst)</code>&nbsp;</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocationClientOption.html#setDeviceModeDistanceFilter-float-">setDeviceModeDistanceFilter</a></span>(float&nbsp;distanceFilter)</code>
<div class="block">设置仅设备模式/高精度模式的系统定位自动回调最少间隔距离值 <br>
 单位：米 <br>
 默认值：0米
 
     只有当定位模式为<a href="../../../../com/amap/api/location/AMapLocationClientOption.AMapLocationMode.html#Device_Sensors"><code>AMapLocationClientOption.AMapLocationMode.Device_Sensors</code></a>（仅设备模式）或 <a href="../../../../com/amap/api/location/AMapLocationClientOption.AMapLocationMode.html#Hight_Accuracy"><code>AMapLocationClientOption.AMapLocationMode.Hight_Accuracy</code></a>（高精度模式）有效，值小于0时无效
 </div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code><a href="../../../../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocationClientOption.html#setGeoLanguage-com.amap.api.location.AMapLocationClientOption.GeoLanguage-">setGeoLanguage</a></span>(<a href="../../../../com/amap/api/location/AMapLocationClientOption.GeoLanguage.html" title="com.amap.api.location中的枚举">AMapLocationClientOption.GeoLanguage</a>&nbsp;geoLanguage)</code>
<div class="block">设置逆地理信息的语言,目前之中中文和英文
 
     默认值：<a href="../../../../com/amap/api/location/AMapLocationClientOption.GeoLanguage.html#DEFAULT"><code>AMapLocationClientOption.GeoLanguage.DEFAULT</code></a>
 </div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocationClientOption.html#setGpsFirst-boolean-">setGpsFirst</a></span>(boolean&nbsp;isGpsFirst)</code>
<div class="block">设置首次定位是否等待卫星定位结果<br>
 默认值：false <br>
 <b>只有在单次定位高精度定位模式下有效</b><br>
 设置为true时，会等待卫星定位结果返回，最多等待30秒，若30秒后仍无卫星定位结果返回，返回网络定位结果<br>
 从<b>4.5.0</b>版本开始等待卫星定位结果返回的时间可以通过 <a href="../../../../com/amap/api/location/AMapLocationClientOption.html#setGpsFirstTimeout-long-"><code>AMapLocationClientOption.setGpsFirstTimeout(long)</code></a>进行设置
 </div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code><a href="../../../../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocationClientOption.html#setGpsFirstTimeout-long-">setGpsFirstTimeout</a></span>(long&nbsp;timeout)</code>
<div class="block">设置优先返回卫星定位信息时等待卫星定位结果的超时时间，单位：毫秒
 
     只有在<a href="../../../../com/amap/api/location/AMapLocationClientOption.html#setGpsFirst-boolean-"><code>AMapLocationClientOption.setGpsFirst(boolean)</code></a>设置为true时才有效。</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocationClientOption.html#setHttpTimeOut-long-">setHttpTimeOut</a></span>(long&nbsp;httpTimeOut)</code>
<div class="block">设置联网超时时间<br>
 单位：毫秒<br>
 默认值：30000毫秒 <br></div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code><a href="../../../../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocationClientOption.html#setInterval-long-">setInterval</a></span>(long&nbsp;interval)</code>
<div class="block">设置发起定位请求的时间间隔<br>
 单位：毫秒<br>
 默认值：2000毫秒</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocationClientOption.html#setKillProcess-boolean-">setKillProcess</a></span>(boolean&nbsp;isKillProcess)</code>
<div class="block">设置退出时是否杀死进程<br>
 默认值:false, 不杀死 <br>
 
 <font color="red"><b>注意：如果设置为true，并且配置的service不是remote的则会杀死当前页面进程，请慎重使用
 </b> </font>
 </div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code><a href="../../../../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocationClientOption.html#setLocationCacheEnable-boolean-">setLocationCacheEnable</a></span>(boolean&nbsp;isLocationCacheEnable)</code>
<div class="block">设置是否使用缓存策略, 默认为true 使用缓存策略</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocationClientOption.html#setLocationMode-com.amap.api.location.AMapLocationClientOption.AMapLocationMode-">setLocationMode</a></span>(<a href="../../../../com/amap/api/location/AMapLocationClientOption.AMapLocationMode.html" title="com.amap.api.location中的枚举">AMapLocationClientOption.AMapLocationMode</a>&nbsp;locationMode)</code>
<div class="block">设置定位模式</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocationClientOption.html#setLocationProtocol-com.amap.api.location.AMapLocationClientOption.AMapLocationProtocol-">setLocationProtocol</a></span>(<a href="../../../../com/amap/api/location/AMapLocationClientOption.AMapLocationProtocol.html" title="com.amap.api.location中的枚举">AMapLocationClientOption.AMapLocationProtocol</a>&nbsp;amapLocationProtocol)</code>
<div class="block">设置定位协议</div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocationClientOption.html#setLocationPurpose-com.amap.api.location.AMapLocationClientOption.AMapLocationPurpose-">setLocationPurpose</a></span>(<a href="../../../../com/amap/api/location/AMapLocationClientOption.AMapLocationPurpose.html" title="com.amap.api.location中的枚举">AMapLocationClientOption.AMapLocationPurpose</a>&nbsp;locationPurpose)</code>
<div class="block">设置定位场景，根据场景快速修改option，不支持动态改变，修改后需要调用<a href="../../../../com/amap/api/location/AMapLocationClient.html#startLocation--"><code>AMapLocationClient.startLocation()</code></a>使其生效<br>
 
     当不需要场景时，可以设置为NULL，
 
 <b>注意：
 <font color="red">不建议设置场景和自定义option混合使用</font>
 设置场景后，如果已经开始定位了，建议调用一次<a href="../../../../com/amap/api/location/AMapLocationClient.html#stopLocation--"><code>AMapLocationClient.stopLocation()</code></a>,然后主动调用一次<a href="../../../../com/amap/api/location/AMapLocationClient.html#startLocation--"><code>AMapLocationClient.startLocation()</code></a>以保证option正确生效
 当主动设置的option和场景中的option有冲突时，以后设置的为准，
 比如：签到场景中默认的为单次定位，当主动设置option为连续定位时，
 如果先设置的场景，后改变的option，这时如果不调用startLocation不会变为连续定位，如果调用了startLocation则会变为连续定位，
 如果先改变option，后设置场景为签到场景，则会变为单次定位</b></div>
</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code><a href="../../../../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocationClientOption.html#setMockEnable-boolean-">setMockEnable</a></span>(boolean&nbsp;isMockEnable)</code>
<div class="block">设置是否允许模拟位置 <br>
 
     从<b>3.4.0</b>开始，默认值为true，允许模拟;<br>
     <b>3.4.0</b>之前的版本，默认值为false，不允许模拟
 </div>
</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocationClientOption.html#setNeedAddress-boolean-">setNeedAddress</a></span>(boolean&nbsp;isNeedAddress)</code>
<div class="block">设置是否返回地址信息，默认返回地址信息 <br>
 默认值：true, 返回地址信息
 
 <b>2.9.0</b>之前的版本定位类型为<a href="../../../../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS"><code>AMapLocation.LOCATION_TYPE_GPS</code></a>时不会返回地址信息<br>
 自<b>2.9.0</b>版本开始，当类型为<a href="../../../../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS"><code>AMapLocation.LOCATION_TYPE_GPS</code></a>时也可以返回地址信息(需要网络通畅，第一次有可能没有地址信息返回）
 </div>
</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code><a href="../../../../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocationClientOption.html#setOnceLocation-boolean-">setOnceLocation</a></span>(boolean&nbsp;isOnceLocation)</code>
<div class="block">设置是否单次定位<br>
 默认值：false</div>
</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocationClientOption.html#setOnceLocationLatest-boolean-">setOnceLocationLatest</a></span>(boolean&nbsp;isOnceLocationLatest)</code>
<div class="block">设置定位是否等待WIFI列表刷新
 定位精度会更高，但是定位速度会变慢1-3秒
 
     <b>从3.7.0版本开始，支持连续定位（连续定位时首次会等待刷新）</b>
     3.7.0之前的版本，仅适用于单次定位，当设置为true时，连续定位会自动变为单次定位,
           </div>
</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocationClientOption.html#setOpenAlwaysScanWifi-boolean-">setOpenAlwaysScanWifi</a></span>(boolean&nbsp;openAlwaysScanWifi)</code>
<div class="block">设置是否开启wifi始终扫描
 
     只有设置了android.permission.WRITE_SECURE_SETTINGS权限后才会开启
     开启后，即使关闭wifi开关的情况下也会扫描wifi
     默认值为：true, 开启wifi始终扫描
     此方法为静态方法，设置一次后其他定位Client也会生效
 </div>
</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocationClientOption.html#setSelfStartServiceEnable-boolean-">setSelfStartServiceEnable</a></span>(boolean&nbsp;selfStartServiceEnable)</code>
<div class="block">设置是否允许定位服务自启动，用于连续定位场景下定位服务被系统异常杀死时重新启动。</div>
</td>
</tr>
<tr id="i36" class="altColor">
<td class="colFirst"><code><a href="../../../../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocationClientOption.html#setSensorEnable-boolean-">setSensorEnable</a></span>(boolean&nbsp;sensorEnable)</code>
<div class="block">设置是否使用设备传感器
 
     默认值：false 不使用设备传感器
 </div>
</td>
</tr>
<tr id="i37" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocationClientOption.html#setWifiActiveScan-boolean-">setWifiActiveScan</a></span>(boolean&nbsp;isWifiActiveScan)</code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;&nbsp;</div>
</td>
</tr>
<tr id="i38" class="altColor">
<td class="colFirst"><code><a href="../../../../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocationClientOption.html#setWifiScan-boolean-">setWifiScan</a></span>(boolean&nbsp;isWifiPassiveScan)</code>
<div class="block">设置是否允许调用WIFI刷新
 
 默认值为true，当设置为false时会停止主动调用WIFI刷新，将会极大程度影响定位精度，但可以有效的降低定位耗电<br>
 </div>
</td>
</tr>
<tr id="i39" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/location/AMapLocationClientOption.html#toString--">toString</a></span>()</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="AMapLocationClientOption--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>AMapLocationClientOption</h4>
<pre>public&nbsp;AMapLocationClientOption()</pre>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="isSelfStartServiceEnable--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isSelfStartServiceEnable</h4>
<pre>public&nbsp;boolean&nbsp;isSelfStartServiceEnable()</pre>
</li>
</ul>
<a name="setSelfStartServiceEnable-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSelfStartServiceEnable</h4>
<pre>public&nbsp;void&nbsp;setSelfStartServiceEnable(boolean&nbsp;selfStartServiceEnable)</pre>
<div class="block">设置是否允许定位服务自启动，用于连续定位场景下定位服务被系统异常杀死时重新启动。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>selfStartServiceEnable</code> - </dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.4.0</dd>
</dl>
</li>
</ul>
<a name="isMockEnable--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isMockEnable</h4>
<pre>public&nbsp;boolean&nbsp;isMockEnable()</pre>
<div class="block">获取是否允许模拟位置</br>
 <p>
     从<b>3.4.0</b>开始，默认值为true，允许模拟;<br>
     <b>3.4.0</b>之前的版本，默认值为false，不允许模拟
 </p></div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>true:允许模拟位置<br>
         false:不允许模拟位置</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.0</dd>
</dl>
</li>
</ul>
<a name="setMockEnable-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMockEnable</h4>
<pre>public&nbsp;<a href="../../../../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a>&nbsp;setMockEnable(boolean&nbsp;isMockEnable)</pre>
<div class="block">设置是否允许模拟位置 <br>
 <p>
     从<b>3.4.0</b>开始，默认值为true，允许模拟;<br>
     <b>3.4.0</b>之前的版本，默认值为false，不允许模拟
 </p></div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>isMockEnable</code> - 是否允许模拟位置<br>
            true:允许模拟位置<br>
            false：不允许模拟位置</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.0</dd>
</dl>
</li>
</ul>
<a name="getInterval--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getInterval</h4>
<pre>public&nbsp;long&nbsp;getInterval()</pre>
<div class="block">获取发起定位请求的时间间隔 <br>
 默认值：2000毫秒</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>时间间隔<br>
         单位：毫秒</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.0</dd>
</dl>
</li>
</ul>
<a name="setInterval-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setInterval</h4>
<pre>public&nbsp;<a href="../../../../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a>&nbsp;setInterval(long&nbsp;interval)</pre>
<div class="block">设置发起定位请求的时间间隔<br>
 单位：毫秒<br>
 默认值：2000毫秒</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>interval</code> - 时间间隔 <br>
            单位：毫秒<br>
            <b>小于1000毫秒时，按照1000毫秒计算</b></dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>包含新的定位间隔的AMapLocationClientOption对象</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.0</dd>
</dl>
</li>
</ul>
<a name="isOnceLocation--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isOnceLocation</h4>
<pre>public&nbsp;boolean&nbsp;isOnceLocation()</pre>
<div class="block">是否单次单次定位<br>
 默认值：false</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>true:是<br>
         false:否</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.0</dd>
</dl>
</li>
</ul>
<a name="setOnceLocation-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setOnceLocation</h4>
<pre>public&nbsp;<a href="../../../../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a>&nbsp;setOnceLocation(boolean&nbsp;isOnceLocation)</pre>
<div class="block">设置是否单次定位<br>
 默认值：false</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>isOnceLocation</code> - 是否单次定位 <br>
            true:是 <br>
            false:否</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>包含是否单次定位标识的AMapLocationClientOption对象</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.0</dd>
</dl>
</li>
</ul>
<a name="isNeedAddress--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isNeedAddress</h4>
<pre>public&nbsp;boolean&nbsp;isNeedAddress()</pre>
<div class="block">获取是否需要地址信息 <br>
 默认值：true 返回地址信息<br>
 <b>2.9.0</b>之前的版本定位类型为<a href="../../../../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS"><code>AMapLocation.LOCATION_TYPE_GPS</code></a>时不会返回地址信息<br>
 自<b>2.9.0</b>版本开始，当类型为<a href="../../../../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS"><code>AMapLocation.LOCATION_TYPE_GPS</code></a>时也可以返回地址信息(需要网络通畅，第一次有可能没有地址信息返回）</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>true:需要<br>
 false:不需要</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.0</dd>
</dl>
</li>
</ul>
<a name="setNeedAddress-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setNeedAddress</h4>
<pre>public&nbsp;<a href="../../../../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a>&nbsp;setNeedAddress(boolean&nbsp;isNeedAddress)</pre>
<div class="block">设置是否返回地址信息，默认返回地址信息 <br>
 默认值：true, 返回地址信息
 <p>
 <b>2.9.0</b>之前的版本定位类型为<a href="../../../../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS"><code>AMapLocation.LOCATION_TYPE_GPS</code></a>时不会返回地址信息<br>
 自<b>2.9.0</b>版本开始，当类型为<a href="../../../../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS"><code>AMapLocation.LOCATION_TYPE_GPS</code></a>时也可以返回地址信息(需要网络通畅，第一次有可能没有地址信息返回）
 </p></div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>isNeedAddress</code> - 是否返回地址信息<br>
                      true:需要<br>
                      false:不需要</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>包含是否返回地址信息标识的AMapLocationClientOption对象</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.0</dd>
</dl>
</li>
</ul>
<a name="isWifiActiveScan--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isWifiActiveScan</h4>
<pre>public&nbsp;boolean&nbsp;isWifiActiveScan()</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;</div>
<div class="block">是否主动刷新WIFI <br>
 默认值：true 主动刷新<br>
 模式为<b>仅设备定位(Device_Sensors)</b>时无效</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>true:主动刷新<br>
         false:被动刷新</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.0</dd>
</dl>
</li>
</ul>
<a name="setWifiActiveScan-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setWifiActiveScan</h4>
<pre>public&nbsp;<a href="../../../../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a>&nbsp;setWifiActiveScan(boolean&nbsp;isWifiActiveScan)</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;</div>
<div class="block">设置是否主动刷新WIFI <br>
 默认值：true 主动刷新<br>
 模式为<b>仅设备模式(Device_Sensors)</b>时无效</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>isWifiActiveScan</code> - 是否主动刷新WIFI true:主动刷新<br>
            false:被动刷新</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.0</dd>
</dl>
</li>
</ul>
<a name="isWifiScan--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isWifiScan</h4>
<pre>public&nbsp;boolean&nbsp;isWifiScan()</pre>
<div class="block">获取是否允许主动调用WIFI刷新</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>是否允许WIFI刷新</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.0</dd>
</dl>
</li>
</ul>
<a name="setWifiScan-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setWifiScan</h4>
<pre>public&nbsp;<a href="../../../../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a>&nbsp;setWifiScan(boolean&nbsp;isWifiPassiveScan)</pre>
<div class="block">设置是否允许调用WIFI刷新
 <p>
 默认值为true，当设置为false时会停止主动调用WIFI刷新，将会极大程度影响定位精度，但可以有效的降低定位耗电<br>
 </p></div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>isWifiPassiveScan</code> - 是否允许wifi刷新，默认为：true</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.0</dd>
</dl>
</li>
</ul>
<a name="getLocationMode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLocationMode</h4>
<pre>public&nbsp;<a href="../../../../com/amap/api/location/AMapLocationClientOption.AMapLocationMode.html" title="com.amap.api.location中的枚举">AMapLocationClientOption.AMapLocationMode</a>&nbsp;getLocationMode()</pre>
<div class="block">获取定位模式 默认值：Hight_Accuracy 高精度模式<br></div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>定位模式</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.0</dd>
</dl>
</li>
</ul>
<a name="setLocationMode-com.amap.api.location.AMapLocationClientOption.AMapLocationMode-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLocationMode</h4>
<pre>public&nbsp;<a href="../../../../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a>&nbsp;setLocationMode(<a href="../../../../com/amap/api/location/AMapLocationClientOption.AMapLocationMode.html" title="com.amap.api.location中的枚举">AMapLocationClientOption.AMapLocationMode</a>&nbsp;locationMode)</pre>
<div class="block">设置定位模式</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>locationMode</code> - 定位模式<br>
            默认值：Hight_Accuracy 高精度模式<br>
            模式参见AMapLocationOption.AMapLocationMode</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>包含最新定位模式的LocationManagerOption对象</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.0</dd>
</dl>
</li>
</ul>
<a name="getLocationProtocol--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLocationProtocol</h4>
<pre>public&nbsp;<a href="../../../../com/amap/api/location/AMapLocationClientOption.AMapLocationProtocol.html" title="com.amap.api.location中的枚举">AMapLocationClientOption.AMapLocationProtocol</a>&nbsp;getLocationProtocol()</pre>
<div class="block">获取定位协议 默认值：HTTP http协议<br></div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>定位协议</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.9.0</dd>
</dl>
</li>
</ul>
<a name="setLocationProtocol-com.amap.api.location.AMapLocationClientOption.AMapLocationProtocol-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLocationProtocol</h4>
<pre>public static&nbsp;void&nbsp;setLocationProtocol(<a href="../../../../com/amap/api/location/AMapLocationClientOption.AMapLocationProtocol.html" title="com.amap.api.location中的枚举">AMapLocationClientOption.AMapLocationProtocol</a>&nbsp;amapLocationProtocol)</pre>
<div class="block">设置定位协议</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>amapLocationProtocol</code> - 协议类型<br>
                           默认值：HTTP http协议<br>
                           模式参见AMapLocationOption.AMapLocationProtocol</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.9.0</dd>
</dl>
</li>
</ul>
<a name="isKillProcess--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isKillProcess</h4>
<pre>public&nbsp;boolean&nbsp;isKillProcess()</pre>
<div class="block">获取退出时是否杀死进程<br>
 默认值:false, 不杀死 <br>
 <p>
 <font color="red"><b>注意：如果设置为true，并且配置的service不是remote的则会杀死当前页面进程，请慎重使用
 </b> </font>
 </p></div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>true:退出时杀死<br>
         false:退出时不杀死</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.0</dd>
</dl>
</li>
</ul>
<a name="setKillProcess-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setKillProcess</h4>
<pre>public&nbsp;<a href="../../../../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a>&nbsp;setKillProcess(boolean&nbsp;isKillProcess)</pre>
<div class="block">设置退出时是否杀死进程<br>
 默认值:false, 不杀死 <br>
 <p>
 <font color="red"><b>注意：如果设置为true，并且配置的service不是remote的则会杀死当前页面进程，请慎重使用
 </b> </font>
 </p></div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>isKillProcess</code> - 退出时是否杀死进程<br>
            true:退出时杀死<br>
            false:退出时不杀死</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>包含退出时是否杀死进程标识的AMapLocationClientOption对象</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.0</dd>
</dl>
</li>
</ul>
<a name="isGpsFirst--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isGpsFirst</h4>
<pre>public&nbsp;boolean&nbsp;isGpsFirst()</pre>
<div class="block">获取高精度模式下单次定位是否优先返回卫星定位信息<br>
 默认值：false <br>
 <p>
 <b>只有在单次定位高精度定位模式下有效<br>
 为true时，会等待卫星定位结果返回，最多等待30秒，若30秒后仍无卫星定位结果返回，返回网络定位结果</b></div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>是否优先返回卫星定位信息
         </p></dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.0</dd>
</dl>
</li>
</ul>
<a name="setGpsFirst-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setGpsFirst</h4>
<pre>public&nbsp;<a href="../../../../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a>&nbsp;setGpsFirst(boolean&nbsp;isGpsFirst)</pre>
<div class="block">设置首次定位是否等待卫星定位结果<br>
 默认值：false <br>
 <p><b>只有在单次定位高精度定位模式下有效</b><br>
 设置为true时，会等待卫星定位结果返回，最多等待30秒，若30秒后仍无卫星定位结果返回，返回网络定位结果<br>
 从<b>4.5.0</b>版本开始等待卫星定位结果返回的时间可以通过 <a href="../../../../com/amap/api/location/AMapLocationClientOption.html#setGpsFirstTimeout-long-"><code>AMapLocationClientOption.setGpsFirstTimeout(long)</code></a>进行设置
 </p></div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>isGpsFirst</code> - 是否优先返回卫星定位信息<br></dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>包含是否优先返回卫星定位信息标识的AMapLocationClientOption对象</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../com/amap/api/location/AMapLocationClientOption.html#setGpsFirstTimeout-long-"><code>AMapLocationClientOption.setGpsFirstTimeout(long)</code></a></dd>
</dl>
</li>
</ul>
<a name="setGpsFirstTimeout-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setGpsFirstTimeout</h4>
<pre>public&nbsp;<a href="../../../../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a>&nbsp;setGpsFirstTimeout(long&nbsp;timeout)</pre>
<div class="block">设置优先返回卫星定位信息时等待卫星定位结果的超时时间，单位：毫秒
 <p>
     只有在<a href="../../../../com/amap/api/location/AMapLocationClientOption.html#setGpsFirst-boolean-"><code>AMapLocationClientOption.setGpsFirst(boolean)</code></a>设置为true时才有效。
     最小间隔5s, 最大间隔30s
 </p></div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>timeout</code> - 优先返回卫星定位信息时等待卫星定位结果的超时时间， 范围[5000-30000]， 单位：毫秒</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>包含优先返回卫星定位信息时等待卫星定位结果的超时时间AMapLocationClientOption对象</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>4.5.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../com/amap/api/location/AMapLocationClientOption.html#setGpsFirst-boolean-"><code>AMapLocationClientOption.setGpsFirst(boolean)</code></a></dd>
</dl>
</li>
</ul>
<a name="getGpsFirstTimeout--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getGpsFirstTimeout</h4>
<pre>public&nbsp;long&nbsp;getGpsFirstTimeout()</pre>
<div class="block">获取优先返回定位信息时等待GPS结果的超时时间</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>优先返回定位信息时等待GPS结果的超时时间</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>4.5.0</dd>
</dl>
</li>
</ul>
<a name="isBeidouFirst--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isBeidouFirst</h4>
<pre>public&nbsp;boolean&nbsp;isBeidouFirst()</pre>
</li>
</ul>
<a name="setBeidouFirst-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBeidouFirst</h4>
<pre>public&nbsp;<a href="../../../../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a>&nbsp;setBeidouFirst(boolean&nbsp;isBeidouFirst)</pre>
</li>
</ul>
<a name="clone--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>clone</h4>
<pre>public&nbsp;<a href="../../../../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a>&nbsp;clone()</pre>
<div class="block">获取AMapLocationClientOption对象的拷贝</div>
<dl>
<dt><span class="overrideSpecifyLabel">覆盖:</span></dt>
<dd><code>clone</code>&nbsp;在类中&nbsp;<code>java.lang.Object</code></dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.0</dd>
</dl>
</li>
</ul>
<a name="getHttpTimeOut--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHttpTimeOut</h4>
<pre>public&nbsp;long&nbsp;getHttpTimeOut()</pre>
<div class="block">获取联网超时时间<br>
 单位：毫秒<br>
 默认值：30000毫秒 <br></div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>超时时间</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.0</dd>
</dl>
</li>
</ul>
<a name="setHttpTimeOut-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setHttpTimeOut</h4>
<pre>public&nbsp;<a href="../../../../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a>&nbsp;setHttpTimeOut(long&nbsp;httpTimeOut)</pre>
<div class="block">设置联网超时时间<br>
 单位：毫秒<br>
 默认值：30000毫秒 <br></div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>httpTimeOut</code> - 联网超时时间<br>
            单位：毫秒</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.0</dd>
</dl>
</li>
</ul>
<a name="setLocationCacheEnable-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLocationCacheEnable</h4>
<pre>public&nbsp;<a href="../../../../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a>&nbsp;setLocationCacheEnable(boolean&nbsp;isLocationCacheEnable)</pre>
<div class="block">设置是否使用缓存策略, 默认为true 使用缓存策略</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>isLocationCacheEnable</code> - 是否使用缓存策略</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.5.0</dd>
</dl>
</li>
</ul>
<a name="setOnceLocationLatest-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setOnceLocationLatest</h4>
<pre>public&nbsp;<a href="../../../../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a>&nbsp;setOnceLocationLatest(boolean&nbsp;isOnceLocationLatest)</pre>
<div class="block">设置定位是否等待WIFI列表刷新
 <p>定位精度会更高，但是定位速度会变慢1-3秒</p>
 <p>
     <b>从3.7.0版本开始，支持连续定位（连续定位时首次会等待刷新）</b>
     3.7.0之前的版本，仅适用于单次定位，当设置为true时，连续定位会自动变为单次定位,
           </p></div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>isOnceLocationLatest</code> - 是否等待WIFI列表刷新</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.6.0</dd>
</dl>
</li>
</ul>
<a name="setSensorEnable-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSensorEnable</h4>
<pre>public&nbsp;<a href="../../../../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a>&nbsp;setSensorEnable(boolean&nbsp;sensorEnable)</pre>
<div class="block">设置是否使用设备传感器
 <p>
     默认值：false 不使用设备传感器
 </p></div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>sensorEnable</code> - 是否开启设备传感器，当设置为true时，网络定位可以返回海拔、角度和速度。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.1.0</dd>
</dl>
</li>
</ul>
<a name="setGeoLanguage-com.amap.api.location.AMapLocationClientOption.GeoLanguage-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setGeoLanguage</h4>
<pre>public&nbsp;<a href="../../../../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a>&nbsp;setGeoLanguage(<a href="../../../../com/amap/api/location/AMapLocationClientOption.GeoLanguage.html" title="com.amap.api.location中的枚举">AMapLocationClientOption.GeoLanguage</a>&nbsp;geoLanguage)</pre>
<div class="block">设置逆地理信息的语言,目前之中中文和英文
 <p>
     默认值：<a href="../../../../com/amap/api/location/AMapLocationClientOption.GeoLanguage.html#DEFAULT"><code>AMapLocationClientOption.GeoLanguage.DEFAULT</code></a>
 </p></div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>geoLanguage</code> - </dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>包含逆地理语言信息的AMapLocationClientOption对象</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.7.0</dd>
</dl>
</li>
</ul>
<a name="getDeviceModeDistanceFilter--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDeviceModeDistanceFilter</h4>
<pre>public&nbsp;float&nbsp;getDeviceModeDistanceFilter()</pre>
<div class="block">获取仅设备模式/高精度模式的系统定位自动回调最少间隔距离值 <br>
 默认值：0米
 <p>
     只有当定位模式为<a href="../../../../com/amap/api/location/AMapLocationClientOption.AMapLocationMode.html#Device_Sensors"><code>AMapLocationClientOption.AMapLocationMode.Device_Sensors</code></a>（仅设备模式）或 <a href="../../../../com/amap/api/location/AMapLocationClientOption.AMapLocationMode.html#Hight_Accuracy"><code>AMapLocationClientOption.AMapLocationMode.Hight_Accuracy</code></a>（高精度模式）有效
 </p></div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.7.0</dd>
</dl>
</li>
</ul>
<a name="setDeviceModeDistanceFilter-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDeviceModeDistanceFilter</h4>
<pre>public&nbsp;<a href="../../../../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a>&nbsp;setDeviceModeDistanceFilter(float&nbsp;distanceFilter)</pre>
<div class="block">设置仅设备模式/高精度模式的系统定位自动回调最少间隔距离值 <br>
 单位：米 <br>
 默认值：0米
 <p>
     只有当定位模式为<a href="../../../../com/amap/api/location/AMapLocationClientOption.AMapLocationMode.html#Device_Sensors"><code>AMapLocationClientOption.AMapLocationMode.Device_Sensors</code></a>（仅设备模式）或 <a href="../../../../com/amap/api/location/AMapLocationClientOption.AMapLocationMode.html#Hight_Accuracy"><code>AMapLocationClientOption.AMapLocationMode.Hight_Accuracy</code></a>（高精度模式）有效，值小于0时无效
 </p></div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>distanceFilter</code> - </dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>包含新设置最少距离值的AMapLocationClientOption对象</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.7.0</dd>
</dl>
</li>
</ul>
<a name="setLocationPurpose-com.amap.api.location.AMapLocationClientOption.AMapLocationPurpose-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLocationPurpose</h4>
<pre>public&nbsp;<a href="../../../../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a>&nbsp;setLocationPurpose(<a href="../../../../com/amap/api/location/AMapLocationClientOption.AMapLocationPurpose.html" title="com.amap.api.location中的枚举">AMapLocationClientOption.AMapLocationPurpose</a>&nbsp;locationPurpose)</pre>
<div class="block">设置定位场景，根据场景快速修改option，不支持动态改变，修改后需要调用<a href="../../../../com/amap/api/location/AMapLocationClient.html#startLocation--"><code>AMapLocationClient.startLocation()</code></a>使其生效<br>
 <p>
     当不需要场景时，可以设置为NULL，
 </p>
 <b>注意：
 <li><font color="red">不建议设置场景和自定义option混合使用</font></li>
 <li>设置场景后，如果已经开始定位了，建议调用一次<a href="../../../../com/amap/api/location/AMapLocationClient.html#stopLocation--"><code>AMapLocationClient.stopLocation()</code></a>,然后主动调用一次<a href="../../../../com/amap/api/location/AMapLocationClient.html#startLocation--"><code>AMapLocationClient.startLocation()</code></a>以保证option正确生效</li>
 <li>当主动设置的option和场景中的option有冲突时，以后设置的为准，
 比如：签到场景中默认的为单次定位，当主动设置option为连续定位时，
 如果先设置的场景，后改变的option，这时如果不调用startLocation不会变为连续定位，如果调用了startLocation则会变为连续定位，
 如果先改变option，后设置场景为签到场景，则会变为单次定位</li></b></div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>locationPurpose</code> - 定位场景, 默认值 :NULL(无场景设置）</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>AMapLocationClientOption</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.7.0</dd>
</dl>
</li>
</ul>
<a name="getLocationPurpose--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLocationPurpose</h4>
<pre>public&nbsp;<a href="../../../../com/amap/api/location/AMapLocationClientOption.AMapLocationPurpose.html" title="com.amap.api.location中的枚举">AMapLocationClientOption.AMapLocationPurpose</a>&nbsp;getLocationPurpose()</pre>
<div class="block">获取定位场景</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>定位场景</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.7.0</dd>
</dl>
</li>
</ul>
<a name="toString--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toString</h4>
<pre>public&nbsp;java.lang.String&nbsp;toString()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">覆盖:</span></dt>
<dd><code>toString</code>&nbsp;在类中&nbsp;<code>java.lang.Object</code></dd>
</dl>
</li>
</ul>
<a name="isOpenAlwaysScanWifi--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isOpenAlwaysScanWifi</h4>
<pre>public static&nbsp;boolean&nbsp;isOpenAlwaysScanWifi()</pre>
<div class="block">是否开启始终wifi扫描
 <p>
     <li>只有设置了android.permission.WRITE_SECURE_SETTINGS权限后才会开启</li>
     <li>开启后，即使关闭wifi开关的情况下也会扫描wifi</li>
     <li>默认值为：true, 开启wifi始终扫描</li>
 </p></div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>是否开启始终wifi扫描</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>4.3.0</dd>
</dl>
</li>
</ul>
<a name="setOpenAlwaysScanWifi-boolean-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setOpenAlwaysScanWifi</h4>
<pre>public static&nbsp;void&nbsp;setOpenAlwaysScanWifi(boolean&nbsp;openAlwaysScanWifi)</pre>
<div class="block">设置是否开启wifi始终扫描
 <p>
     <li>只有设置了android.permission.WRITE_SECURE_SETTINGS权限后才会开启</li>
     <li>开启后，即使关闭wifi开关的情况下也会扫描wifi</li>
     <li>默认值为：true, 开启wifi始终扫描</li>
     <li>此方法为静态方法，设置一次后其他定位Client也会生效</li>
 </p></div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>openAlwaysScanWifi</code> - 是否开启</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>4.3.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/AMapLocationClientOption.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/amap/api/location/AMapLocationClient.html" title="com.amap.api.location中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../com/amap/api/location/AMapLocationClientOption.AMapLocationMode.html" title="com.amap.api.location中的枚举"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/amap/api/location/AMapLocationClientOption.html" target="_top">框架</a></li>
<li><a href="AMapLocationClientOption.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li><a href="#nested.class.summary">嵌套</a>&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
