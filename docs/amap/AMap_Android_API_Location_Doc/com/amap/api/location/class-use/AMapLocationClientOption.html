<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>类 com.amap.api.location.AMapLocationClientOption的使用</title>
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="\u7C7B com.amap.api.location.AMapLocationClientOption\u7684\u4F7F\u7528";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="../package-summary.html">程序包</a></li>
<li><a href="../../../../../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">类</a></li>
<li class="navBarCell1Rev">使用</li>
<li><a href="../../../../../overview-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个</li>
<li>下一个</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/location/class-use/AMapLocationClientOption.html" target="_top">框架</a></li>
<li><a href="AMapLocationClientOption.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h2 title="类的使用 com.amap.api.location.AMapLocationClientOption" class="title">类的使用<br>com.amap.api.location.AMapLocationClientOption</h2>
</div>
<div class="classUseContainer">
<ul class="blockList">
<li class="blockList">
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表程序包和解释">
<caption><span>使用<a href="../../../../../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a>的程序包</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">程序包</th>
<th class="colLast" scope="col">说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="#com.amap.api.location">com.amap.api.location</a></td>
<td class="colLast">&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<ul class="blockList">
<li class="blockList"><a name="com.amap.api.location">
<!--   -->
</a>
<h3><a href="../../../../../com/amap/api/location/package-summary.html">com.amap.api.location</a>中<a href="../../../../../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a>的使用</h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表方法和解释">
<caption><span>返回<a href="../../../../../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a>的<a href="../../../../../com/amap/api/location/package-summary.html">com.amap.api.location</a>中的方法</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a></code></td>
<td class="colLast"><span class="typeNameLabel">AMapLocationClientOption.</span><code><span class="memberNameLink"><a href="../../../../../com/amap/api/location/AMapLocationClientOption.html#clone--">clone</a></span>()</code>
<div class="block">获取AMapLocationClientOption对象的拷贝</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a></code></td>
<td class="colLast"><span class="typeNameLabel">AMapLocationClientOption.</span><code><span class="memberNameLink"><a href="../../../../../com/amap/api/location/AMapLocationClientOption.html#setBeidouFirst-boolean-">setBeidouFirst</a></span>(boolean&nbsp;isBeidouFirst)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a></code></td>
<td class="colLast"><span class="typeNameLabel">AMapLocationClientOption.</span><code><span class="memberNameLink"><a href="../../../../../com/amap/api/location/AMapLocationClientOption.html#setDeviceModeDistanceFilter-float-">setDeviceModeDistanceFilter</a></span>(float&nbsp;distanceFilter)</code>
<div class="block">设置仅设备模式/高精度模式的系统定位自动回调最少间隔距离值 <br>
 单位：米 <br>
 默认值：0米
 
     只有当定位模式为<a href="../../../../../com/amap/api/location/AMapLocationClientOption.AMapLocationMode.html#Device_Sensors"><code>AMapLocationClientOption.AMapLocationMode.Device_Sensors</code></a>（仅设备模式）或 <a href="../../../../../com/amap/api/location/AMapLocationClientOption.AMapLocationMode.html#Hight_Accuracy"><code>AMapLocationClientOption.AMapLocationMode.Hight_Accuracy</code></a>（高精度模式）有效，值小于0时无效
 </div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a></code></td>
<td class="colLast"><span class="typeNameLabel">AMapLocationClientOption.</span><code><span class="memberNameLink"><a href="../../../../../com/amap/api/location/AMapLocationClientOption.html#setGeoLanguage-com.amap.api.location.AMapLocationClientOption.GeoLanguage-">setGeoLanguage</a></span>(<a href="../../../../../com/amap/api/location/AMapLocationClientOption.GeoLanguage.html" title="com.amap.api.location中的枚举">AMapLocationClientOption.GeoLanguage</a>&nbsp;geoLanguage)</code>
<div class="block">设置逆地理信息的语言,目前之中中文和英文
 
     默认值：<a href="../../../../../com/amap/api/location/AMapLocationClientOption.GeoLanguage.html#DEFAULT"><code>AMapLocationClientOption.GeoLanguage.DEFAULT</code></a>
 </div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a></code></td>
<td class="colLast"><span class="typeNameLabel">AMapLocationClientOption.</span><code><span class="memberNameLink"><a href="../../../../../com/amap/api/location/AMapLocationClientOption.html#setGpsFirst-boolean-">setGpsFirst</a></span>(boolean&nbsp;isGpsFirst)</code>
<div class="block">设置首次定位是否等待卫星定位结果<br>
 默认值：false <br>
 <b>只有在单次定位高精度定位模式下有效</b><br>
 设置为true时，会等待卫星定位结果返回，最多等待30秒，若30秒后仍无卫星定位结果返回，返回网络定位结果<br>
 从<b>4.5.0</b>版本开始等待卫星定位结果返回的时间可以通过 <a href="../../../../../com/amap/api/location/AMapLocationClientOption.html#setGpsFirstTimeout-long-"><code>AMapLocationClientOption.setGpsFirstTimeout(long)</code></a>进行设置
 </div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a></code></td>
<td class="colLast"><span class="typeNameLabel">AMapLocationClientOption.</span><code><span class="memberNameLink"><a href="../../../../../com/amap/api/location/AMapLocationClientOption.html#setGpsFirstTimeout-long-">setGpsFirstTimeout</a></span>(long&nbsp;timeout)</code>
<div class="block">设置优先返回卫星定位信息时等待卫星定位结果的超时时间，单位：毫秒
 
     只有在<a href="../../../../../com/amap/api/location/AMapLocationClientOption.html#setGpsFirst-boolean-"><code>AMapLocationClientOption.setGpsFirst(boolean)</code></a>设置为true时才有效。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a></code></td>
<td class="colLast"><span class="typeNameLabel">AMapLocationClientOption.</span><code><span class="memberNameLink"><a href="../../../../../com/amap/api/location/AMapLocationClientOption.html#setHttpTimeOut-long-">setHttpTimeOut</a></span>(long&nbsp;httpTimeOut)</code>
<div class="block">设置联网超时时间<br>
 单位：毫秒<br>
 默认值：30000毫秒 <br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a></code></td>
<td class="colLast"><span class="typeNameLabel">AMapLocationClientOption.</span><code><span class="memberNameLink"><a href="../../../../../com/amap/api/location/AMapLocationClientOption.html#setInterval-long-">setInterval</a></span>(long&nbsp;interval)</code>
<div class="block">设置发起定位请求的时间间隔<br>
 单位：毫秒<br>
 默认值：2000毫秒</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a></code></td>
<td class="colLast"><span class="typeNameLabel">AMapLocationClientOption.</span><code><span class="memberNameLink"><a href="../../../../../com/amap/api/location/AMapLocationClientOption.html#setKillProcess-boolean-">setKillProcess</a></span>(boolean&nbsp;isKillProcess)</code>
<div class="block">设置退出时是否杀死进程<br>
 默认值:false, 不杀死 <br>
 
 <font color="red"><b>注意：如果设置为true，并且配置的service不是remote的则会杀死当前页面进程，请慎重使用
 </b> </font>
 </div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a></code></td>
<td class="colLast"><span class="typeNameLabel">AMapLocationClientOption.</span><code><span class="memberNameLink"><a href="../../../../../com/amap/api/location/AMapLocationClientOption.html#setLocationCacheEnable-boolean-">setLocationCacheEnable</a></span>(boolean&nbsp;isLocationCacheEnable)</code>
<div class="block">设置是否使用缓存策略, 默认为true 使用缓存策略</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a></code></td>
<td class="colLast"><span class="typeNameLabel">AMapLocationClientOption.</span><code><span class="memberNameLink"><a href="../../../../../com/amap/api/location/AMapLocationClientOption.html#setLocationMode-com.amap.api.location.AMapLocationClientOption.AMapLocationMode-">setLocationMode</a></span>(<a href="../../../../../com/amap/api/location/AMapLocationClientOption.AMapLocationMode.html" title="com.amap.api.location中的枚举">AMapLocationClientOption.AMapLocationMode</a>&nbsp;locationMode)</code>
<div class="block">设置定位模式</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a></code></td>
<td class="colLast"><span class="typeNameLabel">AMapLocationClientOption.</span><code><span class="memberNameLink"><a href="../../../../../com/amap/api/location/AMapLocationClientOption.html#setLocationPurpose-com.amap.api.location.AMapLocationClientOption.AMapLocationPurpose-">setLocationPurpose</a></span>(<a href="../../../../../com/amap/api/location/AMapLocationClientOption.AMapLocationPurpose.html" title="com.amap.api.location中的枚举">AMapLocationClientOption.AMapLocationPurpose</a>&nbsp;locationPurpose)</code>
<div class="block">设置定位场景，根据场景快速修改option，不支持动态改变，修改后需要调用<a href="../../../../../com/amap/api/location/AMapLocationClient.html#startLocation--"><code>AMapLocationClient.startLocation()</code></a>使其生效<br>
 
     当不需要场景时，可以设置为NULL，
 
 <b>注意：
 <font color="red">不建议设置场景和自定义option混合使用</font>
 设置场景后，如果已经开始定位了，建议调用一次<a href="../../../../../com/amap/api/location/AMapLocationClient.html#stopLocation--"><code>AMapLocationClient.stopLocation()</code></a>,然后主动调用一次<a href="../../../../../com/amap/api/location/AMapLocationClient.html#startLocation--"><code>AMapLocationClient.startLocation()</code></a>以保证option正确生效
 当主动设置的option和场景中的option有冲突时，以后设置的为准，
 比如：签到场景中默认的为单次定位，当主动设置option为连续定位时，
 如果先设置的场景，后改变的option，这时如果不调用startLocation不会变为连续定位，如果调用了startLocation则会变为连续定位，
 如果先改变option，后设置场景为签到场景，则会变为单次定位</b></div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a></code></td>
<td class="colLast"><span class="typeNameLabel">AMapLocationClientOption.</span><code><span class="memberNameLink"><a href="../../../../../com/amap/api/location/AMapLocationClientOption.html#setMockEnable-boolean-">setMockEnable</a></span>(boolean&nbsp;isMockEnable)</code>
<div class="block">设置是否允许模拟位置 <br>
 
     从<b>3.4.0</b>开始，默认值为true，允许模拟;<br>
     <b>3.4.0</b>之前的版本，默认值为false，不允许模拟
 </div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a></code></td>
<td class="colLast"><span class="typeNameLabel">AMapLocationClientOption.</span><code><span class="memberNameLink"><a href="../../../../../com/amap/api/location/AMapLocationClientOption.html#setNeedAddress-boolean-">setNeedAddress</a></span>(boolean&nbsp;isNeedAddress)</code>
<div class="block">设置是否返回地址信息，默认返回地址信息 <br>
 默认值：true, 返回地址信息
 
 <b>2.9.0</b>之前的版本定位类型为<a href="../../../../../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS"><code>AMapLocation.LOCATION_TYPE_GPS</code></a>时不会返回地址信息<br>
 自<b>2.9.0</b>版本开始，当类型为<a href="../../../../../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS"><code>AMapLocation.LOCATION_TYPE_GPS</code></a>时也可以返回地址信息(需要网络通畅，第一次有可能没有地址信息返回）
 </div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a></code></td>
<td class="colLast"><span class="typeNameLabel">AMapLocationClientOption.</span><code><span class="memberNameLink"><a href="../../../../../com/amap/api/location/AMapLocationClientOption.html#setOnceLocation-boolean-">setOnceLocation</a></span>(boolean&nbsp;isOnceLocation)</code>
<div class="block">设置是否单次定位<br>
 默认值：false</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a></code></td>
<td class="colLast"><span class="typeNameLabel">AMapLocationClientOption.</span><code><span class="memberNameLink"><a href="../../../../../com/amap/api/location/AMapLocationClientOption.html#setOnceLocationLatest-boolean-">setOnceLocationLatest</a></span>(boolean&nbsp;isOnceLocationLatest)</code>
<div class="block">设置定位是否等待WIFI列表刷新
 定位精度会更高，但是定位速度会变慢1-3秒
 
     <b>从3.7.0版本开始，支持连续定位（连续定位时首次会等待刷新）</b>
     3.7.0之前的版本，仅适用于单次定位，当设置为true时，连续定位会自动变为单次定位,
           </div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a></code></td>
<td class="colLast"><span class="typeNameLabel">AMapLocationClientOption.</span><code><span class="memberNameLink"><a href="../../../../../com/amap/api/location/AMapLocationClientOption.html#setSensorEnable-boolean-">setSensorEnable</a></span>(boolean&nbsp;sensorEnable)</code>
<div class="block">设置是否使用设备传感器
 
     默认值：false 不使用设备传感器
 </div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a></code></td>
<td class="colLast"><span class="typeNameLabel">AMapLocationClientOption.</span><code><span class="memberNameLink"><a href="../../../../../com/amap/api/location/AMapLocationClientOption.html#setWifiActiveScan-boolean-">setWifiActiveScan</a></span>(boolean&nbsp;isWifiActiveScan)</code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;&nbsp;</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a></code></td>
<td class="colLast"><span class="typeNameLabel">AMapLocationClientOption.</span><code><span class="memberNameLink"><a href="../../../../../com/amap/api/location/AMapLocationClientOption.html#setWifiScan-boolean-">setWifiScan</a></span>(boolean&nbsp;isWifiPassiveScan)</code>
<div class="block">设置是否允许调用WIFI刷新
 
 默认值为true，当设置为false时会停止主动调用WIFI刷新，将会极大程度影响定位精度，但可以有效的降低定位耗电<br>
 </div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表方法和解释">
<caption><span>参数类型为<a href="../../../../../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a>的<a href="../../../../../com/amap/api/location/package-summary.html">com.amap.api.location</a>中的方法</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">AMapLocationClient.</span><code><span class="memberNameLink"><a href="../../../../../com/amap/api/location/AMapLocationClient.html#setLocationOption-com.amap.api.location.AMapLocationClientOption-">setLocationOption</a></span>(<a href="../../../../../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a>&nbsp;option)</code>
<div class="block">设置定位参数</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="../package-summary.html">程序包</a></li>
<li><a href="../../../../../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">类</a></li>
<li class="navBarCell1Rev">使用</li>
<li><a href="../../../../../overview-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个</li>
<li>下一个</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/location/class-use/AMapLocationClientOption.html" target="_top">框架</a></li>
<li><a href="AMapLocationClientOption.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
