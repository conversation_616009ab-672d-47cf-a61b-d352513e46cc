<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>类 com.amap.api.location.AMapLocationClientOption.AMapLocationPurpose的使用</title>
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="\u7C7B com.amap.api.location.AMapLocationClientOption.AMapLocationPurpose\u7684\u4F7F\u7528";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="../package-summary.html">程序包</a></li>
<li><a href="../../../../../com/amap/api/location/AMapLocationClientOption.AMapLocationPurpose.html" title="com.amap.api.location中的枚举">类</a></li>
<li class="navBarCell1Rev">使用</li>
<li><a href="../../../../../overview-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个</li>
<li>下一个</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/location/class-use/AMapLocationClientOption.AMapLocationPurpose.html" target="_top">框架</a></li>
<li><a href="AMapLocationClientOption.AMapLocationPurpose.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h2 title="类的使用 com.amap.api.location.AMapLocationClientOption.AMapLocationPurpose" class="title">类的使用<br>com.amap.api.location.AMapLocationClientOption.AMapLocationPurpose</h2>
</div>
<div class="classUseContainer">
<ul class="blockList">
<li class="blockList">
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表程序包和解释">
<caption><span>使用<a href="../../../../../com/amap/api/location/AMapLocationClientOption.AMapLocationPurpose.html" title="com.amap.api.location中的枚举">AMapLocationClientOption.AMapLocationPurpose</a>的程序包</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">程序包</th>
<th class="colLast" scope="col">说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="#com.amap.api.location">com.amap.api.location</a></td>
<td class="colLast">&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<ul class="blockList">
<li class="blockList"><a name="com.amap.api.location">
<!--   -->
</a>
<h3><a href="../../../../../com/amap/api/location/package-summary.html">com.amap.api.location</a>中<a href="../../../../../com/amap/api/location/AMapLocationClientOption.AMapLocationPurpose.html" title="com.amap.api.location中的枚举">AMapLocationClientOption.AMapLocationPurpose</a>的使用</h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表方法和解释">
<caption><span>返回<a href="../../../../../com/amap/api/location/AMapLocationClientOption.AMapLocationPurpose.html" title="com.amap.api.location中的枚举">AMapLocationClientOption.AMapLocationPurpose</a>的<a href="../../../../../com/amap/api/location/package-summary.html">com.amap.api.location</a>中的方法</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/location/AMapLocationClientOption.AMapLocationPurpose.html" title="com.amap.api.location中的枚举">AMapLocationClientOption.AMapLocationPurpose</a></code></td>
<td class="colLast"><span class="typeNameLabel">AMapLocationClientOption.</span><code><span class="memberNameLink"><a href="../../../../../com/amap/api/location/AMapLocationClientOption.html#getLocationPurpose--">getLocationPurpose</a></span>()</code>
<div class="block">获取定位场景</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../../com/amap/api/location/AMapLocationClientOption.AMapLocationPurpose.html" title="com.amap.api.location中的枚举">AMapLocationClientOption.AMapLocationPurpose</a></code></td>
<td class="colLast"><span class="typeNameLabel">AMapLocationClientOption.AMapLocationPurpose.</span><code><span class="memberNameLink"><a href="../../../../../com/amap/api/location/AMapLocationClientOption.AMapLocationPurpose.html#valueOf-java.lang.String-">valueOf</a></span>(java.lang.String&nbsp;name)</code>
<div class="block">返回带有指定名称的该类型的枚举常量。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../../com/amap/api/location/AMapLocationClientOption.AMapLocationPurpose.html" title="com.amap.api.location中的枚举">AMapLocationClientOption.AMapLocationPurpose</a>[]</code></td>
<td class="colLast"><span class="typeNameLabel">AMapLocationClientOption.AMapLocationPurpose.</span><code><span class="memberNameLink"><a href="../../../../../com/amap/api/location/AMapLocationClientOption.AMapLocationPurpose.html#values--">values</a></span>()</code>
<div class="block">按照声明该枚举类型的常量的顺序, 返回
包含这些常量的数组。</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表方法和解释">
<caption><span>参数类型为<a href="../../../../../com/amap/api/location/AMapLocationClientOption.AMapLocationPurpose.html" title="com.amap.api.location中的枚举">AMapLocationClientOption.AMapLocationPurpose</a>的<a href="../../../../../com/amap/api/location/package-summary.html">com.amap.api.location</a>中的方法</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a></code></td>
<td class="colLast"><span class="typeNameLabel">AMapLocationClientOption.</span><code><span class="memberNameLink"><a href="../../../../../com/amap/api/location/AMapLocationClientOption.html#setLocationPurpose-com.amap.api.location.AMapLocationClientOption.AMapLocationPurpose-">setLocationPurpose</a></span>(<a href="../../../../../com/amap/api/location/AMapLocationClientOption.AMapLocationPurpose.html" title="com.amap.api.location中的枚举">AMapLocationClientOption.AMapLocationPurpose</a>&nbsp;locationPurpose)</code>
<div class="block">设置定位场景，根据场景快速修改option，不支持动态改变，修改后需要调用<a href="../../../../../com/amap/api/location/AMapLocationClient.html#startLocation--"><code>AMapLocationClient.startLocation()</code></a>使其生效<br>
 
     当不需要场景时，可以设置为NULL，
 
 <b>注意：
 <font color="red">不建议设置场景和自定义option混合使用</font>
 设置场景后，如果已经开始定位了，建议调用一次<a href="../../../../../com/amap/api/location/AMapLocationClient.html#stopLocation--"><code>AMapLocationClient.stopLocation()</code></a>,然后主动调用一次<a href="../../../../../com/amap/api/location/AMapLocationClient.html#startLocation--"><code>AMapLocationClient.startLocation()</code></a>以保证option正确生效
 当主动设置的option和场景中的option有冲突时，以后设置的为准，
 比如：签到场景中默认的为单次定位，当主动设置option为连续定位时，
 如果先设置的场景，后改变的option，这时如果不调用startLocation不会变为连续定位，如果调用了startLocation则会变为连续定位，
 如果先改变option，后设置场景为签到场景，则会变为单次定位</b></div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="../package-summary.html">程序包</a></li>
<li><a href="../../../../../com/amap/api/location/AMapLocationClientOption.AMapLocationPurpose.html" title="com.amap.api.location中的枚举">类</a></li>
<li class="navBarCell1Rev">使用</li>
<li><a href="../../../../../overview-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个</li>
<li>下一个</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/location/class-use/AMapLocationClientOption.AMapLocationPurpose.html" target="_top">框架</a></li>
<li><a href="AMapLocationClientOption.AMapLocationPurpose.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
