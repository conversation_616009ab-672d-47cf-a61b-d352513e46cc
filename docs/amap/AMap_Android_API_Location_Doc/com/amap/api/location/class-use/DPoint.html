<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>类 com.amap.api.location.DPoint的使用</title>
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="\u7C7B com.amap.api.location.DPoint\u7684\u4F7F\u7528";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="../package-summary.html">程序包</a></li>
<li><a href="../../../../../com/amap/api/location/DPoint.html" title="com.amap.api.location中的类">类</a></li>
<li class="navBarCell1Rev">使用</li>
<li><a href="../../../../../overview-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个</li>
<li>下一个</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/location/class-use/DPoint.html" target="_top">框架</a></li>
<li><a href="DPoint.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h2 title="类的使用 com.amap.api.location.DPoint" class="title">类的使用<br>com.amap.api.location.DPoint</h2>
</div>
<div class="classUseContainer">
<ul class="blockList">
<li class="blockList">
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表程序包和解释">
<caption><span>使用<a href="../../../../../com/amap/api/location/DPoint.html" title="com.amap.api.location中的类">DPoint</a>的程序包</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">程序包</th>
<th class="colLast" scope="col">说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="#com.amap.api.fence">com.amap.api.fence</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.amap.api.location">com.amap.api.location</a></td>
<td class="colLast">&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<ul class="blockList">
<li class="blockList"><a name="com.amap.api.fence">
<!--   -->
</a>
<h3><a href="../../../../../com/amap/api/fence/package-summary.html">com.amap.api.fence</a>中<a href="../../../../../com/amap/api/location/DPoint.html" title="com.amap.api.location中的类">DPoint</a>的使用</h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表方法和解释">
<caption><span>返回<a href="../../../../../com/amap/api/location/DPoint.html" title="com.amap.api.location中的类">DPoint</a>的<a href="../../../../../com/amap/api/fence/package-summary.html">com.amap.api.fence</a>中的方法</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/location/DPoint.html" title="com.amap.api.location中的类">DPoint</a></code></td>
<td class="colLast"><span class="typeNameLabel">GeoFence.</span><code><span class="memberNameLink"><a href="../../../../../com/amap/api/fence/GeoFence.html#getCenter--">getCenter</a></span>()</code>
<div class="block">获取围栏中心点坐标</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表方法和解释">
<caption><span>返回变量类型为<a href="../../../../../com/amap/api/location/DPoint.html" title="com.amap.api.location中的类">DPoint</a>的类型的<a href="../../../../../com/amap/api/fence/package-summary.html">com.amap.api.fence</a>中的方法</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>java.util.List&lt;java.util.List&lt;<a href="../../../../../com/amap/api/location/DPoint.html" title="com.amap.api.location中的类">DPoint</a>&gt;&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">GeoFence.</span><code><span class="memberNameLink"><a href="../../../../../com/amap/api/fence/GeoFence.html#getPointList--">getPointList</a></span>()</code>
<div class="block">获取围栏坐标点</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../../com/amap/api/location/DPoint.html" title="com.amap.api.location中的类">DPoint</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">DistrictItem.</span><code><span class="memberNameLink"><a href="../../../../../com/amap/api/fence/DistrictItem.html#getPolyline--">getPolyline</a></span>()</code>
<div class="block">获取行政区划轮廓坐标点</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表方法和解释">
<caption><span>参数类型为<a href="../../../../../com/amap/api/location/DPoint.html" title="com.amap.api.location中的类">DPoint</a>的<a href="../../../../../com/amap/api/fence/package-summary.html">com.amap.api.fence</a>中的方法</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">GeoFenceClient.</span><code><span class="memberNameLink"><a href="../../../../../com/amap/api/fence/GeoFenceClient.html#addGeoFence-com.amap.api.location.DPoint-float-java.lang.String-">addGeoFence</a></span>(<a href="../../../../../com/amap/api/location/DPoint.html" title="com.amap.api.location中的类">DPoint</a>&nbsp;point,
           float&nbsp;radius,
           java.lang.String&nbsp;customId)</code>
<div class="block">创建自定义围栏
 
 圆形围栏
 </div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">GeoFenceClient.</span><code><span class="memberNameLink"><a href="../../../../../com/amap/api/fence/GeoFenceClient.html#addGeoFence-java.lang.String-java.lang.String-com.amap.api.location.DPoint-float-int-java.lang.String-">addGeoFence</a></span>(java.lang.String&nbsp;keyword,
           java.lang.String&nbsp;poiType,
           <a href="../../../../../com/amap/api/location/DPoint.html" title="com.amap.api.location中的类">DPoint</a>&nbsp;point,
           float&nbsp;aroundRadius,
           int&nbsp;size,
           java.lang.String&nbsp;customId)</code>
<div class="block">创建高德POI地理围栏
 
 根据周边创建围栏
 <br>
     <font color="red"><b>注意：</b>通过此方法创建的围栏半径全部为200米，暂时不支持自定位围栏半径</font>
 </div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表方法和解释">
<caption><span>类型变量类型为<a href="../../../../../com/amap/api/location/DPoint.html" title="com.amap.api.location中的类">DPoint</a>的<a href="../../../../../com/amap/api/fence/package-summary.html">com.amap.api.fence</a>中的方法参数</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">GeoFenceClient.</span><code><span class="memberNameLink"><a href="../../../../../com/amap/api/fence/GeoFenceClient.html#addGeoFence-java.util.List-java.lang.String-">addGeoFence</a></span>(java.util.List&lt;<a href="../../../../../com/amap/api/location/DPoint.html" title="com.amap.api.location中的类">DPoint</a>&gt;&nbsp;points,
           java.lang.String&nbsp;customId)</code>
<div class="block">创建自定义围栏
 
 多边形围栏
 </div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.amap.api.location">
<!--   -->
</a>
<h3><a href="../../../../../com/amap/api/location/package-summary.html">com.amap.api.location</a>中<a href="../../../../../com/amap/api/location/DPoint.html" title="com.amap.api.location中的类">DPoint</a>的使用</h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表方法和解释">
<caption><span>返回<a href="../../../../../com/amap/api/location/DPoint.html" title="com.amap.api.location中的类">DPoint</a>的<a href="../../../../../com/amap/api/location/package-summary.html">com.amap.api.location</a>中的方法</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/location/DPoint.html" title="com.amap.api.location中的类">DPoint</a></code></td>
<td class="colLast"><span class="typeNameLabel">CoordinateConverter.</span><code><span class="memberNameLink"><a href="../../../../../com/amap/api/location/CoordinateConverter.html#convert--">convert</a></span>()</code>
<div class="block">进行坐标转换</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表方法和解释">
<caption><span>参数类型为<a href="../../../../../com/amap/api/location/DPoint.html" title="com.amap.api.location中的类">DPoint</a>的<a href="../../../../../com/amap/api/location/package-summary.html">com.amap.api.location</a>中的方法</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>static float</code></td>
<td class="colLast"><span class="typeNameLabel">CoordinateConverter.</span><code><span class="memberNameLink"><a href="../../../../../com/amap/api/location/CoordinateConverter.html#calculateLineDistance-com.amap.api.location.DPoint-com.amap.api.location.DPoint-">calculateLineDistance</a></span>(<a href="../../../../../com/amap/api/location/DPoint.html" title="com.amap.api.location中的类">DPoint</a>&nbsp;startLatlng,
                     <a href="../../../../../com/amap/api/location/DPoint.html" title="com.amap.api.location中的类">DPoint</a>&nbsp;endLatlng)</code>
<div class="block">计算两点间距离 单位：米</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/location/CoordinateConverter.html" title="com.amap.api.location中的类">CoordinateConverter</a></code></td>
<td class="colLast"><span class="typeNameLabel">CoordinateConverter.</span><code><span class="memberNameLink"><a href="../../../../../com/amap/api/location/CoordinateConverter.html#coord-com.amap.api.location.DPoint-">coord</a></span>(<a href="../../../../../com/amap/api/location/DPoint.html" title="com.amap.api.location中的类">DPoint</a>&nbsp;latLng)</code>
<div class="block">设置偏转数据源</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="../package-summary.html">程序包</a></li>
<li><a href="../../../../../com/amap/api/location/DPoint.html" title="com.amap.api.location中的类">类</a></li>
<li class="navBarCell1Rev">使用</li>
<li><a href="../../../../../overview-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个</li>
<li>下一个</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/location/class-use/DPoint.html" target="_top">框架</a></li>
<li><a href="DPoint.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
