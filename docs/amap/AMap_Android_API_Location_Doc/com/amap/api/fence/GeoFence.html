<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>GeoFence</title>
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="GeoFence";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10};
var tabs = {65535:["t0","所有方法"],2:["t2","实例方法"],8:["t4","具体方法"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/GeoFence.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/amap/api/fence/DistrictItem.html" title="com.amap.api.fence中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../com/amap/api/fence/GeoFenceClient.html" title="com.amap.api.fence中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/amap/api/fence/GeoFence.html" target="_top">框架</a></li>
<li><a href="GeoFence.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li><a href="#field.summary">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li><a href="#field.detail">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.amap.api.fence</div>
<h2 title="类 GeoFence" class="title">类 GeoFence</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.amap.api.fence.GeoFence</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">GeoFence</span>
extends java.lang.Object</pre>
<div class="block">地理围栏对象</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.0</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>字段概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="字段概要表, 列表字段和解释">
<caption><span>字段</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">字段和说明</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/fence/GeoFence.html#ADDGEOFENCE_SUCCESS">ADDGEOFENCE_SUCCESS</a></span></code>
<div class="block">创建地理围栏成功</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/fence/GeoFence.html#BUNDLE_KEY_CUSTOMID">BUNDLE_KEY_CUSTOMID</a></span></code>
<div class="block">地理围栏广播中Bundle的key-业务Id</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/fence/GeoFence.html#BUNDLE_KEY_FENCE">BUNDLE_KEY_FENCE</a></span></code>
<div class="block">地理围栏广播中Bundle的key-围栏对象</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/fence/GeoFence.html#BUNDLE_KEY_FENCEID">BUNDLE_KEY_FENCEID</a></span></code>
<div class="block">地理围栏广播中Bundle的key-围栏Id</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/fence/GeoFence.html#BUNDLE_KEY_FENCESTATUS">BUNDLE_KEY_FENCESTATUS</a></span></code>
<div class="block">地理围栏广播中Bundle的key-围栏状态</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/fence/GeoFence.html#BUNDLE_KEY_LOCERRORCODE">BUNDLE_KEY_LOCERRORCODE</a></span></code>
<div class="block">地理围栏广播中Bundle的key-定位错误码</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static &lt;any&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/fence/GeoFence.html#CREATOR">CREATOR</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/fence/GeoFence.html#ERROR_CODE_EXISTS">ERROR_CODE_EXISTS</a></span></code>
<div class="block">错误码： 相同的围栏已经存在，无需重复添加
 
 当地理围栏的customID，半径，周边点（多边形），中心点坐标（圆形）这几个属性完全一致时，则认为是相同围栏
 </div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/fence/GeoFence.html#ERROR_CODE_FAILURE_AUTH">ERROR_CODE_FAILURE_AUTH</a></span></code>
<div class="block">错误码：鉴权失败</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/fence/GeoFence.html#ERROR_CODE_FAILURE_CONNECTION">ERROR_CODE_FAILURE_CONNECTION</a></span></code>
<div class="block">错误码：网络连接异常</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/fence/GeoFence.html#ERROR_CODE_FAILURE_PARSER">ERROR_CODE_FAILURE_PARSER</a></span></code>
<div class="block">错误码：解析数据失败（有可能是连接的需要登录的网络但是没有登录）</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/fence/GeoFence.html#ERROR_CODE_INVALID_PARAMETER">ERROR_CODE_INVALID_PARAMETER</a></span></code>
<div class="block">错误码：参数错误</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/fence/GeoFence.html#ERROR_CODE_UNKNOWN">ERROR_CODE_UNKNOWN</a></span></code>
<div class="block">错误码：其他未知错误</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/fence/GeoFence.html#ERROR_NO_VALIDFENCE">ERROR_NO_VALIDFENCE</a></span></code>
<div class="block">错误码：无可用地理围栏</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/fence/GeoFence.html#STATUS_IN">STATUS_IN</a></span></code>
<div class="block">围栏状态-进入围栏</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/fence/GeoFence.html#STATUS_LOCFAIL">STATUS_LOCFAIL</a></span></code>
<div class="block">围栏状态-定位失败(定位失败时，围栏状态无法进行检测)</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/fence/GeoFence.html#STATUS_OUT">STATUS_OUT</a></span></code>
<div class="block">围栏状态-离开围栏</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/fence/GeoFence.html#STATUS_STAYED">STATUS_STAYED</a></span></code>
<div class="block">围栏状态-在围栏内停留</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/fence/GeoFence.html#STATUS_UNKNOWN">STATUS_UNKNOWN</a></span></code>
<div class="block">围栏状态-初始状态，表示从未触发过围栏</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/fence/GeoFence.html#TYPE_AMAPPOI">TYPE_AMAPPOI</a></span></code>
<div class="block">高德POI围栏</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/fence/GeoFence.html#TYPE_DISTRICT">TYPE_DISTRICT</a></span></code>
<div class="block">高德行政区划围栏</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/fence/GeoFence.html#TYPE_POLYGON">TYPE_POLYGON</a></span></code>
<div class="block">自建的多边形围栏</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/fence/GeoFence.html#TYPE_ROUND">TYPE_ROUND</a></span></code>
<div class="block">自建的圆形围栏</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/amap/api/fence/GeoFence.html#GeoFence--">GeoFence</a></span>()</code>
<div class="block">构造方法</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>所有方法</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">实例方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">具体方法</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/fence/GeoFence.html#describeContents--">describeContents</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/fence/GeoFence.html#getActivatesAction--">getActivatesAction</a></span>()</code>
<div class="block">获取设置的地理围栏触发条件
 </div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../../../com/amap/api/location/DPoint.html" title="com.amap.api.location中的类">DPoint</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/fence/GeoFence.html#getCenter--">getCenter</a></span>()</code>
<div class="block">获取围栏中心点坐标</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/amap/api/location/AMapLocation.html" title="com.amap.api.location中的类">AMapLocation</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/fence/GeoFence.html#getCurrentLocation--">getCurrentLocation</a></span>()</code>
<div class="block">获取当前位置，只有在围栏触发时才有值,其它情况值为null</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/fence/GeoFence.html#getCustomId--">getCustomId</a></span>()</code>
<div class="block">获取自定义的围栏ID(添加围栏时设置)</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../com/amap/api/fence/DistrictItem.html" title="com.amap.api.fence中的类">DistrictItem</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/fence/GeoFence.html#getDistrictItemList--">getDistrictItemList</a></span>()</code>
<div class="block">获取该围栏中的行政区划列表
 
 只有围栏类型为:<a href="../../../../com/amap/api/fence/GeoFence.html#TYPE_DISTRICT"><code>GeoFence.TYPE_DISTRICT</code></a>时才有值
 </div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/fence/GeoFence.html#getFenceId--">getFenceId</a></span>()</code>
<div class="block">获取地理围栏的ID</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>PendingIntent</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/fence/GeoFence.html#getPendingIntent--">getPendingIntent</a></span>()</code>
<div class="block">获取对应的PendingIntent对象</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/fence/GeoFence.html#getPendingIntentAction--">getPendingIntentAction</a></span>()</code>
<div class="block">获取对应的PendingIntent的action字符串</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/amap/api/fence/PoiItem.html" title="com.amap.api.fence中的类">PoiItem</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/fence/GeoFence.html#getPoiItem--">getPoiItem</a></span>()</code>
<div class="block">获取围栏内的兴趣点属性
 
 只有围栏类型为:<a href="../../../../com/amap/api/fence/GeoFence.html#TYPE_AMAPPOI"><code>GeoFence.TYPE_AMAPPOI</code></a>时才有值
 </div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>java.util.List&lt;java.util.List&lt;<a href="../../../../com/amap/api/location/DPoint.html" title="com.amap.api.location中的类">DPoint</a>&gt;&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/fence/GeoFence.html#getPointList--">getPointList</a></span>()</code>
<div class="block">获取围栏坐标点</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/fence/GeoFence.html#getStatus--">getStatus</a></span>()</code>
<div class="block">获取围栏的状态</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/fence/GeoFence.html#getType--">getType</a></span>()</code>
<div class="block">获取围栏类型</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/fence/GeoFence.html#isAble--">isAble</a></span>()</code>
<div class="block">获取围栏是否可用</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/fence/GeoFence.html#setAble-boolean-">setAble</a></span>(boolean&nbsp;able)</code>
<div class="block">设置围栏是否可用</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/fence/GeoFence.html#writeToParcel-Parcel-int-">writeToParcel</a></span>(Parcel&nbsp;dest,
             int&nbsp;flags)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>字段详细资料</h3>
<a name="BUNDLE_KEY_FENCEID">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BUNDLE_KEY_FENCEID</h4>
<pre>public static final&nbsp;java.lang.String BUNDLE_KEY_FENCEID</pre>
<div class="block">地理围栏广播中Bundle的key-围栏Id</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.fence.GeoFence.BUNDLE_KEY_FENCEID">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="BUNDLE_KEY_CUSTOMID">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BUNDLE_KEY_CUSTOMID</h4>
<pre>public static final&nbsp;java.lang.String BUNDLE_KEY_CUSTOMID</pre>
<div class="block">地理围栏广播中Bundle的key-业务Id</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.fence.GeoFence.BUNDLE_KEY_CUSTOMID">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="BUNDLE_KEY_FENCESTATUS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BUNDLE_KEY_FENCESTATUS</h4>
<pre>public static final&nbsp;java.lang.String BUNDLE_KEY_FENCESTATUS</pre>
<div class="block">地理围栏广播中Bundle的key-围栏状态</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.fence.GeoFence.BUNDLE_KEY_FENCESTATUS">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="BUNDLE_KEY_LOCERRORCODE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BUNDLE_KEY_LOCERRORCODE</h4>
<pre>public static final&nbsp;java.lang.String BUNDLE_KEY_LOCERRORCODE</pre>
<div class="block">地理围栏广播中Bundle的key-定位错误码</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.3.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.fence.GeoFence.BUNDLE_KEY_LOCERRORCODE">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="BUNDLE_KEY_FENCE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BUNDLE_KEY_FENCE</h4>
<pre>public static final&nbsp;java.lang.String BUNDLE_KEY_FENCE</pre>
<div class="block">地理围栏广播中Bundle的key-围栏对象</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.fence.GeoFence.BUNDLE_KEY_FENCE">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="ADDGEOFENCE_SUCCESS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ADDGEOFENCE_SUCCESS</h4>
<pre>public static final&nbsp;int ADDGEOFENCE_SUCCESS</pre>
<div class="block">创建地理围栏成功</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.fence.GeoFence.ADDGEOFENCE_SUCCESS">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="ERROR_CODE_INVALID_PARAMETER">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ERROR_CODE_INVALID_PARAMETER</h4>
<pre>public static final&nbsp;int ERROR_CODE_INVALID_PARAMETER</pre>
<div class="block">错误码：参数错误</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.fence.GeoFence.ERROR_CODE_INVALID_PARAMETER">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="ERROR_CODE_FAILURE_CONNECTION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ERROR_CODE_FAILURE_CONNECTION</h4>
<pre>public static final&nbsp;int ERROR_CODE_FAILURE_CONNECTION</pre>
<div class="block">错误码：网络连接异常</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.fence.GeoFence.ERROR_CODE_FAILURE_CONNECTION">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="ERROR_CODE_FAILURE_PARSER">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ERROR_CODE_FAILURE_PARSER</h4>
<pre>public static final&nbsp;int ERROR_CODE_FAILURE_PARSER</pre>
<div class="block">错误码：解析数据失败（有可能是连接的需要登录的网络但是没有登录）</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.4.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.fence.GeoFence.ERROR_CODE_FAILURE_PARSER">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="ERROR_CODE_FAILURE_AUTH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ERROR_CODE_FAILURE_AUTH</h4>
<pre>public static final&nbsp;int ERROR_CODE_FAILURE_AUTH</pre>
<div class="block">错误码：鉴权失败</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.fence.GeoFence.ERROR_CODE_FAILURE_AUTH">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="ERROR_CODE_UNKNOWN">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ERROR_CODE_UNKNOWN</h4>
<pre>public static final&nbsp;int ERROR_CODE_UNKNOWN</pre>
<div class="block">错误码：其他未知错误</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.fence.GeoFence.ERROR_CODE_UNKNOWN">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="ERROR_NO_VALIDFENCE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ERROR_NO_VALIDFENCE</h4>
<pre>public static final&nbsp;int ERROR_NO_VALIDFENCE</pre>
<div class="block">错误码：无可用地理围栏</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.fence.GeoFence.ERROR_NO_VALIDFENCE">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="ERROR_CODE_EXISTS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ERROR_CODE_EXISTS</h4>
<pre>public static final&nbsp;int ERROR_CODE_EXISTS</pre>
<div class="block">错误码： 相同的围栏已经存在，无需重复添加
 <p>
 当地理围栏的customID，半径，周边点（多边形），中心点坐标（圆形）这几个属性完全一致时，则认为是相同围栏
 </p></div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.3.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.fence.GeoFence.ERROR_CODE_EXISTS">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="STATUS_IN">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>STATUS_IN</h4>
<pre>public static final&nbsp;int STATUS_IN</pre>
<div class="block">围栏状态-进入围栏</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.fence.GeoFence.STATUS_IN">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="STATUS_OUT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>STATUS_OUT</h4>
<pre>public static final&nbsp;int STATUS_OUT</pre>
<div class="block">围栏状态-离开围栏</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.fence.GeoFence.STATUS_OUT">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="STATUS_STAYED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>STATUS_STAYED</h4>
<pre>public static final&nbsp;int STATUS_STAYED</pre>
<div class="block">围栏状态-在围栏内停留</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.fence.GeoFence.STATUS_STAYED">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="STATUS_LOCFAIL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>STATUS_LOCFAIL</h4>
<pre>public static final&nbsp;int STATUS_LOCFAIL</pre>
<div class="block">围栏状态-定位失败(定位失败时，围栏状态无法进行检测)</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.fence.GeoFence.STATUS_LOCFAIL">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="STATUS_UNKNOWN">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>STATUS_UNKNOWN</h4>
<pre>public static final&nbsp;int STATUS_UNKNOWN</pre>
<div class="block">围栏状态-初始状态，表示从未触发过围栏</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.fence.GeoFence.STATUS_UNKNOWN">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="TYPE_ROUND">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TYPE_ROUND</h4>
<pre>public static final&nbsp;int TYPE_ROUND</pre>
<div class="block">自建的圆形围栏</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.fence.GeoFence.TYPE_ROUND">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="TYPE_POLYGON">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TYPE_POLYGON</h4>
<pre>public static final&nbsp;int TYPE_POLYGON</pre>
<div class="block">自建的多边形围栏</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.fence.GeoFence.TYPE_POLYGON">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="TYPE_AMAPPOI">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TYPE_AMAPPOI</h4>
<pre>public static final&nbsp;int TYPE_AMAPPOI</pre>
<div class="block">高德POI围栏</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.fence.GeoFence.TYPE_AMAPPOI">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="TYPE_DISTRICT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TYPE_DISTRICT</h4>
<pre>public static final&nbsp;int TYPE_DISTRICT</pre>
<div class="block">高德行政区划围栏</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.fence.GeoFence.TYPE_DISTRICT">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="CREATOR">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>CREATOR</h4>
<pre>public static final&nbsp;&lt;any&gt; CREATOR</pre>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="GeoFence--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>GeoFence</h4>
<pre>public&nbsp;GeoFence()</pre>
<div class="block">构造方法</div>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="getFenceId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFenceId</h4>
<pre>public&nbsp;java.lang.String&nbsp;getFenceId()</pre>
<div class="block">获取地理围栏的ID</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>围栏的ID(自动生成)</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.0</dd>
</dl>
</li>
</ul>
<a name="getCustomId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCustomId</h4>
<pre>public&nbsp;java.lang.String&nbsp;getCustomId()</pre>
<div class="block">获取自定义的围栏ID(添加围栏时设置)</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>自定义的围栏ID</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.0</dd>
</dl>
</li>
</ul>
<a name="getPendingIntentAction--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPendingIntentAction</h4>
<pre>public&nbsp;java.lang.String&nbsp;getPendingIntentAction()</pre>
<div class="block">获取对应的PendingIntent的action字符串</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>对应的PendingIntent的action字符串</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.0</dd>
</dl>
</li>
</ul>
<a name="getPendingIntent--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPendingIntent</h4>
<pre>public&nbsp;PendingIntent&nbsp;getPendingIntent()</pre>
<div class="block">获取对应的PendingIntent对象</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>PendingIntent对象</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.0</dd>
</dl>
</li>
</ul>
<a name="getType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getType</h4>
<pre>public&nbsp;int&nbsp;getType()</pre>
<div class="block">获取围栏类型</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>围栏类型
         <p>
         目前支持的类型:
         <dl>
         <dt><a href="../../../../com/amap/api/fence/GeoFence.html#TYPE_ROUND"><code>GeoFence.TYPE_ROUND</code></a>(自建的圆形围栏)</dt>
         <dt><a href="../../../../com/amap/api/fence/GeoFence.html#TYPE_POLYGON"><code>GeoFence.TYPE_POLYGON</code></a>(自建的圆形围栏)</dt>
         <dt><a href="../../../../com/amap/api/fence/GeoFence.html#TYPE_AMAPPOI"><code>GeoFence.TYPE_AMAPPOI</code></a>(高德POI围栏)</dt>
         <dt><a href="../../../../com/amap/api/fence/GeoFence.html#TYPE_DISTRICT"><code>GeoFence.TYPE_DISTRICT</code></a>(高德政区划围栏)</dt>
         </dl>
         </p></dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.0</dd>
</dl>
</li>
</ul>
<a name="getPoiItem--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPoiItem</h4>
<pre>public&nbsp;<a href="../../../../com/amap/api/fence/PoiItem.html" title="com.amap.api.fence中的类">PoiItem</a>&nbsp;getPoiItem()</pre>
<div class="block">获取围栏内的兴趣点属性
 <p>
 只有围栏类型为:<a href="../../../../com/amap/api/fence/GeoFence.html#TYPE_AMAPPOI"><code>GeoFence.TYPE_AMAPPOI</code></a>时才有值
 </p></div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>围栏内的兴趣点属性（如果有）</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.0</dd>
</dl>
</li>
</ul>
<a name="getDistrictItemList--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDistrictItemList</h4>
<pre>public&nbsp;java.util.List&lt;<a href="../../../../com/amap/api/fence/DistrictItem.html" title="com.amap.api.fence中的类">DistrictItem</a>&gt;&nbsp;getDistrictItemList()</pre>
<div class="block">获取该围栏中的行政区划列表
 <p>
 只有围栏类型为:<a href="../../../../com/amap/api/fence/GeoFence.html#TYPE_DISTRICT"><code>GeoFence.TYPE_DISTRICT</code></a>时才有值
 </p></div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>围栏中的行政区划列表（如果有）</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.0</dd>
</dl>
</li>
</ul>
<a name="getPointList--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPointList</h4>
<pre>public&nbsp;java.util.List&lt;java.util.List&lt;<a href="../../../../com/amap/api/location/DPoint.html" title="com.amap.api.location中的类">DPoint</a>&gt;&gt;&nbsp;getPointList()</pre>
<div class="block">获取围栏坐标点</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>围栏坐标点
         <p>
         <dl>
         <dt>当围栏类型为<a href="../../../../com/amap/api/fence/GeoFence.html#TYPE_ROUND"><code>GeoFence.TYPE_ROUND</code></a>或者
         <a href="../../../../com/amap/api/fence/GeoFence.html#TYPE_AMAPPOI"><code>GeoFence.TYPE_AMAPPOI</code></a>时, 坐标点只有一个，即围栏的中心点</dt>
         <dt>当围栏类型为<a href="../../../../com/amap/api/fence/GeoFence.html#TYPE_POLYGON"><code>GeoFence.TYPE_POLYGON</code></a>或者
         <a href="../../../../com/amap/api/fence/GeoFence.html#TYPE_DISTRICT"><code>GeoFence.TYPE_DISTRICT</code></a>时，坐标点是多个，即围栏的边线点</dt>
         </dl>
         </p></dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.0</dd>
</dl>
</li>
</ul>
<a name="getActivatesAction--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getActivatesAction</h4>
<pre>public&nbsp;int&nbsp;getActivatesAction()</pre>
<div class="block">获取设置的地理围栏触发条件
 <p></div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>触发地理围栏的条件</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>GeoFenceClient#setActivateAction(int)
      GeoFenceClientsetActivatesAction}
      </p></code></dd>
</dl>
</li>
</ul>
<a name="getStatus--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStatus</h4>
<pre>public&nbsp;int&nbsp;getStatus()</pre>
<div class="block">获取围栏的状态</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>围栏的状态
         <p>
         目前的状态有：
         <dl>
         <dt><a href="../../../../com/amap/api/fence/GeoFence.html#STATUS_IN"><code>GeoFence.STATUS_IN</code></a> 进入围栏</dt>
         <dt><a href="../../../../com/amap/api/fence/GeoFence.html#STATUS_OUT"><code>GeoFence.STATUS_OUT</code></a> 离开围栏</dt>
         <dt><a href="../../../../com/amap/api/fence/GeoFence.html#STATUS_STAYED"><code>GeoFence.STATUS_STAYED</code></a> 停留在围栏内</dt>
         </dl>
         </p></dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.0</dd>
</dl>
</li>
</ul>
<a name="getCenter--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCenter</h4>
<pre>public&nbsp;<a href="../../../../com/amap/api/location/DPoint.html" title="com.amap.api.location中的类">DPoint</a>&nbsp;getCenter()</pre>
<div class="block">获取围栏中心点坐标</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.0</dd>
</dl>
</li>
</ul>
<a name="isAble--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isAble</h4>
<pre>public&nbsp;boolean&nbsp;isAble()</pre>
<div class="block">获取围栏是否可用</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>围栏是否可用, 默认为true，可用</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.6.0</dd>
</dl>
</li>
</ul>
<a name="setAble-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAble</h4>
<pre>public&nbsp;void&nbsp;setAble(boolean&nbsp;able)</pre>
<div class="block">设置围栏是否可用</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>able</code> - true:可用；false:不可用</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.6.0</dd>
</dl>
</li>
</ul>
<a name="getCurrentLocation--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCurrentLocation</h4>
<pre>public&nbsp;<a href="../../../../com/amap/api/location/AMapLocation.html" title="com.amap.api.location中的类">AMapLocation</a>&nbsp;getCurrentLocation()</pre>
<div class="block">获取当前位置，只有在围栏触发时才有值,其它情况值为null</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.7.0</dd>
</dl>
</li>
</ul>
<a name="describeContents--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>describeContents</h4>
<pre>public&nbsp;int&nbsp;describeContents()</pre>
</li>
</ul>
<a name="writeToParcel-Parcel-int-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>writeToParcel</h4>
<pre>public&nbsp;void&nbsp;writeToParcel(Parcel&nbsp;dest,
                          int&nbsp;flags)</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/GeoFence.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/amap/api/fence/DistrictItem.html" title="com.amap.api.fence中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../com/amap/api/fence/GeoFenceClient.html" title="com.amap.api.fence中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/amap/api/fence/GeoFence.html" target="_top">框架</a></li>
<li><a href="GeoFence.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li><a href="#field.summary">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li><a href="#field.detail">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
