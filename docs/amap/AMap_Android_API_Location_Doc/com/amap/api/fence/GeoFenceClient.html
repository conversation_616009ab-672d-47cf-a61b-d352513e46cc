<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>GeoFenceClient</title>
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="GeoFenceClient";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10};
var tabs = {65535:["t0","所有方法"],2:["t2","实例方法"],8:["t4","具体方法"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/GeoFenceClient.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/amap/api/fence/GeoFence.html" title="com.amap.api.fence中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../com/amap/api/fence/GeoFenceListener.html" title="com.amap.api.fence中的接口"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/amap/api/fence/GeoFenceClient.html" target="_top">框架</a></li>
<li><a href="GeoFenceClient.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li><a href="#field.summary">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li><a href="#field.detail">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.amap.api.fence</div>
<h2 title="类 GeoFenceClient" class="title">类 GeoFenceClient</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.amap.api.fence.GeoFenceClient</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">GeoFenceClient</span>
extends java.lang.Object</pre>
<div class="block">地理围栏客户端</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.0</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>字段概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="字段概要表, 列表字段和解释">
<caption><span>字段</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">字段和说明</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/fence/GeoFenceClient.html#GEOFENCE_IN">GEOFENCE_IN</a></span></code>
<div class="block">触发围栏行为-进入围栏</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/fence/GeoFenceClient.html#GEOFENCE_OUT">GEOFENCE_OUT</a></span></code>
<div class="block">触发围栏行为-离开围栏</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/fence/GeoFenceClient.html#GEOFENCE_STAYED">GEOFENCE_STAYED</a></span></code>
<div class="block">触发围栏行为-停留在围栏内（在围栏内停留10分钟以上）</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/amap/api/fence/GeoFenceClient.html#GeoFenceClient-Context-">GeoFenceClient</a></span>(Context&nbsp;context)</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>所有方法</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">实例方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">具体方法</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/fence/GeoFenceClient.html#addGeoFence-com.amap.api.location.DPoint-float-java.lang.String-">addGeoFence</a></span>(<a href="../../../../com/amap/api/location/DPoint.html" title="com.amap.api.location中的类">DPoint</a>&nbsp;point,
           float&nbsp;radius,
           java.lang.String&nbsp;customId)</code>
<div class="block">创建自定义围栏
 
 圆形围栏
 </div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/fence/GeoFenceClient.html#addGeoFence-java.util.List-java.lang.String-">addGeoFence</a></span>(java.util.List&lt;<a href="../../../../com/amap/api/location/DPoint.html" title="com.amap.api.location中的类">DPoint</a>&gt;&nbsp;points,
           java.lang.String&nbsp;customId)</code>
<div class="block">创建自定义围栏
 
 多边形围栏
 </div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/fence/GeoFenceClient.html#addGeoFence-java.lang.String-java.lang.String-">addGeoFence</a></span>(java.lang.String&nbsp;keyword,
           java.lang.String&nbsp;customId)</code>
<div class="block">创建行政区划围栏</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/fence/GeoFenceClient.html#addGeoFence-java.lang.String-java.lang.String-com.amap.api.location.DPoint-float-int-java.lang.String-">addGeoFence</a></span>(java.lang.String&nbsp;keyword,
           java.lang.String&nbsp;poiType,
           <a href="../../../../com/amap/api/location/DPoint.html" title="com.amap.api.location中的类">DPoint</a>&nbsp;point,
           float&nbsp;aroundRadius,
           int&nbsp;size,
           java.lang.String&nbsp;customId)</code>
<div class="block">创建高德POI地理围栏
 
 根据周边创建围栏
 <br>
     <font color="red"><b>注意：</b>通过此方法创建的围栏半径全部为200米，暂时不支持自定位围栏半径</font>
 </div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/fence/GeoFenceClient.html#addGeoFence-java.lang.String-java.lang.String-java.lang.String-int-java.lang.String-">addGeoFence</a></span>(java.lang.String&nbsp;keyword,
           java.lang.String&nbsp;poiType,
           java.lang.String&nbsp;city,
           int&nbsp;size,
           java.lang.String&nbsp;customId)</code>
<div class="block">创建高德POI地理围栏
 
 根据关键字创建围栏<br>
     <font color="red"><b>注意：</b>通过此方法创建的围栏半径全部为1000米，暂时不支持自定义围栏半径</font>
 </div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>PendingIntent</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/fence/GeoFenceClient.html#createPendingIntent-java.lang.String-">createPendingIntent</a></span>(java.lang.String&nbsp;action)</code>
<div class="block">创建PendingIntent
 
 <b>注意：一个GeoFenceClient只接受一个PendingIntent,如果重复设置多个，则以最后一个为准</b>
 </div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../com/amap/api/fence/GeoFence.html" title="com.amap.api.fence中的类">GeoFence</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/fence/GeoFenceClient.html#getAllGeoFence--">getAllGeoFence</a></span>()</code>
<div class="block">获取所有已经创建的地理围栏</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/fence/GeoFenceClient.html#isPause--">isPause</a></span>()</code>
<div class="block">地理围栏是否已经暂停</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/fence/GeoFenceClient.html#pauseGeoFence--">pauseGeoFence</a></span>()</code>
<div class="block">暂停地理围栏
 
     暂停地理围栏的监听、同时地理围栏内部的定位也会停止
 </div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/fence/GeoFenceClient.html#removeGeoFence--">removeGeoFence</a></span>()</code>
<div class="block">移除所有地理围栏</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/fence/GeoFenceClient.html#removeGeoFence-com.amap.api.fence.GeoFence-">removeGeoFence</a></span>(<a href="../../../../com/amap/api/fence/GeoFence.html" title="com.amap.api.fence中的类">GeoFence</a>&nbsp;geoFence)</code>
<div class="block">移除指定的geofence
 
 当要移除所有围栏时可以使用<a href="../../../../com/amap/api/fence/GeoFenceClient.html#removeGeoFence--"><code>GeoFenceClient.removeGeoFence()</code></a>方法 当要销毁GeoFneceClient时，需要调用一次
 <a href="../../../../com/amap/api/fence/GeoFenceClient.html#removeGeoFence--"><code>GeoFenceClient.removeGeoFence()</code></a>方法，确保所有围栏已移除
 </div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/fence/GeoFenceClient.html#resumeGeoFence--">resumeGeoFence</a></span>()</code>
<div class="block">恢复地理围栏
 
     恢复地理围栏的监听、同时地理围栏内部的定位也会重新开始定位<br>
     <font color="red"><b>注意：</b>通过setGeoFenceAble接口设置为false的围栏不会重新生效</font>
 </div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/fence/GeoFenceClient.html#setActivateAction-int-">setActivateAction</a></span>(int&nbsp;action)</code>
<div class="block">设置触发地理围栏的条件
 
 <b>注意：重复设置不同的触发行为会重复触发围栏</b>
 </div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/fence/GeoFenceClient.html#setGeoFenceAble-java.lang.String-boolean-">setGeoFenceAble</a></span>(java.lang.String&nbsp;fenceId,
               boolean&nbsp;able)</code>
<div class="block">设置围栏是否生效，</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/fence/GeoFenceClient.html#setGeoFenceListener-com.amap.api.fence.GeoFenceListener-">setGeoFenceListener</a></span>(<a href="../../../../com/amap/api/fence/GeoFenceListener.html" title="com.amap.api.fence中的接口">GeoFenceListener</a>&nbsp;geoFenceListener)</code>
<div class="block">设置地理围栏的回调接口</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>字段详细资料</h3>
<a name="GEOFENCE_IN">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>GEOFENCE_IN</h4>
<pre>public static final&nbsp;int GEOFENCE_IN</pre>
<div class="block">触发围栏行为-进入围栏</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.fence.GeoFenceClient.GEOFENCE_IN">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="GEOFENCE_OUT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>GEOFENCE_OUT</h4>
<pre>public static final&nbsp;int GEOFENCE_OUT</pre>
<div class="block">触发围栏行为-离开围栏</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.fence.GeoFenceClient.GEOFENCE_OUT">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="GEOFENCE_STAYED">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>GEOFENCE_STAYED</h4>
<pre>public static final&nbsp;int GEOFENCE_STAYED</pre>
<div class="block">触发围栏行为-停留在围栏内（在围栏内停留10分钟以上）</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.fence.GeoFenceClient.GEOFENCE_STAYED">常量字段值</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="GeoFenceClient-Context-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>GeoFenceClient</h4>
<pre>public&nbsp;GeoFenceClient(Context&nbsp;context)</pre>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>context</code> - </dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="createPendingIntent-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createPendingIntent</h4>
<pre>public&nbsp;PendingIntent&nbsp;createPendingIntent(java.lang.String&nbsp;action)</pre>
<div class="block">创建PendingIntent
 <p>
 <b>注意：一个GeoFenceClient只接受一个PendingIntent,如果重复设置多个，则以最后一个为准</b>
 </p></div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>action</code> - PendingIntent的过滤条件</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>创建后的PendingIntent</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.0</dd>
</dl>
</li>
</ul>
<a name="setActivateAction-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActivateAction</h4>
<pre>public&nbsp;void&nbsp;setActivateAction(int&nbsp;action)</pre>
<div class="block">设置触发地理围栏的条件
 <p>
 <b>注意：重复设置不同的触发行为会重复触发围栏</b>
 </p></div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>action</code> - 触发围栏的条件
            <p>
            目前支持的条件有：
            <dl>
            <dt><a href="../../../../com/amap/api/fence/GeoFenceClient.html#GEOFENCE_IN"><code>GeoFenceClient.GEOFENCE_IN</code></a> 进入</dt>
            <dt><a href="../../../../com/amap/api/fence/GeoFenceClient.html#GEOFENCE_OUT"><code>GeoFenceClient.GEOFENCE_OUT</code></a> 离开</dt>
            <dt><a href="../../../../com/amap/api/fence/GeoFenceClient.html#GEOFENCE_STAYED"><code>GeoFenceClient.GEOFENCE_STAYED</code></a> 停留</dt>
            </dl>
            三种条件可以通过|进行组合使用，例如： setActivatesAction(GEOFENCE_IN)为进入提醒<br>
            setActivatesAction(GEOFENCE_IN|GEOFENCE_OUT)为进入和离开都提醒<br>
            setActivatesAction(GEOFENCE_IN|GEOFENCE_OUT|GEOFENCE_STAYED)
            为全部提醒（进入、离开、停留)<br>
            </p></dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.0</dd>
</dl>
</li>
</ul>
<a name="setGeoFenceListener-com.amap.api.fence.GeoFenceListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setGeoFenceListener</h4>
<pre>public&nbsp;void&nbsp;setGeoFenceListener(<a href="../../../../com/amap/api/fence/GeoFenceListener.html" title="com.amap.api.fence中的接口">GeoFenceListener</a>&nbsp;geoFenceListener)</pre>
<div class="block">设置地理围栏的回调接口</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>geoFenceListener</code> - </dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.0</dd>
</dl>
</li>
</ul>
<a name="addGeoFence-com.amap.api.location.DPoint-float-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addGeoFence</h4>
<pre>public&nbsp;void&nbsp;addGeoFence(<a href="../../../../com/amap/api/location/DPoint.html" title="com.amap.api.location中的类">DPoint</a>&nbsp;point,
                        float&nbsp;radius,
                        java.lang.String&nbsp;customId)</pre>
<div class="block">创建自定义围栏
 <p>
 圆形围栏
 </p></div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>point</code> - 围栏中心点坐标, 必填项</dd>
<dd><code>radius</code> - 围栏半径, 必填项</dd>
<dd><code>customId</code> - 自定义的围栏标识， 选填，<b>建议必填并且保证唯一性</b></dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.0</dd>
</dl>
</li>
</ul>
<a name="addGeoFence-java.util.List-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addGeoFence</h4>
<pre>public&nbsp;void&nbsp;addGeoFence(java.util.List&lt;<a href="../../../../com/amap/api/location/DPoint.html" title="com.amap.api.location中的类">DPoint</a>&gt;&nbsp;points,
                        java.lang.String&nbsp;customId)</pre>
<div class="block">创建自定义围栏
 <p>
 多边形围栏
 </p></div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>points</code> - 多边形的边界点, 必须大于2个点, 必填项</dd>
<dd><code>customId</code> - 自定义的围栏标识， 选填，<b>建议必填并且保证唯一性</b></dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.0</dd>
</dl>
</li>
</ul>
<a name="addGeoFence-java.lang.String-java.lang.String-com.amap.api.location.DPoint-float-int-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addGeoFence</h4>
<pre>public&nbsp;void&nbsp;addGeoFence(java.lang.String&nbsp;keyword,
                        java.lang.String&nbsp;poiType,
                        <a href="../../../../com/amap/api/location/DPoint.html" title="com.amap.api.location中的类">DPoint</a>&nbsp;point,
                        float&nbsp;aroundRadius,
                        int&nbsp;size,
                        java.lang.String&nbsp;customId)</pre>
<div class="block">创建高德POI地理围栏
 <p>
 根据周边创建围栏
 <br>
     <font color="red"><b>注意：</b>通过此方法创建的围栏半径全部为200米，暂时不支持自定位围栏半径</font>
 </p></div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>keyword</code> - 关键字， 例如：肯德基， 必填项</dd>
<dd><code>poiType</code> - poi类型，例如：餐饮， 选填项</dd>
<dd><code>point</code> - 中心点坐标， 必填项</dd>
<dd><code>aroundRadius</code> - 周边半径,取值范围[1-50000]</dd>
<dd><code>size</code> - 最多创建的围栏数目, 最大25个</dd>
<dd><code>customId</code> - 自定义的围栏标识， 选填，<b>建议必填并且保证唯一性</b></dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="http://lbs.amap.com/api/android-location-sdk/download/" target="_blank">POI分类编码表和城市编码表</a></dd>
</dl>
</li>
</ul>
<a name="addGeoFence-java.lang.String-java.lang.String-java.lang.String-int-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addGeoFence</h4>
<pre>public&nbsp;void&nbsp;addGeoFence(java.lang.String&nbsp;keyword,
                        java.lang.String&nbsp;poiType,
                        java.lang.String&nbsp;city,
                        int&nbsp;size,
                        java.lang.String&nbsp;customId)</pre>
<div class="block">创建高德POI地理围栏
 <p>
 根据关键字创建围栏<br>
     <font color="red"><b>注意：</b>通过此方法创建的围栏半径全部为1000米，暂时不支持自定义围栏半径</font>
 </p></div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>keyword</code> - POI关键字， 例如：首开广场, 必填项（keyword和poiType必须至少填一项）</dd>
<dd><code>poiType</code> - POI类型，例如：写字楼， 必填项（keyword和poiType必须至少填一项）</dd>
<dd><code>city</code> - POI所在的城市名称， 例如：北京, 选填</dd>
<dd><code>size</code> - 最大创建的围栏数目，取值范围[1-25]</dd>
<dd><code>customId</code> - 自定义的围栏标识， 选填，<b>建议必填并且保证唯一性</b></dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="http://lbs.amap.com/api/android-location-sdk/download/" target="_blank">POI分类编码表和城市编码表</a></dd>
</dl>
</li>
</ul>
<a name="addGeoFence-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addGeoFence</h4>
<pre>public&nbsp;void&nbsp;addGeoFence(java.lang.String&nbsp;keyword,
                        java.lang.String&nbsp;customId)</pre>
<div class="block">创建行政区划围栏</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>keyword</code> - 关键字 必填项</dd>
<dd><code>customId</code> - 自定义的围栏标识， 选填，<b>建议必填并且保证唯一性</b></dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.0</dd>
</dl>
</li>
</ul>
<a name="removeGeoFence--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removeGeoFence</h4>
<pre>public&nbsp;void&nbsp;removeGeoFence()</pre>
<div class="block">移除所有地理围栏</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.0</dd>
</dl>
</li>
</ul>
<a name="removeGeoFence-com.amap.api.fence.GeoFence-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removeGeoFence</h4>
<pre>public&nbsp;boolean&nbsp;removeGeoFence(<a href="../../../../com/amap/api/fence/GeoFence.html" title="com.amap.api.fence中的类">GeoFence</a>&nbsp;geoFence)</pre>
<div class="block">移除指定的geofence
 <p>
 当要移除所有围栏时可以使用<a href="../../../../com/amap/api/fence/GeoFenceClient.html#removeGeoFence--"><code>GeoFenceClient.removeGeoFence()</code></a>方法 当要销毁GeoFneceClient时，需要调用一次
 <a href="../../../../com/amap/api/fence/GeoFenceClient.html#removeGeoFence--"><code>GeoFenceClient.removeGeoFence()</code></a>方法，确保所有围栏已移除
 </p></div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>geoFence</code> - 要移除的geofence</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>是否移除成功</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.3.0</dd>
</dl>
</li>
</ul>
<a name="getAllGeoFence--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAllGeoFence</h4>
<pre>public&nbsp;java.util.List&lt;<a href="../../../../com/amap/api/fence/GeoFence.html" title="com.amap.api.fence中的类">GeoFence</a>&gt;&nbsp;getAllGeoFence()</pre>
<div class="block">获取所有已经创建的地理围栏</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>所有已经创建的地理围栏</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.3.0</dd>
</dl>
</li>
</ul>
<a name="setGeoFenceAble-java.lang.String-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setGeoFenceAble</h4>
<pre>public&nbsp;void&nbsp;setGeoFenceAble(java.lang.String&nbsp;fenceId,
                            boolean&nbsp;able)</pre>
<div class="block">设置围栏是否生效，</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>fenceId</code> - 需要改变的地理围栏的fenceId</dd>
<dd><code>able</code> - 是否生效，true:生效；false:不生效, 默认：true</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.6.0</dd>
</dl>
</li>
</ul>
<a name="pauseGeoFence--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>pauseGeoFence</h4>
<pre>public&nbsp;void&nbsp;pauseGeoFence()</pre>
<div class="block">暂停地理围栏
 <p>
     暂停地理围栏的监听、同时地理围栏内部的定位也会停止
 </p></div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.6.0</dd>
</dl>
</li>
</ul>
<a name="resumeGeoFence--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>resumeGeoFence</h4>
<pre>public&nbsp;void&nbsp;resumeGeoFence()</pre>
<div class="block">恢复地理围栏
 <p>
     恢复地理围栏的监听、同时地理围栏内部的定位也会重新开始定位<br>
     <font color="red"><b>注意：</b>通过setGeoFenceAble接口设置为false的围栏不会重新生效</font>
 </p></div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.6.0</dd>
</dl>
</li>
</ul>
<a name="isPause--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>isPause</h4>
<pre>public&nbsp;boolean&nbsp;isPause()</pre>
<div class="block">地理围栏是否已经暂停</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>地理围栏是否处于暂停状态</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.6.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/GeoFenceClient.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/amap/api/fence/GeoFence.html" title="com.amap.api.fence中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../com/amap/api/fence/GeoFenceListener.html" title="com.amap.api.fence中的接口"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/amap/api/fence/GeoFenceClient.html" target="_top">框架</a></li>
<li><a href="GeoFenceClient.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li><a href="#field.summary">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li><a href="#field.detail">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
