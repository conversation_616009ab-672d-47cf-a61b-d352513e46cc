<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>常量字段值</title>
<link rel="stylesheet" type="text/css" href="stylesheet.css" title="Style">
<script type="text/javascript" src="script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="\u5E38\u91CF\u5B57\u6BB5\u503C";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="overview-tree.html">树</a></li>
<li><a href="deprecated-list.html">已过时</a></li>
<li><a href="index-files/index-1.html">索引</a></li>
<li><a href="help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个</li>
<li>下一个</li>
</ul>
<ul class="navList">
<li><a href="index.html?constant-values.html" target="_top">框架</a></li>
<li><a href="constant-values.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 title="常量字段值" class="title">常量字段值</h1>
<h2 title="目录">目录</h2>
<ul>
<li><a href="#com.amap">com.amap.*</a></li>
</ul>
</div>
<div class="constantValuesContainer"><a name="com.amap">
<!--   -->
</a>
<h2 title="com.amap">com.amap.*</h2>
<ul class="blockList">
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="常量字段值表, 列表常量字段和值">
<caption><span>com.amap.api.fence.<a href="com/amap/api/fence/GeoFence.html" title="com.amap.api.fence中的类">GeoFence</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th scope="col">常量字段</th>
<th class="colLast" scope="col">值</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.fence.GeoFence.ADDGEOFENCE_SUCCESS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/fence/GeoFence.html#ADDGEOFENCE_SUCCESS">ADDGEOFENCE_SUCCESS</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.fence.GeoFence.BUNDLE_KEY_CUSTOMID">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/fence/GeoFence.html#BUNDLE_KEY_CUSTOMID">BUNDLE_KEY_CUSTOMID</a></code></td>
<td class="colLast"><code>"customId"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.fence.GeoFence.BUNDLE_KEY_FENCE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/fence/GeoFence.html#BUNDLE_KEY_FENCE">BUNDLE_KEY_FENCE</a></code></td>
<td class="colLast"><code>"fence"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.fence.GeoFence.BUNDLE_KEY_FENCEID">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/fence/GeoFence.html#BUNDLE_KEY_FENCEID">BUNDLE_KEY_FENCEID</a></code></td>
<td class="colLast"><code>"fenceid"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.fence.GeoFence.BUNDLE_KEY_FENCESTATUS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/fence/GeoFence.html#BUNDLE_KEY_FENCESTATUS">BUNDLE_KEY_FENCESTATUS</a></code></td>
<td class="colLast"><code>"event"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.fence.GeoFence.BUNDLE_KEY_LOCERRORCODE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/fence/GeoFence.html#BUNDLE_KEY_LOCERRORCODE">BUNDLE_KEY_LOCERRORCODE</a></code></td>
<td class="colLast"><code>"location_errorcode"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.fence.GeoFence.ERROR_CODE_EXISTS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/fence/GeoFence.html#ERROR_CODE_EXISTS">ERROR_CODE_EXISTS</a></code></td>
<td class="colLast"><code>17</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.fence.GeoFence.ERROR_CODE_FAILURE_AUTH">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/fence/GeoFence.html#ERROR_CODE_FAILURE_AUTH">ERROR_CODE_FAILURE_AUTH</a></code></td>
<td class="colLast"><code>7</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.fence.GeoFence.ERROR_CODE_FAILURE_CONNECTION">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/fence/GeoFence.html#ERROR_CODE_FAILURE_CONNECTION">ERROR_CODE_FAILURE_CONNECTION</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.fence.GeoFence.ERROR_CODE_FAILURE_PARSER">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/fence/GeoFence.html#ERROR_CODE_FAILURE_PARSER">ERROR_CODE_FAILURE_PARSER</a></code></td>
<td class="colLast"><code>5</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.fence.GeoFence.ERROR_CODE_INVALID_PARAMETER">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/fence/GeoFence.html#ERROR_CODE_INVALID_PARAMETER">ERROR_CODE_INVALID_PARAMETER</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.fence.GeoFence.ERROR_CODE_UNKNOWN">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/fence/GeoFence.html#ERROR_CODE_UNKNOWN">ERROR_CODE_UNKNOWN</a></code></td>
<td class="colLast"><code>8</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.fence.GeoFence.ERROR_NO_VALIDFENCE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/fence/GeoFence.html#ERROR_NO_VALIDFENCE">ERROR_NO_VALIDFENCE</a></code></td>
<td class="colLast"><code>16</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.fence.GeoFence.STATUS_IN">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/fence/GeoFence.html#STATUS_IN">STATUS_IN</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.fence.GeoFence.STATUS_LOCFAIL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/fence/GeoFence.html#STATUS_LOCFAIL">STATUS_LOCFAIL</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.fence.GeoFence.STATUS_OUT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/fence/GeoFence.html#STATUS_OUT">STATUS_OUT</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.fence.GeoFence.STATUS_STAYED">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/fence/GeoFence.html#STATUS_STAYED">STATUS_STAYED</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.fence.GeoFence.STATUS_UNKNOWN">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/fence/GeoFence.html#STATUS_UNKNOWN">STATUS_UNKNOWN</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.fence.GeoFence.TYPE_AMAPPOI">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/fence/GeoFence.html#TYPE_AMAPPOI">TYPE_AMAPPOI</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.fence.GeoFence.TYPE_DISTRICT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/fence/GeoFence.html#TYPE_DISTRICT">TYPE_DISTRICT</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.fence.GeoFence.TYPE_POLYGON">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/fence/GeoFence.html#TYPE_POLYGON">TYPE_POLYGON</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.fence.GeoFence.TYPE_ROUND">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/fence/GeoFence.html#TYPE_ROUND">TYPE_ROUND</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="常量字段值表, 列表常量字段和值">
<caption><span>com.amap.api.fence.<a href="com/amap/api/fence/GeoFenceClient.html" title="com.amap.api.fence中的类">GeoFenceClient</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th scope="col">常量字段</th>
<th class="colLast" scope="col">值</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.fence.GeoFenceClient.GEOFENCE_IN">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/fence/GeoFenceClient.html#GEOFENCE_IN">GEOFENCE_IN</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.fence.GeoFenceClient.GEOFENCE_OUT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/fence/GeoFenceClient.html#GEOFENCE_OUT">GEOFENCE_OUT</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.fence.GeoFenceClient.GEOFENCE_STAYED">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/fence/GeoFenceClient.html#GEOFENCE_STAYED">GEOFENCE_STAYED</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
</tbody>
</table>
</li>
</ul>
<ul class="blockList">
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="常量字段值表, 列表常量字段和值">
<caption><span>com.amap.api.location.<a href="com/amap/api/location/AMapLocation.html" title="com.amap.api.location中的类">AMapLocation</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th scope="col">常量字段</th>
<th class="colLast" scope="col">值</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.location.AMapLocation.ERROR_CODE_AIRPLANEMODE_WIFIOFF">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/location/AMapLocation.html#ERROR_CODE_AIRPLANEMODE_WIFIOFF">ERROR_CODE_AIRPLANEMODE_WIFIOFF</a></code></td>
<td class="colLast"><code>18</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.location.AMapLocation.ERROR_CODE_FAILURE_AUTH">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/location/AMapLocation.html#ERROR_CODE_FAILURE_AUTH">ERROR_CODE_FAILURE_AUTH</a></code></td>
<td class="colLast"><code>7</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.location.AMapLocation.ERROR_CODE_FAILURE_CELL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/location/AMapLocation.html#ERROR_CODE_FAILURE_CELL">ERROR_CODE_FAILURE_CELL</a></code></td>
<td class="colLast"><code>11</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.location.AMapLocation.ERROR_CODE_FAILURE_COARSE_LOCATION">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/location/AMapLocation.html#ERROR_CODE_FAILURE_COARSE_LOCATION">ERROR_CODE_FAILURE_COARSE_LOCATION</a></code></td>
<td class="colLast"><code>20</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.location.AMapLocation.ERROR_CODE_FAILURE_CONNECTION">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/location/AMapLocation.html#ERROR_CODE_FAILURE_CONNECTION">ERROR_CODE_FAILURE_CONNECTION</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.location.AMapLocation.ERROR_CODE_FAILURE_INIT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/location/AMapLocation.html#ERROR_CODE_FAILURE_INIT">ERROR_CODE_FAILURE_INIT</a></code></td>
<td class="colLast"><code>9</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.location.AMapLocation.ERROR_CODE_FAILURE_LOCATION">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/location/AMapLocation.html#ERROR_CODE_FAILURE_LOCATION">ERROR_CODE_FAILURE_LOCATION</a></code></td>
<td class="colLast"><code>6</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.location.AMapLocation.ERROR_CODE_FAILURE_LOCATION_PARAMETER">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/location/AMapLocation.html#ERROR_CODE_FAILURE_LOCATION_PARAMETER">ERROR_CODE_FAILURE_LOCATION_PARAMETER</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.location.AMapLocation.ERROR_CODE_FAILURE_LOCATION_PERMISSION">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/location/AMapLocation.html#ERROR_CODE_FAILURE_LOCATION_PERMISSION">ERROR_CODE_FAILURE_LOCATION_PERMISSION</a></code></td>
<td class="colLast"><code>12</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.location.AMapLocation.ERROR_CODE_FAILURE_NOENOUGHSATELLITES">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/location/AMapLocation.html#ERROR_CODE_FAILURE_NOENOUGHSATELLITES">ERROR_CODE_FAILURE_NOENOUGHSATELLITES</a></code></td>
<td class="colLast"><code>14</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.location.AMapLocation.ERROR_CODE_FAILURE_NOWIFIANDAP">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/location/AMapLocation.html#ERROR_CODE_FAILURE_NOWIFIANDAP">ERROR_CODE_FAILURE_NOWIFIANDAP</a></code></td>
<td class="colLast"><code>13</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.location.AMapLocation.ERROR_CODE_FAILURE_PARSER">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/location/AMapLocation.html#ERROR_CODE_FAILURE_PARSER">ERROR_CODE_FAILURE_PARSER</a></code></td>
<td class="colLast"><code>5</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.location.AMapLocation.ERROR_CODE_FAILURE_SIMULATION_LOCATION">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/location/AMapLocation.html#ERROR_CODE_FAILURE_SIMULATION_LOCATION">ERROR_CODE_FAILURE_SIMULATION_LOCATION</a></code></td>
<td class="colLast"><code>15</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.location.AMapLocation.ERROR_CODE_FAILURE_WIFI_INFO">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/location/AMapLocation.html#ERROR_CODE_FAILURE_WIFI_INFO">ERROR_CODE_FAILURE_WIFI_INFO</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.location.AMapLocation.ERROR_CODE_INVALID_PARAMETER">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/location/AMapLocation.html#ERROR_CODE_INVALID_PARAMETER">ERROR_CODE_INVALID_PARAMETER</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.location.AMapLocation.ERROR_CODE_NOCGI_WIFIOFF">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/location/AMapLocation.html#ERROR_CODE_NOCGI_WIFIOFF">ERROR_CODE_NOCGI_WIFIOFF</a></code></td>
<td class="colLast"><code>19</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.location.AMapLocation.ERROR_CODE_SERVICE_FAIL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/location/AMapLocation.html#ERROR_CODE_SERVICE_FAIL">ERROR_CODE_SERVICE_FAIL</a></code></td>
<td class="colLast"><code>10</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.location.AMapLocation.ERROR_CODE_UNKNOWN">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/location/AMapLocation.html#ERROR_CODE_UNKNOWN">ERROR_CODE_UNKNOWN</a></code></td>
<td class="colLast"><code>8</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.location.AMapLocation.GPS_ACCURACY_BAD">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/location/AMapLocation.html#GPS_ACCURACY_BAD">GPS_ACCURACY_BAD</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.location.AMapLocation.GPS_ACCURACY_GOOD">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/location/AMapLocation.html#GPS_ACCURACY_GOOD">GPS_ACCURACY_GOOD</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.location.AMapLocation.GPS_ACCURACY_UNKNOWN">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/location/AMapLocation.html#GPS_ACCURACY_UNKNOWN">GPS_ACCURACY_UNKNOWN</a></code></td>
<td class="colLast"><code>-1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.location.AMapLocation.LOCATION_COMPENSATION">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/location/AMapLocation.html#LOCATION_COMPENSATION">LOCATION_COMPENSATION</a></code></td>
<td class="colLast"><code>10</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.location.AMapLocation.LOCATION_SUCCESS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/location/AMapLocation.html#LOCATION_SUCCESS">LOCATION_SUCCESS</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.location.AMapLocation.LOCATION_TYPE_CELL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/location/AMapLocation.html#LOCATION_TYPE_CELL">LOCATION_TYPE_CELL</a></code></td>
<td class="colLast"><code>6</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.location.AMapLocation.LOCATION_TYPE_COARSE_LOCATION">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/location/AMapLocation.html#LOCATION_TYPE_COARSE_LOCATION">LOCATION_TYPE_COARSE_LOCATION</a></code></td>
<td class="colLast"><code>11</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.location.AMapLocation.LOCATION_TYPE_FAST">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/location/AMapLocation.html#LOCATION_TYPE_FAST">LOCATION_TYPE_FAST</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.location.AMapLocation.LOCATION_TYPE_FIX_CACHE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/location/AMapLocation.html#LOCATION_TYPE_FIX_CACHE">LOCATION_TYPE_FIX_CACHE</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.location.AMapLocation.LOCATION_TYPE_GPS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS">LOCATION_TYPE_GPS</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.location.AMapLocation.LOCATION_TYPE_LAST_LOCATION_CACHE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/location/AMapLocation.html#LOCATION_TYPE_LAST_LOCATION_CACHE">LOCATION_TYPE_LAST_LOCATION_CACHE</a></code></td>
<td class="colLast"><code>9</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.location.AMapLocation.LOCATION_TYPE_OFFLINE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/location/AMapLocation.html#LOCATION_TYPE_OFFLINE">LOCATION_TYPE_OFFLINE</a></code></td>
<td class="colLast"><code>8</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.location.AMapLocation.LOCATION_TYPE_SAME_REQ">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/location/AMapLocation.html#LOCATION_TYPE_SAME_REQ">LOCATION_TYPE_SAME_REQ</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.location.AMapLocation.LOCATION_TYPE_WIFI">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/location/AMapLocation.html#LOCATION_TYPE_WIFI">LOCATION_TYPE_WIFI</a></code></td>
<td class="colLast"><code>5</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.location.AMapLocation.TRUSTED_LEVEL_BAD">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/location/AMapLocation.html#TRUSTED_LEVEL_BAD">TRUSTED_LEVEL_BAD</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.location.AMapLocation.TRUSTED_LEVEL_HIGH">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/location/AMapLocation.html#TRUSTED_LEVEL_HIGH">TRUSTED_LEVEL_HIGH</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.location.AMapLocation.TRUSTED_LEVEL_LOW">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/location/AMapLocation.html#TRUSTED_LEVEL_LOW">TRUSTED_LEVEL_LOW</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.location.AMapLocation.TRUSTED_LEVEL_NORMAL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/location/AMapLocation.html#TRUSTED_LEVEL_NORMAL">TRUSTED_LEVEL_NORMAL</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="常量字段值表, 列表常量字段和值">
<caption><span>com.amap.api.location.<a href="com/amap/api/location/AMapLocationQualityReport.html" title="com.amap.api.location中的类">AMapLocationQualityReport</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th scope="col">常量字段</th>
<th class="colLast" scope="col">值</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.location.AMapLocationQualityReport.GPS_STATUS_MODE_SAVING">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/location/AMapLocationQualityReport.html#GPS_STATUS_MODE_SAVING">GPS_STATUS_MODE_SAVING</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.location.AMapLocationQualityReport.GPS_STATUS_NOGPSPERMISSION">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/location/AMapLocationQualityReport.html#GPS_STATUS_NOGPSPERMISSION">GPS_STATUS_NOGPSPERMISSION</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.location.AMapLocationQualityReport.GPS_STATUS_NOGPSPROVIDER">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/location/AMapLocationQualityReport.html#GPS_STATUS_NOGPSPROVIDER">GPS_STATUS_NOGPSPROVIDER</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.location.AMapLocationQualityReport.GPS_STATUS_OFF">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/location/AMapLocationQualityReport.html#GPS_STATUS_OFF">GPS_STATUS_OFF</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.location.AMapLocationQualityReport.GPS_STATUS_OK">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/location/AMapLocationQualityReport.html#GPS_STATUS_OK">GPS_STATUS_OK</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
</tbody>
</table>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="overview-tree.html">树</a></li>
<li><a href="deprecated-list.html">已过时</a></li>
<li><a href="index-files/index-1.html">索引</a></li>
<li><a href="help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个</li>
<li>下一个</li>
</ul>
<ul class="navList">
<li><a href="index.html?constant-values.html" target="_top">框架</a></li>
<li><a href="constant-values.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
