<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>L - 索引</title>
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="L - \u7D22\u5F15";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-8.html">上一个字母</a></li>
<li><a href="index-10.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-9.html" target="_top">框架</a></li>
<li><a href="index-9.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="contentContainer"><a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">I</a>&nbsp;<a href="index-9.html">L</a>&nbsp;<a href="index-10.html">O</a>&nbsp;<a href="index-11.html">P</a>&nbsp;<a href="index-12.html">R</a>&nbsp;<a href="index-13.html">S</a>&nbsp;<a href="index-14.html">T</a>&nbsp;<a href="index-15.html">U</a>&nbsp;<a href="index-16.html">V</a>&nbsp;<a href="index-17.html">W</a>&nbsp;<a name="I:L">
<!--   -->
</a>
<h2 class="title">L</h2>
<dl>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocation.html#LOCATION_COMPENSATION">LOCATION_COMPENSATION</a></span> - 类 中的静态变量com.amap.api.location.<a href="../com/amap/api/location/AMapLocation.html" title="com.amap.api.location中的类">AMapLocation</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocation.html#LOCATION_SUCCESS">LOCATION_SUCCESS</a></span> - 类 中的静态变量com.amap.api.location.<a href="../com/amap/api/location/AMapLocation.html" title="com.amap.api.location中的类">AMapLocation</a></dt>
<dd>
<div class="block">定位错误码：定位成功</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_CELL">LOCATION_TYPE_CELL</a></span> - 类 中的静态变量com.amap.api.location.<a href="../com/amap/api/location/AMapLocation.html" title="com.amap.api.location中的类">AMapLocation</a></dt>
<dd>
<div class="block">定位结果类型：基站定位结果
 
 属于网络定位</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_COARSE_LOCATION">LOCATION_TYPE_COARSE_LOCATION</a></span> - 类 中的静态变量com.amap.api.location.<a href="../com/amap/api/location/AMapLocation.html" title="com.amap.api.location中的类">AMapLocation</a></dt>
<dd>
<div class="block">模糊定位类型</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_FAST">LOCATION_TYPE_FAST</a></span> - 类 中的静态变量com.amap.api.location.<a href="../com/amap/api/location/AMapLocation.html" title="com.amap.api.location中的类">AMapLocation</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span>
<div class="block"><span class="deprecationComment">已合并到<a href="../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_SAME_REQ"><code>AMapLocation.LOCATION_TYPE_SAME_REQ</code></a></span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_FIX_CACHE">LOCATION_TYPE_FIX_CACHE</a></span> - 类 中的静态变量com.amap.api.location.<a href="../com/amap/api/location/AMapLocation.html" title="com.amap.api.location中的类">AMapLocation</a></dt>
<dd>
<div class="block">定位结果类型：缓存定位结果
 
 返回一段时间前设备在相同的环境中缓存下来的网络定位结果，节省无必要的设备定位消耗</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS">LOCATION_TYPE_GPS</a></span> - 类 中的静态变量com.amap.api.location.<a href="../com/amap/api/location/AMapLocation.html" title="com.amap.api.location中的类">AMapLocation</a></dt>
<dd>
<div class="block">定位结果类型：卫星定位结果
 
 通过设备卫星定位模块返回的定位结果</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_LAST_LOCATION_CACHE">LOCATION_TYPE_LAST_LOCATION_CACHE</a></span> - 类 中的静态变量com.amap.api.location.<a href="../com/amap/api/location/AMapLocation.html" title="com.amap.api.location中的类">AMapLocation</a></dt>
<dd>
<div class="block">定位结果类型： 最后位置缓存</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_OFFLINE">LOCATION_TYPE_OFFLINE</a></span> - 类 中的静态变量com.amap.api.location.<a href="../com/amap/api/location/AMapLocation.html" title="com.amap.api.location中的类">AMapLocation</a></dt>
<dd>
<div class="block">定位结果类型： 离线定位结果</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_SAME_REQ">LOCATION_TYPE_SAME_REQ</a></span> - 类 中的静态变量com.amap.api.location.<a href="../com/amap/api/location/AMapLocation.html" title="com.amap.api.location中的类">AMapLocation</a></dt>
<dd>
<div class="block">定位结果类型：前次定位结果
 
 网络定位请求低于1秒、或两次定位之间设备位置变化非常小时返回，设备位移通过传感器感知</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_WIFI">LOCATION_TYPE_WIFI</a></span> - 类 中的静态变量com.amap.api.location.<a href="../com/amap/api/location/AMapLocation.html" title="com.amap.api.location中的类">AMapLocation</a></dt>
<dd>
<div class="block">定位结果类型：Wifi定位结果
 
 属于网络定位，定位精度相对基站定位会更好</div>
</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">I</a>&nbsp;<a href="index-9.html">L</a>&nbsp;<a href="index-10.html">O</a>&nbsp;<a href="index-11.html">P</a>&nbsp;<a href="index-12.html">R</a>&nbsp;<a href="index-13.html">S</a>&nbsp;<a href="index-14.html">T</a>&nbsp;<a href="index-15.html">U</a>&nbsp;<a href="index-16.html">V</a>&nbsp;<a href="index-17.html">W</a>&nbsp;</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-8.html">上一个字母</a></li>
<li><a href="index-10.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-9.html" target="_top">框架</a></li>
<li><a href="index-9.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
