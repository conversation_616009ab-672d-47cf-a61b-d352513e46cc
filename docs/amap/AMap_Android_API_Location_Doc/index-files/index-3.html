<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>C - 索引</title>
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="C - \u7D22\u5F15";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-2.html">上一个字母</a></li>
<li><a href="index-4.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-3.html" target="_top">框架</a></li>
<li><a href="index-3.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="contentContainer"><a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">I</a>&nbsp;<a href="index-9.html">L</a>&nbsp;<a href="index-10.html">O</a>&nbsp;<a href="index-11.html">P</a>&nbsp;<a href="index-12.html">R</a>&nbsp;<a href="index-13.html">S</a>&nbsp;<a href="index-14.html">T</a>&nbsp;<a href="index-15.html">U</a>&nbsp;<a href="index-16.html">V</a>&nbsp;<a href="index-17.html">W</a>&nbsp;<a name="I:C">
<!--   -->
</a>
<h2 class="title">C</h2>
<dl>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/CoordinateConverter.html#calculateLineDistance-com.amap.api.location.DPoint-com.amap.api.location.DPoint-">calculateLineDistance(DPoint, DPoint)</a></span> - 类 中的静态方法com.amap.api.location.<a href="../com/amap/api/location/CoordinateConverter.html" title="com.amap.api.location中的类">CoordinateConverter</a></dt>
<dd>
<div class="block">计算两点间距离 单位：米</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocationClientOption.html#clone--">clone()</a></span> - 类 中的方法com.amap.api.location.<a href="../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a></dt>
<dd>
<div class="block">获取AMapLocationClientOption对象的拷贝</div>
</dd>
<dt><a href="../com/amap/api/fence/package-summary.html">com.amap.api.fence</a> - 程序包 com.amap.api.fence</dt>
<dd>&nbsp;</dd>
<dt><a href="../com/amap/api/location/package-summary.html">com.amap.api.location</a> - 程序包 com.amap.api.location</dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/CoordinateConverter.html#convert--">convert()</a></span> - 类 中的方法com.amap.api.location.<a href="../com/amap/api/location/CoordinateConverter.html" title="com.amap.api.location中的类">CoordinateConverter</a></dt>
<dd>
<div class="block">进行坐标转换</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/CoordinateConverter.html#coord-com.amap.api.location.DPoint-">coord(DPoint)</a></span> - 类 中的方法com.amap.api.location.<a href="../com/amap/api/location/CoordinateConverter.html" title="com.amap.api.location中的类">CoordinateConverter</a></dt>
<dd>
<div class="block">设置偏转数据源</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocation.html#COORD_TYPE_GCJ02">COORD_TYPE_GCJ02</a></span> - 类 中的静态变量com.amap.api.location.<a href="../com/amap/api/location/AMapLocation.html" title="com.amap.api.location中的类">AMapLocation</a></dt>
<dd>
<div class="block">GCJ02坐标系</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocation.html#COORD_TYPE_WGS84">COORD_TYPE_WGS84</a></span> - 类 中的静态变量com.amap.api.location.<a href="../com/amap/api/location/AMapLocation.html" title="com.amap.api.location中的类">AMapLocation</a></dt>
<dd>
<div class="block">WGS84坐标系</div>
</dd>
<dt><a href="../com/amap/api/location/CoordinateConverter.html" title="com.amap.api.location中的类"><span class="typeNameLink">CoordinateConverter</span></a> - <a href="../com/amap/api/location/package-summary.html">com.amap.api.location</a>中的类</dt>
<dd>
<div class="block">坐标转换类</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/CoordinateConverter.html#CoordinateConverter-Context-">CoordinateConverter(Context)</a></span> - 类 的构造器com.amap.api.location.<a href="../com/amap/api/location/CoordinateConverter.html" title="com.amap.api.location中的类">CoordinateConverter</a></dt>
<dd>
<div class="block">构造方法</div>
</dd>
<dt><a href="../com/amap/api/location/CoordinateConverter.CoordType.html" title="com.amap.api.location中的枚举"><span class="typeNameLink">CoordinateConverter.CoordType</span></a> - <a href="../com/amap/api/location/package-summary.html">com.amap.api.location</a>中的枚举</dt>
<dd>
<div class="block">坐标类型</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/fence/GeoFenceClient.html#createPendingIntent-java.lang.String-">createPendingIntent(String)</a></span> - 类 中的方法com.amap.api.fence.<a href="../com/amap/api/fence/GeoFenceClient.html" title="com.amap.api.fence中的类">GeoFenceClient</a></dt>
<dd>
<div class="block">创建PendingIntent
 
 <b>注意：一个GeoFenceClient只接受一个PendingIntent,如果重复设置多个，则以最后一个为准</b>
 </div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/fence/GeoFence.html#CREATOR">CREATOR</a></span> - 类 中的静态变量com.amap.api.fence.<a href="../com/amap/api/fence/GeoFence.html" title="com.amap.api.fence中的类">GeoFence</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/DPoint.html#CREATOR">CREATOR</a></span> - 类 中的静态变量com.amap.api.location.<a href="../com/amap/api/location/DPoint.html" title="com.amap.api.location中的类">DPoint</a></dt>
<dd>&nbsp;</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">I</a>&nbsp;<a href="index-9.html">L</a>&nbsp;<a href="index-10.html">O</a>&nbsp;<a href="index-11.html">P</a>&nbsp;<a href="index-12.html">R</a>&nbsp;<a href="index-13.html">S</a>&nbsp;<a href="index-14.html">T</a>&nbsp;<a href="index-15.html">U</a>&nbsp;<a href="index-16.html">V</a>&nbsp;<a href="index-17.html">W</a>&nbsp;</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-2.html">上一个字母</a></li>
<li><a href="index-4.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-3.html" target="_top">框架</a></li>
<li><a href="index-3.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
