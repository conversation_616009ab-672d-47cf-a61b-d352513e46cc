<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>E - 索引</title>
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="E - \u7D22\u5F15";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-4.html">上一个字母</a></li>
<li><a href="index-6.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-5.html" target="_top">框架</a></li>
<li><a href="index-5.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="contentContainer"><a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">I</a>&nbsp;<a href="index-9.html">L</a>&nbsp;<a href="index-10.html">O</a>&nbsp;<a href="index-11.html">P</a>&nbsp;<a href="index-12.html">R</a>&nbsp;<a href="index-13.html">S</a>&nbsp;<a href="index-14.html">T</a>&nbsp;<a href="index-15.html">U</a>&nbsp;<a href="index-16.html">V</a>&nbsp;<a href="index-17.html">W</a>&nbsp;<a name="I:E">
<!--   -->
</a>
<h2 class="title">E</h2>
<dl>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocationClient.html#enableBackgroundLocation-int-Notification-">enableBackgroundLocation(int, Notification)</a></span> - 类 中的方法com.amap.api.location.<a href="../com/amap/api/location/AMapLocationClient.html" title="com.amap.api.location中的类">AMapLocationClient</a></dt>
<dd>
<div class="block">开启后台定位功能
 <font color="red"><b>注意:</b></font>
 如果您设置了target>=28,需要增加android.permission.FOREGROUND_SERVICE权限,<br/>
 如果您的app需要运行在Android Q版本的手机上，需要为ApsService增加android:foregroundServiceType="location"属性，
 例：&lt;service
             android:name="com.amap.api.location.APSService"
             android:foregroundServiceType="location"&#x2F;&gt;
 
 主要是为了解决Android 8.0以上版本对后台定位的限制，开启后会显示通知栏,如果您的应用本身已经存在一个前台服务通知，则无需再开启此接口<br>
 <font color="red"><b>注意:</b></font>启动后台定位只是代表开启了后台定位的能力，并不代表已经开始定位，开始定位请调用<a href="../com/amap/api/location/AMapLocationClient.html#startLocation--"><code>AMapLocationClient.startLocation()</code></a>。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocation.html#ERROR_CODE_AIRPLANEMODE_WIFIOFF">ERROR_CODE_AIRPLANEMODE_WIFIOFF</a></span> - 类 中的静态变量com.amap.api.location.<a href="../com/amap/api/location/AMapLocation.html" title="com.amap.api.location中的类">AMapLocation</a></dt>
<dd>
<div class="block">定位错误码：定位失败，飞行模式下关闭了WIFI开关，请关闭飞行模式或者打开WIFI开关</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/fence/GeoFence.html#ERROR_CODE_EXISTS">ERROR_CODE_EXISTS</a></span> - 类 中的静态变量com.amap.api.fence.<a href="../com/amap/api/fence/GeoFence.html" title="com.amap.api.fence中的类">GeoFence</a></dt>
<dd>
<div class="block">错误码： 相同的围栏已经存在，无需重复添加
 
 当地理围栏的customID，半径，周边点（多边形），中心点坐标（圆形）这几个属性完全一致时，则认为是相同围栏
 </div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/fence/GeoFence.html#ERROR_CODE_FAILURE_AUTH">ERROR_CODE_FAILURE_AUTH</a></span> - 类 中的静态变量com.amap.api.fence.<a href="../com/amap/api/fence/GeoFence.html" title="com.amap.api.fence中的类">GeoFence</a></dt>
<dd>
<div class="block">错误码：鉴权失败</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocation.html#ERROR_CODE_FAILURE_AUTH">ERROR_CODE_FAILURE_AUTH</a></span> - 类 中的静态变量com.amap.api.location.<a href="../com/amap/api/location/AMapLocation.html" title="com.amap.api.location中的类">AMapLocation</a></dt>
<dd>
<div class="block">定位错误码：KEY错误,可以通过<a href="../com/amap/api/location/AMapLocation.html#getLocationDetail--"><code>AMapLocation.getLocationDetail()</code></a>获取详细信息来跟注册的KEY信息进行对照</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocation.html#ERROR_CODE_FAILURE_CELL">ERROR_CODE_FAILURE_CELL</a></span> - 类 中的静态变量com.amap.api.location.<a href="../com/amap/api/location/AMapLocation.html" title="com.amap.api.location中的类">AMapLocation</a></dt>
<dd>
<div class="block">定位错误码：错误的基站信息，请检查是否安装SIM卡</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocation.html#ERROR_CODE_FAILURE_COARSE_LOCATION">ERROR_CODE_FAILURE_COARSE_LOCATION</a></span> - 类 中的静态变量com.amap.api.location.<a href="../com/amap/api/location/AMapLocation.html" title="com.amap.api.location中的类">AMapLocation</a></dt>
<dd>
<div class="block">定位错误码：定位失败，模糊权限下定位异常，可以通过<a href="../com/amap/api/location/AMapLocation.html#getErrorInfo--"><code>AMapLocation.getErrorInfo()</code></a> 获取详细信息</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/fence/GeoFence.html#ERROR_CODE_FAILURE_CONNECTION">ERROR_CODE_FAILURE_CONNECTION</a></span> - 类 中的静态变量com.amap.api.fence.<a href="../com/amap/api/fence/GeoFence.html" title="com.amap.api.fence中的类">GeoFence</a></dt>
<dd>
<div class="block">错误码：网络连接异常</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocation.html#ERROR_CODE_FAILURE_CONNECTION">ERROR_CODE_FAILURE_CONNECTION</a></span> - 类 中的静态变量com.amap.api.location.<a href="../com/amap/api/location/AMapLocation.html" title="com.amap.api.location中的类">AMapLocation</a></dt>
<dd>
<div class="block">定位错误码：网络连接异常,可以通过<a href="../com/amap/api/location/AMapLocation.html#getLocationDetail--"><code>AMapLocation.getLocationDetail()</code></a>获取详细信息</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocation.html#ERROR_CODE_FAILURE_INIT">ERROR_CODE_FAILURE_INIT</a></span> - 类 中的静态变量com.amap.api.location.<a href="../com/amap/api/location/AMapLocation.html" title="com.amap.api.location中的类">AMapLocation</a></dt>
<dd>
<div class="block">定位错误码：初始化异常,可以通过<a href="../com/amap/api/location/AMapLocation.html#getLocationDetail--"><code>AMapLocation.getLocationDetail()</code></a>获取详细信息</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocation.html#ERROR_CODE_FAILURE_LOCATION">ERROR_CODE_FAILURE_LOCATION</a></span> - 类 中的静态变量com.amap.api.location.<a href="../com/amap/api/location/AMapLocation.html" title="com.amap.api.location中的类">AMapLocation</a></dt>
<dd>
<div class="block">定位错误码：定位结果错误,可以通过<a href="../com/amap/api/location/AMapLocation.html#getLocationDetail--"><code>AMapLocation.getLocationDetail()</code></a>获取详细信息</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocation.html#ERROR_CODE_FAILURE_LOCATION_PARAMETER">ERROR_CODE_FAILURE_LOCATION_PARAMETER</a></span> - 类 中的静态变量com.amap.api.location.<a href="../com/amap/api/location/AMapLocation.html" title="com.amap.api.location中的类">AMapLocation</a></dt>
<dd>
<div class="block">定位错误码：获取到的请求参数为空，可能获取过程中出现异常,可以通过<a href="../com/amap/api/location/AMapLocation.html#getLocationDetail--"><code>AMapLocation.getLocationDetail()</code></a>获取详细信息</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocation.html#ERROR_CODE_FAILURE_LOCATION_PERMISSION">ERROR_CODE_FAILURE_LOCATION_PERMISSION</a></span> - 类 中的静态变量com.amap.api.location.<a href="../com/amap/api/location/AMapLocation.html" title="com.amap.api.location中的类">AMapLocation</a></dt>
<dd>
<div class="block">定位错误码：缺少定位权限,请检查是否配置定位权限,并在安全软件和设置中给应用打开定位权限</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocation.html#ERROR_CODE_FAILURE_NOENOUGHSATELLITES">ERROR_CODE_FAILURE_NOENOUGHSATELLITES</a></span> - 类 中的静态变量com.amap.api.location.<a href="../com/amap/api/location/AMapLocation.html" title="com.amap.api.location中的类">AMapLocation</a></dt>
<dd>
<div class="block">定位错误码：卫星定位失败，可用卫星数不足</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocation.html#ERROR_CODE_FAILURE_NOWIFIANDAP">ERROR_CODE_FAILURE_NOWIFIANDAP</a></span> - 类 中的静态变量com.amap.api.location.<a href="../com/amap/api/location/AMapLocation.html" title="com.amap.api.location中的类">AMapLocation</a></dt>
<dd>
<div class="block">定位错误码：网络定位失败，请检查设备是否插入sim卡、开启移动网络或开启了wifi模块</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/fence/GeoFence.html#ERROR_CODE_FAILURE_PARSER">ERROR_CODE_FAILURE_PARSER</a></span> - 类 中的静态变量com.amap.api.fence.<a href="../com/amap/api/fence/GeoFence.html" title="com.amap.api.fence中的类">GeoFence</a></dt>
<dd>
<div class="block">错误码：解析数据失败（有可能是连接的需要登录的网络但是没有登录）</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocation.html#ERROR_CODE_FAILURE_PARSER">ERROR_CODE_FAILURE_PARSER</a></span> - 类 中的静态变量com.amap.api.location.<a href="../com/amap/api/location/AMapLocation.html" title="com.amap.api.location中的类">AMapLocation</a></dt>
<dd>
<div class="block">定位错误码：解析XML出错,可以通过<a href="../com/amap/api/location/AMapLocation.html#getLocationDetail--"><code>AMapLocation.getLocationDetail()</code></a>获取详细信息</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocation.html#ERROR_CODE_FAILURE_SIMULATION_LOCATION">ERROR_CODE_FAILURE_SIMULATION_LOCATION</a></span> - 类 中的静态变量com.amap.api.location.<a href="../com/amap/api/location/AMapLocation.html" title="com.amap.api.location中的类">AMapLocation</a></dt>
<dd>
<div class="block">定位错误码：定位位置可能被模拟</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocation.html#ERROR_CODE_FAILURE_WIFI_INFO">ERROR_CODE_FAILURE_WIFI_INFO</a></span> - 类 中的静态变量com.amap.api.location.<a href="../com/amap/api/location/AMapLocation.html" title="com.amap.api.location中的类">AMapLocation</a></dt>
<dd>
<div class="block">定位错误码：定位失败，由于设备仅扫描到单个wifi，不能精准的计算出位置信息。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/fence/GeoFence.html#ERROR_CODE_INVALID_PARAMETER">ERROR_CODE_INVALID_PARAMETER</a></span> - 类 中的静态变量com.amap.api.fence.<a href="../com/amap/api/fence/GeoFence.html" title="com.amap.api.fence中的类">GeoFence</a></dt>
<dd>
<div class="block">错误码：参数错误</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocation.html#ERROR_CODE_INVALID_PARAMETER">ERROR_CODE_INVALID_PARAMETER</a></span> - 类 中的静态变量com.amap.api.location.<a href="../com/amap/api/location/AMapLocation.html" title="com.amap.api.location中的类">AMapLocation</a></dt>
<dd>
<div class="block">定位错误码：一些重要参数为空,如context,可以通过<a href="../com/amap/api/location/AMapLocation.html#getLocationDetail--"><code>AMapLocation.getLocationDetail()</code></a>获取详细信息</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocation.html#ERROR_CODE_NOCGI_WIFIOFF">ERROR_CODE_NOCGI_WIFIOFF</a></span> - 类 中的静态变量com.amap.api.location.<a href="../com/amap/api/location/AMapLocation.html" title="com.amap.api.location中的类">AMapLocation</a></dt>
<dd>
<div class="block">定位错误码：定位失败，没有检查到SIM卡，并且关闭了WIFI开关，请打开WIFI开关或者插入SIM卡</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocation.html#ERROR_CODE_SERVICE_FAIL">ERROR_CODE_SERVICE_FAIL</a></span> - 类 中的静态变量com.amap.api.location.<a href="../com/amap/api/location/AMapLocation.html" title="com.amap.api.location中的类">AMapLocation</a></dt>
<dd>
<div class="block">定位错误码：定位服务启动失败，请检查是否配置service并且manifest中service标签是否配置在application标签内</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/fence/GeoFence.html#ERROR_CODE_UNKNOWN">ERROR_CODE_UNKNOWN</a></span> - 类 中的静态变量com.amap.api.fence.<a href="../com/amap/api/fence/GeoFence.html" title="com.amap.api.fence中的类">GeoFence</a></dt>
<dd>
<div class="block">错误码：其他未知错误</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocation.html#ERROR_CODE_UNKNOWN">ERROR_CODE_UNKNOWN</a></span> - 类 中的静态变量com.amap.api.location.<a href="../com/amap/api/location/AMapLocation.html" title="com.amap.api.location中的类">AMapLocation</a></dt>
<dd>
<div class="block">定位错误码：其他错误,可以通过<a href="../com/amap/api/location/AMapLocation.html#getLocationDetail--"><code>AMapLocation.getLocationDetail()</code></a>获取详细信息</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/fence/GeoFence.html#ERROR_NO_VALIDFENCE">ERROR_NO_VALIDFENCE</a></span> - 类 中的静态变量com.amap.api.fence.<a href="../com/amap/api/fence/GeoFence.html" title="com.amap.api.fence中的类">GeoFence</a></dt>
<dd>
<div class="block">错误码：无可用地理围栏</div>
</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">I</a>&nbsp;<a href="index-9.html">L</a>&nbsp;<a href="index-10.html">O</a>&nbsp;<a href="index-11.html">P</a>&nbsp;<a href="index-12.html">R</a>&nbsp;<a href="index-13.html">S</a>&nbsp;<a href="index-14.html">T</a>&nbsp;<a href="index-15.html">U</a>&nbsp;<a href="index-16.html">V</a>&nbsp;<a href="index-17.html">W</a>&nbsp;</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-4.html">上一个字母</a></li>
<li><a href="index-6.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-5.html" target="_top">框架</a></li>
<li><a href="index-5.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
