<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>G - 索引</title>
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="G - \u7D22\u5F15";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-6.html">上一个字母</a></li>
<li><a href="index-8.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-7.html" target="_top">框架</a></li>
<li><a href="index-7.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="contentContainer"><a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">I</a>&nbsp;<a href="index-9.html">L</a>&nbsp;<a href="index-10.html">O</a>&nbsp;<a href="index-11.html">P</a>&nbsp;<a href="index-12.html">R</a>&nbsp;<a href="index-13.html">S</a>&nbsp;<a href="index-14.html">T</a>&nbsp;<a href="index-15.html">U</a>&nbsp;<a href="index-16.html">V</a>&nbsp;<a href="index-17.html">W</a>&nbsp;<a name="I:G">
<!--   -->
</a>
<h2 class="title">G</h2>
<dl>
<dt><a href="../com/amap/api/fence/GeoFence.html" title="com.amap.api.fence中的类"><span class="typeNameLink">GeoFence</span></a> - <a href="../com/amap/api/fence/package-summary.html">com.amap.api.fence</a>中的类</dt>
<dd>
<div class="block">地理围栏对象</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/fence/GeoFence.html#GeoFence--">GeoFence()</a></span> - 类 的构造器com.amap.api.fence.<a href="../com/amap/api/fence/GeoFence.html" title="com.amap.api.fence中的类">GeoFence</a></dt>
<dd>
<div class="block">构造方法</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/fence/GeoFenceClient.html#GEOFENCE_IN">GEOFENCE_IN</a></span> - 类 中的静态变量com.amap.api.fence.<a href="../com/amap/api/fence/GeoFenceClient.html" title="com.amap.api.fence中的类">GeoFenceClient</a></dt>
<dd>
<div class="block">触发围栏行为-进入围栏</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/fence/GeoFenceClient.html#GEOFENCE_OUT">GEOFENCE_OUT</a></span> - 类 中的静态变量com.amap.api.fence.<a href="../com/amap/api/fence/GeoFenceClient.html" title="com.amap.api.fence中的类">GeoFenceClient</a></dt>
<dd>
<div class="block">触发围栏行为-离开围栏</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/fence/GeoFenceClient.html#GEOFENCE_STAYED">GEOFENCE_STAYED</a></span> - 类 中的静态变量com.amap.api.fence.<a href="../com/amap/api/fence/GeoFenceClient.html" title="com.amap.api.fence中的类">GeoFenceClient</a></dt>
<dd>
<div class="block">触发围栏行为-停留在围栏内（在围栏内停留10分钟以上）</div>
</dd>
<dt><a href="../com/amap/api/fence/GeoFenceClient.html" title="com.amap.api.fence中的类"><span class="typeNameLink">GeoFenceClient</span></a> - <a href="../com/amap/api/fence/package-summary.html">com.amap.api.fence</a>中的类</dt>
<dd>
<div class="block">地理围栏客户端</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/fence/GeoFenceClient.html#GeoFenceClient-Context-">GeoFenceClient(Context)</a></span> - 类 的构造器com.amap.api.fence.<a href="../com/amap/api/fence/GeoFenceClient.html" title="com.amap.api.fence中的类">GeoFenceClient</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/amap/api/fence/GeoFenceListener.html" title="com.amap.api.fence中的接口"><span class="typeNameLink">GeoFenceListener</span></a> - <a href="../com/amap/api/fence/package-summary.html">com.amap.api.fence</a>中的接口</dt>
<dd>
<div class="block">地理围栏回调接口</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocation.html#getAccuracy--">getAccuracy()</a></span> - 类 中的方法com.amap.api.location.<a href="../com/amap/api/location/AMapLocation.html" title="com.amap.api.location中的类">AMapLocation</a></dt>
<dd>
<div class="block">获取定位精度
 
 单位:米</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/fence/GeoFence.html#getActivatesAction--">getActivatesAction()</a></span> - 类 中的方法com.amap.api.fence.<a href="../com/amap/api/fence/GeoFence.html" title="com.amap.api.fence中的类">GeoFence</a></dt>
<dd>
<div class="block">获取设置的地理围栏触发条件
 </div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/fence/DistrictItem.html#getAdcode--">getAdcode()</a></span> - 类 中的方法com.amap.api.fence.<a href="../com/amap/api/fence/DistrictItem.html" title="com.amap.api.fence中的类">DistrictItem</a></dt>
<dd>
<div class="block">获取区域编码</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocation.html#getAdCode--">getAdCode()</a></span> - 类 中的方法com.amap.api.location.<a href="../com/amap/api/location/AMapLocation.html" title="com.amap.api.location中的类">AMapLocation</a></dt>
<dd>
<div class="block">获取区域编码
 
 <b>2.9.0</b>之前的版本定位类型为<a href="../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS"><code>AMapLocation.LOCATION_TYPE_GPS</code></a>时不会返回区域编码<br>
 自<b>2.9.0</b>版本开始，当<a href="../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS"><code>AMapLocation.LOCATION_TYPE_GPS</code></a>时也可以返回区域编码
 </div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/fence/PoiItem.html#getAddress--">getAddress()</a></span> - 类 中的方法com.amap.api.fence.<a href="../com/amap/api/fence/PoiItem.html" title="com.amap.api.fence中的类">PoiItem</a></dt>
<dd>
<div class="block">获取POI的地址信息</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocation.html#getAddress--">getAddress()</a></span> - 类 中的方法com.amap.api.location.<a href="../com/amap/api/location/AMapLocation.html" title="com.amap.api.location中的类">AMapLocation</a></dt>
<dd>
<div class="block">获取地址
 
 <b>2.9.0</b>之前的版本定位类型为<a href="../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS"><code>AMapLocation.LOCATION_TYPE_GPS</code></a>时不会返回地址信息<br>
 自<b>2.9.0</b>版本开始，当类型为<a href="../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS"><code>AMapLocation.LOCATION_TYPE_GPS</code></a>时也可以返回地址信息(需要网络通畅，第一次有可能没有地址信息返回）
 </div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/fence/PoiItem.html#getAdname--">getAdname()</a></span> - 类 中的方法com.amap.api.fence.<a href="../com/amap/api/fence/PoiItem.html" title="com.amap.api.fence中的类">PoiItem</a></dt>
<dd>
<div class="block">获取POI所在的区域名称</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocationQualityReport.html#getAdviseMessage--">getAdviseMessage()</a></span> - 类 中的方法com.amap.api.location.<a href="../com/amap/api/location/AMapLocationQualityReport.html" title="com.amap.api.location中的类">AMapLocationQualityReport</a></dt>
<dd>
<div class="block">获取提示语义,状态良好时，返回的是内容为空
 
     根据当前的质量报告，给出相应的建议
 </div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/fence/GeoFenceClient.html#getAllGeoFence--">getAllGeoFence()</a></span> - 类 中的方法com.amap.api.fence.<a href="../com/amap/api/fence/GeoFenceClient.html" title="com.amap.api.fence中的类">GeoFenceClient</a></dt>
<dd>
<div class="block">获取所有已经创建的地理围栏</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocation.html#getAltitude--">getAltitude()</a></span> - 类 中的方法com.amap.api.location.<a href="../com/amap/api/location/AMapLocation.html" title="com.amap.api.location中的类">AMapLocation</a></dt>
<dd>
<div class="block">获取海拔高度(单位：米)
 
 默认值：0.0<br>
 3.1.0之前的版本只有定位类型为 <a href="../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS"><code>AMapLocation.LOCATION_TYPE_GPS</code></a>时才有值<br>
 自3.1.0版本开始，不限定定位类型，当定位类型不是<a href="../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS"><code>AMapLocation.LOCATION_TYPE_GPS</code></a>时，可以通过
 <a href="../com/amap/api/location/AMapLocationClientOption.html#setSensorEnable-boolean-"><code>AMapLocationClientOption.setSensorEnable(boolean)</code></a>
 控制是否返回海拔高度，当设置为true时会通过手机传感器获取海拔高度,如果手机没有对应的传感器会返回0.0
 </div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocation.html#getAoiName--">getAoiName()</a></span> - 类 中的方法com.amap.api.location.<a href="../com/amap/api/location/AMapLocation.html" title="com.amap.api.location中的类">AMapLocation</a></dt>
<dd>
<div class="block">获取兴趣面名称
 
 <b>2.9.0</b>之前的版本定位类型为<a href="../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS"><code>AMapLocation.LOCATION_TYPE_GPS</code></a>时不会返回兴趣面名称<br>
 自<b>2.9.0</b>版本开始，当<a href="../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS"><code>AMapLocation.LOCATION_TYPE_GPS</code></a>时也可以返回兴趣面名称
 </div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocation.html#getBearing--">getBearing()</a></span> - 类 中的方法com.amap.api.location.<a href="../com/amap/api/location/AMapLocation.html" title="com.amap.api.location中的类">AMapLocation</a></dt>
<dd>
<div class="block">获取方向角(单位：度）
 
 默认值：0.0<br>
 取值范围：【0，360】，其中0度表示正北方向，90度表示正东，180度表示正南，270度表示正西<br>
 3.1.0之前的版本只有定位类型为 <a href="../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS"><code>AMapLocation.LOCATION_TYPE_GPS</code></a>时才有值<br>
 自3.1.0版本开始，不限定定位类型，当定位类型不是<a href="../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS"><code>AMapLocation.LOCATION_TYPE_GPS</code></a>时，可以通过
 <a href="../com/amap/api/location/AMapLocationClientOption.html#setSensorEnable-boolean-"><code>AMapLocationClientOption.setSensorEnable(boolean)</code></a>
 控制是否返回方向角，当设置为true时会通过手机传感器获取方向角,如果手机没有对应的传感器会返回0.0
 
 
     注意：<br>
     定位类型为<a href="../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS"><code>AMapLocation.LOCATION_TYPE_GPS</code></a>时，方向角指的是运动方向<br>
     定位类型不是<a href="../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS"><code>AMapLocation.LOCATION_TYPE_GPS</code></a>时，方向角指的是手机朝向<br>
 </div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocation.html#getBuildingId--">getBuildingId()</a></span> - 类 中的方法com.amap.api.location.<a href="../com/amap/api/location/AMapLocation.html" title="com.amap.api.location中的类">AMapLocation</a></dt>
<dd>
<div class="block">返回支持室内定位的建筑物ID信息</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/fence/GeoFence.html#getCenter--">getCenter()</a></span> - 类 中的方法com.amap.api.fence.<a href="../com/amap/api/fence/GeoFence.html" title="com.amap.api.fence中的类">GeoFence</a></dt>
<dd>
<div class="block">获取围栏中心点坐标</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/fence/PoiItem.html#getCity--">getCity()</a></span> - 类 中的方法com.amap.api.fence.<a href="../com/amap/api/fence/PoiItem.html" title="com.amap.api.fence中的类">PoiItem</a></dt>
<dd>
<div class="block">获取POI所在的城市名称</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocation.html#getCity--">getCity()</a></span> - 类 中的方法com.amap.api.location.<a href="../com/amap/api/location/AMapLocation.html" title="com.amap.api.location中的类">AMapLocation</a></dt>
<dd>
<div class="block">获取城市名称
 
 <b>2.9.0</b>之前的版本定位类型为<a href="../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS"><code>AMapLocation.LOCATION_TYPE_GPS</code></a>时不会返回城市名称<br>
 自<b>2.9.0</b>版本开始，当<a href="../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS"><code>AMapLocation.LOCATION_TYPE_GPS</code></a>时也可以返回省城市名称
 </div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/fence/DistrictItem.html#getCitycode--">getCitycode()</a></span> - 类 中的方法com.amap.api.fence.<a href="../com/amap/api/fence/DistrictItem.html" title="com.amap.api.fence中的类">DistrictItem</a></dt>
<dd>
<div class="block">获取城市编码</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocation.html#getCityCode--">getCityCode()</a></span> - 类 中的方法com.amap.api.location.<a href="../com/amap/api/location/AMapLocation.html" title="com.amap.api.location中的类">AMapLocation</a></dt>
<dd>
<div class="block">获取城市编码
 
 <b>2.9.0</b>之前的版本定位类型为<a href="../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS"><code>AMapLocation.LOCATION_TYPE_GPS</code></a>时不会返回城市编码<br>
 自<b>2.9.0</b>版本开始，当<a href="../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS"><code>AMapLocation.LOCATION_TYPE_GPS</code></a>时也可以返回城市编码
 </div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocation.html#getConScenario--">getConScenario()</a></span> - 类 中的方法com.amap.api.location.<a href="../com/amap/api/location/AMapLocation.html" title="com.amap.api.location中的类">AMapLocation</a></dt>
<dd>
<div class="block">室内外置信度
 
        室内：且置信度取值在[1 ～ 100]，值越大在室内的可能性越大
        室外：且置信度取值在[-100 ～ -1] ,值越小在室外的可能性越大
        无法识别室内外：置信度返回值为 0
 </div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocation.html#getCoordType--">getCoordType()</a></span> - 类 中的方法com.amap.api.location.<a href="../com/amap/api/location/AMapLocation.html" title="com.amap.api.location中的类">AMapLocation</a></dt>
<dd>
<div class="block">获取坐标系类型
 
     高德定位sdk会返回两种坐标系
     
         <a href="../com/amap/api/location/AMapLocation.html#COORD_TYPE_GCJ02"><code>AMapLocation.COORD_TYPE_GCJ02</code></a> -- GCJ02坐标系
     
     
         <a href="../com/amap/api/location/AMapLocation.html#COORD_TYPE_WGS84"><code>AMapLocation.COORD_TYPE_WGS84</code></a> -- WGS84坐标系,国外定位时返回的是WGS84坐标系
     
 </div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocation.html#getCountry--">getCountry()</a></span> - 类 中的方法com.amap.api.location.<a href="../com/amap/api/location/AMapLocation.html" title="com.amap.api.location中的类">AMapLocation</a></dt>
<dd>
<div class="block">获取国家名称</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/fence/GeoFence.html#getCurrentLocation--">getCurrentLocation()</a></span> - 类 中的方法com.amap.api.fence.<a href="../com/amap/api/fence/GeoFence.html" title="com.amap.api.fence中的类">GeoFence</a></dt>
<dd>
<div class="block">获取当前位置，只有在围栏触发时才有值,其它情况值为null</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/fence/GeoFence.html#getCustomId--">getCustomId()</a></span> - 类 中的方法com.amap.api.fence.<a href="../com/amap/api/fence/GeoFence.html" title="com.amap.api.fence中的类">GeoFence</a></dt>
<dd>
<div class="block">获取自定义的围栏ID(添加围栏时设置)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocation.html#getDescription--">getDescription()</a></span> - 类 中的方法com.amap.api.location.<a href="../com/amap/api/location/AMapLocation.html" title="com.amap.api.location中的类">AMapLocation</a></dt>
<dd>
<div class="block">获取位置语义信息</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocationClient.html#getDeviceId-Context-">getDeviceId(Context)</a></span> - 类 中的静态方法com.amap.api.location.<a href="../com/amap/api/location/AMapLocationClient.html" title="com.amap.api.location中的类">AMapLocationClient</a></dt>
<dd>
<div class="block">获取设备号</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocationClientOption.html#getDeviceModeDistanceFilter--">getDeviceModeDistanceFilter()</a></span> - 类 中的方法com.amap.api.location.<a href="../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a></dt>
<dd>
<div class="block">获取仅设备模式/高精度模式的系统定位自动回调最少间隔距离值 <br>
 默认值：0米
 
     只有当定位模式为<a href="../com/amap/api/location/AMapLocationClientOption.AMapLocationMode.html#Device_Sensors"><code>AMapLocationClientOption.AMapLocationMode.Device_Sensors</code></a>（仅设备模式）或 <a href="../com/amap/api/location/AMapLocationClientOption.AMapLocationMode.html#Hight_Accuracy"><code>AMapLocationClientOption.AMapLocationMode.Hight_Accuracy</code></a>（高精度模式）有效
 </div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocation.html#getDistrict--">getDistrict()</a></span> - 类 中的方法com.amap.api.location.<a href="../com/amap/api/location/AMapLocation.html" title="com.amap.api.location中的类">AMapLocation</a></dt>
<dd>
<div class="block">获取区的名称
 
 <b>2.9.0</b>之前的版本定位类型为<a href="../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS"><code>AMapLocation.LOCATION_TYPE_GPS</code></a>时不会返回区的名称<br>
 自<b>2.9.0</b>版本开始，当<a href="../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS"><code>AMapLocation.LOCATION_TYPE_GPS</code></a>时也可以返回区的名称
 </div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/fence/GeoFence.html#getDistrictItemList--">getDistrictItemList()</a></span> - 类 中的方法com.amap.api.fence.<a href="../com/amap/api/fence/GeoFence.html" title="com.amap.api.fence中的类">GeoFence</a></dt>
<dd>
<div class="block">获取该围栏中的行政区划列表
 
 只有围栏类型为:<a href="../com/amap/api/fence/GeoFence.html#TYPE_DISTRICT"><code>GeoFence.TYPE_DISTRICT</code></a>时才有值
 </div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/fence/DistrictItem.html#getDistrictName--">getDistrictName()</a></span> - 类 中的方法com.amap.api.fence.<a href="../com/amap/api/fence/DistrictItem.html" title="com.amap.api.fence中的类">DistrictItem</a></dt>
<dd>
<div class="block">获取行政区划名称</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocation.html#getErrorCode--">getErrorCode()</a></span> - 类 中的方法com.amap.api.location.<a href="../com/amap/api/location/AMapLocation.html" title="com.amap.api.location中的类">AMapLocation</a></dt>
<dd>
<div class="block">获取错误码</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocation.html#getErrorInfo--">getErrorInfo()</a></span> - 类 中的方法com.amap.api.location.<a href="../com/amap/api/location/AMapLocation.html" title="com.amap.api.location中的类">AMapLocation</a></dt>
<dd>
<div class="block">获取错误信息</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocation.html#getExtras--">getExtras()</a></span> - 类 中的方法com.amap.api.location.<a href="../com/amap/api/location/AMapLocation.html" title="com.amap.api.location中的类">AMapLocation</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/fence/GeoFence.html#getFenceId--">getFenceId()</a></span> - 类 中的方法com.amap.api.fence.<a href="../com/amap/api/fence/GeoFence.html" title="com.amap.api.fence中的类">GeoFence</a></dt>
<dd>
<div class="block">获取地理围栏的ID</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocation.html#getFloor--">getFloor()</a></span> - 类 中的方法com.amap.api.location.<a href="../com/amap/api/location/AMapLocation.html" title="com.amap.api.location中的类">AMapLocation</a></dt>
<dd>
<div class="block">获取室内定位的楼层信息</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocation.html#getGpsAccuracyStatus--">getGpsAccuracyStatus()</a></span> - 类 中的方法com.amap.api.location.<a href="../com/amap/api/location/AMapLocation.html" title="com.amap.api.location中的类">AMapLocation</a></dt>
<dd>
<div class="block">获取卫星信号强度，仅在卫星定位时有效,值为
 <code>#GPS_ACCURACY_BAD，#GPS_ACCURACY_GOOD，#GPS_ACCURACY_UNKNOWN</code></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocationClientOption.html#getGpsFirstTimeout--">getGpsFirstTimeout()</a></span> - 类 中的方法com.amap.api.location.<a href="../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a></dt>
<dd>
<div class="block">获取优先返回定位信息时等待GPS结果的超时时间</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocationQualityReport.html#getGPSSatellites--">getGPSSatellites()</a></span> - 类 中的方法com.amap.api.location.<a href="../com/amap/api/location/AMapLocationQualityReport.html" title="com.amap.api.location中的类">AMapLocationQualityReport</a></dt>
<dd>
<div class="block">获取当前的卫星数， 只有在非低功耗模式下此值才有效</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocationQualityReport.html#getGPSStatus--">getGPSStatus()</a></span> - 类 中的方法com.amap.api.location.<a href="../com/amap/api/location/AMapLocationQualityReport.html" title="com.amap.api.location中的类">AMapLocationQualityReport</a></dt>
<dd>
<div class="block">获取卫星状态信息，只有在非低功耗模式下此值才有效</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocationClientOption.html#getHttpTimeOut--">getHttpTimeOut()</a></span> - 类 中的方法com.amap.api.location.<a href="../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a></dt>
<dd>
<div class="block">获取联网超时时间<br>
 单位：毫秒<br>
 默认值：30000毫秒 <br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocationClientOption.html#getInterval--">getInterval()</a></span> - 类 中的方法com.amap.api.location.<a href="../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a></dt>
<dd>
<div class="block">获取发起定位请求的时间间隔 <br>
 默认值：2000毫秒</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocationClient.html#getLastKnownLocation--">getLastKnownLocation()</a></span> - 类 中的方法com.amap.api.location.<a href="../com/amap/api/location/AMapLocationClient.html" title="com.amap.api.location中的类">AMapLocationClient</a></dt>
<dd>
<div class="block">获取最后位置</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/fence/PoiItem.html#getLatitude--">getLatitude()</a></span> - 类 中的方法com.amap.api.fence.<a href="../com/amap/api/fence/PoiItem.html" title="com.amap.api.fence中的类">PoiItem</a></dt>
<dd>
<div class="block">获取POI的纬度</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocation.html#getLatitude--">getLatitude()</a></span> - 类 中的方法com.amap.api.location.<a href="../com/amap/api/location/AMapLocation.html" title="com.amap.api.location中的类">AMapLocation</a></dt>
<dd>
<div class="block">获取纬度</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/DPoint.html#getLatitude--">getLatitude()</a></span> - 类 中的方法com.amap.api.location.<a href="../com/amap/api/location/DPoint.html" title="com.amap.api.location中的类">DPoint</a></dt>
<dd>
<div class="block">获取坐标点的纬度</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocation.html#getLocationDetail--">getLocationDetail()</a></span> - 类 中的方法com.amap.api.location.<a href="../com/amap/api/location/AMapLocation.html" title="com.amap.api.location中的类">AMapLocation</a></dt>
<dd>
<div class="block">获取定位信息描述</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocationClientOption.html#getLocationMode--">getLocationMode()</a></span> - 类 中的方法com.amap.api.location.<a href="../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a></dt>
<dd>
<div class="block">获取定位模式 默认值：Hight_Accuracy 高精度模式<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocationClientOption.html#getLocationProtocol--">getLocationProtocol()</a></span> - 类 中的方法com.amap.api.location.<a href="../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a></dt>
<dd>
<div class="block">获取定位协议 默认值：HTTP http协议<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocationClientOption.html#getLocationPurpose--">getLocationPurpose()</a></span> - 类 中的方法com.amap.api.location.<a href="../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a></dt>
<dd>
<div class="block">获取定位场景</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocation.html#getLocationQualityReport--">getLocationQualityReport()</a></span> - 类 中的方法com.amap.api.location.<a href="../com/amap/api/location/AMapLocation.html" title="com.amap.api.location中的类">AMapLocation</a></dt>
<dd>
<div class="block">获取定位质量</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocation.html#getLocationType--">getLocationType()</a></span> - 类 中的方法com.amap.api.location.<a href="../com/amap/api/location/AMapLocation.html" title="com.amap.api.location中的类">AMapLocation</a></dt>
<dd>
<div class="block">获取定位结果来源</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/fence/PoiItem.html#getLongitude--">getLongitude()</a></span> - 类 中的方法com.amap.api.fence.<a href="../com/amap/api/fence/PoiItem.html" title="com.amap.api.fence中的类">PoiItem</a></dt>
<dd>
<div class="block">获取POI的经度</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocation.html#getLongitude--">getLongitude()</a></span> - 类 中的方法com.amap.api.location.<a href="../com/amap/api/location/AMapLocation.html" title="com.amap.api.location中的类">AMapLocation</a></dt>
<dd>
<div class="block">获取经度</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/DPoint.html#getLongitude--">getLongitude()</a></span> - 类 中的方法com.amap.api.location.<a href="../com/amap/api/location/DPoint.html" title="com.amap.api.location中的类">DPoint</a></dt>
<dd>
<div class="block">获取坐标点的经度</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocationQualityReport.html#getNetUseTime--">getNetUseTime()</a></span> - 类 中的方法com.amap.api.location.<a href="../com/amap/api/location/AMapLocationQualityReport.html" title="com.amap.api.location中的类">AMapLocationQualityReport</a></dt>
<dd>
<div class="block">获取网络定位时的网络耗时 单位：毫秒</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocationQualityReport.html#getNetworkType--">getNetworkType()</a></span> - 类 中的方法com.amap.api.location.<a href="../com/amap/api/location/AMapLocationQualityReport.html" title="com.amap.api.location中的类">AMapLocationQualityReport</a></dt>
<dd>
<div class="block">获取网络连接类型（2G、3G、4G、WIFI)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/fence/GeoFence.html#getPendingIntent--">getPendingIntent()</a></span> - 类 中的方法com.amap.api.fence.<a href="../com/amap/api/fence/GeoFence.html" title="com.amap.api.fence中的类">GeoFence</a></dt>
<dd>
<div class="block">获取对应的PendingIntent对象</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/fence/GeoFence.html#getPendingIntentAction--">getPendingIntentAction()</a></span> - 类 中的方法com.amap.api.fence.<a href="../com/amap/api/fence/GeoFence.html" title="com.amap.api.fence中的类">GeoFence</a></dt>
<dd>
<div class="block">获取对应的PendingIntent的action字符串</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/fence/PoiItem.html#getPoiId--">getPoiId()</a></span> - 类 中的方法com.amap.api.fence.<a href="../com/amap/api/fence/PoiItem.html" title="com.amap.api.fence中的类">PoiItem</a></dt>
<dd>
<div class="block">获取POI的ID</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/fence/GeoFence.html#getPoiItem--">getPoiItem()</a></span> - 类 中的方法com.amap.api.fence.<a href="../com/amap/api/fence/GeoFence.html" title="com.amap.api.fence中的类">GeoFence</a></dt>
<dd>
<div class="block">获取围栏内的兴趣点属性
 
 只有围栏类型为:<a href="../com/amap/api/fence/GeoFence.html#TYPE_AMAPPOI"><code>GeoFence.TYPE_AMAPPOI</code></a>时才有值
 </div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/fence/PoiItem.html#getPoiName--">getPoiName()</a></span> - 类 中的方法com.amap.api.fence.<a href="../com/amap/api/fence/PoiItem.html" title="com.amap.api.fence中的类">PoiItem</a></dt>
<dd>
<div class="block">获取POI的名称</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocation.html#getPoiName--">getPoiName()</a></span> - 类 中的方法com.amap.api.location.<a href="../com/amap/api/location/AMapLocation.html" title="com.amap.api.location中的类">AMapLocation</a></dt>
<dd>
<div class="block">获取兴趣点名称
 
 <b>2.9.0</b>之前的版本定位类型为<a href="../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS"><code>AMapLocation.LOCATION_TYPE_GPS</code></a>时不会返回兴趣点名称<br>
 自<b>2.9.0</b>版本开始，当<a href="../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS"><code>AMapLocation.LOCATION_TYPE_GPS</code></a>时也可以返回兴趣点名称
 </div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/fence/GeoFence.html#getPointList--">getPointList()</a></span> - 类 中的方法com.amap.api.fence.<a href="../com/amap/api/fence/GeoFence.html" title="com.amap.api.fence中的类">GeoFence</a></dt>
<dd>
<div class="block">获取围栏坐标点</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/fence/PoiItem.html#getPoiType--">getPoiType()</a></span> - 类 中的方法com.amap.api.fence.<a href="../com/amap/api/fence/PoiItem.html" title="com.amap.api.fence中的类">PoiItem</a></dt>
<dd>
<div class="block">获取POI的类型</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/fence/DistrictItem.html#getPolyline--">getPolyline()</a></span> - 类 中的方法com.amap.api.fence.<a href="../com/amap/api/fence/DistrictItem.html" title="com.amap.api.fence中的类">DistrictItem</a></dt>
<dd>
<div class="block">获取行政区划轮廓坐标点</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocation.html#getProvider--">getProvider()</a></span> - 类 中的方法com.amap.api.location.<a href="../com/amap/api/location/AMapLocation.html" title="com.amap.api.location中的类">AMapLocation</a></dt>
<dd>
<div class="block">获取定位提供者</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/fence/PoiItem.html#getProvince--">getProvince()</a></span> - 类 中的方法com.amap.api.fence.<a href="../com/amap/api/fence/PoiItem.html" title="com.amap.api.fence中的类">PoiItem</a></dt>
<dd>
<div class="block">获取POI所在的省份名称</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocation.html#getProvince--">getProvince()</a></span> - 类 中的方法com.amap.api.location.<a href="../com/amap/api/location/AMapLocation.html" title="com.amap.api.location中的类">AMapLocation</a></dt>
<dd>
<div class="block">获取省的名称
 
 <b>2.9.0</b>之前的版本定位类型为<a href="../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS"><code>AMapLocation.LOCATION_TYPE_GPS</code></a>时不会返回省份名称<br>
 自<b>2.9.0</b>版本开始，当<a href="../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS"><code>AMapLocation.LOCATION_TYPE_GPS</code></a>时也可以返回省份名称
 </div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocation.html#getRoad--">getRoad()</a></span> - 类 中的方法com.amap.api.location.<a href="../com/amap/api/location/AMapLocation.html" title="com.amap.api.location中的类">AMapLocation</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span>
<div class="block"><span class="deprecationComment">使用<a href="../com/amap/api/location/AMapLocation.html#getStreet--"><code>AMapLocation.getStreet()</code></a>方法代替</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocation.html#getSatellites--">getSatellites()</a></span> - 类 中的方法com.amap.api.location.<a href="../com/amap/api/location/AMapLocation.html" title="com.amap.api.location中的类">AMapLocation</a></dt>
<dd>
<div class="block">获取当前可用卫星数量, 仅在卫星定位时有效,</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocation.html#getSpeed--">getSpeed()</a></span> - 类 中的方法com.amap.api.location.<a href="../com/amap/api/location/AMapLocation.html" title="com.amap.api.location中的类">AMapLocation</a></dt>
<dd>
<div class="block">获取当前速度(单位：米/秒)
 
 默认值：0.0<br>
 3.1.0之前的版本只有定位类型为 <a href="../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS"><code>AMapLocation.LOCATION_TYPE_GPS</code></a>时才有值<br>
 自3.1.0版本开始，不限定定位类型，当定位类型不是<a href="../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS"><code>AMapLocation.LOCATION_TYPE_GPS</code></a>时，可以通过
 <a href="../com/amap/api/location/AMapLocationClientOption.html#setSensorEnable-boolean-"><code>AMapLocationClientOption.setSensorEnable(boolean)</code></a>
 控制是否返回速度值，当设置为true时会通过手机传感器获取速度,如果手机没有对应的传感器会返回0.0
 </div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/fence/GeoFence.html#getStatus--">getStatus()</a></span> - 类 中的方法com.amap.api.fence.<a href="../com/amap/api/fence/GeoFence.html" title="com.amap.api.fence中的类">GeoFence</a></dt>
<dd>
<div class="block">获取围栏的状态</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocation.html#getStreet--">getStreet()</a></span> - 类 中的方法com.amap.api.location.<a href="../com/amap/api/location/AMapLocation.html" title="com.amap.api.location中的类">AMapLocation</a></dt>
<dd>
<div class="block">获取街道名称
 
 <b>2.9.0</b>之前的版本定位类型为<a href="../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS"><code>AMapLocation.LOCATION_TYPE_GPS</code></a>时不会返回街道名称<br>
 自<b>2.9.0</b>版本开始，当<a href="../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS"><code>AMapLocation.LOCATION_TYPE_GPS</code></a>时也可以返回街道名称
 </div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocation.html#getStreetNum--">getStreetNum()</a></span> - 类 中的方法com.amap.api.location.<a href="../com/amap/api/location/AMapLocation.html" title="com.amap.api.location中的类">AMapLocation</a></dt>
<dd>
<div class="block">获取门牌号
 
 <b>2.9.0</b>之前的版本定位类型为<a href="../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS"><code>AMapLocation.LOCATION_TYPE_GPS</code></a>时不会返回门牌号<br>
 自<b>2.9.0</b>版本开始，当<a href="../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS"><code>AMapLocation.LOCATION_TYPE_GPS</code></a>时也可以返回门牌号
 </div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/fence/PoiItem.html#getTel--">getTel()</a></span> - 类 中的方法com.amap.api.fence.<a href="../com/amap/api/fence/PoiItem.html" title="com.amap.api.fence中的类">PoiItem</a></dt>
<dd>
<div class="block">获取POI的电话号码</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocation.html#getTrustedLevel--">getTrustedLevel()</a></span> - 类 中的方法com.amap.api.location.<a href="../com/amap/api/location/AMapLocation.html" title="com.amap.api.location中的类">AMapLocation</a></dt>
<dd>
<div class="block">获取定位结果的可信度
 
     只有在定位成功时才有意义
     非常可信 <a href="../com/amap/api/location/AMapLocation.html#TRUSTED_LEVEL_HIGH"><code>AMapLocation.TRUSTED_LEVEL_HIGH</code></a>
     可信度一般<a href="../com/amap/api/location/AMapLocation.html#TRUSTED_LEVEL_NORMAL"><code>AMapLocation.TRUSTED_LEVEL_NORMAL</code></a>
     可信度较低 <a href="../com/amap/api/location/AMapLocation.html#TRUSTED_LEVEL_LOW"><code>AMapLocation.TRUSTED_LEVEL_LOW</code></a>
     非常不可信 <a href="../com/amap/api/location/AMapLocation.html#TRUSTED_LEVEL_BAD"><code>AMapLocation.TRUSTED_LEVEL_BAD</code></a>
 </div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/fence/GeoFence.html#getType--">getType()</a></span> - 类 中的方法com.amap.api.fence.<a href="../com/amap/api/fence/GeoFence.html" title="com.amap.api.fence中的类">GeoFence</a></dt>
<dd>
<div class="block">获取围栏类型</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/fence/PoiItem.html#getTypeCode--">getTypeCode()</a></span> - 类 中的方法com.amap.api.fence.<a href="../com/amap/api/fence/PoiItem.html" title="com.amap.api.fence中的类">PoiItem</a></dt>
<dd>
<div class="block">获取POI的类型编码</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocationClientOption.AMapLocationProtocol.html#getValue--">getValue()</a></span> - 枚举 中的方法com.amap.api.location.<a href="../com/amap/api/location/AMapLocationClientOption.AMapLocationProtocol.html" title="com.amap.api.location中的枚举">AMapLocationClientOption.AMapLocationProtocol</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocationClient.html#getVersion--">getVersion()</a></span> - 类 中的方法com.amap.api.location.<a href="../com/amap/api/location/AMapLocationClient.html" title="com.amap.api.location中的类">AMapLocationClient</a></dt>
<dd>
<div class="block">获取定位sdk版本信息</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocation.html#GPS_ACCURACY_BAD">GPS_ACCURACY_BAD</a></span> - 类 中的静态变量com.amap.api.location.<a href="../com/amap/api/location/AMapLocation.html" title="com.amap.api.location中的类">AMapLocation</a></dt>
<dd>
<div class="block">卫星信号弱</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocation.html#GPS_ACCURACY_GOOD">GPS_ACCURACY_GOOD</a></span> - 类 中的静态变量com.amap.api.location.<a href="../com/amap/api/location/AMapLocation.html" title="com.amap.api.location中的类">AMapLocation</a></dt>
<dd>
<div class="block">卫星信号强</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocation.html#GPS_ACCURACY_UNKNOWN">GPS_ACCURACY_UNKNOWN</a></span> - 类 中的静态变量com.amap.api.location.<a href="../com/amap/api/location/AMapLocation.html" title="com.amap.api.location中的类">AMapLocation</a></dt>
<dd>
<div class="block">卫星状态未知</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocationQualityReport.html#GPS_STATUS_MODE_SAVING">GPS_STATUS_MODE_SAVING</a></span> - 类 中的静态变量com.amap.api.location.<a href="../com/amap/api/location/AMapLocationQualityReport.html" title="com.amap.api.location中的类">AMapLocationQualityReport</a></dt>
<dd>
<div class="block">卫星定位状态--选择的定位模式中不包含卫星定位
 
     Android 4.4以上的手机设置中开启了定位（位置）服务，但是选择的模式为省电模式，不包含卫星定位<br>
     建议选择包含gps定位的模式（例如：高精度、仅设备）
 </div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocationQualityReport.html#GPS_STATUS_NOGPSPERMISSION">GPS_STATUS_NOGPSPERMISSION</a></span> - 类 中的静态变量com.amap.api.location.<a href="../com/amap/api/location/AMapLocationQualityReport.html" title="com.amap.api.location中的类">AMapLocationQualityReport</a></dt>
<dd>
<div class="block">卫星定位状态--没有GPS定位权限
 
  如果没有GPS定位权限无法进行卫星定位, 建议在安全软件中授予GPS定位权限
 </div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocationQualityReport.html#GPS_STATUS_NOGPSPROVIDER">GPS_STATUS_NOGPSPROVIDER</a></span> - 类 中的静态变量com.amap.api.location.<a href="../com/amap/api/location/AMapLocationQualityReport.html" title="com.amap.api.location中的类">AMapLocationQualityReport</a></dt>
<dd>
<div class="block">卫星定位状态--手机中没有GPS Provider，无法进行卫星定位</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocationQualityReport.html#GPS_STATUS_OFF">GPS_STATUS_OFF</a></span> - 类 中的静态变量com.amap.api.location.<a href="../com/amap/api/location/AMapLocationQualityReport.html" title="com.amap.api.location中的类">AMapLocationQualityReport</a></dt>
<dd>
<div class="block">卫星定位状态--GPS开关关闭
 
     建议开启GPS开关，提高定位质量<br>
     Android 4.4以下的手机是gps开关关闭-建议开启gps开关<br>
     Android 4.4以上的手机设置中关闭了定位（位置）服务-建议开启定位服务，并选择包含gps的定位模式<br>

 </div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocationQualityReport.html#GPS_STATUS_OK">GPS_STATUS_OK</a></span> - 类 中的静态变量com.amap.api.location.<a href="../com/amap/api/location/AMapLocationQualityReport.html" title="com.amap.api.location中的类">AMapLocationQualityReport</a></dt>
<dd>
<div class="block">卫星定位状态--正常</div>
</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">I</a>&nbsp;<a href="index-9.html">L</a>&nbsp;<a href="index-10.html">O</a>&nbsp;<a href="index-11.html">P</a>&nbsp;<a href="index-12.html">R</a>&nbsp;<a href="index-13.html">S</a>&nbsp;<a href="index-14.html">T</a>&nbsp;<a href="index-15.html">U</a>&nbsp;<a href="index-16.html">V</a>&nbsp;<a href="index-17.html">W</a>&nbsp;</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-6.html">上一个字母</a></li>
<li><a href="index-8.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-7.html" target="_top">框架</a></li>
<li><a href="index-7.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
