<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>I - 索引</title>
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="I - \u7D22\u5F15";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-7.html">上一个字母</a></li>
<li><a href="index-9.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-8.html" target="_top">框架</a></li>
<li><a href="index-8.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="contentContainer"><a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">I</a>&nbsp;<a href="index-9.html">L</a>&nbsp;<a href="index-10.html">O</a>&nbsp;<a href="index-11.html">P</a>&nbsp;<a href="index-12.html">R</a>&nbsp;<a href="index-13.html">S</a>&nbsp;<a href="index-14.html">T</a>&nbsp;<a href="index-15.html">U</a>&nbsp;<a href="index-16.html">V</a>&nbsp;<a href="index-17.html">W</a>&nbsp;<a name="I:I">
<!--   -->
</a>
<h2 class="title">I</h2>
<dl>
<dt><span class="memberNameLink"><a href="../com/amap/api/fence/GeoFence.html#isAble--">isAble()</a></span> - 类 中的方法com.amap.api.fence.<a href="../com/amap/api/fence/GeoFence.html" title="com.amap.api.fence中的类">GeoFence</a></dt>
<dd>
<div class="block">获取围栏是否可用</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/CoordinateConverter.html#isAMapDataAvailable-double-double-">isAMapDataAvailable(double, double)</a></span> - 类 中的静态方法com.amap.api.location.<a href="../com/amap/api/location/CoordinateConverter.html" title="com.amap.api.location中的类">CoordinateConverter</a></dt>
<dd>
<div class="block">是否是高德地图可用数据</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocationClientOption.html#isBeidouFirst--">isBeidouFirst()</a></span> - 类 中的方法com.amap.api.location.<a href="../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocationClientOption.html#isGpsFirst--">isGpsFirst()</a></span> - 类 中的方法com.amap.api.location.<a href="../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a></dt>
<dd>
<div class="block">获取高精度模式下单次定位是否优先返回卫星定位信息<br>
 默认值：false <br>
 
 <b>只有在单次定位高精度定位模式下有效<br>
 为true时，会等待卫星定位结果返回，最多等待30秒，若30秒后仍无卫星定位结果返回，返回网络定位结果</b></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocationQualityReport.html#isInstalledHighDangerMockApp--">isInstalledHighDangerMockApp()</a></span> - 类 中的方法com.amap.api.location.<a href="../com/amap/api/location/AMapLocationQualityReport.html" title="com.amap.api.location中的类">AMapLocationQualityReport</a></dt>
<dd>
<div class="block">是否安装了高危位置模拟软件
 首次定位可能没有结果</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocationClientOption.html#isKillProcess--">isKillProcess()</a></span> - 类 中的方法com.amap.api.location.<a href="../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a></dt>
<dd>
<div class="block">获取退出时是否杀死进程<br>
 默认值:false, 不杀死 <br>
 
 <font color="red"><b>注意：如果设置为true，并且配置的service不是remote的则会杀死当前页面进程，请慎重使用
 </b> </font>
 </div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocationClientOption.html#isMockEnable--">isMockEnable()</a></span> - 类 中的方法com.amap.api.location.<a href="../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a></dt>
<dd>
<div class="block">获取是否允许模拟位置</br>
 
     从<b>3.4.0</b>开始，默认值为true，允许模拟;<br>
     <b>3.4.0</b>之前的版本，默认值为false，不允许模拟
 </div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocationClientOption.html#isNeedAddress--">isNeedAddress()</a></span> - 类 中的方法com.amap.api.location.<a href="../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a></dt>
<dd>
<div class="block">获取是否需要地址信息 <br>
 默认值：true 返回地址信息<br>
 <b>2.9.0</b>之前的版本定位类型为<a href="../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS"><code>AMapLocation.LOCATION_TYPE_GPS</code></a>时不会返回地址信息<br>
 自<b>2.9.0</b>版本开始，当类型为<a href="../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS"><code>AMapLocation.LOCATION_TYPE_GPS</code></a>时也可以返回地址信息(需要网络通畅，第一次有可能没有地址信息返回）</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocationClientOption.html#isOnceLocation--">isOnceLocation()</a></span> - 类 中的方法com.amap.api.location.<a href="../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a></dt>
<dd>
<div class="block">是否单次单次定位<br>
 默认值：false</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocationClientOption.html#isOpenAlwaysScanWifi--">isOpenAlwaysScanWifi()</a></span> - 类 中的静态方法com.amap.api.location.<a href="../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a></dt>
<dd>
<div class="block">是否开启始终wifi扫描
 
     只有设置了android.permission.WRITE_SECURE_SETTINGS权限后才会开启
     开启后，即使关闭wifi开关的情况下也会扫描wifi
     默认值为：true, 开启wifi始终扫描
 </div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/fence/GeoFenceClient.html#isPause--">isPause()</a></span> - 类 中的方法com.amap.api.fence.<a href="../com/amap/api/fence/GeoFenceClient.html" title="com.amap.api.fence中的类">GeoFenceClient</a></dt>
<dd>
<div class="block">地理围栏是否已经暂停</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocationClientOption.html#isSelfStartServiceEnable--">isSelfStartServiceEnable()</a></span> - 类 中的方法com.amap.api.location.<a href="../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocationClient.html#isStarted--">isStarted()</a></span> - 类 中的方法com.amap.api.location.<a href="../com/amap/api/location/AMapLocationClient.html" title="com.amap.api.location中的类">AMapLocationClient</a></dt>
<dd>
<div class="block">本地定位服务是否已经启动，用于用户检查服务是否已经启动</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocationQualityReport.html#isWifiAble--">isWifiAble()</a></span> - 类 中的方法com.amap.api.location.<a href="../com/amap/api/location/AMapLocationQualityReport.html" title="com.amap.api.location中的类">AMapLocationQualityReport</a></dt>
<dd>
<div class="block">wifi开关是否打开
 
     如果wifi关闭建议打开wifi开关，提高定位质量
 </div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocationClientOption.html#isWifiActiveScan--">isWifiActiveScan()</a></span> - 类 中的方法com.amap.api.location.<a href="../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocationClientOption.html#isWifiScan--">isWifiScan()</a></span> - 类 中的方法com.amap.api.location.<a href="../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a></dt>
<dd>
<div class="block">获取是否允许主动调用WIFI刷新</div>
</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">I</a>&nbsp;<a href="index-9.html">L</a>&nbsp;<a href="index-10.html">O</a>&nbsp;<a href="index-11.html">P</a>&nbsp;<a href="index-12.html">R</a>&nbsp;<a href="index-13.html">S</a>&nbsp;<a href="index-14.html">T</a>&nbsp;<a href="index-15.html">U</a>&nbsp;<a href="index-16.html">V</a>&nbsp;<a href="index-17.html">W</a>&nbsp;</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-7.html">上一个字母</a></li>
<li><a href="index-9.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-8.html" target="_top">框架</a></li>
<li><a href="index-8.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
