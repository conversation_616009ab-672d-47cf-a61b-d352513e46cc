<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>A - 索引</title>
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="A - \u7D22\u5F15";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个字母</li>
<li><a href="index-2.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-1.html" target="_top">框架</a></li>
<li><a href="index-1.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="contentContainer"><a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">I</a>&nbsp;<a href="index-9.html">L</a>&nbsp;<a href="index-10.html">O</a>&nbsp;<a href="index-11.html">P</a>&nbsp;<a href="index-12.html">R</a>&nbsp;<a href="index-13.html">S</a>&nbsp;<a href="index-14.html">T</a>&nbsp;<a href="index-15.html">U</a>&nbsp;<a href="index-16.html">V</a>&nbsp;<a href="index-17.html">W</a>&nbsp;<a name="I:A">
<!--   -->
</a>
<h2 class="title">A</h2>
<dl>
<dt><span class="memberNameLink"><a href="../com/amap/api/fence/GeoFenceClient.html#addGeoFence-com.amap.api.location.DPoint-float-java.lang.String-">addGeoFence(DPoint, float, String)</a></span> - 类 中的方法com.amap.api.fence.<a href="../com/amap/api/fence/GeoFenceClient.html" title="com.amap.api.fence中的类">GeoFenceClient</a></dt>
<dd>
<div class="block">创建自定义围栏
 
 圆形围栏
 </div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/fence/GeoFenceClient.html#addGeoFence-java.util.List-java.lang.String-">addGeoFence(List&lt;DPoint&gt;, String)</a></span> - 类 中的方法com.amap.api.fence.<a href="../com/amap/api/fence/GeoFenceClient.html" title="com.amap.api.fence中的类">GeoFenceClient</a></dt>
<dd>
<div class="block">创建自定义围栏
 
 多边形围栏
 </div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/fence/GeoFenceClient.html#addGeoFence-java.lang.String-java.lang.String-com.amap.api.location.DPoint-float-int-java.lang.String-">addGeoFence(String, String, DPoint, float, int, String)</a></span> - 类 中的方法com.amap.api.fence.<a href="../com/amap/api/fence/GeoFenceClient.html" title="com.amap.api.fence中的类">GeoFenceClient</a></dt>
<dd>
<div class="block">创建高德POI地理围栏
 
 根据周边创建围栏
 <br>
     <font color="red"><b>注意：</b>通过此方法创建的围栏半径全部为200米，暂时不支持自定位围栏半径</font>
 </div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/fence/GeoFenceClient.html#addGeoFence-java.lang.String-java.lang.String-java.lang.String-int-java.lang.String-">addGeoFence(String, String, String, int, String)</a></span> - 类 中的方法com.amap.api.fence.<a href="../com/amap/api/fence/GeoFenceClient.html" title="com.amap.api.fence中的类">GeoFenceClient</a></dt>
<dd>
<div class="block">创建高德POI地理围栏
 
 根据关键字创建围栏<br>
     <font color="red"><b>注意：</b>通过此方法创建的围栏半径全部为1000米，暂时不支持自定义围栏半径</font>
 </div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/fence/GeoFenceClient.html#addGeoFence-java.lang.String-java.lang.String-">addGeoFence(String, String)</a></span> - 类 中的方法com.amap.api.fence.<a href="../com/amap/api/fence/GeoFenceClient.html" title="com.amap.api.fence中的类">GeoFenceClient</a></dt>
<dd>
<div class="block">创建行政区划围栏</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/fence/GeoFence.html#ADDGEOFENCE_SUCCESS">ADDGEOFENCE_SUCCESS</a></span> - 类 中的静态变量com.amap.api.fence.<a href="../com/amap/api/fence/GeoFence.html" title="com.amap.api.fence中的类">GeoFence</a></dt>
<dd>
<div class="block">创建地理围栏成功</div>
</dd>
<dt><a href="../com/amap/api/location/AMapLocation.html" title="com.amap.api.location中的类"><span class="typeNameLink">AMapLocation</span></a> - <a href="../com/amap/api/location/package-summary.html">com.amap.api.location</a>中的类</dt>
<dd>
<div class="block">定位信息类。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocation.html#AMapLocation-java.lang.String-">AMapLocation(String)</a></span> - 类 的构造器com.amap.api.location.<a href="../com/amap/api/location/AMapLocation.html" title="com.amap.api.location中的类">AMapLocation</a></dt>
<dd>
<div class="block">根据定位提供者构造</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocation.html#AMapLocation-Location-">AMapLocation(Location)</a></span> - 类 的构造器com.amap.api.location.<a href="../com/amap/api/location/AMapLocation.html" title="com.amap.api.location中的类">AMapLocation</a></dt>
<dd>
<div class="block">根据android.location.Location对象构造</div>
</dd>
<dt><a href="../com/amap/api/location/AMapLocationClient.html" title="com.amap.api.location中的类"><span class="typeNameLink">AMapLocationClient</span></a> - <a href="../com/amap/api/location/package-summary.html">com.amap.api.location</a>中的类</dt>
<dd>
<div class="block">定位服务类。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocationClient.html#AMapLocationClient-Context-">AMapLocationClient(Context)</a></span> - 类 的构造器com.amap.api.location.<a href="../com/amap/api/location/AMapLocationClient.html" title="com.amap.api.location中的类">AMapLocationClient</a></dt>
<dd>
<div class="block">构造方法</div>
</dd>
<dt><a href="../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类"><span class="typeNameLink">AMapLocationClientOption</span></a> - <a href="../com/amap/api/location/package-summary.html">com.amap.api.location</a>中的类</dt>
<dd>
<div class="block">定位参数设置，通过这个类可以对定位的相关参数进行设置 <br>
 在<link>AMapLocationClient</link>进行定位时需要这些参数</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocationClientOption.html#AMapLocationClientOption--">AMapLocationClientOption()</a></span> - 类 的构造器com.amap.api.location.<a href="../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/amap/api/location/AMapLocationClientOption.AMapLocationMode.html" title="com.amap.api.location中的枚举"><span class="typeNameLink">AMapLocationClientOption.AMapLocationMode</span></a> - <a href="../com/amap/api/location/package-summary.html">com.amap.api.location</a>中的枚举</dt>
<dd>
<div class="block">定位模式，目前支持三种定位模式<br>
 
 
 高精度定位模式：
 
 在这种定位模式下，将同时使用高德网络定位和卫星定位,优先返回精度高的定位
 
 
 低功耗定位模式：
 
 在这种模式下，将只使用高德网络定位
 
 
 仅设备定位模式：
 
 在这种模式下，将只使用卫星定位。</div>
</dd>
<dt><a href="../com/amap/api/location/AMapLocationClientOption.AMapLocationProtocol.html" title="com.amap.api.location中的枚举"><span class="typeNameLink">AMapLocationClientOption.AMapLocationProtocol</span></a> - <a href="../com/amap/api/location/package-summary.html">com.amap.api.location</a>中的枚举</dt>
<dd>
<div class="block">定位协议，目前支持二种定位协议<br>
 
 
 http协议：
 
 在这种定位协议下，会使用http请求定位
 
 
 https协议：
 
 在这种定位协议下，会使用https请求定位</div>
</dd>
<dt><a href="../com/amap/api/location/AMapLocationClientOption.AMapLocationPurpose.html" title="com.amap.api.location中的枚举"><span class="typeNameLink">AMapLocationClientOption.AMapLocationPurpose</span></a> - <a href="../com/amap/api/location/package-summary.html">com.amap.api.location</a>中的枚举</dt>
<dd>
<div class="block">定位场景</div>
</dd>
<dt><a href="../com/amap/api/location/AMapLocationClientOption.GeoLanguage.html" title="com.amap.api.location中的枚举"><span class="typeNameLink">AMapLocationClientOption.GeoLanguage</span></a> - <a href="../com/amap/api/location/package-summary.html">com.amap.api.location</a>中的枚举</dt>
<dd>
<div class="block">设置返回逆地理使用的语言，目前有三种选择<br>
 
 
 默认：
 
 选择这种模式，会根据位置按照相应的语言返回逆地理信息，在国外按英语返回，在国内按中文返回 
 
 中文：
 
 设置只中文后，无论在国外还是国内都为返回中文的逆地理信息 
 
 英文：
 
 设置英文后，无论在国外还是国内都为返回英文的逆地理信息</div>
</dd>
<dt><a href="../com/amap/api/location/AMapLocationListener.html" title="com.amap.api.location中的接口"><span class="typeNameLink">AMapLocationListener</span></a> - <a href="../com/amap/api/location/package-summary.html">com.amap.api.location</a>中的接口</dt>
<dd>
<div class="block">定位回调接口</div>
</dd>
<dt><a href="../com/amap/api/location/AMapLocationQualityReport.html" title="com.amap.api.location中的类"><span class="typeNameLink">AMapLocationQualityReport</span></a> - <a href="../com/amap/api/location/package-summary.html">com.amap.api.location</a>中的类</dt>
<dd>
<div class="block">高德定位质量报告，随定位结果一起返回
 Created by hongming.wang on 2017/8/29.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocationQualityReport.html#AMapLocationQualityReport--">AMapLocationQualityReport()</a></span> - 类 的构造器com.amap.api.location.<a href="../com/amap/api/location/AMapLocationQualityReport.html" title="com.amap.api.location中的类">AMapLocationQualityReport</a></dt>
<dd>&nbsp;</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">I</a>&nbsp;<a href="index-9.html">L</a>&nbsp;<a href="index-10.html">O</a>&nbsp;<a href="index-11.html">P</a>&nbsp;<a href="index-12.html">R</a>&nbsp;<a href="index-13.html">S</a>&nbsp;<a href="index-14.html">T</a>&nbsp;<a href="index-15.html">U</a>&nbsp;<a href="index-16.html">V</a>&nbsp;<a href="index-17.html">W</a>&nbsp;</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个字母</li>
<li><a href="index-2.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-1.html" target="_top">框架</a></li>
<li><a href="index-1.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
