<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>T - 索引</title>
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="T - \u7D22\u5F15";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-13.html">上一个字母</a></li>
<li><a href="index-15.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-14.html" target="_top">框架</a></li>
<li><a href="index-14.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="contentContainer"><a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">I</a>&nbsp;<a href="index-9.html">L</a>&nbsp;<a href="index-10.html">O</a>&nbsp;<a href="index-11.html">P</a>&nbsp;<a href="index-12.html">R</a>&nbsp;<a href="index-13.html">S</a>&nbsp;<a href="index-14.html">T</a>&nbsp;<a href="index-15.html">U</a>&nbsp;<a href="index-16.html">V</a>&nbsp;<a href="index-17.html">W</a>&nbsp;<a name="I:T">
<!--   -->
</a>
<h2 class="title">T</h2>
<dl>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocation.html#toStr--">toStr()</a></span> - 类 中的方法com.amap.api.location.<a href="../com/amap/api/location/AMapLocation.html" title="com.amap.api.location中的类">AMapLocation</a></dt>
<dd>
<div class="block">将定位结果转换成字符串</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocation.html#toStr-int-">toStr(int)</a></span> - 类 中的方法com.amap.api.location.<a href="../com/amap/api/location/AMapLocation.html" title="com.amap.api.location中的类">AMapLocation</a></dt>
<dd>
<div class="block">将定位结果转化为字符串</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocation.html#toString--">toString()</a></span> - 类 中的方法com.amap.api.location.<a href="../com/amap/api/location/AMapLocation.html" title="com.amap.api.location中的类">AMapLocation</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocationClientOption.html#toString--">toString()</a></span> - 类 中的方法com.amap.api.location.<a href="../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocation.html#TRUSTED_LEVEL_BAD">TRUSTED_LEVEL_BAD</a></span> - 类 中的静态变量com.amap.api.location.<a href="../com/amap/api/location/AMapLocation.html" title="com.amap.api.location中的类">AMapLocation</a></dt>
<dd>
<div class="block">定位结果的可信度-非常不可信
 
     周边信息的新鲜度超过10分钟
     模拟定位结果
 </div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocation.html#TRUSTED_LEVEL_HIGH">TRUSTED_LEVEL_HIGH</a></span> - 类 中的静态变量com.amap.api.location.<a href="../com/amap/api/location/AMapLocation.html" title="com.amap.api.location中的类">AMapLocation</a></dt>
<dd>
<div class="block">定位结果的可信度-非常可信
 
     周边信息的新鲜度在15s之内
     实时GPS定位结果
 </div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocation.html#TRUSTED_LEVEL_LOW">TRUSTED_LEVEL_LOW</a></span> - 类 中的静态变量com.amap.api.location.<a href="../com/amap/api/location/AMapLocation.html" title="com.amap.api.location中的类">AMapLocation</a></dt>
<dd>
<div class="block">定位结果的可信度-可信度较低
 
     周边信息的新鲜度在2-10分钟之间
 </div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocation.html#TRUSTED_LEVEL_NORMAL">TRUSTED_LEVEL_NORMAL</a></span> - 类 中的静态变量com.amap.api.location.<a href="../com/amap/api/location/AMapLocation.html" title="com.amap.api.location中的类">AMapLocation</a></dt>
<dd>
<div class="block">定位结果的可信度-可信度一般
 
     周边信息的新鲜度在15秒-2分钟之间
     缓存、离线定位、最后位置
 </div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/fence/GeoFence.html#TYPE_AMAPPOI">TYPE_AMAPPOI</a></span> - 类 中的静态变量com.amap.api.fence.<a href="../com/amap/api/fence/GeoFence.html" title="com.amap.api.fence中的类">GeoFence</a></dt>
<dd>
<div class="block">高德POI围栏</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/fence/GeoFence.html#TYPE_DISTRICT">TYPE_DISTRICT</a></span> - 类 中的静态变量com.amap.api.fence.<a href="../com/amap/api/fence/GeoFence.html" title="com.amap.api.fence中的类">GeoFence</a></dt>
<dd>
<div class="block">高德行政区划围栏</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/fence/GeoFence.html#TYPE_POLYGON">TYPE_POLYGON</a></span> - 类 中的静态变量com.amap.api.fence.<a href="../com/amap/api/fence/GeoFence.html" title="com.amap.api.fence中的类">GeoFence</a></dt>
<dd>
<div class="block">自建的多边形围栏</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/fence/GeoFence.html#TYPE_ROUND">TYPE_ROUND</a></span> - 类 中的静态变量com.amap.api.fence.<a href="../com/amap/api/fence/GeoFence.html" title="com.amap.api.fence中的类">GeoFence</a></dt>
<dd>
<div class="block">自建的圆形围栏</div>
</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">I</a>&nbsp;<a href="index-9.html">L</a>&nbsp;<a href="index-10.html">O</a>&nbsp;<a href="index-11.html">P</a>&nbsp;<a href="index-12.html">R</a>&nbsp;<a href="index-13.html">S</a>&nbsp;<a href="index-14.html">T</a>&nbsp;<a href="index-15.html">U</a>&nbsp;<a href="index-16.html">V</a>&nbsp;<a href="index-17.html">W</a>&nbsp;</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-13.html">上一个字母</a></li>
<li><a href="index-15.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-14.html" target="_top">框架</a></li>
<li><a href="index-14.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
