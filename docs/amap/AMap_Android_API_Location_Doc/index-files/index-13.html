<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>S - 索引</title>
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="S - \u7D22\u5F15";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-12.html">上一个字母</a></li>
<li><a href="index-14.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-13.html" target="_top">框架</a></li>
<li><a href="index-13.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="contentContainer"><a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">I</a>&nbsp;<a href="index-9.html">L</a>&nbsp;<a href="index-10.html">O</a>&nbsp;<a href="index-11.html">P</a>&nbsp;<a href="index-12.html">R</a>&nbsp;<a href="index-13.html">S</a>&nbsp;<a href="index-14.html">T</a>&nbsp;<a href="index-15.html">U</a>&nbsp;<a href="index-16.html">V</a>&nbsp;<a href="index-17.html">W</a>&nbsp;<a name="I:S">
<!--   -->
</a>
<h2 class="title">S</h2>
<dl>
<dt><span class="memberNameLink"><a href="../com/amap/api/fence/GeoFence.html#setAble-boolean-">setAble(boolean)</a></span> - 类 中的方法com.amap.api.fence.<a href="../com/amap/api/fence/GeoFence.html" title="com.amap.api.fence中的类">GeoFence</a></dt>
<dd>
<div class="block">设置围栏是否可用</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/fence/GeoFenceClient.html#setActivateAction-int-">setActivateAction(int)</a></span> - 类 中的方法com.amap.api.fence.<a href="../com/amap/api/fence/GeoFenceClient.html" title="com.amap.api.fence中的类">GeoFenceClient</a></dt>
<dd>
<div class="block">设置触发地理围栏的条件
 
 <b>注意：重复设置不同的触发行为会重复触发围栏</b>
 </div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocationClient.html#setApiKey-java.lang.String-">setApiKey(String)</a></span> - 类 中的静态方法com.amap.api.location.<a href="../com/amap/api/location/AMapLocationClient.html" title="com.amap.api.location中的类">AMapLocationClient</a></dt>
<dd>
<div class="block">设置apikey <b>必须在AmapLocationClient实例化之前调用</b></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocationClientOption.html#setBeidouFirst-boolean-">setBeidouFirst(boolean)</a></span> - 类 中的方法com.amap.api.location.<a href="../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocationClientOption.html#setDeviceModeDistanceFilter-float-">setDeviceModeDistanceFilter(float)</a></span> - 类 中的方法com.amap.api.location.<a href="../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a></dt>
<dd>
<div class="block">设置仅设备模式/高精度模式的系统定位自动回调最少间隔距离值 <br>
 单位：米 <br>
 默认值：0米
 
     只有当定位模式为<a href="../com/amap/api/location/AMapLocationClientOption.AMapLocationMode.html#Device_Sensors"><code>AMapLocationClientOption.AMapLocationMode.Device_Sensors</code></a>（仅设备模式）或 <a href="../com/amap/api/location/AMapLocationClientOption.AMapLocationMode.html#Hight_Accuracy"><code>AMapLocationClientOption.AMapLocationMode.Hight_Accuracy</code></a>（高精度模式）有效，值小于0时无效
 </div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/fence/GeoFenceClient.html#setGeoFenceAble-java.lang.String-boolean-">setGeoFenceAble(String, boolean)</a></span> - 类 中的方法com.amap.api.fence.<a href="../com/amap/api/fence/GeoFenceClient.html" title="com.amap.api.fence中的类">GeoFenceClient</a></dt>
<dd>
<div class="block">设置围栏是否生效，</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/fence/GeoFenceClient.html#setGeoFenceListener-com.amap.api.fence.GeoFenceListener-">setGeoFenceListener(GeoFenceListener)</a></span> - 类 中的方法com.amap.api.fence.<a href="../com/amap/api/fence/GeoFenceClient.html" title="com.amap.api.fence中的类">GeoFenceClient</a></dt>
<dd>
<div class="block">设置地理围栏的回调接口</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocationClientOption.html#setGeoLanguage-com.amap.api.location.AMapLocationClientOption.GeoLanguage-">setGeoLanguage(AMapLocationClientOption.GeoLanguage)</a></span> - 类 中的方法com.amap.api.location.<a href="../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a></dt>
<dd>
<div class="block">设置逆地理信息的语言,目前之中中文和英文
 
     默认值：<a href="../com/amap/api/location/AMapLocationClientOption.GeoLanguage.html#DEFAULT"><code>AMapLocationClientOption.GeoLanguage.DEFAULT</code></a>
 </div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocationClientOption.html#setGpsFirst-boolean-">setGpsFirst(boolean)</a></span> - 类 中的方法com.amap.api.location.<a href="../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a></dt>
<dd>
<div class="block">设置首次定位是否等待卫星定位结果<br>
 默认值：false <br>
 <b>只有在单次定位高精度定位模式下有效</b><br>
 设置为true时，会等待卫星定位结果返回，最多等待30秒，若30秒后仍无卫星定位结果返回，返回网络定位结果<br>
 从<b>4.5.0</b>版本开始等待卫星定位结果返回的时间可以通过 <a href="../com/amap/api/location/AMapLocationClientOption.html#setGpsFirstTimeout-long-"><code>AMapLocationClientOption.setGpsFirstTimeout(long)</code></a>进行设置
 </div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocationClientOption.html#setGpsFirstTimeout-long-">setGpsFirstTimeout(long)</a></span> - 类 中的方法com.amap.api.location.<a href="../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a></dt>
<dd>
<div class="block">设置优先返回卫星定位信息时等待卫星定位结果的超时时间，单位：毫秒
 
     只有在<a href="../com/amap/api/location/AMapLocationClientOption.html#setGpsFirst-boolean-"><code>AMapLocationClientOption.setGpsFirst(boolean)</code></a>设置为true时才有效。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocationClientOption.html#setHttpTimeOut-long-">setHttpTimeOut(long)</a></span> - 类 中的方法com.amap.api.location.<a href="../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a></dt>
<dd>
<div class="block">设置联网超时时间<br>
 单位：毫秒<br>
 默认值：30000毫秒 <br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocationClientOption.html#setInterval-long-">setInterval(long)</a></span> - 类 中的方法com.amap.api.location.<a href="../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a></dt>
<dd>
<div class="block">设置发起定位请求的时间间隔<br>
 单位：毫秒<br>
 默认值：2000毫秒</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocationClientOption.html#setKillProcess-boolean-">setKillProcess(boolean)</a></span> - 类 中的方法com.amap.api.location.<a href="../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a></dt>
<dd>
<div class="block">设置退出时是否杀死进程<br>
 默认值:false, 不杀死 <br>
 
 <font color="red"><b>注意：如果设置为true，并且配置的service不是remote的则会杀死当前页面进程，请慎重使用
 </b> </font>
 </div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/DPoint.html#setLatitude-double-">setLatitude(double)</a></span> - 类 中的方法com.amap.api.location.<a href="../com/amap/api/location/DPoint.html" title="com.amap.api.location中的类">DPoint</a></dt>
<dd>
<div class="block">设置坐标点的纬度</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocationClientOption.html#setLocationCacheEnable-boolean-">setLocationCacheEnable(boolean)</a></span> - 类 中的方法com.amap.api.location.<a href="../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a></dt>
<dd>
<div class="block">设置是否使用缓存策略, 默认为true 使用缓存策略</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocationClient.html#setLocationListener-com.amap.api.location.AMapLocationListener-">setLocationListener(AMapLocationListener)</a></span> - 类 中的方法com.amap.api.location.<a href="../com/amap/api/location/AMapLocationClient.html" title="com.amap.api.location中的类">AMapLocationClient</a></dt>
<dd>
<div class="block">设置定位回调监听</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocationClientOption.html#setLocationMode-com.amap.api.location.AMapLocationClientOption.AMapLocationMode-">setLocationMode(AMapLocationClientOption.AMapLocationMode)</a></span> - 类 中的方法com.amap.api.location.<a href="../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a></dt>
<dd>
<div class="block">设置定位模式</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocationClient.html#setLocationOption-com.amap.api.location.AMapLocationClientOption-">setLocationOption(AMapLocationClientOption)</a></span> - 类 中的方法com.amap.api.location.<a href="../com/amap/api/location/AMapLocationClient.html" title="com.amap.api.location中的类">AMapLocationClient</a></dt>
<dd>
<div class="block">设置定位参数</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocationClientOption.html#setLocationProtocol-com.amap.api.location.AMapLocationClientOption.AMapLocationProtocol-">setLocationProtocol(AMapLocationClientOption.AMapLocationProtocol)</a></span> - 类 中的静态方法com.amap.api.location.<a href="../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a></dt>
<dd>
<div class="block">设置定位协议</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocationClientOption.html#setLocationPurpose-com.amap.api.location.AMapLocationClientOption.AMapLocationPurpose-">setLocationPurpose(AMapLocationClientOption.AMapLocationPurpose)</a></span> - 类 中的方法com.amap.api.location.<a href="../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a></dt>
<dd>
<div class="block">设置定位场景，根据场景快速修改option，不支持动态改变，修改后需要调用<a href="../com/amap/api/location/AMapLocationClient.html#startLocation--"><code>AMapLocationClient.startLocation()</code></a>使其生效<br>
 
     当不需要场景时，可以设置为NULL，
 
 <b>注意：
 <font color="red">不建议设置场景和自定义option混合使用</font>
 设置场景后，如果已经开始定位了，建议调用一次<a href="../com/amap/api/location/AMapLocationClient.html#stopLocation--"><code>AMapLocationClient.stopLocation()</code></a>,然后主动调用一次<a href="../com/amap/api/location/AMapLocationClient.html#startLocation--"><code>AMapLocationClient.startLocation()</code></a>以保证option正确生效
 当主动设置的option和场景中的option有冲突时，以后设置的为准，
 比如：签到场景中默认的为单次定位，当主动设置option为连续定位时，
 如果先设置的场景，后改变的option，这时如果不调用startLocation不会变为连续定位，如果调用了startLocation则会变为连续定位，
 如果先改变option，后设置场景为签到场景，则会变为单次定位</b></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/DPoint.html#setLongitude-double-">setLongitude(double)</a></span> - 类 中的方法com.amap.api.location.<a href="../com/amap/api/location/DPoint.html" title="com.amap.api.location中的类">DPoint</a></dt>
<dd>
<div class="block">设置坐标点的经度</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocationClientOption.html#setMockEnable-boolean-">setMockEnable(boolean)</a></span> - 类 中的方法com.amap.api.location.<a href="../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a></dt>
<dd>
<div class="block">设置是否允许模拟位置 <br>
 
     从<b>3.4.0</b>开始，默认值为true，允许模拟;<br>
     <b>3.4.0</b>之前的版本，默认值为false，不允许模拟
 </div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocationClientOption.html#setNeedAddress-boolean-">setNeedAddress(boolean)</a></span> - 类 中的方法com.amap.api.location.<a href="../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a></dt>
<dd>
<div class="block">设置是否返回地址信息，默认返回地址信息 <br>
 默认值：true, 返回地址信息
 
 <b>2.9.0</b>之前的版本定位类型为<a href="../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS"><code>AMapLocation.LOCATION_TYPE_GPS</code></a>时不会返回地址信息<br>
 自<b>2.9.0</b>版本开始，当类型为<a href="../com/amap/api/location/AMapLocation.html#LOCATION_TYPE_GPS"><code>AMapLocation.LOCATION_TYPE_GPS</code></a>时也可以返回地址信息(需要网络通畅，第一次有可能没有地址信息返回）
 </div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocationClientOption.html#setOnceLocation-boolean-">setOnceLocation(boolean)</a></span> - 类 中的方法com.amap.api.location.<a href="../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a></dt>
<dd>
<div class="block">设置是否单次定位<br>
 默认值：false</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocationClientOption.html#setOnceLocationLatest-boolean-">setOnceLocationLatest(boolean)</a></span> - 类 中的方法com.amap.api.location.<a href="../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a></dt>
<dd>
<div class="block">设置定位是否等待WIFI列表刷新
 定位精度会更高，但是定位速度会变慢1-3秒
 
     <b>从3.7.0版本开始，支持连续定位（连续定位时首次会等待刷新）</b>
     3.7.0之前的版本，仅适用于单次定位，当设置为true时，连续定位会自动变为单次定位,
           </div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocationClientOption.html#setOpenAlwaysScanWifi-boolean-">setOpenAlwaysScanWifi(boolean)</a></span> - 类 中的静态方法com.amap.api.location.<a href="../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a></dt>
<dd>
<div class="block">设置是否开启wifi始终扫描
 
     只有设置了android.permission.WRITE_SECURE_SETTINGS权限后才会开启
     开启后，即使关闭wifi开关的情况下也会扫描wifi
     默认值为：true, 开启wifi始终扫描
     此方法为静态方法，设置一次后其他定位Client也会生效
 </div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocationClientOption.html#setSelfStartServiceEnable-boolean-">setSelfStartServiceEnable(boolean)</a></span> - 类 中的方法com.amap.api.location.<a href="../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a></dt>
<dd>
<div class="block">设置是否允许定位服务自启动，用于连续定位场景下定位服务被系统异常杀死时重新启动。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocationClientOption.html#setSensorEnable-boolean-">setSensorEnable(boolean)</a></span> - 类 中的方法com.amap.api.location.<a href="../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a></dt>
<dd>
<div class="block">设置是否使用设备传感器
 
     默认值：false 不使用设备传感器
 </div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocationClientOption.html#setWifiActiveScan-boolean-">setWifiActiveScan(boolean)</a></span> - 类 中的方法com.amap.api.location.<a href="../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocationClientOption.html#setWifiScan-boolean-">setWifiScan(boolean)</a></span> - 类 中的方法com.amap.api.location.<a href="../com/amap/api/location/AMapLocationClientOption.html" title="com.amap.api.location中的类">AMapLocationClientOption</a></dt>
<dd>
<div class="block">设置是否允许调用WIFI刷新
 
 默认值为true，当设置为false时会停止主动调用WIFI刷新，将会极大程度影响定位精度，但可以有效的降低定位耗电<br>
 </div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocationClient.html#startAssistantLocation-WebView-">startAssistantLocation(WebView)</a></span> - 类 中的方法com.amap.api.location.<a href="../com/amap/api/location/AMapLocationClient.html" title="com.amap.api.location中的类">AMapLocationClient</a></dt>
<dd>
<div class="block">启动H5辅助定位
 
     只适用于Android 4.2及以后版本
     该接口只用于配合Web JS API的H5辅助定位，开启后并没有开始定位，开始定位由JS API触发。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocationClient.html#startLocation--">startLocation()</a></span> - 类 中的方法com.amap.api.location.<a href="../com/amap/api/location/AMapLocationClient.html" title="com.amap.api.location中的类">AMapLocationClient</a></dt>
<dd>
<div class="block">开始定位</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/fence/GeoFence.html#STATUS_IN">STATUS_IN</a></span> - 类 中的静态变量com.amap.api.fence.<a href="../com/amap/api/fence/GeoFence.html" title="com.amap.api.fence中的类">GeoFence</a></dt>
<dd>
<div class="block">围栏状态-进入围栏</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/fence/GeoFence.html#STATUS_LOCFAIL">STATUS_LOCFAIL</a></span> - 类 中的静态变量com.amap.api.fence.<a href="../com/amap/api/fence/GeoFence.html" title="com.amap.api.fence中的类">GeoFence</a></dt>
<dd>
<div class="block">围栏状态-定位失败(定位失败时，围栏状态无法进行检测)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/fence/GeoFence.html#STATUS_OUT">STATUS_OUT</a></span> - 类 中的静态变量com.amap.api.fence.<a href="../com/amap/api/fence/GeoFence.html" title="com.amap.api.fence中的类">GeoFence</a></dt>
<dd>
<div class="block">围栏状态-离开围栏</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/fence/GeoFence.html#STATUS_STAYED">STATUS_STAYED</a></span> - 类 中的静态变量com.amap.api.fence.<a href="../com/amap/api/fence/GeoFence.html" title="com.amap.api.fence中的类">GeoFence</a></dt>
<dd>
<div class="block">围栏状态-在围栏内停留</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/fence/GeoFence.html#STATUS_UNKNOWN">STATUS_UNKNOWN</a></span> - 类 中的静态变量com.amap.api.fence.<a href="../com/amap/api/fence/GeoFence.html" title="com.amap.api.fence中的类">GeoFence</a></dt>
<dd>
<div class="block">围栏状态-初始状态，表示从未触发过围栏</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocationClient.html#stopAssistantLocation--">stopAssistantLocation()</a></span> - 类 中的方法com.amap.api.location.<a href="../com/amap/api/location/AMapLocationClient.html" title="com.amap.api.location中的类">AMapLocationClient</a></dt>
<dd>
<div class="block">停止辅助定位
 
     如果已经调用了startAssistantLocation接口，在destroy时请调用该接口
 </div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/location/AMapLocationClient.html#stopLocation--">stopLocation()</a></span> - 类 中的方法com.amap.api.location.<a href="../com/amap/api/location/AMapLocationClient.html" title="com.amap.api.location中的类">AMapLocationClient</a></dt>
<dd>
<div class="block">停止定位</div>
</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">I</a>&nbsp;<a href="index-9.html">L</a>&nbsp;<a href="index-10.html">O</a>&nbsp;<a href="index-11.html">P</a>&nbsp;<a href="index-12.html">R</a>&nbsp;<a href="index-13.html">S</a>&nbsp;<a href="index-14.html">T</a>&nbsp;<a href="index-15.html">U</a>&nbsp;<a href="index-16.html">V</a>&nbsp;<a href="index-17.html">W</a>&nbsp;</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-12.html">上一个字母</a></li>
<li><a href="index-14.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-13.html" target="_top">框架</a></li>
<li><a href="index-13.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
