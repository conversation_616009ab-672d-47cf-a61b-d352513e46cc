package com.amap.maps.jsmap.demo;



import android.app.Activity;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.view.View.OnClickListener;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.CompoundButton;

import com.amap.api.maps.AMap;
import com.amap.api.maps.AMapWrapper;
import com.amap.api.maps.MapsInitializer;
import com.amap.maps.jsmap.demo.webview.MAWebViewWrapper;
import com.amap.maps.jsmap.demo.webview.MyWebView;

/**
 * AMapV2地图中介绍如何显示一个基本地图
 */
public class BasicMapActivity extends Activity implements OnClickListener{
    private MyWebView webView;
    private AMapWrapper aMapWrapper;
    private AMap aMap;
    private Button basicmap;
    private Button rsmap;
    private Button nightmap;

    private String TAG = "mapcore";
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.basicmap_activity);
        webView = findViewById(R.id.map);
        init();
        MAWebViewWrapper webViewWrapper = new MAWebViewWrapper(webView);
        aMapWrapper = new AMapWrapper(this, webViewWrapper);
        aMapWrapper.onCreate();

        aMapWrapper.getMapAsyn(new AMap.OnMapReadyListener() {
            @Override
            public void onMapReady(AMap map) {
                aMap = map;
            }
        });
    }


    /**
     * 初始化AMap对象
     */
    private void init() {
        basicmap = (Button)findViewById(R.id.basicmap);
        basicmap.setOnClickListener(this);
        rsmap = (Button)findViewById(R.id.rsmap);
        rsmap.setOnClickListener(this);
        nightmap = (Button)findViewById(R.id.nightmap);
        nightmap.setOnClickListener(this);

        CheckBox trafficCheckbox = (CheckBox) findViewById(R.id.check_traffic);
        trafficCheckbox.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                if (aMap != null) {
                    aMap.setTrafficEnabled(isChecked);
                }
            }
        });

        CheckBox mapconCheckbox = (CheckBox) findViewById(R.id.check_mapcon);
        mapconCheckbox.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                if (aMap != null) {
                    aMap.showMapText(isChecked);
                    aMap.showBuildings(isChecked);
                }
            }
        });

    }

    /**
     * 方法必须重写
     */
    @Override
    protected void onResume() {
        super.onResume();
        aMapWrapper.onResume();
    }

    /**
     * 方法必须重写
     */
    @Override
    protected void onPause() {
        super.onPause();
        aMapWrapper.onPause();
    }

    /**
     * 方法必须重写
     */
    @Override
    protected void onSaveInstanceState(Bundle outState) {
        super.onSaveInstanceState(outState);
        aMapWrapper.onSaveInstanceState(outState);
    }

    /**
     * 方法必须重写
     */
    @Override
    protected void onDestroy() {
        super.onDestroy();
        aMapWrapper.onDestroy();
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.basicmap:
                aMap.setMapType(AMap.MAP_TYPE_NORMAL);// 矢量地图模式
                break;
            case R.id.rsmap:
                aMap.setMapType(AMap.MAP_TYPE_SATELLITE);// 卫星地图模式
                break;
            case R.id.nightmap:
                aMap.setMapType(AMap.MAP_TYPE_NIGHT);//夜景地图模式
                break;
            default:break;
        }
        Log.e("mapcore","maptype " + (aMap.getMapType()));

    }

}
