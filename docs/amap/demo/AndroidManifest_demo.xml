<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.amap.maps.jsmap.demo">

    <uses-permission android:name="android.permission.INTERNET" />

    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"/>

    <application
        android:allowBackup="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        >

        <meta-data
            android:name="com.amap.api.v2.apikey"
            android:value="您的key"/>

        <activity android:name=".MainActivity">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <activity android:name=".BasicMapActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
            ></activity>
        <activity android:name=".MarkerActivity"></activity>
        <activity android:name=".PolylineActivity"></activity>
        <activity android:name=".UiSettingsActivity"></activity>
        <activity android:name=".LocationModeSourceActivity_OutLocation"></activity>
        <activity android:name=".SmoothMoveActivity"></activity>
        <activity android:name=".webview.WebviewMapActivity"></activity>
        <activity android:name=".webview.WebviewTestActivity"></activity>

        <service android:name="com.amap.api.location.APSService"></service>
    </application>

</manifest>