<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>已过时的列表</title>
<link rel="stylesheet" type="text/css" href="stylesheet.css" title="Style">
<script type="text/javascript" src="script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="\u5DF2\u8FC7\u65F6\u7684\u5217\u8868";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="overview-tree.html">树</a></li>
<li class="navBarCell1Rev">已过时</li>
<li><a href="index-files/index-1.html">索引</a></li>
<li><a href="help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个</li>
<li>下一个</li>
</ul>
<ul class="navList">
<li><a href="index.html?deprecated-list.html" target="_top">框架</a></li>
<li><a href="deprecated-list.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 title="已过时的 API" class="title">已过时的 API</h1>
<h2 title="目录">目录</h2>
<ul>
<li><a href="#interface">已过时的接口</a></li>
<li><a href="#class">已过时的类</a></li>
<li><a href="#field">已过时的字段</a></li>
<li><a href="#method">已过时的方法</a></li>
<li><a href="#constructor">已过时的构造器</a></li>
</ul>
</div>
<div class="contentContainer"><a name="interface">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<table class="deprecatedSummary" border="0" cellpadding="3" cellspacing="0" summary="已过时的接口表, 列表已过时的接口和解释">
<caption><span>已过时的接口</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">接口和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="com/amap/api/maps/AMap.onMapPrintScreenListener.html" title="com.amap.api.maps中的接口">com.amap.api.maps.AMap.onMapPrintScreenListener</a>
<div class="block"><span class="deprecationComment">建议使用 <a href="com/amap/api/maps/AMap.OnMapScreenShotListener.html" title="com.amap.api.maps中的接口"><code>AMap.OnMapScreenShotListener</code></a></span></div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
<a name="class">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<table class="deprecatedSummary" border="0" cellpadding="3" cellspacing="0" summary="已过时的类表, 列表已过时的类和解释">
<caption><span>已过时的类</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">类和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="com/amap/api/maps/utils/overlay/SmoothMoveMarker.html" title="com.amap.api.maps.utils.overlay中的类">com.amap.api.maps.utils.overlay.SmoothMoveMarker</a>
<div class="block"><span class="deprecationComment">建议使用 <a href="com/amap/api/maps/utils/overlay/MovingPointOverlay.html" title="com.amap.api.maps.utils.overlay中的类"><code>MovingPointOverlay</code></a> 替换</span></div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
<a name="field">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<table class="deprecatedSummary" border="0" cellpadding="3" cellspacing="0" summary="已过时的字段表, 列表已过时的字段和解释">
<caption><span>已过时的字段</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">字段和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="com/amap/api/maps/model/NaviPara.html#DRIVING_AVOID_CONGESTION">com.amap.api.maps.model.NaviPara.DRIVING_AVOID_CONGESTION</a></td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="com/amap/api/maps/model/NaviPara.html#DRIVING_DEFAULT">com.amap.api.maps.model.NaviPara.DRIVING_DEFAULT</a></td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="com/amap/api/maps/model/NaviPara.html#DRIVING_NO_HIGHWAY">com.amap.api.maps.model.NaviPara.DRIVING_NO_HIGHWAY</a></td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="com/amap/api/maps/model/NaviPara.html#DRIVING_NO_HIGHWAY_AVOID_CONGESTION">com.amap.api.maps.model.NaviPara.DRIVING_NO_HIGHWAY_AVOID_CONGESTION</a></td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="com/amap/api/maps/model/NaviPara.html#DRIVING_NO_HIGHWAY_AVOID_SHORT_MONEY">com.amap.api.maps.model.NaviPara.DRIVING_NO_HIGHWAY_AVOID_SHORT_MONEY</a></td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="com/amap/api/maps/model/NaviPara.html#DRIVING_NO_HIGHWAY_SAVE_MONEY_AVOID_CONGESTION">com.amap.api.maps.model.NaviPara.DRIVING_NO_HIGHWAY_SAVE_MONEY_AVOID_CONGESTION</a></td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="com/amap/api/maps/model/NaviPara.html#DRIVING_SAVE_MONEY">com.amap.api.maps.model.NaviPara.DRIVING_SAVE_MONEY</a></td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="com/amap/api/maps/model/NaviPara.html#DRIVING_SAVE_MONEY_AVOID_CONGESTION">com.amap.api.maps.model.NaviPara.DRIVING_SAVE_MONEY_AVOID_CONGESTION</a></td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="com/amap/api/maps/model/NaviPara.html#DRIVING_SHORT_DISTANCE">com.amap.api.maps.model.NaviPara.DRIVING_SHORT_DISTANCE</a></td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="com/amap/api/maps/AMap.html#LOCATION_TYPE_LOCATE">com.amap.api.maps.AMap.LOCATION_TYPE_LOCATE</a>
<div class="block"><span class="deprecationComment">5.0.0 之后请参考 <a href="com/amap/api/maps/model/MyLocationStyle.html#LOCATION_TYPE_LOCATE"><code>MyLocationStyle.LOCATION_TYPE_LOCATE</code></a></span></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="com/amap/api/maps/AMap.html#LOCATION_TYPE_MAP_FOLLOW">com.amap.api.maps.AMap.LOCATION_TYPE_MAP_FOLLOW</a>
<div class="block"><span class="deprecationComment">5.0.0 之后请参考 <a href="com/amap/api/maps/model/MyLocationStyle.html#LOCATION_TYPE_FOLLOW"><code>MyLocationStyle.LOCATION_TYPE_FOLLOW</code></a></span></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="com/amap/api/maps/AMap.html#LOCATION_TYPE_MAP_ROTATE">com.amap.api.maps.AMap.LOCATION_TYPE_MAP_ROTATE</a>
<div class="block"><span class="deprecationComment">5.0.0 之后请参考 <a href="com/amap/api/maps/model/MyLocationStyle.html#LOCATION_TYPE_MAP_ROTATE"><code>MyLocationStyle.LOCATION_TYPE_MAP_ROTATE</code></a></span></div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
<a name="method">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<table class="deprecatedSummary" border="0" cellpadding="3" cellspacing="0" summary="已过时的方法表, 列表已过时的方法和解释">
<caption><span>已过时的方法</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">方法和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="com/amap/api/maps/AMap.html#getMapPrintScreen-com.amap.api.maps.AMap.onMapPrintScreenListener-">com.amap.api.maps.AMap.getMapPrintScreen(AMap.onMapPrintScreenListener)</a>
<div class="block"><span class="deprecationComment">建议使用<a href="com/amap/api/maps/AMap.html#getMapScreenShot-com.amap.api.maps.AMap.OnMapScreenShotListener-"><code>AMap.getMapScreenShot(OnMapScreenShotListener)</code></a></span></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="com/amap/api/maps/AMap.html#getMyTrafficStyle--">com.amap.api.maps.AMap.getMyTrafficStyle()</a>
<div class="block"><span class="deprecationComment">自7.8.0之后不再支持</span></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="com/amap/api/maps/MapsInitializer.html#getNetWorkEnable--">com.amap.api.maps.MapsInitializer.getNetWorkEnable()</a>
<div class="block"><span class="deprecationComment">5.0.0开始废弃</span></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="com/amap/api/maps/model/NavigateArrow.html#getSideColor--">com.amap.api.maps.model.NavigateArrow.getSideColor()</a></td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="com/amap/api/maps/AMap.html#getVersion--">com.amap.api.maps.AMap.getVersion()</a></td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="com/amap/api/maps/model/Marker.html#isPerspective--">com.amap.api.maps.model.Marker.isPerspective()</a>
<div class="block"><span class="deprecationComment">已取消这个效果</span></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="com/amap/api/maps/model/MarkerOptions.html#isPerspective--">com.amap.api.maps.model.MarkerOptions.isPerspective()</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="com/amap/api/maps/model/MarkerOptions.html#perspective-boolean-">com.amap.api.maps.model.MarkerOptions.perspective(boolean)</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="com/amap/api/maps/AMap.html#setCustomMapStyleID-java.lang.String-">com.amap.api.maps.AMap.setCustomMapStyleID(String)</a>
<div class="block"><span class="deprecationComment">该方法已无效，建议到官网lbs.amap.com更新样式文件并使用 <a href="com/amap/api/maps/AMap.html#setCustomMapStyle-com.amap.api.maps.model.CustomMapStyleOptions-"><code>AMap.setCustomMapStyle(CustomMapStyleOptions)</code></a> )} 替换</span></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="com/amap/api/maps/AMap.html#setCustomMapStylePath-java.lang.String-">com.amap.api.maps.AMap.setCustomMapStylePath(String)</a>
<div class="block"><span class="deprecationComment">该方法已无效，建议到官网lbs.amap.com更新样式文件并使用 <a href="com/amap/api/maps/AMap.html#setCustomMapStyle-com.amap.api.maps.model.CustomMapStyleOptions-"><code>AMap.setCustomMapStyle(CustomMapStyleOptions)</code></a> )} 替换</span></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="com/amap/api/maps/AMap.html#setMapCustomEnable-boolean-">com.amap.api.maps.AMap.setMapCustomEnable(boolean)</a>
<div class="block"><span class="deprecationComment">该方法已无效，建议到官网lbs.amap.com更新样式文件并使用 <a href="com/amap/api/maps/AMap.html#setCustomMapStyle-com.amap.api.maps.model.CustomMapStyleOptions-"><code>AMap.setCustomMapStyle(CustomMapStyleOptions)</code></a> )} 替换</span></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="com/amap/api/maps/AMap.html#setMyLocationRotateAngle-float-">com.amap.api.maps.AMap.setMyLocationRotateAngle(float)</a>
<div class="block"><span class="deprecationComment">自5.0.0 废弃</span></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="com/amap/api/maps/AMap.html#setMyLocationType-int-">com.amap.api.maps.AMap.setMyLocationType(int)</a>
<div class="block"><span class="deprecationComment">5.0.0 之后请使用 <a href="com/amap/api/maps/AMap.html#setMyLocationStyle-com.amap.api.maps.model.MyLocationStyle-"><code>AMap.setMyLocationStyle(MyLocationStyle)</code></a> 代替</span></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="com/amap/api/maps/AMap.html#setMyTrafficStyle-com.amap.api.maps.model.MyTrafficStyle-">com.amap.api.maps.AMap.setMyTrafficStyle(MyTrafficStyle)</a>
<div class="block"><span class="deprecationComment">自7.8.0之后不再支持</span></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="com/amap/api/maps/MapsInitializer.html#setNetWorkEnable-boolean-">com.amap.api.maps.MapsInitializer.setNetWorkEnable(boolean)</a>
<div class="block"><span class="deprecationComment">5.0.0开始废弃</span></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="com/amap/api/maps/model/Marker.html#setPerspective-boolean-">com.amap.api.maps.model.Marker.setPerspective(boolean)</a>
<div class="block"><span class="deprecationComment">已取消这个效果</span></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="com/amap/api/maps/model/BuildingOverlayOptions.html#setZIndex-float-">com.amap.api.maps.model.BuildingOverlayOptions.setZIndex(float)</a>
<div class="block"><span class="deprecationComment">该设置废弃，请使用<a href="com/amap/api/maps/model/BuildingOverlay.html#setZIndex-float-"><code>BuildingOverlay.setZIndex(float)</code></a></span></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="com/amap/api/maps/Projection.html#toMapLocation-com.amap.api.maps.model.LatLng-">com.amap.api.maps.Projection.toMapLocation(LatLng)</a></td>
</tr>
</tbody>
</table>
</li>
</ul>
<a name="constructor">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<table class="deprecatedSummary" border="0" cellpadding="3" cellspacing="0" summary="已过时的构造器表, 列表已过时的构造器和解释">
<caption><span>已过时的构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="com/amap/api/trace/LBSTraceClient.html#LBSTraceClient-android.content.Context-">com.amap.api.trace.LBSTraceClient(Context)</a>
<div class="block"><span class="deprecationComment">使用 {<a href="com/amap/api/trace/LBSTraceClient.html#getInstance-android.content.Context-"><code>LBSTraceClient.getInstance(Context)</code></a>}</span></div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="overview-tree.html">树</a></li>
<li class="navBarCell1Rev">已过时</li>
<li><a href="index-files/index-1.html">索引</a></li>
<li><a href="help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个</li>
<li>下一个</li>
</ul>
<ul class="navList">
<li><a href="index.html?deprecated-list.html" target="_top">框架</a></li>
<li><a href="deprecated-list.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
