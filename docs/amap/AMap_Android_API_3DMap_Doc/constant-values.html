<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>常量字段值</title>
<link rel="stylesheet" type="text/css" href="stylesheet.css" title="Style">
<script type="text/javascript" src="script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="\u5E38\u91CF\u5B57\u6BB5\u503C";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="overview-tree.html">树</a></li>
<li><a href="deprecated-list.html">已过时</a></li>
<li><a href="index-files/index-1.html">索引</a></li>
<li><a href="help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个</li>
<li>下一个</li>
</ul>
<ul class="navList">
<li><a href="index.html?constant-values.html" target="_top">框架</a></li>
<li><a href="constant-values.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 title="常量字段值" class="title">常量字段值</h1>
<h2 title="目录">目录</h2>
<ul>
<li><a href="#com.amap">com.amap.*</a></li>
</ul>
</div>
<div class="constantValuesContainer"><a name="com.amap">
<!--   -->
</a>
<h2 title="com.amap">com.amap.*</h2>
<ul class="blockList">
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="常量字段值表, 列表常量字段和值">
<caption><span>com.amap.api.maps.<a href="com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th scope="col">常量字段</th>
<th class="colLast" scope="col">值</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.maps.AMap.LOCATION_TYPE_LOCATE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/AMap.html#LOCATION_TYPE_LOCATE">LOCATION_TYPE_LOCATE</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.maps.AMap.LOCATION_TYPE_MAP_FOLLOW">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/AMap.html#LOCATION_TYPE_MAP_FOLLOW">LOCATION_TYPE_MAP_FOLLOW</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.maps.AMap.LOCATION_TYPE_MAP_ROTATE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/AMap.html#LOCATION_TYPE_MAP_ROTATE">LOCATION_TYPE_MAP_ROTATE</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.maps.AMap.MAP_TYPE_BUS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/AMap.html#MAP_TYPE_BUS">MAP_TYPE_BUS</a></code></td>
<td class="colLast"><code>5</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.maps.AMap.MAP_TYPE_NAVI">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/AMap.html#MAP_TYPE_NAVI">MAP_TYPE_NAVI</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.maps.AMap.MAP_TYPE_NIGHT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/AMap.html#MAP_TYPE_NIGHT">MAP_TYPE_NIGHT</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.maps.AMap.MAP_TYPE_NORMAL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/AMap.html#MAP_TYPE_NORMAL">MAP_TYPE_NORMAL</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.maps.AMap.MAP_TYPE_SATELLITE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/AMap.html#MAP_TYPE_SATELLITE">MAP_TYPE_SATELLITE</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="常量字段值表, 列表常量字段和值">
<caption><span>com.amap.api.maps.<a href="com/amap/api/maps/AMapException.html" title="com.amap.api.maps中的类">AMapException</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th scope="col">常量字段</th>
<th class="colLast" scope="col">值</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.maps.AMapException.AMAP_NOT_SUPPORT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/maps/AMapException.html#AMAP_NOT_SUPPORT">AMAP_NOT_SUPPORT</a></code></td>
<td class="colLast"><code>"\u79fb\u52a8\u8bbe\u5907\u4e0a\u672a\u5b89\u88c5\u9ad8\u5fb7\u5730\u56fe\u6216\u9ad8\u5fb7\u5730\u56fe\u7248\u672c\u8f83\u65e7"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.maps.AMapException.ERROR_CONNECTION">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/maps/AMapException.html#ERROR_CONNECTION">ERROR_CONNECTION</a></code></td>
<td class="colLast"><code>"http\u8fde\u63a5\u5931\u8d25 - ConnectionException"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.maps.AMapException.ERROR_FAILURE_AUTH">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/maps/AMapException.html#ERROR_FAILURE_AUTH">ERROR_FAILURE_AUTH</a></code></td>
<td class="colLast"><code>"key\u9274\u6743\u5931\u8d25"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.maps.AMapException.ERROR_FAILURE_OVERSEA_AUTH">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/maps/AMapException.html#ERROR_FAILURE_OVERSEA_AUTH">ERROR_FAILURE_OVERSEA_AUTH</a></code></td>
<td class="colLast"><code>"\u6d77\u5916\u9274\u6743\u5931\u8d25"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.maps.AMapException.ERROR_ILLEGAL_VALUE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/maps/AMapException.html#ERROR_ILLEGAL_VALUE">ERROR_ILLEGAL_VALUE</a></code></td>
<td class="colLast"><code>"\u975e\u6cd5\u5750\u6807\u503c"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.maps.AMapException.ERROR_INVALID_PARAMETER">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/maps/AMapException.html#ERROR_INVALID_PARAMETER">ERROR_INVALID_PARAMETER</a></code></td>
<td class="colLast"><code>"\u65e0\u6548\u7684\u53c2\u6570 - IllegalArgumentException"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.maps.AMapException.ERROR_IO">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/maps/AMapException.html#ERROR_IO">ERROR_IO</a></code></td>
<td class="colLast"><code>"IO \u64cd\u4f5c\u5f02\u5e38 - IOException"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.maps.AMapException.ERROR_NOT_AVAILABLE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/maps/AMapException.html#ERROR_NOT_AVAILABLE">ERROR_NOT_AVAILABLE</a></code></td>
<td class="colLast"><code>"\u4e0d\u53ef\u5199\u5165\u5f02\u5e38"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.maps.AMapException.ERROR_NOT_ENOUGH_SPACE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/maps/AMapException.html#ERROR_NOT_ENOUGH_SPACE">ERROR_NOT_ENOUGH_SPACE</a></code></td>
<td class="colLast"><code>"\u7a7a\u95f4\u4e0d\u8db3"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.maps.AMapException.ERROR_NULL_PARAMETER">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/maps/AMapException.html#ERROR_NULL_PARAMETER">ERROR_NULL_PARAMETER</a></code></td>
<td class="colLast"><code>"\u7a7a\u6307\u9488\u5f02\u5e38 - NullPointException"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.maps.AMapException.ERROR_PROTOCOL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/maps/AMapException.html#ERROR_PROTOCOL">ERROR_PROTOCOL</a></code></td>
<td class="colLast"><code>"\u534f\u8bae\u89e3\u6790\u9519\u8bef - ProtocolException"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.maps.AMapException.ERROR_SOCKE_TIME_OUT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/maps/AMapException.html#ERROR_SOCKE_TIME_OUT">ERROR_SOCKE_TIME_OUT</a></code></td>
<td class="colLast"><code>"socket \u8fde\u63a5\u8d85\u65f6 - SocketTimeoutException"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.maps.AMapException.ERROR_SOCKET">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/maps/AMapException.html#ERROR_SOCKET">ERROR_SOCKET</a></code></td>
<td class="colLast"><code>"socket \u8fde\u63a5\u5f02\u5e38 - SocketException"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.maps.AMapException.ERROR_UNKNOW_HOST">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/maps/AMapException.html#ERROR_UNKNOW_HOST">ERROR_UNKNOW_HOST</a></code></td>
<td class="colLast"><code>"\u672a\u77e5\u4e3b\u673a - UnKnowHostException"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.maps.AMapException.ERROR_UNKNOW_SERVICE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/maps/AMapException.html#ERROR_UNKNOW_SERVICE">ERROR_UNKNOW_SERVICE</a></code></td>
<td class="colLast"><code>"\u670d\u52a1\u5668\u8fde\u63a5\u5931\u8d25 - UnknownServiceException"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.maps.AMapException.ERROR_UNKNOWN">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/maps/AMapException.html#ERROR_UNKNOWN">ERROR_UNKNOWN</a></code></td>
<td class="colLast"><code>"\u672a\u77e5\u7684\u9519\u8bef"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.maps.AMapException.ERROR_URL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/maps/AMapException.html#ERROR_URL">ERROR_URL</a></code></td>
<td class="colLast"><code>"url\u5f02\u5e38 - MalformedURLException"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.maps.AMapException.FEATURE_GLTF_NOT_SUPPORT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/maps/AMapException.html#FEATURE_GLTF_NOT_SUPPORT">FEATURE_GLTF_NOT_SUPPORT</a></code></td>
<td class="colLast"><code>"GLTF\u529f\u80fd\u4e0d\u652f\u6301"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.maps.AMapException.FEATURE_MVT_NOT_SUPPORT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/maps/AMapException.html#FEATURE_MVT_NOT_SUPPORT">FEATURE_MVT_NOT_SUPPORT</a></code></td>
<td class="colLast"><code>"MVT\u529f\u80fd\u4e0d\u652f\u6301"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.maps.AMapException.FEATURE_TERRAIN_NOT_SUPPORT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/maps/AMapException.html#FEATURE_TERRAIN_NOT_SUPPORT">FEATURE_TERRAIN_NOT_SUPPORT</a></code></td>
<td class="colLast"><code>"\u5730\u5f62\u56fe\u529f\u80fd\u4e3a\u8ba1\u8d39\u80fd\u529b\uff0c\u8bf7\u8054\u7cfb\u5546\u52a1\u8fdb\u884c\u529f\u80fd\u5f00\u901a\u3002"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.maps.AMapException.ILLEGAL_AMAP_ARGUMENT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/maps/AMapException.html#ILLEGAL_AMAP_ARGUMENT">ILLEGAL_AMAP_ARGUMENT</a></code></td>
<td class="colLast"><code>"\u975e\u6cd5\u53c2\u6570"</code></td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="常量字段值表, 列表常量字段和值">
<caption><span>com.amap.api.maps.<a href="com/amap/api/maps/AMapOptions.html" title="com.amap.api.maps中的类">AMapOptions</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th scope="col">常量字段</th>
<th class="colLast" scope="col">值</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.maps.AMapOptions.LOGO_MARGIN_BOTTOM">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/AMapOptions.html#LOGO_MARGIN_BOTTOM">LOGO_MARGIN_BOTTOM</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.maps.AMapOptions.LOGO_MARGIN_LEFT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/AMapOptions.html#LOGO_MARGIN_LEFT">LOGO_MARGIN_LEFT</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.maps.AMapOptions.LOGO_MARGIN_RIGHT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/AMapOptions.html#LOGO_MARGIN_RIGHT">LOGO_MARGIN_RIGHT</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.maps.AMapOptions.LOGO_POSITION_BOTTOM_CENTER">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/AMapOptions.html#LOGO_POSITION_BOTTOM_CENTER">LOGO_POSITION_BOTTOM_CENTER</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.maps.AMapOptions.LOGO_POSITION_BOTTOM_LEFT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/AMapOptions.html#LOGO_POSITION_BOTTOM_LEFT">LOGO_POSITION_BOTTOM_LEFT</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.maps.AMapOptions.LOGO_POSITION_BOTTOM_RIGHT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/AMapOptions.html#LOGO_POSITION_BOTTOM_RIGHT">LOGO_POSITION_BOTTOM_RIGHT</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.maps.AMapOptions.ZOOM_POSITION_RIGHT_BUTTOM">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/AMapOptions.html#ZOOM_POSITION_RIGHT_BUTTOM">ZOOM_POSITION_RIGHT_BUTTOM</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.maps.AMapOptions.ZOOM_POSITION_RIGHT_CENTER">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/AMapOptions.html#ZOOM_POSITION_RIGHT_CENTER">ZOOM_POSITION_RIGHT_CENTER</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="常量字段值表, 列表常量字段和值">
<caption><span>com.amap.api.maps.<a href="com/amap/api/maps/AMapUtils.html" title="com.amap.api.maps中的类">AMapUtils</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th scope="col">常量字段</th>
<th class="colLast" scope="col">值</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.maps.AMapUtils.BUS_COMFORT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/AMapUtils.html#BUS_COMFORT">BUS_COMFORT</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.maps.AMapUtils.BUS_MONEY_LITTLE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/AMapUtils.html#BUS_MONEY_LITTLE">BUS_MONEY_LITTLE</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.maps.AMapUtils.BUS_NO_SUBWAY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/AMapUtils.html#BUS_NO_SUBWAY">BUS_NO_SUBWAY</a></code></td>
<td class="colLast"><code>5</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.maps.AMapUtils.BUS_TIME_FIRST">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/AMapUtils.html#BUS_TIME_FIRST">BUS_TIME_FIRST</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.maps.AMapUtils.BUS_TRANSFER_LITTLE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/AMapUtils.html#BUS_TRANSFER_LITTLE">BUS_TRANSFER_LITTLE</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.maps.AMapUtils.BUS_WALK_LITTLE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/AMapUtils.html#BUS_WALK_LITTLE">BUS_WALK_LITTLE</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.maps.AMapUtils.DRIVING_AVOID_CONGESTION">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/AMapUtils.html#DRIVING_AVOID_CONGESTION">DRIVING_AVOID_CONGESTION</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.maps.AMapUtils.DRIVING_DEFAULT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/AMapUtils.html#DRIVING_DEFAULT">DRIVING_DEFAULT</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.maps.AMapUtils.DRIVING_NO_HIGHWAY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/AMapUtils.html#DRIVING_NO_HIGHWAY">DRIVING_NO_HIGHWAY</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.maps.AMapUtils.DRIVING_NO_HIGHWAY_AVOID_CONGESTION">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/AMapUtils.html#DRIVING_NO_HIGHWAY_AVOID_CONGESTION">DRIVING_NO_HIGHWAY_AVOID_CONGESTION</a></code></td>
<td class="colLast"><code>6</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.maps.AMapUtils.DRIVING_NO_HIGHWAY_AVOID_SHORT_MONEY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/AMapUtils.html#DRIVING_NO_HIGHWAY_AVOID_SHORT_MONEY">DRIVING_NO_HIGHWAY_AVOID_SHORT_MONEY</a></code></td>
<td class="colLast"><code>5</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.maps.AMapUtils.DRIVING_NO_HIGHWAY_SAVE_MONEY_AVOID_CONGESTION">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/AMapUtils.html#DRIVING_NO_HIGHWAY_SAVE_MONEY_AVOID_CONGESTION">DRIVING_NO_HIGHWAY_SAVE_MONEY_AVOID_CONGESTION</a></code></td>
<td class="colLast"><code>8</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.maps.AMapUtils.DRIVING_SAVE_MONEY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/AMapUtils.html#DRIVING_SAVE_MONEY">DRIVING_SAVE_MONEY</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.maps.AMapUtils.DRIVING_SAVE_MONEY_AVOID_CONGESTION">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/AMapUtils.html#DRIVING_SAVE_MONEY_AVOID_CONGESTION">DRIVING_SAVE_MONEY_AVOID_CONGESTION</a></code></td>
<td class="colLast"><code>7</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.maps.AMapUtils.DRIVING_SHORT_DISTANCE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/AMapUtils.html#DRIVING_SHORT_DISTANCE">DRIVING_SHORT_DISTANCE</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="常量字段值表, 列表常量字段和值">
<caption><span>com.amap.api.maps.<a href="com/amap/api/maps/MapsInitializer.html" title="com.amap.api.maps中的类">MapsInitializer</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th scope="col">常量字段</th>
<th class="colLast" scope="col">值</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.maps.MapsInitializer.HTTP">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/MapsInitializer.html#HTTP">HTTP</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.maps.MapsInitializer.HTTPS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/MapsInitializer.html#HTTPS">HTTPS</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
</tbody>
</table>
</li>
</ul>
<ul class="blockList">
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="常量字段值表, 列表常量字段和值">
<caption><span>com.amap.api.maps.model.<a href="com/amap/api/maps/model/AMapPara.html" title="com.amap.api.maps.model中的类">AMapPara</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th scope="col">常量字段</th>
<th class="colLast" scope="col">值</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.maps.model.AMapPara.DOTTEDLINE_TYPE_CIRCLE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/model/AMapPara.html#DOTTEDLINE_TYPE_CIRCLE">DOTTEDLINE_TYPE_CIRCLE</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.maps.model.AMapPara.DOTTEDLINE_TYPE_DEFAULT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/model/AMapPara.html#DOTTEDLINE_TYPE_DEFAULT">DOTTEDLINE_TYPE_DEFAULT</a></code></td>
<td class="colLast"><code>-1</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.maps.model.AMapPara.DOTTEDLINE_TYPE_SQUARE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/model/AMapPara.html#DOTTEDLINE_TYPE_SQUARE">DOTTEDLINE_TYPE_SQUARE</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="常量字段值表, 列表常量字段和值">
<caption><span>com.amap.api.maps.model.<a href="com/amap/api/maps/model/BitmapDescriptorFactory.html" title="com.amap.api.maps.model中的类">BitmapDescriptorFactory</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th scope="col">常量字段</th>
<th class="colLast" scope="col">值</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.maps.model.BitmapDescriptorFactory.HUE_AZURE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;float</code></td>
<td><code><a href="com/amap/api/maps/model/BitmapDescriptorFactory.html#HUE_AZURE">HUE_AZURE</a></code></td>
<td class="colLast"><code>210.0f</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.maps.model.BitmapDescriptorFactory.HUE_BLUE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;float</code></td>
<td><code><a href="com/amap/api/maps/model/BitmapDescriptorFactory.html#HUE_BLUE">HUE_BLUE</a></code></td>
<td class="colLast"><code>240.0f</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.maps.model.BitmapDescriptorFactory.HUE_CYAN">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;float</code></td>
<td><code><a href="com/amap/api/maps/model/BitmapDescriptorFactory.html#HUE_CYAN">HUE_CYAN</a></code></td>
<td class="colLast"><code>180.0f</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.maps.model.BitmapDescriptorFactory.HUE_GREEN">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;float</code></td>
<td><code><a href="com/amap/api/maps/model/BitmapDescriptorFactory.html#HUE_GREEN">HUE_GREEN</a></code></td>
<td class="colLast"><code>120.0f</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.maps.model.BitmapDescriptorFactory.HUE_MAGENTA">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;float</code></td>
<td><code><a href="com/amap/api/maps/model/BitmapDescriptorFactory.html#HUE_MAGENTA">HUE_MAGENTA</a></code></td>
<td class="colLast"><code>300.0f</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.maps.model.BitmapDescriptorFactory.HUE_ORANGE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;float</code></td>
<td><code><a href="com/amap/api/maps/model/BitmapDescriptorFactory.html#HUE_ORANGE">HUE_ORANGE</a></code></td>
<td class="colLast"><code>30.0f</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.maps.model.BitmapDescriptorFactory.HUE_RED">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;float</code></td>
<td><code><a href="com/amap/api/maps/model/BitmapDescriptorFactory.html#HUE_RED">HUE_RED</a></code></td>
<td class="colLast"><code>0.0f</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.maps.model.BitmapDescriptorFactory.HUE_ROSE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;float</code></td>
<td><code><a href="com/amap/api/maps/model/BitmapDescriptorFactory.html#HUE_ROSE">HUE_ROSE</a></code></td>
<td class="colLast"><code>330.0f</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.maps.model.BitmapDescriptorFactory.HUE_VIOLET">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;float</code></td>
<td><code><a href="com/amap/api/maps/model/BitmapDescriptorFactory.html#HUE_VIOLET">HUE_VIOLET</a></code></td>
<td class="colLast"><code>270.0f</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.maps.model.BitmapDescriptorFactory.HUE_YELLOW">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;float</code></td>
<td><code><a href="com/amap/api/maps/model/BitmapDescriptorFactory.html#HUE_YELLOW">HUE_YELLOW</a></code></td>
<td class="colLast"><code>60.0f</code></td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="常量字段值表, 列表常量字段和值">
<caption><span>com.amap.api.maps.model.<a href="com/amap/api/maps/model/GroundOverlayOptions.html" title="com.amap.api.maps.model中的类">GroundOverlayOptions</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th scope="col">常量字段</th>
<th class="colLast" scope="col">值</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.maps.model.GroundOverlayOptions.NO_DIMENSION">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;float</code></td>
<td><code><a href="com/amap/api/maps/model/GroundOverlayOptions.html#NO_DIMENSION">NO_DIMENSION</a></code></td>
<td class="colLast"><code>-1.0f</code></td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="常量字段值表, 列表常量字段和值">
<caption><span>com.amap.api.maps.model.<a href="com/amap/api/maps/model/HeatMapGridLayerOptions.html" title="com.amap.api.maps.model中的类">HeatMapGridLayerOptions</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th scope="col">常量字段</th>
<th class="colLast" scope="col">值</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.maps.model.HeatMapGridLayerOptions.TYPE_GRID">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/model/HeatMapGridLayerOptions.html#TYPE_GRID">TYPE_GRID</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.maps.model.HeatMapGridLayerOptions.TYPE_HEXAGON">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/model/HeatMapGridLayerOptions.html#TYPE_HEXAGON">TYPE_HEXAGON</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.maps.model.HeatMapGridLayerOptions.TYPE_NORMAL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/model/HeatMapGridLayerOptions.html#TYPE_NORMAL">TYPE_NORMAL</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="常量字段值表, 列表常量字段和值">
<caption><span>com.amap.api.maps.model.<a href="com/amap/api/maps/model/HeatMapLayerOptions.html" title="com.amap.api.maps.model中的类">HeatMapLayerOptions</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th scope="col">常量字段</th>
<th class="colLast" scope="col">值</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.maps.model.HeatMapLayerOptions.DEFAULT_OPACITY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;double</code></td>
<td><code><a href="com/amap/api/maps/model/HeatMapLayerOptions.html#DEFAULT_OPACITY">DEFAULT_OPACITY</a></code></td>
<td class="colLast"><code>0.6</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.maps.model.HeatMapLayerOptions.DEFAULT_RADIUS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/model/HeatMapLayerOptions.html#DEFAULT_RADIUS">DEFAULT_RADIUS</a></code></td>
<td class="colLast"><code>12</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.maps.model.HeatMapLayerOptions.TYPE_GRID">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/model/HeatMapLayerOptions.html#TYPE_GRID">TYPE_GRID</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.maps.model.HeatMapLayerOptions.TYPE_HEXAGON">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/model/HeatMapLayerOptions.html#TYPE_HEXAGON">TYPE_HEXAGON</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.maps.model.HeatMapLayerOptions.TYPE_NORMAL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/model/HeatMapLayerOptions.html#TYPE_NORMAL">TYPE_NORMAL</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="常量字段值表, 列表常量字段和值">
<caption><span>com.amap.api.maps.model.<a href="com/amap/api/maps/model/HeatmapTileProvider.html" title="com.amap.api.maps.model中的类">HeatmapTileProvider</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th scope="col">常量字段</th>
<th class="colLast" scope="col">值</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.maps.model.HeatmapTileProvider.DEFAULT_OPACITY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;double</code></td>
<td><code><a href="com/amap/api/maps/model/HeatmapTileProvider.html#DEFAULT_OPACITY">DEFAULT_OPACITY</a></code></td>
<td class="colLast"><code>0.6</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.maps.model.HeatmapTileProvider.DEFAULT_RADIUS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/model/HeatmapTileProvider.html#DEFAULT_RADIUS">DEFAULT_RADIUS</a></code></td>
<td class="colLast"><code>12</code></td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="常量字段值表, 列表常量字段和值">
<caption><span>com.amap.api.maps.model.<a href="com/amap/api/maps/model/MyLocationStyle.html" title="com.amap.api.maps.model中的类">MyLocationStyle</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th scope="col">常量字段</th>
<th class="colLast" scope="col">值</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.maps.model.MyLocationStyle.ERROR_CODE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/maps/model/MyLocationStyle.html#ERROR_CODE">ERROR_CODE</a></code></td>
<td class="colLast"><code>"errorCode"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.maps.model.MyLocationStyle.ERROR_INFO">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/maps/model/MyLocationStyle.html#ERROR_INFO">ERROR_INFO</a></code></td>
<td class="colLast"><code>"errorInfo"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.maps.model.MyLocationStyle.LOCATION_TYPE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/maps/model/MyLocationStyle.html#LOCATION_TYPE">LOCATION_TYPE</a></code></td>
<td class="colLast"><code>"locationType"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.maps.model.MyLocationStyle.LOCATION_TYPE_FOLLOW">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/model/MyLocationStyle.html#LOCATION_TYPE_FOLLOW">LOCATION_TYPE_FOLLOW</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.maps.model.MyLocationStyle.LOCATION_TYPE_FOLLOW_NO_CENTER">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/model/MyLocationStyle.html#LOCATION_TYPE_FOLLOW_NO_CENTER">LOCATION_TYPE_FOLLOW_NO_CENTER</a></code></td>
<td class="colLast"><code>6</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.maps.model.MyLocationStyle.LOCATION_TYPE_LOCATE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/model/MyLocationStyle.html#LOCATION_TYPE_LOCATE">LOCATION_TYPE_LOCATE</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.maps.model.MyLocationStyle.LOCATION_TYPE_LOCATION_ROTATE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/model/MyLocationStyle.html#LOCATION_TYPE_LOCATION_ROTATE">LOCATION_TYPE_LOCATION_ROTATE</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.maps.model.MyLocationStyle.LOCATION_TYPE_LOCATION_ROTATE_NO_CENTER">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/model/MyLocationStyle.html#LOCATION_TYPE_LOCATION_ROTATE_NO_CENTER">LOCATION_TYPE_LOCATION_ROTATE_NO_CENTER</a></code></td>
<td class="colLast"><code>5</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.maps.model.MyLocationStyle.LOCATION_TYPE_MAP_ROTATE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/model/MyLocationStyle.html#LOCATION_TYPE_MAP_ROTATE">LOCATION_TYPE_MAP_ROTATE</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.maps.model.MyLocationStyle.LOCATION_TYPE_MAP_ROTATE_NO_CENTER">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/model/MyLocationStyle.html#LOCATION_TYPE_MAP_ROTATE_NO_CENTER">LOCATION_TYPE_MAP_ROTATE_NO_CENTER</a></code></td>
<td class="colLast"><code>7</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.maps.model.MyLocationStyle.LOCATION_TYPE_SHOW">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/model/MyLocationStyle.html#LOCATION_TYPE_SHOW">LOCATION_TYPE_SHOW</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="常量字段值表, 列表常量字段和值">
<caption><span>com.amap.api.maps.model.<a href="com/amap/api/maps/model/NaviPara.html" title="com.amap.api.maps.model中的类">NaviPara</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th scope="col">常量字段</th>
<th class="colLast" scope="col">值</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.maps.model.NaviPara.DRIVING_AVOID_CONGESTION">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/model/NaviPara.html#DRIVING_AVOID_CONGESTION">DRIVING_AVOID_CONGESTION</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.maps.model.NaviPara.DRIVING_DEFAULT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/model/NaviPara.html#DRIVING_DEFAULT">DRIVING_DEFAULT</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.maps.model.NaviPara.DRIVING_NO_HIGHWAY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/model/NaviPara.html#DRIVING_NO_HIGHWAY">DRIVING_NO_HIGHWAY</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.maps.model.NaviPara.DRIVING_NO_HIGHWAY_AVOID_CONGESTION">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/model/NaviPara.html#DRIVING_NO_HIGHWAY_AVOID_CONGESTION">DRIVING_NO_HIGHWAY_AVOID_CONGESTION</a></code></td>
<td class="colLast"><code>6</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.maps.model.NaviPara.DRIVING_NO_HIGHWAY_AVOID_SHORT_MONEY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/model/NaviPara.html#DRIVING_NO_HIGHWAY_AVOID_SHORT_MONEY">DRIVING_NO_HIGHWAY_AVOID_SHORT_MONEY</a></code></td>
<td class="colLast"><code>5</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.maps.model.NaviPara.DRIVING_NO_HIGHWAY_SAVE_MONEY_AVOID_CONGESTION">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/model/NaviPara.html#DRIVING_NO_HIGHWAY_SAVE_MONEY_AVOID_CONGESTION">DRIVING_NO_HIGHWAY_SAVE_MONEY_AVOID_CONGESTION</a></code></td>
<td class="colLast"><code>8</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.maps.model.NaviPara.DRIVING_SAVE_MONEY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/model/NaviPara.html#DRIVING_SAVE_MONEY">DRIVING_SAVE_MONEY</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.maps.model.NaviPara.DRIVING_SAVE_MONEY_AVOID_CONGESTION">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/model/NaviPara.html#DRIVING_SAVE_MONEY_AVOID_CONGESTION">DRIVING_SAVE_MONEY_AVOID_CONGESTION</a></code></td>
<td class="colLast"><code>7</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.maps.model.NaviPara.DRIVING_SHORT_DISTANCE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/model/NaviPara.html#DRIVING_SHORT_DISTANCE">DRIVING_SHORT_DISTANCE</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="常量字段值表, 列表常量字段和值">
<caption><span>com.amap.api.maps.model.<a href="com/amap/api/maps/model/PolylineOptions.html" title="com.amap.api.maps.model中的类">PolylineOptions</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th scope="col">常量字段</th>
<th class="colLast" scope="col">值</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.maps.model.PolylineOptions.DOTTEDLINE_TYPE_CIRCLE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/model/PolylineOptions.html#DOTTEDLINE_TYPE_CIRCLE">DOTTEDLINE_TYPE_CIRCLE</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.maps.model.PolylineOptions.DOTTEDLINE_TYPE_SQUARE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/model/PolylineOptions.html#DOTTEDLINE_TYPE_SQUARE">DOTTEDLINE_TYPE_SQUARE</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="常量字段值表, 列表常量字段和值">
<caption><span>com.amap.api.maps.model.<a href="com/amap/api/maps/model/Text.html" title="com.amap.api.maps.model中的类">Text</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th scope="col">常量字段</th>
<th class="colLast" scope="col">值</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.maps.model.Text.ALIGN_BOTTOM">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/model/Text.html#ALIGN_BOTTOM">ALIGN_BOTTOM</a></code></td>
<td class="colLast"><code>16</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.maps.model.Text.ALIGN_CENTER_HORIZONTAL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/model/Text.html#ALIGN_CENTER_HORIZONTAL">ALIGN_CENTER_HORIZONTAL</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.maps.model.Text.ALIGN_CENTER_VERTICAL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/model/Text.html#ALIGN_CENTER_VERTICAL">ALIGN_CENTER_VERTICAL</a></code></td>
<td class="colLast"><code>32</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.maps.model.Text.ALIGN_LEFT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/model/Text.html#ALIGN_LEFT">ALIGN_LEFT</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.maps.model.Text.ALIGN_RIGHT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/model/Text.html#ALIGN_RIGHT">ALIGN_RIGHT</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.maps.model.Text.ALIGN_TOP">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/model/Text.html#ALIGN_TOP">ALIGN_TOP</a></code></td>
<td class="colLast"><code>8</code></td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="常量字段值表, 列表常量字段和值">
<caption><span>com.amap.api.maps.model.<a href="com/amap/api/maps/model/WeightedLatLng.html" title="com.amap.api.maps.model中的类">WeightedLatLng</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th scope="col">常量字段</th>
<th class="colLast" scope="col">值</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.maps.model.WeightedLatLng.DEFAULT_INTENSITY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;double</code></td>
<td><code><a href="com/amap/api/maps/model/WeightedLatLng.html#DEFAULT_INTENSITY">DEFAULT_INTENSITY</a></code></td>
<td class="colLast"><code>1.0</code></td>
</tr>
</tbody>
</table>
</li>
</ul>
<ul class="blockList">
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="常量字段值表, 列表常量字段和值">
<caption><span>com.amap.api.maps.model.animation.<a href="com/amap/api/maps/model/animation/Animation.html" title="com.amap.api.maps.model.animation中的类">Animation</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th scope="col">常量字段</th>
<th class="colLast" scope="col">值</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.maps.model.animation.Animation.FILL_MODE_BACKWARDS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/model/animation/Animation.html#FILL_MODE_BACKWARDS">FILL_MODE_BACKWARDS</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.maps.model.animation.Animation.FILL_MODE_FORWARDS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/model/animation/Animation.html#FILL_MODE_FORWARDS">FILL_MODE_FORWARDS</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.maps.model.animation.Animation.INFINITE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/model/animation/Animation.html#INFINITE">INFINITE</a></code></td>
<td class="colLast"><code>-1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.maps.model.animation.Animation.RESTART">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/model/animation/Animation.html#RESTART">RESTART</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.maps.model.animation.Animation.REVERSE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/model/animation/Animation.html#REVERSE">REVERSE</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
</tbody>
</table>
</li>
</ul>
<ul class="blockList">
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="常量字段值表, 列表常量字段和值">
<caption><span>com.amap.api.maps.model.particle.<a href="com/amap/api/maps/model/particle/ColorGenerate.html" title="com.amap.api.maps.model.particle中的类">ColorGenerate</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th scope="col">常量字段</th>
<th class="colLast" scope="col">值</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.maps.model.particle.ColorGenerate.TYPE_DEFAULT">
<!--   -->
</a><code>protected&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/model/particle/ColorGenerate.html#TYPE_DEFAULT">TYPE_DEFAULT</a></code></td>
<td class="colLast"><code>-1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.maps.model.particle.ColorGenerate.TYPE_RANDOMCOLORBETWEENTWOCONSTANTS">
<!--   -->
</a><code>protected&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/model/particle/ColorGenerate.html#TYPE_RANDOMCOLORBETWEENTWOCONSTANTS">TYPE_RANDOMCOLORBETWEENTWOCONSTANTS</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="常量字段值表, 列表常量字段和值">
<caption><span>com.amap.api.maps.model.particle.<a href="com/amap/api/maps/model/particle/ParticleOverlayOptionsFactory.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlayOptionsFactory</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th scope="col">常量字段</th>
<th class="colLast" scope="col">值</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.maps.model.particle.ParticleOverlayOptionsFactory.PARTICLE_TYPE_HAZE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/model/particle/ParticleOverlayOptionsFactory.html#PARTICLE_TYPE_HAZE">PARTICLE_TYPE_HAZE</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.maps.model.particle.ParticleOverlayOptionsFactory.PARTICLE_TYPE_RAIN">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/model/particle/ParticleOverlayOptionsFactory.html#PARTICLE_TYPE_RAIN">PARTICLE_TYPE_RAIN</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.maps.model.particle.ParticleOverlayOptionsFactory.PARTICLE_TYPE_SNOWY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/model/particle/ParticleOverlayOptionsFactory.html#PARTICLE_TYPE_SNOWY">PARTICLE_TYPE_SNOWY</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.maps.model.particle.ParticleOverlayOptionsFactory.PARTICLE_TYPE_SUNNY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/model/particle/ParticleOverlayOptionsFactory.html#PARTICLE_TYPE_SUNNY">PARTICLE_TYPE_SUNNY</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="常量字段值表, 列表常量字段和值">
<caption><span>com.amap.api.maps.model.particle.<a href="com/amap/api/maps/model/particle/ParticleShapeModule.html" title="com.amap.api.maps.model.particle中的类">ParticleShapeModule</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th scope="col">常量字段</th>
<th class="colLast" scope="col">值</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.maps.model.particle.ParticleShapeModule.TYPE_DEFAULT">
<!--   -->
</a><code>protected&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/model/particle/ParticleShapeModule.html#TYPE_DEFAULT">TYPE_DEFAULT</a></code></td>
<td class="colLast"><code>-1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.maps.model.particle.ParticleShapeModule.TYPE_RECT">
<!--   -->
</a><code>protected&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/model/particle/ParticleShapeModule.html#TYPE_RECT">TYPE_RECT</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.maps.model.particle.ParticleShapeModule.TYPE_SINGLEPOINT">
<!--   -->
</a><code>protected&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/model/particle/ParticleShapeModule.html#TYPE_SINGLEPOINT">TYPE_SINGLEPOINT</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="常量字段值表, 列表常量字段和值">
<caption><span>com.amap.api.maps.model.particle.<a href="com/amap/api/maps/model/particle/RotationOverLife.html" title="com.amap.api.maps.model.particle中的类">RotationOverLife</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th scope="col">常量字段</th>
<th class="colLast" scope="col">值</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.maps.model.particle.RotationOverLife.TYPE_CONSTANTROTATIONOVERLIFE">
<!--   -->
</a><code>protected&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/model/particle/RotationOverLife.html#TYPE_CONSTANTROTATIONOVERLIFE">TYPE_CONSTANTROTATIONOVERLIFE</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.maps.model.particle.RotationOverLife.TYPE_DEFAULT">
<!--   -->
</a><code>protected&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/model/particle/RotationOverLife.html#TYPE_DEFAULT">TYPE_DEFAULT</a></code></td>
<td class="colLast"><code>-1</code></td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="常量字段值表, 列表常量字段和值">
<caption><span>com.amap.api.maps.model.particle.<a href="com/amap/api/maps/model/particle/SizeOverLife.html" title="com.amap.api.maps.model.particle中的类">SizeOverLife</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th scope="col">常量字段</th>
<th class="colLast" scope="col">值</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.maps.model.particle.SizeOverLife.TYPE_CURVESIZEOVERLIFE">
<!--   -->
</a><code>protected&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/model/particle/SizeOverLife.html#TYPE_CURVESIZEOVERLIFE">TYPE_CURVESIZEOVERLIFE</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.maps.model.particle.SizeOverLife.TYPE_DEFAULT">
<!--   -->
</a><code>protected&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/model/particle/SizeOverLife.html#TYPE_DEFAULT">TYPE_DEFAULT</a></code></td>
<td class="colLast"><code>-1</code></td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="常量字段值表, 列表常量字段和值">
<caption><span>com.amap.api.maps.model.particle.<a href="com/amap/api/maps/model/particle/VelocityGenerate.html" title="com.amap.api.maps.model.particle中的类">VelocityGenerate</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th scope="col">常量字段</th>
<th class="colLast" scope="col">值</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.maps.model.particle.VelocityGenerate.TYPE_DEFAULT">
<!--   -->
</a><code>protected&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/model/particle/VelocityGenerate.html#TYPE_DEFAULT">TYPE_DEFAULT</a></code></td>
<td class="colLast"><code>-1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.maps.model.particle.VelocityGenerate.TYPE_RANDOMVELOCITYBETWEENTWOCONSTANTS">
<!--   -->
</a><code>protected&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/model/particle/VelocityGenerate.html#TYPE_RANDOMVELOCITYBETWEENTWOCONSTANTS">TYPE_RANDOMVELOCITYBETWEENTWOCONSTANTS</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
</tbody>
</table>
</li>
</ul>
<ul class="blockList">
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="常量字段值表, 列表常量字段和值">
<caption><span>com.amap.api.maps.offlinemap.<a href="com/amap/api/maps/offlinemap/OfflineMapStatus.html" title="com.amap.api.maps.offlinemap中的类">OfflineMapStatus</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th scope="col">常量字段</th>
<th class="colLast" scope="col">值</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.maps.offlinemap.OfflineMapStatus.CHECKUPDATES">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/offlinemap/OfflineMapStatus.html#CHECKUPDATES">CHECKUPDATES</a></code></td>
<td class="colLast"><code>6</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.maps.offlinemap.OfflineMapStatus.ERROR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/offlinemap/OfflineMapStatus.html#ERROR">ERROR</a></code></td>
<td class="colLast"><code>-1</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.maps.offlinemap.OfflineMapStatus.EXCEPTION_AMAP">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/offlinemap/OfflineMapStatus.html#EXCEPTION_AMAP">EXCEPTION_AMAP</a></code></td>
<td class="colLast"><code>102</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.maps.offlinemap.OfflineMapStatus.EXCEPTION_NETWORK_LOADING">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/offlinemap/OfflineMapStatus.html#EXCEPTION_NETWORK_LOADING">EXCEPTION_NETWORK_LOADING</a></code></td>
<td class="colLast"><code>101</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.maps.offlinemap.OfflineMapStatus.EXCEPTION_SDCARD">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/offlinemap/OfflineMapStatus.html#EXCEPTION_SDCARD">EXCEPTION_SDCARD</a></code></td>
<td class="colLast"><code>103</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.maps.offlinemap.OfflineMapStatus.LOADING">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/offlinemap/OfflineMapStatus.html#LOADING">LOADING</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.maps.offlinemap.OfflineMapStatus.NEW_VERSION">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/offlinemap/OfflineMapStatus.html#NEW_VERSION">NEW_VERSION</a></code></td>
<td class="colLast"><code>7</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.maps.offlinemap.OfflineMapStatus.PAUSE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/offlinemap/OfflineMapStatus.html#PAUSE">PAUSE</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.maps.offlinemap.OfflineMapStatus.START_DOWNLOAD_FAILD">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/offlinemap/OfflineMapStatus.html#START_DOWNLOAD_FAILD">START_DOWNLOAD_FAILD</a></code></td>
<td class="colLast"><code>1002</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.maps.offlinemap.OfflineMapStatus.STOP">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/offlinemap/OfflineMapStatus.html#STOP">STOP</a></code></td>
<td class="colLast"><code>5</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.maps.offlinemap.OfflineMapStatus.SUCCESS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/offlinemap/OfflineMapStatus.html#SUCCESS">SUCCESS</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.maps.offlinemap.OfflineMapStatus.UNZIP">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/offlinemap/OfflineMapStatus.html#UNZIP">UNZIP</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.maps.offlinemap.OfflineMapStatus.WAITING">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/offlinemap/OfflineMapStatus.html#WAITING">WAITING</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
</tbody>
</table>
</li>
</ul>
<ul class="blockList">
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="常量字段值表, 列表常量字段和值">
<caption><span>com.amap.api.maps.utils.<a href="com/amap/api/maps/utils/SpatialRelationUtil.html" title="com.amap.api.maps.utils中的类">SpatialRelationUtil</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th scope="col">常量字段</th>
<th class="colLast" scope="col">值</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.maps.utils.SpatialRelationUtil.A_CIRCLE_DEGREE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/utils/SpatialRelationUtil.html#A_CIRCLE_DEGREE">A_CIRCLE_DEGREE</a></code></td>
<td class="colLast"><code>360</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.maps.utils.SpatialRelationUtil.A_HALF_CIRCLE_DEGREE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/utils/SpatialRelationUtil.html#A_HALF_CIRCLE_DEGREE">A_HALF_CIRCLE_DEGREE</a></code></td>
<td class="colLast"><code>180</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.maps.utils.SpatialRelationUtil.MIN_OFFSET_DEGREE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/utils/SpatialRelationUtil.html#MIN_OFFSET_DEGREE">MIN_OFFSET_DEGREE</a></code></td>
<td class="colLast"><code>50</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.maps.utils.SpatialRelationUtil.MIN_POLYLINE_POINT_SIZE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/maps/utils/SpatialRelationUtil.html#MIN_POLYLINE_POINT_SIZE">MIN_POLYLINE_POINT_SIZE</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
</tbody>
</table>
</li>
</ul>
<ul class="blockList">
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="常量字段值表, 列表常量字段和值">
<caption><span>com.amap.api.maps.utils.overlay.<a href="com/amap/api/maps/utils/overlay/SmoothMoveMarker.html" title="com.amap.api.maps.utils.overlay中的类">SmoothMoveMarker</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th scope="col">常量字段</th>
<th class="colLast" scope="col">值</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.maps.utils.overlay.SmoothMoveMarker.MIN_OFFSET_DISTANCE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;float</code></td>
<td><code><a href="com/amap/api/maps/utils/overlay/SmoothMoveMarker.html#MIN_OFFSET_DISTANCE">MIN_OFFSET_DISTANCE</a></code></td>
<td class="colLast"><code>5.0f</code></td>
</tr>
</tbody>
</table>
</li>
</ul>
<ul class="blockList">
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="常量字段值表, 列表常量字段和值">
<caption><span>com.amap.api.trace.<a href="com/amap/api/trace/LBSTraceClient.html" title="com.amap.api.trace中的类">LBSTraceClient</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th scope="col">常量字段</th>
<th class="colLast" scope="col">值</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.trace.LBSTraceClient.LOCATE_TIMEOUT_ERROR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/trace/LBSTraceClient.html#LOCATE_TIMEOUT_ERROR">LOCATE_TIMEOUT_ERROR</a></code></td>
<td class="colLast"><code>"\u5b9a\u4f4d\u8d85\u65f6"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.trace.LBSTraceClient.MIN_GRASP_POINT_ERROR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/trace/LBSTraceClient.html#MIN_GRASP_POINT_ERROR">MIN_GRASP_POINT_ERROR</a></code></td>
<td class="colLast"><code>"\u8f68\u8ff9\u70b9\u592a\u5c11\u6216\u8ddd\u79bb\u592a\u8fd1,\u8f68\u8ff9\u7ea0\u504f\u5931\u8d25"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.trace.LBSTraceClient.TRACE_SUCCESS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/amap/api/trace/LBSTraceClient.html#TRACE_SUCCESS">TRACE_SUCCESS</a></code></td>
<td class="colLast"><code>"\u7ea0\u504f\u6210\u529f"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.trace.LBSTraceClient.TYPE_AMAP">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/trace/LBSTraceClient.html#TYPE_AMAP">TYPE_AMAP</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.trace.LBSTraceClient.TYPE_BAIDU">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/trace/LBSTraceClient.html#TYPE_BAIDU">TYPE_BAIDU</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.trace.LBSTraceClient.TYPE_GPS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/trace/LBSTraceClient.html#TYPE_GPS">TYPE_GPS</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="常量字段值表, 列表常量字段和值">
<caption><span>com.amap.api.trace.<a href="com/amap/api/trace/TraceOverlay.html" title="com.amap.api.trace中的类">TraceOverlay</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th scope="col">常量字段</th>
<th class="colLast" scope="col">值</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.trace.TraceOverlay.TRACE_STATUS_FAILURE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/trace/TraceOverlay.html#TRACE_STATUS_FAILURE">TRACE_STATUS_FAILURE</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.trace.TraceOverlay.TRACE_STATUS_FINISH">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/trace/TraceOverlay.html#TRACE_STATUS_FINISH">TRACE_STATUS_FINISH</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.amap.api.trace.TraceOverlay.TRACE_STATUS_PREPARE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/trace/TraceOverlay.html#TRACE_STATUS_PREPARE">TRACE_STATUS_PREPARE</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.amap.api.trace.TraceOverlay.TRACE_STATUS_PROCESSING">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/amap/api/trace/TraceOverlay.html#TRACE_STATUS_PROCESSING">TRACE_STATUS_PROCESSING</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
</tbody>
</table>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="overview-tree.html">树</a></li>
<li><a href="deprecated-list.html">已过时</a></li>
<li><a href="index-files/index-1.html">索引</a></li>
<li><a href="help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个</li>
<li>下一个</li>
</ul>
<ul class="navList">
<li><a href="index.html?constant-values.html" target="_top">框架</a></li>
<li><a href="constant-values.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
