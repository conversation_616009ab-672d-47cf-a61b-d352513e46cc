<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>类分层结构</title>
<link rel="stylesheet" type="text/css" href="stylesheet.css" title="Style">
<script type="text/javascript" src="script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="\u7C7B\u5206\u5C42\u7ED3\u6784";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li class="navBarCell1Rev">树</li>
<li><a href="deprecated-list.html">已过时</a></li>
<li><a href="index-files/index-1.html">索引</a></li>
<li><a href="help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个</li>
<li>下一个</li>
</ul>
<ul class="navList">
<li><a href="index.html?overview-tree.html" target="_top">框架</a></li>
<li><a href="overview-tree.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 class="title">所有程序包的分层结构</h1>
<span class="packageHierarchyLabel">程序包分层结构:</span>
<ul class="horizontal">
<li><a href="com/amap/api/maps/package-tree.html">com.amap.api.maps</a>, </li>
<li><a href="com/amap/api/maps/model/package-tree.html">com.amap.api.maps.model</a>, </li>
<li><a href="com/amap/api/maps/model/amap3dmodeltile/package-tree.html">com.amap.api.maps.model.amap3dmodeltile</a>, </li>
<li><a href="com/amap/api/maps/model/animation/package-tree.html">com.amap.api.maps.model.animation</a>, </li>
<li><a href="com/amap/api/maps/model/particle/package-tree.html">com.amap.api.maps.model.particle</a>, </li>
<li><a href="com/amap/api/maps/offlinemap/package-tree.html">com.amap.api.maps.offlinemap</a>, </li>
<li><a href="com/amap/api/maps/utils/package-tree.html">com.amap.api.maps.utils</a>, </li>
<li><a href="com/amap/api/maps/utils/overlay/package-tree.html">com.amap.api.maps.utils.overlay</a>, </li>
<li><a href="com/amap/api/trace/package-tree.html">com.amap.api.trace</a></li>
</ul>
</div>
<div class="contentContainer">
<h2 title="类分层结构">类分层结构</h2>
<ul>
<li type="circle">java.lang.Object
<ul>
<li type="circle">com.amap.api.maps.<a href="com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类"><span class="typeNameLink">AMap</span></a></li>
<li type="circle">com.amap.api.maps.model.<a href="com/amap/api/maps/model/AMapCameraInfo.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">AMapCameraInfo</span></a></li>
<li type="circle">com.amap.api.maps.model.<a href="com/amap/api/maps/model/AMapGLOverlay.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">AMapGLOverlay</span></a></li>
<li type="circle">com.amap.api.maps.<a href="com/amap/api/maps/AMapOptions.html" title="com.amap.api.maps中的类"><span class="typeNameLink">AMapOptions</span></a> (implements android.os.Parcelable)</li>
<li type="circle">com.amap.api.maps.model.<a href="com/amap/api/maps/model/AMapPara.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">AMapPara</span></a></li>
<li type="circle">AMapPermissionActivity
<ul>
<li type="circle">com.amap.api.maps.offlinemap.<a href="com/amap/api/maps/offlinemap/OfflineMapActivity.html" title="com.amap.api.maps.offlinemap中的类"><span class="typeNameLink">OfflineMapActivity</span></a> (implements android.view.View.OnClickListener)</li>
</ul>
</li>
<li type="circle">com.amap.api.maps.<a href="com/amap/api/maps/AMapUtils.html" title="com.amap.api.maps中的类"><span class="typeNameLink">AMapUtils</span></a></li>
<li type="circle">com.amap.api.maps.model.animation.<a href="com/amap/api/maps/model/animation/Animation.html" title="com.amap.api.maps.model.animation中的类"><span class="typeNameLink">Animation</span></a>
<ul>
<li type="circle">com.amap.api.maps.model.animation.<a href="com/amap/api/maps/model/animation/AlphaAnimation.html" title="com.amap.api.maps.model.animation中的类"><span class="typeNameLink">AlphaAnimation</span></a></li>
<li type="circle">com.amap.api.maps.model.animation.<a href="com/amap/api/maps/model/animation/AnimationSet.html" title="com.amap.api.maps.model.animation中的类"><span class="typeNameLink">AnimationSet</span></a></li>
<li type="circle">com.amap.api.maps.model.animation.<a href="com/amap/api/maps/model/animation/RotateAnimation.html" title="com.amap.api.maps.model.animation中的类"><span class="typeNameLink">RotateAnimation</span></a></li>
<li type="circle">com.amap.api.maps.model.animation.<a href="com/amap/api/maps/model/animation/ScaleAnimation.html" title="com.amap.api.maps.model.animation中的类"><span class="typeNameLink">ScaleAnimation</span></a></li>
<li type="circle">com.amap.api.maps.model.animation.<a href="com/amap/api/maps/model/animation/TranslateAnimation.html" title="com.amap.api.maps.model.animation中的类"><span class="typeNameLink">TranslateAnimation</span></a></li>
</ul>
</li>
<li type="circle">com.amap.api.maps.model.<a href="com/amap/api/maps/model/BaseOptions.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">BaseOptions</span></a>
<ul>
<li type="circle">com.amap.api.maps.model.<a href="com/amap/api/maps/model/ArcOptions.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">ArcOptions</span></a> (implements java.lang.Cloneable, android.os.Parcelable)</li>
<li type="circle">com.amap.api.maps.model.<a href="com/amap/api/maps/model/BaseHoleOptions.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">BaseHoleOptions</span></a>
<ul>
<li type="circle">com.amap.api.maps.model.<a href="com/amap/api/maps/model/CircleHoleOptions.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">CircleHoleOptions</span></a> (implements android.os.Parcelable)</li>
<li type="circle">com.amap.api.maps.model.<a href="com/amap/api/maps/model/PolygonHoleOptions.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">PolygonHoleOptions</span></a> (implements android.os.Parcelable)</li>
</ul>
</li>
<li type="circle">com.amap.api.maps.model.<a href="com/amap/api/maps/model/BuildingOverlay.BuildingOverlayTotalOptions.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">BuildingOverlay.BuildingOverlayTotalOptions</span></a></li>
<li type="circle">com.amap.api.maps.model.<a href="com/amap/api/maps/model/BuildingOverlayOptions.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">BuildingOverlayOptions</span></a></li>
<li type="circle">com.amap.api.maps.model.<a href="com/amap/api/maps/model/CircleOptions.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">CircleOptions</span></a> (implements java.lang.Cloneable, android.os.Parcelable)</li>
<li type="circle">com.amap.api.maps.model.<a href="com/amap/api/maps/model/GLTFOverlayOptions.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">GLTFOverlayOptions</span></a> (implements java.lang.Cloneable, android.os.Parcelable)</li>
<li type="circle">com.amap.api.maps.model.<a href="com/amap/api/maps/model/GroundOverlayOptions.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">GroundOverlayOptions</span></a> (implements java.lang.Cloneable, android.os.Parcelable)</li>
<li type="circle">com.amap.api.maps.model.<a href="com/amap/api/maps/model/HeatMapGridLayerOptions.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">HeatMapGridLayerOptions</span></a></li>
<li type="circle">com.amap.api.maps.model.<a href="com/amap/api/maps/model/HeatMapLayerOptions.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">HeatMapLayerOptions</span></a></li>
<li type="circle">com.amap.api.maps.model.<a href="com/amap/api/maps/model/MarkerOptions.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">MarkerOptions</span></a> (implements java.lang.Cloneable, android.os.Parcelable)</li>
<li type="circle">com.amap.api.maps.model.<a href="com/amap/api/maps/model/MultiPointOverlayOptions.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">MultiPointOverlayOptions</span></a> (implements java.lang.Cloneable)</li>
<li type="circle">com.amap.api.maps.model.<a href="com/amap/api/maps/model/MVTTileOverlayOptions.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">MVTTileOverlayOptions</span></a> (implements android.os.Parcelable)</li>
<li type="circle">com.amap.api.maps.model.<a href="com/amap/api/maps/model/NavigateArrowOptions.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">NavigateArrowOptions</span></a> (implements java.lang.Cloneable, android.os.Parcelable)</li>
<li type="circle">com.amap.api.maps.model.particle.<a href="com/amap/api/maps/model/particle/ParticleOverlayOptions.html" title="com.amap.api.maps.model.particle中的类"><span class="typeNameLink">ParticleOverlayOptions</span></a> (implements java.lang.Cloneable, android.os.Parcelable)</li>
<li type="circle">com.amap.api.maps.model.<a href="com/amap/api/maps/model/PolygonOptions.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">PolygonOptions</span></a> (implements java.lang.Cloneable, android.os.Parcelable)</li>
<li type="circle">com.amap.api.maps.model.<a href="com/amap/api/maps/model/PolylineOptions.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">PolylineOptions</span></a> (implements android.os.Parcelable)</li>
<li type="circle">com.amap.api.maps.model.<a href="com/amap/api/maps/model/TileOverlayOptions.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">TileOverlayOptions</span></a> (implements android.os.Parcelable)</li>
</ul>
</li>
<li type="circle">com.amap.api.maps.model.<a href="com/amap/api/maps/model/BaseOverlay.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">BaseOverlay</span></a>
<ul>
<li type="circle">com.amap.api.maps.model.<a href="com/amap/api/maps/model/Arc.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">Arc</span></a></li>
<li type="circle">com.amap.api.maps.model.<a href="com/amap/api/maps/model/BasePointOverlay.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">BasePointOverlay</span></a>
<ul>
<li type="circle">com.amap.api.maps.model.<a href="com/amap/api/maps/model/Marker.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">Marker</span></a></li>
</ul>
</li>
<li type="circle">com.amap.api.maps.model.<a href="com/amap/api/maps/model/BuildingOverlay.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">BuildingOverlay</span></a></li>
<li type="circle">com.amap.api.maps.model.<a href="com/amap/api/maps/model/Circle.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">Circle</span></a></li>
<li type="circle">com.amap.api.maps.model.<a href="com/amap/api/maps/model/GLTFOverlay.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">GLTFOverlay</span></a></li>
<li type="circle">com.amap.api.maps.model.<a href="com/amap/api/maps/model/GroundOverlay.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">GroundOverlay</span></a></li>
<li type="circle">com.amap.api.maps.model.<a href="com/amap/api/maps/model/HeatMapGridLayer.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">HeatMapGridLayer</span></a></li>
<li type="circle">com.amap.api.maps.model.<a href="com/amap/api/maps/model/HeatMapLayer.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">HeatMapLayer</span></a></li>
<li type="circle">com.amap.api.maps.model.<a href="com/amap/api/maps/model/MultiPointOverlay.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">MultiPointOverlay</span></a></li>
<li type="circle">com.amap.api.maps.model.<a href="com/amap/api/maps/model/MVTTileOverlay.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">MVTTileOverlay</span></a></li>
<li type="circle">com.amap.api.maps.model.<a href="com/amap/api/maps/model/NavigateArrow.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">NavigateArrow</span></a></li>
<li type="circle">com.amap.api.maps.model.particle.<a href="com/amap/api/maps/model/particle/ParticleOverlay.html" title="com.amap.api.maps.model.particle中的类"><span class="typeNameLink">ParticleOverlay</span></a></li>
<li type="circle">com.amap.api.maps.model.<a href="com/amap/api/maps/model/Polygon.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">Polygon</span></a></li>
<li type="circle">com.amap.api.maps.model.<a href="com/amap/api/maps/model/Polyline.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">Polyline</span></a></li>
<li type="circle">com.amap.api.maps.model.<a href="com/amap/api/maps/model/TileOverlay.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">TileOverlay</span></a></li>
</ul>
</li>
<li type="circle">BaseTileRequest
<ul>
<li type="circle">com.amap.api.maps.model.amap3dmodeltile.<a href="com/amap/api/maps/model/amap3dmodeltile/AMap3DModelTileProvider.AMap3DModelRequest.html" title="com.amap.api.maps.model.amap3dmodeltile中的类"><span class="typeNameLink">AMap3DModelTileProvider.AMap3DModelRequest</span></a></li>
</ul>
</li>
<li type="circle">com.amap.api.maps.model.<a href="com/amap/api/maps/model/BitmapDescriptor.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">BitmapDescriptor</span></a> (implements java.lang.Cloneable, android.os.Parcelable)</li>
<li type="circle">com.amap.api.maps.model.<a href="com/amap/api/maps/model/BitmapDescriptorFactory.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">BitmapDescriptorFactory</span></a></li>
<li type="circle">com.amap.api.maps.model.<a href="com/amap/api/maps/model/CameraPosition.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">CameraPosition</span></a> (implements android.os.Parcelable)</li>
<li type="circle">com.amap.api.maps.model.<a href="com/amap/api/maps/model/CameraPosition.Builder.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">CameraPosition.Builder</span></a></li>
<li type="circle">com.amap.api.maps.<a href="com/amap/api/maps/CameraUpdate.html" title="com.amap.api.maps中的类"><span class="typeNameLink">CameraUpdate</span></a></li>
<li type="circle">com.amap.api.maps.<a href="com/amap/api/maps/CameraUpdateFactory.html" title="com.amap.api.maps中的类"><span class="typeNameLink">CameraUpdateFactory</span></a></li>
<li type="circle">com.amap.api.maps.offlinemap.<a href="com/amap/api/maps/offlinemap/City.html" title="com.amap.api.maps.offlinemap中的类"><span class="typeNameLink">City</span></a> (implements android.os.Parcelable)
<ul>
<li type="circle">com.amap.api.maps.offlinemap.<a href="com/amap/api/maps/offlinemap/OfflineMapCity.html" title="com.amap.api.maps.offlinemap中的类"><span class="typeNameLink">OfflineMapCity</span></a></li>
</ul>
</li>
<li type="circle">com.amap.api.maps.model.particle.<a href="com/amap/api/maps/model/particle/ColorGenerate.html" title="com.amap.api.maps.model.particle中的类"><span class="typeNameLink">ColorGenerate</span></a>
<ul>
<li type="circle">com.amap.api.maps.model.particle.<a href="com/amap/api/maps/model/particle/RandomColorBetWeenTwoConstants.html" title="com.amap.api.maps.model.particle中的类"><span class="typeNameLink">RandomColorBetWeenTwoConstants</span></a></li>
</ul>
</li>
<li type="circle">com.amap.api.maps.model.<a href="com/amap/api/maps/model/ColorLatLng.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">ColorLatLng</span></a></li>
<li type="circle">com.amap.api.maps.<a href="com/amap/api/maps/CoordinateConverter.html" title="com.amap.api.maps中的类"><span class="typeNameLink">CoordinateConverter</span></a></li>
<li type="circle">com.amap.api.maps.model.<a href="com/amap/api/maps/model/CrossOverlay.UpdateItem.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">CrossOverlay.UpdateItem</span></a></li>
<li type="circle">com.amap.api.maps.model.<a href="com/amap/api/maps/model/CustomMapStyleOptions.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">CustomMapStyleOptions</span></a></li>
<li type="circle">android.app.Fragment (implements android.content.ComponentCallbacks2, android.view.View.OnCreateContextMenuListener)
<ul>
<li type="circle">com.amap.api.maps.<a href="com/amap/api/maps/MapFragment.html" title="com.amap.api.maps中的类"><span class="typeNameLink">MapFragment</span></a></li>
<li type="circle">com.amap.api.maps.<a href="com/amap/api/maps/TextureMapFragment.html" title="com.amap.api.maps中的类"><span class="typeNameLink">TextureMapFragment</span></a></li>
</ul>
</li>
<li type="circle">android.support.v4.app.Fragment
<ul>
<li type="circle">com.amap.api.maps.<a href="com/amap/api/maps/SupportMapFragment.html" title="com.amap.api.maps中的类"><span class="typeNameLink">SupportMapFragment</span></a></li>
</ul>
</li>
<li type="circle">android.support.v4.app.Fragment
<ul>
<li type="circle">com.amap.api.maps.<a href="com/amap/api/maps/TextureSupportMapFragment.html" title="com.amap.api.maps中的类"><span class="typeNameLink">TextureSupportMapFragment</span></a></li>
</ul>
</li>
<li type="circle">com.amap.api.maps.model.<a href="com/amap/api/maps/model/GLTFOverlayOptionsCreator.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">GLTFOverlayOptionsCreator</span></a> (implements android.os.Parcelable.Creator&lt;T&gt;)</li>
<li type="circle">com.amap.api.maps.model.<a href="com/amap/api/maps/model/GLTFResourceIterm.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">GLTFResourceIterm</span></a></li>
<li type="circle">com.amap.api.maps.model.<a href="com/amap/api/maps/model/Gradient.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">Gradient</span></a></li>
<li type="circle">com.amap.api.maps.model.<a href="com/amap/api/maps/model/HeatMapItem.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">HeatMapItem</span></a></li>
<li type="circle">com.amap.api.maps.model.<a href="com/amap/api/maps/model/HeatmapTileProvider.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">HeatmapTileProvider</span></a> (implements com.amap.api.maps.model.<a href="com/amap/api/maps/model/TileProvider.html" title="com.amap.api.maps.model中的接口">TileProvider</a>)</li>
<li type="circle">com.amap.api.maps.model.<a href="com/amap/api/maps/model/HeatmapTileProvider.Builder.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">HeatmapTileProvider.Builder</span></a></li>
<li type="circle">com.amap.api.maps.model.<a href="com/amap/api/maps/model/ImageOptions.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">ImageOptions</span></a></li>
<li type="circle">com.amap.api.maps.model.<a href="com/amap/api/maps/model/IndoorBuildingInfo.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">IndoorBuildingInfo</span></a></li>
<li type="circle">com.amap.api.maps.model.<a href="com/amap/api/maps/model/LatLng.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">LatLng</span></a> (implements java.lang.Cloneable, android.os.Parcelable)</li>
<li type="circle">com.amap.api.maps.model.<a href="com/amap/api/maps/model/LatLngBounds.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">LatLngBounds</span></a> (implements android.os.Parcelable)</li>
<li type="circle">com.amap.api.maps.model.<a href="com/amap/api/maps/model/LatLngBounds.Builder.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">LatLngBounds.Builder</span></a></li>
<li type="circle">com.amap.api.trace.<a href="com/amap/api/trace/LBSTraceClient.html" title="com.amap.api.trace中的类"><span class="typeNameLink">LBSTraceClient</span></a></li>
<li type="circle">com.amap.api.maps.<a href="com/amap/api/maps/MapsInitializer.html" title="com.amap.api.maps中的类"><span class="typeNameLink">MapsInitializer</span></a></li>
<li type="circle">com.amap.api.maps.utils.overlay.<a href="com/amap/api/maps/utils/overlay/MovingPointOverlay.html" title="com.amap.api.maps.utils.overlay中的类"><span class="typeNameLink">MovingPointOverlay</span></a></li>
<li type="circle">com.amap.api.maps.model.<a href="com/amap/api/maps/model/MultiPointItem.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">MultiPointItem</span></a></li>
<li type="circle">com.amap.api.maps.model.<a href="com/amap/api/maps/model/MVTTileOverlayOptions.Builder.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">MVTTileOverlayOptions.Builder</span></a></li>
<li type="circle">com.amap.api.maps.model.<a href="com/amap/api/maps/model/MVTTileProvider.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">MVTTileProvider</span></a> (implements com.amap.api.maps.model.<a href="com/amap/api/maps/model/TileProvider.html" title="com.amap.api.maps.model中的接口">TileProvider</a>)</li>
<li type="circle">com.amap.api.maps.model.<a href="com/amap/api/maps/model/MyLocationStyle.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">MyLocationStyle</span></a> (implements android.os.Parcelable)</li>
<li type="circle">com.amap.api.maps.model.<a href="com/amap/api/maps/model/MyTrafficStyle.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">MyTrafficStyle</span></a></li>
<li type="circle">com.amap.api.maps.model.<a href="com/amap/api/maps/model/NaviPara.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">NaviPara</span></a></li>
<li type="circle">com.amap.api.maps.offlinemap.<a href="com/amap/api/maps/offlinemap/OfflineMapManager.html" title="com.amap.api.maps.offlinemap中的类"><span class="typeNameLink">OfflineMapManager</span></a></li>
<li type="circle">com.amap.api.maps.offlinemap.<a href="com/amap/api/maps/offlinemap/OfflineMapStatus.html" title="com.amap.api.maps.offlinemap中的类"><span class="typeNameLink">OfflineMapStatus</span></a></li>
<li type="circle">com.amap.api.maps.model.particle.<a href="com/amap/api/maps/model/particle/ParticleEmissionModule.html" title="com.amap.api.maps.model.particle中的类"><span class="typeNameLink">ParticleEmissionModule</span></a></li>
<li type="circle">com.amap.api.maps.model.particle.<a href="com/amap/api/maps/model/particle/ParticleOverlayOptionsFactory.html" title="com.amap.api.maps.model.particle中的类"><span class="typeNameLink">ParticleOverlayOptionsFactory</span></a></li>
<li type="circle">com.amap.api.maps.model.particle.<a href="com/amap/api/maps/model/particle/ParticleOverLifeModule.html" title="com.amap.api.maps.model.particle中的类"><span class="typeNameLink">ParticleOverLifeModule</span></a></li>
<li type="circle">com.amap.api.maps.model.particle.<a href="com/amap/api/maps/model/particle/ParticleShapeModule.html" title="com.amap.api.maps.model.particle中的类"><span class="typeNameLink">ParticleShapeModule</span></a>
<ul>
<li type="circle">com.amap.api.maps.model.particle.<a href="com/amap/api/maps/model/particle/RectParticleShape.html" title="com.amap.api.maps.model.particle中的类"><span class="typeNameLink">RectParticleShape</span></a></li>
<li type="circle">com.amap.api.maps.model.particle.<a href="com/amap/api/maps/model/particle/SinglePointParticleShape.html" title="com.amap.api.maps.model.particle中的类"><span class="typeNameLink">SinglePointParticleShape</span></a></li>
</ul>
</li>
<li type="circle">com.amap.api.maps.model.<a href="com/amap/api/maps/model/Poi.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">Poi</span></a> (implements android.os.Parcelable)</li>
<li type="circle">com.amap.api.maps.model.<a href="com/amap/api/maps/model/PoiPara.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">PoiPara</span></a></li>
<li type="circle">com.amap.api.maps.<a href="com/amap/api/maps/Projection.html" title="com.amap.api.maps中的类"><span class="typeNameLink">Projection</span></a></li>
<li type="circle">com.amap.api.maps.offlinemap.<a href="com/amap/api/maps/offlinemap/Province.html" title="com.amap.api.maps.offlinemap中的类"><span class="typeNameLink">Province</span></a> (implements android.os.Parcelable)
<ul>
<li type="circle">com.amap.api.maps.offlinemap.<a href="com/amap/api/maps/offlinemap/OfflineMapProvince.html" title="com.amap.api.maps.offlinemap中的类"><span class="typeNameLink">OfflineMapProvince</span></a></li>
</ul>
</li>
<li type="circle">com.amap.api.maps.model.particle.<a href="com/amap/api/maps/model/particle/RotationOverLife.html" title="com.amap.api.maps.model.particle中的类"><span class="typeNameLink">RotationOverLife</span></a>
<ul>
<li type="circle">com.amap.api.maps.model.particle.<a href="com/amap/api/maps/model/particle/ConstantRotationOverLife.html" title="com.amap.api.maps.model.particle中的类"><span class="typeNameLink">ConstantRotationOverLife</span></a></li>
</ul>
</li>
<li type="circle">com.amap.api.maps.model.<a href="com/amap/api/maps/model/RoutePara.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">RoutePara</span></a></li>
<li type="circle">com.amap.api.maps.model.particle.<a href="com/amap/api/maps/model/particle/SizeOverLife.html" title="com.amap.api.maps.model.particle中的类"><span class="typeNameLink">SizeOverLife</span></a>
<ul>
<li type="circle">com.amap.api.maps.model.particle.<a href="com/amap/api/maps/model/particle/CurveSizeOverLife.html" title="com.amap.api.maps.model.particle中的类"><span class="typeNameLink">CurveSizeOverLife</span></a></li>
</ul>
</li>
<li type="circle">com.amap.api.maps.utils.overlay.<a href="com/amap/api/maps/utils/overlay/SmoothMoveMarker.html" title="com.amap.api.maps.utils.overlay中的类"><span class="typeNameLink">SmoothMoveMarker</span></a></li>
<li type="circle">com.amap.api.maps.utils.<a href="com/amap/api/maps/utils/SpatialRelationUtil.html" title="com.amap.api.maps.utils中的类"><span class="typeNameLink">SpatialRelationUtil</span></a></li>
<li type="circle">com.amap.api.maps.model.<a href="com/amap/api/maps/model/Text.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">Text</span></a></li>
<li type="circle">com.amap.api.maps.model.<a href="com/amap/api/maps/model/TextOptions.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">TextOptions</span></a> (implements java.lang.Cloneable, android.os.Parcelable)</li>
<li type="circle">java.lang.Throwable (implements java.io.Serializable)
<ul>
<li type="circle">java.lang.Exception
<ul>
<li type="circle">com.amap.api.maps.<a href="com/amap/api/maps/AMapException.html" title="com.amap.api.maps中的类"><span class="typeNameLink">AMapException</span></a></li>
<li type="circle">java.lang.RuntimeException
<ul>
<li type="circle">com.amap.api.maps.model.<a href="com/amap/api/maps/model/RuntimeRemoteException.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">RuntimeRemoteException</span></a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
<li type="circle">com.amap.api.maps.model.<a href="com/amap/api/maps/model/Tile.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">Tile</span></a> (implements android.os.Parcelable)</li>
<li type="circle">com.amap.api.maps.model.<a href="com/amap/api/maps/model/TileOverlaySource.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">TileOverlaySource</span></a></li>
<li type="circle">com.amap.api.maps.model.<a href="com/amap/api/maps/model/TileProjection.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">TileProjection</span></a> (implements android.os.Parcelable)</li>
<li type="circle">com.amap.api.trace.<a href="com/amap/api/trace/TraceLocation.html" title="com.amap.api.trace中的类"><span class="typeNameLink">TraceLocation</span></a></li>
<li type="circle">com.amap.api.trace.<a href="com/amap/api/trace/TraceOverlay.html" title="com.amap.api.trace中的类"><span class="typeNameLink">TraceOverlay</span></a></li>
<li type="circle">com.amap.api.maps.<a href="com/amap/api/maps/UiSettings.html" title="com.amap.api.maps中的类"><span class="typeNameLink">UiSettings</span></a></li>
<li type="circle">com.amap.api.maps.model.<a href="com/amap/api/maps/model/UrlTileProvider.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">UrlTileProvider</span></a> (implements com.amap.api.maps.model.<a href="com/amap/api/maps/model/TileProvider.html" title="com.amap.api.maps.model中的接口">TileProvider</a>)</li>
<li type="circle">com.amap.api.maps.model.particle.<a href="com/amap/api/maps/model/particle/VelocityGenerate.html" title="com.amap.api.maps.model.particle中的类"><span class="typeNameLink">VelocityGenerate</span></a>
<ul>
<li type="circle">com.amap.api.maps.model.particle.<a href="com/amap/api/maps/model/particle/RandomVelocityBetweenTwoConstants.html" title="com.amap.api.maps.model.particle中的类"><span class="typeNameLink">RandomVelocityBetweenTwoConstants</span></a></li>
</ul>
</li>
<li type="circle">android.view.View (implements android.view.accessibility.AccessibilityEventSource, android.graphics.drawable.Drawable.Callback, android.view.KeyEvent.Callback)
<ul>
<li type="circle">com.amap.api.maps.offlinemap.<a href="com/amap/api/maps/offlinemap/DownloadProgressView.html" title="com.amap.api.maps.offlinemap中的类"><span class="typeNameLink">DownloadProgressView</span></a></li>
<li type="circle">android.view.ViewGroup (implements android.view.ViewManager, android.view.ViewParent)
<ul>
<li type="circle">android.widget.AdapterView&lt;T&gt;
<ul>
<li type="circle">android.widget.AbsListView (implements android.widget.Filter.FilterListener, android.text.TextWatcher, android.view.ViewTreeObserver.OnGlobalLayoutListener, android.view.ViewTreeObserver.OnTouchModeChangeListener)
<ul>
<li type="circle">android.widget.ListView
<ul>
<li type="circle">com.amap.api.maps.offlinemap.<a href="com/amap/api/maps/offlinemap/DownLoadListView.html" title="com.amap.api.maps.offlinemap中的类"><span class="typeNameLink">DownLoadListView</span></a></li>
<li type="circle">android.widget.ExpandableListView
<ul>
<li type="circle">com.amap.api.maps.offlinemap.<a href="com/amap/api/maps/offlinemap/CityExpandView.html" title="com.amap.api.maps.offlinemap中的类"><span class="typeNameLink">CityExpandView</span></a></li>
<li type="circle">com.amap.api.maps.offlinemap.<a href="com/amap/api/maps/offlinemap/DownLoadExpandListView.html" title="com.amap.api.maps.offlinemap中的类"><span class="typeNameLink">DownLoadExpandListView</span></a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
<li type="circle">android.widget.FrameLayout
<ul>
<li type="circle">com.amap.api.maps.<a href="com/amap/api/maps/MapView.html" title="com.amap.api.maps中的类"><span class="typeNameLink">MapView</span></a></li>
<li type="circle">com.amap.api.maps.<a href="com/amap/api/maps/TextureMapView.html" title="com.amap.api.maps中的类"><span class="typeNameLink">TextureMapView</span></a></li>
</ul>
</li>
<li type="circle">com.amap.api.maps.<a href="com/amap/api/maps/WearMapView.html" title="com.amap.api.maps中的类"><span class="typeNameLink">WearMapView</span></a></li>
</ul>
</li>
</ul>
</li>
<li type="circle">com.amap.api.maps.model.<a href="com/amap/api/maps/model/VisibleRegion.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">VisibleRegion</span></a> (implements android.os.Parcelable)</li>
<li type="circle">com.amap.api.maps.model.<a href="com/amap/api/maps/model/WeightedLatLng.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">WeightedLatLng</span></a></li>
</ul>
</li>
</ul>
<h2 title="接口分层结构">接口分层结构</h2>
<ul>
<li type="circle">com.amap.api.maps.<a href="com/amap/api/maps/AMap.AMapAppResourceRequestListener.html" title="com.amap.api.maps中的接口"><span class="typeNameLink">AMap.AMapAppResourceRequestListener</span></a></li>
<li type="circle">com.amap.api.maps.<a href="com/amap/api/maps/AMap.CancelableCallback.html" title="com.amap.api.maps中的接口"><span class="typeNameLink">AMap.CancelableCallback</span></a></li>
<li type="circle">com.amap.api.maps.<a href="com/amap/api/maps/AMap.InfoWindowAdapter.html" title="com.amap.api.maps中的接口"><span class="typeNameLink">AMap.InfoWindowAdapter</span></a>
<ul>
<li type="circle">com.amap.api.maps.<a href="com/amap/api/maps/AMap.ImageInfoWindowAdapter.html" title="com.amap.api.maps中的接口"><span class="typeNameLink">AMap.ImageInfoWindowAdapter</span></a></li>
</ul>
</li>
<li type="circle">com.amap.api.maps.<a href="com/amap/api/maps/AMap.OnCacheRemoveListener.html" title="com.amap.api.maps中的接口"><span class="typeNameLink">AMap.OnCacheRemoveListener</span></a></li>
<li type="circle">com.amap.api.maps.<a href="com/amap/api/maps/AMap.OnCameraChangeListener.html" title="com.amap.api.maps中的接口"><span class="typeNameLink">AMap.OnCameraChangeListener</span></a></li>
<li type="circle">com.amap.api.maps.<a href="com/amap/api/maps/AMap.OnIndoorBuildingActiveListener.html" title="com.amap.api.maps中的接口"><span class="typeNameLink">AMap.OnIndoorBuildingActiveListener</span></a></li>
<li type="circle">com.amap.api.maps.<a href="com/amap/api/maps/AMap.OnInfoWindowClickListener.html" title="com.amap.api.maps中的接口"><span class="typeNameLink">AMap.OnInfoWindowClickListener</span></a></li>
<li type="circle">com.amap.api.maps.<a href="com/amap/api/maps/AMap.OnMapClickListener.html" title="com.amap.api.maps中的接口"><span class="typeNameLink">AMap.OnMapClickListener</span></a></li>
<li type="circle">com.amap.api.maps.<a href="com/amap/api/maps/AMap.OnMapLoadedListener.html" title="com.amap.api.maps中的接口"><span class="typeNameLink">AMap.OnMapLoadedListener</span></a></li>
<li type="circle">com.amap.api.maps.<a href="com/amap/api/maps/AMap.OnMapLongClickListener.html" title="com.amap.api.maps中的接口"><span class="typeNameLink">AMap.OnMapLongClickListener</span></a></li>
<li type="circle">com.amap.api.maps.<a href="com/amap/api/maps/AMap.onMapPrintScreenListener.html" title="com.amap.api.maps中的接口"><span class="typeNameLink">AMap.onMapPrintScreenListener</span></a></li>
<li type="circle">com.amap.api.maps.<a href="com/amap/api/maps/AMap.OnMapScreenShotListener.html" title="com.amap.api.maps中的接口"><span class="typeNameLink">AMap.OnMapScreenShotListener</span></a></li>
<li type="circle">com.amap.api.maps.<a href="com/amap/api/maps/AMap.OnMapSnapshotListener.html" title="com.amap.api.maps中的接口"><span class="typeNameLink">AMap.OnMapSnapshotListener</span></a></li>
<li type="circle">com.amap.api.maps.<a href="com/amap/api/maps/AMap.OnMapTouchListener.html" title="com.amap.api.maps中的接口"><span class="typeNameLink">AMap.OnMapTouchListener</span></a></li>
<li type="circle">com.amap.api.maps.<a href="com/amap/api/maps/AMap.OnMarkerClickListener.html" title="com.amap.api.maps中的接口"><span class="typeNameLink">AMap.OnMarkerClickListener</span></a></li>
<li type="circle">com.amap.api.maps.<a href="com/amap/api/maps/AMap.OnMarkerDragListener.html" title="com.amap.api.maps中的接口"><span class="typeNameLink">AMap.OnMarkerDragListener</span></a></li>
<li type="circle">com.amap.api.maps.<a href="com/amap/api/maps/AMap.OnMultiPointClickListener.html" title="com.amap.api.maps中的接口"><span class="typeNameLink">AMap.OnMultiPointClickListener</span></a></li>
<li type="circle">com.amap.api.maps.<a href="com/amap/api/maps/AMap.OnMyLocationChangeListener.html" title="com.amap.api.maps中的接口"><span class="typeNameLink">AMap.OnMyLocationChangeListener</span></a></li>
<li type="circle">com.amap.api.maps.<a href="com/amap/api/maps/AMap.OnPOIClickListener.html" title="com.amap.api.maps中的接口"><span class="typeNameLink">AMap.OnPOIClickListener</span></a></li>
<li type="circle">com.amap.api.maps.<a href="com/amap/api/maps/AMap.OnPolylineClickListener.html" title="com.amap.api.maps中的接口"><span class="typeNameLink">AMap.OnPolylineClickListener</span></a></li>
<li type="circle">com.amap.api.maps.model.<a href="com/amap/api/maps/model/AMapGestureListener.html" title="com.amap.api.maps.model中的接口"><span class="typeNameLink">AMapGestureListener</span></a></li>
<li type="circle">com.amap.api.maps.model.animation.<a href="com/amap/api/maps/model/animation/Animation.AnimationListener.html" title="com.amap.api.maps.model.animation中的接口"><span class="typeNameLink">Animation.AnimationListener</span></a></li>
<li type="circle">com.amap.api.maps.model.<a href="com/amap/api/maps/model/CrossOverlay.GenerateCrossImageListener.html" title="com.amap.api.maps.model中的接口"><span class="typeNameLink">CrossOverlay.GenerateCrossImageListener</span></a></li>
<li type="circle">com.amap.api.maps.model.<a href="com/amap/api/maps/model/CrossOverlay.OnCrossVectorUpdateListener.html" title="com.amap.api.maps.model中的接口"><span class="typeNameLink">CrossOverlay.OnCrossVectorUpdateListener</span></a></li>
<li type="circle">android.opengl.GLSurfaceView.Renderer
<ul>
<li type="circle">com.amap.api.maps.<a href="com/amap/api/maps/CustomRenderer.html" title="com.amap.api.maps中的接口"><span class="typeNameLink">CustomRenderer</span></a></li>
</ul>
</li>
<li type="circle">com.amap.api.maps.<a href="com/amap/api/maps/LocationSource.html" title="com.amap.api.maps中的接口"><span class="typeNameLink">LocationSource</span></a></li>
<li type="circle">com.amap.api.maps.<a href="com/amap/api/maps/LocationSource.OnLocationChangedListener.html" title="com.amap.api.maps中的接口"><span class="typeNameLink">LocationSource.OnLocationChangedListener</span></a></li>
<li type="circle">com.amap.api.maps.utils.overlay.<a href="com/amap/api/maps/utils/overlay/MovingPointOverlay.MoveListener.html" title="com.amap.api.maps.utils.overlay中的接口"><span class="typeNameLink">MovingPointOverlay.MoveListener</span></a></li>
<li type="circle">com.amap.api.maps.offlinemap.<a href="com/amap/api/maps/offlinemap/OfflineMapManager.OfflineLoadedListener.html" title="com.amap.api.maps.offlinemap中的接口"><span class="typeNameLink">OfflineMapManager.OfflineLoadedListener</span></a></li>
<li type="circle">com.amap.api.maps.offlinemap.<a href="com/amap/api/maps/offlinemap/OfflineMapManager.OfflineMapDownloadListener.html" title="com.amap.api.maps.offlinemap中的接口"><span class="typeNameLink">OfflineMapManager.OfflineMapDownloadListener</span></a></li>
<li type="circle">com.amap.api.maps.utils.overlay.<a href="com/amap/api/maps/utils/overlay/SmoothMoveMarker.MoveListener.html" title="com.amap.api.maps.utils.overlay中的接口"><span class="typeNameLink">SmoothMoveMarker.MoveListener</span></a></li>
<li type="circle">com.amap.api.maps.<a href="com/amap/api/maps/SwipeDismissTouchListener.DismissCallbacks.html" title="com.amap.api.maps中的接口"><span class="typeNameLink">SwipeDismissTouchListener.DismissCallbacks</span></a></li>
<li type="circle">com.amap.api.maps.model.<a href="com/amap/api/maps/model/TileProvider.html" title="com.amap.api.maps.model中的接口"><span class="typeNameLink">TileProvider</span></a></li>
<li type="circle">com.amap.api.trace.<a href="com/amap/api/trace/TraceListener.html" title="com.amap.api.trace中的接口"><span class="typeNameLink">TraceListener</span></a></li>
<li type="circle">com.amap.api.trace.<a href="com/amap/api/trace/TraceStatusListener.html" title="com.amap.api.trace中的接口"><span class="typeNameLink">TraceStatusListener</span></a></li>
<li type="circle">com.amap.api.maps.<a href="com/amap/api/maps/WearMapView.OnDismissCallback.html" title="com.amap.api.maps中的接口"><span class="typeNameLink">WearMapView.OnDismissCallback</span></a></li>
</ul>
<h2 title="枚举分层结构">枚举分层结构</h2>
<ul>
<li type="circle">java.lang.Object
<ul>
<li type="circle">java.lang.Enum&lt;E&gt; (implements java.lang.Comparable&lt;T&gt;, java.io.Serializable)
<ul>
<li type="circle">com.amap.api.maps.<a href="com/amap/api/maps/CoordinateConverter.CoordType.html" title="com.amap.api.maps中的枚举"><span class="typeNameLink">CoordinateConverter.CoordType</span></a></li>
<li type="circle">com.amap.api.maps.model.<a href="com/amap/api/maps/model/AMapPara.LineCapType.html" title="com.amap.api.maps.model中的枚举"><span class="typeNameLink">AMapPara.LineCapType</span></a></li>
<li type="circle">com.amap.api.maps.model.<a href="com/amap/api/maps/model/AMapPara.LineJoinType.html" title="com.amap.api.maps.model中的枚举"><span class="typeNameLink">AMapPara.LineJoinType</span></a></li>
<li type="circle">com.amap.api.maps.model.<a href="com/amap/api/maps/model/ImageOptions.ShapeType.html" title="com.amap.api.maps.model中的枚举"><span class="typeNameLink">ImageOptions.ShapeType</span></a></li>
<li type="circle">com.amap.api.maps.model.<a href="com/amap/api/maps/model/PolylineOptions.LineCapType.html" title="com.amap.api.maps.model中的枚举"><span class="typeNameLink">PolylineOptions.LineCapType</span></a></li>
<li type="circle">com.amap.api.maps.model.<a href="com/amap/api/maps/model/PolylineOptions.LineJoinType.html" title="com.amap.api.maps.model中的枚举"><span class="typeNameLink">PolylineOptions.LineJoinType</span></a></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li class="navBarCell1Rev">树</li>
<li><a href="deprecated-list.html">已过时</a></li>
<li><a href="index-files/index-1.html">索引</a></li>
<li><a href="help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个</li>
<li>下一个</li>
</ul>
<ul class="navList">
<li><a href="index.html?overview-tree.html" target="_top">框架</a></li>
<li><a href="overview-tree.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
