<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>概览</title>
<link rel="stylesheet" type="text/css" href="stylesheet.css" title="Style">
<script type="text/javascript" src="script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="\u6982\u89C8";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li class="navBarCell1Rev">概览</li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="overview-tree.html">树</a></li>
<li><a href="deprecated-list.html">已过时</a></li>
<li><a href="index-files/index-1.html">索引</a></li>
<li><a href="help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个</li>
<li>下一个</li>
</ul>
<ul class="navList">
<li><a href="index.html?overview-summary.html" target="_top">框架</a></li>
<li><a href="overview-summary.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="contentContainer">
<table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="程序包表, 列表程序包和解释">
<caption><span>程序包</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">程序包</th>
<th class="colLast" scope="col">说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="com/amap/api/maps/package-summary.html">com.amap.api.maps</a></td>
<td class="colLast">
<div class="block">
地图显示包，帮助您在Android应用程序中显示高德地图，并且支持地图交互手势的功能。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="com/amap/api/maps/model/package-summary.html">com.amap.api.maps.model</a></td>
<td class="colLast">
<div class="block">
覆盖物包，覆盖物（叠加或覆盖到地图的内容）支持标记、折线、多边形和圆。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="com/amap/api/maps/model/amap3dmodeltile/package-summary.html">com.amap.api.maps.model.amap3dmodeltile</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="com/amap/api/maps/model/animation/package-summary.html">com.amap.api.maps.model.animation</a></td>
<td class="colLast">
<div class="block">
  动画类，可用于支持动画的覆盖物。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="com/amap/api/maps/model/particle/package-summary.html">com.amap.api.maps.model.particle</a></td>
<td class="colLast">
<div class="block">
  粒子效果类，用于添加粒子效果覆盖物。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="com/amap/api/maps/offlinemap/package-summary.html">com.amap.api.maps.offlinemap</a></td>
<td class="colLast">
<div class="block">
离线地图包，用户可以通过手机WiFi下载高德3D离线地图。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="com/amap/api/maps/utils/package-summary.html">com.amap.api.maps.utils</a></td>
<td class="colLast">
<div class="block">
工具类，基于地图现有接口的辅助工具。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="com/amap/api/maps/utils/overlay/package-summary.html">com.amap.api.maps.utils.overlay</a></td>
<td class="colLast">
<div class="block">
工具类，基于地图现有接口实现的高级功能。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="com/amap/api/trace/package-summary.html">com.amap.api.trace</a></td>
<td class="colLast">
<div class="block">
轨迹纠偏包，提供高精度定位轨迹抓路后绘制平滑轨迹。</div>
</td>
</tr>
</tbody>
</table>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li class="navBarCell1Rev">概览</li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="overview-tree.html">树</a></li>
<li><a href="deprecated-list.html">已过时</a></li>
<li><a href="index-files/index-1.html">索引</a></li>
<li><a href="help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个</li>
<li>下一个</li>
</ul>
<ul class="navList">
<li><a href="index.html?overview-summary.html" target="_top">框架</a></li>
<li><a href="overview-summary.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
