<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Z - 索引</title>
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Z - \u7D22\u5F15";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-22.html">上一个字母</a></li>
<li>下一个字母</li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-23.html" target="_top">框架</a></li>
<li><a href="index-23.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="contentContainer"><a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">K</a>&nbsp;<a href="index-11.html">L</a>&nbsp;<a href="index-12.html">M</a>&nbsp;<a href="index-13.html">N</a>&nbsp;<a href="index-14.html">O</a>&nbsp;<a href="index-15.html">P</a>&nbsp;<a href="index-16.html">Q</a>&nbsp;<a href="index-17.html">R</a>&nbsp;<a href="index-18.html">S</a>&nbsp;<a href="index-19.html">T</a>&nbsp;<a href="index-20.html">U</a>&nbsp;<a href="index-21.html">V</a>&nbsp;<a href="index-22.html">W</a>&nbsp;<a href="index-23.html">Z</a>&nbsp;<a name="I:Z">
<!--   -->
</a>
<h2 class="title">Z</h2>
<dl>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/ArcOptions.html#zIndex-float-">zIndex(float)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/ArcOptions.html" title="com.amap.api.maps.model中的类">ArcOptions</a></dt>
<dd>
<div class="block">设置弧形Z轴数值参数，默认为0。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/CircleOptions.html#zIndex-float-">zIndex(float)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/CircleOptions.html" title="com.amap.api.maps.model中的类">CircleOptions</a></dt>
<dd>
<div class="block">设置圆的Z轴数值，默认为0。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/GroundOverlayOptions.html#zIndex-float-">zIndex(float)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/GroundOverlayOptions.html" title="com.amap.api.maps.model中的类">GroundOverlayOptions</a></dt>
<dd>
<div class="block">设置ground 覆盖物的z轴指数。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/HeatMapGridLayerOptions.html#zIndex-float-">zIndex(float)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/HeatMapGridLayerOptions.html" title="com.amap.api.maps.model中的类">HeatMapGridLayerOptions</a></dt>
<dd>
<div class="block">设置Z轴的值。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/HeatMapLayerOptions.html#zIndex-float-">zIndex(float)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/HeatMapLayerOptions.html" title="com.amap.api.maps.model中的类">HeatMapLayerOptions</a></dt>
<dd>
<div class="block">设置Z轴的值。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MarkerOptions.html#zIndex-float-">zIndex(float)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MarkerOptions.html" title="com.amap.api.maps.model中的类">MarkerOptions</a></dt>
<dd>
<div class="block">设置Marker覆盖物 zIndex。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MVTTileOverlayOptions.Builder.html#zIndex-float-">zIndex(float)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MVTTileOverlayOptions.Builder.html" title="com.amap.api.maps.model中的类">MVTTileOverlayOptions.Builder</a></dt>
<dd>
<div class="block">覆盖物的zindex</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/NavigateArrowOptions.html#zIndex-float-">zIndex(float)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/NavigateArrowOptions.html" title="com.amap.api.maps.model中的类">NavigateArrowOptions</a></dt>
<dd>
<div class="block">设置箭头箭头(NavigateArrow)覆盖物的Z轴值。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/particle/ParticleOverlayOptions.html#zIndex-float-">zIndex(float)</a></span> - 类 中的方法com.amap.api.maps.model.particle.<a href="../com/amap/api/maps/model/particle/ParticleOverlayOptions.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlayOptions</a></dt>
<dd>
<div class="block">获取覆盖物Z轴的值。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/PolygonOptions.html#zIndex-float-">zIndex(float)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/PolygonOptions.html" title="com.amap.api.maps.model中的类">PolygonOptions</a></dt>
<dd>
<div class="block">设置多边形的Z轴数值</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/PolylineOptions.html#zIndex-float-">zIndex(float)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/PolylineOptions.html" title="com.amap.api.maps.model中的类">PolylineOptions</a></dt>
<dd>
<div class="block">设置线段Z轴的值。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/TextOptions.html#zIndex-float-">zIndex(float)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/TextOptions.html" title="com.amap.api.maps.model中的类">TextOptions</a></dt>
<dd>
<div class="block">设置文字覆盖物 zIndex。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/TileOverlayOptions.html#zIndex-float-">zIndex(float)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/TileOverlayOptions.html" title="com.amap.api.maps.model中的类">TileOverlayOptions</a></dt>
<dd>
<div class="block">设置瓦片图层的Z轴值，默认为0。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/CameraPosition.Builder.html#zoom-float-">zoom(float)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/CameraPosition.Builder.html" title="com.amap.api.maps.model中的类">CameraPosition.Builder</a></dt>
<dd>
<div class="block">设置目标可视区域的缩放级别。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/CameraPosition.html#zoom">zoom</a></span> - 类 中的变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/CameraPosition.html" title="com.amap.api.maps.model中的类">CameraPosition</a></dt>
<dd>
<div class="block">目标可视区域的缩放级别。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMapOptions.html#ZOOM_POSITION_RIGHT_BUTTOM">ZOOM_POSITION_RIGHT_BUTTOM</a></span> - 类 中的静态变量com.amap.api.maps.<a href="../com/amap/api/maps/AMapOptions.html" title="com.amap.api.maps中的类">AMapOptions</a></dt>
<dd>
<div class="block">缩放按钮位置常量（地图右下角）</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMapOptions.html#ZOOM_POSITION_RIGHT_CENTER">ZOOM_POSITION_RIGHT_CENTER</a></span> - 类 中的静态变量com.amap.api.maps.<a href="../com/amap/api/maps/AMapOptions.html" title="com.amap.api.maps中的类">AMapOptions</a></dt>
<dd>
<div class="block">缩放按钮位置常量（地图右边居中）。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/CameraUpdateFactory.html#zoomBy-float-">zoomBy(float)</a></span> - 类 中的静态方法com.amap.api.maps.<a href="../com/amap/api/maps/CameraUpdateFactory.html" title="com.amap.api.maps中的类">CameraUpdateFactory</a></dt>
<dd>
<div class="block">根据给定增量缩放地图级别，在当前地图显示的级别基础上加上这个增量。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/CameraUpdateFactory.html#zoomBy-float-android.graphics.Point-">zoomBy(float, Point)</a></span> - 类 中的静态方法com.amap.api.maps.<a href="../com/amap/api/maps/CameraUpdateFactory.html" title="com.amap.api.maps中的类">CameraUpdateFactory</a></dt>
<dd>
<div class="block">根据给定增量并以给定的屏幕像素点为中心点缩放地图级别。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMapOptions.html#zoomControlsEnabled-boolean-">zoomControlsEnabled(boolean)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMapOptions.html" title="com.amap.api.maps中的类">AMapOptions</a></dt>
<dd>
<div class="block">设置地图是否允许缩放。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMapOptions.html#zoomGesturesEnabled-boolean-">zoomGesturesEnabled(boolean)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMapOptions.html" title="com.amap.api.maps中的类">AMapOptions</a></dt>
<dd>
<div class="block">设置地图是否可以通过手势进行缩放。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/CameraUpdateFactory.html#zoomIn--">zoomIn()</a></span> - 类 中的静态方法com.amap.api.maps.<a href="../com/amap/api/maps/CameraUpdateFactory.html" title="com.amap.api.maps中的类">CameraUpdateFactory</a></dt>
<dd>
<div class="block">放大地图缩放级别，在当前地图显示的级别基础上加1。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/CameraUpdateFactory.html#zoomOut--">zoomOut()</a></span> - 类 中的静态方法com.amap.api.maps.<a href="../com/amap/api/maps/CameraUpdateFactory.html" title="com.amap.api.maps中的类">CameraUpdateFactory</a></dt>
<dd>
<div class="block">缩小地图缩放级别，在当前地图显示的级别基础上减1。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/CameraUpdateFactory.html#zoomTo-float-">zoomTo(float)</a></span> - 类 中的静态方法com.amap.api.maps.<a href="../com/amap/api/maps/CameraUpdateFactory.html" title="com.amap.api.maps中的类">CameraUpdateFactory</a></dt>
<dd>
<div class="block">设置地图缩放级别。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/trace/TraceOverlay.html#zoopToSpan--">zoopToSpan()</a></span> - 类 中的方法com.amap.api.trace.<a href="../com/amap/api/trace/TraceOverlay.html" title="com.amap.api.trace中的类">TraceOverlay</a></dt>
<dd>
<div class="block">设置当前合适显示Camera</div>
</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">K</a>&nbsp;<a href="index-11.html">L</a>&nbsp;<a href="index-12.html">M</a>&nbsp;<a href="index-13.html">N</a>&nbsp;<a href="index-14.html">O</a>&nbsp;<a href="index-15.html">P</a>&nbsp;<a href="index-16.html">Q</a>&nbsp;<a href="index-17.html">R</a>&nbsp;<a href="index-18.html">S</a>&nbsp;<a href="index-19.html">T</a>&nbsp;<a href="index-20.html">U</a>&nbsp;<a href="index-21.html">V</a>&nbsp;<a href="index-22.html">W</a>&nbsp;<a href="index-23.html">Z</a>&nbsp;</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-22.html">上一个字母</a></li>
<li>下一个字母</li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-23.html" target="_top">框架</a></li>
<li><a href="index-23.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
