<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>L - 索引</title>
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="L - \u7D22\u5F15";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-10.html">上一个字母</a></li>
<li><a href="index-12.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-11.html" target="_top">框架</a></li>
<li><a href="index-11.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="contentContainer"><a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">K</a>&nbsp;<a href="index-11.html">L</a>&nbsp;<a href="index-12.html">M</a>&nbsp;<a href="index-13.html">N</a>&nbsp;<a href="index-14.html">O</a>&nbsp;<a href="index-15.html">P</a>&nbsp;<a href="index-16.html">Q</a>&nbsp;<a href="index-17.html">R</a>&nbsp;<a href="index-18.html">S</a>&nbsp;<a href="index-19.html">T</a>&nbsp;<a href="index-20.html">U</a>&nbsp;<a href="index-21.html">V</a>&nbsp;<a href="index-22.html">W</a>&nbsp;<a href="index-23.html">Z</a>&nbsp;<a name="I:L">
<!--   -->
</a>
<h2 class="title">L</h2>
<dl>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/LatLng.html#latitude">latitude</a></span> - 类 中的变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/LatLng.html" title="com.amap.api.maps.model中的类">LatLng</a></dt>
<dd>
<div class="block">纬度 (垂直方向)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/GLTFOverlayOptions.html#latLng-com.amap.api.maps.model.LatLng-">latLng(LatLng)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/GLTFOverlayOptions.html" title="com.amap.api.maps.model中的类">GLTFOverlayOptions</a></dt>
<dd>
<div class="block">设置模型的中心点经纬度坐标</div>
</dd>
<dt><a href="../com/amap/api/maps/model/LatLng.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">LatLng</span></a> - <a href="../com/amap/api/maps/model/package-summary.html">com.amap.api.maps.model</a>中的类</dt>
<dd>
<div class="block">存储经纬度坐标值的类，单位角度。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/LatLng.html#LatLng-double-double-">LatLng(double, double)</a></span> - 类 的构造器com.amap.api.maps.model.<a href="../com/amap/api/maps/model/LatLng.html" title="com.amap.api.maps.model中的类">LatLng</a></dt>
<dd>
<div class="block">使用传入的经纬度构造LatLng 对象，一对经纬度值代表地球上一个地点。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/LatLng.html#LatLng-double-double-boolean-">LatLng(double, double, boolean)</a></span> - 类 的构造器com.amap.api.maps.model.<a href="../com/amap/api/maps/model/LatLng.html" title="com.amap.api.maps.model中的类">LatLng</a></dt>
<dd>
<div class="block">使用传入的经纬度构造LatLng 对象，一对经纬度值代表地球上一个地点。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/WeightedLatLng.html#latLng">latLng</a></span> - 类 中的变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/WeightedLatLng.html" title="com.amap.api.maps.model中的类">WeightedLatLng</a></dt>
<dd>
<div class="block">地理位置</div>
</dd>
<dt><a href="../com/amap/api/maps/model/LatLngBounds.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">LatLngBounds</span></a> - <a href="../com/amap/api/maps/model/package-summary.html">com.amap.api.maps.model</a>中的类</dt>
<dd>
<div class="block">代表了经纬度划分的一个矩形区域。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/LatLngBounds.html#LatLngBounds-com.amap.api.maps.model.LatLng-com.amap.api.maps.model.LatLng-">LatLngBounds(LatLng, LatLng)</a></span> - 类 的构造器com.amap.api.maps.model.<a href="../com/amap/api/maps/model/LatLngBounds.html" title="com.amap.api.maps.model中的类">LatLngBounds</a></dt>
<dd>
<div class="block">使用传入的西南角坐标和东北角坐标创建一个矩形区域。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/VisibleRegion.html#latLngBounds">latLngBounds</a></span> - 类 中的变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/VisibleRegion.html" title="com.amap.api.maps.model中的类">VisibleRegion</a></dt>
<dd>
<div class="block">由可视区域的四个顶点形成的经纬度范围</div>
</dd>
<dt><a href="../com/amap/api/maps/model/LatLngBounds.Builder.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">LatLngBounds.Builder</span></a> - <a href="../com/amap/api/maps/model/package-summary.html">com.amap.api.maps.model</a>中的类</dt>
<dd>
<div class="block">经纬度坐标矩形区域的生成器。</div>
</dd>
<dt><a href="../com/amap/api/trace/LBSTraceClient.html" title="com.amap.api.trace中的类"><span class="typeNameLink">LBSTraceClient</span></a> - <a href="../com/amap/api/trace/package-summary.html">com.amap.api.trace</a>中的类</dt>
<dd>
<div class="block">轨迹纠偏类，对行车GPS高精度定位轨迹集合纠偏</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/trace/LBSTraceClient.html#LBSTraceClient-android.content.Context-">LBSTraceClient(Context)</a></span> - 类 的构造器com.amap.api.trace.<a href="../com/amap/api/trace/LBSTraceClient.html" title="com.amap.api.trace中的类">LBSTraceClient</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span>
<div class="block"><span class="deprecationComment">使用 {<a href="../com/amap/api/trace/LBSTraceClient.html#getInstance-android.content.Context-"><code>LBSTraceClient.getInstance(Context)</code></a>}</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/PolylineOptions.html#lineCapType-com.amap.api.maps.model.PolylineOptions.LineCapType-">lineCapType(PolylineOptions.LineCapType)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/PolylineOptions.html" title="com.amap.api.maps.model中的类">PolylineOptions</a></dt>
<dd>
<div class="block">设置Polyline尾部形状</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/PolygonOptions.html#lineJoinType-com.amap.api.maps.model.AMapPara.LineJoinType-">lineJoinType(AMapPara.LineJoinType)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/PolygonOptions.html" title="com.amap.api.maps.model中的类">PolygonOptions</a></dt>
<dd>
<div class="block">设置边框连接处形状</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/PolylineOptions.html#lineJoinType-com.amap.api.maps.model.PolylineOptions.LineJoinType-">lineJoinType(PolylineOptions.LineJoinType)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/PolylineOptions.html" title="com.amap.api.maps.model中的类">PolylineOptions</a></dt>
<dd>
<div class="block">设置Polyline连接处形状</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/OfflineMapStatus.html#LOADING">LOADING</a></span> - 类 中的静态变量com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/OfflineMapStatus.html" title="com.amap.api.maps.offlinemap中的类">OfflineMapStatus</a></dt>
<dd>
<div class="block">正在下载。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/trace/LBSTraceClient.html#LOCATE_TIMEOUT_ERROR">LOCATE_TIMEOUT_ERROR</a></span> - 类 中的静态变量com.amap.api.trace.<a href="../com/amap/api/trace/LBSTraceClient.html" title="com.amap.api.trace中的类">LBSTraceClient</a></dt>
<dd>
<div class="block">轨迹纠偏错误信息，定位超时</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MyLocationStyle.html#LOCATION_TYPE">LOCATION_TYPE</a></span> - 类 中的静态变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MyLocationStyle.html" title="com.amap.api.maps.model中的类">MyLocationStyle</a></dt>
<dd>
<div class="block">在<a href="../com/amap/api/maps/AMap.OnMyLocationChangeListener.html#onMyLocationChange-android.location.Location-"><code>AMap.OnMyLocationChangeListener.onMyLocationChange(Location)</code></a> 中会返回定位信息<br>
     可通过 Location.getExtras().getInt(<a href="../com/amap/api/maps/model/MyLocationStyle.html#LOCATION_TYPE"><code>MyLocationStyle.LOCATION_TYPE</code></a>) 获取定位类型</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MyLocationStyle.html#LOCATION_TYPE_FOLLOW">LOCATION_TYPE_FOLLOW</a></span> - 类 中的静态变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MyLocationStyle.html" title="com.amap.api.maps.model中的类">MyLocationStyle</a></dt>
<dd>
<div class="block">定位、且将视角移动到地图中心点，定位点跟随设备移动。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MyLocationStyle.html#LOCATION_TYPE_FOLLOW_NO_CENTER">LOCATION_TYPE_FOLLOW_NO_CENTER</a></span> - 类 中的静态变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MyLocationStyle.html" title="com.amap.api.maps.model中的类">MyLocationStyle</a></dt>
<dd>
<div class="block">定位、但不会移动到地图中心点，并且会跟随设备移动。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#LOCATION_TYPE_LOCATE">LOCATION_TYPE_LOCATE</a></span> - 类 中的静态变量com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span>
<div class="block"><span class="deprecationComment">5.0.0 之后请参考 <a href="../com/amap/api/maps/model/MyLocationStyle.html#LOCATION_TYPE_LOCATE"><code>MyLocationStyle.LOCATION_TYPE_LOCATE</code></a></span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MyLocationStyle.html#LOCATION_TYPE_LOCATE">LOCATION_TYPE_LOCATE</a></span> - 类 中的静态变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MyLocationStyle.html" title="com.amap.api.maps.model中的类">MyLocationStyle</a></dt>
<dd>
<div class="block">定位、且将视角移动到地图中心点。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MyLocationStyle.html#LOCATION_TYPE_LOCATION_ROTATE">LOCATION_TYPE_LOCATION_ROTATE</a></span> - 类 中的静态变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MyLocationStyle.html" title="com.amap.api.maps.model中的类">MyLocationStyle</a></dt>
<dd>
<div class="block">定位、且将视角移动到地图中心点，定位点依照设备方向旋转，并且会跟随设备移动。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MyLocationStyle.html#LOCATION_TYPE_LOCATION_ROTATE_NO_CENTER">LOCATION_TYPE_LOCATION_ROTATE_NO_CENTER</a></span> - 类 中的静态变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MyLocationStyle.html" title="com.amap.api.maps.model中的类">MyLocationStyle</a></dt>
<dd>
<div class="block">定位、但不会移动到地图中心点，定位点依照设备方向旋转，并且会跟随设备移动。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#LOCATION_TYPE_MAP_FOLLOW">LOCATION_TYPE_MAP_FOLLOW</a></span> - 类 中的静态变量com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span>
<div class="block"><span class="deprecationComment">5.0.0 之后请参考 <a href="../com/amap/api/maps/model/MyLocationStyle.html#LOCATION_TYPE_FOLLOW"><code>MyLocationStyle.LOCATION_TYPE_FOLLOW</code></a></span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#LOCATION_TYPE_MAP_ROTATE">LOCATION_TYPE_MAP_ROTATE</a></span> - 类 中的静态变量com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span>
<div class="block"><span class="deprecationComment">5.0.0 之后请参考 <a href="../com/amap/api/maps/model/MyLocationStyle.html#LOCATION_TYPE_MAP_ROTATE"><code>MyLocationStyle.LOCATION_TYPE_MAP_ROTATE</code></a></span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MyLocationStyle.html#LOCATION_TYPE_MAP_ROTATE">LOCATION_TYPE_MAP_ROTATE</a></span> - 类 中的静态变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MyLocationStyle.html" title="com.amap.api.maps.model中的类">MyLocationStyle</a></dt>
<dd>
<div class="block">定位、且将视角移动到地图中心点，地图依照设备方向旋转，定位点会跟随设备移动。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MyLocationStyle.html#LOCATION_TYPE_MAP_ROTATE_NO_CENTER">LOCATION_TYPE_MAP_ROTATE_NO_CENTER</a></span> - 类 中的静态变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MyLocationStyle.html" title="com.amap.api.maps.model中的类">MyLocationStyle</a></dt>
<dd>
<div class="block">定位、但不会移动到地图中心点，地图依照设备方向旋转，并且会跟随设备移动。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MyLocationStyle.html#LOCATION_TYPE_SHOW">LOCATION_TYPE_SHOW</a></span> - 类 中的静态变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MyLocationStyle.html" title="com.amap.api.maps.model中的类">MyLocationStyle</a></dt>
<dd>
<div class="block">只定位。</div>
</dd>
<dt><a href="../com/amap/api/maps/LocationSource.html" title="com.amap.api.maps中的接口"><span class="typeNameLink">LocationSource</span></a> - <a href="../com/amap/api/maps/package-summary.html">com.amap.api.maps</a>中的接口</dt>
<dd>
<div class="block">定义了一个定位源，为地图提供定位数据。</div>
</dd>
<dt><a href="../com/amap/api/maps/LocationSource.OnLocationChangedListener.html" title="com.amap.api.maps中的接口"><span class="typeNameLink">LocationSource.OnLocationChangedListener</span></a> - <a href="../com/amap/api/maps/package-summary.html">com.amap.api.maps</a>中的接口</dt>
<dd>
<div class="block">位置改变的监听接口。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMapOptions.html#LOGO_MARGIN_BOTTOM">LOGO_MARGIN_BOTTOM</a></span> - 类 中的静态变量com.amap.api.maps.<a href="../com/amap/api/maps/AMapOptions.html" title="com.amap.api.maps中的类">AMapOptions</a></dt>
<dd>
<div class="block">LOGO边缘MARGIN常量（底部）</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMapOptions.html#LOGO_MARGIN_LEFT">LOGO_MARGIN_LEFT</a></span> - 类 中的静态变量com.amap.api.maps.<a href="../com/amap/api/maps/AMapOptions.html" title="com.amap.api.maps中的类">AMapOptions</a></dt>
<dd>
<div class="block">LOGO边缘MARGIN常量（左边）</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMapOptions.html#LOGO_MARGIN_RIGHT">LOGO_MARGIN_RIGHT</a></span> - 类 中的静态变量com.amap.api.maps.<a href="../com/amap/api/maps/AMapOptions.html" title="com.amap.api.maps中的类">AMapOptions</a></dt>
<dd>
<div class="block">LOGO边缘MARGIN常量（右边）</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMapOptions.html#LOGO_POSITION_BOTTOM_CENTER">LOGO_POSITION_BOTTOM_CENTER</a></span> - 类 中的静态变量com.amap.api.maps.<a href="../com/amap/api/maps/AMapOptions.html" title="com.amap.api.maps中的类">AMapOptions</a></dt>
<dd>
<div class="block">Logo位置常量（地图底部居中）。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMapOptions.html#LOGO_POSITION_BOTTOM_LEFT">LOGO_POSITION_BOTTOM_LEFT</a></span> - 类 中的静态变量com.amap.api.maps.<a href="../com/amap/api/maps/AMapOptions.html" title="com.amap.api.maps中的类">AMapOptions</a></dt>
<dd>
<div class="block">Logo位置常量（地图左下角）。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMapOptions.html#LOGO_POSITION_BOTTOM_RIGHT">LOGO_POSITION_BOTTOM_RIGHT</a></span> - 类 中的静态变量com.amap.api.maps.<a href="../com/amap/api/maps/AMapOptions.html" title="com.amap.api.maps中的类">AMapOptions</a></dt>
<dd>
<div class="block">Logo位置常量（地图右下角）。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMapOptions.html#logoPosition-int-">logoPosition(int)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMapOptions.html" title="com.amap.api.maps中的类">AMapOptions</a></dt>
<dd>
<div class="block">设置“高德地图”Logo的位置。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/LatLng.html#longitude">longitude</a></span> - 类 中的变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/LatLng.html" title="com.amap.api.maps.model中的类">LatLng</a></dt>
<dd>
<div class="block">经度 (水平方向)</div>
</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">K</a>&nbsp;<a href="index-11.html">L</a>&nbsp;<a href="index-12.html">M</a>&nbsp;<a href="index-13.html">N</a>&nbsp;<a href="index-14.html">O</a>&nbsp;<a href="index-15.html">P</a>&nbsp;<a href="index-16.html">Q</a>&nbsp;<a href="index-17.html">R</a>&nbsp;<a href="index-18.html">S</a>&nbsp;<a href="index-19.html">T</a>&nbsp;<a href="index-20.html">U</a>&nbsp;<a href="index-21.html">V</a>&nbsp;<a href="index-22.html">W</a>&nbsp;<a href="index-23.html">Z</a>&nbsp;</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-10.html">上一个字母</a></li>
<li><a href="index-12.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-11.html" target="_top">框架</a></li>
<li><a href="index-11.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
