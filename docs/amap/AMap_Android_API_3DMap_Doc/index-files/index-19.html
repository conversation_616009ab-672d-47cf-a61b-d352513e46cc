<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>T - 索引</title>
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="T - \u7D22\u5F15";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-18.html">上一个字母</a></li>
<li><a href="index-20.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-19.html" target="_top">框架</a></li>
<li><a href="index-19.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="contentContainer"><a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">K</a>&nbsp;<a href="index-11.html">L</a>&nbsp;<a href="index-12.html">M</a>&nbsp;<a href="index-13.html">N</a>&nbsp;<a href="index-14.html">O</a>&nbsp;<a href="index-15.html">P</a>&nbsp;<a href="index-16.html">Q</a>&nbsp;<a href="index-17.html">R</a>&nbsp;<a href="index-18.html">S</a>&nbsp;<a href="index-19.html">T</a>&nbsp;<a href="index-20.html">U</a>&nbsp;<a href="index-21.html">V</a>&nbsp;<a href="index-22.html">W</a>&nbsp;<a href="index-23.html">Z</a>&nbsp;<a name="I:T">
<!--   -->
</a>
<h2 class="title">T</h2>
<dl>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/GLTFOverlay.html#tapClick--">tapClick()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/GLTFOverlay.html" title="com.amap.api.maps.model中的类">GLTFOverlay</a></dt>
<dd>
<div class="block">触发点击展示infoWindow</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/GLTFOverlayOptions.html#tapClick--">tapClick()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/GLTFOverlayOptions.html" title="com.amap.api.maps.model中的类">GLTFOverlayOptions</a></dt>
<dd>
<div class="block">触发点击展示infoWindow</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/CameraPosition.Builder.html#target-com.amap.api.maps.model.LatLng-">target(LatLng)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/CameraPosition.Builder.html" title="com.amap.api.maps.model中的类">CameraPosition.Builder</a></dt>
<dd>
<div class="block">设置目标位置的地图中心点经纬度坐标。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/CameraPosition.html#target">target</a></span> - 类 中的变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/CameraPosition.html" title="com.amap.api.maps.model中的类">CameraPosition</a></dt>
<dd>
<div class="block">目标位置的屏幕中心点经纬度坐标。</div>
</dd>
<dt><a href="../com/amap/api/maps/model/Text.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">Text</span></a> - <a href="../com/amap/api/maps/model/package-summary.html">com.amap.api.maps.model</a>中的类</dt>
<dd>
<div class="block">定义在地图中绘制的文字覆盖物。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/TextOptions.html#text-java.lang.String-">text(String)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/TextOptions.html" title="com.amap.api.maps.model中的类">TextOptions</a></dt>
<dd>
<div class="block">设置文字覆盖物的文字内容。</div>
</dd>
<dt><a href="../com/amap/api/maps/model/TextOptions.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">TextOptions</span></a> - <a href="../com/amap/api/maps/model/package-summary.html">com.amap.api.maps.model</a>中的类</dt>
<dd>
<div class="block">创建文字覆盖物选项</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/TextOptions.html#TextOptions--">TextOptions()</a></span> - 类 的构造器com.amap.api.maps.model.<a href="../com/amap/api/maps/model/TextOptions.html" title="com.amap.api.maps.model中的类">TextOptions</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/amap/api/maps/TextureMapFragment.html" title="com.amap.api.maps中的类"><span class="typeNameLink">TextureMapFragment</span></a> - <a href="../com/amap/api/maps/package-summary.html">com.amap.api.maps</a>中的类</dt>
<dd>
<div class="block">TextureMapFragment 类, 管理地图生命周期，android4.0以上版本使用。</div>
</dd>
<dt><a href="../com/amap/api/maps/TextureMapView.html" title="com.amap.api.maps中的类"><span class="typeNameLink">TextureMapView</span></a> - <a href="../com/amap/api/maps/package-summary.html">com.amap.api.maps</a>中的类</dt>
<dd>
<div class="block">一个显示地图的视图（View），它负责从服务端获取地图数据，它将会捕捉屏幕触控手势事件。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/TextureMapView.html#TextureMapView-android.content.Context-">TextureMapView(Context)</a></span> - 类 的构造器com.amap.api.maps.<a href="../com/amap/api/maps/TextureMapView.html" title="com.amap.api.maps中的类">TextureMapView</a></dt>
<dd>
<div class="block">根据给定的参数构造一个TextureMapView 的新对象。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/TextureMapView.html#TextureMapView-android.content.Context-android.util.AttributeSet-">TextureMapView(Context, AttributeSet)</a></span> - 类 的构造器com.amap.api.maps.<a href="../com/amap/api/maps/TextureMapView.html" title="com.amap.api.maps中的类">TextureMapView</a></dt>
<dd>
<div class="block">根据给定的参数构造一个TextureMapView 的新对象。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/TextureMapView.html#TextureMapView-android.content.Context-android.util.AttributeSet-int-">TextureMapView(Context, AttributeSet, int)</a></span> - 类 的构造器com.amap.api.maps.<a href="../com/amap/api/maps/TextureMapView.html" title="com.amap.api.maps中的类">TextureMapView</a></dt>
<dd>
<div class="block">根据给定的参数构造一个TextureMapView 的新对象。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/TextureMapView.html#TextureMapView-android.content.Context-com.amap.api.maps.AMapOptions-">TextureMapView(Context, AMapOptions)</a></span> - 类 的构造器com.amap.api.maps.<a href="../com/amap/api/maps/TextureMapView.html" title="com.amap.api.maps中的类">TextureMapView</a></dt>
<dd>
<div class="block">根据给定的参数构造一个MapView 的新对象。</div>
</dd>
<dt><a href="../com/amap/api/maps/TextureSupportMapFragment.html" title="com.amap.api.maps中的类"><span class="typeNameLink">TextureSupportMapFragment</span></a> - <a href="../com/amap/api/maps/package-summary.html">com.amap.api.maps</a>中的类</dt>
<dd>
<div class="block">TextureSupportMapFragment 类, 管理地图生命周期。</div>
</dd>
<dt><a href="../com/amap/api/maps/model/Tile.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">Tile</span></a> - <a href="../com/amap/api/maps/model/package-summary.html">com.amap.api.maps.model</a>中的类</dt>
<dd>
<div class="block">图片瓦块信息类。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Tile.html#Tile-int-int-byte:A-">Tile(int, int, byte[])</a></span> - 类 的构造器com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Tile.html" title="com.amap.api.maps.model中的类">Tile</a></dt>
<dd>
<div class="block">构建图片瓦块图层。</div>
</dd>
<dt><a href="../com/amap/api/maps/model/TileOverlay.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">TileOverlay</span></a> - <a href="../com/amap/api/maps/model/package-summary.html">com.amap.api.maps.model</a>中的类</dt>
<dd>
<div class="block">定位地图瓦片图TileOverlay
 TileOverlay是瓦片图层的相关类。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/TileOverlay.html#TileOverlay-IGlOverlayLayer-com.amap.api.maps.model.TileOverlayOptions-java.lang.String-">TileOverlay(IGlOverlayLayer, TileOverlayOptions, String)</a></span> - 类 的构造器com.amap.api.maps.model.<a href="../com/amap/api/maps/model/TileOverlay.html" title="com.amap.api.maps.model中的类">TileOverlay</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/amap/api/maps/model/TileOverlayOptions.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">TileOverlayOptions</span></a> - <a href="../com/amap/api/maps/model/package-summary.html">com.amap.api.maps.model</a>中的类</dt>
<dd>
<div class="block">瓦片TileOverlay的构造选项。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/TileOverlayOptions.html#TileOverlayOptions--">TileOverlayOptions()</a></span> - 类 的构造器com.amap.api.maps.model.<a href="../com/amap/api/maps/model/TileOverlayOptions.html" title="com.amap.api.maps.model中的类">TileOverlayOptions</a></dt>
<dd>
<div class="block">构造一个TileOverlayOptions对象。</div>
</dd>
<dt><a href="../com/amap/api/maps/model/TileOverlaySource.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">TileOverlaySource</span></a> - <a href="../com/amap/api/maps/model/package-summary.html">com.amap.api.maps.model</a>中的类</dt>
<dd>
<div class="block">Tile 数据源</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/TileOverlaySource.html#TileOverlaySource-int-int-java.lang.String-">TileOverlaySource(int, int, String)</a></span> - 类 的构造器com.amap.api.maps.model.<a href="../com/amap/api/maps/model/TileOverlaySource.html" title="com.amap.api.maps.model中的类">TileOverlaySource</a></dt>
<dd>
<div class="block">Tile数据源，需要定数据的类型</div>
</dd>
<dt><a href="../com/amap/api/maps/model/TileProjection.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">TileProjection</span></a> - <a href="../com/amap/api/maps/model/package-summary.html">com.amap.api.maps.model</a>中的类</dt>
<dd>
<div class="block">瓦片编号范围。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/TileProjection.html#TileProjection-int-int-int-int-int-int-">TileProjection(int, int, int, int, int, int)</a></span> - 类 的构造器com.amap.api.maps.model.<a href="../com/amap/api/maps/model/TileProjection.html" title="com.amap.api.maps.model中的类">TileProjection</a></dt>
<dd>
<div class="block">根据给定的参数构造一个 TileProjection 新对象。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/TileOverlayOptions.html#tileProvider-com.amap.api.maps.model.TileProvider-">tileProvider(TileProvider)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/TileOverlayOptions.html" title="com.amap.api.maps.model中的类">TileOverlayOptions</a></dt>
<dd>
<div class="block">设置瓦片图层的提供者。</div>
</dd>
<dt><a href="../com/amap/api/maps/model/TileProvider.html" title="com.amap.api.maps.model中的接口"><span class="typeNameLink">TileProvider</span></a> - <a href="../com/amap/api/maps/model/package-summary.html">com.amap.api.maps.model</a>中的接口</dt>
<dd>
<div class="block">接口类，为类TileOverlay提供万片图像。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/TileOverlaySource.html#TILESOURCE_TYPE_FBO_TEXTURE">TILESOURCE_TYPE_FBO_TEXTURE</a></span> - 类 中的静态变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/TileOverlaySource.html" title="com.amap.api.maps.model中的类">TileOverlaySource</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/TileOverlaySource.html#TILESOURCE_TYPE_IMAGE">TILESOURCE_TYPE_IMAGE</a></span> - 类 中的静态变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/TileOverlaySource.html" title="com.amap.api.maps.model中的类">TileOverlaySource</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/TileOverlaySource.html#TILESOURCE_TYPE_IMAGE_DEM">TILESOURCE_TYPE_IMAGE_DEM</a></span> - 类 中的静态变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/TileOverlaySource.html" title="com.amap.api.maps.model中的类">TileOverlaySource</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/TileOverlaySource.html#TILESOURCE_TYPE_VECTOR">TILESOURCE_TYPE_VECTOR</a></span> - 类 中的静态变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/TileOverlaySource.html" title="com.amap.api.maps.model中的类">TileOverlaySource</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/CameraPosition.Builder.html#tilt-float-">tilt(float)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/CameraPosition.Builder.html" title="com.amap.api.maps.model中的类">CameraPosition.Builder</a></dt>
<dd>
<div class="block">设置目标可视区域的倾斜度</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/CameraPosition.html#tilt">tilt</a></span> - 类 中的变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/CameraPosition.html" title="com.amap.api.maps.model中的类">CameraPosition</a></dt>
<dd>
<div class="block">目标可视区域的倾斜度，以角度为单位。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMapOptions.html#tiltGesturesEnabled-boolean-">tiltGesturesEnabled(boolean)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMapOptions.html" title="com.amap.api.maps中的类">AMapOptions</a></dt>
<dd>
<div class="block">设置地图是否可以通过手势倾斜（3D效果），默认为true。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MarkerOptions.html#title-java.lang.String-">title(String)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MarkerOptions.html" title="com.amap.api.maps.model中的类">MarkerOptions</a></dt>
<dd>
<div class="block">设置 Marker覆盖物 的标题</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/Projection.html#toMapLocation-com.amap.api.maps.model.LatLng-">toMapLocation(LatLng)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/Projection.html" title="com.amap.api.maps中的类">Projection</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/Projection.html#toOpenGLLocation-com.amap.api.maps.model.LatLng-">toOpenGLLocation(LatLng)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/Projection.html" title="com.amap.api.maps中的类">Projection</a></dt>
<dd>
<div class="block">将地理坐标转换成openGL坐标，在<code>GLSurfaceView.Renderer.onDrawFrame(GL10)</code>中使用openGL坐标绘制。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/Projection.html#toOpenGLWidth-int-">toOpenGLWidth(int)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/Projection.html" title="com.amap.api.maps中的类">Projection</a></dt>
<dd>
<div class="block">返回一个屏幕宽度转换来的openGL 需要的宽度。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/NavigateArrowOptions.html#topColor-int-">topColor(int)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/NavigateArrowOptions.html" title="com.amap.api.maps.model中的类">NavigateArrowOptions</a></dt>
<dd>
<div class="block">设置箭头(NavigateArrow)覆盖物的顶颜色，需要传入32位的ARGB格式。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/Projection.html#toScreenLocation-com.amap.api.maps.model.LatLng-">toScreenLocation(LatLng)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/Projection.html" title="com.amap.api.maps中的类">Projection</a></dt>
<dd>
<div class="block">将地理坐标转换成屏幕坐标。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/AMapCameraInfo.html#toString--">toString()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/AMapCameraInfo.html" title="com.amap.api.maps.model中的类">AMapCameraInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/ColorLatLng.html#toString--">toString()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/ColorLatLng.html" title="com.amap.api.maps.model中的类">ColorLatLng</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/LatLngBounds.html#toString--">toString()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/LatLngBounds.html" title="com.amap.api.maps.model中的类">LatLngBounds</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/trace/TraceOverlay.html#TRACE_STATUS_FAILURE">TRACE_STATUS_FAILURE</a></span> - 类 中的静态变量com.amap.api.trace.<a href="../com/amap/api/trace/TraceOverlay.html" title="com.amap.api.trace中的类">TraceOverlay</a></dt>
<dd>
<div class="block">描述轨迹绘制状态，失败</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/trace/TraceOverlay.html#TRACE_STATUS_FINISH">TRACE_STATUS_FINISH</a></span> - 类 中的静态变量com.amap.api.trace.<a href="../com/amap/api/trace/TraceOverlay.html" title="com.amap.api.trace中的类">TraceOverlay</a></dt>
<dd>
<div class="block">描述轨迹绘制状态，绘制完成</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/trace/TraceOverlay.html#TRACE_STATUS_PREPARE">TRACE_STATUS_PREPARE</a></span> - 类 中的静态变量com.amap.api.trace.<a href="../com/amap/api/trace/TraceOverlay.html" title="com.amap.api.trace中的类">TraceOverlay</a></dt>
<dd>
<div class="block">描述轨迹绘制状态，准备（默认状态）</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/trace/TraceOverlay.html#TRACE_STATUS_PROCESSING">TRACE_STATUS_PROCESSING</a></span> - 类 中的静态变量com.amap.api.trace.<a href="../com/amap/api/trace/TraceOverlay.html" title="com.amap.api.trace中的类">TraceOverlay</a></dt>
<dd>
<div class="block">描述轨迹绘制状态，绘制过程中</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/trace/LBSTraceClient.html#TRACE_SUCCESS">TRACE_SUCCESS</a></span> - 类 中的静态变量com.amap.api.trace.<a href="../com/amap/api/trace/LBSTraceClient.html" title="com.amap.api.trace中的类">LBSTraceClient</a></dt>
<dd>
<div class="block">轨迹纠偏错误信息，纠偏运行正常</div>
</dd>
<dt><a href="../com/amap/api/trace/TraceListener.html" title="com.amap.api.trace中的接口"><span class="typeNameLink">TraceListener</span></a> - <a href="../com/amap/api/trace/package-summary.html">com.amap.api.trace</a>中的接口</dt>
<dd>
<div class="block">轨迹纠偏回调</div>
</dd>
<dt><a href="../com/amap/api/trace/TraceLocation.html" title="com.amap.api.trace中的类"><span class="typeNameLink">TraceLocation</span></a> - <a href="../com/amap/api/trace/package-summary.html">com.amap.api.trace</a>中的类</dt>
<dd>
<div class="block">用于轨迹纠偏的一个点的信息，目前该点需要：经度、纬度、速度、方向角（和gps返回一致）、定位时间</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/trace/TraceLocation.html#TraceLocation-double-double-float-float-long-">TraceLocation(double, double, float, float, long)</a></span> - 类 的构造器com.amap.api.trace.<a href="../com/amap/api/trace/TraceLocation.html" title="com.amap.api.trace中的类">TraceLocation</a></dt>
<dd>
<div class="block">构造方法</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/trace/TraceLocation.html#TraceLocation--">TraceLocation()</a></span> - 类 的构造器com.amap.api.trace.<a href="../com/amap/api/trace/TraceLocation.html" title="com.amap.api.trace中的类">TraceLocation</a></dt>
<dd>
<div class="block">默认构造方法</div>
</dd>
<dt><a href="../com/amap/api/trace/TraceOverlay.html" title="com.amap.api.trace中的类"><span class="typeNameLink">TraceOverlay</span></a> - <a href="../com/amap/api/trace/package-summary.html">com.amap.api.trace</a>中的类</dt>
<dd>
<div class="block">用于绘制轨迹纠偏接口回调的一条平滑轨迹</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/trace/TraceOverlay.html#TraceOverlay-com.amap.api.maps.AMap-java.util.List-">TraceOverlay(AMap, List&lt;LatLng&gt;)</a></span> - 类 的构造器com.amap.api.trace.<a href="../com/amap/api/trace/TraceOverlay.html" title="com.amap.api.trace中的类">TraceOverlay</a></dt>
<dd>
<div class="block">构造方法</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/trace/TraceOverlay.html#TraceOverlay-com.amap.api.maps.AMap-">TraceOverlay(AMap)</a></span> - 类 的构造器com.amap.api.trace.<a href="../com/amap/api/trace/TraceOverlay.html" title="com.amap.api.trace中的类">TraceOverlay</a></dt>
<dd>
<div class="block">构造方法</div>
</dd>
<dt><a href="../com/amap/api/trace/TraceStatusListener.html" title="com.amap.api.trace中的接口"><span class="typeNameLink">TraceStatusListener</span></a> - <a href="../com/amap/api/trace/package-summary.html">com.amap.api.trace</a>中的接口</dt>
<dd>
<div class="block">轨迹记录回调监听
 </div>
</dd>
<dt><a href="../com/amap/api/maps/model/animation/TranslateAnimation.html" title="com.amap.api.maps.model.animation中的类"><span class="typeNameLink">TranslateAnimation</span></a> - <a href="../com/amap/api/maps/model/animation/package-summary.html">com.amap.api.maps.model.animation</a>中的类</dt>
<dd>
<div class="block">控制移动的动画类</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/animation/TranslateAnimation.html#TranslateAnimation-com.amap.api.maps.model.LatLng-">TranslateAnimation(LatLng)</a></span> - 类 的构造器com.amap.api.maps.model.animation.<a href="../com/amap/api/maps/model/animation/TranslateAnimation.html" title="com.amap.api.maps.model.animation中的类">TranslateAnimation</a></dt>
<dd>
<div class="block">控制移动的动画类</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/GroundOverlayOptions.html#transparency-float-">transparency(float)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/GroundOverlayOptions.html" title="com.amap.api.maps.model中的类">GroundOverlayOptions</a></dt>
<dd>
<div class="block">设置ground 覆盖物的透明度。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/HeatmapTileProvider.Builder.html#transparency-double-">transparency(double)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/HeatmapTileProvider.Builder.html" title="com.amap.api.maps.model中的类">HeatmapTileProvider.Builder</a></dt>
<dd>
<div class="block">设置热力图层透明度，默认 0.6，可不设置该接口</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/PolylineOptions.html#transparency-float-">transparency(float)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/PolylineOptions.html" title="com.amap.api.maps.model中的类">PolylineOptions</a></dt>
<dd>
<div class="block">设置线段的透明度0~1，默认是1,1表示不透明</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/HeatMapGridLayerOptions.html#type-int-">type(int)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/HeatMapGridLayerOptions.html" title="com.amap.api.maps.model中的类">HeatMapGridLayerOptions</a></dt>
<dd>
<div class="block">设置热力图类型</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/HeatMapLayerOptions.html#type-int-">type(int)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/HeatMapLayerOptions.html" title="com.amap.api.maps.model中的类">HeatMapLayerOptions</a></dt>
<dd>
<div class="block">设置热力图类型</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/ImageOptions.html#type">type</a></span> - 类 中的变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/ImageOptions.html" title="com.amap.api.maps.model中的类">ImageOptions</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/particle/ColorGenerate.html#type">type</a></span> - 类 中的变量com.amap.api.maps.model.particle.<a href="../com/amap/api/maps/model/particle/ColorGenerate.html" title="com.amap.api.maps.model.particle中的类">ColorGenerate</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/particle/ParticleShapeModule.html#type">type</a></span> - 类 中的变量com.amap.api.maps.model.particle.<a href="../com/amap/api/maps/model/particle/ParticleShapeModule.html" title="com.amap.api.maps.model.particle中的类">ParticleShapeModule</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/particle/RotationOverLife.html#type">type</a></span> - 类 中的变量com.amap.api.maps.model.particle.<a href="../com/amap/api/maps/model/particle/RotationOverLife.html" title="com.amap.api.maps.model.particle中的类">RotationOverLife</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/particle/SizeOverLife.html#type">type</a></span> - 类 中的变量com.amap.api.maps.model.particle.<a href="../com/amap/api/maps/model/particle/SizeOverLife.html" title="com.amap.api.maps.model.particle中的类">SizeOverLife</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/particle/VelocityGenerate.html#type">type</a></span> - 类 中的变量com.amap.api.maps.model.particle.<a href="../com/amap/api/maps/model/particle/VelocityGenerate.html" title="com.amap.api.maps.model.particle中的类">VelocityGenerate</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/trace/LBSTraceClient.html#TYPE_AMAP">TYPE_AMAP</a></span> - 类 中的静态变量com.amap.api.trace.<a href="../com/amap/api/trace/LBSTraceClient.html" title="com.amap.api.trace中的类">LBSTraceClient</a></dt>
<dd>
<div class="block">用于<a href="../com/amap/api/trace/LBSTraceClient.html#queryProcessedTrace-int-java.util.List-int-com.amap.api.trace.TraceListener-"><code>LBSTraceClient.queryProcessedTrace(int, List, int, TraceListener)</code></a> type 坐标系类型为高德</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/trace/LBSTraceClient.html#TYPE_BAIDU">TYPE_BAIDU</a></span> - 类 中的静态变量com.amap.api.trace.<a href="../com/amap/api/trace/LBSTraceClient.html" title="com.amap.api.trace中的类">LBSTraceClient</a></dt>
<dd>
<div class="block">用于<a href="../com/amap/api/trace/LBSTraceClient.html#queryProcessedTrace-int-java.util.List-int-com.amap.api.trace.TraceListener-"><code>LBSTraceClient.queryProcessedTrace(int, List, int, TraceListener)</code></a> type 坐标系类型为百度</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/particle/RotationOverLife.html#TYPE_CONSTANTROTATIONOVERLIFE">TYPE_CONSTANTROTATIONOVERLIFE</a></span> - 类 中的变量com.amap.api.maps.model.particle.<a href="../com/amap/api/maps/model/particle/RotationOverLife.html" title="com.amap.api.maps.model.particle中的类">RotationOverLife</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/particle/SizeOverLife.html#TYPE_CURVESIZEOVERLIFE">TYPE_CURVESIZEOVERLIFE</a></span> - 类 中的变量com.amap.api.maps.model.particle.<a href="../com/amap/api/maps/model/particle/SizeOverLife.html" title="com.amap.api.maps.model.particle中的类">SizeOverLife</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/particle/ColorGenerate.html#TYPE_DEFAULT">TYPE_DEFAULT</a></span> - 类 中的变量com.amap.api.maps.model.particle.<a href="../com/amap/api/maps/model/particle/ColorGenerate.html" title="com.amap.api.maps.model.particle中的类">ColorGenerate</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/particle/ParticleShapeModule.html#TYPE_DEFAULT">TYPE_DEFAULT</a></span> - 类 中的变量com.amap.api.maps.model.particle.<a href="../com/amap/api/maps/model/particle/ParticleShapeModule.html" title="com.amap.api.maps.model.particle中的类">ParticleShapeModule</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/particle/RotationOverLife.html#TYPE_DEFAULT">TYPE_DEFAULT</a></span> - 类 中的变量com.amap.api.maps.model.particle.<a href="../com/amap/api/maps/model/particle/RotationOverLife.html" title="com.amap.api.maps.model.particle中的类">RotationOverLife</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/particle/SizeOverLife.html#TYPE_DEFAULT">TYPE_DEFAULT</a></span> - 类 中的变量com.amap.api.maps.model.particle.<a href="../com/amap/api/maps/model/particle/SizeOverLife.html" title="com.amap.api.maps.model.particle中的类">SizeOverLife</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/particle/VelocityGenerate.html#TYPE_DEFAULT">TYPE_DEFAULT</a></span> - 类 中的变量com.amap.api.maps.model.particle.<a href="../com/amap/api/maps/model/particle/VelocityGenerate.html" title="com.amap.api.maps.model.particle中的类">VelocityGenerate</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/trace/LBSTraceClient.html#TYPE_GPS">TYPE_GPS</a></span> - 类 中的静态变量com.amap.api.trace.<a href="../com/amap/api/trace/LBSTraceClient.html" title="com.amap.api.trace中的类">LBSTraceClient</a></dt>
<dd>
<div class="block">用于<a href="../com/amap/api/trace/LBSTraceClient.html#queryProcessedTrace-int-java.util.List-int-com.amap.api.trace.TraceListener-"><code>LBSTraceClient.queryProcessedTrace(int, List, int, TraceListener)</code></a> type 坐标系类型为GPS原始坐标</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/HeatMapGridLayerOptions.html#TYPE_GRID">TYPE_GRID</a></span> - 类 中的静态变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/HeatMapGridLayerOptions.html" title="com.amap.api.maps.model中的类">HeatMapGridLayerOptions</a></dt>
<dd>
<div class="block">热力图层类型-网格热力图</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/HeatMapLayerOptions.html#TYPE_GRID">TYPE_GRID</a></span> - 类 中的静态变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/HeatMapLayerOptions.html" title="com.amap.api.maps.model中的类">HeatMapLayerOptions</a></dt>
<dd>
<div class="block">热力图层类型-网格热力图</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/HeatMapGridLayerOptions.html#TYPE_HEXAGON">TYPE_HEXAGON</a></span> - 类 中的静态变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/HeatMapGridLayerOptions.html" title="com.amap.api.maps.model中的类">HeatMapGridLayerOptions</a></dt>
<dd>
<div class="block">热力图层类型-蜂巢热力图</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/HeatMapLayerOptions.html#TYPE_HEXAGON">TYPE_HEXAGON</a></span> - 类 中的静态变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/HeatMapLayerOptions.html" title="com.amap.api.maps.model中的类">HeatMapLayerOptions</a></dt>
<dd>
<div class="block">热力图层类型-蜂巢热力图</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/HeatMapGridLayerOptions.html#TYPE_NORMAL">TYPE_NORMAL</a></span> - 类 中的静态变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/HeatMapGridLayerOptions.html" title="com.amap.api.maps.model中的类">HeatMapGridLayerOptions</a></dt>
<dd>
<div class="block">热力图层类型-普通热力图（暂不支持）</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/HeatMapLayerOptions.html#TYPE_NORMAL">TYPE_NORMAL</a></span> - 类 中的静态变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/HeatMapLayerOptions.html" title="com.amap.api.maps.model中的类">HeatMapLayerOptions</a></dt>
<dd>
<div class="block">热力图层类型-普通热力图（暂不支持）</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/particle/ColorGenerate.html#TYPE_RANDOMCOLORBETWEENTWOCONSTANTS">TYPE_RANDOMCOLORBETWEENTWOCONSTANTS</a></span> - 类 中的变量com.amap.api.maps.model.particle.<a href="../com/amap/api/maps/model/particle/ColorGenerate.html" title="com.amap.api.maps.model.particle中的类">ColorGenerate</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/particle/VelocityGenerate.html#TYPE_RANDOMVELOCITYBETWEENTWOCONSTANTS">TYPE_RANDOMVELOCITYBETWEENTWOCONSTANTS</a></span> - 类 中的变量com.amap.api.maps.model.particle.<a href="../com/amap/api/maps/model/particle/VelocityGenerate.html" title="com.amap.api.maps.model.particle中的类">VelocityGenerate</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/particle/ParticleShapeModule.html#TYPE_RECT">TYPE_RECT</a></span> - 类 中的变量com.amap.api.maps.model.particle.<a href="../com/amap/api/maps/model/particle/ParticleShapeModule.html" title="com.amap.api.maps.model.particle中的类">ParticleShapeModule</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/particle/ParticleShapeModule.html#TYPE_SINGLEPOINT">TYPE_SINGLEPOINT</a></span> - 类 中的变量com.amap.api.maps.model.particle.<a href="../com/amap/api/maps/model/particle/ParticleShapeModule.html" title="com.amap.api.maps.model.particle中的类">ParticleShapeModule</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/TextOptions.html#typeface-android.graphics.Typeface-">typeface(Typeface)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/TextOptions.html" title="com.amap.api.maps.model中的类">TextOptions</a></dt>
<dd>
<div class="block">设置文字覆盖物的字体。</div>
</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">K</a>&nbsp;<a href="index-11.html">L</a>&nbsp;<a href="index-12.html">M</a>&nbsp;<a href="index-13.html">N</a>&nbsp;<a href="index-14.html">O</a>&nbsp;<a href="index-15.html">P</a>&nbsp;<a href="index-16.html">Q</a>&nbsp;<a href="index-17.html">R</a>&nbsp;<a href="index-18.html">S</a>&nbsp;<a href="index-19.html">T</a>&nbsp;<a href="index-20.html">U</a>&nbsp;<a href="index-21.html">V</a>&nbsp;<a href="index-22.html">W</a>&nbsp;<a href="index-23.html">Z</a>&nbsp;</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-18.html">上一个字母</a></li>
<li><a href="index-20.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-19.html" target="_top">框架</a></li>
<li><a href="index-19.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
