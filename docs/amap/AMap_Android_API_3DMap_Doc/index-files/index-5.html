<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>E - 索引</title>
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="E - \u7D22\u5F15";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-4.html">上一个字母</a></li>
<li><a href="index-6.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-5.html" target="_top">框架</a></li>
<li><a href="index-5.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="contentContainer"><a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">K</a>&nbsp;<a href="index-11.html">L</a>&nbsp;<a href="index-12.html">M</a>&nbsp;<a href="index-13.html">N</a>&nbsp;<a href="index-14.html">O</a>&nbsp;<a href="index-15.html">P</a>&nbsp;<a href="index-16.html">Q</a>&nbsp;<a href="index-17.html">R</a>&nbsp;<a href="index-18.html">S</a>&nbsp;<a href="index-19.html">T</a>&nbsp;<a href="index-20.html">U</a>&nbsp;<a href="index-21.html">V</a>&nbsp;<a href="index-22.html">W</a>&nbsp;<a href="index-23.html">Z</a>&nbsp;<a name="I:E">
<!--   -->
</a>
<h2 class="title">E</h2>
<dl>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Arc.html#equals-java.lang.Object-">equals(Object)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Arc.html" title="com.amap.api.maps.model中的类">Arc</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Circle.html#equals-java.lang.Object-">equals(Object)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Circle.html" title="com.amap.api.maps.model中的类">Circle</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/ColorLatLng.html#equals-java.lang.Object-">equals(Object)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/ColorLatLng.html" title="com.amap.api.maps.model中的类">ColorLatLng</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/HeatMapGridLayer.html#equals-java.lang.Object-">equals(Object)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/HeatMapGridLayer.html" title="com.amap.api.maps.model中的类">HeatMapGridLayer</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/HeatMapLayer.html#equals-java.lang.Object-">equals(Object)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/HeatMapLayer.html" title="com.amap.api.maps.model中的类">HeatMapLayer</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/LatLng.html#equals-java.lang.Object-">equals(Object)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/LatLng.html" title="com.amap.api.maps.model中的类">LatLng</a></dt>
<dd>
<div class="block">判断是否与另一个LatLng 对象相等的方法。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/LatLngBounds.html#equals-java.lang.Object-">equals(Object)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/LatLngBounds.html" title="com.amap.api.maps.model中的类">LatLngBounds</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Marker.html#equals-java.lang.Object-">equals(Object)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Marker.html" title="com.amap.api.maps.model中的类">Marker</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/NavigateArrow.html#equals-java.lang.Object-">equals(Object)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/NavigateArrow.html" title="com.amap.api.maps.model中的类">NavigateArrow</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Polygon.html#equals-java.lang.Object-">equals(Object)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Polygon.html" title="com.amap.api.maps.model中的类">Polygon</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Polyline.html#equals-java.lang.Object-">equals(Object)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Polyline.html" title="com.amap.api.maps.model中的类">Polyline</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/TileOverlay.html#equals-java.lang.Object-">equals(Object)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/TileOverlay.html" title="com.amap.api.maps.model中的类">TileOverlay</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/VisibleRegion.html#equals-java.lang.Object-">equals(Object)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/VisibleRegion.html" title="com.amap.api.maps.model中的类">VisibleRegion</a></dt>
<dd>
<div class="block">将这个可见区域和另个对象进行比较。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/OfflineMapStatus.html#ERROR">ERROR</a></span> - 类 中的静态变量com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/OfflineMapStatus.html" title="com.amap.api.maps.offlinemap中的类">OfflineMapStatus</a></dt>
<dd>
<div class="block">解压失败错误，数据有可能有问题，所以重新下载</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MyLocationStyle.html#ERROR_CODE">ERROR_CODE</a></span> - 类 中的静态变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MyLocationStyle.html" title="com.amap.api.maps.model中的类">MyLocationStyle</a></dt>
<dd>
<div class="block">在<a href="../com/amap/api/maps/AMap.OnMyLocationChangeListener.html#onMyLocationChange-android.location.Location-"><code>AMap.OnMyLocationChangeListener.onMyLocationChange(Location)</code></a> 中会返回定位信息<br>
     可通过 Location.getExtras().getInt(<a href="../com/amap/api/maps/model/MyLocationStyle.html#ERROR_CODE"><code>MyLocationStyle.ERROR_CODE</code></a>) 获取错误码</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMapException.html#ERROR_CONNECTION">ERROR_CONNECTION</a></span> - 异常错误 中的静态变量com.amap.api.maps.<a href="../com/amap/api/maps/AMapException.html" title="com.amap.api.maps中的类">AMapException</a></dt>
<dd>
<div class="block">http连接失败。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMapException.html#ERROR_FAILURE_AUTH">ERROR_FAILURE_AUTH</a></span> - 异常错误 中的静态变量com.amap.api.maps.<a href="../com/amap/api/maps/AMapException.html" title="com.amap.api.maps中的类">AMapException</a></dt>
<dd>
<div class="block">key鉴权失败。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMapException.html#ERROR_FAILURE_OVERSEA_AUTH">ERROR_FAILURE_OVERSEA_AUTH</a></span> - 异常错误 中的静态变量com.amap.api.maps.<a href="../com/amap/api/maps/AMapException.html" title="com.amap.api.maps中的类">AMapException</a></dt>
<dd>
<div class="block">海外鉴权失败</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMapException.html#ERROR_ILLEGAL_VALUE">ERROR_ILLEGAL_VALUE</a></span> - 异常错误 中的静态变量com.amap.api.maps.<a href="../com/amap/api/maps/AMapException.html" title="com.amap.api.maps中的类">AMapException</a></dt>
<dd>
<div class="block">非法坐标值</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MyLocationStyle.html#ERROR_INFO">ERROR_INFO</a></span> - 类 中的静态变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MyLocationStyle.html" title="com.amap.api.maps.model中的类">MyLocationStyle</a></dt>
<dd>
<div class="block">在<a href="../com/amap/api/maps/AMap.OnMyLocationChangeListener.html#onMyLocationChange-android.location.Location-"><code>AMap.OnMyLocationChangeListener.onMyLocationChange(Location)</code></a> 中会返回定位信息<br>
     可通过 Location.getExtras().getString(<a href="../com/amap/api/maps/model/MyLocationStyle.html#ERROR_INFO"><code>MyLocationStyle.ERROR_INFO</code></a>) 获取错误详细信息</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMapException.html#ERROR_INVALID_PARAMETER">ERROR_INVALID_PARAMETER</a></span> - 异常错误 中的静态变量com.amap.api.maps.<a href="../com/amap/api/maps/AMapException.html" title="com.amap.api.maps中的类">AMapException</a></dt>
<dd>
<div class="block">无效的参数。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMapException.html#ERROR_IO">ERROR_IO</a></span> - 异常错误 中的静态变量com.amap.api.maps.<a href="../com/amap/api/maps/AMapException.html" title="com.amap.api.maps中的类">AMapException</a></dt>
<dd>
<div class="block">输入输出异常。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMapException.html#ERROR_NOT_AVAILABLE">ERROR_NOT_AVAILABLE</a></span> - 异常错误 中的静态变量com.amap.api.maps.<a href="../com/amap/api/maps/AMapException.html" title="com.amap.api.maps中的类">AMapException</a></dt>
<dd>
<div class="block">不可写入异常</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMapException.html#ERROR_NOT_ENOUGH_SPACE">ERROR_NOT_ENOUGH_SPACE</a></span> - 异常错误 中的静态变量com.amap.api.maps.<a href="../com/amap/api/maps/AMapException.html" title="com.amap.api.maps中的类">AMapException</a></dt>
<dd>
<div class="block">空间不足</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMapException.html#ERROR_NULL_PARAMETER">ERROR_NULL_PARAMETER</a></span> - 异常错误 中的静态变量com.amap.api.maps.<a href="../com/amap/api/maps/AMapException.html" title="com.amap.api.maps中的类">AMapException</a></dt>
<dd>
<div class="block">空指针异常。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMapException.html#ERROR_PROTOCOL">ERROR_PROTOCOL</a></span> - 异常错误 中的静态变量com.amap.api.maps.<a href="../com/amap/api/maps/AMapException.html" title="com.amap.api.maps中的类">AMapException</a></dt>
<dd>
<div class="block">协议解析错误。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMapException.html#ERROR_SOCKE_TIME_OUT">ERROR_SOCKE_TIME_OUT</a></span> - 异常错误 中的静态变量com.amap.api.maps.<a href="../com/amap/api/maps/AMapException.html" title="com.amap.api.maps中的类">AMapException</a></dt>
<dd>
<div class="block">socket 连接超时。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMapException.html#ERROR_SOCKET">ERROR_SOCKET</a></span> - 异常错误 中的静态变量com.amap.api.maps.<a href="../com/amap/api/maps/AMapException.html" title="com.amap.api.maps中的类">AMapException</a></dt>
<dd>
<div class="block">socket 连接异常。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMapException.html#ERROR_UNKNOW_HOST">ERROR_UNKNOW_HOST</a></span> - 异常错误 中的静态变量com.amap.api.maps.<a href="../com/amap/api/maps/AMapException.html" title="com.amap.api.maps中的类">AMapException</a></dt>
<dd>
<div class="block">未知主机。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMapException.html#ERROR_UNKNOW_SERVICE">ERROR_UNKNOW_SERVICE</a></span> - 异常错误 中的静态变量com.amap.api.maps.<a href="../com/amap/api/maps/AMapException.html" title="com.amap.api.maps中的类">AMapException</a></dt>
<dd>
<div class="block">服务器连接失败。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMapException.html#ERROR_UNKNOWN">ERROR_UNKNOWN</a></span> - 异常错误 中的静态变量com.amap.api.maps.<a href="../com/amap/api/maps/AMapException.html" title="com.amap.api.maps中的类">AMapException</a></dt>
<dd>
<div class="block">未知的错误。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMapException.html#ERROR_URL">ERROR_URL</a></span> - 异常错误 中的静态变量com.amap.api.maps.<a href="../com/amap/api/maps/AMapException.html" title="com.amap.api.maps中的类">AMapException</a></dt>
<dd>
<div class="block">url异常。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/OfflineMapStatus.html#EXCEPTION_AMAP">EXCEPTION_AMAP</a></span> - 类 中的静态变量com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/OfflineMapStatus.html" title="com.amap.api.maps.offlinemap中的类">OfflineMapStatus</a></dt>
<dd>
<div class="block">AMap认证等异常，请检查key，下次还可以继续下载</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/OfflineMapStatus.html#EXCEPTION_NETWORK_LOADING">EXCEPTION_NETWORK_LOADING</a></span> - 类 中的静态变量com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/OfflineMapStatus.html" title="com.amap.api.maps.offlinemap中的类">OfflineMapStatus</a></dt>
<dd>
<div class="block">下载过程中网络问题，不属于错误，下次还可以继续下载</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/OfflineMapStatus.html#EXCEPTION_SDCARD">EXCEPTION_SDCARD</a></span> - 类 中的静态变量com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/OfflineMapStatus.html" title="com.amap.api.maps.offlinemap中的类">OfflineMapStatus</a></dt>
<dd>
<div class="block">SD卡读写异常,下载过程有写入文件，解压过程也有写入文件<br>
 即出现IOexception</div>
</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">K</a>&nbsp;<a href="index-11.html">L</a>&nbsp;<a href="index-12.html">M</a>&nbsp;<a href="index-13.html">N</a>&nbsp;<a href="index-14.html">O</a>&nbsp;<a href="index-15.html">P</a>&nbsp;<a href="index-16.html">Q</a>&nbsp;<a href="index-17.html">R</a>&nbsp;<a href="index-18.html">S</a>&nbsp;<a href="index-19.html">T</a>&nbsp;<a href="index-20.html">U</a>&nbsp;<a href="index-21.html">V</a>&nbsp;<a href="index-22.html">W</a>&nbsp;<a href="index-23.html">Z</a>&nbsp;</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-4.html">上一个字母</a></li>
<li><a href="index-6.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-5.html" target="_top">框架</a></li>
<li><a href="index-5.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
