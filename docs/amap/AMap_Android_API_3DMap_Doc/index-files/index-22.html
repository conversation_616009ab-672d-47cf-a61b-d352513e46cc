<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>W - 索引</title>
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="W - \u7D22\u5F15";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-21.html">上一个字母</a></li>
<li><a href="index-23.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-22.html" target="_top">框架</a></li>
<li><a href="index-22.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="contentContainer"><a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">K</a>&nbsp;<a href="index-11.html">L</a>&nbsp;<a href="index-12.html">M</a>&nbsp;<a href="index-13.html">N</a>&nbsp;<a href="index-14.html">O</a>&nbsp;<a href="index-15.html">P</a>&nbsp;<a href="index-16.html">Q</a>&nbsp;<a href="index-17.html">R</a>&nbsp;<a href="index-18.html">S</a>&nbsp;<a href="index-19.html">T</a>&nbsp;<a href="index-20.html">U</a>&nbsp;<a href="index-21.html">V</a>&nbsp;<a href="index-22.html">W</a>&nbsp;<a href="index-23.html">Z</a>&nbsp;<a name="I:W">
<!--   -->
</a>
<h2 class="title">W</h2>
<dl>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/OfflineMapStatus.html#WAITING">WAITING</a></span> - 类 中的静态变量com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/OfflineMapStatus.html" title="com.amap.api.maps.offlinemap中的类">OfflineMapStatus</a></dt>
<dd>
<div class="block">等待下载。</div>
</dd>
<dt><a href="../com/amap/api/maps/WearMapView.html" title="com.amap.api.maps中的类"><span class="typeNameLink">WearMapView</span></a> - <a href="../com/amap/api/maps/package-summary.html">com.amap.api.maps</a>中的类</dt>
<dd>
<div class="block">一个显示手表地图的视图（View）。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/WearMapView.html#WearMapView-android.content.Context-">WearMapView(Context)</a></span> - 类 的构造器com.amap.api.maps.<a href="../com/amap/api/maps/WearMapView.html" title="com.amap.api.maps中的类">WearMapView</a></dt>
<dd>
<div class="block">根据给定的参数构造一个WearMapView 的新对象。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/WearMapView.html#WearMapView-android.content.Context-android.util.AttributeSet-">WearMapView(Context, AttributeSet)</a></span> - 类 的构造器com.amap.api.maps.<a href="../com/amap/api/maps/WearMapView.html" title="com.amap.api.maps中的类">WearMapView</a></dt>
<dd>
<div class="block">根据给定的参数构造一个WearMapView的新对象。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/WearMapView.html#WearMapView-android.content.Context-android.util.AttributeSet-int-">WearMapView(Context, AttributeSet, int)</a></span> - 类 的构造器com.amap.api.maps.<a href="../com/amap/api/maps/WearMapView.html" title="com.amap.api.maps中的类">WearMapView</a></dt>
<dd>
<div class="block">根据给定的参数构造一个WearMapView的新对象。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/WearMapView.html#WearMapView-android.content.Context-com.amap.api.maps.AMapOptions-">WearMapView(Context, AMapOptions)</a></span> - 类 的构造器com.amap.api.maps.<a href="../com/amap/api/maps/WearMapView.html" title="com.amap.api.maps中的类">WearMapView</a></dt>
<dd>
<div class="block">根据给定的参数构造一个WearMapView的新对象。</div>
</dd>
<dt><a href="../com/amap/api/maps/WearMapView.OnDismissCallback.html" title="com.amap.api.maps中的接口"><span class="typeNameLink">WearMapView.OnDismissCallback</span></a> - <a href="../com/amap/api/maps/package-summary.html">com.amap.api.maps</a>中的接口</dt>
<dd>
<div class="block">手势滑动监听</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/HeatMapLayerOptions.html#weightedData-java.util.Collection-">weightedData(Collection&lt;WeightedLatLng&gt;)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/HeatMapLayerOptions.html" title="com.amap.api.maps.model中的类">HeatMapLayerOptions</a></dt>
<dd>
<div class="block">设置热力图绘制的数据，带权值的位置点集合，data 或 weightedData接口必须设置其中之一</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/HeatmapTileProvider.Builder.html#weightedData-java.util.Collection-">weightedData(Collection&lt;WeightedLatLng&gt;)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/HeatmapTileProvider.Builder.html" title="com.amap.api.maps.model中的类">HeatmapTileProvider.Builder</a></dt>
<dd>
<div class="block">设置热力图绘制的数据，带权值的位置点集合，data 或 weightedData接口必须设置其中之一</div>
</dd>
<dt><a href="../com/amap/api/maps/model/WeightedLatLng.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">WeightedLatLng</span></a> - <a href="../com/amap/api/maps/model/package-summary.html">com.amap.api.maps.model</a>中的类</dt>
<dd>
<div class="block">带权值的经纬度位置点</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/WeightedLatLng.html#WeightedLatLng-com.amap.api.maps.model.LatLng-double-">WeightedLatLng(LatLng, double)</a></span> - 类 的构造器com.amap.api.maps.model.<a href="../com/amap/api/maps/model/WeightedLatLng.html" title="com.amap.api.maps.model中的类">WeightedLatLng</a></dt>
<dd>
<div class="block">构造函数</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/WeightedLatLng.html#WeightedLatLng-com.amap.api.maps.model.LatLng-">WeightedLatLng(LatLng)</a></span> - 类 的构造器com.amap.api.maps.model.<a href="../com/amap/api/maps/model/WeightedLatLng.html" title="com.amap.api.maps.model中的类">WeightedLatLng</a></dt>
<dd>
<div class="block">构造函数，使用默认权值</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/NavigateArrowOptions.html#width-float-">width(float)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/NavigateArrowOptions.html" title="com.amap.api.maps.model中的类">NavigateArrowOptions</a></dt>
<dd>
<div class="block">设置箭头(NavigateArrow)覆盖物的宽度，单位像素。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/PolylineOptions.html#width-float-">width(float)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/PolylineOptions.html" title="com.amap.api.maps.model中的类">PolylineOptions</a></dt>
<dd>
<div class="block">设置线段的宽度，默认为10。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Tile.html#width">width</a></span> - 类 中的变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Tile.html" title="com.amap.api.maps.model中的类">Tile</a></dt>
<dd>
<div class="block">编码的图片像素宽度，单位像素。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/ArcOptions.html#writeToParcel-android.os.Parcel-int-">writeToParcel(Parcel, int)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/ArcOptions.html" title="com.amap.api.maps.model中的类">ArcOptions</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/GLTFOverlayOptions.html#writeToParcel-android.os.Parcel-int-">writeToParcel(Parcel, int)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/GLTFOverlayOptions.html" title="com.amap.api.maps.model中的类">GLTFOverlayOptions</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MVTTileOverlayOptions.html#writeToParcel-android.os.Parcel-int-">writeToParcel(Parcel, int)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MVTTileOverlayOptions.html" title="com.amap.api.maps.model中的类">MVTTileOverlayOptions</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/TextOptions.html#writeToParcel-android.os.Parcel-int-">writeToParcel(Parcel, int)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/TextOptions.html" title="com.amap.api.maps.model中的类">TextOptions</a></dt>
<dd>&nbsp;</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">K</a>&nbsp;<a href="index-11.html">L</a>&nbsp;<a href="index-12.html">M</a>&nbsp;<a href="index-13.html">N</a>&nbsp;<a href="index-14.html">O</a>&nbsp;<a href="index-15.html">P</a>&nbsp;<a href="index-16.html">Q</a>&nbsp;<a href="index-17.html">R</a>&nbsp;<a href="index-18.html">S</a>&nbsp;<a href="index-19.html">T</a>&nbsp;<a href="index-20.html">U</a>&nbsp;<a href="index-21.html">V</a>&nbsp;<a href="index-22.html">W</a>&nbsp;<a href="index-23.html">Z</a>&nbsp;</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-21.html">上一个字母</a></li>
<li><a href="index-23.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-22.html" target="_top">框架</a></li>
<li><a href="index-22.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
