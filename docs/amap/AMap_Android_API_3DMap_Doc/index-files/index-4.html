<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>D - 索引</title>
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="D - \u7D22\u5F15";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-3.html">上一个字母</a></li>
<li><a href="index-5.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-4.html" target="_top">框架</a></li>
<li><a href="index-4.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="contentContainer"><a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">K</a>&nbsp;<a href="index-11.html">L</a>&nbsp;<a href="index-12.html">M</a>&nbsp;<a href="index-13.html">N</a>&nbsp;<a href="index-14.html">O</a>&nbsp;<a href="index-15.html">P</a>&nbsp;<a href="index-16.html">Q</a>&nbsp;<a href="index-17.html">R</a>&nbsp;<a href="index-18.html">S</a>&nbsp;<a href="index-19.html">T</a>&nbsp;<a href="index-20.html">U</a>&nbsp;<a href="index-21.html">V</a>&nbsp;<a href="index-22.html">W</a>&nbsp;<a href="index-23.html">Z</a>&nbsp;<a name="I:D">
<!--   -->
</a>
<h2 class="title">D</h2>
<dl>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/HeatMapGridLayerOptions.html#data-java.util.List-">data(List&lt;ColorLatLng&gt;)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/HeatMapGridLayerOptions.html" title="com.amap.api.maps.model中的类">HeatMapGridLayerOptions</a></dt>
<dd>
<div class="block">设置热力图绘制的数据，data</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/HeatMapLayerOptions.html#data-java.util.Collection-">data(Collection&lt;LatLng&gt;)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/HeatMapLayerOptions.html" title="com.amap.api.maps.model中的类">HeatMapLayerOptions</a></dt>
<dd>
<div class="block">设置热力图绘制的数据，data 或 weightedData接口必须设置其中之一</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/HeatmapTileProvider.Builder.html#data-java.util.Collection-">data(Collection&lt;LatLng&gt;)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/HeatmapTileProvider.Builder.html" title="com.amap.api.maps.model中的类">HeatmapTileProvider.Builder</a></dt>
<dd>
<div class="block">设置热力图绘制的数据，data 或 weightedData接口必须设置其中之一</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Tile.html#data">data</a></span> - 类 中的变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Tile.html" title="com.amap.api.maps.model中的类">Tile</a></dt>
<dd>
<div class="block">包含图片数据的字节数组，这个图片可以通过调用类BitmapFactory的decodeByteArray(byte[], int, int)方法来创建。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/CrossOverlay.UpdateItem.html#dataUpdateFlag">dataUpdateFlag</a></span> - 类 中的变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/CrossOverlay.UpdateItem.html" title="com.amap.api.maps.model中的类">CrossOverlay.UpdateItem</a></dt>
<dd>
<div class="block">设置路口放大图数据</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/LocationSource.html#deactivate--">deactivate()</a></span> - 接口 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/LocationSource.html" title="com.amap.api.maps中的接口">LocationSource</a></dt>
<dd>
<div class="block">停止定位。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/HeatMapLayerOptions.html#DEFAULT_GRADIENT">DEFAULT_GRADIENT</a></span> - 类 中的静态变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/HeatMapLayerOptions.html" title="com.amap.api.maps.model中的类">HeatMapLayerOptions</a></dt>
<dd>
<div class="block">热力图默认渐变</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/HeatmapTileProvider.html#DEFAULT_GRADIENT">DEFAULT_GRADIENT</a></span> - 类 中的静态变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/HeatmapTileProvider.html" title="com.amap.api.maps.model中的类">HeatmapTileProvider</a></dt>
<dd>
<div class="block">热力图默认渐变</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/WeightedLatLng.html#DEFAULT_INTENSITY">DEFAULT_INTENSITY</a></span> - 类 中的静态变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/WeightedLatLng.html" title="com.amap.api.maps.model中的类">WeightedLatLng</a></dt>
<dd>
<div class="block">位置点默认权值1.0</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/HeatMapLayerOptions.html#DEFAULT_OPACITY">DEFAULT_OPACITY</a></span> - 类 中的静态变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/HeatMapLayerOptions.html" title="com.amap.api.maps.model中的类">HeatMapLayerOptions</a></dt>
<dd>
<div class="block">热力图层默认透明度</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/HeatmapTileProvider.html#DEFAULT_OPACITY">DEFAULT_OPACITY</a></span> - 类 中的静态变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/HeatmapTileProvider.html" title="com.amap.api.maps.model中的类">HeatmapTileProvider</a></dt>
<dd>
<div class="block">热力图层默认透明度</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/HeatMapLayerOptions.html#DEFAULT_RADIUS">DEFAULT_RADIUS</a></span> - 类 中的静态变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/HeatMapLayerOptions.html" title="com.amap.api.maps.model中的类">HeatMapLayerOptions</a></dt>
<dd>
<div class="block">热力图默认的点半径</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/HeatmapTileProvider.html#DEFAULT_RADIUS">DEFAULT_RADIUS</a></span> - 类 中的静态变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/HeatmapTileProvider.html" title="com.amap.api.maps.model中的类">HeatmapTileProvider</a></dt>
<dd>
<div class="block">热力图默认的点半径</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/BitmapDescriptorFactory.html#defaultMarker--">defaultMarker()</a></span> - 类 中的静态方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/BitmapDescriptorFactory.html" title="com.amap.api.maps.model中的类">BitmapDescriptorFactory</a></dt>
<dd>
<div class="block">创建默认的marker 图标的 bitmap 描述信息对象。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/BitmapDescriptorFactory.html#defaultMarker-float-">defaultMarker(float)</a></span> - 类 中的静态方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/BitmapDescriptorFactory.html" title="com.amap.api.maps.model中的类">BitmapDescriptorFactory</a></dt>
<dd>
<div class="block">API 提供了10 个颜色的Marker 图标，用户可以通过此方法传入值来调用。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/particle/ParticleOverlayOptionsFactory.html#defaultOptions-int-">defaultOptions(int)</a></span> - 类 中的静态方法com.amap.api.maps.model.particle.<a href="../com/amap/api/maps/model/particle/ParticleOverlayOptionsFactory.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlayOptionsFactory</a></dt>
<dd>
<div class="block">默认粒子效果</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/GLTFOverlayOptions.html#describeContents--">describeContents()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/GLTFOverlayOptions.html" title="com.amap.api.maps.model中的类">GLTFOverlayOptions</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MVTTileOverlayOptions.html#describeContents--">describeContents()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MVTTileOverlayOptions.html" title="com.amap.api.maps.model中的类">MVTTileOverlayOptions</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/TextOptions.html#describeContents--">describeContents()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/TextOptions.html" title="com.amap.api.maps.model中的类">TextOptions</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/AMapGLOverlay.html#destroy--">destroy()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/AMapGLOverlay.html" title="com.amap.api.maps.model中的类">AMapGLOverlay</a></dt>
<dd>
<div class="block">销毁自己本身</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/BasePointOverlay.html#destroy--">destroy()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/BasePointOverlay.html" title="com.amap.api.maps.model中的类">BasePointOverlay</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/BuildingOverlay.html#destroy--">destroy()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/BuildingOverlay.html" title="com.amap.api.maps.model中的类">BuildingOverlay</a></dt>
<dd>
<div class="block">销毁添加的overlay。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/GroundOverlay.html#destroy--">destroy()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/GroundOverlay.html" title="com.amap.api.maps.model中的类">GroundOverlay</a></dt>
<dd>
<div class="block">是否图片资源</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/HeatMapGridLayer.html#destroy--">destroy()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/HeatMapGridLayer.html" title="com.amap.api.maps.model中的类">HeatMapGridLayer</a></dt>
<dd>
<div class="block">从地图上删除图层</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/HeatMapLayer.html#destroy--">destroy()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/HeatMapLayer.html" title="com.amap.api.maps.model中的类">HeatMapLayer</a></dt>
<dd>
<div class="block">从地图上删除图层</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Marker.html#destroy--">destroy()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Marker.html" title="com.amap.api.maps.model中的类">Marker</a></dt>
<dd>
<div class="block">删除当前marker并销毁Marker的图片等资源。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MultiPointOverlay.html#destroy--">destroy()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MultiPointOverlay.html" title="com.amap.api.maps.model中的类">MultiPointOverlay</a></dt>
<dd>
<div class="block">海量点图层销毁</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/particle/ParticleOverlay.html#destroy--">destroy()</a></span> - 类 中的方法com.amap.api.maps.model.particle.<a href="../com/amap/api/maps/model/particle/ParticleOverlay.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlay</a></dt>
<dd>
<div class="block">销毁粒子效果</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Text.html#destroy--">destroy()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Text.html" title="com.amap.api.maps.model中的类">Text</a></dt>
<dd>
<div class="block">销毁文字覆盖物的图片等资源</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/OfflineMapManager.html#destroy--">destroy()</a></span> - 类 中的方法com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/OfflineMapManager.html" title="com.amap.api.maps.offlinemap中的类">OfflineMapManager</a></dt>
<dd>
<div class="block">销毁offlineManager中的资源</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/utils/overlay/MovingPointOverlay.html#destroy--">destroy()</a></span> - 类 中的方法com.amap.api.maps.utils.overlay.<a href="../com/amap/api/maps/utils/overlay/MovingPointOverlay.html" title="com.amap.api.maps.utils.overlay中的类">MovingPointOverlay</a></dt>
<dd>
<div class="block">停止滑动并销毁Marker</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/utils/overlay/SmoothMoveMarker.html#destroy--">destroy()</a></span> - 类 中的方法com.amap.api.maps.utils.overlay.<a href="../com/amap/api/maps/utils/overlay/SmoothMoveMarker.html" title="com.amap.api.maps.utils.overlay中的类">SmoothMoveMarker</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">停止滑动并销毁Marker</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/trace/LBSTraceClient.html#destroy--">destroy()</a></span> - 类 中的方法com.amap.api.trace.<a href="../com/amap/api/trace/LBSTraceClient.html" title="com.amap.api.trace中的类">LBSTraceClient</a></dt>
<dd>
<div class="block">销毁LBSTraceClient</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/TileOverlayOptions.html#diskCacheDir-java.lang.String-">diskCacheDir(String)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/TileOverlayOptions.html" title="com.amap.api.maps.model中的类">TileOverlayOptions</a></dt>
<dd>
<div class="block">设置瓦片图层的磁盘缓存目录。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/TileOverlayOptions.html#diskCacheEnabled-boolean-">diskCacheEnabled(boolean)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/TileOverlayOptions.html" title="com.amap.api.maps.model中的类">TileOverlayOptions</a></dt>
<dd>
<div class="block">设置瓦片图层是否开启磁盘缓存。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/TileOverlayOptions.html#diskCacheSize-int-">diskCacheSize(int)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/TileOverlayOptions.html" title="com.amap.api.maps.model中的类">TileOverlayOptions</a></dt>
<dd>
<div class="block">设置瓦片图层的默认磁盘缓存大小，默认值20MB。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/AMapPara.html#DOTTEDLINE_TYPE_CIRCLE">DOTTEDLINE_TYPE_CIRCLE</a></span> - 类 中的静态变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/AMapPara.html" title="com.amap.api.maps.model中的类">AMapPara</a></dt>
<dd>
<div class="block">虚线类型：圆形，值为1；</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/PolylineOptions.html#DOTTEDLINE_TYPE_CIRCLE">DOTTEDLINE_TYPE_CIRCLE</a></span> - 类 中的静态变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/PolylineOptions.html" title="com.amap.api.maps.model中的类">PolylineOptions</a></dt>
<dd>
<div class="block">虚线类型：圆形，值为1；</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/AMapPara.html#DOTTEDLINE_TYPE_DEFAULT">DOTTEDLINE_TYPE_DEFAULT</a></span> - 类 中的静态变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/AMapPara.html" title="com.amap.api.maps.model中的类">AMapPara</a></dt>
<dd>
<div class="block">虚线类型：不画虚线，值为-1；</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/AMapPara.html#DOTTEDLINE_TYPE_SQUARE">DOTTEDLINE_TYPE_SQUARE</a></span> - 类 中的静态变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/AMapPara.html" title="com.amap.api.maps.model中的类">AMapPara</a></dt>
<dd>
<div class="block">虚线类型：方形，值为0；</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/PolylineOptions.html#DOTTEDLINE_TYPE_SQUARE">DOTTEDLINE_TYPE_SQUARE</a></span> - 类 中的静态变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/PolylineOptions.html" title="com.amap.api.maps.model中的类">PolylineOptions</a></dt>
<dd>
<div class="block">虚线类型：方形，值为0；</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/OfflineMapManager.html#downloadByCityCode-java.lang.String-">downloadByCityCode(String)</a></span> - 类 中的方法com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/OfflineMapManager.html" title="com.amap.api.maps.offlinemap中的类">OfflineMapManager</a></dt>
<dd>
<div class="block">根据给定的城市编码下载该城市的离线地图包<br>
 异步方法，如果有注册OfflineMapDownloadListener监听，下载状态会回调onDownload方法</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/OfflineMapManager.html#downloadByCityName-java.lang.String-">downloadByCityName(String)</a></span> - 类 中的方法com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/OfflineMapManager.html" title="com.amap.api.maps.offlinemap中的类">OfflineMapManager</a></dt>
<dd>
<div class="block">根据给定的城市名称下载该城市的离线地图包<br>
 异步方法，如果有注册OfflineMapDownloadListener监听，下载状态会回调onDownload方法</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/OfflineMapManager.html#downloadByProvinceName-java.lang.String-">downloadByProvinceName(String)</a></span> - 类 中的方法com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/OfflineMapManager.html" title="com.amap.api.maps.offlinemap中的类">OfflineMapManager</a></dt>
<dd>
<div class="block">异步方法，如果有注册OfflineMapDownloadListener监听，下载状态会回调onDownload方法。</div>
</dd>
<dt><a href="../com/amap/api/maps/offlinemap/DownLoadExpandListView.html" title="com.amap.api.maps.offlinemap中的类"><span class="typeNameLink">DownLoadExpandListView</span></a> - <a href="../com/amap/api/maps/offlinemap/package-summary.html">com.amap.api.maps.offlinemap</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/DownLoadExpandListView.html#DownLoadExpandListView-android.content.Context-">DownLoadExpandListView(Context)</a></span> - 类 的构造器com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/DownLoadExpandListView.html" title="com.amap.api.maps.offlinemap中的类">DownLoadExpandListView</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/DownLoadExpandListView.html#DownLoadExpandListView-android.content.Context-android.util.AttributeSet-">DownLoadExpandListView(Context, AttributeSet)</a></span> - 类 的构造器com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/DownLoadExpandListView.html" title="com.amap.api.maps.offlinemap中的类">DownLoadExpandListView</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/amap/api/maps/offlinemap/DownLoadListView.html" title="com.amap.api.maps.offlinemap中的类"><span class="typeNameLink">DownLoadListView</span></a> - <a href="../com/amap/api/maps/offlinemap/package-summary.html">com.amap.api.maps.offlinemap</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/DownLoadListView.html#DownLoadListView-android.content.Context-">DownLoadListView(Context)</a></span> - 类 的构造器com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/DownLoadListView.html" title="com.amap.api.maps.offlinemap中的类">DownLoadListView</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/DownLoadListView.html#DownLoadListView-android.content.Context-android.util.AttributeSet-">DownLoadListView(Context, AttributeSet)</a></span> - 类 的构造器com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/DownLoadListView.html" title="com.amap.api.maps.offlinemap中的类">DownLoadListView</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/amap/api/maps/offlinemap/DownloadProgressView.html" title="com.amap.api.maps.offlinemap中的类"><span class="typeNameLink">DownloadProgressView</span></a> - <a href="../com/amap/api/maps/offlinemap/package-summary.html">com.amap.api.maps.offlinemap</a>中的类</dt>
<dd>
<div class="block">TODO: document your custom view class.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/DownloadProgressView.html#DownloadProgressView-android.content.Context-">DownloadProgressView(Context)</a></span> - 类 的构造器com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/DownloadProgressView.html" title="com.amap.api.maps.offlinemap中的类">DownloadProgressView</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/DownloadProgressView.html#DownloadProgressView-android.content.Context-android.util.AttributeSet-">DownloadProgressView(Context, AttributeSet)</a></span> - 类 的构造器com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/DownloadProgressView.html" title="com.amap.api.maps.offlinemap中的类">DownloadProgressView</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/DownloadProgressView.html#DownloadProgressView-android.content.Context-android.util.AttributeSet-int-">DownloadProgressView(Context, AttributeSet, int)</a></span> - 类 的构造器com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/DownloadProgressView.html" title="com.amap.api.maps.offlinemap中的类">DownloadProgressView</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MarkerOptions.html#draggable-boolean-">draggable(boolean)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MarkerOptions.html" title="com.amap.api.maps.model中的类">MarkerOptions</a></dt>
<dd>
<div class="block">设置Marker覆盖物是否可拖拽。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMapUtils.html#DRIVING_AVOID_CONGESTION">DRIVING_AVOID_CONGESTION</a></span> - 类 中的静态变量com.amap.api.maps.<a href="../com/amap/api/maps/AMapUtils.html" title="com.amap.api.maps中的类">AMapUtils</a></dt>
<dd>
<div class="block">驾车路径规划策略：（避免拥堵 : 4）</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/NaviPara.html#DRIVING_AVOID_CONGESTION">DRIVING_AVOID_CONGESTION</a></span> - 类 中的静态变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/NaviPara.html" title="com.amap.api.maps.model中的类">NaviPara</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMapUtils.html#DRIVING_DEFAULT">DRIVING_DEFAULT</a></span> - 类 中的静态变量com.amap.api.maps.<a href="../com/amap/api/maps/AMapUtils.html" title="com.amap.api.maps中的类">AMapUtils</a></dt>
<dd>
<div class="block">驾车路径规划策略：（速度优先 : 0）</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/NaviPara.html#DRIVING_DEFAULT">DRIVING_DEFAULT</a></span> - 类 中的静态变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/NaviPara.html" title="com.amap.api.maps.model中的类">NaviPara</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMapUtils.html#DRIVING_NO_HIGHWAY">DRIVING_NO_HIGHWAY</a></span> - 类 中的静态变量com.amap.api.maps.<a href="../com/amap/api/maps/AMapUtils.html" title="com.amap.api.maps中的类">AMapUtils</a></dt>
<dd>
<div class="block">驾车路径规划策略：（不走高速 : 3）</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/NaviPara.html#DRIVING_NO_HIGHWAY">DRIVING_NO_HIGHWAY</a></span> - 类 中的静态变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/NaviPara.html" title="com.amap.api.maps.model中的类">NaviPara</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMapUtils.html#DRIVING_NO_HIGHWAY_AVOID_CONGESTION">DRIVING_NO_HIGHWAY_AVOID_CONGESTION</a></span> - 类 中的静态变量com.amap.api.maps.<a href="../com/amap/api/maps/AMapUtils.html" title="com.amap.api.maps中的类">AMapUtils</a></dt>
<dd>
<div class="block">驾车路径规划策略：（不走高速且躲避拥堵 : 6）</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/NaviPara.html#DRIVING_NO_HIGHWAY_AVOID_CONGESTION">DRIVING_NO_HIGHWAY_AVOID_CONGESTION</a></span> - 类 中的静态变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/NaviPara.html" title="com.amap.api.maps.model中的类">NaviPara</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMapUtils.html#DRIVING_NO_HIGHWAY_AVOID_SHORT_MONEY">DRIVING_NO_HIGHWAY_AVOID_SHORT_MONEY</a></span> - 类 中的静态变量com.amap.api.maps.<a href="../com/amap/api/maps/AMapUtils.html" title="com.amap.api.maps中的类">AMapUtils</a></dt>
<dd>
<div class="block">驾车路径规划策略：（不走高速且避免收费 : 5）</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/NaviPara.html#DRIVING_NO_HIGHWAY_AVOID_SHORT_MONEY">DRIVING_NO_HIGHWAY_AVOID_SHORT_MONEY</a></span> - 类 中的静态变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/NaviPara.html" title="com.amap.api.maps.model中的类">NaviPara</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMapUtils.html#DRIVING_NO_HIGHWAY_SAVE_MONEY_AVOID_CONGESTION">DRIVING_NO_HIGHWAY_SAVE_MONEY_AVOID_CONGESTION</a></span> - 类 中的静态变量com.amap.api.maps.<a href="../com/amap/api/maps/AMapUtils.html" title="com.amap.api.maps中的类">AMapUtils</a></dt>
<dd>
<div class="block">驾车路径规划策略：（不走高速躲避收费和拥堵 : 8）</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/NaviPara.html#DRIVING_NO_HIGHWAY_SAVE_MONEY_AVOID_CONGESTION">DRIVING_NO_HIGHWAY_SAVE_MONEY_AVOID_CONGESTION</a></span> - 类 中的静态变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/NaviPara.html" title="com.amap.api.maps.model中的类">NaviPara</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMapUtils.html#DRIVING_SAVE_MONEY">DRIVING_SAVE_MONEY</a></span> - 类 中的静态变量com.amap.api.maps.<a href="../com/amap/api/maps/AMapUtils.html" title="com.amap.api.maps中的类">AMapUtils</a></dt>
<dd>
<div class="block">驾车路径规划策略：（费用优先 : 1）</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/NaviPara.html#DRIVING_SAVE_MONEY">DRIVING_SAVE_MONEY</a></span> - 类 中的静态变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/NaviPara.html" title="com.amap.api.maps.model中的类">NaviPara</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMapUtils.html#DRIVING_SAVE_MONEY_AVOID_CONGESTION">DRIVING_SAVE_MONEY_AVOID_CONGESTION</a></span> - 类 中的静态变量com.amap.api.maps.<a href="../com/amap/api/maps/AMapUtils.html" title="com.amap.api.maps中的类">AMapUtils</a></dt>
<dd>
<div class="block">驾车路径规划策略：（躲避收费和拥堵 : 7）</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/NaviPara.html#DRIVING_SAVE_MONEY_AVOID_CONGESTION">DRIVING_SAVE_MONEY_AVOID_CONGESTION</a></span> - 类 中的静态变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/NaviPara.html" title="com.amap.api.maps.model中的类">NaviPara</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMapUtils.html#DRIVING_SHORT_DISTANCE">DRIVING_SHORT_DISTANCE</a></span> - 类 中的静态变量com.amap.api.maps.<a href="../com/amap/api/maps/AMapUtils.html" title="com.amap.api.maps中的类">AMapUtils</a></dt>
<dd>
<div class="block">驾车路径规划策略：（距离优先 : 2）</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/NaviPara.html#DRIVING_SHORT_DISTANCE">DRIVING_SHORT_DISTANCE</a></span> - 类 中的静态变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/NaviPara.html" title="com.amap.api.maps.model中的类">NaviPara</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">K</a>&nbsp;<a href="index-11.html">L</a>&nbsp;<a href="index-12.html">M</a>&nbsp;<a href="index-13.html">N</a>&nbsp;<a href="index-14.html">O</a>&nbsp;<a href="index-15.html">P</a>&nbsp;<a href="index-16.html">Q</a>&nbsp;<a href="index-17.html">R</a>&nbsp;<a href="index-18.html">S</a>&nbsp;<a href="index-19.html">T</a>&nbsp;<a href="index-20.html">U</a>&nbsp;<a href="index-21.html">V</a>&nbsp;<a href="index-22.html">W</a>&nbsp;<a href="index-23.html">Z</a>&nbsp;</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-3.html">上一个字母</a></li>
<li><a href="index-5.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-4.html" target="_top">框架</a></li>
<li><a href="index-4.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
