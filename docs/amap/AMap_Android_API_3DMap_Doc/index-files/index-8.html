<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>H - 索引</title>
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="H - \u7D22\u5F15";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-7.html">上一个字母</a></li>
<li><a href="index-9.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-8.html" target="_top">框架</a></li>
<li><a href="index-8.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="contentContainer"><a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">K</a>&nbsp;<a href="index-11.html">L</a>&nbsp;<a href="index-12.html">M</a>&nbsp;<a href="index-13.html">N</a>&nbsp;<a href="index-14.html">O</a>&nbsp;<a href="index-15.html">P</a>&nbsp;<a href="index-16.html">Q</a>&nbsp;<a href="index-17.html">R</a>&nbsp;<a href="index-18.html">S</a>&nbsp;<a href="index-19.html">T</a>&nbsp;<a href="index-20.html">U</a>&nbsp;<a href="index-21.html">V</a>&nbsp;<a href="index-22.html">W</a>&nbsp;<a href="index-23.html">Z</a>&nbsp;<a name="I:H">
<!--   -->
</a>
<h2 class="title">H</h2>
<dl>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Arc.html#hashCode--">hashCode()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Arc.html" title="com.amap.api.maps.model中的类">Arc</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Circle.html#hashCode--">hashCode()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Circle.html" title="com.amap.api.maps.model中的类">Circle</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/ColorLatLng.html#hashCode--">hashCode()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/ColorLatLng.html" title="com.amap.api.maps.model中的类">ColorLatLng</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/HeatMapGridLayer.html#hashCode--">hashCode()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/HeatMapGridLayer.html" title="com.amap.api.maps.model中的类">HeatMapGridLayer</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/HeatMapLayer.html#hashCode--">hashCode()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/HeatMapLayer.html" title="com.amap.api.maps.model中的类">HeatMapLayer</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/LatLngBounds.html#hashCode--">hashCode()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/LatLngBounds.html" title="com.amap.api.maps.model中的类">LatLngBounds</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Marker.html#hashCode--">hashCode()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Marker.html" title="com.amap.api.maps.model中的类">Marker</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/NavigateArrow.html#hashCode--">hashCode()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/NavigateArrow.html" title="com.amap.api.maps.model中的类">NavigateArrow</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Poi.html#hashCode--">hashCode()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Poi.html" title="com.amap.api.maps.model中的类">Poi</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Polygon.html#hashCode--">hashCode()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Polygon.html" title="com.amap.api.maps.model中的类">Polygon</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Polyline.html#hashCode--">hashCode()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Polyline.html" title="com.amap.api.maps.model中的类">Polyline</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/TileOverlay.html#hashCode--">hashCode()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/TileOverlay.html" title="com.amap.api.maps.model中的类">TileOverlay</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/amap/api/maps/model/HeatMapGridLayer.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">HeatMapGridLayer</span></a> - <a href="../com/amap/api/maps/model/package-summary.html">com.amap.api.maps.model</a>中的类</dt>
<dd>
<div class="block">热力图图层</div>
</dd>
<dt><a href="../com/amap/api/maps/model/HeatMapGridLayerOptions.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">HeatMapGridLayerOptions</span></a> - <a href="../com/amap/api/maps/model/package-summary.html">com.amap.api.maps.model</a>中的类</dt>
<dd>
<div class="block">实现热力图</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/HeatMapGridLayerOptions.html#HeatMapGridLayerOptions--">HeatMapGridLayerOptions()</a></span> - 类 的构造器com.amap.api.maps.model.<a href="../com/amap/api/maps/model/HeatMapGridLayerOptions.html" title="com.amap.api.maps.model中的类">HeatMapGridLayerOptions</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/amap/api/maps/model/HeatMapItem.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">HeatMapItem</span></a> - <a href="../com/amap/api/maps/model/package-summary.html">com.amap.api.maps.model</a>中的类</dt>
<dd>
<div class="block">热力图聚合点信息</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/HeatMapItem.html#HeatMapItem--">HeatMapItem()</a></span> - 类 的构造器com.amap.api.maps.model.<a href="../com/amap/api/maps/model/HeatMapItem.html" title="com.amap.api.maps.model中的类">HeatMapItem</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/amap/api/maps/model/HeatMapLayer.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">HeatMapLayer</span></a> - <a href="../com/amap/api/maps/model/package-summary.html">com.amap.api.maps.model</a>中的类</dt>
<dd>
<div class="block">热力图图层</div>
</dd>
<dt><a href="../com/amap/api/maps/model/HeatMapLayerOptions.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">HeatMapLayerOptions</span></a> - <a href="../com/amap/api/maps/model/package-summary.html">com.amap.api.maps.model</a>中的类</dt>
<dd>
<div class="block">实现高德热力图</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/HeatMapLayerOptions.html#HeatMapLayerOptions--">HeatMapLayerOptions()</a></span> - 类 的构造器com.amap.api.maps.model.<a href="../com/amap/api/maps/model/HeatMapLayerOptions.html" title="com.amap.api.maps.model中的类">HeatMapLayerOptions</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/amap/api/maps/model/HeatmapTileProvider.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">HeatmapTileProvider</span></a> - <a href="../com/amap/api/maps/model/package-summary.html">com.amap.api.maps.model</a>中的类</dt>
<dd>
<div class="block">实现TileProvider类，高德热力图Provider</div>
</dd>
<dt><a href="../com/amap/api/maps/model/HeatmapTileProvider.Builder.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">HeatmapTileProvider.Builder</span></a> - <a href="../com/amap/api/maps/model/package-summary.html">com.amap.api.maps.model</a>中的类</dt>
<dd>
<div class="block">热力图构造器.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Tile.html#height">height</a></span> - 类 中的变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Tile.html" title="com.amap.api.maps.model中的类">Tile</a></dt>
<dd>
<div class="block">编码的图片像素高度，单位像素。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#hideBuildings-java.util.List-">hideBuildings(List&lt;LatLng&gt;)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block">隐藏建筑物</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Marker.html#hideInfoWindow--">hideInfoWindow()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Marker.html" title="com.amap.api.maps.model中的类">Marker</a></dt>
<dd>
<div class="block">隐藏Marker覆盖物的信息窗口。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/MapsInitializer.html#HTTP">HTTP</a></span> - 类 中的静态变量com.amap.api.maps.<a href="../com/amap/api/maps/MapsInitializer.html" title="com.amap.api.maps中的类">MapsInitializer</a></dt>
<dd>
<div class="block">Http协议。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/MapsInitializer.html#HTTPS">HTTPS</a></span> - 类 中的静态变量com.amap.api.maps.<a href="../com/amap/api/maps/MapsInitializer.html" title="com.amap.api.maps中的类">MapsInitializer</a></dt>
<dd>
<div class="block">Https协议。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/BitmapDescriptorFactory.html#HUE_AZURE">HUE_AZURE</a></span> - 类 中的静态变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/BitmapDescriptorFactory.html" title="com.amap.api.maps.model中的类">BitmapDescriptorFactory</a></dt>
<dd>
<div class="block">天蓝色。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/BitmapDescriptorFactory.html#HUE_BLUE">HUE_BLUE</a></span> - 类 中的静态变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/BitmapDescriptorFactory.html" title="com.amap.api.maps.model中的类">BitmapDescriptorFactory</a></dt>
<dd>
<div class="block">蓝色。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/BitmapDescriptorFactory.html#HUE_CYAN">HUE_CYAN</a></span> - 类 中的静态变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/BitmapDescriptorFactory.html" title="com.amap.api.maps.model中的类">BitmapDescriptorFactory</a></dt>
<dd>
<div class="block">青色。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/BitmapDescriptorFactory.html#HUE_GREEN">HUE_GREEN</a></span> - 类 中的静态变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/BitmapDescriptorFactory.html" title="com.amap.api.maps.model中的类">BitmapDescriptorFactory</a></dt>
<dd>
<div class="block">绿色。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/BitmapDescriptorFactory.html#HUE_MAGENTA">HUE_MAGENTA</a></span> - 类 中的静态变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/BitmapDescriptorFactory.html" title="com.amap.api.maps.model中的类">BitmapDescriptorFactory</a></dt>
<dd>
<div class="block">酒红色。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/BitmapDescriptorFactory.html#HUE_ORANGE">HUE_ORANGE</a></span> - 类 中的静态变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/BitmapDescriptorFactory.html" title="com.amap.api.maps.model中的类">BitmapDescriptorFactory</a></dt>
<dd>
<div class="block">橙色。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/BitmapDescriptorFactory.html#HUE_RED">HUE_RED</a></span> - 类 中的静态变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/BitmapDescriptorFactory.html" title="com.amap.api.maps.model中的类">BitmapDescriptorFactory</a></dt>
<dd>
<div class="block">红色。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/BitmapDescriptorFactory.html#HUE_ROSE">HUE_ROSE</a></span> - 类 中的静态变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/BitmapDescriptorFactory.html" title="com.amap.api.maps.model中的类">BitmapDescriptorFactory</a></dt>
<dd>
<div class="block">玫瑰红。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/BitmapDescriptorFactory.html#HUE_VIOLET">HUE_VIOLET</a></span> - 类 中的静态变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/BitmapDescriptorFactory.html" title="com.amap.api.maps.model中的类">BitmapDescriptorFactory</a></dt>
<dd>
<div class="block">紫色。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/BitmapDescriptorFactory.html#HUE_YELLOW">HUE_YELLOW</a></span> - 类 中的静态变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/BitmapDescriptorFactory.html" title="com.amap.api.maps.model中的类">BitmapDescriptorFactory</a></dt>
<dd>
<div class="block">黄色。</div>
</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">K</a>&nbsp;<a href="index-11.html">L</a>&nbsp;<a href="index-12.html">M</a>&nbsp;<a href="index-13.html">N</a>&nbsp;<a href="index-14.html">O</a>&nbsp;<a href="index-15.html">P</a>&nbsp;<a href="index-16.html">Q</a>&nbsp;<a href="index-17.html">R</a>&nbsp;<a href="index-18.html">S</a>&nbsp;<a href="index-19.html">T</a>&nbsp;<a href="index-20.html">U</a>&nbsp;<a href="index-21.html">V</a>&nbsp;<a href="index-22.html">W</a>&nbsp;<a href="index-23.html">Z</a>&nbsp;</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-7.html">上一个字母</a></li>
<li><a href="index-9.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-8.html" target="_top">框架</a></li>
<li><a href="index-8.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
