<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>N - 索引</title>
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="N - \u7D22\u5F15";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-12.html">上一个字母</a></li>
<li><a href="index-14.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-13.html" target="_top">框架</a></li>
<li><a href="index-13.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="contentContainer"><a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">K</a>&nbsp;<a href="index-11.html">L</a>&nbsp;<a href="index-12.html">M</a>&nbsp;<a href="index-13.html">N</a>&nbsp;<a href="index-14.html">O</a>&nbsp;<a href="index-15.html">P</a>&nbsp;<a href="index-16.html">Q</a>&nbsp;<a href="index-17.html">R</a>&nbsp;<a href="index-18.html">S</a>&nbsp;<a href="index-19.html">T</a>&nbsp;<a href="index-20.html">U</a>&nbsp;<a href="index-21.html">V</a>&nbsp;<a href="index-22.html">W</a>&nbsp;<a href="index-23.html">Z</a>&nbsp;<a name="I:N">
<!--   -->
</a>
<h2 class="title">N</h2>
<dl>
<dt><a href="../com/amap/api/maps/model/NavigateArrow.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">NavigateArrow</span></a> - <a href="../com/amap/api/maps/model/package-summary.html">com.amap.api.maps.model</a>中的类</dt>
<dd>
<div class="block">定义一个地图 箭头(NavigateArrow) 覆盖物
 一个导航箭头是多个连贯点的集合，拥有以下属性：
 
 顶点
 箭头是由两个顶点之间连贯的点构成的。</div>
</dd>
<dt><a href="../com/amap/api/maps/model/NavigateArrowOptions.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">NavigateArrowOptions</span></a> - <a href="../com/amap/api/maps/model/package-summary.html">com.amap.api.maps.model</a>中的类</dt>
<dd>
<div class="block">导航箭头(NavigateArrow)覆盖物的选项类。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/NavigateArrowOptions.html#NavigateArrowOptions--">NavigateArrowOptions()</a></span> - 类 的构造器com.amap.api.maps.model.<a href="../com/amap/api/maps/model/NavigateArrowOptions.html" title="com.amap.api.maps.model中的类">NavigateArrowOptions</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/amap/api/maps/model/NaviPara.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">NaviPara</span></a> - <a href="../com/amap/api/maps/model/package-summary.html">com.amap.api.maps.model</a>中的类</dt>
<dd>
<div class="block">NaviPara 是唤起高德地图导航功能的参数类。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/NaviPara.html#NaviPara--">NaviPara()</a></span> - 类 的构造器com.amap.api.maps.model.<a href="../com/amap/api/maps/model/NaviPara.html" title="com.amap.api.maps.model中的类">NaviPara</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/VisibleRegion.html#nearLeft">nearLeft</a></span> - 类 中的变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/VisibleRegion.html" title="com.amap.api.maps.model中的类">VisibleRegion</a></dt>
<dd>
<div class="block">可视区域的左下角。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/VisibleRegion.html#nearRight">nearRight</a></span> - 类 中的变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/VisibleRegion.html" title="com.amap.api.maps.model中的类">VisibleRegion</a></dt>
<dd>
<div class="block">可视区域的右下角。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/OfflineMapStatus.html#NEW_VERSION">NEW_VERSION</a></span> - 类 中的静态变量com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/OfflineMapStatus.html" title="com.amap.api.maps.offlinemap中的类">OfflineMapStatus</a></dt>
<dd>
<div class="block">有更新，对于已下载的城市出现，线上出现新版本的时候会出现此状态。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/GLTFOverlayOptionsCreator.html#newArray-int-">newArray(int)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/GLTFOverlayOptionsCreator.html" title="com.amap.api.maps.model中的类">GLTFOverlayOptionsCreator</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/CameraUpdateFactory.html#newCameraPosition-com.amap.api.maps.model.CameraPosition-">newCameraPosition(CameraPosition)</a></span> - 类 中的静态方法com.amap.api.maps.<a href="../com/amap/api/maps/CameraUpdateFactory.html" title="com.amap.api.maps中的类">CameraUpdateFactory</a></dt>
<dd>
<div class="block">给地图设置一个新的状态。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/MapFragment.html#newInstance--">newInstance()</a></span> - 类 中的静态方法com.amap.api.maps.<a href="../com/amap/api/maps/MapFragment.html" title="com.amap.api.maps中的类">MapFragment</a></dt>
<dd>
<div class="block">使用默认的选项创建MapFragment。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/MapFragment.html#newInstance-com.amap.api.maps.AMapOptions-">newInstance(AMapOptions)</a></span> - 类 中的静态方法com.amap.api.maps.<a href="../com/amap/api/maps/MapFragment.html" title="com.amap.api.maps中的类">MapFragment</a></dt>
<dd>
<div class="block">根据用户传入的AMapOptions创建MapFragment。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/SupportMapFragment.html#newInstance--">newInstance()</a></span> - 类 中的静态方法com.amap.api.maps.<a href="../com/amap/api/maps/SupportMapFragment.html" title="com.amap.api.maps中的类">SupportMapFragment</a></dt>
<dd>
<div class="block">使用默认的选项创建SupportMapFragment。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/SupportMapFragment.html#newInstance-com.amap.api.maps.AMapOptions-">newInstance(AMapOptions)</a></span> - 类 中的静态方法com.amap.api.maps.<a href="../com/amap/api/maps/SupportMapFragment.html" title="com.amap.api.maps中的类">SupportMapFragment</a></dt>
<dd>
<div class="block">根据用户传入的AMapOptions 创建SupportMapFragment。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/TextureMapFragment.html#newInstance--">newInstance()</a></span> - 类 中的静态方法com.amap.api.maps.<a href="../com/amap/api/maps/TextureMapFragment.html" title="com.amap.api.maps中的类">TextureMapFragment</a></dt>
<dd>
<div class="block">使用默认的选项创建TextureMapFragment。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/TextureMapFragment.html#newInstance-com.amap.api.maps.AMapOptions-">newInstance(AMapOptions)</a></span> - 类 中的静态方法com.amap.api.maps.<a href="../com/amap/api/maps/TextureMapFragment.html" title="com.amap.api.maps中的类">TextureMapFragment</a></dt>
<dd>
<div class="block">根据用户传入的AMapOptions 创建TextureMapFragment。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/TextureSupportMapFragment.html#newInstance--">newInstance()</a></span> - 类 中的静态方法com.amap.api.maps.<a href="../com/amap/api/maps/TextureSupportMapFragment.html" title="com.amap.api.maps中的类">TextureSupportMapFragment</a></dt>
<dd>
<div class="block">使用默认的选项创建TextureSupportMapFragment。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/TextureSupportMapFragment.html#newInstance-com.amap.api.maps.AMapOptions-">newInstance(AMapOptions)</a></span> - 类 中的静态方法com.amap.api.maps.<a href="../com/amap/api/maps/TextureSupportMapFragment.html" title="com.amap.api.maps中的类">TextureSupportMapFragment</a></dt>
<dd>
<div class="block">根据用户传入的AMapOptions 创建TextureSupportMapFragment。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/CameraUpdateFactory.html#newLatLng-com.amap.api.maps.model.LatLng-">newLatLng(LatLng)</a></span> - 类 中的静态方法com.amap.api.maps.<a href="../com/amap/api/maps/CameraUpdateFactory.html" title="com.amap.api.maps中的类">CameraUpdateFactory</a></dt>
<dd>
<div class="block">设置地图的中心点。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/CameraUpdateFactory.html#newLatLngBounds-com.amap.api.maps.model.LatLngBounds-int-">newLatLngBounds(LatLngBounds, int)</a></span> - 类 中的静态方法com.amap.api.maps.<a href="../com/amap/api/maps/CameraUpdateFactory.html" title="com.amap.api.maps中的类">CameraUpdateFactory</a></dt>
<dd>
<div class="block">设置显示在规定屏幕范围内的地图经纬度范围。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/CameraUpdateFactory.html#newLatLngBounds-com.amap.api.maps.model.LatLngBounds-int-int-int-">newLatLngBounds(LatLngBounds, int, int, int)</a></span> - 类 中的静态方法com.amap.api.maps.<a href="../com/amap/api/maps/CameraUpdateFactory.html" title="com.amap.api.maps中的类">CameraUpdateFactory</a></dt>
<dd>
<div class="block">设置显示在规定宽高中的地图经纬度范围。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/CameraUpdateFactory.html#newLatLngBoundsRect-com.amap.api.maps.model.LatLngBounds-int-int-int-int-">newLatLngBoundsRect(LatLngBounds, int, int, int, int)</a></span> - 类 中的静态方法com.amap.api.maps.<a href="../com/amap/api/maps/CameraUpdateFactory.html" title="com.amap.api.maps中的类">CameraUpdateFactory</a></dt>
<dd>
<div class="block">设置显示在规定屏幕范围内的地图经纬度范围。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/CameraUpdateFactory.html#newLatLngZoom-com.amap.api.maps.model.LatLng-float-">newLatLngZoom(LatLng, float)</a></span> - 类 中的静态方法com.amap.api.maps.<a href="../com/amap/api/maps/CameraUpdateFactory.html" title="com.amap.api.maps中的类">CameraUpdateFactory</a></dt>
<dd>
<div class="block">设置地图中心点以及缩放级别。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/GroundOverlayOptions.html#NO_DIMENSION">NO_DIMENSION</a></span> - 类 中的静态变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/GroundOverlayOptions.html" title="com.amap.api.maps.model中的类">GroundOverlayOptions</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/TileProvider.html#NO_TILE">NO_TILE</a></span> - 接口 中的静态变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/TileProvider.html" title="com.amap.api.maps.model中的接口">TileProvider</a></dt>
<dd>
<div class="block">指定tile坐标后不存在瓦片的特殊tile。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/LatLngBounds.html#northeast">northeast</a></span> - 类 中的变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/LatLngBounds.html" title="com.amap.api.maps.model中的类">LatLngBounds</a></dt>
<dd>&nbsp;</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">K</a>&nbsp;<a href="index-11.html">L</a>&nbsp;<a href="index-12.html">M</a>&nbsp;<a href="index-13.html">N</a>&nbsp;<a href="index-14.html">O</a>&nbsp;<a href="index-15.html">P</a>&nbsp;<a href="index-16.html">Q</a>&nbsp;<a href="index-17.html">R</a>&nbsp;<a href="index-18.html">S</a>&nbsp;<a href="index-19.html">T</a>&nbsp;<a href="index-20.html">U</a>&nbsp;<a href="index-21.html">V</a>&nbsp;<a href="index-22.html">W</a>&nbsp;<a href="index-23.html">Z</a>&nbsp;</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-12.html">上一个字母</a></li>
<li><a href="index-14.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-13.html" target="_top">框架</a></li>
<li><a href="index-13.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
