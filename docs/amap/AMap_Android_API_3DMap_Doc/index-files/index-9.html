<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>I - 索引</title>
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="I - \u7D22\u5F15";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-8.html">上一个字母</a></li>
<li><a href="index-10.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-9.html" target="_top">框架</a></li>
<li><a href="index-9.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="contentContainer"><a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">K</a>&nbsp;<a href="index-11.html">L</a>&nbsp;<a href="index-12.html">M</a>&nbsp;<a href="index-13.html">N</a>&nbsp;<a href="index-14.html">O</a>&nbsp;<a href="index-15.html">P</a>&nbsp;<a href="index-16.html">Q</a>&nbsp;<a href="index-17.html">R</a>&nbsp;<a href="index-18.html">S</a>&nbsp;<a href="index-19.html">T</a>&nbsp;<a href="index-20.html">U</a>&nbsp;<a href="index-21.html">V</a>&nbsp;<a href="index-22.html">W</a>&nbsp;<a href="index-23.html">Z</a>&nbsp;<a name="I:I">
<!--   -->
</a>
<h2 class="title">I</h2>
<dl>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MarkerOptions.html#icon-com.amap.api.maps.model.BitmapDescriptor-">icon(BitmapDescriptor)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MarkerOptions.html" title="com.amap.api.maps.model中的类">MarkerOptions</a></dt>
<dd>
<div class="block">设置Marker覆盖物的图标。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MultiPointOverlayOptions.html#icon-com.amap.api.maps.model.BitmapDescriptor-">icon(BitmapDescriptor)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MultiPointOverlayOptions.html" title="com.amap.api.maps.model中的类">MultiPointOverlayOptions</a></dt>
<dd>
<div class="block">设置海量点的显示图标</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/particle/ParticleOverlayOptions.html#icon-com.amap.api.maps.model.BitmapDescriptor-">icon(BitmapDescriptor)</a></span> - 类 中的方法com.amap.api.maps.model.particle.<a href="../com/amap/api/maps/model/particle/ParticleOverlayOptions.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlayOptions</a></dt>
<dd>
<div class="block">设置覆盖物的图标。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MarkerOptions.html#icons-java.util.ArrayList-">icons(ArrayList&lt;BitmapDescriptor&gt;)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MarkerOptions.html" title="com.amap.api.maps.model中的类">MarkerOptions</a></dt>
<dd>
<div class="block">设置Marker覆盖物的动画帧图标列表，多张图片模拟gif的效果。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MVTTileOverlayOptions.Builder.html#id-java.lang.String-">id(String)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MVTTileOverlayOptions.Builder.html" title="com.amap.api.maps.model中的类">MVTTileOverlayOptions.Builder</a></dt>
<dd>
<div class="block">geohub上MVT数据的服务id</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMapException.html#ILLEGAL_AMAP_ARGUMENT">ILLEGAL_AMAP_ARGUMENT</a></span> - 异常错误 中的静态变量com.amap.api.maps.<a href="../com/amap/api/maps/AMapException.html" title="com.amap.api.maps中的类">AMapException</a></dt>
<dd>
<div class="block">非法导航参数</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/GroundOverlayOptions.html#image-com.amap.api.maps.model.BitmapDescriptor-">image(BitmapDescriptor)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/GroundOverlayOptions.html" title="com.amap.api.maps.model中的类">GroundOverlayOptions</a></dt>
<dd>
<div class="block">设置 ground 覆盖物的图片信息。</div>
</dd>
<dt><a href="../com/amap/api/maps/model/ImageOptions.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">ImageOptions</span></a> - <a href="../com/amap/api/maps/model/package-summary.html">com.amap.api.maps.model</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/ImageOptions.html#ImageOptions--">ImageOptions()</a></span> - 类 的构造器com.amap.api.maps.model.<a href="../com/amap/api/maps/model/ImageOptions.html" title="com.amap.api.maps.model中的类">ImageOptions</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/amap/api/maps/model/ImageOptions.ShapeType.html" title="com.amap.api.maps.model中的枚举"><span class="typeNameLink">ImageOptions.ShapeType</span></a> - <a href="../com/amap/api/maps/model/package-summary.html">com.amap.api.maps.model</a>中的枚举</dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/LatLngBounds.Builder.html#include-com.amap.api.maps.model.LatLng-">include(LatLng)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/LatLngBounds.Builder.html" title="com.amap.api.maps.model中的类">LatLngBounds.Builder</a></dt>
<dd>
<div class="block">区域包含传入的坐标。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/LatLngBounds.html#including-com.amap.api.maps.model.LatLng-">including(LatLng)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/LatLngBounds.html" title="com.amap.api.maps.model中的类">LatLngBounds</a></dt>
<dd>
<div class="block">返回一个新的矩形区域。</div>
</dd>
<dt><a href="../com/amap/api/maps/model/IndoorBuildingInfo.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">IndoorBuildingInfo</span></a> - <a href="../com/amap/api/maps/model/package-summary.html">com.amap.api.maps.model</a>中的类</dt>
<dd>
<div class="block">室内地图属性类，包含室内地图的POIID、楼层总数和当前显示楼层等。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/animation/Animation.html#INFINITE">INFINITE</a></span> - 类 中的静态变量com.amap.api.maps.model.animation.<a href="../com/amap/api/maps/model/animation/Animation.html" title="com.amap.api.maps.model.animation中的类">Animation</a></dt>
<dd>
<div class="block">无限期地重复动画.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MarkerOptions.html#infoWindowEnable-boolean-">infoWindowEnable(boolean)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MarkerOptions.html" title="com.amap.api.maps.model中的类">MarkerOptions</a></dt>
<dd>
<div class="block">设置Marker覆盖物的InfoWindow是否允许显示,默认为true</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/GLTFOverlayOptions.html#infoWindowView-com.amap.api.maps.model.BitmapDescriptor-">infoWindowView(BitmapDescriptor)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/GLTFOverlayOptions.html" title="com.amap.api.maps.model中的类">GLTFOverlayOptions</a></dt>
<dd>
<div class="block">设置点击的infoWindowView</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/MapsInitializer.html#initialize-android.content.Context-">initialize(Context)</a></span> - 类 中的静态方法com.amap.api.maps.<a href="../com/amap/api/maps/MapsInitializer.html" title="com.amap.api.maps中的类">MapsInitializer</a></dt>
<dd>
<div class="block">初始化全局 context。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/WeightedLatLng.html#intensity">intensity</a></span> - 类 中的变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/WeightedLatLng.html" title="com.amap.api.maps.model中的类">WeightedLatLng</a></dt>
<dd>
<div class="block">强度权值</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/LatLngBounds.html#intersects-com.amap.api.maps.model.LatLngBounds-">intersects(LatLngBounds)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/LatLngBounds.html" title="com.amap.api.maps.model中的类">LatLngBounds</a></dt>
<dd>
<div class="block">判断两个矩形区域是否相交</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MyLocationStyle.html#interval-long-">interval(long)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MyLocationStyle.html" title="com.amap.api.maps.model中的类">MyLocationStyle</a></dt>
<dd>
<div class="block">设置发起定位请求的时间间隔，单位：毫秒，默认值：1000毫秒，如果传小于1000的任何值将执行单次定位。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/NavigateArrow.html#is3DModel--">is3DModel()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/NavigateArrow.html" title="com.amap.api.maps.model中的类">NavigateArrow</a></dt>
<dd>
<div class="block">获取箭头(NavigateArrow)覆盖物是否是3D模式</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/NavigateArrowOptions.html#is3DModel--">is3DModel()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/NavigateArrowOptions.html" title="com.amap.api.maps.model中的类">NavigateArrowOptions</a></dt>
<dd>
<div class="block">获取箭头(NavigateArrow)覆盖物是否是3D模式</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/CameraPosition.html#isAbroad">isAbroad</a></span> - 类 中的变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/CameraPosition.html" title="com.amap.api.maps.model中的类">CameraPosition</a></dt>
<dd>
<div class="block">该位置是否在国内（此属性不是精确计算，不能用于边界区域）</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/CoordinateConverter.html#isAMapDataAvailable-double-double-">isAMapDataAvailable(double, double)</a></span> - 类 中的静态方法com.amap.api.maps.<a href="../com/amap/api/maps/CoordinateConverter.html" title="com.amap.api.maps中的类">CoordinateConverter</a></dt>
<dd>
<div class="block">是否是高德地图可用数据</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/GLTFOverlay.html#isAnimated--">isAnimated()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/GLTFOverlay.html" title="com.amap.api.maps.model中的类">GLTFOverlay</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Gradient.html#isAvailable--">isAvailable()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Gradient.html" title="com.amap.api.maps.model中的类">Gradient</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/TileOverlaySource.html#isCacheEnabled--">isCacheEnabled()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/TileOverlaySource.html" title="com.amap.api.maps.model中的类">TileOverlaySource</a></dt>
<dd>
<div class="block">是否会缓存瓦片数据</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/GLTFOverlay.html#isClickable--">isClickable()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/GLTFOverlay.html" title="com.amap.api.maps.model中的类">GLTFOverlay</a></dt>
<dd>
<div class="block">获取是否可点击</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/GLTFOverlayOptions.html#isClickable--">isClickable()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/GLTFOverlayOptions.html" title="com.amap.api.maps.model中的类">GLTFOverlayOptions</a></dt>
<dd>
<div class="block">获取是否可点击</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Marker.html#isClickable--">isClickable()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Marker.html" title="com.amap.api.maps.model中的类">Marker</a></dt>
<dd>
<div class="block">获取Maker覆盖物的点击状态,可以通过 <a href="../com/amap/api/maps/model/Marker.html#setClickable-boolean-"><code>Marker.setClickable(boolean)</code></a> 设置是否可以点击</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/UiSettings.html#isCompassEnabled--">isCompassEnabled()</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/UiSettings.html" title="com.amap.api.maps中的类">UiSettings</a></dt>
<dd>
<div class="block">返回指南针控件是否可见。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Polyline.html#isDottedLine--">isDottedLine()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Polyline.html" title="com.amap.api.maps.model中的类">Polyline</a></dt>
<dd>
<div class="block">获取线段是否虚线。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/PolylineOptions.html#isDottedLine--">isDottedLine()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/PolylineOptions.html" title="com.amap.api.maps.model中的类">PolylineOptions</a></dt>
<dd>
<div class="block">获取线段是否画虚线，默认为false，画实线。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/GLTFOverlay.html#isDraggable--">isDraggable()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/GLTFOverlay.html" title="com.amap.api.maps.model中的类">GLTFOverlay</a></dt>
<dd>
<div class="block">获取是否可拖拽</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/GLTFOverlayOptions.html#isDraggable--">isDraggable()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/GLTFOverlayOptions.html" title="com.amap.api.maps.model中的类">GLTFOverlayOptions</a></dt>
<dd>
<div class="block">获取是否可拖拽</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Marker.html#isDraggable--">isDraggable()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Marker.html" title="com.amap.api.maps.model中的类">Marker</a></dt>
<dd>
<div class="block">获得Marker覆盖物的拖拽状态。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MarkerOptions.html#isDraggable--">isDraggable()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MarkerOptions.html" title="com.amap.api.maps.model中的类">MarkerOptions</a></dt>
<dd>
<div class="block">获取Marker覆盖物的拖拽状态。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/CustomMapStyleOptions.html#isEnable--">isEnable()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/CustomMapStyleOptions.html" title="com.amap.api.maps.model中的类">CustomMapStyleOptions</a></dt>
<dd>
<div class="block">是否开启底图自定义样式， 默认为开启</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Marker.html#isFlat--">isFlat()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Marker.html" title="com.amap.api.maps.model中的类">Marker</a></dt>
<dd>
<div class="block">返回Marker覆盖物是否是平贴在地图上。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MarkerOptions.html#isFlat--">isFlat()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MarkerOptions.html" title="com.amap.api.maps.model中的类">MarkerOptions</a></dt>
<dd>
<div class="block">获取Marker覆盖物是否平贴地图。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Polyline.html#isGeodesic--">isGeodesic()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Polyline.html" title="com.amap.api.maps.model中的类">Polyline</a></dt>
<dd>
<div class="block">获取线段是否为大地曲线，默认false，不画大地曲线。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/PolylineOptions.html#isGeodesic--">isGeodesic()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/PolylineOptions.html" title="com.amap.api.maps.model中的类">PolylineOptions</a></dt>
<dd>
<div class="block">获取线段是否为大地曲线，默认false，不画大地曲线。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/UiSettings.html#isGestureScaleByMapCenter--">isGestureScaleByMapCenter()</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/UiSettings.html" title="com.amap.api.maps中的类">UiSettings</a></dt>
<dd>
<div class="block">返回是否以地图中心点缩放</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MarkerOptions.html#isGps--">isGps()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MarkerOptions.html" title="com.amap.api.maps.model中的类">MarkerOptions</a></dt>
<dd>
<div class="block">获取Marker覆盖物的坐标是否是Gps，默认为false。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/UiSettings.html#isIndoorSwitchEnabled--">isIndoorSwitchEnabled()</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/UiSettings.html" title="com.amap.api.maps中的类">UiSettings</a></dt>
<dd>
<div class="block">返回室内地图楼层切换控件是否显示。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/BasePointOverlay.html#isInfoWindowEnable--">isInfoWindowEnable()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/BasePointOverlay.html" title="com.amap.api.maps.model中的类">BasePointOverlay</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Marker.html#isInfoWindowEnable--">isInfoWindowEnable()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Marker.html" title="com.amap.api.maps.model中的类">Marker</a></dt>
<dd>
<div class="block">获取Marker覆盖物是否允许InfoWindow显示, 可以通过 <a href="../com/amap/api/maps/model/Marker.html#setInfoWindowEnable-boolean-"><code>Marker.setInfoWindowEnable(boolean)</code></a> 进行设置</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MarkerOptions.html#isInfoWindowEnable--">isInfoWindowEnable()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MarkerOptions.html" title="com.amap.api.maps.model中的类">MarkerOptions</a></dt>
<dd>
<div class="block">获取Marker覆盖物的InfoWindow是否允许显示, 可以通过 <a href="../com/amap/api/maps/model/MarkerOptions.html#infoWindowEnable-boolean-"><code>MarkerOptions.infoWindowEnable(boolean)</code></a> 进行设置</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/GLTFOverlay.html#isInfoWindowShow--">isInfoWindowShow()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/GLTFOverlay.html" title="com.amap.api.maps.model中的类">GLTFOverlay</a></dt>
<dd>
<div class="block">获取infoWindow是否展示</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/GLTFOverlayOptions.html#isInfoWindowShow--">isInfoWindowShow()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/GLTFOverlayOptions.html" title="com.amap.api.maps.model中的类">GLTFOverlayOptions</a></dt>
<dd>
<div class="block">获取infoWindow是否展示</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Marker.html#isInfoWindowShown--">isInfoWindowShown()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Marker.html" title="com.amap.api.maps.model中的类">Marker</a></dt>
<dd>
<div class="block">返回Marker覆盖物的信息窗口是否显示，true: 显示，false: 不显示。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/UiSettings.html#isLogoEnable--">isLogoEnable()</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/UiSettings.html" title="com.amap.api.maps中的类">UiSettings</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/particle/ParticleOverlayOptions.html#isLoop--">isLoop()</a></span> - 类 中的方法com.amap.api.maps.model.particle.<a href="../com/amap/api/maps/model/particle/ParticleOverlayOptions.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlayOptions</a></dt>
<dd>
<div class="block">整个粒子效果是否循环</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/UiSettings.html#isMyLocationButtonEnabled--">isMyLocationButtonEnabled()</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/UiSettings.html" title="com.amap.api.maps中的类">UiSettings</a></dt>
<dd>
<div class="block">返回定位按钮是否可见。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#isMyLocationEnabled--">isMyLocationEnabled()</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block">返回是否打开定位图层（myLocationOverlay）。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MyLocationStyle.html#isMyLocationShowing--">isMyLocationShowing()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MyLocationStyle.html" title="com.amap.api.maps.model中的类">MyLocationStyle</a></dt>
<dd>
<div class="block">得到是否显示定位小蓝点。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Marker.html#isPerspective--">isPerspective()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Marker.html" title="com.amap.api.maps.model中的类">Marker</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span>
<div class="block"><span class="deprecationComment">已取消这个效果</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MarkerOptions.html#isPerspective--">isPerspective()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MarkerOptions.html" title="com.amap.api.maps.model中的类">MarkerOptions</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Marker.html#isRemoved--">isRemoved()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Marker.html" title="com.amap.api.maps.model中的类">Marker</a></dt>
<dd>
<div class="block">获取当前Marker是否是被移除状态</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/UiSettings.html#isRotateGesturesEnabled--">isRotateGesturesEnabled()</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/UiSettings.html" title="com.amap.api.maps中的类">UiSettings</a></dt>
<dd>
<div class="block">返回旋转手势是否可用。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/UiSettings.html#isScaleControlsEnabled--">isScaleControlsEnabled()</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/UiSettings.html" title="com.amap.api.maps中的类">UiSettings</a></dt>
<dd>
<div class="block">返回比例尺控件是否可见。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/UiSettings.html#isScrollGesturesEnabled--">isScrollGesturesEnabled()</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/UiSettings.html" title="com.amap.api.maps中的类">UiSettings</a></dt>
<dd>
<div class="block">返回拖拽手势是否可用。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/MapsInitializer.html#isTerrainEnable--">isTerrainEnable()</a></span> - 类 中的静态方法com.amap.api.maps.<a href="../com/amap/api/maps/MapsInitializer.html" title="com.amap.api.maps中的类">MapsInitializer</a></dt>
<dd>
<div class="block">是否打开地形图, 默认为关闭
 打开地形图之后，底图会变成3D模式，添加的点线面等覆盖物也会自动带有高程</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/UiSettings.html#isTiltGesturesEnabled--">isTiltGesturesEnabled()</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/UiSettings.html" title="com.amap.api.maps中的类">UiSettings</a></dt>
<dd>
<div class="block">返回倾斜手势是否可用。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#isTouchPoiEnable--">isTouchPoiEnable()</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block">获取地图POI是否允许点击。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#isTrafficEnabled--">isTrafficEnabled()</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block">获取是否打开交通路况图层。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/PolylineOptions.html#isUseGradient--">isUseGradient()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/PolylineOptions.html" title="com.amap.api.maps.model中的类">PolylineOptions</a></dt>
<dd>
<div class="block">获取线段是否使用渐变色</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/PolylineOptions.html#isUseTexture--">isUseTexture()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/PolylineOptions.html" title="com.amap.api.maps.model中的类">PolylineOptions</a></dt>
<dd>
<div class="block">获取线段是否使用纹理贴图。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/particle/ParticleOverlayOptions.html#isVisibile--">isVisibile()</a></span> - 类 中的方法com.amap.api.maps.model.particle.<a href="../com/amap/api/maps/model/particle/ParticleOverlayOptions.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlayOptions</a></dt>
<dd>
<div class="block">覆盖物的可见属性。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Arc.html#isVisible--">isVisible()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Arc.html" title="com.amap.api.maps.model中的类">Arc</a></dt>
<dd>
<div class="block">返回圆弧是否可见</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/ArcOptions.html#isVisible--">isVisible()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/ArcOptions.html" title="com.amap.api.maps.model中的类">ArcOptions</a></dt>
<dd>
<div class="block">返回弧形是否可见。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/BasePointOverlay.html#isVisible--">isVisible()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/BasePointOverlay.html" title="com.amap.api.maps.model中的类">BasePointOverlay</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/BuildingOverlay.BuildingOverlayTotalOptions.html#isVisible">isVisible</a></span> - 类 中的变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/BuildingOverlay.BuildingOverlayTotalOptions.html" title="com.amap.api.maps.model中的类">BuildingOverlay.BuildingOverlayTotalOptions</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/BuildingOverlay.html#isVisible--">isVisible()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/BuildingOverlay.html" title="com.amap.api.maps.model中的类">BuildingOverlay</a></dt>
<dd>
<div class="block">获取overlay是否显示</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/BuildingOverlayOptions.html#isVisible--">isVisible()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/BuildingOverlayOptions.html" title="com.amap.api.maps.model中的类">BuildingOverlayOptions</a></dt>
<dd>
<div class="block">获取overlay是否显示</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Circle.html#isVisible--">isVisible()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Circle.html" title="com.amap.api.maps.model中的类">Circle</a></dt>
<dd>
<div class="block">获取圆是否可见</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/CircleOptions.html#isVisible--">isVisible()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/CircleOptions.html" title="com.amap.api.maps.model中的类">CircleOptions</a></dt>
<dd>
<div class="block">获取圆是否可见</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/GroundOverlay.html#isVisible--">isVisible()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/GroundOverlay.html" title="com.amap.api.maps.model中的类">GroundOverlay</a></dt>
<dd>
<div class="block">得到当前ground 覆盖物的可见性。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/GroundOverlayOptions.html#isVisible--">isVisible()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/GroundOverlayOptions.html" title="com.amap.api.maps.model中的类">GroundOverlayOptions</a></dt>
<dd>
<div class="block">获取ground 覆盖物是否可见。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/HeatMapGridLayer.html#isVisible--">isVisible()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/HeatMapGridLayer.html" title="com.amap.api.maps.model中的类">HeatMapGridLayer</a></dt>
<dd>
<div class="block">获取图层是否可见。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/HeatMapGridLayerOptions.html#isVisible--">isVisible()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/HeatMapGridLayerOptions.html" title="com.amap.api.maps.model中的类">HeatMapGridLayerOptions</a></dt>
<dd>
<div class="block">获取的可见属性</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/HeatMapLayer.html#isVisible--">isVisible()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/HeatMapLayer.html" title="com.amap.api.maps.model中的类">HeatMapLayer</a></dt>
<dd>
<div class="block">获取图层是否可见。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/HeatMapLayerOptions.html#isVisible--">isVisible()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/HeatMapLayerOptions.html" title="com.amap.api.maps.model中的类">HeatMapLayerOptions</a></dt>
<dd>
<div class="block">获取的可见属性</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Marker.html#isVisible--">isVisible()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Marker.html" title="com.amap.api.maps.model中的类">Marker</a></dt>
<dd>
<div class="block">返回Marker是否可见。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MarkerOptions.html#isVisible--">isVisible()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MarkerOptions.html" title="com.amap.api.maps.model中的类">MarkerOptions</a></dt>
<dd>
<div class="block">获取Marker覆盖物是否可见。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/NavigateArrow.html#isVisible--">isVisible()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/NavigateArrow.html" title="com.amap.api.maps.model中的类">NavigateArrow</a></dt>
<dd>
<div class="block">获取箭头(NavigateArrow)覆盖物是否可见。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/NavigateArrowOptions.html#isVisible--">isVisible()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/NavigateArrowOptions.html" title="com.amap.api.maps.model中的类">NavigateArrowOptions</a></dt>
<dd>
<div class="block">获取箭头(NavigateArrow)覆盖物是否可见</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Polygon.html#isVisible--">isVisible()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Polygon.html" title="com.amap.api.maps.model中的类">Polygon</a></dt>
<dd>
<div class="block">获取多边形是否可见。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/PolygonOptions.html#isVisible--">isVisible()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/PolygonOptions.html" title="com.amap.api.maps.model中的类">PolygonOptions</a></dt>
<dd>
<div class="block">获取多边形是否可见</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Polyline.html#isVisible--">isVisible()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Polyline.html" title="com.amap.api.maps.model中的类">Polyline</a></dt>
<dd>
<div class="block">获取线段是否可见</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/PolylineOptions.html#isVisible--">isVisible()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/PolylineOptions.html" title="com.amap.api.maps.model中的类">PolylineOptions</a></dt>
<dd>
<div class="block">获取线段的可见属性</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Text.html#isVisible--">isVisible()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Text.html" title="com.amap.api.maps.model中的类">Text</a></dt>
<dd>
<div class="block">获取文字覆盖物是否可见</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/TextOptions.html#isVisible--">isVisible()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/TextOptions.html" title="com.amap.api.maps.model中的类">TextOptions</a></dt>
<dd>
<div class="block">获取文字覆盖物的可见性</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/TileOverlay.html#isVisible--">isVisible()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/TileOverlay.html" title="com.amap.api.maps.model中的类">TileOverlay</a></dt>
<dd>
<div class="block">获取瓦片图层是否可见。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/TileOverlayOptions.html#isVisible--">isVisible()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/TileOverlayOptions.html" title="com.amap.api.maps.model中的类">TileOverlayOptions</a></dt>
<dd>
<div class="block">获取瓦片图层是否可见</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/UiSettings.html#isZoomControlsEnabled--">isZoomControlsEnabled()</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/UiSettings.html" title="com.amap.api.maps中的类">UiSettings</a></dt>
<dd>
<div class="block">返回缩放按钮是否可见。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/UiSettings.html#isZoomGesturesEnabled--">isZoomGesturesEnabled()</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/UiSettings.html" title="com.amap.api.maps中的类">UiSettings</a></dt>
<dd>
<div class="block">返回缩放手势是否可用。</div>
</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">K</a>&nbsp;<a href="index-11.html">L</a>&nbsp;<a href="index-12.html">M</a>&nbsp;<a href="index-13.html">N</a>&nbsp;<a href="index-14.html">O</a>&nbsp;<a href="index-15.html">P</a>&nbsp;<a href="index-16.html">Q</a>&nbsp;<a href="index-17.html">R</a>&nbsp;<a href="index-18.html">S</a>&nbsp;<a href="index-19.html">T</a>&nbsp;<a href="index-20.html">U</a>&nbsp;<a href="index-21.html">V</a>&nbsp;<a href="index-22.html">W</a>&nbsp;<a href="index-23.html">Z</a>&nbsp;</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-8.html">上一个字母</a></li>
<li><a href="index-10.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-9.html" target="_top">框架</a></li>
<li><a href="index-9.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
