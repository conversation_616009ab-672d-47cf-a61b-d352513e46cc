<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>A - 索引</title>
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="A - \u7D22\u5F15";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个字母</li>
<li><a href="index-2.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-1.html" target="_top">框架</a></li>
<li><a href="index-1.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="contentContainer"><a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">K</a>&nbsp;<a href="index-11.html">L</a>&nbsp;<a href="index-12.html">M</a>&nbsp;<a href="index-13.html">N</a>&nbsp;<a href="index-14.html">O</a>&nbsp;<a href="index-15.html">P</a>&nbsp;<a href="index-16.html">Q</a>&nbsp;<a href="index-17.html">R</a>&nbsp;<a href="index-18.html">S</a>&nbsp;<a href="index-19.html">T</a>&nbsp;<a href="index-20.html">U</a>&nbsp;<a href="index-21.html">V</a>&nbsp;<a href="index-22.html">W</a>&nbsp;<a href="index-23.html">Z</a>&nbsp;<a name="I:A">
<!--   -->
</a>
<h2 class="title">A</h2>
<dl>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/utils/SpatialRelationUtil.html#A_CIRCLE_DEGREE">A_CIRCLE_DEGREE</a></span> - 类 中的静态变量com.amap.api.maps.utils.<a href="../com/amap/api/maps/utils/SpatialRelationUtil.html" title="com.amap.api.maps.utils中的类">SpatialRelationUtil</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/utils/SpatialRelationUtil.html#A_HALF_CIRCLE_DEGREE">A_HALF_CIRCLE_DEGREE</a></span> - 类 中的静态变量com.amap.api.maps.utils.<a href="../com/amap/api/maps/utils/SpatialRelationUtil.html" title="com.amap.api.maps.utils中的类">SpatialRelationUtil</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/LocationSource.html#activate-com.amap.api.maps.LocationSource.OnLocationChangedListener-">activate(LocationSource.OnLocationChangedListener)</a></span> - 接口 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/LocationSource.html" title="com.amap.api.maps中的接口">LocationSource</a></dt>
<dd>
<div class="block">激活定位源。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/IndoorBuildingInfo.html#activeFloorIndex">activeFloorIndex</a></span> - 类 中的变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/IndoorBuildingInfo.html" title="com.amap.api.maps.model中的类">IndoorBuildingInfo</a></dt>
<dd>
<div class="block">当显示楼层,如 1</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/IndoorBuildingInfo.html#activeFloorName">activeFloorName</a></span> - 类 中的变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/IndoorBuildingInfo.html" title="com.amap.api.maps.model中的类">IndoorBuildingInfo</a></dt>
<dd>
<div class="block">当前显示楼层的名称，如 F1</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/NavigateArrowOptions.html#add-com.amap.api.maps.model.LatLng-">add(LatLng)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/NavigateArrowOptions.html" title="com.amap.api.maps.model中的类">NavigateArrowOptions</a></dt>
<dd>
<div class="block">追加一个顶点坐标到箭头的终点。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/NavigateArrowOptions.html#add-com.amap.api.maps.model.LatLng...-">add(LatLng...)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/NavigateArrowOptions.html" title="com.amap.api.maps.model中的类">NavigateArrowOptions</a></dt>
<dd>
<div class="block">追加一批顶点坐标到箭头终点。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/PolygonOptions.html#add-com.amap.api.maps.model.LatLng-">add(LatLng)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/PolygonOptions.html" title="com.amap.api.maps.model中的类">PolygonOptions</a></dt>
<dd>
<div class="block">添加一个多边形边框的顶点</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/PolygonOptions.html#add-com.amap.api.maps.model.LatLng...-">add(LatLng...)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/PolygonOptions.html" title="com.amap.api.maps.model中的类">PolygonOptions</a></dt>
<dd>
<div class="block">添加多个多边形边框的顶点</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/PolylineOptions.html#add-com.amap.api.maps.model.LatLng-">add(LatLng)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/PolylineOptions.html" title="com.amap.api.maps.model中的类">PolylineOptions</a></dt>
<dd>
<div class="block">追加一个顶点到线段的坐标集合。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/PolylineOptions.html#add-com.amap.api.maps.model.LatLng...-">add(LatLng...)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/PolylineOptions.html" title="com.amap.api.maps.model中的类">PolylineOptions</a></dt>
<dd>
<div class="block">追加一批顶点到线段的坐标集合。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/trace/TraceOverlay.html#add-java.util.List-">add(List&lt;LatLng&gt;)</a></span> - 类 中的方法com.amap.api.trace.<a href="../com/amap/api/trace/TraceOverlay.html" title="com.amap.api.trace中的类">TraceOverlay</a></dt>
<dd>
<div class="block">添加轨迹回调的一段，需要按照index顺序添加才能正常绘制</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/NavigateArrowOptions.html#addAll-java.lang.Iterable-">addAll(Iterable&lt;LatLng&gt;)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/NavigateArrowOptions.html" title="com.amap.api.maps.model中的类">NavigateArrowOptions</a></dt>
<dd>
<div class="block">追加一批顶点坐标到箭头终点。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/PolygonHoleOptions.html#addAll-java.lang.Iterable-">addAll(Iterable&lt;LatLng&gt;)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/PolygonHoleOptions.html" title="com.amap.api.maps.model中的类">PolygonHoleOptions</a></dt>
<dd>
<div class="block">添加多边形洞边框的顶点</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/PolygonOptions.html#addAll-java.lang.Iterable-">addAll(Iterable&lt;LatLng&gt;)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/PolygonOptions.html" title="com.amap.api.maps.model中的类">PolygonOptions</a></dt>
<dd>
<div class="block">添加多个多边形边框的顶点</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/PolylineOptions.html#addAll-java.lang.Iterable-">addAll(Iterable&lt;LatLng&gt;)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/PolylineOptions.html" title="com.amap.api.maps.model中的类">PolylineOptions</a></dt>
<dd>
<div class="block">追加一批顶点到线段的坐标集合。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#addAMapAppResourceListener-com.amap.api.maps.AMap.AMapAppResourceRequestListener-">addAMapAppResourceListener(AMap.AMapAppResourceRequestListener)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/animation/AnimationSet.html#addAnimation-com.amap.api.maps.model.animation.Animation-">addAnimation(Animation)</a></span> - 类 中的方法com.amap.api.maps.model.animation.<a href="../com/amap/api/maps/model/animation/AnimationSet.html" title="com.amap.api.maps.model.animation中的类">AnimationSet</a></dt>
<dd>
<div class="block">添加动画</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#addArc-com.amap.api.maps.model.ArcOptions-">addArc(ArcOptions)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block">在地图上添加一个圆弧（arc）对象。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#addBuildingOverlay--">addBuildingOverlay()</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block">添加建筑物图层，默认图层的区域为全世界。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#addCircle-com.amap.api.maps.model.CircleOptions-">addCircle(CircleOptions)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block">在地图上添加一个圆（circle）对象。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#addGLTFOverlay-com.amap.api.maps.model.GLTFOverlayOptions-">addGLTFOverlay(GLTFOverlayOptions)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block">在地图上添加一个gltf对象。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#addGroundOverlay-com.amap.api.maps.model.GroundOverlayOptions-">addGroundOverlay(GroundOverlayOptions)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block">在地图上添加一个Ground覆盖物（groundOverlay）对象</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#addHeatMapGridLayer-com.amap.api.maps.model.HeatMapGridLayerOptions-">addHeatMapGridLayer(HeatMapGridLayerOptions)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block">在地图上添加一个热力图网格对象。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#addHeatMapLayer-com.amap.api.maps.model.HeatMapLayerOptions-">addHeatMapLayer(HeatMapLayerOptions)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block">在地图上添加一个热力图对象。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/CircleOptions.html#addHoles-com.amap.api.maps.model.BaseHoleOptions...-">addHoles(BaseHoleOptions...)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/CircleOptions.html" title="com.amap.api.maps.model中的类">CircleOptions</a></dt>
<dd>
<div class="block">添加空心洞的配置项</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/CircleOptions.html#addHoles-java.lang.Iterable-">addHoles(Iterable&lt;BaseHoleOptions&gt;)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/CircleOptions.html" title="com.amap.api.maps.model中的类">CircleOptions</a></dt>
<dd>
<div class="block">添加空心洞的配置项</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/PolygonOptions.html#addHoles-com.amap.api.maps.model.BaseHoleOptions...-">addHoles(BaseHoleOptions...)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/PolygonOptions.html" title="com.amap.api.maps.model中的类">PolygonOptions</a></dt>
<dd>
<div class="block">设置空心洞的配置项</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/PolygonOptions.html#addHoles-java.lang.Iterable-">addHoles(Iterable&lt;BaseHoleOptions&gt;)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/PolygonOptions.html" title="com.amap.api.maps.model中的类">PolygonOptions</a></dt>
<dd>
<div class="block">添加空心洞的配置项</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#addMarker-com.amap.api.maps.model.MarkerOptions-">addMarker(MarkerOptions)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block">在地图上添一个图片标记（marker）对象。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#addMarkers-java.util.ArrayList-boolean-">addMarkers(ArrayList&lt;MarkerOptions&gt;, boolean)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block">在地图上添一组图片标记（marker）对象，并设置是否改变地图状态以至于所有的marker对象都在当前地图可视区域范围内显示。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#addMultiPointOverlay-com.amap.api.maps.model.MultiPointOverlayOptions-">addMultiPointOverlay(MultiPointOverlayOptions)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block">在地图上添加一个海量点覆盖物（MultiPointOverlay）对象</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#addMVTTileOverlay-com.amap.api.maps.model.MVTTileOverlayOptions-">addMVTTileOverlay(MVTTileOverlayOptions)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block">在地图上添加一个瓦片图层覆盖物（MVTtileOverlay）对象。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#addNavigateArrow-com.amap.api.maps.model.NavigateArrowOptions-">addNavigateArrow(NavigateArrowOptions)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block">在地图上添加一个导航指示箭头对象（navigateArrow）对象。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#addParticleOverlay-com.amap.api.maps.model.particle.ParticleOverlayOptions-">addParticleOverlay(ParticleOverlayOptions)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block">在地图上添加一个粒子系统对象</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#addPolygon-com.amap.api.maps.model.PolygonOptions-">addPolygon(PolygonOptions)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block">在地图上添加一个多边形（polygon）对象。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#addPolyline-com.amap.api.maps.model.PolylineOptions-">addPolyline(PolylineOptions)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block">在地图上添加一个折线对象（polyline）对象。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#addText-com.amap.api.maps.model.TextOptions-">addText(TextOptions)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block">在地图上添一个文字标记（text）对象。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#addTileOverlay-com.amap.api.maps.model.TileOverlayOptions-">addTileOverlay(TileOverlayOptions)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block">在地图上添加一个瓦片图层覆盖物（tileOverlay）对象。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/TextOptions.html#align-int-int-">align(int, int)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/TextOptions.html" title="com.amap.api.maps.model中的类">TextOptions</a></dt>
<dd>
<div class="block">设置文字覆盖物的对齐方式,默认居中对齐。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Text.html#ALIGN_BOTTOM">ALIGN_BOTTOM</a></span> - 类 中的静态变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Text.html" title="com.amap.api.maps.model中的类">Text</a></dt>
<dd>
<div class="block">下对齐。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Text.html#ALIGN_CENTER_HORIZONTAL">ALIGN_CENTER_HORIZONTAL</a></span> - 类 中的静态变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Text.html" title="com.amap.api.maps.model中的类">Text</a></dt>
<dd>
<div class="block">水平居中对齐。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Text.html#ALIGN_CENTER_VERTICAL">ALIGN_CENTER_VERTICAL</a></span> - 类 中的静态变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Text.html" title="com.amap.api.maps.model中的类">Text</a></dt>
<dd>
<div class="block">垂直居中对齐。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Text.html#ALIGN_LEFT">ALIGN_LEFT</a></span> - 类 中的静态变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Text.html" title="com.amap.api.maps.model中的类">Text</a></dt>
<dd>
<div class="block">左对齐。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Text.html#ALIGN_RIGHT">ALIGN_RIGHT</a></span> - 类 中的静态变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Text.html" title="com.amap.api.maps.model中的类">Text</a></dt>
<dd>
<div class="block">右对齐。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Text.html#ALIGN_TOP">ALIGN_TOP</a></span> - 类 中的静态变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Text.html" title="com.amap.api.maps.model中的类">Text</a></dt>
<dd>
<div class="block">上对齐。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/BuildingOverlay.BuildingOverlayTotalOptions.html#allOptionList">allOptionList</a></span> - 类 中的变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/BuildingOverlay.BuildingOverlayTotalOptions.html" title="com.amap.api.maps.model中的类">BuildingOverlay.BuildingOverlayTotalOptions</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MarkerOptions.html#alpha-float-">alpha(float)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MarkerOptions.html" title="com.amap.api.maps.model中的类">MarkerOptions</a></dt>
<dd>
<div class="block">设置Marker覆盖物的透明度</div>
</dd>
<dt><a href="../com/amap/api/maps/model/animation/AlphaAnimation.html" title="com.amap.api.maps.model.animation中的类"><span class="typeNameLink">AlphaAnimation</span></a> - <a href="../com/amap/api/maps/model/animation/package-summary.html">com.amap.api.maps.model.animation</a>中的类</dt>
<dd>
<div class="block">控制透明度的动画类</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/animation/AlphaAnimation.html#AlphaAnimation-float-float-">AlphaAnimation(float, float)</a></span> - 类 的构造器com.amap.api.maps.model.animation.<a href="../com/amap/api/maps/model/animation/AlphaAnimation.html" title="com.amap.api.maps.model.animation中的类">AlphaAnimation</a></dt>
<dd>
<div class="block">控制透明度的动画类<br>
 透明度范围[0,1], 1为不透明</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/GLTFOverlayOptions.html#altitude-double-">altitude(double)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/GLTFOverlayOptions.html" title="com.amap.api.maps.model中的类">GLTFOverlayOptions</a></dt>
<dd>
<div class="block">设置模型距离地面的高度</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MarkerOptions.html#altitude-float-">altitude(float)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MarkerOptions.html" title="com.amap.api.maps.model中的类">MarkerOptions</a></dt>
<dd>
<div class="block">marker的海拔</div>
</dd>
<dt><a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类"><span class="typeNameLink">AMap</span></a> - <a href="../com/amap/api/maps/package-summary.html">com.amap.api.maps</a>中的类</dt>
<dd>
<div class="block">定义AMap 地图对象的操作方法与接口。</div>
</dd>
<dt><a href="../com/amap/api/maps/AMap.AMapAppResourceRequestListener.html" title="com.amap.api.maps中的接口"><span class="typeNameLink">AMap.AMapAppResourceRequestListener</span></a> - <a href="../com/amap/api/maps/package-summary.html">com.amap.api.maps</a>中的接口</dt>
<dd>&nbsp;</dd>
<dt><a href="../com/amap/api/maps/AMap.CancelableCallback.html" title="com.amap.api.maps中的接口"><span class="typeNameLink">AMap.CancelableCallback</span></a> - <a href="../com/amap/api/maps/package-summary.html">com.amap.api.maps</a>中的接口</dt>
<dd>
<div class="block">在<a href="../com/amap/api/maps/AMap.html#animateCamera-com.amap.api.maps.CameraUpdate-com.amap.api.maps.AMap.CancelableCallback-"><code>AMap.animateCamera(CameraUpdate, CancelableCallback)</code></a>设置一个CancelableCallback，用来监听该CameraUpdate是否执行完成或者被中断。</div>
</dd>
<dt><a href="../com/amap/api/maps/AMap.ImageInfoWindowAdapter.html" title="com.amap.api.maps中的接口"><span class="typeNameLink">AMap.ImageInfoWindowAdapter</span></a> - <a href="../com/amap/api/maps/package-summary.html">com.amap.api.maps</a>中的接口</dt>
<dd>
<div class="block">用来实现marker与对应InfoWindow同步移动<br>
 默认情况下，InfoWindow是一个View， 拖动地图的时候由于View 布局较慢，会有延迟的效果。</div>
</dd>
<dt><a href="../com/amap/api/maps/AMap.InfoWindowAdapter.html" title="com.amap.api.maps中的接口"><span class="typeNameLink">AMap.InfoWindowAdapter</span></a> - <a href="../com/amap/api/maps/package-summary.html">com.amap.api.maps</a>中的接口</dt>
<dd>
<div class="block">用来定制marker的信息窗口<br>
 默认情况下，当单击某个marker时，如果该marker的Title和Snippet不为空，则会触发getInfoWindow和getInfoContents回调。</div>
</dd>
<dt><a href="../com/amap/api/maps/AMap.OnCacheRemoveListener.html" title="com.amap.api.maps中的接口"><span class="typeNameLink">AMap.OnCacheRemoveListener</span></a> - <a href="../com/amap/api/maps/package-summary.html">com.amap.api.maps</a>中的接口</dt>
<dd>
<div class="block">缓存数据清除监听接口</div>
</dd>
<dt><a href="../com/amap/api/maps/AMap.OnCameraChangeListener.html" title="com.amap.api.maps中的接口"><span class="typeNameLink">AMap.OnCameraChangeListener</span></a> - <a href="../com/amap/api/maps/package-summary.html">com.amap.api.maps</a>中的接口</dt>
<dd>
<div class="block">地图状态发生变化的监听接口。</div>
</dd>
<dt><a href="../com/amap/api/maps/AMap.OnIndoorBuildingActiveListener.html" title="com.amap.api.maps中的接口"><span class="typeNameLink">AMap.OnIndoorBuildingActiveListener</span></a> - <a href="../com/amap/api/maps/package-summary.html">com.amap.api.maps</a>中的接口</dt>
<dd>
<div class="block">室内地图状态监听接口。</div>
</dd>
<dt><a href="../com/amap/api/maps/AMap.OnInfoWindowClickListener.html" title="com.amap.api.maps中的接口"><span class="typeNameLink">AMap.OnInfoWindowClickListener</span></a> - <a href="../com/amap/api/maps/package-summary.html">com.amap.api.maps</a>中的接口</dt>
<dd>
<div class="block">marker的信息窗口点击事件监听接口。</div>
</dd>
<dt><a href="../com/amap/api/maps/AMap.OnMapClickListener.html" title="com.amap.api.maps中的接口"><span class="typeNameLink">AMap.OnMapClickListener</span></a> - <a href="../com/amap/api/maps/package-summary.html">com.amap.api.maps</a>中的接口</dt>
<dd>
<div class="block">地图点击事件监听接口。</div>
</dd>
<dt><a href="../com/amap/api/maps/AMap.OnMapLoadedListener.html" title="com.amap.api.maps中的接口"><span class="typeNameLink">AMap.OnMapLoadedListener</span></a> - <a href="../com/amap/api/maps/package-summary.html">com.amap.api.maps</a>中的接口</dt>
<dd>
<div class="block">地图加载完成监听接口</div>
</dd>
<dt><a href="../com/amap/api/maps/AMap.OnMapLongClickListener.html" title="com.amap.api.maps中的接口"><span class="typeNameLink">AMap.OnMapLongClickListener</span></a> - <a href="../com/amap/api/maps/package-summary.html">com.amap.api.maps</a>中的接口</dt>
<dd>
<div class="block">地图长按事件监听接口。</div>
</dd>
<dt><a href="../com/amap/api/maps/AMap.onMapPrintScreenListener.html" title="com.amap.api.maps中的接口"><span class="typeNameLink">AMap.onMapPrintScreenListener</span></a> - <a href="../com/amap/api/maps/package-summary.html">com.amap.api.maps</a>中的接口</dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span>
<div class="block"><span class="deprecationComment">建议使用 <a href="../com/amap/api/maps/AMap.OnMapScreenShotListener.html" title="com.amap.api.maps中的接口"><code>AMap.OnMapScreenShotListener</code></a></span></div>
</div>
</dd>
<dt><a href="../com/amap/api/maps/AMap.OnMapScreenShotListener.html" title="com.amap.api.maps中的接口"><span class="typeNameLink">AMap.OnMapScreenShotListener</span></a> - <a href="../com/amap/api/maps/package-summary.html">com.amap.api.maps</a>中的接口</dt>
<dd>
<div class="block">地图截屏监听接口 。</div>
</dd>
<dt><a href="../com/amap/api/maps/AMap.OnMapSnapshotListener.html" title="com.amap.api.maps中的接口"><span class="typeNameLink">AMap.OnMapSnapshotListener</span></a> - <a href="../com/amap/api/maps/package-summary.html">com.amap.api.maps</a>中的接口</dt>
<dd>
<div class="block">地图区域图异步返回接口 。</div>
</dd>
<dt><a href="../com/amap/api/maps/AMap.OnMapTouchListener.html" title="com.amap.api.maps中的接口"><span class="typeNameLink">AMap.OnMapTouchListener</span></a> - <a href="../com/amap/api/maps/package-summary.html">com.amap.api.maps</a>中的接口</dt>
<dd>
<div class="block">地图触摸事件监听接口。</div>
</dd>
<dt><a href="../com/amap/api/maps/AMap.OnMarkerClickListener.html" title="com.amap.api.maps中的接口"><span class="typeNameLink">AMap.OnMarkerClickListener</span></a> - <a href="../com/amap/api/maps/package-summary.html">com.amap.api.maps</a>中的接口</dt>
<dd>
<div class="block">marker点击事件监听接口。</div>
</dd>
<dt><a href="../com/amap/api/maps/AMap.OnMarkerDragListener.html" title="com.amap.api.maps中的接口"><span class="typeNameLink">AMap.OnMarkerDragListener</span></a> - <a href="../com/amap/api/maps/package-summary.html">com.amap.api.maps</a>中的接口</dt>
<dd>
<div class="block">marker拖动事件监听接口。</div>
</dd>
<dt><a href="../com/amap/api/maps/AMap.OnMultiPointClickListener.html" title="com.amap.api.maps中的接口"><span class="typeNameLink">AMap.OnMultiPointClickListener</span></a> - <a href="../com/amap/api/maps/package-summary.html">com.amap.api.maps</a>中的接口</dt>
<dd>
<div class="block">海量点中某一点被点击时的回调。</div>
</dd>
<dt><a href="../com/amap/api/maps/AMap.OnMyLocationChangeListener.html" title="com.amap.api.maps中的接口"><span class="typeNameLink">AMap.OnMyLocationChangeListener</span></a> - <a href="../com/amap/api/maps/package-summary.html">com.amap.api.maps</a>中的接口</dt>
<dd>
<div class="block">用户定位信息监听接口。</div>
</dd>
<dt><a href="../com/amap/api/maps/AMap.OnPOIClickListener.html" title="com.amap.api.maps中的接口"><span class="typeNameLink">AMap.OnPOIClickListener</span></a> - <a href="../com/amap/api/maps/package-summary.html">com.amap.api.maps</a>中的接口</dt>
<dd>
<div class="block">地图底图poi点击事件监听接口。</div>
</dd>
<dt><a href="../com/amap/api/maps/AMap.OnPolylineClickListener.html" title="com.amap.api.maps中的接口"><span class="typeNameLink">AMap.OnPolylineClickListener</span></a> - <a href="../com/amap/api/maps/package-summary.html">com.amap.api.maps</a>中的接口</dt>
<dd>
<div class="block">polyline点击事件监听接口。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/amap3dmodeltile/AMap3DModelTileProvider.AMap3DModelRequest.html#AMap3DModelRequest-java.lang.String-">AMap3DModelRequest(String)</a></span> - 类 的构造器com.amap.api.maps.model.amap3dmodeltile.<a href="../com/amap/api/maps/model/amap3dmodeltile/AMap3DModelTileProvider.AMap3DModelRequest.html" title="com.amap.api.maps.model.amap3dmodeltile中的类">AMap3DModelTileProvider.AMap3DModelRequest</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/amap/api/maps/model/amap3dmodeltile/AMap3DModelTileProvider.AMap3DModelRequest.html" title="com.amap.api.maps.model.amap3dmodeltile中的类"><span class="typeNameLink">AMap3DModelTileProvider.AMap3DModelRequest</span></a> - <a href="../com/amap/api/maps/model/amap3dmodeltile/package-summary.html">com.amap.api.maps.model.amap3dmodeltile</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMapException.html#AMAP_NOT_SUPPORT">AMAP_NOT_SUPPORT</a></span> - 异常错误 中的静态变量com.amap.api.maps.<a href="../com/amap/api/maps/AMapException.html" title="com.amap.api.maps中的类">AMapException</a></dt>
<dd>
<div class="block">移动设备上未安装高德地图或高德地图版本较旧</div>
</dd>
<dt><a href="../com/amap/api/maps/model/AMapCameraInfo.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">AMapCameraInfo</span></a> - <a href="../com/amap/api/maps/model/package-summary.html">com.amap.api.maps.model</a>中的类</dt>
<dd>
<div class="block">引擎Camera参数获取</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/AMapCameraInfo.html#AMapCameraInfo-float-float-float-float-float-float-">AMapCameraInfo(float, float, float, float, float, float)</a></span> - 类 的构造器com.amap.api.maps.model.<a href="../com/amap/api/maps/model/AMapCameraInfo.html" title="com.amap.api.maps.model中的类">AMapCameraInfo</a></dt>
<dd>
<div class="block">构造函数</div>
</dd>
<dt><a href="../com/amap/api/maps/AMapException.html" title="com.amap.api.maps中的类"><span class="typeNameLink">AMapException</span></a> - <a href="../com/amap/api/maps/package-summary.html">com.amap.api.maps</a>中的异常错误</dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMapException.html#AMapException-java.lang.String-">AMapException(String)</a></span> - 异常错误 的构造器com.amap.api.maps.<a href="../com/amap/api/maps/AMapException.html" title="com.amap.api.maps中的类">AMapException</a></dt>
<dd>
<div class="block">构造定位异常对象。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMapException.html#AMapException--">AMapException()</a></span> - 异常错误 的构造器com.amap.api.maps.<a href="../com/amap/api/maps/AMapException.html" title="com.amap.api.maps中的类">AMapException</a></dt>
<dd>
<div class="block">构造定位异常对象。</div>
</dd>
<dt><a href="../com/amap/api/maps/model/AMapGestureListener.html" title="com.amap.api.maps.model中的接口"><span class="typeNameLink">AMapGestureListener</span></a> - <a href="../com/amap/api/maps/model/package-summary.html">com.amap.api.maps.model</a>中的接口</dt>
<dd>
<div class="block">地图手势识别的回调，包含单双击、滑动等以及地图操作地图后稳定下来的回调</div>
</dd>
<dt><a href="../com/amap/api/maps/model/AMapGLOverlay.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">AMapGLOverlay</span></a> - <a href="../com/amap/api/maps/model/package-summary.html">com.amap.api.maps.model</a>中的类</dt>
<dd>
<div class="block">OpenGLES绘制的自定义覆盖物</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/AMapGLOverlay.html#AMapGLOverlay--">AMapGLOverlay()</a></span> - 类 的构造器com.amap.api.maps.model.<a href="../com/amap/api/maps/model/AMapGLOverlay.html" title="com.amap.api.maps.model中的类">AMapGLOverlay</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/amap/api/maps/AMapOptions.html" title="com.amap.api.maps中的类"><span class="typeNameLink">AMapOptions</span></a> - <a href="../com/amap/api/maps/package-summary.html">com.amap.api.maps</a>中的类</dt>
<dd>
<div class="block">MapView 初始化选项。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMapOptions.html#AMapOptions--">AMapOptions()</a></span> - 类 的构造器com.amap.api.maps.<a href="../com/amap/api/maps/AMapOptions.html" title="com.amap.api.maps中的类">AMapOptions</a></dt>
<dd>
<div class="block">MapView 初始化选项。</div>
</dd>
<dt><a href="../com/amap/api/maps/model/AMapPara.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">AMapPara</span></a> - <a href="../com/amap/api/maps/model/package-summary.html">com.amap.api.maps.model</a>中的类</dt>
<dd>
<div class="block">地图中通用参数，如虚线类型可以使用在多个覆盖物中作为参数</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/AMapPara.html#AMapPara--">AMapPara()</a></span> - 类 的构造器com.amap.api.maps.model.<a href="../com/amap/api/maps/model/AMapPara.html" title="com.amap.api.maps.model中的类">AMapPara</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/amap/api/maps/model/AMapPara.LineCapType.html" title="com.amap.api.maps.model中的枚举"><span class="typeNameLink">AMapPara.LineCapType</span></a> - <a href="../com/amap/api/maps/model/package-summary.html">com.amap.api.maps.model</a>中的枚举</dt>
<dd>
<div class="block">边框尾部形状</div>
</dd>
<dt><a href="../com/amap/api/maps/model/AMapPara.LineJoinType.html" title="com.amap.api.maps.model中的枚举"><span class="typeNameLink">AMapPara.LineJoinType</span></a> - <a href="../com/amap/api/maps/model/package-summary.html">com.amap.api.maps.model</a>中的枚举</dt>
<dd>
<div class="block">边框连接处形状</div>
</dd>
<dt><a href="../com/amap/api/maps/AMapUtils.html" title="com.amap.api.maps中的类"><span class="typeNameLink">AMapUtils</span></a> - <a href="../com/amap/api/maps/package-summary.html">com.amap.api.maps</a>中的类</dt>
<dd>
<div class="block">AMap辅助工具类，包含计算距离、面积、调起高德地图APP进行导航等系列功能。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMapUtils.html#AMapUtils--">AMapUtils()</a></span> - 类 的构造器com.amap.api.maps.<a href="../com/amap/api/maps/AMapUtils.html" title="com.amap.api.maps中的类">AMapUtils</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/GroundOverlayOptions.html#anchor-float-float-">anchor(float, float)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/GroundOverlayOptions.html" title="com.amap.api.maps.model中的类">GroundOverlayOptions</a></dt>
<dd>
<div class="block">设置图片的对齐方式，[0,0]是左上角，[1,1]是右下角 。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MarkerOptions.html#anchor-float-float-">anchor(float, float)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MarkerOptions.html" title="com.amap.api.maps.model中的类">MarkerOptions</a></dt>
<dd>
<div class="block">设置Marker覆盖物的锚点比例。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MultiPointOverlayOptions.html#anchor-float-float-">anchor(float, float)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MultiPointOverlayOptions.html" title="com.amap.api.maps.model中的类">MultiPointOverlayOptions</a></dt>
<dd>
<div class="block">设置海量点的锚点比例。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MyLocationStyle.html#anchor-float-float-">anchor(float, float)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MyLocationStyle.html" title="com.amap.api.maps.model中的类">MyLocationStyle</a></dt>
<dd>
<div class="block">设置定位图标的锚点。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#animateCamera-com.amap.api.maps.CameraUpdate-">animateCamera(CameraUpdate)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block">以动画方式按照传入的CameraUpdate参数更新地图状态，默认动画耗时250毫秒。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#animateCamera-com.amap.api.maps.CameraUpdate-com.amap.api.maps.AMap.CancelableCallback-">animateCamera(CameraUpdate, AMap.CancelableCallback)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block">以动画方式按照传入的CameraUpdate参数更新地图状态，默认动画耗时250毫秒，同时设置一个cancelableCallback来监听动画执行的结果。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#animateCamera-com.amap.api.maps.CameraUpdate-long-com.amap.api.maps.AMap.CancelableCallback-">animateCamera(CameraUpdate, long, AMap.CancelableCallback)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block">按照指定的动画时长及传入的CameraUpdate参数更新地图状态，，同时设置一个cancelableCallback来监听动画执行的结果。</div>
</dd>
<dt><a href="../com/amap/api/maps/model/animation/Animation.html" title="com.amap.api.maps.model.animation中的类"><span class="typeNameLink">Animation</span></a> - <a href="../com/amap/api/maps/model/animation/package-summary.html">com.amap.api.maps.model.animation</a>中的类</dt>
<dd>
<div class="block">动画,可用于支持动画的覆盖物<br>
 使用方法如同Android系统自带的<code>Animation</code></div>
</dd>
<dt><a href="../com/amap/api/maps/model/animation/Animation.AnimationListener.html" title="com.amap.api.maps.model.animation中的接口"><span class="typeNameLink">Animation.AnimationListener</span></a> - <a href="../com/amap/api/maps/model/animation/package-summary.html">com.amap.api.maps.model.animation</a>中的接口</dt>
<dd>
<div class="block">动画监听，包含动画开始和结束时的回调</div>
</dd>
<dt><a href="../com/amap/api/maps/model/animation/AnimationSet.html" title="com.amap.api.maps.model.animation中的类"><span class="typeNameLink">AnimationSet</span></a> - <a href="../com/amap/api/maps/model/animation/package-summary.html">com.amap.api.maps.model.animation</a>中的类</dt>
<dd>
<div class="block">动画集合</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/animation/AnimationSet.html#AnimationSet-boolean-">AnimationSet(boolean)</a></span> - 类 的构造器com.amap.api.maps.model.animation.<a href="../com/amap/api/maps/model/animation/AnimationSet.html" title="com.amap.api.maps.model.animation中的类">AnimationSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/animation/Animation.html#animationType">animationType</a></span> - 类 中的变量com.amap.api.maps.model.animation.<a href="../com/amap/api/maps/model/animation/Animation.html" title="com.amap.api.maps.model.animation中的类">Animation</a></dt>
<dd>
<div class="block">jni使用</div>
</dd>
<dt><a href="../com/amap/api/maps/model/Arc.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">Arc</span></a> - <a href="../com/amap/api/maps/model/package-summary.html">com.amap.api.maps.model</a>中的类</dt>
<dd>
<div class="block">定义了在地图上绘制弧形的类。</div>
</dd>
<dt><a href="../com/amap/api/maps/model/ArcOptions.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">ArcOptions</span></a> - <a href="../com/amap/api/maps/model/package-summary.html">com.amap.api.maps.model</a>中的类</dt>
<dd>
<div class="block">圆形选项类</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/ArcOptions.html#ArcOptions--">ArcOptions()</a></span> - 类 的构造器com.amap.api.maps.model.<a href="../com/amap/api/maps/model/ArcOptions.html" title="com.amap.api.maps.model中的类">ArcOptions</a></dt>
<dd>&nbsp;</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">K</a>&nbsp;<a href="index-11.html">L</a>&nbsp;<a href="index-12.html">M</a>&nbsp;<a href="index-13.html">N</a>&nbsp;<a href="index-14.html">O</a>&nbsp;<a href="index-15.html">P</a>&nbsp;<a href="index-16.html">Q</a>&nbsp;<a href="index-17.html">R</a>&nbsp;<a href="index-18.html">S</a>&nbsp;<a href="index-19.html">T</a>&nbsp;<a href="index-20.html">U</a>&nbsp;<a href="index-21.html">V</a>&nbsp;<a href="index-22.html">W</a>&nbsp;<a href="index-23.html">Z</a>&nbsp;</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个字母</li>
<li><a href="index-2.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-1.html" target="_top">框架</a></li>
<li><a href="index-1.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
