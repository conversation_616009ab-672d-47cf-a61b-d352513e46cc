<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>F - 索引</title>
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="F - \u7D22\u5F15";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-5.html">上一个字母</a></li>
<li><a href="index-7.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-6.html" target="_top">框架</a></li>
<li><a href="index-6.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="contentContainer"><a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">K</a>&nbsp;<a href="index-11.html">L</a>&nbsp;<a href="index-12.html">M</a>&nbsp;<a href="index-13.html">N</a>&nbsp;<a href="index-14.html">O</a>&nbsp;<a href="index-15.html">P</a>&nbsp;<a href="index-16.html">Q</a>&nbsp;<a href="index-17.html">R</a>&nbsp;<a href="index-18.html">S</a>&nbsp;<a href="index-19.html">T</a>&nbsp;<a href="index-20.html">U</a>&nbsp;<a href="index-21.html">V</a>&nbsp;<a href="index-22.html">W</a>&nbsp;<a href="index-23.html">Z</a>&nbsp;<a name="I:F">
<!--   -->
</a>
<h2 class="title">F</h2>
<dl>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/VisibleRegion.html#farLeft">farLeft</a></span> - 类 中的变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/VisibleRegion.html" title="com.amap.api.maps.model中的类">VisibleRegion</a></dt>
<dd>
<div class="block">可视区域的左上角。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/VisibleRegion.html#farRight">farRight</a></span> - 类 中的变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/VisibleRegion.html" title="com.amap.api.maps.model中的类">VisibleRegion</a></dt>
<dd>
<div class="block">可视区域的右上角。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMapException.html#FEATURE_GLTF_NOT_SUPPORT">FEATURE_GLTF_NOT_SUPPORT</a></span> - 异常错误 中的静态变量com.amap.api.maps.<a href="../com/amap/api/maps/AMapException.html" title="com.amap.api.maps中的类">AMapException</a></dt>
<dd>
<div class="block">GLTF功能不支持</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMapException.html#FEATURE_MVT_NOT_SUPPORT">FEATURE_MVT_NOT_SUPPORT</a></span> - 异常错误 中的静态变量com.amap.api.maps.<a href="../com/amap/api/maps/AMapException.html" title="com.amap.api.maps中的类">AMapException</a></dt>
<dd>
<div class="block">MVT功能不支持</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMapException.html#FEATURE_TERRAIN_NOT_SUPPORT">FEATURE_TERRAIN_NOT_SUPPORT</a></span> - 异常错误 中的静态变量com.amap.api.maps.<a href="../com/amap/api/maps/AMapException.html" title="com.amap.api.maps中的类">AMapException</a></dt>
<dd>
<div class="block">地形图功能不支持</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/animation/Animation.html#FILL_MODE_BACKWARDS">FILL_MODE_BACKWARDS</a></span> - 类 中的静态变量com.amap.api.maps.model.animation.<a href="../com/amap/api/maps/model/animation/Animation.html" title="com.amap.api.maps.model.animation中的类">Animation</a></dt>
<dd>
<div class="block">动画执行后保持在第一帧</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/animation/Animation.html#FILL_MODE_FORWARDS">FILL_MODE_FORWARDS</a></span> - 类 中的静态变量com.amap.api.maps.model.animation.<a href="../com/amap/api/maps/model/animation/Animation.html" title="com.amap.api.maps.model.animation中的类">Animation</a></dt>
<dd>
<div class="block">动画执行后保持在最后一帧</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/CircleOptions.html#fillColor-int-">fillColor(int)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/CircleOptions.html" title="com.amap.api.maps.model中的类">CircleOptions</a></dt>
<dd>
<div class="block">设置圆的填充颜色。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/PolygonOptions.html#fillColor-int-">fillColor(int)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/PolygonOptions.html" title="com.amap.api.maps.model中的类">PolygonOptions</a></dt>
<dd>
<div class="block">设置多边形的填充颜色，32位ARGB格式。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/IndoorBuildingInfo.html#floor_indexs">floor_indexs</a></span> - 类 中的变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/IndoorBuildingInfo.html" title="com.amap.api.maps.model中的类">IndoorBuildingInfo</a></dt>
<dd>
<div class="block">室内地图楼层数组，如[-2,-1,1,2]</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/IndoorBuildingInfo.html#floor_names">floor_names</a></span> - 类 中的变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/IndoorBuildingInfo.html" title="com.amap.api.maps.model中的类">IndoorBuildingInfo</a></dt>
<dd>
<div class="block">室内地图楼层名称数组，如['B2','B1','F1','F2']</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/TextOptions.html#fontColor-int-">fontColor(int)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/TextOptions.html" title="com.amap.api.maps.model中的类">TextOptions</a></dt>
<dd>
<div class="block">设置文字覆盖物的字体颜色,默认黑色。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/ImageOptions.html#fontSize">fontSize</a></span> - 类 中的变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/ImageOptions.html" title="com.amap.api.maps.model中的类">ImageOptions</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/TextOptions.html#fontSize-int-">fontSize(int)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/TextOptions.html" title="com.amap.api.maps.model中的类">TextOptions</a></dt>
<dd>
<div class="block">设置文字覆盖物字体大小</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/CoordinateConverter.html#from-com.amap.api.maps.CoordinateConverter.CoordType-">from(CoordinateConverter.CoordType)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/CoordinateConverter.html" title="com.amap.api.maps中的类">CoordinateConverter</a></dt>
<dd>
<div class="block">设置需要转换的坐标系类型</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/BitmapDescriptorFactory.html#fromAsset-java.lang.String-">fromAsset(String)</a></span> - 类 中的静态方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/BitmapDescriptorFactory.html" title="com.amap.api.maps.model中的类">BitmapDescriptorFactory</a></dt>
<dd>
<div class="block">根据 asset 目录内资源名称，创建 bitmap 描述信息对象。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/BitmapDescriptorFactory.html#fromBitmap-android.graphics.Bitmap-">fromBitmap(Bitmap)</a></span> - 类 中的静态方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/BitmapDescriptorFactory.html" title="com.amap.api.maps.model中的类">BitmapDescriptorFactory</a></dt>
<dd>
<div class="block">根据 Bitmap 创建 bitmap 描述信息对象。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/Projection.html#fromBoundsToTile-com.amap.api.maps.model.LatLngBounds-int-int-">fromBoundsToTile(LatLngBounds, int, int)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/Projection.html" title="com.amap.api.maps中的类">Projection</a></dt>
<dd>
<div class="block">根据指定的经纬度范围及缩放级别，返回SDK适用的瓦片编号范围（按照指定的瓦片宽度切图）。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/BitmapDescriptorFactory.html#fromFile-java.lang.String-">fromFile(String)</a></span> - 类 中的静态方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/BitmapDescriptorFactory.html" title="com.amap.api.maps.model中的类">BitmapDescriptorFactory</a></dt>
<dd>
<div class="block">根据应用程序私有文件夹里包含文件的文件名创建 bitmap 描述信息对象。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/CameraPosition.html#fromLatLngZoom-com.amap.api.maps.model.LatLng-float-">fromLatLngZoom(LatLng, float)</a></span> - 类 中的静态方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/CameraPosition.html" title="com.amap.api.maps.model中的类">CameraPosition</a></dt>
<dd>
<div class="block">根据传入的经纬度、缩放级别构造一个CameraPosition 对象。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/BitmapDescriptorFactory.html#fromPath-java.lang.String-">fromPath(String)</a></span> - 类 中的静态方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/BitmapDescriptorFactory.html" title="com.amap.api.maps.model中的类">BitmapDescriptorFactory</a></dt>
<dd>
<div class="block">根据图片文件的绝对地址，创建 bitmap 描述信息对象。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/BitmapDescriptorFactory.html#fromResource-int-">fromResource(int)</a></span> - 类 中的静态方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/BitmapDescriptorFactory.html" title="com.amap.api.maps.model中的类">BitmapDescriptorFactory</a></dt>
<dd>
<div class="block">根据图片的资源Id，创建bitmap描述信息对象。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/Projection.html#fromScreenLocation-android.graphics.Point-">fromScreenLocation(Point)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/Projection.html" title="com.amap.api.maps中的类">Projection</a></dt>
<dd>
<div class="block">将屏幕坐标转换成地理坐标。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/BitmapDescriptorFactory.html#fromView-android.view.View-">fromView(View)</a></span> - 类 中的静态方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/BitmapDescriptorFactory.html" title="com.amap.api.maps.model中的类">BitmapDescriptorFactory</a></dt>
<dd>
<div class="block">根据一个 View 创建 Bitmap 描述信息对象</div>
</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">K</a>&nbsp;<a href="index-11.html">L</a>&nbsp;<a href="index-12.html">M</a>&nbsp;<a href="index-13.html">N</a>&nbsp;<a href="index-14.html">O</a>&nbsp;<a href="index-15.html">P</a>&nbsp;<a href="index-16.html">Q</a>&nbsp;<a href="index-17.html">R</a>&nbsp;<a href="index-18.html">S</a>&nbsp;<a href="index-19.html">T</a>&nbsp;<a href="index-20.html">U</a>&nbsp;<a href="index-21.html">V</a>&nbsp;<a href="index-22.html">W</a>&nbsp;<a href="index-23.html">Z</a>&nbsp;</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-5.html">上一个字母</a></li>
<li><a href="index-7.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-6.html" target="_top">框架</a></li>
<li><a href="index-6.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
