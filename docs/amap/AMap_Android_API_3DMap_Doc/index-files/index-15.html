<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>P - 索引</title>
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="P - \u7D22\u5F15";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-14.html">上一个字母</a></li>
<li><a href="index-16.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-15.html" target="_top">框架</a></li>
<li><a href="index-15.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="contentContainer"><a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">K</a>&nbsp;<a href="index-11.html">L</a>&nbsp;<a href="index-12.html">M</a>&nbsp;<a href="index-13.html">N</a>&nbsp;<a href="index-14.html">O</a>&nbsp;<a href="index-15.html">P</a>&nbsp;<a href="index-16.html">Q</a>&nbsp;<a href="index-17.html">R</a>&nbsp;<a href="index-18.html">S</a>&nbsp;<a href="index-19.html">T</a>&nbsp;<a href="index-20.html">U</a>&nbsp;<a href="index-21.html">V</a>&nbsp;<a href="index-22.html">W</a>&nbsp;<a href="index-23.html">Z</a>&nbsp;<a name="I:P">
<!--   -->
</a>
<h2 class="title">P</h2>
<dl>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/particle/ParticleOverlayOptionsFactory.html#PARTICLE_TYPE_HAZE">PARTICLE_TYPE_HAZE</a></span> - 类 中的静态变量com.amap.api.maps.model.particle.<a href="../com/amap/api/maps/model/particle/ParticleOverlayOptionsFactory.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlayOptionsFactory</a></dt>
<dd>
<div class="block">雾霾天</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/particle/ParticleOverlayOptionsFactory.html#PARTICLE_TYPE_RAIN">PARTICLE_TYPE_RAIN</a></span> - 类 中的静态变量com.amap.api.maps.model.particle.<a href="../com/amap/api/maps/model/particle/ParticleOverlayOptionsFactory.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlayOptionsFactory</a></dt>
<dd>
<div class="block">雨天</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/particle/ParticleOverlayOptionsFactory.html#PARTICLE_TYPE_SNOWY">PARTICLE_TYPE_SNOWY</a></span> - 类 中的静态变量com.amap.api.maps.model.particle.<a href="../com/amap/api/maps/model/particle/ParticleOverlayOptionsFactory.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlayOptionsFactory</a></dt>
<dd>
<div class="block">雪天</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/particle/ParticleOverlayOptionsFactory.html#PARTICLE_TYPE_SUNNY">PARTICLE_TYPE_SUNNY</a></span> - 类 中的静态变量com.amap.api.maps.model.particle.<a href="../com/amap/api/maps/model/particle/ParticleOverlayOptionsFactory.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlayOptionsFactory</a></dt>
<dd>
<div class="block">晴天</div>
</dd>
<dt><a href="../com/amap/api/maps/model/particle/ParticleEmissionModule.html" title="com.amap.api.maps.model.particle中的类"><span class="typeNameLink">ParticleEmissionModule</span></a> - <a href="../com/amap/api/maps/model/particle/package-summary.html">com.amap.api.maps.model.particle</a>中的类</dt>
<dd>
<div class="block">粒子系统分组,发射率，指定时间内发射多少个粒子，越多会越密集
 但不会超过最大粒子数量</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/particle/ParticleEmissionModule.html#ParticleEmissionModule-int-int-">ParticleEmissionModule(int, int)</a></span> - 类 的构造器com.amap.api.maps.model.particle.<a href="../com/amap/api/maps/model/particle/ParticleEmissionModule.html" title="com.amap.api.maps.model.particle中的类">ParticleEmissionModule</a></dt>
<dd>
<div class="block">rateTime内发射rate个粒子</div>
</dd>
<dt><a href="../com/amap/api/maps/model/particle/ParticleOverlay.html" title="com.amap.api.maps.model.particle中的类"><span class="typeNameLink">ParticleOverlay</span></a> - <a href="../com/amap/api/maps/model/particle/package-summary.html">com.amap.api.maps.model.particle</a>中的类</dt>
<dd>
<div class="block">粒子效果，可以在地图上实现下雨、下雪和雾霾等天气效果，也可以通过开放的接口自定义
 建议在<a href="../com/amap/api/maps/AMap.OnMapLoadedListener.html" title="com.amap.api.maps中的接口"><code>AMap.OnMapLoadedListener</code></a> 之后调用</div>
</dd>
<dt><a href="../com/amap/api/maps/model/particle/ParticleOverlayOptions.html" title="com.amap.api.maps.model.particle中的类"><span class="typeNameLink">ParticleOverlayOptions</span></a> - <a href="../com/amap/api/maps/model/particle/package-summary.html">com.amap.api.maps.model.particle</a>中的类</dt>
<dd>
<div class="block">粒子效果初始化属性控制<br>
 建议在<a href="../com/amap/api/maps/AMap.OnMapLoadedListener.html" title="com.amap.api.maps中的接口"><code>AMap.OnMapLoadedListener</code></a> 之后调用</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/particle/ParticleOverlayOptions.html#ParticleOverlayOptions--">ParticleOverlayOptions()</a></span> - 类 的构造器com.amap.api.maps.model.particle.<a href="../com/amap/api/maps/model/particle/ParticleOverlayOptions.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlayOptions</a></dt>
<dd>
<div class="block">粒子效果初始化属性控制</div>
</dd>
<dt><a href="../com/amap/api/maps/model/particle/ParticleOverlayOptionsFactory.html" title="com.amap.api.maps.model.particle中的类"><span class="typeNameLink">ParticleOverlayOptionsFactory</span></a> - <a href="../com/amap/api/maps/model/particle/package-summary.html">com.amap.api.maps.model.particle</a>中的类</dt>
<dd>
<div class="block">粒子效果工厂类
<br>
 建议在<a href="../com/amap/api/maps/AMap.OnMapLoadedListener.html" title="com.amap.api.maps中的接口"><code>AMap.OnMapLoadedListener</code></a> 之后调用</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/particle/ParticleOverlayOptionsFactory.html#ParticleOverlayOptionsFactory--">ParticleOverlayOptionsFactory()</a></span> - 类 的构造器com.amap.api.maps.model.particle.<a href="../com/amap/api/maps/model/particle/ParticleOverlayOptionsFactory.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlayOptionsFactory</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/amap/api/maps/model/particle/ParticleOverLifeModule.html" title="com.amap.api.maps.model.particle中的类"><span class="typeNameLink">ParticleOverLifeModule</span></a> - <a href="../com/amap/api/maps/model/particle/package-summary.html">com.amap.api.maps.model.particle</a>中的类</dt>
<dd>
<div class="block">生命周期内的一些属性变化
 速度变化
 角度变化等等</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/particle/ParticleOverLifeModule.html#ParticleOverLifeModule--">ParticleOverLifeModule()</a></span> - 类 的构造器com.amap.api.maps.model.particle.<a href="../com/amap/api/maps/model/particle/ParticleOverLifeModule.html" title="com.amap.api.maps.model.particle中的类">ParticleOverLifeModule</a></dt>
<dd>
<div class="block">每个粒子生命周期过程中状态变化，包含速度、旋转和颜色的变化</div>
</dd>
<dt><a href="../com/amap/api/maps/model/particle/ParticleShapeModule.html" title="com.amap.api.maps.model.particle中的类"><span class="typeNameLink">ParticleShapeModule</span></a> - <a href="../com/amap/api/maps/model/particle/package-summary.html">com.amap.api.maps.model.particle</a>中的类</dt>
<dd>
<div class="block">发射器形状基类，具体使用时需要使用它的子类</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/OfflineMapManager.html#pause--">pause()</a></span> - 类 中的方法com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/OfflineMapManager.html" title="com.amap.api.maps.offlinemap中的类">OfflineMapManager</a></dt>
<dd>
<div class="block">暂停离线地图下载。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/OfflineMapStatus.html#PAUSE">PAUSE</a></span> - 类 中的静态变量com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/OfflineMapStatus.html" title="com.amap.api.maps.offlinemap中的类">OfflineMapStatus</a></dt>
<dd>
<div class="block">暂停下载。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/OfflineMapManager.html#pauseByName-java.lang.String-">pauseByName(String)</a></span> - 类 中的方法com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/OfflineMapManager.html" title="com.amap.api.maps.offlinemap中的类">OfflineMapManager</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MarkerOptions.html#period-int-">period(int)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MarkerOptions.html" title="com.amap.api.maps.model中的类">MarkerOptions</a></dt>
<dd>
<div class="block">设置多少帧刷新一次图片资源，Marker动画的间隔时间，值越小动画越快。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MarkerOptions.html#perspective-boolean-">perspective(boolean)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MarkerOptions.html" title="com.amap.api.maps.model中的类">MarkerOptions</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;</div>
</dd>
<dt><a href="../com/amap/api/maps/model/Poi.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">Poi</span></a> - <a href="../com/amap/api/maps/model/package-summary.html">com.amap.api.maps.model</a>中的类</dt>
<dd>
<div class="block">点击地图Poi点时，该兴趣点的描述信息
 Poi 是指底图上的一个自带Poi点。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Poi.html#Poi-java.lang.String-com.amap.api.maps.model.LatLng-java.lang.String-">Poi(String, LatLng, String)</a></span> - 类 的构造器com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Poi.html" title="com.amap.api.maps.model中的类">Poi</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/IndoorBuildingInfo.html#poiid">poiid</a></span> - 类 中的变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/IndoorBuildingInfo.html" title="com.amap.api.maps.model中的类">IndoorBuildingInfo</a></dt>
<dd>
<div class="block">室内地图的poiid，是室内地图的唯一标识。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/ArcOptions.html#point-com.amap.api.maps.model.LatLng-com.amap.api.maps.model.LatLng-com.amap.api.maps.model.LatLng-">point(LatLng, LatLng, LatLng)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/ArcOptions.html" title="com.amap.api.maps.model中的类">ArcOptions</a></dt>
<dd>
<div class="block">设置弧线的起终点和途径点。</div>
</dd>
<dt><a href="../com/amap/api/maps/model/PoiPara.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">PoiPara</span></a> - <a href="../com/amap/api/maps/model/package-summary.html">com.amap.api.maps.model</a>中的类</dt>
<dd>
<div class="block">唤起高德地图周边搜索功能的参数类。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/PoiPara.html#PoiPara--">PoiPara()</a></span> - 类 的构造器com.amap.api.maps.model.<a href="../com/amap/api/maps/model/PoiPara.html" title="com.amap.api.maps.model中的类">PoiPara</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/amap/api/maps/model/Polygon.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">Polygon</span></a> - <a href="../com/amap/api/maps/model/package-summary.html">com.amap.api.maps.model</a>中的类</dt>
<dd>
<div class="block">定义在地图上绘制多边形覆盖物。</div>
</dd>
<dt><a href="../com/amap/api/maps/model/PolygonHoleOptions.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">PolygonHoleOptions</span></a> - <a href="../com/amap/api/maps/model/package-summary.html">com.amap.api.maps.model</a>中的类</dt>
<dd>
<div class="block">多边形洞配置类</div>
</dd>
<dt><a href="../com/amap/api/maps/model/PolygonOptions.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">PolygonOptions</span></a> - <a href="../com/amap/api/maps/model/package-summary.html">com.amap.api.maps.model</a>中的类</dt>
<dd>
<div class="block">多边形覆盖物选项类</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/PolygonOptions.html#PolygonOptions--">PolygonOptions()</a></span> - 类 的构造器com.amap.api.maps.model.<a href="../com/amap/api/maps/model/PolygonOptions.html" title="com.amap.api.maps.model中的类">PolygonOptions</a></dt>
<dd>
<div class="block">创建PolygonOptions对象。</div>
</dd>
<dt><a href="../com/amap/api/maps/model/Polyline.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">Polyline</span></a> - <a href="../com/amap/api/maps/model/package-summary.html">com.amap.api.maps.model</a>中的类</dt>
<dd>
<div class="block">定义地图线段覆盖物。</div>
</dd>
<dt><a href="../com/amap/api/maps/model/PolylineOptions.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">PolylineOptions</span></a> - <a href="../com/amap/api/maps/model/package-summary.html">com.amap.api.maps.model</a>中的类</dt>
<dd>
<div class="block">线段的选项类</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/PolylineOptions.html#PolylineOptions--">PolylineOptions()</a></span> - 类 的构造器com.amap.api.maps.model.<a href="../com/amap/api/maps/model/PolylineOptions.html" title="com.amap.api.maps.model中的类">PolylineOptions</a></dt>
<dd>
<div class="block">构造一个新的PolylineOptions对象。</div>
</dd>
<dt><a href="../com/amap/api/maps/model/PolylineOptions.LineCapType.html" title="com.amap.api.maps.model中的枚举"><span class="typeNameLink">PolylineOptions.LineCapType</span></a> - <a href="../com/amap/api/maps/model/package-summary.html">com.amap.api.maps.model</a>中的枚举</dt>
<dd>
<div class="block">Polyline尾部形状</div>
</dd>
<dt><a href="../com/amap/api/maps/model/PolylineOptions.LineJoinType.html" title="com.amap.api.maps.model中的枚举"><span class="typeNameLink">PolylineOptions.LineJoinType</span></a> - <a href="../com/amap/api/maps/model/package-summary.html">com.amap.api.maps.model</a>中的枚举</dt>
<dd>
<div class="block">Polyline连接处形状</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/GroundOverlayOptions.html#position-com.amap.api.maps.model.LatLng-float-">position(LatLng, float)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/GroundOverlayOptions.html" title="com.amap.api.maps.model中的类">GroundOverlayOptions</a></dt>
<dd>
<div class="block">根据位置和宽设置ground 覆盖物。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/GroundOverlayOptions.html#position-com.amap.api.maps.model.LatLng-float-float-">position(LatLng, float, float)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/GroundOverlayOptions.html" title="com.amap.api.maps.model中的类">GroundOverlayOptions</a></dt>
<dd>
<div class="block">根据位置和宽高设置ground 覆盖物。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MarkerOptions.html#position-com.amap.api.maps.model.LatLng-">position(LatLng)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MarkerOptions.html" title="com.amap.api.maps.model中的类">MarkerOptions</a></dt>
<dd>
<div class="block">设置Marker覆盖物的位置坐标。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/TextOptions.html#position-com.amap.api.maps.model.LatLng-">position(LatLng)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/TextOptions.html" title="com.amap.api.maps.model中的类">TextOptions</a></dt>
<dd>
<div class="block">设置文字覆盖物的地理坐标。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/GroundOverlayOptions.html#positionFromBounds-com.amap.api.maps.model.LatLngBounds-">positionFromBounds(LatLngBounds)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/GroundOverlayOptions.html" title="com.amap.api.maps.model中的类">GroundOverlayOptions</a></dt>
<dd>
<div class="block">根据矩形区域设置ground 覆盖物的位置。</div>
</dd>
<dt><a href="../com/amap/api/maps/Projection.html" title="com.amap.api.maps中的类"><span class="typeNameLink">Projection</span></a> - <a href="../com/amap/api/maps/package-summary.html">com.amap.api.maps</a>中的类</dt>
<dd>
<div class="block">Projection接口用于屏幕像素点坐标系统和地球表面经纬度点坐标系统之间的变换。</div>
</dd>
<dt><a href="../com/amap/api/maps/offlinemap/Province.html" title="com.amap.api.maps.offlinemap中的类"><span class="typeNameLink">Province</span></a> - <a href="../com/amap/api/maps/offlinemap/package-summary.html">com.amap.api.maps.offlinemap</a>中的类</dt>
<dd>
<div class="block">省属性的相关类。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/Province.html#Province--">Province()</a></span> - 类 的构造器com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/Province.html" title="com.amap.api.maps.offlinemap中的类">Province</a></dt>
<dd>
<div class="block">构造一个Province对象。</div>
</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">K</a>&nbsp;<a href="index-11.html">L</a>&nbsp;<a href="index-12.html">M</a>&nbsp;<a href="index-13.html">N</a>&nbsp;<a href="index-14.html">O</a>&nbsp;<a href="index-15.html">P</a>&nbsp;<a href="index-16.html">Q</a>&nbsp;<a href="index-17.html">R</a>&nbsp;<a href="index-18.html">S</a>&nbsp;<a href="index-19.html">T</a>&nbsp;<a href="index-20.html">U</a>&nbsp;<a href="index-21.html">V</a>&nbsp;<a href="index-22.html">W</a>&nbsp;<a href="index-23.html">Z</a>&nbsp;</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-14.html">上一个字母</a></li>
<li><a href="index-16.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-15.html" target="_top">框架</a></li>
<li><a href="index-15.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
