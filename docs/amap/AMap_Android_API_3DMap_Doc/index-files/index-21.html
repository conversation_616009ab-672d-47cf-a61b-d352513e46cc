<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>V - 索引</title>
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="V - \u7D22\u5F15";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-20.html">上一个字母</a></li>
<li><a href="index-22.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-21.html" target="_top">框架</a></li>
<li><a href="index-21.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="contentContainer"><a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">K</a>&nbsp;<a href="index-11.html">L</a>&nbsp;<a href="index-12.html">M</a>&nbsp;<a href="index-13.html">N</a>&nbsp;<a href="index-14.html">O</a>&nbsp;<a href="index-15.html">P</a>&nbsp;<a href="index-16.html">Q</a>&nbsp;<a href="index-17.html">R</a>&nbsp;<a href="index-18.html">S</a>&nbsp;<a href="index-19.html">T</a>&nbsp;<a href="index-20.html">U</a>&nbsp;<a href="index-21.html">V</a>&nbsp;<a href="index-22.html">W</a>&nbsp;<a href="index-23.html">Z</a>&nbsp;<a name="I:V">
<!--   -->
</a>
<h2 class="title">V</h2>
<dl>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/ImageOptions.ShapeType.html#value--">value()</a></span> - 枚举 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/ImageOptions.ShapeType.html" title="com.amap.api.maps.model中的枚举">ImageOptions.ShapeType</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/CoordinateConverter.CoordType.html#valueOf-java.lang.String-">valueOf(String)</a></span> - 枚举 中的静态方法com.amap.api.maps.<a href="../com/amap/api/maps/CoordinateConverter.CoordType.html" title="com.amap.api.maps中的枚举">CoordinateConverter.CoordType</a></dt>
<dd>
<div class="block">返回带有指定名称的该类型的枚举常量。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/AMapPara.LineCapType.html#valueOf-java.lang.String-">valueOf(String)</a></span> - 枚举 中的静态方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/AMapPara.LineCapType.html" title="com.amap.api.maps.model中的枚举">AMapPara.LineCapType</a></dt>
<dd>
<div class="block">返回带有指定名称的该类型的枚举常量。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/AMapPara.LineCapType.html#valueOf-int-">valueOf(int)</a></span> - 枚举 中的静态方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/AMapPara.LineCapType.html" title="com.amap.api.maps.model中的枚举">AMapPara.LineCapType</a></dt>
<dd>
<div class="block">根据value返回对应的形状</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/AMapPara.LineJoinType.html#valueOf-java.lang.String-">valueOf(String)</a></span> - 枚举 中的静态方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/AMapPara.LineJoinType.html" title="com.amap.api.maps.model中的枚举">AMapPara.LineJoinType</a></dt>
<dd>
<div class="block">返回带有指定名称的该类型的枚举常量。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/AMapPara.LineJoinType.html#valueOf-int-">valueOf(int)</a></span> - 枚举 中的静态方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/AMapPara.LineJoinType.html" title="com.amap.api.maps.model中的枚举">AMapPara.LineJoinType</a></dt>
<dd>
<div class="block">根据value返回对应的形状</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/ImageOptions.ShapeType.html#valueOf-java.lang.String-">valueOf(String)</a></span> - 枚举 中的静态方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/ImageOptions.ShapeType.html" title="com.amap.api.maps.model中的枚举">ImageOptions.ShapeType</a></dt>
<dd>
<div class="block">返回带有指定名称的该类型的枚举常量。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/PolylineOptions.LineCapType.html#valueOf-java.lang.String-">valueOf(String)</a></span> - 枚举 中的静态方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/PolylineOptions.LineCapType.html" title="com.amap.api.maps.model中的枚举">PolylineOptions.LineCapType</a></dt>
<dd>
<div class="block">返回带有指定名称的该类型的枚举常量。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/PolylineOptions.LineCapType.html#valueOf-int-">valueOf(int)</a></span> - 枚举 中的静态方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/PolylineOptions.LineCapType.html" title="com.amap.api.maps.model中的枚举">PolylineOptions.LineCapType</a></dt>
<dd>
<div class="block">根据value返回对应的形状</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/PolylineOptions.LineJoinType.html#valueOf-java.lang.String-">valueOf(String)</a></span> - 枚举 中的静态方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/PolylineOptions.LineJoinType.html" title="com.amap.api.maps.model中的枚举">PolylineOptions.LineJoinType</a></dt>
<dd>
<div class="block">返回带有指定名称的该类型的枚举常量。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/PolylineOptions.LineJoinType.html#valueOf-int-">valueOf(int)</a></span> - 枚举 中的静态方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/PolylineOptions.LineJoinType.html" title="com.amap.api.maps.model中的枚举">PolylineOptions.LineJoinType</a></dt>
<dd>
<div class="block">根据value返回对应的形状</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/CoordinateConverter.CoordType.html#values--">values()</a></span> - 枚举 中的静态方法com.amap.api.maps.<a href="../com/amap/api/maps/CoordinateConverter.CoordType.html" title="com.amap.api.maps中的枚举">CoordinateConverter.CoordType</a></dt>
<dd>
<div class="block">按照声明该枚举类型的常量的顺序, 返回
包含这些常量的数组。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/AMapPara.LineCapType.html#values--">values()</a></span> - 枚举 中的静态方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/AMapPara.LineCapType.html" title="com.amap.api.maps.model中的枚举">AMapPara.LineCapType</a></dt>
<dd>
<div class="block">按照声明该枚举类型的常量的顺序, 返回
包含这些常量的数组。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/AMapPara.LineJoinType.html#values--">values()</a></span> - 枚举 中的静态方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/AMapPara.LineJoinType.html" title="com.amap.api.maps.model中的枚举">AMapPara.LineJoinType</a></dt>
<dd>
<div class="block">按照声明该枚举类型的常量的顺序, 返回
包含这些常量的数组。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/ImageOptions.ShapeType.html#values--">values()</a></span> - 枚举 中的静态方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/ImageOptions.ShapeType.html" title="com.amap.api.maps.model中的枚举">ImageOptions.ShapeType</a></dt>
<dd>
<div class="block">按照声明该枚举类型的常量的顺序, 返回
包含这些常量的数组。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/PolylineOptions.LineCapType.html#values--">values()</a></span> - 枚举 中的静态方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/PolylineOptions.LineCapType.html" title="com.amap.api.maps.model中的枚举">PolylineOptions.LineCapType</a></dt>
<dd>
<div class="block">按照声明该枚举类型的常量的顺序, 返回
包含这些常量的数组。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/PolylineOptions.LineJoinType.html#values--">values()</a></span> - 枚举 中的静态方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/PolylineOptions.LineJoinType.html" title="com.amap.api.maps.model中的枚举">PolylineOptions.LineJoinType</a></dt>
<dd>
<div class="block">按照声明该枚举类型的常量的顺序, 返回
包含这些常量的数组。</div>
</dd>
<dt><a href="../com/amap/api/maps/model/particle/VelocityGenerate.html" title="com.amap.api.maps.model.particle中的类"><span class="typeNameLink">VelocityGenerate</span></a> - <a href="../com/amap/api/maps/model/particle/package-summary.html">com.amap.api.maps.model.particle</a>中的类</dt>
<dd>
<div class="block">速度变化控制基类，具体使用时需要使用它的子类</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/particle/VelocityGenerate.html#VelocityGenerate--">VelocityGenerate()</a></span> - 类 的构造器com.amap.api.maps.model.particle.<a href="../com/amap/api/maps/model/particle/VelocityGenerate.html" title="com.amap.api.maps.model.particle中的类">VelocityGenerate</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/ArcOptions.html#visible-boolean-">visible(boolean)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/ArcOptions.html" title="com.amap.api.maps.model中的类">ArcOptions</a></dt>
<dd>
<div class="block">设置弧形是否可见参数。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/CircleOptions.html#visible-boolean-">visible(boolean)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/CircleOptions.html" title="com.amap.api.maps.model中的类">CircleOptions</a></dt>
<dd>
<div class="block">设置圆的可见属性</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/GroundOverlayOptions.html#visible-boolean-">visible(boolean)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/GroundOverlayOptions.html" title="com.amap.api.maps.model中的类">GroundOverlayOptions</a></dt>
<dd>
<div class="block">设置ground 覆盖物是否可见。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/HeatMapGridLayerOptions.html#visible-boolean-">visible(boolean)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/HeatMapGridLayerOptions.html" title="com.amap.api.maps.model中的类">HeatMapGridLayerOptions</a></dt>
<dd>
<div class="block">设置可见性。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/HeatMapLayerOptions.html#visible-boolean-">visible(boolean)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/HeatMapLayerOptions.html" title="com.amap.api.maps.model中的类">HeatMapLayerOptions</a></dt>
<dd>
<div class="block">设置可见性。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MarkerOptions.html#visible-boolean-">visible(boolean)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MarkerOptions.html" title="com.amap.api.maps.model中的类">MarkerOptions</a></dt>
<dd>
<div class="block">设置Marker覆盖物是否可见。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MVTTileOverlay.html#visible--">visible()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MVTTileOverlay.html" title="com.amap.api.maps.model中的类">MVTTileOverlay</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MVTTileOverlayOptions.Builder.html#visible-boolean-">visible(boolean)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MVTTileOverlayOptions.Builder.html" title="com.amap.api.maps.model中的类">MVTTileOverlayOptions.Builder</a></dt>
<dd>
<div class="block">覆盖物是否可见</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MVTTileOverlayOptions.html#visible--">visible()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MVTTileOverlayOptions.html" title="com.amap.api.maps.model中的类">MVTTileOverlayOptions</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/NavigateArrowOptions.html#visible-boolean-">visible(boolean)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/NavigateArrowOptions.html" title="com.amap.api.maps.model中的类">NavigateArrowOptions</a></dt>
<dd>
<div class="block">设置箭头(NavigateArrow)覆盖物的可见性。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/PolygonOptions.html#visible-boolean-">visible(boolean)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/PolygonOptions.html" title="com.amap.api.maps.model中的类">PolygonOptions</a></dt>
<dd>
<div class="block">设置多边形是否可见。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/PolylineOptions.html#visible-boolean-">visible(boolean)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/PolylineOptions.html" title="com.amap.api.maps.model中的类">PolylineOptions</a></dt>
<dd>
<div class="block">设置线段的可见性。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/TextOptions.html#visible-boolean-">visible(boolean)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/TextOptions.html" title="com.amap.api.maps.model中的类">TextOptions</a></dt>
<dd>
<div class="block">设置文字覆盖物的可见性。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/TileOverlayOptions.html#visible-boolean-">visible(boolean)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/TileOverlayOptions.html" title="com.amap.api.maps.model中的类">TileOverlayOptions</a></dt>
<dd>
<div class="block">设置瓦片图层的可见属性，默认为可见。</div>
</dd>
<dt><a href="../com/amap/api/maps/model/VisibleRegion.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">VisibleRegion</span></a> - <a href="../com/amap/api/maps/model/package-summary.html">com.amap.api.maps.model</a>中的类</dt>
<dd>
<div class="block">可视区域：地图View四个顶点对应的经纬度所围成的多边形被称作 可视区域；<br>

 此多边形是不规则四边形，如果地图没有倾斜时，可视区域为矩形，如果地图有倾斜时，可视区域为梯形。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/VisibleRegion.html#VisibleRegion-com.amap.api.maps.model.LatLng-com.amap.api.maps.model.LatLng-com.amap.api.maps.model.LatLng-com.amap.api.maps.model.LatLng-com.amap.api.maps.model.LatLngBounds-">VisibleRegion(LatLng, LatLng, LatLng, LatLng, LatLngBounds)</a></span> - 类 的构造器com.amap.api.maps.model.<a href="../com/amap/api/maps/model/VisibleRegion.html" title="com.amap.api.maps.model中的类">VisibleRegion</a></dt>
<dd>
<div class="block">给出可视区域四个角的坐标，创建VisibleRegion对象。</div>
</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">K</a>&nbsp;<a href="index-11.html">L</a>&nbsp;<a href="index-12.html">M</a>&nbsp;<a href="index-13.html">N</a>&nbsp;<a href="index-14.html">O</a>&nbsp;<a href="index-15.html">P</a>&nbsp;<a href="index-16.html">Q</a>&nbsp;<a href="index-17.html">R</a>&nbsp;<a href="index-18.html">S</a>&nbsp;<a href="index-19.html">T</a>&nbsp;<a href="index-20.html">U</a>&nbsp;<a href="index-21.html">V</a>&nbsp;<a href="index-22.html">W</a>&nbsp;<a href="index-23.html">Z</a>&nbsp;</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-20.html">上一个字母</a></li>
<li><a href="index-22.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-21.html" target="_top">框架</a></li>
<li><a href="index-21.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
