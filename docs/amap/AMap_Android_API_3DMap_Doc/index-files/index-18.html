<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>S - 索引</title>
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="S - \u7D22\u5F15";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-17.html">上一个字母</a></li>
<li><a href="index-19.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-18.html" target="_top">框架</a></li>
<li><a href="index-18.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="contentContainer"><a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">K</a>&nbsp;<a href="index-11.html">L</a>&nbsp;<a href="index-12.html">M</a>&nbsp;<a href="index-13.html">N</a>&nbsp;<a href="index-14.html">O</a>&nbsp;<a href="index-15.html">P</a>&nbsp;<a href="index-16.html">Q</a>&nbsp;<a href="index-17.html">R</a>&nbsp;<a href="index-18.html">S</a>&nbsp;<a href="index-19.html">T</a>&nbsp;<a href="index-20.html">U</a>&nbsp;<a href="index-21.html">V</a>&nbsp;<a href="index-22.html">W</a>&nbsp;<a href="index-23.html">Z</a>&nbsp;<a name="I:S">
<!--   -->
</a>
<h2 class="title">S</h2>
<dl>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/GLTFOverlayOptions.html#scale-double-">scale(double)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/GLTFOverlayOptions.html" title="com.amap.api.maps.model中的类">GLTFOverlayOptions</a></dt>
<dd>
<div class="block">设置模型的缩放比</div>
</dd>
<dt><a href="../com/amap/api/maps/model/animation/ScaleAnimation.html" title="com.amap.api.maps.model.animation中的类"><span class="typeNameLink">ScaleAnimation</span></a> - <a href="../com/amap/api/maps/model/animation/package-summary.html">com.amap.api.maps.model.animation</a>中的类</dt>
<dd>
<div class="block">控制缩放的动画类。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/animation/ScaleAnimation.html#ScaleAnimation-float-float-float-float-">ScaleAnimation(float, float, float, float)</a></span> - 类 的构造器com.amap.api.maps.model.animation.<a href="../com/amap/api/maps/model/animation/ScaleAnimation.html" title="com.amap.api.maps.model.animation中的类">ScaleAnimation</a></dt>
<dd>
<div class="block">控制缩放的动画类<br>
 比如要实现一个Marker生长动画,可以使用 (0,1,0,1)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMapOptions.html#scaleControlsEnabled-boolean-">scaleControlsEnabled(boolean)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMapOptions.html" title="com.amap.api.maps中的类">AMapOptions</a></dt>
<dd>
<div class="block">设置地图是否显示比例尺，默认为false。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/CameraUpdateFactory.html#scrollBy-float-float-">scrollBy(float, float)</a></span> - 类 中的静态方法com.amap.api.maps.<a href="../com/amap/api/maps/CameraUpdateFactory.html" title="com.amap.api.maps中的类">CameraUpdateFactory</a></dt>
<dd>
<div class="block">按像素移动地图中心点</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMapOptions.html#scrollGesturesEnabled-boolean-">scrollGesturesEnabled(boolean)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMapOptions.html" title="com.amap.api.maps中的类">AMapOptions</a></dt>
<dd>
<div class="block">设置地图是否可以通过手势滑动。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/MapsInitializer.html#sdcardDir">sdcardDir</a></span> - 类 中的静态变量com.amap.api.maps.<a href="../com/amap/api/maps/MapsInitializer.html" title="com.amap.api.maps中的类">MapsInitializer</a></dt>
<dd>
<div class="block">地图缓存目录。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/NavigateArrow.html#set3DModel-boolean-">set3DModel(boolean)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/NavigateArrow.html" title="com.amap.api.maps.model中的类">NavigateArrow</a></dt>
<dd>
<div class="block">设置箭头(NavigateArrow)覆盖物的是否为3D模式。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/NavigateArrowOptions.html#set3DModel-boolean-">set3DModel(boolean)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/NavigateArrowOptions.html" title="com.amap.api.maps.model中的类">NavigateArrowOptions</a></dt>
<dd>
<div class="block">设置箭头(NavigateArrow)覆盖物的是否为3D模式。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/City.html#setAdcode-java.lang.String-">setAdcode(String)</a></span> - 类 中的方法com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/City.html" title="com.amap.api.maps.offlinemap中的类">City</a></dt>
<dd>
<div class="block">设置城市行政编码</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Text.html#setAlign-int-int-">setAlign(int, int)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Text.html" title="com.amap.api.maps.model中的类">Text</a></dt>
<dd>
<div class="block">设置文字覆盖物的对齐方式，默认居中对齐。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/UiSettings.html#setAllGesturesEnabled-boolean-">setAllGesturesEnabled(boolean)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/UiSettings.html" title="com.amap.api.maps中的类">UiSettings</a></dt>
<dd>
<div class="block">设置所有手势是否可用</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Marker.html#setAlpha-float-">setAlpha(float)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Marker.html" title="com.amap.api.maps.model中的类">Marker</a></dt>
<dd>
<div class="block">设置Marker覆盖物的透明度</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Marker.html#setAltitude-float-">setAltitude(float)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Marker.html" title="com.amap.api.maps.model中的类">Marker</a></dt>
<dd>
<div class="block">设置marker海拔</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#setAMapGestureListener-com.amap.api.maps.model.AMapGestureListener-">setAMapGestureListener(AMapGestureListener)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block">设置手势监听接口</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Marker.html#setAnchor-float-float-">setAnchor(float, float)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Marker.html" title="com.amap.api.maps.model中的类">Marker</a></dt>
<dd>
<div class="block">设置Marker覆盖物的锚点比例。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MultiPointOverlay.html#setAnchor-float-float-">setAnchor(float, float)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MultiPointOverlay.html" title="com.amap.api.maps.model中的类">MultiPointOverlay</a></dt>
<dd>
<div class="block">统一设置海量点图标锚点比例。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/BasePointOverlay.html#setAnimation-com.amap.api.maps.model.animation.Animation-">setAnimation(Animation)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/BasePointOverlay.html" title="com.amap.api.maps.model中的类">BasePointOverlay</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Marker.html#setAnimation-com.amap.api.maps.model.animation.Animation-">setAnimation(Animation)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Marker.html" title="com.amap.api.maps.model中的类">Marker</a></dt>
<dd>
<div class="block">设置动画,动画包含，旋转，缩放，消失，平移以及它们的组合动画</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/animation/Animation.html#setAnimationListener-com.amap.api.maps.model.animation.Animation.AnimationListener-">setAnimationListener(Animation.AnimationListener)</a></span> - 类 中的方法com.amap.api.maps.model.animation.<a href="../com/amap/api/maps/model/animation/Animation.html" title="com.amap.api.maps.model.animation中的类">Animation</a></dt>
<dd>
<div class="block">设置动画监听器</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/MapsInitializer.html#setApiKey-java.lang.String-">setApiKey(String)</a></span> - 类 中的静态方法com.amap.api.maps.<a href="../com/amap/api/maps/MapsInitializer.html" title="com.amap.api.maps中的类">MapsInitializer</a></dt>
<dd>
<div class="block">动态设置apiKey。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Text.html#setBackgroundColor-int-">setBackgroundColor(int)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Text.html" title="com.amap.api.maps.model中的类">Text</a></dt>
<dd>
<div class="block">设置文字覆盖物的背景颜色。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/GroundOverlay.html#setBearing-float-">setBearing(float)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/GroundOverlay.html" title="com.amap.api.maps.model中的类">GroundOverlay</a></dt>
<dd>
<div class="block">设置ground 覆盖物从正北开始顺时针旋转的角度，中心点为锚点。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/trace/TraceLocation.html#setBearing-float-">setBearing(float)</a></span> - 类 中的方法com.amap.api.trace.<a href="../com/amap/api/trace/TraceLocation.html" title="com.amap.api.trace中的类">TraceLocation</a></dt>
<dd>
<div class="block">设置方向角</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/BuildingOverlayOptions.html#setBuildingHeight-int-">setBuildingHeight(int)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/BuildingOverlayOptions.html" title="com.amap.api.maps.model中的类">BuildingOverlayOptions</a></dt>
<dd>
<div class="block">设置建筑物的高度</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/BuildingOverlayOptions.html#setBuildingHeightScale-int-">setBuildingHeightScale(int)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/BuildingOverlayOptions.html" title="com.amap.api.maps.model中的类">BuildingOverlayOptions</a></dt>
<dd>
<div class="block">设置overlay的建筑物相对于默认高度的倍数，此方法与setBuildingHeight方法互斥，调用setBuildingHeight方法将使用此倍数值失效。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/BuildingOverlayOptions.html#setBuildingLatlngs-java.util.List-">setBuildingLatlngs(List&lt;LatLng&gt;)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/BuildingOverlayOptions.html" title="com.amap.api.maps.model中的类">BuildingOverlayOptions</a></dt>
<dd>
<div class="block">设置建筑物围栏坐标列表</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/BuildingOverlayOptions.html#setBuildingSideColor-int-">setBuildingSideColor(int)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/BuildingOverlayOptions.html" title="com.amap.api.maps.model中的类">BuildingOverlayOptions</a></dt>
<dd>
<div class="block">设置建筑物侧面颜色值</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/BuildingOverlayOptions.html#setBuildingTopColor-int-">setBuildingTopColor(int)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/BuildingOverlayOptions.html" title="com.amap.api.maps.model中的类">BuildingOverlayOptions</a></dt>
<dd>
<div class="block">设置建筑物顶部颜色值</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/TileOverlaySource.html#setCacheEnabled-boolean-">setCacheEnabled(boolean)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/TileOverlaySource.html" title="com.amap.api.maps.model中的类">TileOverlaySource</a></dt>
<dd>
<div class="block">是否会缓存瓦片数据</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Circle.html#setCenter-com.amap.api.maps.model.LatLng-">setCenter(LatLng)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Circle.html" title="com.amap.api.maps.model中的类">Circle</a></dt>
<dd>
<div class="block">设置圆心经纬度坐标，参数不能为null，无默认值。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/HeatMapItem.html#setCenter-double-double-">setCenter(double, double)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/HeatMapItem.html" title="com.amap.api.maps.model中的类">HeatMapItem</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/PoiPara.html#setCenter-com.amap.api.maps.model.LatLng-">setCenter(LatLng)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/PoiPara.html" title="com.amap.api.maps.model中的类">PoiPara</a></dt>
<dd>
<div class="block">poi周边检索中心点。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/City.html#setCity-java.lang.String-">setCity(String)</a></span> - 类 中的方法com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/City.html" title="com.amap.api.maps.offlinemap中的类">City</a></dt>
<dd>
<div class="block">设置城市名称。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/OfflineMapProvince.html#setCityList-java.util.ArrayList-">setCityList(ArrayList&lt;OfflineMapCity&gt;)</a></span> - 类 中的方法com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/OfflineMapProvince.html" title="com.amap.api.maps.offlinemap中的类">OfflineMapProvince</a></dt>
<dd>
<div class="block">设置省下面的城市</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/GLTFOverlay.html#setClickable-boolean-">setClickable(boolean)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/GLTFOverlay.html" title="com.amap.api.maps.model中的类">GLTFOverlay</a></dt>
<dd>
<div class="block">设置是否可点击</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/GLTFOverlayOptions.html#setClickable-boolean-">setClickable(boolean)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/GLTFOverlayOptions.html" title="com.amap.api.maps.model中的类">GLTFOverlayOptions</a></dt>
<dd>
<div class="block">设置是否可点击，默认false</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Marker.html#setClickable-boolean-">setClickable(boolean)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Marker.html" title="com.amap.api.maps.model中的类">Marker</a></dt>
<dd>
<div class="block">设置Marker覆盖物是否可以点击</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/City.html#setCode-java.lang.String-">setCode(String)</a></span> - 类 中的方法com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/City.html" title="com.amap.api.maps.offlinemap中的类">City</a></dt>
<dd>
<div class="block">设置城市代码。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Polyline.html#setColor-int-">setColor(int)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Polyline.html" title="com.amap.api.maps.model中的类">Polyline</a></dt>
<dd>
<div class="block">设置线段的颜色</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/particle/ParticleOverLifeModule.html#setColorGenerate-com.amap.api.maps.model.particle.ColorGenerate-">setColorGenerate(ColorGenerate)</a></span> - 类 中的方法com.amap.api.maps.model.particle.<a href="../com/amap/api/maps/model/particle/ParticleOverLifeModule.html" title="com.amap.api.maps.model.particle中的类">ParticleOverLifeModule</a></dt>
<dd>
<div class="block">颜色变化</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/UiSettings.html#setCompassEnabled-boolean-">setCompassEnabled(boolean)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/UiSettings.html" title="com.amap.api.maps中的类">UiSettings</a></dt>
<dd>
<div class="block">设置指南针是否可见。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/OfflineMapCity.html#setCompleteCode-int-">setCompleteCode(int)</a></span> - 类 中的方法com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/OfflineMapCity.html" title="com.amap.api.maps.offlinemap中的类">OfflineMapCity</a></dt>
<dd>
<div class="block">设置下载完成的百分比，100表示下载完成。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/OfflineMapProvince.html#setCompleteCode-int-">setCompleteCode(int)</a></span> - 类 中的方法com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/OfflineMapProvince.html" title="com.amap.api.maps.offlinemap中的类">OfflineMapProvince</a></dt>
<dd>
<div class="block">设置省下载完成的百分比，100表示下载完成。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MyTrafficStyle.html#setCongestedColor-int-">setCongestedColor(int)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MyTrafficStyle.html" title="com.amap.api.maps.model中的类">MyTrafficStyle</a></dt>
<dd>
<div class="block">设置行驶拥堵路段的标记颜色。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#setConstructingRoadEnable-boolean-">setConstructingRoadEnable(boolean)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block">设置在建道路图层是否显示,默认不显示。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/GLTFOverlay.html#setCurrentAnimationIndex-int-">setCurrentAnimationIndex(int)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/GLTFOverlay.html" title="com.amap.api.maps.model中的类">GLTFOverlay</a></dt>
<dd>
<div class="block">设置当前执行动画的索引，取值必须小于模型支持动画的个数。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/GLTFOverlayOptions.html#setCurrentAnimationIndex-int-">setCurrentAnimationIndex(int)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/GLTFOverlayOptions.html" title="com.amap.api.maps.model中的类">GLTFOverlayOptions</a></dt>
<dd>
<div class="block">设置当前执行动画的索引，取值必须小于模型支持动画的个数。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MultiPointItem.html#setCustomerId-java.lang.String-">setCustomerId(String)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MultiPointItem.html" title="com.amap.api.maps.model中的类">MultiPointItem</a></dt>
<dd>
<div class="block">为海量点设置一个唯一标识，可以用来判断，默认为null</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#setCustomMapStyle-com.amap.api.maps.model.CustomMapStyleOptions-">setCustomMapStyle(CustomMapStyleOptions)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block">设置底图自定义样式对应配置文件路径。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#setCustomMapStyleID-java.lang.String-">setCustomMapStyleID(String)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span>
<div class="block"><span class="deprecationComment">该方法已无效，建议到官网lbs.amap.com更新样式文件并使用 <a href="../com/amap/api/maps/AMap.html#setCustomMapStyle-com.amap.api.maps.model.CustomMapStyleOptions-"><code>AMap.setCustomMapStyle(CustomMapStyleOptions)</code></a> )} 替换</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#setCustomMapStylePath-java.lang.String-">setCustomMapStylePath(String)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span>
<div class="block"><span class="deprecationComment">该方法已无效，建议到官网lbs.amap.com更新样式文件并使用 <a href="../com/amap/api/maps/AMap.html#setCustomMapStyle-com.amap.api.maps.model.CustomMapStyleOptions-"><code>AMap.setCustomMapStyle(CustomMapStyleOptions)</code></a> )} 替换</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/BuildingOverlay.html#setCustomOptions-java.util.List-">setCustomOptions(List&lt;BuildingOverlayOptions&gt;)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/BuildingOverlay.html" title="com.amap.api.maps.model中的类">BuildingOverlay</a></dt>
<dd>
<div class="block">设置自定义overlay子区域.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#setCustomRenderer-com.amap.api.maps.CustomRenderer-">setCustomRenderer(CustomRenderer)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block">设置地图在初始化及每一帧绘制时的回调接口，该接口在OpenGL线程中调用。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Polyline.html#setCustomTexture-com.amap.api.maps.model.BitmapDescriptor-">setCustomTexture(BitmapDescriptor)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Polyline.html" title="com.amap.api.maps.model中的类">Polyline</a></dt>
<dd>
<div class="block">设置线段的自定义纹理</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/PolylineOptions.html#setCustomTexture-com.amap.api.maps.model.BitmapDescriptor-">setCustomTexture(BitmapDescriptor)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/PolylineOptions.html" title="com.amap.api.maps.model中的类">PolylineOptions</a></dt>
<dd>
<div class="block">设置线段的纹理图，图片为2的n次方。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/PolylineOptions.html#setCustomTextureIndex-java.util.List-">setCustomTextureIndex(List&lt;Integer&gt;)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/PolylineOptions.html" title="com.amap.api.maps.model中的类">PolylineOptions</a></dt>
<dd>
<div class="block">设置线段纹理index数组</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/PolylineOptions.html#setCustomTextureList-java.util.List-">setCustomTextureList(List&lt;BitmapDescriptor&gt;)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/PolylineOptions.html" title="com.amap.api.maps.model中的类">PolylineOptions</a></dt>
<dd>
<div class="block">设置线段纹理list</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/BuildingOverlay.html#setDefaultOptions-com.amap.api.maps.model.BuildingOverlayOptions-">setDefaultOptions(BuildingOverlayOptions)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/BuildingOverlay.html" title="com.amap.api.maps.model中的类">BuildingOverlay</a></dt>
<dd>
<div class="block">设置overlay的显示区域，默认全世界区域</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/utils/overlay/SmoothMoveMarker.html#setDescriptor-com.amap.api.maps.model.BitmapDescriptor-">setDescriptor(BitmapDescriptor)</a></span> - 类 中的方法com.amap.api.maps.utils.overlay.<a href="../com/amap/api/maps/utils/overlay/SmoothMoveMarker.html" title="com.amap.api.maps.utils.overlay中的类">SmoothMoveMarker</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">设置移动Marker的图标</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/GroundOverlay.html#setDimensions-float-">setDimensions(float)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/GroundOverlay.html" title="com.amap.api.maps.model中的类">GroundOverlay</a></dt>
<dd>
<div class="block">设置ground 覆盖物的宽度，ground 覆盖物的高度根据图片的比例自动变化。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/GroundOverlay.html#setDimensions-float-float-">setDimensions(float, float)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/GroundOverlay.html" title="com.amap.api.maps.model中的类">GroundOverlay</a></dt>
<dd>
<div class="block">设置ground 覆盖物的宽度和高度，图片会被拉伸，可能不会保留之前的图片比例。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/trace/TraceOverlay.html#setDistance-int-">setDistance(int)</a></span> - 类 中的方法com.amap.api.trace.<a href="../com/amap/api/trace/TraceOverlay.html" title="com.amap.api.trace中的类">TraceOverlay</a></dt>
<dd>
<div class="block">设置线路行驶距离</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Polyline.html#setDottedLine-boolean-">setDottedLine(boolean)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Polyline.html" title="com.amap.api.maps.model中的类">Polyline</a></dt>
<dd>
<div class="block">设置线段是否虚线，默认为false，画实线。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/PolylineOptions.html#setDottedLine-boolean-">setDottedLine(boolean)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/PolylineOptions.html" title="com.amap.api.maps.model中的类">PolylineOptions</a></dt>
<dd>
<div class="block">设置是否画虚线，默认为false，画实线。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/PolylineOptions.html#setDottedLineType-int-">setDottedLineType(int)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/PolylineOptions.html" title="com.amap.api.maps.model中的类">PolylineOptions</a></dt>
<dd>
<div class="block">设置虚线形状。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/GLTFOverlay.html#setDraggable-boolean-">setDraggable(boolean)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/GLTFOverlay.html" title="com.amap.api.maps.model中的类">GLTFOverlay</a></dt>
<dd>
<div class="block">设置是否可拖拽</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/GLTFOverlayOptions.html#setDraggable-boolean-">setDraggable(boolean)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/GLTFOverlayOptions.html" title="com.amap.api.maps.model中的类">GLTFOverlayOptions</a></dt>
<dd>
<div class="block">设置是否可拖拽，默认false</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Marker.html#setDraggable-boolean-">setDraggable(boolean)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Marker.html" title="com.amap.api.maps.model中的类">Marker</a></dt>
<dd>
<div class="block">设置Marker覆盖物是否允许拖拽。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/RoutePara.html#setDrivingRouteStyle-int-">setDrivingRouteStyle(int)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/RoutePara.html" title="com.amap.api.maps.model中的类">RoutePara</a></dt>
<dd>
<div class="block">设置驾车路径规划的规划模式。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/animation/Animation.html#setDuration-long-">setDuration(long)</a></span> - 类 中的方法com.amap.api.maps.model.animation.<a href="../com/amap/api/maps/model/animation/Animation.html" title="com.amap.api.maps.model.animation中的类">Animation</a></dt>
<dd>
<div class="block">设置动画持续时间,如果设置为负数,会修正为0</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/particle/ParticleOverlay.html#setDuration-long-">setDuration(long)</a></span> - 类 中的方法com.amap.api.maps.model.particle.<a href="../com/amap/api/maps/model/particle/ParticleOverlay.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlay</a></dt>
<dd>
<div class="block">整个粒子效果存活时间</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/particle/ParticleOverlayOptions.html#setDuration-long-">setDuration(long)</a></span> - 类 中的方法com.amap.api.maps.model.particle.<a href="../com/amap/api/maps/model/particle/ParticleOverlayOptions.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlayOptions</a></dt>
<dd>
<div class="block">整个粒子效果的存活时间,单位毫秒</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/CustomMapStyleOptions.html#setEnable-boolean-">setEnable(boolean)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/CustomMapStyleOptions.html" title="com.amap.api.maps.model中的类">CustomMapStyleOptions</a></dt>
<dd>
<div class="block">设置是否开启底图自定义样式， 默认为开启</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MultiPointOverlay.html#setEnable-boolean-">setEnable(boolean)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MultiPointOverlay.html" title="com.amap.api.maps.model中的类">MultiPointOverlay</a></dt>
<dd>
<div class="block">设置海量点是否显示，默认为显示</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/RoutePara.html#setEndName-java.lang.String-">setEndName(String)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/RoutePara.html" title="com.amap.api.maps.model中的类">RoutePara</a></dt>
<dd>
<div class="block">设置路线检索终点名称。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/RoutePara.html#setEndPoint-com.amap.api.maps.model.LatLng-">setEndPoint(LatLng)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/RoutePara.html" title="com.amap.api.maps.model中的类">RoutePara</a></dt>
<dd>
<div class="block">设置路线检索终点坐标。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Polyline.html#setEraseColor-boolean-int-">setEraseColor(boolean, int)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Polyline.html" title="com.amap.api.maps.model中的类">Polyline</a></dt>
<dd>
<div class="block">设置线段擦除（显示范围外）颜色，需要传入32位的ARGB格式，针对颜色线段生效。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/PolylineOptions.html#setEraseColor-boolean-int-">setEraseColor(boolean, int)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/PolylineOptions.html" title="com.amap.api.maps.model中的类">PolylineOptions</a></dt>
<dd>
<div class="block">设置线段擦除（显示范围外）颜色，需要传入32位的ARGB格式，针对颜色线段生效。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Circle.html#setFillColor-int-">setFillColor(int)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Circle.html" title="com.amap.api.maps.model中的类">Circle</a></dt>
<dd>
<div class="block">设置圆的填充颜色。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Polygon.html#setFillColor-int-">setFillColor(int)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Polygon.html" title="com.amap.api.maps.model中的类">Polygon</a></dt>
<dd>
<div class="block">设置多边形的填充颜色</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/animation/Animation.html#setFillMode-int-">setFillMode(int)</a></span> - 类 中的方法com.amap.api.maps.model.animation.<a href="../com/amap/api/maps/model/animation/Animation.html" title="com.amap.api.maps.model.animation中的类">Animation</a></dt>
<dd>
<div class="block">设置动画执行完成后的状态</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Marker.html#setFlat-boolean-">setFlat(boolean)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Marker.html" title="com.amap.api.maps.model中的类">Marker</a></dt>
<dd>
<div class="block">设置Marker覆盖物是否平贴在地图上。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MarkerOptions.html#setFlat-boolean-">setFlat(boolean)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MarkerOptions.html" title="com.amap.api.maps.model中的类">MarkerOptions</a></dt>
<dd>
<div class="block">设置Marker覆盖物是否平贴地图。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Text.html#setFontColor-int-">setFontColor(int)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Text.html" title="com.amap.api.maps.model中的类">Text</a></dt>
<dd>
<div class="block">设置文字覆盖物的字体颜色</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Text.html#setFontSize-int-">setFontSize(int)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Text.html" title="com.amap.api.maps.model中的类">Text</a></dt>
<dd>
<div class="block">设置文字覆盖物的字体大小</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Polyline.html#setGeodesic-boolean-">setGeodesic(boolean)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Polyline.html" title="com.amap.api.maps.model中的类">Polyline</a></dt>
<dd>
<div class="block">设置线段是否画大地曲线，默认false，不画大地曲线。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/BasePointOverlay.html#setGeoPoint-IPoint-">setGeoPoint(IPoint)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/BasePointOverlay.html" title="com.amap.api.maps.model中的类">BasePointOverlay</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/UiSettings.html#setGestureScaleByMapCenter-boolean-">setGestureScaleByMapCenter(boolean)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/UiSettings.html" title="com.amap.api.maps中的类">UiSettings</a></dt>
<dd>
<div class="block">设置是否以地图中心点缩放 <br>
 注：优先级低于<a href="../com/amap/api/maps/AMap.html#setPointToCenter-int-int-"><code>AMap.setPointToCenter(int, int)</code></a></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MarkerOptions.html#setGps-boolean-">setGps(boolean)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MarkerOptions.html" title="com.amap.api.maps.model中的类">MarkerOptions</a></dt>
<dd>
<div class="block">设置Marker覆盖物的坐标是否是Gps，默认为false。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Circle.html#setHoleOptions-java.util.List-">setHoleOptions(List&lt;BaseHoleOptions&gt;)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Circle.html" title="com.amap.api.maps.model中的类">Circle</a></dt>
<dd>
<div class="block">设置洞的配置项集合</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Polygon.html#setHoleOptions-java.util.List-">setHoleOptions(List&lt;BaseHoleOptions&gt;)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Polygon.html" title="com.amap.api.maps.model中的类">Polygon</a></dt>
<dd>
<div class="block">设置多边形空心洞的配置项集合</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Marker.html#setIcon-com.amap.api.maps.model.BitmapDescriptor-">setIcon(BitmapDescriptor)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Marker.html" title="com.amap.api.maps.model中的类">Marker</a></dt>
<dd>
<div class="block">设置 Marker覆盖物的图标</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Marker.html#setIcons-java.util.ArrayList-">setIcons(ArrayList&lt;BitmapDescriptor&gt;)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Marker.html" title="com.amap.api.maps.model中的类">Marker</a></dt>
<dd>
<div class="block">设置 Marker 的图标集合，相同图案的 icon 的 marker 最好使用同一个 BitmapDescriptor 对象以节省内存空间。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/GroundOverlay.html#setImage-com.amap.api.maps.model.BitmapDescriptor-">setImage(BitmapDescriptor)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/GroundOverlay.html" title="com.amap.api.maps.model中的类">GroundOverlay</a></dt>
<dd>
<div class="block">设置ground 覆盖物的图片信息，新图片会使用老图片的矩形区域。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/HeatMapItem.html#setIndexes-int:A-">setIndexes(int[])</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/HeatMapItem.html" title="com.amap.api.maps.model中的类">HeatMapItem</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#setIndoorBuildingInfo-com.amap.api.maps.model.IndoorBuildingInfo-">setIndoorBuildingInfo(IndoorBuildingInfo)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block">室内地图楼层控制接口，通过此接口可以控制某个室内地图显示的楼层。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/UiSettings.html#setIndoorSwitchEnabled-boolean-">setIndoorSwitchEnabled(boolean)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/UiSettings.html" title="com.amap.api.maps中的类">UiSettings</a></dt>
<dd>
<div class="block">设置室内地图楼层切换控件是否可见。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#setInfoWindowAdapter-com.amap.api.maps.AMap.InfoWindowAdapter-">setInfoWindowAdapter(AMap.InfoWindowAdapter)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block">设置marker的信息窗口定制接口。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Marker.html#setInfoWindowEnable-boolean-">setInfoWindowEnable(boolean)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Marker.html" title="com.amap.api.maps.model中的类">Marker</a></dt>
<dd>
<div class="block">设置Marker覆盖物的InfoWindow是否允许显示,默认为true <br>
 设置为false之后, 调用<a href="../com/amap/api/maps/model/Marker.html#showInfoWindow--"><code>Marker.showInfoWindow()</code></a> 将不会生效</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MarkerOptions.html#setInfoWindowOffset-int-int-">setInfoWindowOffset(int, int)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MarkerOptions.html" title="com.amap.api.maps.model中的类">MarkerOptions</a></dt>
<dd>
<div class="block">设置Marker覆盖物的InfoWindow相对Marker的偏移。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/GLTFOverlay.html#setInfoWindowView-com.amap.api.maps.model.BitmapDescriptor-">setInfoWindowView(BitmapDescriptor)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/GLTFOverlay.html" title="com.amap.api.maps.model中的类">GLTFOverlay</a></dt>
<dd>
<div class="block">设置InfoWindowView</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/HeatMapItem.html#setIntensity-double-">setIntensity(double)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/HeatMapItem.html" title="com.amap.api.maps.model中的类">HeatMapItem</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/animation/Animation.html#setInterpolator-android.view.animation.Interpolator-">setInterpolator(Interpolator)</a></span> - 类 中的方法com.amap.api.maps.model.animation.<a href="../com/amap/api/maps/model/animation/Animation.html" title="com.amap.api.maps.model.animation中的类">Animation</a></dt>
<dd>
<div class="block">设置插值器,默认是线性插值器</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MultiPointOverlay.html#setItems-java.util.List-">setItems(List&lt;MultiPointItem&gt;)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MultiPointOverlay.html" title="com.amap.api.maps.model中的类">MultiPointOverlay</a></dt>
<dd>
<div class="block">设置海量点，初始化或者更新海量点时可以调用此方法<br>
 同步方法，如果点很多时，会消耗一定的时间，建议放在子线程中处理。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/City.html#setJianpin-java.lang.String-">setJianpin(String)</a></span> - 类 中的方法com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/City.html" title="com.amap.api.maps.offlinemap中的类">City</a></dt>
<dd>
<div class="block">设置城市名称简拼拼音。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/Province.html#setJianpin-java.lang.String-">setJianpin(String)</a></span> - 类 中的方法com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/Province.html" title="com.amap.api.maps.offlinemap中的类">Province</a></dt>
<dd>
<div class="block">设置省名称简拼拼音。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/PoiPara.html#setKeywords-java.lang.String-">setKeywords(String)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/PoiPara.html" title="com.amap.api.maps.model中的类">PoiPara</a></dt>
<dd>
<div class="block">poi 检索关键字”|”分割。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/trace/TraceLocation.html#setLatitude-double-">setLatitude(double)</a></span> - 类 中的方法com.amap.api.trace.<a href="../com/amap/api/trace/TraceLocation.html" title="com.amap.api.trace中的类">TraceLocation</a></dt>
<dd>
<div class="block">设置纬度</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/GLTFOverlay.html#setLatLng-com.amap.api.maps.model.LatLng-">setLatLng(LatLng)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/GLTFOverlay.html" title="com.amap.api.maps.model中的类">GLTFOverlay</a></dt>
<dd>
<div class="block">设置位置</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MultiPointItem.html#setLatLng-com.amap.api.maps.model.LatLng-">setLatLng(LatLng)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MultiPointItem.html" title="com.amap.api.maps.model中的类">MultiPointItem</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#setLoadOfflineData-boolean-">setLoadOfflineData(boolean)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block">重新加载离线地图数据。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#setLocationSource-com.amap.api.maps.LocationSource-">setLocationSource(LocationSource)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block">设置定位源（locationSource）。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/UiSettings.html#setLogoBottomMargin-int-">setLogoBottomMargin(int)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/UiSettings.html" title="com.amap.api.maps中的类">UiSettings</a></dt>
<dd>
<div class="block">设置Logo下边界距离屏幕底部的边距</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/UiSettings.html#setLogoLeftMargin-int-">setLogoLeftMargin(int)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/UiSettings.html" title="com.amap.api.maps中的类">UiSettings</a></dt>
<dd>
<div class="block">设置Logo左边界距离屏幕左侧的边距</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/UiSettings.html#setLogoPosition-int-">setLogoPosition(int)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/UiSettings.html" title="com.amap.api.maps中的类">UiSettings</a></dt>
<dd>
<div class="block">设置“高德地图”Logo的位置。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/trace/TraceLocation.html#setLongitude-double-">setLongitude(double)</a></span> - 类 中的方法com.amap.api.trace.<a href="../com/amap/api/trace/TraceLocation.html" title="com.amap.api.trace中的类">TraceLocation</a></dt>
<dd>
<div class="block">设置经度</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/particle/ParticleOverlay.html#setLoop-boolean-">setLoop(boolean)</a></span> - 类 中的方法com.amap.api.maps.model.particle.<a href="../com/amap/api/maps/model/particle/ParticleOverlay.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlay</a></dt>
<dd>
<div class="block">整个粒子效果是否循环</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/particle/ParticleOverlayOptions.html#setLoop-boolean-">setLoop(boolean)</a></span> - 类 中的方法com.amap.api.maps.model.particle.<a href="../com/amap/api/maps/model/particle/ParticleOverlayOptions.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlayOptions</a></dt>
<dd>
<div class="block">整个粒子效果是否循环</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#setMapCustomEnable-boolean-">setMapCustomEnable(boolean)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span>
<div class="block"><span class="deprecationComment">该方法已无效，建议到官网lbs.amap.com更新样式文件并使用 <a href="../com/amap/api/maps/AMap.html#setCustomMapStyle-com.amap.api.maps.model.CustomMapStyleOptions-"><code>AMap.setCustomMapStyle(CustomMapStyleOptions)</code></a> )} 替换</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#setMapStatusLimits-com.amap.api.maps.model.LatLngBounds-">setMapStatusLimits(LatLngBounds)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block">设置地图显示范围，无论如何操作地图，显示区域都不能超过该矩形区域。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#setMapTextZIndex-int-">setMapTextZIndex(int)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block">设置地图底图文字标注的层级指数，默认为0，用来比较覆盖物（polyline、polygon、circle等）的zIndex。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#setMapType-int-">setMapType(int)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block">设置地图模式。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Marker.html#setMarkerOptions-com.amap.api.maps.model.MarkerOptions-">setMarkerOptions(MarkerOptions)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Marker.html" title="com.amap.api.maps.model中的类">Marker</a></dt>
<dd>
<div class="block">设置Marker覆盖物的属性选项类
 通过markerOption 给marker设置属性</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/particle/ParticleOverlay.html#setMaxParticles-int-">setMaxParticles(int)</a></span> - 类 中的方法com.amap.api.maps.model.particle.<a href="../com/amap/api/maps/model/particle/ParticleOverlay.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlay</a></dt>
<dd>
<div class="block">整个粒子效果的粒子最大数量</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/particle/ParticleOverlayOptions.html#setMaxParticles-int-">setMaxParticles(int)</a></span> - 类 中的方法com.amap.api.maps.model.particle.<a href="../com/amap/api/maps/model/particle/ParticleOverlayOptions.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlayOptions</a></dt>
<dd>
<div class="block">整个粒子效果的粒子最大数量</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/TileOverlaySource.html#setMaxZoom-int-">setMaxZoom(int)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/TileOverlaySource.html" title="com.amap.api.maps.model中的类">TileOverlaySource</a></dt>
<dd>
<div class="block">数据最大级别，高于了没有数据</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#setMaxZoomLevel-float-">setMaxZoomLevel(float)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block">设置地图最大缩放级别 缩放级别范围为[3, 20],超出范围将按最大级别计算 。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/TileOverlaySource.html#setMinZoom-int-">setMinZoom(int)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/TileOverlaySource.html" title="com.amap.api.maps.model中的类">TileOverlaySource</a></dt>
<dd>
<div class="block">数据最小级别，低于了没有数据</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#setMinZoomLevel-float-">setMinZoomLevel(float)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block">设置最小缩放级别 缩放级别范围为[3, 20],超出范围将按最小级别计算</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/utils/overlay/MovingPointOverlay.html#setMoveListener-com.amap.api.maps.utils.overlay.MovingPointOverlay.MoveListener-">setMoveListener(MovingPointOverlay.MoveListener)</a></span> - 类 中的方法com.amap.api.maps.utils.overlay.<a href="../com/amap/api/maps/utils/overlay/MovingPointOverlay.html" title="com.amap.api.maps.utils.overlay中的类">MovingPointOverlay</a></dt>
<dd>
<div class="block">设置Marker移动的回调方法</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/utils/overlay/SmoothMoveMarker.html#setMoveListener-com.amap.api.maps.utils.overlay.SmoothMoveMarker.MoveListener-">setMoveListener(SmoothMoveMarker.MoveListener)</a></span> - 类 中的方法com.amap.api.maps.utils.overlay.<a href="../com/amap/api/maps/utils/overlay/SmoothMoveMarker.html" title="com.amap.api.maps.utils.overlay中的类">SmoothMoveMarker</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">设置Marker移动的回调方法</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/UiSettings.html#setMyLocationButtonEnabled-boolean-">setMyLocationButtonEnabled(boolean)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/UiSettings.html" title="com.amap.api.maps中的类">UiSettings</a></dt>
<dd>
<div class="block">设置定位按钮是否可见。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#setMyLocationEnabled-boolean-">setMyLocationEnabled(boolean)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block">设置是否打开定位图层（myLocationOverlay）。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#setMyLocationRotateAngle-float-">setMyLocationRotateAngle(float)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span>
<div class="block"><span class="deprecationComment">自5.0.0 废弃</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#setMyLocationStyle-com.amap.api.maps.model.MyLocationStyle-">setMyLocationStyle(MyLocationStyle)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block">设置定位图层（myLocationOverlay）的样式。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#setMyLocationType-int-">setMyLocationType(int)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span>
<div class="block"><span class="deprecationComment">5.0.0 之后请使用 <a href="../com/amap/api/maps/AMap.html#setMyLocationStyle-com.amap.api.maps.model.MyLocationStyle-"><code>AMap.setMyLocationStyle(MyLocationStyle)</code></a> 代替</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#setMyTrafficStyle-com.amap.api.maps.model.MyTrafficStyle-">setMyTrafficStyle(MyTrafficStyle)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span>
<div class="block"><span class="deprecationComment">自7.8.0之后不再支持</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/NaviPara.html#setNaviStyle-int-">setNaviStyle(int)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/NaviPara.html" title="com.amap.api.maps.model中的类">NaviPara</a></dt>
<dd>
<div class="block">设置导航类型。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/MapsInitializer.html#setNetWorkEnable-boolean-">setNetWorkEnable(boolean)</a></span> - 类 中的静态方法com.amap.api.maps.<a href="../com/amap/api/maps/MapsInitializer.html" title="com.amap.api.maps中的类">MapsInitializer</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span>
<div class="block"><span class="deprecationComment">5.0.0开始废弃</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/BasePointOverlay.html#setObject-java.lang.Object-">setObject(Object)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/BasePointOverlay.html" title="com.amap.api.maps.model中的类">BasePointOverlay</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Marker.html#setObject-java.lang.Object-">setObject(Object)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Marker.html" title="com.amap.api.maps.model中的类">Marker</a></dt>
<dd>
<div class="block">设置Marker覆盖物的附加信息对象。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MultiPointItem.html#setObject-java.lang.Object-">setObject(Object)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MultiPointItem.html" title="com.amap.api.maps.model中的类">MultiPointItem</a></dt>
<dd>
<div class="block">设置附加信息对象。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Text.html#setObject-java.lang.Object-">setObject(Object)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Text.html" title="com.amap.api.maps.model中的类">Text</a></dt>
<dd>
<div class="block">设置文字覆盖物的额外信息。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/TextOptions.html#setObject-java.lang.Object-">setObject(Object)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/TextOptions.html" title="com.amap.api.maps.model中的类">TextOptions</a></dt>
<dd>
<div class="block">设置文字覆盖物的额外信息</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#setOnCameraChangeListener-com.amap.api.maps.AMap.OnCameraChangeListener-">setOnCameraChangeListener(AMap.OnCameraChangeListener)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block">设置地图状态的监听接口。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/WearMapView.html#setOnDismissCallbackListener-com.amap.api.maps.WearMapView.OnDismissCallback-">setOnDismissCallbackListener(WearMapView.OnDismissCallback)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/WearMapView.html" title="com.amap.api.maps中的类">WearMapView</a></dt>
<dd>
<div class="block">设置View退出回调监听</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#setOnIndoorBuildingActiveListener-com.amap.api.maps.AMap.OnIndoorBuildingActiveListener-">setOnIndoorBuildingActiveListener(AMap.OnIndoorBuildingActiveListener)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block">设置室内地图状态监听接口</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#setOnInfoWindowClickListener-com.amap.api.maps.AMap.OnInfoWindowClickListener-">setOnInfoWindowClickListener(AMap.OnInfoWindowClickListener)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block">设置marker的信息窗口点击事件监听接口。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#setOnMapClickListener-com.amap.api.maps.AMap.OnMapClickListener-">setOnMapClickListener(AMap.OnMapClickListener)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block">设置地图点击事件监听接口。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#setOnMapLoadedListener-com.amap.api.maps.AMap.OnMapLoadedListener-">setOnMapLoadedListener(AMap.OnMapLoadedListener)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block">设置地图加载完成监听接口</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#setOnMapLongClickListener-com.amap.api.maps.AMap.OnMapLongClickListener-">setOnMapLongClickListener(AMap.OnMapLongClickListener)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block">设置地图长按事件监听接口。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#setOnMapTouchListener-com.amap.api.maps.AMap.OnMapTouchListener-">setOnMapTouchListener(AMap.OnMapTouchListener)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block">设置地图触摸事件监听接口。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#setOnMarkerClickListener-com.amap.api.maps.AMap.OnMarkerClickListener-">setOnMarkerClickListener(AMap.OnMarkerClickListener)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block">设置marker点击事件监听接口。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#setOnMarkerDragListener-com.amap.api.maps.AMap.OnMarkerDragListener-">setOnMarkerDragListener(AMap.OnMarkerDragListener)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block">marker拖动事件监听接口</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#setOnMultiPointClickListener-com.amap.api.maps.AMap.OnMultiPointClickListener-">setOnMultiPointClickListener(AMap.OnMultiPointClickListener)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block">设置海量点单击事件监听</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#setOnMyLocationChangeListener-com.amap.api.maps.AMap.OnMyLocationChangeListener-">setOnMyLocationChangeListener(AMap.OnMyLocationChangeListener)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block">设置用户定位信息监听接口。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/OfflineMapManager.html#setOnOfflineLoadedListener-com.amap.api.maps.offlinemap.OfflineMapManager.OfflineLoadedListener-">setOnOfflineLoadedListener(OfflineMapManager.OfflineLoadedListener)</a></span> - 类 中的方法com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/OfflineMapManager.html" title="com.amap.api.maps.offlinemap中的类">OfflineMapManager</a></dt>
<dd>
<div class="block">设置离线地图城市列表初始化回调。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#setOnPOIClickListener-com.amap.api.maps.AMap.OnPOIClickListener-">setOnPOIClickListener(AMap.OnPOIClickListener)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block">设置底图poi点击事件监听接口。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#setOnPolylineClickListener-com.amap.api.maps.AMap.OnPolylineClickListener-">setOnPolylineClickListener(AMap.OnPolylineClickListener)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block">设置polyline点击事件监听接口。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/HeatMapGridLayer.html#setOptions-com.amap.api.maps.model.HeatMapGridLayerOptions-">setOptions(HeatMapGridLayerOptions)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/HeatMapGridLayer.html" title="com.amap.api.maps.model中的类">HeatMapGridLayer</a></dt>
<dd>
<div class="block">更新热力图属性
 更新时会重新计算热力图数据，建议不要频繁更新</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/HeatMapLayer.html#setOptions-com.amap.api.maps.model.HeatMapLayerOptions-">setOptions(HeatMapLayerOptions)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/HeatMapLayer.html" title="com.amap.api.maps.model中的类">HeatMapLayer</a></dt>
<dd>
<div class="block">更新热力图属性
 更新时会重新计算热力图数据，建议不要频繁更新</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Polyline.html#setOptions-com.amap.api.maps.model.PolylineOptions-">setOptions(PolylineOptions)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Polyline.html" title="com.amap.api.maps.model中的类">Polyline</a></dt>
<dd>
<div class="block">设置线段选项信息<br>
 设置多个属性时可以使用此方法,单个属性设置建议使用各自对应的方法。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/particle/ParticleOverlay.html#setParticleEmission-com.amap.api.maps.model.particle.ParticleEmissionModule-">setParticleEmission(ParticleEmissionModule)</a></span> - 类 中的方法com.amap.api.maps.model.particle.<a href="../com/amap/api/maps/model/particle/ParticleOverlay.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlay</a></dt>
<dd>
<div class="block">设置发射率，每隔多少时间发射粒子数量，越多会越密集</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/particle/ParticleOverlayOptions.html#setParticleEmissionModule-com.amap.api.maps.model.particle.ParticleEmissionModule-">setParticleEmissionModule(ParticleEmissionModule)</a></span> - 类 中的方法com.amap.api.maps.model.particle.<a href="../com/amap/api/maps/model/particle/ParticleOverlayOptions.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlayOptions</a></dt>
<dd>
<div class="block">发射率，每个多少时间发射粒子数量，越多会越密集</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/particle/ParticleOverlay.html#setParticleLifeTime-long-">setParticleLifeTime(long)</a></span> - 类 中的方法com.amap.api.maps.model.particle.<a href="../com/amap/api/maps/model/particle/ParticleOverlay.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlay</a></dt>
<dd>
<div class="block">每个粒子的存活时间</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/particle/ParticleOverlayOptions.html#setParticleLifeTime-long-">setParticleLifeTime(long)</a></span> - 类 中的方法com.amap.api.maps.model.particle.<a href="../com/amap/api/maps/model/particle/ParticleOverlayOptions.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlayOptions</a></dt>
<dd>
<div class="block">每个粒子的存活时间,单位毫秒</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/particle/ParticleOverlay.html#setParticleOverLifeModule-com.amap.api.maps.model.particle.ParticleOverLifeModule-">setParticleOverLifeModule(ParticleOverLifeModule)</a></span> - 类 中的方法com.amap.api.maps.model.particle.<a href="../com/amap/api/maps/model/particle/ParticleOverlay.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlay</a></dt>
<dd>
<div class="block">设置每个粒子生命周期过程中状态变化，包含速度、旋转和颜色的变化</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/particle/ParticleOverlayOptions.html#setParticleOverLifeModule-com.amap.api.maps.model.particle.ParticleOverLifeModule-">setParticleOverLifeModule(ParticleOverLifeModule)</a></span> - 类 中的方法com.amap.api.maps.model.particle.<a href="../com/amap/api/maps/model/particle/ParticleOverlayOptions.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlayOptions</a></dt>
<dd>
<div class="block">每个粒子生命周期过程中状态变化，包含速度、旋转和颜色的变化</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/particle/ParticleOverlay.html#setParticleShapeModule-com.amap.api.maps.model.particle.ParticleShapeModule-">setParticleShapeModule(ParticleShapeModule)</a></span> - 类 中的方法com.amap.api.maps.model.particle.<a href="../com/amap/api/maps/model/particle/ParticleOverlay.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlay</a></dt>
<dd>
<div class="block">设置发射模型，比如所有粒子从一个点出来，或者从某个区域出来</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/particle/ParticleOverlayOptions.html#setParticleShapeModule-com.amap.api.maps.model.particle.ParticleShapeModule-">setParticleShapeModule(ParticleShapeModule)</a></span> - 类 中的方法com.amap.api.maps.model.particle.<a href="../com/amap/api/maps/model/particle/ParticleOverlayOptions.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlayOptions</a></dt>
<dd>
<div class="block">发射模型，比如所有粒子从一个点出来，或者从某个区域出来</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/particle/ParticleOverlayOptions.html#setParticleStartColor-com.amap.api.maps.model.particle.ColorGenerate-">setParticleStartColor(ColorGenerate)</a></span> - 类 中的方法com.amap.api.maps.model.particle.<a href="../com/amap/api/maps/model/particle/ParticleOverlayOptions.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlayOptions</a></dt>
<dd>
<div class="block">每个粒子的初始颜色</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/particle/ParticleOverlay.html#setParticleStartSpeed-com.amap.api.maps.model.particle.VelocityGenerate-">setParticleStartSpeed(VelocityGenerate)</a></span> - 类 中的方法com.amap.api.maps.model.particle.<a href="../com/amap/api/maps/model/particle/ParticleOverlay.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlay</a></dt>
<dd>
<div class="block">每个粒子的初始速度，单位像素/秒</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/particle/ParticleOverlayOptions.html#setParticleStartSpeed-com.amap.api.maps.model.particle.VelocityGenerate-">setParticleStartSpeed(VelocityGenerate)</a></span> - 类 中的方法com.amap.api.maps.model.particle.<a href="../com/amap/api/maps/model/particle/ParticleOverlayOptions.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlayOptions</a></dt>
<dd>
<div class="block">粒子初始速度，每秒多少个像素</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Marker.html#setPeriod-int-">setPeriod(int)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Marker.html" title="com.amap.api.maps.model中的类">Marker</a></dt>
<dd>
<div class="block">设置多少帧刷新一次图片资源，Marker动画的间隔时间，值越小动画越快。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Marker.html#setPerspective-boolean-">setPerspective(boolean)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Marker.html" title="com.amap.api.maps.model中的类">Marker</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span>
<div class="block"><span class="deprecationComment">已取消这个效果</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/City.html#setPinyin-java.lang.String-">setPinyin(String)</a></span> - 类 中的方法com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/City.html" title="com.amap.api.maps.offlinemap中的类">City</a></dt>
<dd>
<div class="block">设置城市名称拼音</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/Province.html#setPinyin-java.lang.String-">setPinyin(String)</a></span> - 类 中的方法com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/Province.html" title="com.amap.api.maps.offlinemap中的类">Province</a></dt>
<dd>
<div class="block">设置省名称拼音。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/NavigateArrow.html#setPoints-java.util.List-">setPoints(List&lt;LatLng&gt;)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/NavigateArrow.html" title="com.amap.api.maps.model中的类">NavigateArrow</a></dt>
<dd>
<div class="block">设置箭头(NavigateArrow)覆盖物的顶点坐标。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Polygon.html#setPoints-java.util.List-">setPoints(List&lt;LatLng&gt;)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Polygon.html" title="com.amap.api.maps.model中的类">Polygon</a></dt>
<dd>
<div class="block">设置多边形的顶点。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Polyline.html#setPoints-java.util.List-">setPoints(List&lt;LatLng&gt;)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Polyline.html" title="com.amap.api.maps.model中的类">Polyline</a></dt>
<dd>
<div class="block">设置线段的坐标点列表。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/PolylineOptions.html#setPoints-java.util.List-">setPoints(List&lt;LatLng&gt;)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/PolylineOptions.html" title="com.amap.api.maps.model中的类">PolylineOptions</a></dt>
<dd>
<div class="block">设置线段的点坐标集合,如果以前已经存在点,则会清空以前的点。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/utils/overlay/MovingPointOverlay.html#setPoints-java.util.List-">setPoints(List&lt;LatLng&gt;)</a></span> - 类 中的方法com.amap.api.maps.utils.overlay.<a href="../com/amap/api/maps/utils/overlay/MovingPointOverlay.html" title="com.amap.api.maps.utils.overlay中的类">MovingPointOverlay</a></dt>
<dd>
<div class="block">设置平滑移动的经纬度数组</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/utils/overlay/SmoothMoveMarker.html#setPoints-java.util.List-">setPoints(List&lt;LatLng&gt;)</a></span> - 类 中的方法com.amap.api.maps.utils.overlay.<a href="../com/amap/api/maps/utils/overlay/SmoothMoveMarker.html" title="com.amap.api.maps.utils.overlay中的类">SmoothMoveMarker</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">设置平滑移动的经纬度数组</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#setPointToCenter-int-int-">setPointToCenter(int, int)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block">设置屏幕上的某个像素点为地图中心点。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/BasePointOverlay.html#setPosition-com.amap.api.maps.model.LatLng-">setPosition(LatLng)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/BasePointOverlay.html" title="com.amap.api.maps.model中的类">BasePointOverlay</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/GroundOverlay.html#setPosition-com.amap.api.maps.model.LatLng-">setPosition(LatLng)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/GroundOverlay.html" title="com.amap.api.maps.model中的类">GroundOverlay</a></dt>
<dd>
<div class="block">设置ground 覆盖物的位置，ground 覆盖物的其他属性不变。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Marker.html#setPosition-com.amap.api.maps.model.LatLng-">setPosition(LatLng)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Marker.html" title="com.amap.api.maps.model中的类">Marker</a></dt>
<dd>
<div class="block">设置 Marker 覆盖物的位置坐标。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Text.html#setPosition-com.amap.api.maps.model.LatLng-">setPosition(LatLng)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Text.html" title="com.amap.api.maps.model中的类">Text</a></dt>
<dd>
<div class="block">设置文字覆盖物的坐标位置。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Marker.html#setPositionByPixels-int-int-">setPositionByPixels(int, int)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Marker.html" title="com.amap.api.maps.model中的类">Marker</a></dt>
<dd>
<div class="block">设置marker覆盖物在屏幕的像素坐标。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/GroundOverlay.html#setPositionFromBounds-com.amap.api.maps.model.LatLngBounds-">setPositionFromBounds(LatLngBounds)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/GroundOverlay.html" title="com.amap.api.maps.model中的类">GroundOverlay</a></dt>
<dd>
<div class="block">根据矩形区域设置ground 覆盖物的位置。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/DownloadProgressView.html#setProgress-int-">setProgress(int)</a></span> - 类 中的方法com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/DownloadProgressView.html" title="com.amap.api.maps.offlinemap中的类">DownloadProgressView</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/trace/TraceOverlay.html#setProperCamera-java.util.List-">setProperCamera(List&lt;LatLng&gt;)</a></span> - 类 中的方法com.amap.api.trace.<a href="../com/amap/api/trace/TraceOverlay.html" title="com.amap.api.trace中的类">TraceOverlay</a></dt>
<dd>
<div class="block">根据传入的点集合设置合适的Camera</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/MapsInitializer.html#setProtocol-int-">setProtocol(int)</a></span> - 类 中的静态方法com.amap.api.maps.<a href="../com/amap/api/maps/MapsInitializer.html" title="com.amap.api.maps中的类">MapsInitializer</a></dt>
<dd>
<div class="block">设置访问使用的协议类别。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/Province.html#setProvinceCode-java.lang.String-">setProvinceCode(String)</a></span> - 类 中的方法com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/Province.html" title="com.amap.api.maps.offlinemap中的类">Province</a></dt>
<dd>
<div class="block">设置省行政编码。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/Province.html#setProvinceName-java.lang.String-">setProvinceName(String)</a></span> - 类 中的方法com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/Province.html" title="com.amap.api.maps.offlinemap中的类">Province</a></dt>
<dd>
<div class="block">设置省名称。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Circle.html#setRadius-double-">setRadius(double)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Circle.html" title="com.amap.api.maps.model中的类">Circle</a></dt>
<dd>
<div class="block">设置圆的半径，单位米。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/animation/Animation.html#setRepeatCount-int-">setRepeatCount(int)</a></span> - 类 中的方法com.amap.api.maps.model.animation.<a href="../com/amap/api/maps/model/animation/Animation.html" title="com.amap.api.maps.model.animation中的类">Animation</a></dt>
<dd>
<div class="block">设置动画重复执行的次数,默认为0</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/animation/Animation.html#setRepeatMode-int-">setRepeatMode(int)</a></span> - 类 中的方法com.amap.api.maps.model.animation.<a href="../com/amap/api/maps/model/animation/Animation.html" title="com.amap.api.maps.model.animation中的类">Animation</a></dt>
<dd>
<div class="block">重复执行的模式，默认从前往后执行</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Text.html#setRotate-float-">setRotate(float)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Text.html" title="com.amap.api.maps.model中的类">Text</a></dt>
<dd>
<div class="block">设置文字覆盖物的旋转角度，以锚点旋转。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/BasePointOverlay.html#setRotateAngle-float-">setRotateAngle(float)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/BasePointOverlay.html" title="com.amap.api.maps.model中的类">BasePointOverlay</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Marker.html#setRotateAngle-float-">setRotateAngle(float)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Marker.html" title="com.amap.api.maps.model中的类">Marker</a></dt>
<dd>
<div class="block">设置Marker覆盖物图片旋转的角度，从正北开始，逆时针计算。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/UiSettings.html#setRotateGesturesEnabled-boolean-">setRotateGesturesEnabled(boolean)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/UiSettings.html" title="com.amap.api.maps中的类">UiSettings</a></dt>
<dd>
<div class="block">设置旋转手势是否可用。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/particle/ParticleOverLifeModule.html#setRotateOverLife-com.amap.api.maps.model.particle.RotationOverLife-">setRotateOverLife(RotationOverLife)</a></span> - 类 中的方法com.amap.api.maps.model.particle.<a href="../com/amap/api/maps/model/particle/ParticleOverLifeModule.html" title="com.amap.api.maps.model.particle中的类">ParticleOverLifeModule</a></dt>
<dd>
<div class="block">旋转角度变化</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/UiSettings.html#setScaleControlsEnabled-boolean-">setScaleControlsEnabled(boolean)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/UiSettings.html" title="com.amap.api.maps中的类">UiSettings</a></dt>
<dd>
<div class="block">设置比例尺控件是否可见</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/UiSettings.html#setScrollGesturesEnabled-boolean-">setScrollGesturesEnabled(boolean)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/UiSettings.html" title="com.amap.api.maps中的类">UiSettings</a></dt>
<dd>
<div class="block">设置拖拽手势是否可用。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MyTrafficStyle.html#setSeriousCongestedColor-int-">setSeriousCongestedColor(int)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MyTrafficStyle.html" title="com.amap.api.maps.model中的类">MyTrafficStyle</a></dt>
<dd>
<div class="block">设置行驶严重拥堵路段的标记颜色。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/NavigateArrow.html#setSideColor-int-">setSideColor(int)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/NavigateArrow.html" title="com.amap.api.maps.model中的类">NavigateArrow</a></dt>
<dd>
<div class="block">设置箭头(NavigateArrow)覆盖物的侧边颜色。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/OfflineMapCity.html#setSize-long-">setSize(long)</a></span> - 类 中的方法com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/OfflineMapCity.html" title="com.amap.api.maps.offlinemap中的类">OfflineMapCity</a></dt>
<dd>
<div class="block">设置下载城市数据的大小，单位字节。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/OfflineMapProvince.html#setSize-long-">setSize(long)</a></span> - 类 中的方法com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/OfflineMapProvince.html" title="com.amap.api.maps.offlinemap中的类">OfflineMapProvince</a></dt>
<dd>
<div class="block">设置省份大小</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/particle/ParticleOverLifeModule.html#setSizeOverLife-com.amap.api.maps.model.particle.SizeOverLife-">setSizeOverLife(SizeOverLife)</a></span> - 类 中的方法com.amap.api.maps.model.particle.<a href="../com/amap/api/maps/model/particle/ParticleOverLifeModule.html" title="com.amap.api.maps.model.particle中的类">ParticleOverLifeModule</a></dt>
<dd>
<div class="block">大小变化</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MyTrafficStyle.html#setSlowColor-int-">setSlowColor(int)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MyTrafficStyle.html" title="com.amap.api.maps.model中的类">MyTrafficStyle</a></dt>
<dd>
<div class="block">设置行驶缓慢路段的标记颜色。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MyTrafficStyle.html#setSmoothColor-int-">setSmoothColor(int)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MyTrafficStyle.html" title="com.amap.api.maps.model中的类">MyTrafficStyle</a></dt>
<dd>
<div class="block">设置行驶畅通路段的标记颜色。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/BasePointOverlay.html#setSnippet-java.lang.String-">setSnippet(String)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/BasePointOverlay.html" title="com.amap.api.maps.model中的类">BasePointOverlay</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Marker.html#setSnippet-java.lang.String-">setSnippet(String)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Marker.html" title="com.amap.api.maps.model中的类">Marker</a></dt>
<dd>
<div class="block">设置Marker 覆盖物的文字片段。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MultiPointItem.html#setSnippet-java.lang.String-">setSnippet(String)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MultiPointItem.html" title="com.amap.api.maps.model中的类">MultiPointItem</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/trace/TraceLocation.html#setSpeed-float-">setSpeed(float)</a></span> - 类 中的方法com.amap.api.trace.<a href="../com/amap/api/trace/TraceLocation.html" title="com.amap.api.trace中的类">TraceLocation</a></dt>
<dd>
<div class="block">设置速度</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/particle/ParticleOverlay.html#setStartColor-com.amap.api.maps.model.particle.ColorGenerate-">setStartColor(ColorGenerate)</a></span> - 类 中的方法com.amap.api.maps.model.particle.<a href="../com/amap/api/maps/model/particle/ParticleOverlay.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlay</a></dt>
<dd>
<div class="block">每个粒子的初始颜色</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/RoutePara.html#setStartName-java.lang.String-">setStartName(String)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/RoutePara.html" title="com.amap.api.maps.model中的类">RoutePara</a></dt>
<dd>
<div class="block">设置路线检索起点地址名称。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/particle/ParticleOverlay.html#setStartParticleSize-int-int-">setStartParticleSize(int, int)</a></span> - 类 中的方法com.amap.api.maps.model.particle.<a href="../com/amap/api/maps/model/particle/ParticleOverlay.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlay</a></dt>
<dd>
<div class="block">每个粒子的初始大小，单位像素</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/particle/ParticleOverlayOptions.html#setStartParticleSize-int-int-">setStartParticleSize(int, int)</a></span> - 类 中的方法com.amap.api.maps.model.particle.<a href="../com/amap/api/maps/model/particle/ParticleOverlayOptions.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlayOptions</a></dt>
<dd>
<div class="block">粒子显示大小-宽高</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/RoutePara.html#setStartPoint-com.amap.api.maps.model.LatLng-">setStartPoint(LatLng)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/RoutePara.html" title="com.amap.api.maps.model中的类">RoutePara</a></dt>
<dd>
<div class="block">设置路线检索起点坐标。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/OfflineMapCity.html#setState-int-">setState(int)</a></span> - 类 中的方法com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/OfflineMapCity.html" title="com.amap.api.maps.offlinemap中的类">OfflineMapCity</a></dt>
<dd>
<div class="block">设置城市下载状态。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/OfflineMapProvince.html#setState-int-">setState(int)</a></span> - 类 中的方法com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/OfflineMapProvince.html" title="com.amap.api.maps.offlinemap中的类">OfflineMapProvince</a></dt>
<dd>
<div class="block">设置省份下载状态</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Arc.html#setStrokeColor-int-">setStrokeColor(int)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Arc.html" title="com.amap.api.maps.model中的类">Arc</a></dt>
<dd>
<div class="block">设置弧形的边框颜色。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Circle.html#setStrokeColor-int-">setStrokeColor(int)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Circle.html" title="com.amap.api.maps.model中的类">Circle</a></dt>
<dd>
<div class="block">设置圆的边框颜色。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Polygon.html#setStrokeColor-int-">setStrokeColor(int)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Polygon.html" title="com.amap.api.maps.model中的类">Polygon</a></dt>
<dd>
<div class="block">设置多边形的边框颜色</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Circle.html#setStrokeDottedLineType-int-">setStrokeDottedLineType(int)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Circle.html" title="com.amap.api.maps.model中的类">Circle</a></dt>
<dd>
<div class="block">设置圆的边框虚线形状。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/CircleOptions.html#setStrokeDottedLineType-int-">setStrokeDottedLineType(int)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/CircleOptions.html" title="com.amap.api.maps.model中的类">CircleOptions</a></dt>
<dd>
<div class="block">设置圆的边框虚线形状。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Arc.html#setStrokeWidth-float-">setStrokeWidth(float)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Arc.html" title="com.amap.api.maps.model中的类">Arc</a></dt>
<dd>
<div class="block">设置弧形的边框宽度，单位像素。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Circle.html#setStrokeWidth-float-">setStrokeWidth(float)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Circle.html" title="com.amap.api.maps.model中的类">Circle</a></dt>
<dd>
<div class="block">设置圆的边框宽度，单位像素。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Polygon.html#setStrokeWidth-float-">setStrokeWidth(float)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Polygon.html" title="com.amap.api.maps.model中的类">Polygon</a></dt>
<dd>
<div class="block">设置多边形的边框宽度</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/CustomMapStyleOptions.html#setStyleData-byte:A-">setStyleData(byte[])</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/CustomMapStyleOptions.html" title="com.amap.api.maps.model中的类">CustomMapStyleOptions</a></dt>
<dd>
<div class="block">自定义样式二进制，使用二进制可以更快加载出自定义样式，如果设置了则不会读取<a href="../com/amap/api/maps/model/CustomMapStyleOptions.html#setStyleDataPath-java.lang.String-"><code>CustomMapStyleOptions.setStyleDataPath(String)</code></a></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/CustomMapStyleOptions.html#setStyleDataPath-java.lang.String-">setStyleDataPath(String)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/CustomMapStyleOptions.html" title="com.amap.api.maps.model中的类">CustomMapStyleOptions</a></dt>
<dd>
<div class="block">自定义样式路径</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/CustomMapStyleOptions.html#setStyleExtraData-byte:A-">setStyleExtraData(byte[])</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/CustomMapStyleOptions.html" title="com.amap.api.maps.model中的类">CustomMapStyleOptions</a></dt>
<dd>
<div class="block">样式额外的配置，比如路况，背景颜色等，使用二进制可以更快加载出自定义样式，如果设置了则不会读取styleExtraPath</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/CustomMapStyleOptions.html#setStyleExtraPath-java.lang.String-">setStyleExtraPath(String)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/CustomMapStyleOptions.html" title="com.amap.api.maps.model中的类">CustomMapStyleOptions</a></dt>
<dd>
<div class="block">样式额外的配置，比如路况，背景颜色等 文件路径</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/CustomMapStyleOptions.html#setStyleId-java.lang.String-">setStyleId(String)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/CustomMapStyleOptions.html" title="com.amap.api.maps.model中的类">CustomMapStyleOptions</a></dt>
<dd>
<div class="block">设置底图自定义样式对应的styleID，id从官网获取。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/CustomMapStyleOptions.html#setStyleTextureData-byte:A-">setStyleTextureData(byte[])</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/CustomMapStyleOptions.html" title="com.amap.api.maps.model中的类">CustomMapStyleOptions</a></dt>
<dd>
<div class="block">自定义样式纹理二进制，使用二进制可以更快加载出自定义样式，如果设置了则不会读取<a href="../com/amap/api/maps/model/CustomMapStyleOptions.html#setStyleTexturePath-java.lang.String-"><code>CustomMapStyleOptions.setStyleTexturePath(String)</code></a> }</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/CustomMapStyleOptions.html#setStyleTexturePath-java.lang.String-">setStyleTexturePath(String)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/CustomMapStyleOptions.html" title="com.amap.api.maps.model中的类">CustomMapStyleOptions</a></dt>
<dd>
<div class="block">自定义样式纹理路径</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/NaviPara.html#setTargetPoint-com.amap.api.maps.model.LatLng-">setTargetPoint(LatLng)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/NaviPara.html" title="com.amap.api.maps.model中的类">NaviPara</a></dt>
<dd>
<div class="block">设置导航目的地坐标。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/MapsInitializer.html#setTerrainEnable-boolean-">setTerrainEnable(boolean)</a></span> - 类 中的静态方法com.amap.api.maps.<a href="../com/amap/api/maps/MapsInitializer.html" title="com.amap.api.maps中的类">MapsInitializer</a></dt>
<dd>
<div class="block">是否打开地形图, 默认为关闭
 打开地形图之后，底图会变成3D模式，添加的点线面等覆盖物也会自动带有高程
 
 注意：需要在MapView创建之前调用</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Text.html#setText-java.lang.String-">setText(String)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Text.html" title="com.amap.api.maps.model中的类">Text</a></dt>
<dd>
<div class="block">设置文字覆盖物的的文字内容</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MVTTileOverlayOptions.html#setTileProvider-com.amap.api.maps.model.TileProvider-">setTileProvider(TileProvider)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MVTTileOverlayOptions.html" title="com.amap.api.maps.model中的类">MVTTileOverlayOptions</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/UiSettings.html#setTiltGesturesEnabled-boolean-">setTiltGesturesEnabled(boolean)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/UiSettings.html" title="com.amap.api.maps中的类">UiSettings</a></dt>
<dd>
<div class="block">设置倾斜手势是否可用。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/trace/TraceLocation.html#setTime-long-">setTime(long)</a></span> - 类 中的方法com.amap.api.trace.<a href="../com/amap/api/trace/TraceLocation.html" title="com.amap.api.trace中的类">TraceLocation</a></dt>
<dd>
<div class="block">设置定位时间</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/BasePointOverlay.html#setTitle-java.lang.String-">setTitle(String)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/BasePointOverlay.html" title="com.amap.api.maps.model中的类">BasePointOverlay</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Marker.html#setTitle-java.lang.String-">setTitle(String)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Marker.html" title="com.amap.api.maps.model中的类">Marker</a></dt>
<dd>
<div class="block">设置Marker 覆盖物的标题。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MultiPointItem.html#setTitle-java.lang.String-">setTitle(String)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MultiPointItem.html" title="com.amap.api.maps.model中的类">MultiPointItem</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/NavigateArrow.html#setTopColor-int-">setTopColor(int)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/NavigateArrow.html" title="com.amap.api.maps.model中的类">NavigateArrow</a></dt>
<dd>
<div class="block">设置箭头(NavigateArrow)覆盖物的顶部颜色</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/utils/overlay/MovingPointOverlay.html#setTotalDuration-int-">setTotalDuration(int)</a></span> - 类 中的方法com.amap.api.maps.utils.overlay.<a href="../com/amap/api/maps/utils/overlay/MovingPointOverlay.html" title="com.amap.api.maps.utils.overlay中的类">MovingPointOverlay</a></dt>
<dd>
<div class="block">设置平滑移动的总时间</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/utils/overlay/SmoothMoveMarker.html#setTotalDuration-int-">setTotalDuration(int)</a></span> - 类 中的方法com.amap.api.maps.utils.overlay.<a href="../com/amap/api/maps/utils/overlay/SmoothMoveMarker.html" title="com.amap.api.maps.utils.overlay中的类">SmoothMoveMarker</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">设置平滑移动的总时间</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Marker.html#setToTop--">setToTop()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Marker.html" title="com.amap.api.maps.model中的类">Marker</a></dt>
<dd>
<div class="block">设置当前marker在最上面。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#setTouchPoiEnable-boolean-">setTouchPoiEnable(boolean)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block">设置地图POI是否允许点击。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/trace/TraceOverlay.html#setTraceStatus-int-">setTraceStatus(int)</a></span> - 类 中的方法com.amap.api.trace.<a href="../com/amap/api/trace/TraceOverlay.html" title="com.amap.api.trace中的类">TraceOverlay</a></dt>
<dd>
<div class="block">设置轨迹绘制状态</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#setTrafficEnabled-boolean-">setTrafficEnabled(boolean)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block">设置是否打开交通路况图层。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/RoutePara.html#setTransitRouteStyle-int-">setTransitRouteStyle(int)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/RoutePara.html" title="com.amap.api.maps.model中的类">RoutePara</a></dt>
<dd>
<div class="block">设置公交路径规划的规划模式。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/GroundOverlay.html#setTransparency-float-">setTransparency(float)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/GroundOverlay.html" title="com.amap.api.maps.model中的类">GroundOverlay</a></dt>
<dd>
<div class="block">设置ground 覆盖物的透明度，默认为0，不透明。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Polyline.html#setTransparency-float-">setTransparency(float)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Polyline.html" title="com.amap.api.maps.model中的类">Polyline</a></dt>
<dd>
<div class="block">设置线段的透明度，需要使用纹理，此方法才有效，如果只设置颜色透明度，使用color即可</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Text.html#setTypeface-android.graphics.Typeface-">setTypeface(Typeface)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Text.html" title="com.amap.api.maps.model中的类">Text</a></dt>
<dd>
<div class="block">设置文字覆盖物的字体样式</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/OfflineMapCity.html#setUrl-java.lang.String-">setUrl(String)</a></span> - 类 中的方法com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/OfflineMapCity.html" title="com.amap.api.maps.offlinemap中的类">OfflineMapCity</a></dt>
<dd>
<div class="block">设置下载城市的数据地址。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/OfflineMapProvince.html#setUrl-java.lang.String-">setUrl(String)</a></span> - 类 中的方法com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/OfflineMapProvince.html" title="com.amap.api.maps.offlinemap中的类">OfflineMapProvince</a></dt>
<dd>
<div class="block">设置省份下载数据地图</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/MapFragment.html#setUserVisibleHint-boolean-">setUserVisibleHint(boolean)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/MapFragment.html" title="com.amap.api.maps中的类">MapFragment</a></dt>
<dd>
<div class="block">设置是否显示，在fragment切换的时候可以使用，或者想隐藏MapFragment的时候可以使用</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/SupportMapFragment.html#setUserVisibleHint-boolean-">setUserVisibleHint(boolean)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/SupportMapFragment.html" title="com.amap.api.maps中的类">SupportMapFragment</a></dt>
<dd>
<div class="block">设置是否显示，在fragment切换的时候可以使用，或者想隐藏MapFragment的时候可以使用</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/PolylineOptions.html#setUseTexture-boolean-">setUseTexture(boolean)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/PolylineOptions.html" title="com.amap.api.maps.model中的类">PolylineOptions</a></dt>
<dd>
<div class="block">设置是否使用纹理贴图画线。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/particle/ParticleOverLifeModule.html#setVelocityOverLife-com.amap.api.maps.model.particle.VelocityGenerate-">setVelocityOverLife(VelocityGenerate)</a></span> - 类 中的方法com.amap.api.maps.model.particle.<a href="../com/amap/api/maps/model/particle/ParticleOverLifeModule.html" title="com.amap.api.maps.model.particle中的类">ParticleOverLifeModule</a></dt>
<dd>
<div class="block">速度变化，生命之前内的速度为 开始速度 + 速度变化
 可以通过速度变化让粒子移动的距离更远或者更近</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/OfflineMapCity.html#setVersion-java.lang.String-">setVersion(String)</a></span> - 类 中的方法com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/OfflineMapCity.html" title="com.amap.api.maps.offlinemap中的类">OfflineMapCity</a></dt>
<dd>
<div class="block">设置下载城市的数据版本。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/OfflineMapProvince.html#setVersion-java.lang.String-">setVersion(String)</a></span> - 类 中的方法com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/OfflineMapProvince.html" title="com.amap.api.maps.offlinemap中的类">OfflineMapProvince</a></dt>
<dd>
<div class="block">设置省份版本号</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/WearMapView.html#setVisibility-int-">setVisibility(int)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/WearMapView.html" title="com.amap.api.maps中的类">WearMapView</a></dt>
<dd>
<div class="block">设置是否显示</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Arc.html#setVisible-boolean-">setVisible(boolean)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Arc.html" title="com.amap.api.maps.model中的类">Arc</a></dt>
<dd>
<div class="block">设置弧形的可见属性。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/BasePointOverlay.html#setVisible-boolean-">setVisible(boolean)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/BasePointOverlay.html" title="com.amap.api.maps.model中的类">BasePointOverlay</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/BuildingOverlay.html#setVisible-boolean-">setVisible(boolean)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/BuildingOverlay.html" title="com.amap.api.maps.model中的类">BuildingOverlay</a></dt>
<dd>
<div class="block">设置overlay显示或隐藏</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/BuildingOverlayOptions.html#setVisible-boolean-">setVisible(boolean)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/BuildingOverlayOptions.html" title="com.amap.api.maps.model中的类">BuildingOverlayOptions</a></dt>
<dd>
<div class="block">设置overlay是否显示</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Circle.html#setVisible-boolean-">setVisible(boolean)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Circle.html" title="com.amap.api.maps.model中的类">Circle</a></dt>
<dd>
<div class="block">设置圆的可见属性。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/GroundOverlay.html#setVisible-boolean-">setVisible(boolean)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/GroundOverlay.html" title="com.amap.api.maps.model中的类">GroundOverlay</a></dt>
<dd>
<div class="block">设置ground 覆盖物是否可见。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/HeatMapGridLayer.html#setVisible-boolean-">setVisible(boolean)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/HeatMapGridLayer.html" title="com.amap.api.maps.model中的类">HeatMapGridLayer</a></dt>
<dd>
<div class="block">设置图层可见属性</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/HeatMapLayer.html#setVisible-boolean-">setVisible(boolean)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/HeatMapLayer.html" title="com.amap.api.maps.model中的类">HeatMapLayer</a></dt>
<dd>
<div class="block">设置图层可见属性</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Marker.html#setVisible-boolean-">setVisible(boolean)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Marker.html" title="com.amap.api.maps.model中的类">Marker</a></dt>
<dd>
<div class="block">设置 Marker 覆盖物的可见属性。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MVTTileOverlay.html#setVisible-boolean-">setVisible(boolean)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MVTTileOverlay.html" title="com.amap.api.maps.model中的类">MVTTileOverlay</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MVTTileOverlayOptions.html#setVisible-boolean-">setVisible(boolean)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MVTTileOverlayOptions.html" title="com.amap.api.maps.model中的类">MVTTileOverlayOptions</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/NavigateArrow.html#setVisible-boolean-">setVisible(boolean)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/NavigateArrow.html" title="com.amap.api.maps.model中的类">NavigateArrow</a></dt>
<dd>
<div class="block">设置箭头(NavigateArrow)覆盖物的是否可见。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/particle/ParticleOverlay.html#setVisible-boolean-">setVisible(boolean)</a></span> - 类 中的方法com.amap.api.maps.model.particle.<a href="../com/amap/api/maps/model/particle/ParticleOverlay.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlay</a></dt>
<dd>
<div class="block">设置 覆盖物的可见属性。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/particle/ParticleOverlayOptions.html#setVisible-boolean-">setVisible(boolean)</a></span> - 类 中的方法com.amap.api.maps.model.particle.<a href="../com/amap/api/maps/model/particle/ParticleOverlayOptions.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlayOptions</a></dt>
<dd>
<div class="block">设置 覆盖物的可见属性。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Polygon.html#setVisible-boolean-">setVisible(boolean)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Polygon.html" title="com.amap.api.maps.model中的类">Polygon</a></dt>
<dd>
<div class="block">设置多边形的可见属性。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Polyline.html#setVisible-boolean-">setVisible(boolean)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Polyline.html" title="com.amap.api.maps.model中的类">Polyline</a></dt>
<dd>
<div class="block">设置线段的可见属性。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Text.html#setVisible-boolean-">setVisible(boolean)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Text.html" title="com.amap.api.maps.model中的类">Text</a></dt>
<dd>
<div class="block">设置文字覆盖物的的可见属性。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/TileOverlay.html#setVisible-boolean-">setVisible(boolean)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/TileOverlay.html" title="com.amap.api.maps.model中的类">TileOverlay</a></dt>
<dd>
<div class="block">设置瓦片图层可见属性</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/utils/overlay/MovingPointOverlay.html#setVisible-boolean-">setVisible(boolean)</a></span> - 类 中的方法com.amap.api.maps.utils.overlay.<a href="../com/amap/api/maps/utils/overlay/MovingPointOverlay.html" title="com.amap.api.maps.utils.overlay中的类">MovingPointOverlay</a></dt>
<dd>
<div class="block">设置 Marker 是否可见</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/utils/overlay/SmoothMoveMarker.html#setVisible-boolean-">setVisible(boolean)</a></span> - 类 中的方法com.amap.api.maps.utils.overlay.<a href="../com/amap/api/maps/utils/overlay/SmoothMoveMarker.html" title="com.amap.api.maps.utils.overlay中的类">SmoothMoveMarker</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">设置 Marker 是否可见</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/trace/TraceOverlay.html#setWaitTime-int-">setWaitTime(int)</a></span> - 类 中的方法com.amap.api.trace.<a href="../com/amap/api/trace/TraceOverlay.html" title="com.amap.api.trace中的类">TraceOverlay</a></dt>
<dd>
<div class="block">设置线路停车时间</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/NavigateArrow.html#setWidth-float-">setWidth(float)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/NavigateArrow.html" title="com.amap.api.maps.model中的类">NavigateArrow</a></dt>
<dd>
<div class="block">设置箭头(NavigateArrow)覆盖物的宽度</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Polyline.html#setWidth-float-">setWidth(float)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Polyline.html" title="com.amap.api.maps.model中的类">Polyline</a></dt>
<dd>
<div class="block">设置线段的宽度</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Arc.html#setZIndex-float-">setZIndex(float)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Arc.html" title="com.amap.api.maps.model中的类">Arc</a></dt>
<dd>
<div class="block">设置弧形Z轴数值。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/BuildingOverlay.html#setZIndex-float-">setZIndex(float)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/BuildingOverlay.html" title="com.amap.api.maps.model中的类">BuildingOverlay</a></dt>
<dd>
<div class="block">设置overlay的zindex值</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/BuildingOverlayOptions.html#setZIndex-float-">setZIndex(float)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/BuildingOverlayOptions.html" title="com.amap.api.maps.model中的类">BuildingOverlayOptions</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span>
<div class="block"><span class="deprecationComment">该设置废弃，请使用<a href="../com/amap/api/maps/model/BuildingOverlay.html#setZIndex-float-"><code>BuildingOverlay.setZIndex(float)</code></a></span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Circle.html#setZIndex-float-">setZIndex(float)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Circle.html" title="com.amap.api.maps.model中的类">Circle</a></dt>
<dd>
<div class="block">设置圆的Z轴数值。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/GroundOverlay.html#setZIndex-float-">setZIndex(float)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/GroundOverlay.html" title="com.amap.api.maps.model中的类">GroundOverlay</a></dt>
<dd>
<div class="block">设置ground 覆盖物的z轴指数。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/HeatMapGridLayer.html#setZIndex-float-">setZIndex(float)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/HeatMapGridLayer.html" title="com.amap.api.maps.model中的类">HeatMapGridLayer</a></dt>
<dd>
<div class="block">设置图层的Z轴值</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/HeatMapLayer.html#setZIndex-float-">setZIndex(float)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/HeatMapLayer.html" title="com.amap.api.maps.model中的类">HeatMapLayer</a></dt>
<dd>
<div class="block">设置图层的Z轴值</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Marker.html#setZIndex-float-">setZIndex(float)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Marker.html" title="com.amap.api.maps.model中的类">Marker</a></dt>
<dd>
<div class="block">设置Marker覆盖物的z轴值。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MVTTileOverlay.html#setZIndex-float-">setZIndex(float)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MVTTileOverlay.html" title="com.amap.api.maps.model中的类">MVTTileOverlay</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MVTTileOverlayOptions.html#setZIndex-float-">setZIndex(float)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MVTTileOverlayOptions.html" title="com.amap.api.maps.model中的类">MVTTileOverlayOptions</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/NavigateArrow.html#setZIndex-float-">setZIndex(float)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/NavigateArrow.html" title="com.amap.api.maps.model中的类">NavigateArrow</a></dt>
<dd>
<div class="block">设置箭头(NavigateArrow)覆盖物Z轴的值。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Polygon.html#setZIndex-float-">setZIndex(float)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Polygon.html" title="com.amap.api.maps.model中的类">Polygon</a></dt>
<dd>
<div class="block">设置多边形的Z轴数值。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Polyline.html#setZIndex-float-">setZIndex(float)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Polyline.html" title="com.amap.api.maps.model中的类">Polyline</a></dt>
<dd>
<div class="block">设置线段的z轴值。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Text.html#setZIndex-float-">setZIndex(float)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Text.html" title="com.amap.api.maps.model中的类">Text</a></dt>
<dd>
<div class="block">设置文字覆盖物 z轴数值。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/TileOverlay.html#setZIndex-float-">setZIndex(float)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/TileOverlay.html" title="com.amap.api.maps.model中的类">TileOverlay</a></dt>
<dd>
<div class="block">设置瓦片图层的Z轴值</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/UiSettings.html#setZoomControlsEnabled-boolean-">setZoomControlsEnabled(boolean)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/UiSettings.html" title="com.amap.api.maps中的类">UiSettings</a></dt>
<dd>
<div class="block">设置缩放按钮是否可见。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/UiSettings.html#setZoomGesturesEnabled-boolean-">setZoomGesturesEnabled(boolean)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/UiSettings.html" title="com.amap.api.maps中的类">UiSettings</a></dt>
<dd>
<div class="block">设置双指缩放手势是否可用。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/UiSettings.html#setZoomPosition-int-">setZoomPosition(int)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/UiSettings.html" title="com.amap.api.maps中的类">UiSettings</a></dt>
<dd>
<div class="block">设置缩放按钮的位置。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/GLTFOverlayOptions.html#setZoomRange-float-float-">setZoomRange(float, float)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/GLTFOverlayOptions.html" title="com.amap.api.maps.model中的类">GLTFOverlayOptions</a></dt>
<dd>
<div class="block">设置缩放范围</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#showBuildings-boolean-">showBuildings(boolean)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block">设置是否显示3D建筑物，默认显示。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#showHideBuildings-int-">showHideBuildings(int)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block">显示隐藏的建筑物</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#showIndoorMap-boolean-">showIndoorMap(boolean)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block">设置是否显示室内地图，默认不显示。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/BasePointOverlay.html#showInfoWindow--">showInfoWindow()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/BasePointOverlay.html" title="com.amap.api.maps.model中的类">BasePointOverlay</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Marker.html#showInfoWindow--">showInfoWindow()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Marker.html" title="com.amap.api.maps.model中的类">Marker</a></dt>
<dd>
<div class="block">显示 Marker 覆盖物的信息窗口。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#showMapText-boolean-">showMapText(boolean)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block">设置是否显示底图文字标注，默认显示。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MyLocationStyle.html#showMyLocation-boolean-">showMyLocation(boolean)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MyLocationStyle.html" title="com.amap.api.maps.model中的类">MyLocationStyle</a></dt>
<dd>
<div class="block">设置是否显示定位小蓝点，true 显示，false不显示。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/OfflineMapActivity.html#showScr--">showScr()</a></span> - 类 中的方法com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/OfflineMapActivity.html" title="com.amap.api.maps.offlinemap中的类">OfflineMapActivity</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/NavigateArrowOptions.html#sideColor-int-">sideColor(int)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/NavigateArrowOptions.html" title="com.amap.api.maps.model中的类">NavigateArrowOptions</a></dt>
<dd>
<div class="block">设置箭头箭头(NavigateArrow)覆盖物的侧边颜色，需要传入32位的ARGB格式。</div>
</dd>
<dt><a href="../com/amap/api/maps/model/particle/SinglePointParticleShape.html" title="com.amap.api.maps.model.particle中的类"><span class="typeNameLink">SinglePointParticleShape</span></a> - <a href="../com/amap/api/maps/model/particle/package-summary.html">com.amap.api.maps.model.particle</a>中的类</dt>
<dd>
<div class="block">指定位置发射</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/particle/SinglePointParticleShape.html#SinglePointParticleShape-float-float-float-boolean-">SinglePointParticleShape(float, float, float, boolean)</a></span> - 类 的构造器com.amap.api.maps.model.particle.<a href="../com/amap/api/maps/model/particle/SinglePointParticleShape.html" title="com.amap.api.maps.model.particle中的类">SinglePointParticleShape</a></dt>
<dd>
<div class="block">是否已比例的形式设置发射区域，此时z建议传递0 比如想要保持位置在中心点，可以使用（0.5,0.5,0, true）</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/particle/SinglePointParticleShape.html#SinglePointParticleShape-float-float-float-">SinglePointParticleShape(float, float, float)</a></span> - 类 的构造器com.amap.api.maps.model.particle.<a href="../com/amap/api/maps/model/particle/SinglePointParticleShape.html" title="com.amap.api.maps.model.particle中的类">SinglePointParticleShape</a></dt>
<dd>
<div class="block">是否已比例的形式设置发射区域，此时z建议传递0 比如想要保持位置在中心点，可以使用（0.5,0.5,0, true）</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/HeatMapLayerOptions.html#size-float-">size(float)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/HeatMapLayerOptions.html" title="com.amap.api.maps.model中的类">HeatMapLayerOptions</a></dt>
<dd>
<div class="block">设置热力图点大小，单位为米，默认为2000米

    —— ——    —— ——
  丨     丨 丨     丨
  丨     丨 丨     丨
    —— ——    —— ——

  每个方框的宽就是 size（六边形同理）
  两个放款之间的间隔就是 gap （六边形同理）</div>
</dd>
<dt><a href="../com/amap/api/maps/model/particle/SizeOverLife.html" title="com.amap.api.maps.model.particle中的类"><span class="typeNameLink">SizeOverLife</span></a> - <a href="../com/amap/api/maps/model/particle/package-summary.html">com.amap.api.maps.model.particle</a>中的类</dt>
<dd>
<div class="block">缩放比例变化控制基类，具体使用时需要使用它的子类</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/particle/SizeOverLife.html#SizeOverLife--">SizeOverLife()</a></span> - 类 的构造器com.amap.api.maps.model.particle.<a href="../com/amap/api/maps/model/particle/SizeOverLife.html" title="com.amap.api.maps.model.particle中的类">SizeOverLife</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/amap/api/maps/utils/overlay/SmoothMoveMarker.html" title="com.amap.api.maps.utils.overlay中的类"><span class="typeNameLink">SmoothMoveMarker</span></a> - <a href="../com/amap/api/maps/utils/overlay/package-summary.html">com.amap.api.maps.utils.overlay</a>中的类</dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span>
<div class="block"><span class="deprecationComment">建议使用 <a href="../com/amap/api/maps/utils/overlay/MovingPointOverlay.html" title="com.amap.api.maps.utils.overlay中的类"><code>MovingPointOverlay</code></a> 替换</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/utils/overlay/SmoothMoveMarker.html#SmoothMoveMarker-com.amap.api.maps.AMap-">SmoothMoveMarker(AMap)</a></span> - 类 的构造器com.amap.api.maps.utils.overlay.<a href="../com/amap/api/maps/utils/overlay/SmoothMoveMarker.html" title="com.amap.api.maps.utils.overlay中的类">SmoothMoveMarker</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">根据给定的参数来构造SmoothMoveMarker对象。</div>
</dd>
<dt><a href="../com/amap/api/maps/utils/overlay/SmoothMoveMarker.MoveListener.html" title="com.amap.api.maps.utils.overlay中的接口"><span class="typeNameLink">SmoothMoveMarker.MoveListener</span></a> - <a href="../com/amap/api/maps/utils/overlay/package-summary.html">com.amap.api.maps.utils.overlay</a>中的接口</dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">平滑移动时返回剩余距离接口</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MarkerOptions.html#snippet-java.lang.String-">snippet(String)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MarkerOptions.html" title="com.amap.api.maps.model中的类">MarkerOptions</a></dt>
<dd>
<div class="block">设置 Marker覆盖物的 文字描述</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/LatLngBounds.html#southwest">southwest</a></span> - 类 中的变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/LatLngBounds.html" title="com.amap.api.maps.model中的类">LatLngBounds</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/amap/api/maps/utils/SpatialRelationUtil.html" title="com.amap.api.maps.utils中的类"><span class="typeNameLink">SpatialRelationUtil</span></a> - <a href="../com/amap/api/maps/utils/package-summary.html">com.amap.api.maps.utils</a>中的类</dt>
<dd>
<div class="block">计算点到线最短距离工具类</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/OfflineMapStatus.html#START_DOWNLOAD_FAILD">START_DOWNLOAD_FAILD</a></span> - 类 中的静态变量com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/OfflineMapStatus.html" title="com.amap.api.maps.offlinemap中的类">OfflineMapStatus</a></dt>
<dd>
<div class="block">开始下载失败，已下载该城市地图</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/BasePointOverlay.html#startAnimation--">startAnimation()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/BasePointOverlay.html" title="com.amap.api.maps.model中的类">BasePointOverlay</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Marker.html#startAnimation--">startAnimation()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Marker.html" title="com.amap.api.maps.model中的类">Marker</a></dt>
<dd>
<div class="block">开始动画</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/utils/overlay/MovingPointOverlay.html#startSmoothMove--">startSmoothMove()</a></span> - 类 中的方法com.amap.api.maps.utils.overlay.<a href="../com/amap/api/maps/utils/overlay/MovingPointOverlay.html" title="com.amap.api.maps.utils.overlay中的类">MovingPointOverlay</a></dt>
<dd>
<div class="block">开始平滑移动</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/utils/overlay/SmoothMoveMarker.html#startSmoothMove--">startSmoothMove()</a></span> - 类 中的方法com.amap.api.maps.utils.overlay.<a href="../com/amap/api/maps/utils/overlay/SmoothMoveMarker.html" title="com.amap.api.maps.utils.overlay中的类">SmoothMoveMarker</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">开始平滑移动</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/trace/LBSTraceClient.html#startTrace-com.amap.api.trace.TraceStatusListener-">startTrace(TraceStatusListener)</a></span> - 类 中的方法com.amap.api.trace.<a href="../com/amap/api/trace/LBSTraceClient.html" title="com.amap.api.trace中的类">LBSTraceClient</a></dt>
<dd>
<div class="block">开始记录轨迹，定位间隔2s，每隔5个点合并请求一次纠偏并回调。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/OfflineMapManager.html#stop--">stop()</a></span> - 类 中的方法com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/OfflineMapManager.html" title="com.amap.api.maps.offlinemap中的类">OfflineMapManager</a></dt>
<dd>
<div class="block">停止离线地图下载。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/OfflineMapStatus.html#STOP">STOP</a></span> - 类 中的静态变量com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/OfflineMapStatus.html" title="com.amap.api.maps.offlinemap中的类">OfflineMapStatus</a></dt>
<dd>
<div class="block">停止下载。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#stopAnimation--">stopAnimation()</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block">停止当前执行的改变地图状态的动画。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/utils/overlay/MovingPointOverlay.html#stopMove--">stopMove()</a></span> - 类 中的方法com.amap.api.maps.utils.overlay.<a href="../com/amap/api/maps/utils/overlay/MovingPointOverlay.html" title="com.amap.api.maps.utils.overlay中的类">MovingPointOverlay</a></dt>
<dd>
<div class="block">停止平滑移动</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/utils/overlay/SmoothMoveMarker.html#stopMove--">stopMove()</a></span> - 类 中的方法com.amap.api.maps.utils.overlay.<a href="../com/amap/api/maps/utils/overlay/SmoothMoveMarker.html" title="com.amap.api.maps.utils.overlay中的类">SmoothMoveMarker</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">停止平滑移动</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/trace/LBSTraceClient.html#stopTrace--">stopTrace()</a></span> - 类 中的方法com.amap.api.trace.<a href="../com/amap/api/trace/LBSTraceClient.html" title="com.amap.api.trace中的类">LBSTraceClient</a></dt>
<dd>
<div class="block">停止记录轨迹</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/ArcOptions.html#strokeColor-int-">strokeColor(int)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/ArcOptions.html" title="com.amap.api.maps.model中的类">ArcOptions</a></dt>
<dd>
<div class="block">设置弧形边框颜色参数，ARGB格式。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/CircleOptions.html#strokeColor-int-">strokeColor(int)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/CircleOptions.html" title="com.amap.api.maps.model中的类">CircleOptions</a></dt>
<dd>
<div class="block">设置圆的边框颜色，ARGB格式。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MyLocationStyle.html#strokeColor-int-">strokeColor(int)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MyLocationStyle.html" title="com.amap.api.maps.model中的类">MyLocationStyle</a></dt>
<dd>
<div class="block">设置圆形区域（以定位位置为圆心，定位半径的圆形区域）的边框颜色。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/PolygonOptions.html#strokeColor-int-">strokeColor(int)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/PolygonOptions.html" title="com.amap.api.maps.model中的类">PolygonOptions</a></dt>
<dd>
<div class="block">设置多边形的边框颜色，32位 ARGB格式，默认为黑色。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/ArcOptions.html#strokeWidth-float-">strokeWidth(float)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/ArcOptions.html" title="com.amap.api.maps.model中的类">ArcOptions</a></dt>
<dd>
<div class="block">设置弧形边框宽度参数，单位：像素。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/CircleOptions.html#strokeWidth-float-">strokeWidth(float)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/CircleOptions.html" title="com.amap.api.maps.model中的类">CircleOptions</a></dt>
<dd>
<div class="block">设置圆的边框宽度，单位像素。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MyLocationStyle.html#strokeWidth-float-">strokeWidth(float)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MyLocationStyle.html" title="com.amap.api.maps.model中的类">MyLocationStyle</a></dt>
<dd>
<div class="block">设置圆形区域（以定位位置为圆心，定位半径的圆形区域）的边框宽度。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/PolygonOptions.html#strokeWidth-float-">strokeWidth(float)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/PolygonOptions.html" title="com.amap.api.maps.model中的类">PolygonOptions</a></dt>
<dd>
<div class="block">设置多边形的边框宽度，单位：像素。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/OfflineMapStatus.html#SUCCESS">SUCCESS</a></span> - 类 中的静态变量com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/OfflineMapStatus.html" title="com.amap.api.maps.offlinemap中的类">OfflineMapStatus</a></dt>
<dd>
<div class="block">下载成功。</div>
</dd>
<dt><a href="../com/amap/api/maps/SupportMapFragment.html" title="com.amap.api.maps中的类"><span class="typeNameLink">SupportMapFragment</span></a> - <a href="../com/amap/api/maps/package-summary.html">com.amap.api.maps</a>中的类</dt>
<dd>
<div class="block">SupportMapFragment 类, 管理地图生命周期。</div>
</dd>
<dt><a href="../com/amap/api/maps/SwipeDismissTouchListener.DismissCallbacks.html" title="com.amap.api.maps中的接口"><span class="typeNameLink">SwipeDismissTouchListener.DismissCallbacks</span></a> - <a href="../com/amap/api/maps/package-summary.html">com.amap.api.maps</a>中的接口</dt>
<dd>
<div class="block">The callback interface used by <a href="../com/amap/api/maps/SwipeDismissTouchListener.html" title="com.amap.api.maps中的类"><code>SwipeDismissTouchListener</code></a> to
 inform its client about a successful dismissal of the view for which it
 was created.</div>
</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">K</a>&nbsp;<a href="index-11.html">L</a>&nbsp;<a href="index-12.html">M</a>&nbsp;<a href="index-13.html">N</a>&nbsp;<a href="index-14.html">O</a>&nbsp;<a href="index-15.html">P</a>&nbsp;<a href="index-16.html">Q</a>&nbsp;<a href="index-17.html">R</a>&nbsp;<a href="index-18.html">S</a>&nbsp;<a href="index-19.html">T</a>&nbsp;<a href="index-20.html">U</a>&nbsp;<a href="index-21.html">V</a>&nbsp;<a href="index-22.html">W</a>&nbsp;<a href="index-23.html">Z</a>&nbsp;</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-17.html">上一个字母</a></li>
<li><a href="index-19.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-18.html" target="_top">框架</a></li>
<li><a href="index-18.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
