<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>C - 索引</title>
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="C - \u7D22\u5F15";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-2.html">上一个字母</a></li>
<li><a href="index-4.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-3.html" target="_top">框架</a></li>
<li><a href="index-3.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="contentContainer"><a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">K</a>&nbsp;<a href="index-11.html">L</a>&nbsp;<a href="index-12.html">M</a>&nbsp;<a href="index-13.html">N</a>&nbsp;<a href="index-14.html">O</a>&nbsp;<a href="index-15.html">P</a>&nbsp;<a href="index-16.html">Q</a>&nbsp;<a href="index-17.html">R</a>&nbsp;<a href="index-18.html">S</a>&nbsp;<a href="index-19.html">T</a>&nbsp;<a href="index-20.html">U</a>&nbsp;<a href="index-21.html">V</a>&nbsp;<a href="index-22.html">W</a>&nbsp;<a href="index-23.html">Z</a>&nbsp;<a name="I:C">
<!--   -->
</a>
<h2 class="title">C</h2>
<dl>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMapUtils.html#calculateArea-com.amap.api.maps.model.LatLng-com.amap.api.maps.model.LatLng-">calculateArea(LatLng, LatLng)</a></span> - 类 中的静态方法com.amap.api.maps.<a href="../com/amap/api/maps/AMapUtils.html" title="com.amap.api.maps中的类">AMapUtils</a></dt>
<dd>
<div class="block">计算地图上矩形区域的面积，单位平方米。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMapUtils.html#calculateArea-java.util.List-">calculateArea(List&lt;LatLng&gt;)</a></span> - 类 中的静态方法com.amap.api.maps.<a href="../com/amap/api/maps/AMapUtils.html" title="com.amap.api.maps中的类">AMapUtils</a></dt>
<dd>
<div class="block">计算多边形的面积，单位平方米。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMapUtils.html#calculateLineDistance-com.amap.api.maps.model.LatLng-com.amap.api.maps.model.LatLng-">calculateLineDistance(LatLng, LatLng)</a></span> - 类 中的静态方法com.amap.api.maps.<a href="../com/amap/api/maps/AMapUtils.html" title="com.amap.api.maps中的类">AMapUtils</a></dt>
<dd>
<div class="block">根据用户的起点和终点经纬度计算两点间距离，单位米。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/TileOverlay.html#callNativeFunction-java.lang.String-java.lang.Object:A-">callNativeFunction(String, Object[])</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/TileOverlay.html" title="com.amap.api.maps.model中的类">TileOverlay</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/utils/SpatialRelationUtil.html#calShortestDistancePoint-java.util.List-com.amap.api.maps.model.LatLng-float-double-">calShortestDistancePoint(List&lt;LatLng&gt;, LatLng, float, double)</a></span> - 类 中的静态方法com.amap.api.maps.utils.<a href="../com/amap/api/maps/utils/SpatialRelationUtil.html" title="com.amap.api.maps.utils中的类">SpatialRelationUtil</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/utils/SpatialRelationUtil.html#calShortestDistancePoint-java.util.List-com.amap.api.maps.model.LatLng-">calShortestDistancePoint(List&lt;LatLng&gt;, LatLng)</a></span> - 类 中的静态方法com.amap.api.maps.utils.<a href="../com/amap/api/maps/utils/SpatialRelationUtil.html" title="com.amap.api.maps.utils中的类">SpatialRelationUtil</a></dt>
<dd>
<div class="block">计算一个点在线上的垂足，如果垂足在线上的某一顶点，则直接返回顶点的下标</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/utils/SpatialRelationUtil.html#calShortestDistancePoint-java.util.List-DPoint-">calShortestDistancePoint(List&lt;DPoint&gt;, DPoint)</a></span> - 类 中的静态方法com.amap.api.maps.utils.<a href="../com/amap/api/maps/utils/SpatialRelationUtil.html" title="com.amap.api.maps.utils中的类">SpatialRelationUtil</a></dt>
<dd>
<div class="block">计算一个点在线上的垂足，如果垂足在线上的某一顶点，则直接返回顶点的下标</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/utils/SpatialRelationUtil.html#calShortestDistancePoint-java.util.List-DPoint-float-">calShortestDistancePoint(List&lt;DPoint&gt;, DPoint, float)</a></span> - 类 中的静态方法com.amap.api.maps.utils.<a href="../com/amap/api/maps/utils/SpatialRelationUtil.html" title="com.amap.api.maps.utils中的类">SpatialRelationUtil</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMapOptions.html#camera-com.amap.api.maps.model.CameraPosition-">camera(CameraPosition)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMapOptions.html" title="com.amap.api.maps中的类">AMapOptions</a></dt>
<dd>
<div class="block">设置地图初始化时的地图状态， 默认地图中心点为北京天安门，缩放级别为 10.0f。</div>
</dd>
<dt><a href="../com/amap/api/maps/model/CameraPosition.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">CameraPosition</span></a> - <a href="../com/amap/api/maps/model/package-summary.html">com.amap.api.maps.model</a>中的类</dt>
<dd>
<div class="block">相机位置，这个类包含了所有的可视区域的位置参数。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/CameraPosition.html#CameraPosition-com.amap.api.maps.model.LatLng-float-float-float-">CameraPosition(LatLng, float, float, float)</a></span> - 类 的构造器com.amap.api.maps.model.<a href="../com/amap/api/maps/model/CameraPosition.html" title="com.amap.api.maps.model中的类">CameraPosition</a></dt>
<dd>
<div class="block">构造一个CameraPosition 对象。</div>
</dd>
<dt><a href="../com/amap/api/maps/model/CameraPosition.Builder.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">CameraPosition.Builder</span></a> - <a href="../com/amap/api/maps/model/package-summary.html">com.amap.api.maps.model</a>中的类</dt>
<dd>
<div class="block">创建一个摄像机的位置。</div>
</dd>
<dt><a href="../com/amap/api/maps/CameraUpdate.html" title="com.amap.api.maps中的类"><span class="typeNameLink">CameraUpdate</span></a> - <a href="../com/amap/api/maps/package-summary.html">com.amap.api.maps</a>中的类</dt>
<dd>
<div class="block">描述地图状态将要发生的变化。</div>
</dd>
<dt><a href="../com/amap/api/maps/CameraUpdateFactory.html" title="com.amap.api.maps中的类"><span class="typeNameLink">CameraUpdateFactory</span></a> - <a href="../com/amap/api/maps/package-summary.html">com.amap.api.maps</a>中的类</dt>
<dd>
<div class="block">创建CameraUpdate 对象,用来改变地图状态。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/CameraUpdateFactory.html#CameraUpdateFactory--">CameraUpdateFactory()</a></span> - 类 的构造器com.amap.api.maps.<a href="../com/amap/api/maps/CameraUpdateFactory.html" title="com.amap.api.maps中的类">CameraUpdateFactory</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/CircleHoleOptions.html#center-com.amap.api.maps.model.LatLng-">center(LatLng)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/CircleHoleOptions.html" title="com.amap.api.maps.model中的类">CircleHoleOptions</a></dt>
<dd>
<div class="block">设置洞圆心经纬度坐标。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/CircleOptions.html#center-com.amap.api.maps.model.LatLng-">center(LatLng)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/CircleOptions.html" title="com.amap.api.maps.model中的类">CircleOptions</a></dt>
<dd>
<div class="block">设置圆心经纬度坐标。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/CameraUpdateFactory.html#changeBearing-float-">changeBearing(float)</a></span> - 类 中的静态方法com.amap.api.maps.<a href="../com/amap/api/maps/CameraUpdateFactory.html" title="com.amap.api.maps中的类">CameraUpdateFactory</a></dt>
<dd>
<div class="block">设置地图的旋转角度。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/CameraUpdateFactory.html#changeLatLng-com.amap.api.maps.model.LatLng-">changeLatLng(LatLng)</a></span> - 类 中的静态方法com.amap.api.maps.<a href="../com/amap/api/maps/CameraUpdateFactory.html" title="com.amap.api.maps中的类">CameraUpdateFactory</a></dt>
<dd>
<div class="block">设置地图的中心点。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/CameraUpdateFactory.html#changeTilt-float-">changeTilt(float)</a></span> - 类 中的静态方法com.amap.api.maps.<a href="../com/amap/api/maps/CameraUpdateFactory.html" title="com.amap.api.maps中的类">CameraUpdateFactory</a></dt>
<dd>
<div class="block">设置地图倾斜度。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/OfflineMapStatus.html#CHECKUPDATES">CHECKUPDATES</a></span> - 类 中的静态变量com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/OfflineMapStatus.html" title="com.amap.api.maps.offlinemap中的类">OfflineMapStatus</a></dt>
<dd>
<div class="block">默认状态</div>
</dd>
<dt><a href="../com/amap/api/maps/model/Circle.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">Circle</span></a> - <a href="../com/amap/api/maps/model/package-summary.html">com.amap.api.maps.model</a>中的类</dt>
<dd>
<div class="block">定义了在地图上绘制圆的类。</div>
</dd>
<dt><a href="../com/amap/api/maps/model/CircleHoleOptions.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">CircleHoleOptions</span></a> - <a href="../com/amap/api/maps/model/package-summary.html">com.amap.api.maps.model</a>中的类</dt>
<dd>
<div class="block">创建圆洞的选项类</div>
</dd>
<dt><a href="../com/amap/api/maps/model/CircleOptions.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">CircleOptions</span></a> - <a href="../com/amap/api/maps/model/package-summary.html">com.amap.api.maps.model</a>中的类</dt>
<dd>
<div class="block">创建圆的选项类</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/CircleOptions.html#CircleOptions--">CircleOptions()</a></span> - 类 的构造器com.amap.api.maps.model.<a href="../com/amap/api/maps/model/CircleOptions.html" title="com.amap.api.maps.model中的类">CircleOptions</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/amap/api/maps/offlinemap/City.html" title="com.amap.api.maps.offlinemap中的类"><span class="typeNameLink">City</span></a> - <a href="../com/amap/api/maps/offlinemap/package-summary.html">com.amap.api.maps.offlinemap</a>中的类</dt>
<dd>
<div class="block">城市属性的相关类。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/City.html#City--">City()</a></span> - 类 的构造器com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/City.html" title="com.amap.api.maps.offlinemap中的类">City</a></dt>
<dd>
<div class="block">构造一个City对象。</div>
</dd>
<dt><a href="../com/amap/api/maps/offlinemap/CityExpandView.html" title="com.amap.api.maps.offlinemap中的类"><span class="typeNameLink">CityExpandView</span></a> - <a href="../com/amap/api/maps/offlinemap/package-summary.html">com.amap.api.maps.offlinemap</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/CityExpandView.html#CityExpandView-android.content.Context-">CityExpandView(Context)</a></span> - 类 的构造器com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/CityExpandView.html" title="com.amap.api.maps.offlinemap中的类">CityExpandView</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/CityExpandView.html#CityExpandView-android.content.Context-android.util.AttributeSet-">CityExpandView(Context, AttributeSet)</a></span> - 类 的构造器com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/CityExpandView.html" title="com.amap.api.maps.offlinemap中的类">CityExpandView</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/animation/AnimationSet.html#cleanAnimation--">cleanAnimation()</a></span> - 类 中的方法com.amap.api.maps.model.animation.<a href="../com/amap/api/maps/model/animation/AnimationSet.html" title="com.amap.api.maps.model.animation中的类">AnimationSet</a></dt>
<dd>
<div class="block">清除动画</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#clear--">clear()</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block">从地图上删除所有的overlay（marker，circle，polyline 等对象）。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#clear-boolean-">clear(boolean)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block">从地图上删除所有的覆盖物（marker，circle，polyline 等对象），但myLocationOverlay（内置定位覆盖物）除外。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/TileOverlay.html#clearTileCache--">clearTileCache()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/TileOverlay.html" title="com.amap.api.maps.model中的类">TileOverlay</a></dt>
<dd>
<div class="block">清空瓦片图层的缓存</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/GLTFOverlayOptions.html#clone--">clone()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/GLTFOverlayOptions.html" title="com.amap.api.maps.model中的类">GLTFOverlayOptions</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/OfflineMapActivity.html#closeScr--">closeScr()</a></span> - 类 中的方法com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/OfflineMapActivity.html" title="com.amap.api.maps.offlinemap中的类">OfflineMapActivity</a></dt>
<dd>
<div class="block">关闭当前页面</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/OfflineMapActivity.html#closeScr-android.os.Bundle-">closeScr(Bundle)</a></span> - 类 中的方法com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/OfflineMapActivity.html" title="com.amap.api.maps.offlinemap中的类">OfflineMapActivity</a></dt>
<dd>
<div class="block">关闭当前页面,并传递数据到前一个page</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/ImageOptions.html#color">color</a></span> - 类 中的变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/ImageOptions.html" title="com.amap.api.maps.model中的类">ImageOptions</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/PolylineOptions.html#color-int-">color(int)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/PolylineOptions.html" title="com.amap.api.maps.model中的类">PolylineOptions</a></dt>
<dd>
<div class="block">设置线段的颜色，需要传入32位的ARGB格式。</div>
</dd>
<dt><a href="../com/amap/api/maps/model/particle/ColorGenerate.html" title="com.amap.api.maps.model.particle中的类"><span class="typeNameLink">ColorGenerate</span></a> - <a href="../com/amap/api/maps/model/particle/package-summary.html">com.amap.api.maps.model.particle</a>中的类</dt>
<dd>
<div class="block">颜色生成器基础类基类，具体使用时需要使用它的子类，颜色rgba</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/particle/ColorGenerate.html#ColorGenerate--">ColorGenerate()</a></span> - 类 的构造器com.amap.api.maps.model.particle.<a href="../com/amap/api/maps/model/particle/ColorGenerate.html" title="com.amap.api.maps.model.particle中的类">ColorGenerate</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/amap/api/maps/model/ColorLatLng.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">ColorLatLng</span></a> - <a href="../com/amap/api/maps/model/package-summary.html">com.amap.api.maps.model</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/ColorLatLng.html#ColorLatLng-java.util.List-int-">ColorLatLng(List&lt;LatLng&gt;, int)</a></span> - 类 的构造器com.amap.api.maps.model.<a href="../com/amap/api/maps/model/ColorLatLng.html" title="com.amap.api.maps.model中的类">ColorLatLng</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/PolylineOptions.html#colorValues-java.util.List-">colorValues(List&lt;Integer&gt;)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/PolylineOptions.html" title="com.amap.api.maps.model中的类">PolylineOptions</a></dt>
<dd>
<div class="block">设置线段的颜色</div>
</dd>
<dt><a href="../com/amap/api/maps/package-summary.html">com.amap.api.maps</a> - 程序包 com.amap.api.maps</dt>
<dd>
<div class="block">
地图显示包，帮助您在Android应用程序中显示高德地图，并且支持地图交互手势的功能。</div>
</dd>
<dt><a href="../com/amap/api/maps/model/package-summary.html">com.amap.api.maps.model</a> - 程序包 com.amap.api.maps.model</dt>
<dd>
<div class="block">
覆盖物包，覆盖物（叠加或覆盖到地图的内容）支持标记、折线、多边形和圆。</div>
</dd>
<dt><a href="../com/amap/api/maps/model/amap3dmodeltile/package-summary.html">com.amap.api.maps.model.amap3dmodeltile</a> - 程序包 com.amap.api.maps.model.amap3dmodeltile</dt>
<dd>&nbsp;</dd>
<dt><a href="../com/amap/api/maps/model/animation/package-summary.html">com.amap.api.maps.model.animation</a> - 程序包 com.amap.api.maps.model.animation</dt>
<dd>
<div class="block">
  动画类，可用于支持动画的覆盖物。</div>
</dd>
<dt><a href="../com/amap/api/maps/model/particle/package-summary.html">com.amap.api.maps.model.particle</a> - 程序包 com.amap.api.maps.model.particle</dt>
<dd>
<div class="block">
  粒子效果类，用于添加粒子效果覆盖物。</div>
</dd>
<dt><a href="../com/amap/api/maps/offlinemap/package-summary.html">com.amap.api.maps.offlinemap</a> - 程序包 com.amap.api.maps.offlinemap</dt>
<dd>
<div class="block">
离线地图包，用户可以通过手机WiFi下载高德3D离线地图。</div>
</dd>
<dt><a href="../com/amap/api/maps/utils/package-summary.html">com.amap.api.maps.utils</a> - 程序包 com.amap.api.maps.utils</dt>
<dd>
<div class="block">
工具类，基于地图现有接口的辅助工具。</div>
</dd>
<dt><a href="../com/amap/api/maps/utils/overlay/package-summary.html">com.amap.api.maps.utils.overlay</a> - 程序包 com.amap.api.maps.utils.overlay</dt>
<dd>
<div class="block">
工具类，基于地图现有接口实现的高级功能。</div>
</dd>
<dt><a href="../com/amap/api/trace/package-summary.html">com.amap.api.trace</a> - 程序包 com.amap.api.trace</dt>
<dd>
<div class="block">
轨迹纠偏包，提供高精度定位轨迹抓路后绘制平滑轨迹。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMapOptions.html#compassEnabled-boolean-">compassEnabled(boolean)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMapOptions.html" title="com.amap.api.maps中的类">AMapOptions</a></dt>
<dd>
<div class="block">设置指南针是否可用。</div>
</dd>
<dt><a href="../com/amap/api/maps/model/particle/ConstantRotationOverLife.html" title="com.amap.api.maps.model.particle中的类"><span class="typeNameLink">ConstantRotationOverLife</span></a> - <a href="../com/amap/api/maps/model/particle/package-summary.html">com.amap.api.maps.model.particle</a>中的类</dt>
<dd>
<div class="block">旋转角度变化控制</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/particle/ConstantRotationOverLife.html#ConstantRotationOverLife-float-">ConstantRotationOverLife(float)</a></span> - 类 的构造器com.amap.api.maps.model.particle.<a href="../com/amap/api/maps/model/particle/ConstantRotationOverLife.html" title="com.amap.api.maps.model.particle中的类">ConstantRotationOverLife</a></dt>
<dd>
<div class="block">设置每秒变化多少角度</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Circle.html#contains-com.amap.api.maps.model.LatLng-">contains(LatLng)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Circle.html" title="com.amap.api.maps.model中的类">Circle</a></dt>
<dd>
<div class="block">判断圆是否包含传入的经纬度点。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/LatLngBounds.html#contains-com.amap.api.maps.model.LatLng-">contains(LatLng)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/LatLngBounds.html" title="com.amap.api.maps.model中的类">LatLngBounds</a></dt>
<dd>
<div class="block">判断矩形区域是否包含传入的经纬度点。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/LatLngBounds.html#contains-com.amap.api.maps.model.LatLngBounds-">contains(LatLngBounds)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/LatLngBounds.html" title="com.amap.api.maps.model中的类">LatLngBounds</a></dt>
<dd>
<div class="block">判断矩形区域是否包含传入的矩形区域。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Polygon.html#contains-com.amap.api.maps.model.LatLng-">contains(LatLng)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Polygon.html" title="com.amap.api.maps.model中的类">Polygon</a></dt>
<dd>
<div class="block">判断多边形是否包含传入的经纬度点。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/ImageOptions.html#content">content</a></span> - 类 中的变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/ImageOptions.html" title="com.amap.api.maps.model中的类">ImageOptions</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/CoordinateConverter.html#convert--">convert()</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/CoordinateConverter.html" title="com.amap.api.maps中的类">CoordinateConverter</a></dt>
<dd>
<div class="block">执行坐标转换操作。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/CoordinateConverter.html#coord-com.amap.api.maps.model.LatLng-">coord(LatLng)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/CoordinateConverter.html" title="com.amap.api.maps中的类">CoordinateConverter</a></dt>
<dd>
<div class="block">设置需要转换的经纬度坐标值</div>
</dd>
<dt><a href="../com/amap/api/maps/CoordinateConverter.html" title="com.amap.api.maps中的类"><span class="typeNameLink">CoordinateConverter</span></a> - <a href="../com/amap/api/maps/package-summary.html">com.amap.api.maps</a>中的类</dt>
<dd>
<div class="block">坐标转换工具类</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/CoordinateConverter.html#CoordinateConverter-android.content.Context-">CoordinateConverter(Context)</a></span> - 类 的构造器com.amap.api.maps.<a href="../com/amap/api/maps/CoordinateConverter.html" title="com.amap.api.maps中的类">CoordinateConverter</a></dt>
<dd>
<div class="block">坐标转换工具类构造方法</div>
</dd>
<dt><a href="../com/amap/api/maps/CoordinateConverter.CoordType.html" title="com.amap.api.maps中的枚举"><span class="typeNameLink">CoordinateConverter.CoordType</span></a> - <a href="../com/amap/api/maps/package-summary.html">com.amap.api.maps</a>中的枚举</dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/trace/TraceLocation.html#copy--">copy()</a></span> - 类 中的方法com.amap.api.trace.<a href="../com/amap/api/trace/TraceLocation.html" title="com.amap.api.trace中的类">TraceLocation</a></dt>
<dd>
<div class="block">复制轨迹纠偏点</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/GLTFOverlayOptionsCreator.html#createFromParcel-android.os.Parcel-">createFromParcel(Parcel)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/GLTFOverlayOptionsCreator.html" title="com.amap.api.maps.model中的类">GLTFOverlayOptionsCreator</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMapOptions.html#CREATOR">CREATOR</a></span> - 类 中的静态变量com.amap.api.maps.<a href="../com/amap/api/maps/AMapOptions.html" title="com.amap.api.maps中的类">AMapOptions</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/ArcOptions.html#CREATOR">CREATOR</a></span> - 类 中的静态变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/ArcOptions.html" title="com.amap.api.maps.model中的类">ArcOptions</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/BitmapDescriptor.html#CREATOR">CREATOR</a></span> - 类 中的静态变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/BitmapDescriptor.html" title="com.amap.api.maps.model中的类">BitmapDescriptor</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/CameraPosition.html#CREATOR">CREATOR</a></span> - 类 中的静态变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/CameraPosition.html" title="com.amap.api.maps.model中的类">CameraPosition</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/CircleOptions.html#CREATOR">CREATOR</a></span> - 类 中的静态变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/CircleOptions.html" title="com.amap.api.maps.model中的类">CircleOptions</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/GLTFOverlayOptions.html#CREATOR">CREATOR</a></span> - 类 中的静态变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/GLTFOverlayOptions.html" title="com.amap.api.maps.model中的类">GLTFOverlayOptions</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/GroundOverlayOptions.html#CREATOR">CREATOR</a></span> - 类 中的静态变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/GroundOverlayOptions.html" title="com.amap.api.maps.model中的类">GroundOverlayOptions</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/LatLng.html#CREATOR">CREATOR</a></span> - 类 中的静态变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/LatLng.html" title="com.amap.api.maps.model中的类">LatLng</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/LatLngBounds.html#CREATOR">CREATOR</a></span> - 类 中的静态变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/LatLngBounds.html" title="com.amap.api.maps.model中的类">LatLngBounds</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MarkerOptions.html#CREATOR">CREATOR</a></span> - 类 中的静态变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MarkerOptions.html" title="com.amap.api.maps.model中的类">MarkerOptions</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MVTTileOverlayOptions.html#CREATOR">CREATOR</a></span> - 类 中的静态变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MVTTileOverlayOptions.html" title="com.amap.api.maps.model中的类">MVTTileOverlayOptions</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/NavigateArrowOptions.html#CREATOR">CREATOR</a></span> - 类 中的静态变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/NavigateArrowOptions.html" title="com.amap.api.maps.model中的类">NavigateArrowOptions</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Poi.html#CREATOR">CREATOR</a></span> - 类 中的静态变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Poi.html" title="com.amap.api.maps.model中的类">Poi</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/PolygonOptions.html#CREATOR">CREATOR</a></span> - 类 中的静态变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/PolygonOptions.html" title="com.amap.api.maps.model中的类">PolygonOptions</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/PolylineOptions.html#CREATOR">CREATOR</a></span> - 类 中的静态变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/PolylineOptions.html" title="com.amap.api.maps.model中的类">PolylineOptions</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/TextOptions.html#CREATOR">CREATOR</a></span> - 类 中的静态变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/TextOptions.html" title="com.amap.api.maps.model中的类">TextOptions</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Tile.html#CREATOR">CREATOR</a></span> - 类 中的静态变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Tile.html" title="com.amap.api.maps.model中的类">Tile</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/TileOverlayOptions.html#CREATOR">CREATOR</a></span> - 类 中的静态变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/TileOverlayOptions.html" title="com.amap.api.maps.model中的类">TileOverlayOptions</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/TileProjection.html#CREATOR">CREATOR</a></span> - 类 中的静态变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/TileProjection.html" title="com.amap.api.maps.model中的类">TileProjection</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/City.html#CREATOR">CREATOR</a></span> - 类 中的静态变量com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/City.html" title="com.amap.api.maps.offlinemap中的类">City</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/OfflineMapProvince.html#CREATOR">CREATOR</a></span> - 类 中的静态变量com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/OfflineMapProvince.html" title="com.amap.api.maps.offlinemap中的类">OfflineMapProvince</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/Province.html#CREATOR">CREATOR</a></span> - 类 中的静态变量com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/Province.html" title="com.amap.api.maps.offlinemap中的类">Province</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/amap/api/maps/model/CrossOverlay.GenerateCrossImageListener.html" title="com.amap.api.maps.model中的接口"><span class="typeNameLink">CrossOverlay.GenerateCrossImageListener</span></a> - <a href="../com/amap/api/maps/model/package-summary.html">com.amap.api.maps.model</a>中的接口</dt>
<dd>
<div class="block">路口放大图图片生成监听</div>
</dd>
<dt><a href="../com/amap/api/maps/model/CrossOverlay.OnCrossVectorUpdateListener.html" title="com.amap.api.maps.model中的接口"><span class="typeNameLink">CrossOverlay.OnCrossVectorUpdateListener</span></a> - <a href="../com/amap/api/maps/model/package-summary.html">com.amap.api.maps.model</a>中的接口</dt>
<dd>
<div class="block">路口更新回调</div>
</dd>
<dt><a href="../com/amap/api/maps/model/CrossOverlay.UpdateItem.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">CrossOverlay.UpdateItem</span></a> - <a href="../com/amap/api/maps/model/package-summary.html">com.amap.api.maps.model</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><a href="../com/amap/api/maps/model/particle/CurveSizeOverLife.html" title="com.amap.api.maps.model.particle中的类"><span class="typeNameLink">CurveSizeOverLife</span></a> - <a href="../com/amap/api/maps/model/particle/package-summary.html">com.amap.api.maps.model.particle</a>中的类</dt>
<dd>
<div class="block">大小根据曲线变化</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/particle/CurveSizeOverLife.html#CurveSizeOverLife-float-float-float-">CurveSizeOverLife(float, float, float)</a></span> - 类 的构造器com.amap.api.maps.model.particle.<a href="../com/amap/api/maps/model/particle/CurveSizeOverLife.html" title="com.amap.api.maps.model.particle中的类">CurveSizeOverLife</a></dt>
<dd>
<div class="block">缩放比例变化控制</div>
</dd>
<dt><a href="../com/amap/api/maps/model/CustomMapStyleOptions.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">CustomMapStyleOptions</span></a> - <a href="../com/amap/api/maps/model/package-summary.html">com.amap.api.maps.model</a>中的类</dt>
<dd>
<div class="block">自定义样式属性集</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/CustomMapStyleOptions.html#CustomMapStyleOptions--">CustomMapStyleOptions()</a></span> - 类 的构造器com.amap.api.maps.model.<a href="../com/amap/api/maps/model/CustomMapStyleOptions.html" title="com.amap.api.maps.model中的类">CustomMapStyleOptions</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/amap/api/maps/CustomRenderer.html" title="com.amap.api.maps中的接口"><span class="typeNameLink">CustomRenderer</span></a> - <a href="../com/amap/api/maps/package-summary.html">com.amap.api.maps</a>中的接口</dt>
<dd>
<div class="block">定义了一个地图在初始化及每一帧绘制时的回调接口。</div>
</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">K</a>&nbsp;<a href="index-11.html">L</a>&nbsp;<a href="index-12.html">M</a>&nbsp;<a href="index-13.html">N</a>&nbsp;<a href="index-14.html">O</a>&nbsp;<a href="index-15.html">P</a>&nbsp;<a href="index-16.html">Q</a>&nbsp;<a href="index-17.html">R</a>&nbsp;<a href="index-18.html">S</a>&nbsp;<a href="index-19.html">T</a>&nbsp;<a href="index-20.html">U</a>&nbsp;<a href="index-21.html">V</a>&nbsp;<a href="index-22.html">W</a>&nbsp;<a href="index-23.html">Z</a>&nbsp;</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-2.html">上一个字母</a></li>
<li><a href="index-4.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-3.html" target="_top">框架</a></li>
<li><a href="index-3.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
