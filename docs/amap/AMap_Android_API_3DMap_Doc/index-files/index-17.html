<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>R - 索引</title>
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="R - \u7D22\u5F15";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-16.html">上一个字母</a></li>
<li><a href="index-18.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-17.html" target="_top">框架</a></li>
<li><a href="index-17.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="contentContainer"><a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">K</a>&nbsp;<a href="index-11.html">L</a>&nbsp;<a href="index-12.html">M</a>&nbsp;<a href="index-13.html">N</a>&nbsp;<a href="index-14.html">O</a>&nbsp;<a href="index-15.html">P</a>&nbsp;<a href="index-16.html">Q</a>&nbsp;<a href="index-17.html">R</a>&nbsp;<a href="index-18.html">S</a>&nbsp;<a href="index-19.html">T</a>&nbsp;<a href="index-20.html">U</a>&nbsp;<a href="index-21.html">V</a>&nbsp;<a href="index-22.html">W</a>&nbsp;<a href="index-23.html">Z</a>&nbsp;<a name="I:R">
<!--   -->
</a>
<h2 class="title">R</h2>
<dl>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/CircleHoleOptions.html#radius-double-">radius(double)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/CircleHoleOptions.html" title="com.amap.api.maps.model中的类">CircleHoleOptions</a></dt>
<dd>
<div class="block">设置圆洞的半径，单位米。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/CircleOptions.html#radius-double-">radius(double)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/CircleOptions.html" title="com.amap.api.maps.model中的类">CircleOptions</a></dt>
<dd>
<div class="block">设置圆的半径，单位米。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/HeatmapTileProvider.Builder.html#radius-int-">radius(int)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/HeatmapTileProvider.Builder.html" title="com.amap.api.maps.model中的类">HeatmapTileProvider.Builder</a></dt>
<dd>
<div class="block">设置热力图点半径，默认为12ps，可不设置该接口</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/ImageOptions.html#radius">radius</a></span> - 类 中的变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/ImageOptions.html" title="com.amap.api.maps.model中的类">ImageOptions</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MyLocationStyle.html#radiusFillColor-int-">radiusFillColor(int)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MyLocationStyle.html" title="com.amap.api.maps.model中的类">MyLocationStyle</a></dt>
<dd>
<div class="block">设置圆形区域（以定位位置为圆心，定位半径的圆形区域）的填充颜色。</div>
</dd>
<dt><a href="../com/amap/api/maps/model/particle/RandomColorBetWeenTwoConstants.html" title="com.amap.api.maps.model.particle中的类"><span class="typeNameLink">RandomColorBetWeenTwoConstants</span></a> - <a href="../com/amap/api/maps/model/particle/package-summary.html">com.amap.api.maps.model.particle</a>中的类</dt>
<dd>
<div class="block">颜色生成器</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/particle/RandomColorBetWeenTwoConstants.html#RandomColorBetWeenTwoConstants-float-float-float-float-float-float-float-float-">RandomColorBetWeenTwoConstants(float, float, float, float, float, float, float, float)</a></span> - 类 的构造器com.amap.api.maps.model.particle.<a href="../com/amap/api/maps/model/particle/RandomColorBetWeenTwoConstants.html" title="com.amap.api.maps.model.particle中的类">RandomColorBetWeenTwoConstants</a></dt>
<dd>
<div class="block">随机生成两个区间的颜色，rgba</div>
</dd>
<dt><a href="../com/amap/api/maps/model/particle/RandomVelocityBetweenTwoConstants.html" title="com.amap.api.maps.model.particle中的类"><span class="typeNameLink">RandomVelocityBetweenTwoConstants</span></a> - <a href="../com/amap/api/maps/model/particle/package-summary.html">com.amap.api.maps.model.particle</a>中的类</dt>
<dd>
<div class="block">速度生长器
 随机产生制定范围内的速度</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/particle/RandomVelocityBetweenTwoConstants.html#RandomVelocityBetweenTwoConstants-float-float-float-float-float-float-">RandomVelocityBetweenTwoConstants(float, float, float, float, float, float)</a></span> - 类 的构造器com.amap.api.maps.model.particle.<a href="../com/amap/api/maps/model/particle/RandomVelocityBetweenTwoConstants.html" title="com.amap.api.maps.model.particle中的类">RandomVelocityBetweenTwoConstants</a></dt>
<dd>
<div class="block">速度生成器，设置指定范围内的速度值</div>
</dd>
<dt><a href="../com/amap/api/maps/model/particle/RectParticleShape.html" title="com.amap.api.maps.model.particle中的类"><span class="typeNameLink">RectParticleShape</span></a> - <a href="../com/amap/api/maps/model/particle/package-summary.html">com.amap.api.maps.model.particle</a>中的类</dt>
<dd>
<div class="block">区域发射器，发起范围是某个区域</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/particle/RectParticleShape.html#RectParticleShape-float-float-float-float-boolean-">RectParticleShape(float, float, float, float, boolean)</a></span> - 类 的构造器com.amap.api.maps.model.particle.<a href="../com/amap/api/maps/model/particle/RectParticleShape.html" title="com.amap.api.maps.model.particle中的类">RectParticleShape</a></dt>
<dd>
<div class="block">指定区域发射器</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/BitmapDescriptor.html#recycle--">recycle()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/BitmapDescriptor.html" title="com.amap.api.maps.model中的类">BitmapDescriptor</a></dt>
<dd>
<div class="block">回收 bitmap 资源，请确保在不再使用该 bitmap descriptor 时再调用该函数。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Tile.html#recycle--">recycle()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Tile.html" title="com.amap.api.maps.model中的类">Tile</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#reloadMap--">reloadMap()</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block">重新加载地图引擎，即调用此接口时会重新加载底图数据，覆盖物不受影响。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Arc.html#remove--">remove()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Arc.html" title="com.amap.api.maps.model中的类">Arc</a></dt>
<dd>
<div class="block">删除从地图对象里圆弧</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/BasePointOverlay.html#remove--">remove()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/BasePointOverlay.html" title="com.amap.api.maps.model中的类">BasePointOverlay</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Circle.html#remove--">remove()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Circle.html" title="com.amap.api.maps.model中的类">Circle</a></dt>
<dd>
<div class="block">删除从地图对象里圆</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/GLTFOverlay.html#remove--">remove()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/GLTFOverlay.html" title="com.amap.api.maps.model中的类">GLTFOverlay</a></dt>
<dd>
<div class="block">从地图中删除当前 GLTFOverlay，删除后它的所有方法不可用。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/GroundOverlay.html#remove--">remove()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/GroundOverlay.html" title="com.amap.api.maps.model中的类">GroundOverlay</a></dt>
<dd>
<div class="block">从地图中删除当前 Ground 覆盖物，删除后它的所有方法不可用。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Marker.html#remove--">remove()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Marker.html" title="com.amap.api.maps.model中的类">Marker</a></dt>
<dd>
<div class="block">删除当前marker。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MultiPointOverlay.html#remove--">remove()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MultiPointOverlay.html" title="com.amap.api.maps.model中的类">MultiPointOverlay</a></dt>
<dd>
<div class="block">从地图上移除海量点图层</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MVTTileOverlay.html#remove--">remove()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MVTTileOverlay.html" title="com.amap.api.maps.model中的类">MVTTileOverlay</a></dt>
<dd>
<div class="block">从地图上删除瓦片图层</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/NavigateArrow.html#remove--">remove()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/NavigateArrow.html" title="com.amap.api.maps.model中的类">NavigateArrow</a></dt>
<dd>
<div class="block">从地图上删除当前箭头(NavigateArrow)覆盖物。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Polygon.html#remove--">remove()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Polygon.html" title="com.amap.api.maps.model中的类">Polygon</a></dt>
<dd>
<div class="block">从地图上删除当前多边形。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Polyline.html#remove--">remove()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Polyline.html" title="com.amap.api.maps.model中的类">Polyline</a></dt>
<dd>
<div class="block">从地图上删除当前线段。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Text.html#remove--">remove()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Text.html" title="com.amap.api.maps.model中的类">Text</a></dt>
<dd>
<div class="block">删除文字覆盖物。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/TileOverlay.html#remove--">remove()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/TileOverlay.html" title="com.amap.api.maps.model中的类">TileOverlay</a></dt>
<dd>
<div class="block">从地图上删除瓦片图层</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/OfflineMapManager.html#remove-java.lang.String-">remove(String)</a></span> - 类 中的方法com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/OfflineMapManager.html" title="com.amap.api.maps.offlinemap中的类">OfflineMapManager</a></dt>
<dd>
<div class="block">根据给定的城市名称删除该城市的离线地图包。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/trace/TraceOverlay.html#remove--">remove()</a></span> - 类 中的方法com.amap.api.trace.<a href="../com/amap/api/trace/TraceOverlay.html" title="com.amap.api.trace中的类">TraceOverlay</a></dt>
<dd>
<div class="block">移除一条轨迹线路</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#removeAMapAppResourceListener-com.amap.api.maps.AMap.AMapAppResourceRequestListener-">removeAMapAppResourceListener(AMap.AMapAppResourceRequestListener)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#removecache--">removecache()</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block">删除地图缓存。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#removecache-com.amap.api.maps.AMap.OnCacheRemoveListener-">removecache(AMap.OnCacheRemoveListener)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block">删除地图缓存。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/utils/overlay/MovingPointOverlay.html#resetIndex--">resetIndex()</a></span> - 类 中的方法com.amap.api.maps.utils.overlay.<a href="../com/amap/api/maps/utils/overlay/MovingPointOverlay.html" title="com.amap.api.maps.utils.overlay中的类">MovingPointOverlay</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#resetMinMaxZoomPreference--">resetMinMaxZoomPreference()</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block">重置最小及最大缩放级别 将恢复最小级别为3，最大级别为20 。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/animation/Animation.html#RESTART">RESTART</a></span> - 类 中的静态变量com.amap.api.maps.model.animation.<a href="../com/amap/api/maps/model/animation/Animation.html" title="com.amap.api.maps.model.animation中的类">Animation</a></dt>
<dd>
<div class="block">动画结束后从头播放，最大重复次数受<a href="../com/amap/api/maps/model/animation/Animation.html#setRepeatCount-int-"><code>Animation.setRepeatCount(int)</code></a> 限制</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/OfflineMapManager.html#restart--">restart()</a></span> - 类 中的方法com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/OfflineMapManager.html" title="com.amap.api.maps.offlinemap中的类">OfflineMapManager</a></dt>
<dd>
<div class="block">重新开始任务,开始下载队列中的第一个为等待中的任务</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/animation/Animation.html#REVERSE">REVERSE</a></span> - 类 中的静态变量com.amap.api.maps.model.animation.<a href="../com/amap/api/maps/model/animation/Animation.html" title="com.amap.api.maps.model.animation中的类">Animation</a></dt>
<dd>
<div class="block">动画结束后从尾倒放，最大重复次数受<a href="../com/amap/api/maps/model/animation/Animation.html#setRepeatCount-int-"><code>Animation.setRepeatCount(int)</code></a> 限制</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/ImageOptions.html#rgba">rgba</a></span> - 类 中的变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/ImageOptions.html" title="com.amap.api.maps.model中的类">ImageOptions</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/TextOptions.html#rotate-float-">rotate(float)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/TextOptions.html" title="com.amap.api.maps.model中的类">TextOptions</a></dt>
<dd>
<div class="block">设置文字覆盖物的旋转角度,逆时针。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MarkerOptions.html#rotateAngle-float-">rotateAngle(float)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MarkerOptions.html" title="com.amap.api.maps.model中的类">MarkerOptions</a></dt>
<dd>
<div class="block">设置Marker覆盖物的图片旋转角度，从正北开始，逆时针计算。</div>
</dd>
<dt><a href="../com/amap/api/maps/model/animation/RotateAnimation.html" title="com.amap.api.maps.model.animation中的类"><span class="typeNameLink">RotateAnimation</span></a> - <a href="../com/amap/api/maps/model/animation/package-summary.html">com.amap.api.maps.model.animation</a>中的类</dt>
<dd>
<div class="block">控制旋转的动画类</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/animation/RotateAnimation.html#RotateAnimation-float-float-">RotateAnimation(float, float)</a></span> - 类 的构造器com.amap.api.maps.model.animation.<a href="../com/amap/api/maps/model/animation/RotateAnimation.html" title="com.amap.api.maps.model.animation中的类">RotateAnimation</a></dt>
<dd>
<div class="block">控制旋转的动画类</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMapOptions.html#rotateGesturesEnabled-boolean-">rotateGesturesEnabled(boolean)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMapOptions.html" title="com.amap.api.maps中的类">AMapOptions</a></dt>
<dd>
<div class="block">设置地图是否可以通过手势进行旋转。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/GLTFOverlayOptions.html#rotationDegree-double-double-double-">rotationDegree(double, double, double)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/GLTFOverlayOptions.html" title="com.amap.api.maps.model中的类">GLTFOverlayOptions</a></dt>
<dd>
<div class="block">设置模型的旋转角度,XYZ</div>
</dd>
<dt><a href="../com/amap/api/maps/model/particle/RotationOverLife.html" title="com.amap.api.maps.model.particle中的类"><span class="typeNameLink">RotationOverLife</span></a> - <a href="../com/amap/api/maps/model/particle/package-summary.html">com.amap.api.maps.model.particle</a>中的类</dt>
<dd>
<div class="block">旋转角度变化控制基类，具体使用时需要使用它的子类</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/particle/RotationOverLife.html#RotationOverLife--">RotationOverLife()</a></span> - 类 的构造器com.amap.api.maps.model.particle.<a href="../com/amap/api/maps/model/particle/RotationOverLife.html" title="com.amap.api.maps.model.particle中的类">RotationOverLife</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/amap/api/maps/model/RoutePara.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">RoutePara</span></a> - <a href="../com/amap/api/maps/model/package-summary.html">com.amap.api.maps.model</a>中的类</dt>
<dd>
<div class="block">唤起高德地图路径规划功能的参数类。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/RoutePara.html#RoutePara--">RoutePara()</a></span> - 类 的构造器com.amap.api.maps.model.<a href="../com/amap/api/maps/model/RoutePara.html" title="com.amap.api.maps.model中的类">RoutePara</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#runOnDrawFrame--">runOnDrawFrame()</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block">触发地图立即刷新。</div>
</dd>
<dt><a href="../com/amap/api/maps/model/RuntimeRemoteException.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">RuntimeRemoteException</span></a> - <a href="../com/amap/api/maps/model/package-summary.html">com.amap.api.maps.model</a>中的异常错误</dt>
<dd>
<div class="block">异常处理</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/RuntimeRemoteException.html#RuntimeRemoteException-java.lang.String-">RuntimeRemoteException(String)</a></span> - 异常错误 的构造器com.amap.api.maps.model.<a href="../com/amap/api/maps/model/RuntimeRemoteException.html" title="com.amap.api.maps.model中的类">RuntimeRemoteException</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/RuntimeRemoteException.html#RuntimeRemoteException-android.os.RemoteException-">RuntimeRemoteException(RemoteException)</a></span> - 异常错误 的构造器com.amap.api.maps.model.<a href="../com/amap/api/maps/model/RuntimeRemoteException.html" title="com.amap.api.maps.model中的类">RuntimeRemoteException</a></dt>
<dd>&nbsp;</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">K</a>&nbsp;<a href="index-11.html">L</a>&nbsp;<a href="index-12.html">M</a>&nbsp;<a href="index-13.html">N</a>&nbsp;<a href="index-14.html">O</a>&nbsp;<a href="index-15.html">P</a>&nbsp;<a href="index-16.html">Q</a>&nbsp;<a href="index-17.html">R</a>&nbsp;<a href="index-18.html">S</a>&nbsp;<a href="index-19.html">T</a>&nbsp;<a href="index-20.html">U</a>&nbsp;<a href="index-21.html">V</a>&nbsp;<a href="index-22.html">W</a>&nbsp;<a href="index-23.html">Z</a>&nbsp;</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-16.html">上一个字母</a></li>
<li><a href="index-18.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-17.html" target="_top">框架</a></li>
<li><a href="index-17.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
