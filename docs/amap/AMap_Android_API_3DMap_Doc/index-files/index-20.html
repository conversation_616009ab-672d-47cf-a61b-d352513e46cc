<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>U - 索引</title>
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="U - \u7D22\u5F15";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-19.html">上一个字母</a></li>
<li><a href="index-21.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-20.html" target="_top">框架</a></li>
<li><a href="index-20.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="contentContainer"><a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">K</a>&nbsp;<a href="index-11.html">L</a>&nbsp;<a href="index-12.html">M</a>&nbsp;<a href="index-13.html">N</a>&nbsp;<a href="index-14.html">O</a>&nbsp;<a href="index-15.html">P</a>&nbsp;<a href="index-16.html">Q</a>&nbsp;<a href="index-17.html">R</a>&nbsp;<a href="index-18.html">S</a>&nbsp;<a href="index-19.html">T</a>&nbsp;<a href="index-20.html">U</a>&nbsp;<a href="index-21.html">V</a>&nbsp;<a href="index-22.html">W</a>&nbsp;<a href="index-23.html">Z</a>&nbsp;<a name="I:U">
<!--   -->
</a>
<h2 class="title">U</h2>
<dl>
<dt><a href="../com/amap/api/maps/UiSettings.html" title="com.amap.api.maps中的类"><span class="typeNameLink">UiSettings</span></a> - <a href="../com/amap/api/maps/package-summary.html">com.amap.api.maps</a>中的类</dt>
<dd>
<div class="block">地图内置UI及手势控制器。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/OfflineMapStatus.html#UNZIP">UNZIP</a></span> - 类 中的静态变量com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/OfflineMapStatus.html" title="com.amap.api.maps.offlinemap中的类">OfflineMapStatus</a></dt>
<dd>
<div class="block">正在解压。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/CrossOverlay.UpdateItem.html#UpdateItem--">UpdateItem()</a></span> - 类 的构造器com.amap.api.maps.model.<a href="../com/amap/api/maps/model/CrossOverlay.UpdateItem.html" title="com.amap.api.maps.model中的类">CrossOverlay.UpdateItem</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/OfflineMapManager.html#updateOfflineCityByCode-java.lang.String-">updateOfflineCityByCode(String)</a></span> - 类 中的方法com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/OfflineMapManager.html" title="com.amap.api.maps.offlinemap中的类">OfflineMapManager</a></dt>
<dd>
<div class="block">判断传入的城市（城市编码）是否有更新的离线数据包。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/OfflineMapManager.html#updateOfflineCityByName-java.lang.String-">updateOfflineCityByName(String)</a></span> - 类 中的方法com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/OfflineMapManager.html" title="com.amap.api.maps.offlinemap中的类">OfflineMapManager</a></dt>
<dd>
<div class="block">判断传入的城市（城市名称）是否有更新的离线数据包。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/OfflineMapManager.html#updateOfflineMapProvinceByName-java.lang.String-">updateOfflineMapProvinceByName(String)</a></span> - 类 中的方法com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/OfflineMapManager.html" title="com.amap.api.maps.offlinemap中的类">OfflineMapManager</a></dt>
<dd>
<div class="block">判断传入的省份名称是否有更新的离线数据包。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MVTTileOverlay.html#updateOption--">updateOption()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MVTTileOverlay.html" title="com.amap.api.maps.model中的类">MVTTileOverlay</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/TileOverlay.html#updateOption--">updateOption()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/TileOverlay.html" title="com.amap.api.maps.model中的类">TileOverlay</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/MapsInitializer.html#updatePrivacyAgree-android.content.Context-boolean-">updatePrivacyAgree(Context, boolean)</a></span> - 类 中的静态方法com.amap.api.maps.<a href="../com/amap/api/maps/MapsInitializer.html" title="com.amap.api.maps中的类">MapsInitializer</a></dt>
<dd>
<div class="block">更新同意隐私状态,需要在初始化地图之前完成</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/MapsInitializer.html#updatePrivacyShow-android.content.Context-boolean-boolean-">updatePrivacyShow(Context, boolean, boolean)</a></span> - 类 中的静态方法com.amap.api.maps.<a href="../com/amap/api/maps/MapsInitializer.html" title="com.amap.api.maps中的类">MapsInitializer</a></dt>
<dd>
<div class="block">更新隐私合规状态,需要在初始化地图之前完成</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/GLTFOverlayOptions.html#uriResources-java.util.List-">uriResources(List&lt;GLTFResourceIterm&gt;)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/GLTFOverlayOptions.html" title="com.amap.api.maps.model中的类">GLTFOverlayOptions</a></dt>
<dd>
<div class="block">设置glTF对应uri资源</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MVTTileOverlayOptions.Builder.html#url-java.lang.String-">url(String)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MVTTileOverlayOptions.Builder.html" title="com.amap.api.maps.model中的类">MVTTileOverlayOptions.Builder</a></dt>
<dd>
<div class="block">MVT数据的url

 https://restapi.amap.com/rest/lbs/geohub/tiles/mvt</div>
</dd>
<dt><a href="../com/amap/api/maps/model/UrlTileProvider.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">UrlTileProvider</span></a> - <a href="../com/amap/api/maps/model/package-summary.html">com.amap.api.maps.model</a>中的类</dt>
<dd>
<div class="block">在线瓦片图层TileOverlay的显示Provider构造器，只需要一个URL指向的图像。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/UrlTileProvider.html#UrlTileProvider-int-int-">UrlTileProvider(int, int)</a></span> - 类 的构造器com.amap.api.maps.model.<a href="../com/amap/api/maps/model/UrlTileProvider.html" title="com.amap.api.maps.model中的类">UrlTileProvider</a></dt>
<dd>
<div class="block">构造一个UrlTileProvider对象。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/PolylineOptions.html#useGradient-boolean-">useGradient(boolean)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/PolylineOptions.html" title="com.amap.api.maps.model中的类">PolylineOptions</a></dt>
<dd>
<div class="block">设置线段是否使用渐变色</div>
</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">K</a>&nbsp;<a href="index-11.html">L</a>&nbsp;<a href="index-12.html">M</a>&nbsp;<a href="index-13.html">N</a>&nbsp;<a href="index-14.html">O</a>&nbsp;<a href="index-15.html">P</a>&nbsp;<a href="index-16.html">Q</a>&nbsp;<a href="index-17.html">R</a>&nbsp;<a href="index-18.html">S</a>&nbsp;<a href="index-19.html">T</a>&nbsp;<a href="index-20.html">U</a>&nbsp;<a href="index-21.html">V</a>&nbsp;<a href="index-22.html">W</a>&nbsp;<a href="index-23.html">Z</a>&nbsp;</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-19.html">上一个字母</a></li>
<li><a href="index-21.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-20.html" target="_top">框架</a></li>
<li><a href="index-20.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
