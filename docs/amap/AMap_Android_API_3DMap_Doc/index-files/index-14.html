<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>O - 索引</title>
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="O - \u7D22\u5F15";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-13.html">上一个字母</a></li>
<li><a href="index-15.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-14.html" target="_top">框架</a></li>
<li><a href="index-14.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="contentContainer"><a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">K</a>&nbsp;<a href="index-11.html">L</a>&nbsp;<a href="index-12.html">M</a>&nbsp;<a href="index-13.html">N</a>&nbsp;<a href="index-14.html">O</a>&nbsp;<a href="index-15.html">P</a>&nbsp;<a href="index-16.html">Q</a>&nbsp;<a href="index-17.html">R</a>&nbsp;<a href="index-18.html">S</a>&nbsp;<a href="index-19.html">T</a>&nbsp;<a href="index-20.html">U</a>&nbsp;<a href="index-21.html">V</a>&nbsp;<a href="index-22.html">W</a>&nbsp;<a href="index-23.html">Z</a>&nbsp;<a name="I:O">
<!--   -->
</a>
<h2 class="title">O</h2>
<dl>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Tile.html#obtain-int-int-byte:A-">obtain(int, int, byte[])</a></span> - 类 中的静态方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Tile.html" title="com.amap.api.maps.model中的类">Tile</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/amap/api/maps/offlinemap/OfflineMapActivity.html" title="com.amap.api.maps.offlinemap中的类"><span class="typeNameLink">OfflineMapActivity</span></a> - <a href="../com/amap/api/maps/offlinemap/package-summary.html">com.amap.api.maps.offlinemap</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/OfflineMapActivity.html#OfflineMapActivity--">OfflineMapActivity()</a></span> - 类 的构造器com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/OfflineMapActivity.html" title="com.amap.api.maps.offlinemap中的类">OfflineMapActivity</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/amap/api/maps/offlinemap/OfflineMapCity.html" title="com.amap.api.maps.offlinemap中的类"><span class="typeNameLink">OfflineMapCity</span></a> - <a href="../com/amap/api/maps/offlinemap/package-summary.html">com.amap.api.maps.offlinemap</a>中的类</dt>
<dd>
<div class="block">下载城市属性的相关类。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/OfflineMapCity.html#OfflineMapCity--">OfflineMapCity()</a></span> - 类 的构造器com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/OfflineMapCity.html" title="com.amap.api.maps.offlinemap中的类">OfflineMapCity</a></dt>
<dd>
<div class="block">构造一个OfflineMapCity对象。</div>
</dd>
<dt><a href="../com/amap/api/maps/offlinemap/OfflineMapManager.html" title="com.amap.api.maps.offlinemap中的类"><span class="typeNameLink">OfflineMapManager</span></a> - <a href="../com/amap/api/maps/offlinemap/package-summary.html">com.amap.api.maps.offlinemap</a>中的类</dt>
<dd>
<div class="block">离线地图下载管理类，支持3D矢量地图下载。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/OfflineMapManager.html#OfflineMapManager-android.content.Context-com.amap.api.maps.offlinemap.OfflineMapManager.OfflineMapDownloadListener-">OfflineMapManager(Context, OfflineMapManager.OfflineMapDownloadListener)</a></span> - 类 的构造器com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/OfflineMapManager.html" title="com.amap.api.maps.offlinemap中的类">OfflineMapManager</a></dt>
<dd>
<div class="block">根据给定的参数来构造OfflineMapManager对象。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/OfflineMapManager.html#OfflineMapManager-android.content.Context-com.amap.api.maps.offlinemap.OfflineMapManager.OfflineMapDownloadListener-com.amap.api.maps.AMap-">OfflineMapManager(Context, OfflineMapManager.OfflineMapDownloadListener, AMap)</a></span> - 类 的构造器com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/OfflineMapManager.html" title="com.amap.api.maps.offlinemap中的类">OfflineMapManager</a></dt>
<dd>
<div class="block">根据给定的参数来构造OfflineMapManager对象。</div>
</dd>
<dt><a href="../com/amap/api/maps/offlinemap/OfflineMapManager.OfflineLoadedListener.html" title="com.amap.api.maps.offlinemap中的接口"><span class="typeNameLink">OfflineMapManager.OfflineLoadedListener</span></a> - <a href="../com/amap/api/maps/offlinemap/package-summary.html">com.amap.api.maps.offlinemap</a>中的接口</dt>
<dd>
<div class="block">离线地图初始化完成回调</div>
</dd>
<dt><a href="../com/amap/api/maps/offlinemap/OfflineMapManager.OfflineMapDownloadListener.html" title="com.amap.api.maps.offlinemap中的接口"><span class="typeNameLink">OfflineMapManager.OfflineMapDownloadListener</span></a> - <a href="../com/amap/api/maps/offlinemap/package-summary.html">com.amap.api.maps.offlinemap</a>中的接口</dt>
<dd>
<div class="block">离线地图下载过程中状态回调</div>
</dd>
<dt><a href="../com/amap/api/maps/offlinemap/OfflineMapProvince.html" title="com.amap.api.maps.offlinemap中的类"><span class="typeNameLink">OfflineMapProvince</span></a> - <a href="../com/amap/api/maps/offlinemap/package-summary.html">com.amap.api.maps.offlinemap</a>中的类</dt>
<dd>
<div class="block">下载省属性的相关类。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/OfflineMapProvince.html#OfflineMapProvince--">OfflineMapProvince()</a></span> - 类 的构造器com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/OfflineMapProvince.html" title="com.amap.api.maps.offlinemap中的类">OfflineMapProvince</a></dt>
<dd>
<div class="block">构造一个OfflineMapProvince对象。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/OfflineMapProvince.html#OfflineMapProvince-android.os.Parcel-">OfflineMapProvince(Parcel)</a></span> - 类 的构造器com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/OfflineMapProvince.html" title="com.amap.api.maps.offlinemap中的类">OfflineMapProvince</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/amap/api/maps/offlinemap/OfflineMapStatus.html" title="com.amap.api.maps.offlinemap中的类"><span class="typeNameLink">OfflineMapStatus</span></a> - <a href="../com/amap/api/maps/offlinemap/package-summary.html">com.amap.api.maps.offlinemap</a>中的类</dt>
<dd>
<div class="block">地图下载的相关状态码。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/OfflineMapStatus.html#OfflineMapStatus--">OfflineMapStatus()</a></span> - 类 的构造器com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/OfflineMapStatus.html" title="com.amap.api.maps.offlinemap中的类">OfflineMapStatus</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/TileProjection.html#offsetX">offsetX</a></span> - 类 中的变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/TileProjection.html" title="com.amap.api.maps.model中的类">TileProjection</a></dt>
<dd>
<div class="block">将西北角经纬度映射到左上角瓦片上，对应的像素点距离瓦片起点（0,0）x方向上的偏移像素数。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/TileProjection.html#offsetY">offsetY</a></span> - 类 中的变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/TileProjection.html" title="com.amap.api.maps.model中的类">TileProjection</a></dt>
<dd>
<div class="block">将西北角经纬度映射到左上角瓦片上，对应的像素点距离瓦片起点（0,0）y方向上的偏移像素数。。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/animation/Animation.AnimationListener.html#onAnimationEnd--">onAnimationEnd()</a></span> - 接口 中的方法com.amap.api.maps.model.animation.<a href="../com/amap/api/maps/model/animation/Animation.AnimationListener.html" title="com.amap.api.maps.model.animation中的接口">Animation.AnimationListener</a></dt>
<dd>
<div class="block">动画结束回调</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/animation/Animation.AnimationListener.html#onAnimationStart--">onAnimationStart()</a></span> - 接口 中的方法com.amap.api.maps.model.animation.<a href="../com/amap/api/maps/model/animation/Animation.AnimationListener.html" title="com.amap.api.maps.model.animation中的接口">Animation.AnimationListener</a></dt>
<dd>
<div class="block">动画开始回调</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.OnCameraChangeListener.html#onCameraChange-com.amap.api.maps.model.CameraPosition-">onCameraChange(CameraPosition)</a></span> - 接口 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.OnCameraChangeListener.html" title="com.amap.api.maps中的接口">AMap.OnCameraChangeListener</a></dt>
<dd>
<div class="block">在地图状态改变过程中回调此方法。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.OnCameraChangeListener.html#onCameraChangeFinish-com.amap.api.maps.model.CameraPosition-">onCameraChangeFinish(CameraPosition)</a></span> - 接口 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.OnCameraChangeListener.html" title="com.amap.api.maps中的接口">AMap.OnCameraChangeListener</a></dt>
<dd>
<div class="block">在地图状态改变完成时回调此方法。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.CancelableCallback.html#onCancel--">onCancel()</a></span> - 接口 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.CancelableCallback.html" title="com.amap.api.maps中的接口">AMap.CancelableCallback</a></dt>
<dd>
<div class="block">当CameraUpdate任务被中断时回调此方法。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/OfflineMapManager.OfflineMapDownloadListener.html#onCheckUpdate-boolean-java.lang.String-">onCheckUpdate(boolean, String)</a></span> - 接口 中的方法com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/OfflineMapManager.OfflineMapDownloadListener.html" title="com.amap.api.maps.offlinemap中的接口">OfflineMapManager.OfflineMapDownloadListener</a></dt>
<dd>
<div class="block">当调用updateOfflineMapCity 等检查更新函数的时候会被调用</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/OfflineMapActivity.html#onClick-android.view.View-">onClick(View)</a></span> - 类 中的方法com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/OfflineMapActivity.html" title="com.amap.api.maps.offlinemap中的类">OfflineMapActivity</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/OfflineMapActivity.html#onConfigurationChanged-android.content.res.Configuration-">onConfigurationChanged(Configuration)</a></span> - 类 中的方法com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/OfflineMapActivity.html" title="com.amap.api.maps.offlinemap中的类">OfflineMapActivity</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/MapView.html#onCreate-android.os.Bundle-">onCreate(Bundle)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/MapView.html" title="com.amap.api.maps中的类">MapView</a></dt>
<dd>
<div class="block">当Activity唤醒时调用地图唤醒。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/OfflineMapActivity.html#onCreate-android.os.Bundle-">onCreate(Bundle)</a></span> - 类 中的方法com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/OfflineMapActivity.html" title="com.amap.api.maps.offlinemap中的类">OfflineMapActivity</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/TextureMapView.html#onCreate-android.os.Bundle-">onCreate(Bundle)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/TextureMapView.html" title="com.amap.api.maps中的类">TextureMapView</a></dt>
<dd>
<div class="block">用户重载这个方法时必须调用父类的这个方法。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/WearMapView.html#onCreate-android.os.Bundle-">onCreate(Bundle)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/WearMapView.html" title="com.amap.api.maps中的类">WearMapView</a></dt>
<dd>
<div class="block">用户创建WearMapView，必须调用。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/MapFragment.html#onDestroy--">onDestroy()</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/MapFragment.html" title="com.amap.api.maps中的类">MapFragment</a></dt>
<dd>
<div class="block">用户重载这个方法时必须调用父类的这个方法，释放地图资源。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/MapView.html#onDestroy--">onDestroy()</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/MapView.html" title="com.amap.api.maps中的类">MapView</a></dt>
<dd>
<div class="block">当Activity销毁时调用地图的销毁。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/OfflineMapActivity.html#onDestroy--">onDestroy()</a></span> - 类 中的方法com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/OfflineMapActivity.html" title="com.amap.api.maps.offlinemap中的类">OfflineMapActivity</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/TextureMapView.html#onDestroy--">onDestroy()</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/TextureMapView.html" title="com.amap.api.maps中的类">TextureMapView</a></dt>
<dd>
<div class="block">当Activity销毁时调用地图的销毁。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/WearMapView.html#onDestroy--">onDestroy()</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/WearMapView.html" title="com.amap.api.maps中的类">WearMapView</a></dt>
<dd>
<div class="block">销毁WearMapView，必须调用。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/MapFragment.html#onDestroyView--">onDestroyView()</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/MapFragment.html" title="com.amap.api.maps中的类">MapFragment</a></dt>
<dd>
<div class="block">用户重载这个方法时必须调用父类的这个方法，销毁view。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/WearMapView.html#onDismiss--">onDismiss()</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/WearMapView.html" title="com.amap.api.maps中的类">WearMapView</a></dt>
<dd>
<div class="block">iew退出时回调</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/WearMapView.OnDismissCallback.html#onDismiss--">onDismiss()</a></span> - 接口 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/WearMapView.OnDismissCallback.html" title="com.amap.api.maps中的接口">WearMapView.OnDismissCallback</a></dt>
<dd>
<div class="block">WearView退出回调</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/AMapGestureListener.html#onDoubleTap-float-float-">onDoubleTap(float, float)</a></span> - 接口 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/AMapGestureListener.html" title="com.amap.api.maps.model中的接口">AMapGestureListener</a></dt>
<dd>
<div class="block">双击</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/AMapGestureListener.html#onDown-float-float-">onDown(float, float)</a></span> - 接口 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/AMapGestureListener.html" title="com.amap.api.maps.model中的接口">AMapGestureListener</a></dt>
<dd>
<div class="block">按下</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/OfflineMapManager.OfflineMapDownloadListener.html#onDownload-int-int-java.lang.String-">onDownload(int, int, String)</a></span> - 接口 中的方法com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/OfflineMapManager.OfflineMapDownloadListener.html" title="com.amap.api.maps.offlinemap中的接口">OfflineMapManager.OfflineMapDownloadListener</a></dt>
<dd>
<div class="block">下载状态回调，在调用downloadByCityName 等下载方法的时候会启动</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/DownloadProgressView.html#onDraw-android.graphics.Canvas-">onDraw(Canvas)</a></span> - 类 中的方法com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/DownloadProgressView.html" title="com.amap.api.maps.offlinemap中的类">DownloadProgressView</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.CancelableCallback.html#onFinish--">onFinish()</a></span> - 接口 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.CancelableCallback.html" title="com.amap.api.maps中的接口">AMap.CancelableCallback</a></dt>
<dd>
<div class="block">当CameraUpdate任务完成时回调此方法。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.OnMapSnapshotListener.html#onFinish--">onFinish()</a></span> - 接口 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.OnMapSnapshotListener.html" title="com.amap.api.maps中的接口">AMap.OnMapSnapshotListener</a></dt>
<dd>
<div class="block">地图截取区域图结束回调此方法。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/trace/TraceListener.html#onFinished-int-java.util.List-int-int-">onFinished(int, List&lt;LatLng&gt;, int, int)</a></span> - 接口 中的方法com.amap.api.trace.<a href="../com/amap/api/trace/TraceListener.html" title="com.amap.api.trace中的接口">TraceListener</a></dt>
<dd>
<div class="block">轨迹纠偏成功回调</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/AMapGestureListener.html#onFling-float-float-">onFling(float, float)</a></span> - 接口 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/AMapGestureListener.html" title="com.amap.api.maps.model中的接口">AMapGestureListener</a></dt>
<dd>
<div class="block">惯性滑动</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/CrossOverlay.GenerateCrossImageListener.html#onGenerateComplete-android.graphics.Bitmap-int-">onGenerateComplete(Bitmap, int)</a></span> - 接口 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/CrossOverlay.GenerateCrossImageListener.html" title="com.amap.api.maps.model中的接口">CrossOverlay.GenerateCrossImageListener</a></dt>
<dd>
<div class="block">生成完成</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.OnIndoorBuildingActiveListener.html#OnIndoorBuilding-com.amap.api.maps.model.IndoorBuildingInfo-">OnIndoorBuilding(IndoorBuildingInfo)</a></span> - 接口 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.OnIndoorBuildingActiveListener.html" title="com.amap.api.maps中的接口">AMap.OnIndoorBuildingActiveListener</a></dt>
<dd>
<div class="block">显示室内地图会回调此方法。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/MapFragment.html#onInflate-android.app.Activity-android.util.AttributeSet-android.os.Bundle-">onInflate(Activity, AttributeSet, Bundle)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/MapFragment.html" title="com.amap.api.maps中的类">MapFragment</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/TextureSupportMapFragment.html#onInflate-android.app.Activity-android.util.AttributeSet-android.os.Bundle-">onInflate(Activity, AttributeSet, Bundle)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/TextureSupportMapFragment.html" title="com.amap.api.maps中的类">TextureSupportMapFragment</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.OnInfoWindowClickListener.html#onInfoWindowClick-com.amap.api.maps.model.Marker-">onInfoWindowClick(Marker)</a></span> - 接口 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.OnInfoWindowClickListener.html" title="com.amap.api.maps中的接口">AMap.OnInfoWindowClickListener</a></dt>
<dd>
<div class="block">当marker的信息窗口被点击时，回调此方法。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/OfflineMapActivity.html#onKeyDown-int-android.view.KeyEvent-">onKeyDown(int, KeyEvent)</a></span> - 类 中的方法com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/OfflineMapActivity.html" title="com.amap.api.maps.offlinemap中的类">OfflineMapActivity</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/LocationSource.OnLocationChangedListener.html#onLocationChanged-android.location.Location-">onLocationChanged(Location)</a></span> - 接口 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/LocationSource.OnLocationChangedListener.html" title="com.amap.api.maps中的接口">LocationSource.OnLocationChangedListener</a></dt>
<dd>
<div class="block">当定位源获取的位置信息发生变化时回调此接口。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/AMapGestureListener.html#onLongPress-float-float-">onLongPress(float, float)</a></span> - 接口 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/AMapGestureListener.html" title="com.amap.api.maps.model中的接口">AMapGestureListener</a></dt>
<dd>
<div class="block">长按</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/MapFragment.html#onLowMemory--">onLowMemory()</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/MapFragment.html" title="com.amap.api.maps中的类">MapFragment</a></dt>
<dd>
<div class="block">用户重载这个方法时必须调用父类的这个方法，低电量模式。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.OnMapClickListener.html#onMapClick-com.amap.api.maps.model.LatLng-">onMapClick(LatLng)</a></span> - 接口 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.OnMapClickListener.html" title="com.amap.api.maps中的接口">AMap.OnMapClickListener</a></dt>
<dd>
<div class="block">当用户点击地图时回调此方法，如果点击在某个覆盖物（如marker、polyline）上，且处理了该点击事件，则不会回调此方法。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.OnMapLoadedListener.html#onMapLoaded--">onMapLoaded()</a></span> - 接口 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.OnMapLoadedListener.html" title="com.amap.api.maps中的接口">AMap.OnMapLoadedListener</a></dt>
<dd>
<div class="block">当地图加载完成后回调此方法</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.OnMapLongClickListener.html#onMapLongClick-com.amap.api.maps.model.LatLng-">onMapLongClick(LatLng)</a></span> - 接口 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.OnMapLongClickListener.html" title="com.amap.api.maps中的接口">AMap.OnMapLongClickListener</a></dt>
<dd>
<div class="block">当用户长按地图时回调此方法，如果长按时触发某一个marker对象的拖动事件，则不会回调此方法。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.onMapPrintScreenListener.html#onMapPrint-android.graphics.drawable.Drawable-">onMapPrint(Drawable)</a></span> - 接口 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.onMapPrintScreenListener.html" title="com.amap.api.maps中的接口">AMap.onMapPrintScreenListener</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">地图截屏回调此方法。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/CustomRenderer.html#OnMapReferencechanged--">OnMapReferencechanged()</a></span> - 接口 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/CustomRenderer.html" title="com.amap.api.maps中的接口">CustomRenderer</a></dt>
<dd>
<div class="block">地图坐标系统刷新，需要重新计算坐标</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.OnMapScreenShotListener.html#onMapScreenShot-android.graphics.Bitmap-">onMapScreenShot(Bitmap)</a></span> - 接口 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.OnMapScreenShotListener.html" title="com.amap.api.maps中的接口">AMap.OnMapScreenShotListener</a></dt>
<dd>
<div class="block">地图截屏回调此方法。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.OnMapScreenShotListener.html#onMapScreenShot-android.graphics.Bitmap-int-">onMapScreenShot(Bitmap, int)</a></span> - 接口 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.OnMapScreenShotListener.html" title="com.amap.api.maps中的接口">AMap.OnMapScreenShotListener</a></dt>
<dd>
<div class="block">地图截屏回调此方法,并返回截屏一瞬间地图是否渲染完成。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/AMapGestureListener.html#onMapStable--">onMapStable()</a></span> - 接口 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/AMapGestureListener.html" title="com.amap.api.maps.model中的接口">AMapGestureListener</a></dt>
<dd>
<div class="block">地图稳定下来会回到此接口</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.OnMapSnapshotListener.html#onMapTile-android.graphics.Rect-android.graphics.Bitmap-int-">onMapTile(Rect, Bitmap, int)</a></span> - 接口 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.OnMapSnapshotListener.html" title="com.amap.api.maps中的接口">AMap.OnMapSnapshotListener</a></dt>
<dd>
<div class="block">地图截取区域图，多次回调此方法,并返回截屏一瞬间地图是否渲染完成。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.OnMarkerClickListener.html#onMarkerClick-com.amap.api.maps.model.Marker-">onMarkerClick(Marker)</a></span> - 接口 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.OnMarkerClickListener.html" title="com.amap.api.maps中的接口">AMap.OnMarkerClickListener</a></dt>
<dd>
<div class="block">当一个marker对象被点击时回调此方法。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.OnMarkerDragListener.html#onMarkerDrag-com.amap.api.maps.model.Marker-">onMarkerDrag(Marker)</a></span> - 接口 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.OnMarkerDragListener.html" title="com.amap.api.maps中的接口">AMap.OnMarkerDragListener</a></dt>
<dd>
<div class="block">在marker拖动过程当中回调此方法。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.OnMarkerDragListener.html#onMarkerDragEnd-com.amap.api.maps.model.Marker-">onMarkerDragEnd(Marker)</a></span> - 接口 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.OnMarkerDragListener.html" title="com.amap.api.maps中的接口">AMap.OnMarkerDragListener</a></dt>
<dd>
<div class="block">在marker拖动停止时回调此方法。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.OnMarkerDragListener.html#onMarkerDragStart-com.amap.api.maps.model.Marker-">onMarkerDragStart(Marker)</a></span> - 接口 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.OnMarkerDragListener.html" title="com.amap.api.maps中的接口">AMap.OnMarkerDragListener</a></dt>
<dd>
<div class="block">当marker开始被拖动时回调此方法。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/CityExpandView.html#onMeasure-int-int-">onMeasure(int, int)</a></span> - 类 中的方法com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/CityExpandView.html" title="com.amap.api.maps.offlinemap中的类">CityExpandView</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/DownLoadExpandListView.html#onMeasure-int-int-">onMeasure(int, int)</a></span> - 类 中的方法com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/DownLoadExpandListView.html" title="com.amap.api.maps.offlinemap中的类">DownLoadExpandListView</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/DownLoadListView.html#onMeasure-int-int-">onMeasure(int, int)</a></span> - 类 中的方法com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/DownLoadListView.html" title="com.amap.api.maps.offlinemap中的类">DownLoadListView</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.OnMyLocationChangeListener.html#onMyLocationChange-android.location.Location-">onMyLocationChange(Location)</a></span> - 接口 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.OnMyLocationChangeListener.html" title="com.amap.api.maps中的接口">AMap.OnMyLocationChangeListener</a></dt>
<dd>
<div class="block">当用户定位信息改变时回调此方法。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/WearMapView.OnDismissCallback.html#onNotifySwipe--">onNotifySwipe()</a></span> - 接口 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/WearMapView.OnDismissCallback.html" title="com.amap.api.maps中的接口">WearMapView.OnDismissCallback</a></dt>
<dd>
<div class="block">开始滑动时回调</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/MapFragment.html#onPause--">onPause()</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/MapFragment.html" title="com.amap.api.maps中的类">MapFragment</a></dt>
<dd>
<div class="block">用户重载这个方法时必须调用父类的这个方法，暂停地图刷新。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/MapView.html#onPause--">onPause()</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/MapView.html" title="com.amap.api.maps中的类">MapView</a></dt>
<dd>
<div class="block">用户重载这个方法时必须调用父类的这个方法。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/OfflineMapActivity.html#onPause--">onPause()</a></span> - 类 中的方法com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/OfflineMapActivity.html" title="com.amap.api.maps.offlinemap中的类">OfflineMapActivity</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/TextureMapView.html#onPause--">onPause()</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/TextureMapView.html" title="com.amap.api.maps中的类">TextureMapView</a></dt>
<dd>
<div class="block">当Activity暂停的时候调用地图暂停。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/WearMapView.html#onPause--">onPause()</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/WearMapView.html" title="com.amap.api.maps中的类">WearMapView</a></dt>
<dd>
<div class="block">暂停WearMapView刷新，必须调用。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.OnPOIClickListener.html#onPOIClick-com.amap.api.maps.model.Poi-">onPOIClick(Poi)</a></span> - 接口 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.OnPOIClickListener.html" title="com.amap.api.maps中的接口">AMap.OnPOIClickListener</a></dt>
<dd>
<div class="block">当用户点击底图上的poi时回调此方法。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.OnMultiPointClickListener.html#onPointClick-com.amap.api.maps.model.MultiPointItem-">onPointClick(MultiPointItem)</a></span> - 接口 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.OnMultiPointClickListener.html" title="com.amap.api.maps中的接口">AMap.OnMultiPointClickListener</a></dt>
<dd>
<div class="block">海量点中某一点被点击时回调此方法。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.OnPolylineClickListener.html#onPolylineClick-com.amap.api.maps.model.Polyline-">onPolylineClick(Polyline)</a></span> - 接口 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.OnPolylineClickListener.html" title="com.amap.api.maps中的接口">AMap.OnPolylineClickListener</a></dt>
<dd>
<div class="block">当一个polyline对象被点击时调用此方法。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/OfflineMapManager.OfflineMapDownloadListener.html#onRemove-boolean-java.lang.String-java.lang.String-">onRemove(boolean, String, String)</a></span> - 接口 中的方法com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/OfflineMapManager.OfflineMapDownloadListener.html" title="com.amap.api.maps.offlinemap中的接口">OfflineMapManager.OfflineMapDownloadListener</a></dt>
<dd>
<div class="block">当调用<a href="../com/amap/api/maps/offlinemap/OfflineMapManager.html#remove-java.lang.String-"><code>OfflineMapManager.remove(String)</code></a>方法时，如果有设置监听，会回调此方法<br>
 当删除省份时，该方法会被调用多次，返回省份内城市删除情况。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.OnCacheRemoveListener.html#onRemoveCacheFinish-boolean-">onRemoveCacheFinish(boolean)</a></span> - 接口 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.OnCacheRemoveListener.html" title="com.amap.api.maps中的接口">AMap.OnCacheRemoveListener</a></dt>
<dd>
<div class="block">删除缓存执行之后回调此方法</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.AMapAppResourceRequestListener.html#onRequest-AMapAppRequestParam-">onRequest(AMapAppRequestParam)</a></span> - 接口 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.AMapAppResourceRequestListener.html" title="com.amap.api.maps中的接口">AMap.AMapAppResourceRequestListener</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/trace/TraceListener.html#onRequestFailed-int-java.lang.String-">onRequestFailed(int, String)</a></span> - 接口 中的方法com.amap.api.trace.<a href="../com/amap/api/trace/TraceListener.html" title="com.amap.api.trace中的接口">TraceListener</a></dt>
<dd>
<div class="block">轨迹纠偏失败回调</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/MapFragment.html#onResume--">onResume()</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/MapFragment.html" title="com.amap.api.maps中的类">MapFragment</a></dt>
<dd>
<div class="block">用户重载这个方法时必须调用父类的这个方法，用来启动地图刷新。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/MapView.html#onResume--">onResume()</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/MapView.html" title="com.amap.api.maps中的类">MapView</a></dt>
<dd>
<div class="block">当Activity暂停的时候调用地图暂停。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/OfflineMapActivity.html#onResume--">onResume()</a></span> - 类 中的方法com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/OfflineMapActivity.html" title="com.amap.api.maps.offlinemap中的类">OfflineMapActivity</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/TextureMapView.html#onResume--">onResume()</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/TextureMapView.html" title="com.amap.api.maps中的类">TextureMapView</a></dt>
<dd>
<div class="block">当Activity唤醒时调用地图唤醒。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/WearMapView.html#onResume--">onResume()</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/WearMapView.html" title="com.amap.api.maps中的类">WearMapView</a></dt>
<dd>
<div class="block">启动WearMapView刷新，必须调用。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/MapFragment.html#onSaveInstanceState-android.os.Bundle-">onSaveInstanceState(Bundle)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/MapFragment.html" title="com.amap.api.maps中的类">MapFragment</a></dt>
<dd>
<div class="block">用户重载这个方法时必须调用父类的这个方法，保存地图状态。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/MapView.html#onSaveInstanceState-android.os.Bundle-">onSaveInstanceState(Bundle)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/MapView.html" title="com.amap.api.maps中的类">MapView</a></dt>
<dd>
<div class="block">用户重载这个方法时必须调用父类的这个方法，用于MapView保存地图状态。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/TextureMapView.html#onSaveInstanceState-android.os.Bundle-">onSaveInstanceState(Bundle)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/TextureMapView.html" title="com.amap.api.maps中的类">TextureMapView</a></dt>
<dd>
<div class="block">用户重载这个方法时必须调用父类的这个方法 用于MapView保存地图状态。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/WearMapView.html#onSaveInstanceState-android.os.Bundle-">onSaveInstanceState(Bundle)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/WearMapView.html" title="com.amap.api.maps中的类">WearMapView</a></dt>
<dd>
<div class="block">保持WearMapView状态。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/AMapGestureListener.html#onScroll-float-float-">onScroll(float, float)</a></span> - 接口 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/AMapGestureListener.html" title="com.amap.api.maps.model中的接口">AMapGestureListener</a></dt>
<dd>
<div class="block">滑动</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/AMapGestureListener.html#onSingleTap-float-float-">onSingleTap(float, float)</a></span> - 接口 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/AMapGestureListener.html" title="com.amap.api.maps.model中的接口">AMapGestureListener</a></dt>
<dd>
<div class="block">单击</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/OfflineMapActivity.html#onStart--">onStart()</a></span> - 类 中的方法com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/OfflineMapActivity.html" title="com.amap.api.maps.offlinemap中的类">OfflineMapActivity</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/OfflineMapActivity.html#onStop--">onStop()</a></span> - 类 中的方法com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/OfflineMapActivity.html" title="com.amap.api.maps.offlinemap中的类">OfflineMapActivity</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.OnMapTouchListener.html#onTouch-android.view.MotionEvent-">onTouch(MotionEvent)</a></span> - 接口 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.OnMapTouchListener.html" title="com.amap.api.maps中的接口">AMap.OnMapTouchListener</a></dt>
<dd>
<div class="block">当用户触摸地图时回调此方法</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/trace/TraceListener.html#onTraceProcessing-int-int-java.util.List-">onTraceProcessing(int, int, List&lt;LatLng&gt;)</a></span> - 接口 中的方法com.amap.api.trace.<a href="../com/amap/api/trace/TraceListener.html" title="com.amap.api.trace中的接口">TraceListener</a></dt>
<dd>
<div class="block">轨迹纠偏过程回调，一条轨迹分割为多个段，按索引顺序回调其中一段</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/trace/TraceStatusListener.html#onTraceStatus-java.util.List-java.util.List-java.lang.String-">onTraceStatus(List&lt;TraceLocation&gt;, List&lt;LatLng&gt;, String)</a></span> - 接口 中的方法com.amap.api.trace.<a href="../com/amap/api/trace/TraceStatusListener.html" title="com.amap.api.trace中的接口">TraceStatusListener</a></dt>
<dd>
<div class="block">轨迹记录状态回调</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/AMapGestureListener.html#onUp-float-float-">onUp(float, float)</a></span> - 接口 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/AMapGestureListener.html" title="com.amap.api.maps.model中的接口">AMapGestureListener</a></dt>
<dd>
<div class="block">抬起</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/CrossOverlay.OnCrossVectorUpdateListener.html#onUpdate-int-com.amap.api.maps.model.CrossOverlay.UpdateItem-">onUpdate(int, CrossOverlay.UpdateItem)</a></span> - 接口 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/CrossOverlay.OnCrossVectorUpdateListener.html" title="com.amap.api.maps.model中的接口">CrossOverlay.OnCrossVectorUpdateListener</a></dt>
<dd>
<div class="block">更新回调</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/OfflineMapManager.OfflineLoadedListener.html#onVerifyComplete--">onVerifyComplete()</a></span> - 接口 中的方法com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/OfflineMapManager.OfflineLoadedListener.html" title="com.amap.api.maps.offlinemap中的接口">OfflineMapManager.OfflineLoadedListener</a></dt>
<dd>
<div class="block">当调用<a href="../com/amap/api/maps/offlinemap/OfflineMapManager.html" title="com.amap.api.maps.offlinemap中的类"><code>OfflineMapManager</code></a>初始化方法时，如果有设置监听，离线地图列表初始化完成会回调此方法<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/HeatMapLayerOptions.html#opacity-float-">opacity(float)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/HeatMapLayerOptions.html" title="com.amap.api.maps.model中的类">HeatMapLayerOptions</a></dt>
<dd>
<div class="block">设置热力图层透明度，默认 0.6，可不设置该接口</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMapUtils.html#openAMapDrivingRoute-com.amap.api.maps.model.RoutePara-android.content.Context-">openAMapDrivingRoute(RoutePara, Context)</a></span> - 类 中的静态方法com.amap.api.maps.<a href="../com/amap/api/maps/AMapUtils.html" title="com.amap.api.maps中的类">AMapUtils</a></dt>
<dd>
<div class="block">调起高德地图APP进行驾车路线规划。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMapUtils.html#openAMapNavi-com.amap.api.maps.model.NaviPara-android.content.Context-">openAMapNavi(NaviPara, Context)</a></span> - 类 中的静态方法com.amap.api.maps.<a href="../com/amap/api/maps/AMapUtils.html" title="com.amap.api.maps中的类">AMapUtils</a></dt>
<dd>
<div class="block">调起高德地图APP进行导航。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMapUtils.html#openAMapPoiNearbySearch-com.amap.api.maps.model.PoiPara-android.content.Context-">openAMapPoiNearbySearch(PoiPara, Context)</a></span> - 类 中的静态方法com.amap.api.maps.<a href="../com/amap/api/maps/AMapUtils.html" title="com.amap.api.maps中的类">AMapUtils</a></dt>
<dd>
<div class="block">调起高德地图APP进行poi周边检索。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMapUtils.html#openAMapTransitRoute-com.amap.api.maps.model.RoutePara-android.content.Context-">openAMapTransitRoute(RoutePara, Context)</a></span> - 类 中的静态方法com.amap.api.maps.<a href="../com/amap/api/maps/AMapUtils.html" title="com.amap.api.maps中的类">AMapUtils</a></dt>
<dd>
<div class="block">调起高德地图APP进行公交路线规划。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMapUtils.html#openAMapWalkingRoute-com.amap.api.maps.model.RoutePara-android.content.Context-">openAMapWalkingRoute(RoutePara, Context)</a></span> - 类 中的静态方法com.amap.api.maps.<a href="../com/amap/api/maps/AMapUtils.html" title="com.amap.api.maps中的类">AMapUtils</a></dt>
<dd>
<div class="block">调起高德地图APP进行步行路线规划。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/TileOverlay.html#options">options</a></span> - 类 中的变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/TileOverlay.html" title="com.amap.api.maps.model中的类">TileOverlay</a></dt>
<dd>&nbsp;</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">K</a>&nbsp;<a href="index-11.html">L</a>&nbsp;<a href="index-12.html">M</a>&nbsp;<a href="index-13.html">N</a>&nbsp;<a href="index-14.html">O</a>&nbsp;<a href="index-15.html">P</a>&nbsp;<a href="index-16.html">Q</a>&nbsp;<a href="index-17.html">R</a>&nbsp;<a href="index-18.html">S</a>&nbsp;<a href="index-19.html">T</a>&nbsp;<a href="index-20.html">U</a>&nbsp;<a href="index-21.html">V</a>&nbsp;<a href="index-22.html">W</a>&nbsp;<a href="index-23.html">Z</a>&nbsp;</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-13.html">上一个字母</a></li>
<li><a href="index-15.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-14.html" target="_top">框架</a></li>
<li><a href="index-14.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
