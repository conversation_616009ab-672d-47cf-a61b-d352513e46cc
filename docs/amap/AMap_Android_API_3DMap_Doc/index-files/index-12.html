<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>M - 索引</title>
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="M - \u7D22\u5F15";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-11.html">上一个字母</a></li>
<li><a href="index-13.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-12.html" target="_top">框架</a></li>
<li><a href="index-12.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="contentContainer"><a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">K</a>&nbsp;<a href="index-11.html">L</a>&nbsp;<a href="index-12.html">M</a>&nbsp;<a href="index-13.html">N</a>&nbsp;<a href="index-14.html">O</a>&nbsp;<a href="index-15.html">P</a>&nbsp;<a href="index-16.html">Q</a>&nbsp;<a href="index-17.html">R</a>&nbsp;<a href="index-18.html">S</a>&nbsp;<a href="index-19.html">T</a>&nbsp;<a href="index-20.html">U</a>&nbsp;<a href="index-21.html">V</a>&nbsp;<a href="index-22.html">W</a>&nbsp;<a href="index-23.html">Z</a>&nbsp;<a name="I:M">
<!--   -->
</a>
<h2 class="title">M</h2>
<dl>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#MAP_TYPE_BUS">MAP_TYPE_BUS</a></span> - 类 中的静态变量com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block">公交模式常量。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#MAP_TYPE_NAVI">MAP_TYPE_NAVI</a></span> - 类 中的静态变量com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block">导航模式常量。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#MAP_TYPE_NIGHT">MAP_TYPE_NIGHT</a></span> - 类 中的静态变量com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block">夜景图模式常量。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#MAP_TYPE_NORMAL">MAP_TYPE_NORMAL</a></span> - 类 中的静态变量com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block">普通地图模式常量。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#MAP_TYPE_SATELLITE">MAP_TYPE_SATELLITE</a></span> - 类 中的静态变量com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block">卫星图模式常量。</div>
</dd>
<dt><a href="../com/amap/api/maps/MapFragment.html" title="com.amap.api.maps中的类"><span class="typeNameLink">MapFragment</span></a> - <a href="../com/amap/api/maps/package-summary.html">com.amap.api.maps</a>中的类</dt>
<dd>
<div class="block">MapFragment 类, 管理地图生命周期。</div>
</dd>
<dt><a href="../com/amap/api/maps/MapsInitializer.html" title="com.amap.api.maps中的类"><span class="typeNameLink">MapsInitializer</span></a> - <a href="../com/amap/api/maps/package-summary.html">com.amap.api.maps</a>中的类</dt>
<dd>
<div class="block">初始化 SDK context 全局变量，指定 sdcard 路径，设置鉴权所需的KEY。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/MapsInitializer.html#MapsInitializer--">MapsInitializer()</a></span> - 类 的构造器com.amap.api.maps.<a href="../com/amap/api/maps/MapsInitializer.html" title="com.amap.api.maps中的类">MapsInitializer</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMapOptions.html#mapType-int-">mapType(int)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMapOptions.html" title="com.amap.api.maps中的类">AMapOptions</a></dt>
<dd>
<div class="block">设置地图模式，默认普通地图。</div>
</dd>
<dt><a href="../com/amap/api/maps/MapView.html" title="com.amap.api.maps中的类"><span class="typeNameLink">MapView</span></a> - <a href="../com/amap/api/maps/package-summary.html">com.amap.api.maps</a>中的类</dt>
<dd>
<div class="block">一个显示地图的视图（View），它负责从服务端获取地图数据，它将会捕捉屏幕触控手势事件。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/MapView.html#MapView-android.content.Context-">MapView(Context)</a></span> - 类 的构造器com.amap.api.maps.<a href="../com/amap/api/maps/MapView.html" title="com.amap.api.maps中的类">MapView</a></dt>
<dd>
<div class="block">根据给定的参数构造一个MapView 的新对象。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/MapView.html#MapView-android.content.Context-android.util.AttributeSet-">MapView(Context, AttributeSet)</a></span> - 类 的构造器com.amap.api.maps.<a href="../com/amap/api/maps/MapView.html" title="com.amap.api.maps中的类">MapView</a></dt>
<dd>
<div class="block">根据给定的参数构造一个MapView 的新对象。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/MapView.html#MapView-android.content.Context-android.util.AttributeSet-int-">MapView(Context, AttributeSet, int)</a></span> - 类 的构造器com.amap.api.maps.<a href="../com/amap/api/maps/MapView.html" title="com.amap.api.maps中的类">MapView</a></dt>
<dd>
<div class="block">根据给定的参数构造一个MapView 的新对象。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/MapView.html#MapView-android.content.Context-com.amap.api.maps.AMapOptions-">MapView(Context, AMapOptions)</a></span> - 类 的构造器com.amap.api.maps.<a href="../com/amap/api/maps/MapView.html" title="com.amap.api.maps中的类">MapView</a></dt>
<dd>
<div class="block">根据给定的参数构造一个MapView 的新对象。</div>
</dd>
<dt><a href="../com/amap/api/maps/model/Marker.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">Marker</span></a> - <a href="../com/amap/api/maps/model/package-summary.html">com.amap.api.maps.model</a>中的类</dt>
<dd>
<div class="block">定义地图 Marker 覆盖物

 Marker 是在地图上的一个点绘制图标。</div>
</dd>
<dt><a href="../com/amap/api/maps/model/MarkerOptions.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">MarkerOptions</span></a> - <a href="../com/amap/api/maps/model/package-summary.html">com.amap.api.maps.model</a>中的类</dt>
<dd>
<div class="block">Marker 的选项类。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MarkerOptions.html#MarkerOptions--">MarkerOptions()</a></span> - 类 的构造器com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MarkerOptions.html" title="com.amap.api.maps.model中的类">MarkerOptions</a></dt>
<dd>
<div class="block">创建MarkerOptions对象。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/HeatMapLayerOptions.html#maxIntensity-double-">maxIntensity(double)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/HeatMapLayerOptions.html" title="com.amap.api.maps.model中的类">HeatMapLayerOptions</a></dt>
<dd>
<div class="block">权重的最大值，默认为0，表示不填，不填则取数据集权重最大值
 对颜色选择有影响，计算颜色比例是，权重最大使用此接口设置的值</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/TileProjection.html#maxX">maxX</a></span> - 类 中的变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/TileProjection.html" title="com.amap.api.maps.model中的类">TileProjection</a></dt>
<dd>
<div class="block">瓦片编号范围最大X。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/TileProjection.html#maxY">maxY</a></span> - 类 中的变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/TileProjection.html" title="com.amap.api.maps.model中的类">TileProjection</a></dt>
<dd>
<div class="block">瓦片编号范围最大Y。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/HeatMapGridLayerOptions.html#maxZoom-float-">maxZoom(float)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/HeatMapGridLayerOptions.html" title="com.amap.api.maps.model中的类">HeatMapGridLayerOptions</a></dt>
<dd>
<div class="block">最大缩放级别</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/HeatMapLayerOptions.html#maxZoom-float-">maxZoom(float)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/HeatMapLayerOptions.html" title="com.amap.api.maps.model中的类">HeatMapLayerOptions</a></dt>
<dd>
<div class="block">最大缩放级别</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/BitmapDescriptor.html#mBitmap">mBitmap</a></span> - 类 中的变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/BitmapDescriptor.html" title="com.amap.api.maps.model中的类">BitmapDescriptor</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/TileOverlayOptions.html#memCacheSize-int-">memCacheSize(int)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/TileOverlayOptions.html" title="com.amap.api.maps.model中的类">TileOverlayOptions</a></dt>
<dd>
<div class="block">设置用于瓦片图层的内存缓存大小，默认值5MB。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/TileOverlayOptions.html#memoryCacheEnabled-boolean-">memoryCacheEnabled(boolean)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/TileOverlayOptions.html" title="com.amap.api.maps.model中的类">TileOverlayOptions</a></dt>
<dd>
<div class="block">设置瓦片图层是否开启内存缓存。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/trace/LBSTraceClient.html#MIN_GRASP_POINT_ERROR">MIN_GRASP_POINT_ERROR</a></span> - 类 中的静态变量com.amap.api.trace.<a href="../com/amap/api/trace/LBSTraceClient.html" title="com.amap.api.trace中的类">LBSTraceClient</a></dt>
<dd>
<div class="block">轨迹纠偏错误信息，轨迹点太少或距离太近,轨迹纠偏失败</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/utils/SpatialRelationUtil.html#MIN_OFFSET_DEGREE">MIN_OFFSET_DEGREE</a></span> - 类 中的静态变量com.amap.api.maps.utils.<a href="../com/amap/api/maps/utils/SpatialRelationUtil.html" title="com.amap.api.maps.utils中的类">SpatialRelationUtil</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/utils/overlay/SmoothMoveMarker.html#MIN_OFFSET_DISTANCE">MIN_OFFSET_DISTANCE</a></span> - 类 中的静态变量com.amap.api.maps.utils.overlay.<a href="../com/amap/api/maps/utils/overlay/SmoothMoveMarker.html" title="com.amap.api.maps.utils.overlay中的类">SmoothMoveMarker</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/utils/SpatialRelationUtil.html#MIN_POLYLINE_POINT_SIZE">MIN_POLYLINE_POINT_SIZE</a></span> - 类 中的静态变量com.amap.api.maps.utils.<a href="../com/amap/api/maps/utils/SpatialRelationUtil.html" title="com.amap.api.maps.utils中的类">SpatialRelationUtil</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/TileProjection.html#minX">minX</a></span> - 类 中的变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/TileProjection.html" title="com.amap.api.maps.model中的类">TileProjection</a></dt>
<dd>
<div class="block">瓦片编号范围最小X。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/TileProjection.html#minY">minY</a></span> - 类 中的变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/TileProjection.html" title="com.amap.api.maps.model中的类">TileProjection</a></dt>
<dd>
<div class="block">瓦片编号范围最小Y。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/HeatMapGridLayerOptions.html#minZoom-float-">minZoom(float)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/HeatMapGridLayerOptions.html" title="com.amap.api.maps.model中的类">HeatMapGridLayerOptions</a></dt>
<dd>
<div class="block">最小缩放级别</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/HeatMapLayerOptions.html#minZoom-float-">minZoom(float)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/HeatMapLayerOptions.html" title="com.amap.api.maps.model中的类">HeatMapLayerOptions</a></dt>
<dd>
<div class="block">最小缩放级别</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/GLTFOverlayOptions.html#modelData-byte:A-">modelData(byte[])</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/GLTFOverlayOptions.html" title="com.amap.api.maps.model中的类">GLTFOverlayOptions</a></dt>
<dd>
<div class="block">设置glTF的模型数据</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/utils/overlay/MovingPointOverlay.MoveListener.html#move-double-">move(double)</a></span> - 接口 中的方法com.amap.api.maps.utils.overlay.<a href="../com/amap/api/maps/utils/overlay/MovingPointOverlay.MoveListener.html" title="com.amap.api.maps.utils.overlay中的接口">MovingPointOverlay.MoveListener</a></dt>
<dd>
<div class="block">返回 距离终点的距离</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/utils/overlay/SmoothMoveMarker.MoveListener.html#move-double-">move(double)</a></span> - 接口 中的方法com.amap.api.maps.utils.overlay.<a href="../com/amap/api/maps/utils/overlay/SmoothMoveMarker.MoveListener.html" title="com.amap.api.maps.utils.overlay中的接口">SmoothMoveMarker.MoveListener</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">返回 距离终点的距离</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#moveCamera-com.amap.api.maps.CameraUpdate-">moveCamera(CameraUpdate)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block">按照传入的CameraUpdate参数改变地图状态。</div>
</dd>
<dt><a href="../com/amap/api/maps/utils/overlay/MovingPointOverlay.html" title="com.amap.api.maps.utils.overlay中的类"><span class="typeNameLink">MovingPointOverlay</span></a> - <a href="../com/amap/api/maps/utils/overlay/package-summary.html">com.amap.api.maps.utils.overlay</a>中的类</dt>
<dd>
<div class="block">按照指定的经纬度数据和时间，平滑移动</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/utils/overlay/MovingPointOverlay.html#MovingPointOverlay-com.amap.api.maps.AMap-com.amap.api.maps.model.BasePointOverlay-">MovingPointOverlay(AMap, BasePointOverlay)</a></span> - 类 的构造器com.amap.api.maps.utils.overlay.<a href="../com/amap/api/maps/utils/overlay/MovingPointOverlay.html" title="com.amap.api.maps.utils.overlay中的类">MovingPointOverlay</a></dt>
<dd>
<div class="block">根据给定的参数来构造SmoothMoveMarker对象。</div>
</dd>
<dt><a href="../com/amap/api/maps/utils/overlay/MovingPointOverlay.MoveListener.html" title="com.amap.api.maps.utils.overlay中的接口"><span class="typeNameLink">MovingPointOverlay.MoveListener</span></a> - <a href="../com/amap/api/maps/utils/overlay/package-summary.html">com.amap.api.maps.utils.overlay</a>中的接口</dt>
<dd>
<div class="block">平滑移动时返回剩余距离接口</div>
</dd>
<dt><a href="../com/amap/api/maps/model/MultiPointItem.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">MultiPointItem</span></a> - <a href="../com/amap/api/maps/model/package-summary.html">com.amap.api.maps.model</a>中的类</dt>
<dd>
<div class="block">海量点中单个点的属性</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MultiPointItem.html#MultiPointItem-com.amap.api.maps.model.LatLng-">MultiPointItem(LatLng)</a></span> - 类 的构造器com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MultiPointItem.html" title="com.amap.api.maps.model中的类">MultiPointItem</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/amap/api/maps/model/MultiPointOverlay.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">MultiPointOverlay</span></a> - <a href="../com/amap/api/maps/model/package-summary.html">com.amap.api.maps.model</a>中的类</dt>
<dd>
<div class="block">海量点 管理对象<br>
 可以将大量点展示在地图上</div>
</dd>
<dt><a href="../com/amap/api/maps/model/MultiPointOverlayOptions.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">MultiPointOverlayOptions</span></a> - <a href="../com/amap/api/maps/model/package-summary.html">com.amap.api.maps.model</a>中的类</dt>
<dd>
<div class="block">海量点 的选项类。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MultiPointOverlayOptions.html#MultiPointOverlayOptions--">MultiPointOverlayOptions()</a></span> - 类 的构造器com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MultiPointOverlayOptions.html" title="com.amap.api.maps.model中的类">MultiPointOverlayOptions</a></dt>
<dd>
<div class="block">创建PolygonOptions对象。</div>
</dd>
<dt><a href="../com/amap/api/maps/model/MVTTileOverlay.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">MVTTileOverlay</span></a> - <a href="../com/amap/api/maps/model/package-summary.html">com.amap.api.maps.model</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MVTTileOverlay.html#MVTTileOverlay-IGlOverlayLayer-com.amap.api.maps.model.MVTTileOverlayOptions-java.lang.String-">MVTTileOverlay(IGlOverlayLayer, MVTTileOverlayOptions, String)</a></span> - 类 的构造器com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MVTTileOverlay.html" title="com.amap.api.maps.model中的类">MVTTileOverlay</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/amap/api/maps/model/MVTTileOverlayOptions.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">MVTTileOverlayOptions</span></a> - <a href="../com/amap/api/maps/model/package-summary.html">com.amap.api.maps.model</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MVTTileOverlayOptions.html#MVTTileOverlayOptions-java.lang.String-java.lang.String-java.lang.String-">MVTTileOverlayOptions(String, String, String)</a></span> - 类 的构造器com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MVTTileOverlayOptions.html" title="com.amap.api.maps.model中的类">MVTTileOverlayOptions</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/amap/api/maps/model/MVTTileOverlayOptions.Builder.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">MVTTileOverlayOptions.Builder</span></a> - <a href="../com/amap/api/maps/model/package-summary.html">com.amap.api.maps.model</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><a href="../com/amap/api/maps/model/MVTTileProvider.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">MVTTileProvider</span></a> - <a href="../com/amap/api/maps/model/package-summary.html">com.amap.api.maps.model</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MVTTileProvider.html#MVTTileProvider-java.lang.String-java.lang.String-java.lang.String-">MVTTileProvider(String, String, String)</a></span> - 类 的构造器com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MVTTileProvider.html" title="com.amap.api.maps.model中的类">MVTTileProvider</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MyLocationStyle.html#myLocationIcon-com.amap.api.maps.model.BitmapDescriptor-">myLocationIcon(BitmapDescriptor)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MyLocationStyle.html" title="com.amap.api.maps.model中的类">MyLocationStyle</a></dt>
<dd>
<div class="block">设置定位（当前位置）的icon图标。</div>
</dd>
<dt><a href="../com/amap/api/maps/model/MyLocationStyle.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">MyLocationStyle</span></a> - <a href="../com/amap/api/maps/model/package-summary.html">com.amap.api.maps.model</a>中的类</dt>
<dd>
<div class="block">定位小蓝点（当前位置）的绘制样式类。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MyLocationStyle.html#MyLocationStyle--">MyLocationStyle()</a></span> - 类 的构造器com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MyLocationStyle.html" title="com.amap.api.maps.model中的类">MyLocationStyle</a></dt>
<dd>
<div class="block">定位（当前位置）的绘制样式类的构造函数</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MyLocationStyle.html#myLocationType-int-">myLocationType(int)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MyLocationStyle.html" title="com.amap.api.maps.model中的类">MyLocationStyle</a></dt>
<dd>
<div class="block">设置我的位置展示模式，模式分别为 <br>
 <a href="../com/amap/api/maps/model/MyLocationStyle.html#LOCATION_TYPE_SHOW"><code>MyLocationStyle.LOCATION_TYPE_SHOW</code></a>
 <a href="../com/amap/api/maps/model/MyLocationStyle.html#LOCATION_TYPE_LOCATE"><code>MyLocationStyle.LOCATION_TYPE_LOCATE</code></a>
 <a href="../com/amap/api/maps/model/MyLocationStyle.html#LOCATION_TYPE_MAP_ROTATE"><code>MyLocationStyle.LOCATION_TYPE_MAP_ROTATE</code></a>
 <a href="../com/amap/api/maps/model/MyLocationStyle.html#LOCATION_TYPE_LOCATION_ROTATE"><code>MyLocationStyle.LOCATION_TYPE_LOCATION_ROTATE</code></a> (默认)</div>
</dd>
<dt><a href="../com/amap/api/maps/model/MyTrafficStyle.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">MyTrafficStyle</span></a> - <a href="../com/amap/api/maps/model/package-summary.html">com.amap.api.maps.model</a>中的类</dt>
<dd>
<div class="block">路况拥堵情况对应的颜色<br>
 默认颜色分布为：<br>
 畅通： 0xff00a209<br>
 缓慢： 0xffff7508<br>
 拥堵： 0xffea0312<br>
 严重拥堵： 0xff92000a<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MyTrafficStyle.html#MyTrafficStyle--">MyTrafficStyle()</a></span> - 类 的构造器com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MyTrafficStyle.html" title="com.amap.api.maps.model中的类">MyTrafficStyle</a></dt>
<dd>&nbsp;</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">K</a>&nbsp;<a href="index-11.html">L</a>&nbsp;<a href="index-12.html">M</a>&nbsp;<a href="index-13.html">N</a>&nbsp;<a href="index-14.html">O</a>&nbsp;<a href="index-15.html">P</a>&nbsp;<a href="index-16.html">Q</a>&nbsp;<a href="index-17.html">R</a>&nbsp;<a href="index-18.html">S</a>&nbsp;<a href="index-19.html">T</a>&nbsp;<a href="index-20.html">U</a>&nbsp;<a href="index-21.html">V</a>&nbsp;<a href="index-22.html">W</a>&nbsp;<a href="index-23.html">Z</a>&nbsp;</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-11.html">上一个字母</a></li>
<li><a href="index-13.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-12.html" target="_top">框架</a></li>
<li><a href="index-12.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
