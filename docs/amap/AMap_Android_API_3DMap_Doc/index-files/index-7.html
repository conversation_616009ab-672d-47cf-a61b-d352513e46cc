<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>G - 索引</title>
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="G - \u7D22\u5F15";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-6.html">上一个字母</a></li>
<li><a href="index-8.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-7.html" target="_top">框架</a></li>
<li><a href="index-7.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="contentContainer"><a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">K</a>&nbsp;<a href="index-11.html">L</a>&nbsp;<a href="index-12.html">M</a>&nbsp;<a href="index-13.html">N</a>&nbsp;<a href="index-14.html">O</a>&nbsp;<a href="index-15.html">P</a>&nbsp;<a href="index-16.html">Q</a>&nbsp;<a href="index-17.html">R</a>&nbsp;<a href="index-18.html">S</a>&nbsp;<a href="index-19.html">T</a>&nbsp;<a href="index-20.html">U</a>&nbsp;<a href="index-21.html">V</a>&nbsp;<a href="index-22.html">W</a>&nbsp;<a href="index-23.html">Z</a>&nbsp;<a name="I:G">
<!--   -->
</a>
<h2 class="title">G</h2>
<dl>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/HeatMapLayerOptions.html#gap-float-">gap(float)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/HeatMapLayerOptions.html" title="com.amap.api.maps.model中的类">HeatMapLayerOptions</a></dt>
<dd>
<div class="block">设置间隔 网格和蜂巢时生效

    —— ——    —— ——
  丨     丨 丨     丨
  丨     丨 丨     丨
    —— ——    —— ——

  每个方框的宽就是 size（六边形同理）
  两个放款之间的间隔就是 gap （六边形同理）</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Gradient.html#generateColorMap-double-">generateColorMap(double)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Gradient.html" title="com.amap.api.maps.model中的类">Gradient</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/PolylineOptions.html#geodesic-boolean-">geodesic(boolean)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/PolylineOptions.html" title="com.amap.api.maps.model中的类">PolylineOptions</a></dt>
<dd>
<div class="block">设置线段是否为大地曲线，默认false，不画大地曲线。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/City.html#getAdcode--">getAdcode()</a></span> - 类 中的方法com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/City.html" title="com.amap.api.maps.offlinemap中的类">City</a></dt>
<dd>
<div class="block">得到城市的行政编码</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Text.html#getAlignX--">getAlignX()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Text.html" title="com.amap.api.maps.model中的类">Text</a></dt>
<dd>
<div class="block">获取文字覆盖物的水平对齐方式</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/TextOptions.html#getAlignX--">getAlignX()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/TextOptions.html" title="com.amap.api.maps.model中的类">TextOptions</a></dt>
<dd>
<div class="block">获取文字覆盖物的水平对齐方式</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Text.html#getAlignY--">getAlignY()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Text.html" title="com.amap.api.maps.model中的类">Text</a></dt>
<dd>
<div class="block">获取文字覆盖物的垂直对齐方式</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/TextOptions.html#getAlignY--">getAlignY()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/TextOptions.html" title="com.amap.api.maps.model中的类">TextOptions</a></dt>
<dd>
<div class="block">获取文字覆盖物的垂直对齐方式</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Marker.html#getAlpha--">getAlpha()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Marker.html" title="com.amap.api.maps.model中的类">Marker</a></dt>
<dd>
<div class="block">获取Marker覆盖物的透明度,透明度范围[0,1] 1为不透明,默认值为1</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MarkerOptions.html#getAlpha--">getAlpha()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MarkerOptions.html" title="com.amap.api.maps.model中的类">MarkerOptions</a></dt>
<dd>
<div class="block">获取Marker覆盖物的透明度,透明度范围[0,1] 1为不透明,默认值为1</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/GLTFOverlayOptions.html#getAltitude--">getAltitude()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/GLTFOverlayOptions.html" title="com.amap.api.maps.model中的类">GLTFOverlayOptions</a></dt>
<dd>
<div class="block">获取 模型距离地面的高度。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Marker.html#getAltitude--">getAltitude()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Marker.html" title="com.amap.api.maps.model中的类">Marker</a></dt>
<dd>
<div class="block">获取marker海拔</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MarkerOptions.html#getAltitude--">getAltitude()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MarkerOptions.html" title="com.amap.api.maps.model中的类">MarkerOptions</a></dt>
<dd>
<div class="block">获取marker海拔高度</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/GroundOverlayOptions.html#getAnchorU--">getAnchorU()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/GroundOverlayOptions.html" title="com.amap.api.maps.model中的类">GroundOverlayOptions</a></dt>
<dd>
<div class="block">从左边开始，水平方向的对齐方式，范围为[0,1]。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MarkerOptions.html#getAnchorU--">getAnchorU()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MarkerOptions.html" title="com.amap.api.maps.model中的类">MarkerOptions</a></dt>
<dd>
<div class="block">获取Marker覆盖物锚点在水平范围的比例。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MultiPointOverlayOptions.html#getAnchorU--">getAnchorU()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MultiPointOverlayOptions.html" title="com.amap.api.maps.model中的类">MultiPointOverlayOptions</a></dt>
<dd>
<div class="block">获取海量点锚点在水平范围的比例。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MyLocationStyle.html#getAnchorU--">getAnchorU()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MyLocationStyle.html" title="com.amap.api.maps.model中的类">MyLocationStyle</a></dt>
<dd>
<div class="block">得到锚点横坐标方向的偏移量。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/GroundOverlayOptions.html#getAnchorV--">getAnchorV()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/GroundOverlayOptions.html" title="com.amap.api.maps.model中的类">GroundOverlayOptions</a></dt>
<dd>
<div class="block">从上边开始，垂直方向的对齐方式，范围为[0,1]。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MarkerOptions.html#getAnchorV--">getAnchorV()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MarkerOptions.html" title="com.amap.api.maps.model中的类">MarkerOptions</a></dt>
<dd>
<div class="block">获取Marker覆盖物锚点垂直范围的比例。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MultiPointOverlayOptions.html#getAnchorV--">getAnchorV()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MultiPointOverlayOptions.html" title="com.amap.api.maps.model中的类">MultiPointOverlayOptions</a></dt>
<dd>
<div class="block">获取海量点锚点垂直范围的比例。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MyLocationStyle.html#getAnchorV--">getAnchorV()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MyLocationStyle.html" title="com.amap.api.maps.model中的类">MyLocationStyle</a></dt>
<dd>
<div class="block">得到锚点纵坐标方向的偏移量。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/animation/AlphaAnimation.html#getAnimationType--">getAnimationType()</a></span> - 类 中的方法com.amap.api.maps.model.animation.<a href="../com/amap/api/maps/model/animation/AlphaAnimation.html" title="com.amap.api.maps.model.animation中的类">AlphaAnimation</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/animation/AnimationSet.html#getAnimationType--">getAnimationType()</a></span> - 类 中的方法com.amap.api.maps.model.animation.<a href="../com/amap/api/maps/model/animation/AnimationSet.html" title="com.amap.api.maps.model.animation中的类">AnimationSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/animation/RotateAnimation.html#getAnimationType--">getAnimationType()</a></span> - 类 中的方法com.amap.api.maps.model.animation.<a href="../com/amap/api/maps/model/animation/RotateAnimation.html" title="com.amap.api.maps.model.animation中的类">RotateAnimation</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/animation/ScaleAnimation.html#getAnimationType--">getAnimationType()</a></span> - 类 中的方法com.amap.api.maps.model.animation.<a href="../com/amap/api/maps/model/animation/ScaleAnimation.html" title="com.amap.api.maps.model.animation中的类">ScaleAnimation</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/animation/TranslateAnimation.html#getAnimationType--">getAnimationType()</a></span> - 类 中的方法com.amap.api.maps.model.animation.<a href="../com/amap/api/maps/model/animation/TranslateAnimation.html" title="com.amap.api.maps.model.animation中的类">TranslateAnimation</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/AMapCameraInfo.html#getAspectRatio--">getAspectRatio()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/AMapCameraInfo.html" title="com.amap.api.maps.model中的类">AMapCameraInfo</a></dt>
<dd>
<div class="block">获取视窗纵横比</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Text.html#getBackgroundColor--">getBackgroundColor()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Text.html" title="com.amap.api.maps.model中的类">Text</a></dt>
<dd>
<div class="block">获取文字覆盖物的背景颜色</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/TextOptions.html#getBackgroundColor--">getBackgroundColor()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/TextOptions.html" title="com.amap.api.maps.model中的类">TextOptions</a></dt>
<dd>
<div class="block">获取文字覆盖物的背景颜色</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/GroundOverlay.html#getBearing--">getBearing()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/GroundOverlay.html" title="com.amap.api.maps.model中的类">GroundOverlay</a></dt>
<dd>
<div class="block">获取ground 覆盖物旋转的角度。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/GroundOverlayOptions.html#getBearing--">getBearing()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/GroundOverlayOptions.html" title="com.amap.api.maps.model中的类">GroundOverlayOptions</a></dt>
<dd>
<div class="block">获取 ground 覆盖物的角度。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/trace/TraceLocation.html#getBearing--">getBearing()</a></span> - 类 中的方法com.amap.api.trace.<a href="../com/amap/api/trace/TraceLocation.html" title="com.amap.api.trace中的类">TraceLocation</a></dt>
<dd>
<div class="block">获取方向角</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/BitmapDescriptor.html#getBitmap--">getBitmap()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/BitmapDescriptor.html" title="com.amap.api.maps.model中的类">BitmapDescriptor</a></dt>
<dd>
<div class="block">获取 对应的Bitmap 对象。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/GroundOverlay.html#getBounds--">getBounds()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/GroundOverlay.html" title="com.amap.api.maps.model中的类">GroundOverlay</a></dt>
<dd>
<div class="block">获取ground 覆盖物的地理坐标范围。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/GroundOverlayOptions.html#getBounds--">getBounds()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/GroundOverlayOptions.html" title="com.amap.api.maps.model中的类">GroundOverlayOptions</a></dt>
<dd>
<div class="block">获取 ground 覆盖物 的矩形区域 。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/BuildingOverlayOptions.html#getBuildingHeight--">getBuildingHeight()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/BuildingOverlayOptions.html" title="com.amap.api.maps.model中的类">BuildingOverlayOptions</a></dt>
<dd>
<div class="block">获取用户设置的建筑物高度。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/BuildingOverlayOptions.html#getBuildingHeightScale--">getBuildingHeightScale()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/BuildingOverlayOptions.html" title="com.amap.api.maps.model中的类">BuildingOverlayOptions</a></dt>
<dd>
<div class="block">获取overlay的建筑物相对于默认高度的倍数值</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/BuildingOverlayOptions.html#getBuildingLatlngs--">getBuildingLatlngs()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/BuildingOverlayOptions.html" title="com.amap.api.maps.model中的类">BuildingOverlayOptions</a></dt>
<dd>
<div class="block">获取建筑物围栏坐标列表</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/BuildingOverlayOptions.html#getBuildingSideColor--">getBuildingSideColor()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/BuildingOverlayOptions.html" title="com.amap.api.maps.model中的类">BuildingOverlayOptions</a></dt>
<dd>
<div class="block">获取建筑物侧面颜色值</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/BuildingOverlayOptions.html#getBuildingTopColor--">getBuildingTopColor()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/BuildingOverlayOptions.html" title="com.amap.api.maps.model中的类">BuildingOverlayOptions</a></dt>
<dd>
<div class="block">获取建筑物顶部颜色值</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMapOptions.html#getCamera--">getCamera()</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMapOptions.html" title="com.amap.api.maps中的类">AMapOptions</a></dt>
<dd>
<div class="block">获取初始化选项中地图状态。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#getCameraPosition--">getCameraPosition()</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block">获取地图的当前状态。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/CameraUpdate.html#getCameraUpdateFactoryDelegate--">getCameraUpdateFactoryDelegate()</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/CameraUpdate.html" title="com.amap.api.maps中的类">CameraUpdate</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Circle.html#getCenter--">getCenter()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Circle.html" title="com.amap.api.maps.model中的类">Circle</a></dt>
<dd>
<div class="block">获取圆心经纬度坐标。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/CircleHoleOptions.html#getCenter--">getCenter()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/CircleHoleOptions.html" title="com.amap.api.maps.model中的类">CircleHoleOptions</a></dt>
<dd>
<div class="block">获取洞圆心经纬度坐标</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/CircleOptions.html#getCenter--">getCenter()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/CircleOptions.html" title="com.amap.api.maps.model中的类">CircleOptions</a></dt>
<dd>
<div class="block">获取圆心经纬度坐标</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/HeatMapItem.html#getCenter--">getCenter()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/HeatMapItem.html" title="com.amap.api.maps.model中的类">HeatMapItem</a></dt>
<dd>
<div class="block">当前蜂窝或者网格中心点</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/PoiPara.html#getCenter--">getCenter()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/PoiPara.html" title="com.amap.api.maps.model中的类">PoiPara</a></dt>
<dd>
<div class="block">获取poi周边检索中心点</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/City.html#getCity--">getCity()</a></span> - 类 中的方法com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/City.html" title="com.amap.api.maps.offlinemap中的类">City</a></dt>
<dd>
<div class="block">返回城市名称。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/OfflineMapProvince.html#getCityList--">getCityList()</a></span> - 类 中的方法com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/OfflineMapProvince.html" title="com.amap.api.maps.offlinemap中的类">OfflineMapProvince</a></dt>
<dd>
<div class="block">得到当前省下所有的城市。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/City.html#getCode--">getCode()</a></span> - 类 中的方法com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/City.html" title="com.amap.api.maps.offlinemap中的类">City</a></dt>
<dd>
<div class="block">返回城市代码。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/ColorLatLng.html#getColor--">getColor()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/ColorLatLng.html" title="com.amap.api.maps.model中的类">ColorLatLng</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Polyline.html#getColor--">getColor()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Polyline.html" title="com.amap.api.maps.model中的类">Polyline</a></dt>
<dd>
<div class="block">获取线段的颜色</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/PolylineOptions.html#getColor--">getColor()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/PolylineOptions.html" title="com.amap.api.maps.model中的类">PolylineOptions</a></dt>
<dd>
<div class="block">获取线段的颜色ARGB格式。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/PolylineOptions.html#getColorValues--">getColorValues()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/PolylineOptions.html" title="com.amap.api.maps.model中的类">PolylineOptions</a></dt>
<dd>
<div class="block">获取线段的颜色列表</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMapOptions.html#getCompassEnabled--">getCompassEnabled()</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMapOptions.html" title="com.amap.api.maps中的类">AMapOptions</a></dt>
<dd>
<div class="block">返回初始化选项中指南针功能是否可用。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/OfflineMapCity.html#getcompleteCode--">getcompleteCode()</a></span> - 类 中的方法com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/OfflineMapCity.html" title="com.amap.api.maps.offlinemap中的类">OfflineMapCity</a></dt>
<dd>
<div class="block">返回城市下载完成的百分比，100表示下载完成。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/OfflineMapProvince.html#getcompleteCode--">getcompleteCode()</a></span> - 类 中的方法com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/OfflineMapProvince.html" title="com.amap.api.maps.offlinemap中的类">OfflineMapProvince</a></dt>
<dd>
<div class="block">返回省下载完成的百分比，100表示下载完成。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MyTrafficStyle.html#getCongestedColor--">getCongestedColor()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MyTrafficStyle.html" title="com.amap.api.maps.model中的类">MyTrafficStyle</a></dt>
<dd>
<div class="block">返回行驶拥堵路段的标记颜色。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/BitmapDescriptorFactory.html#getContext--">getContext()</a></span> - 类 中的静态方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/BitmapDescriptorFactory.html" title="com.amap.api.maps.model中的类">BitmapDescriptorFactory</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Poi.html#getCoordinate--">getCoordinate()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Poi.html" title="com.amap.api.maps.model中的类">Poi</a></dt>
<dd>
<div class="block">获取兴趣点的地理坐标。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/GLTFOverlay.html#getCurrentAnimationIndex--">getCurrentAnimationIndex()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/GLTFOverlay.html" title="com.amap.api.maps.model中的类">GLTFOverlay</a></dt>
<dd>
<div class="block">获取当前执行动画的索引，取值必须小于模型支持动画的个数。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/GLTFOverlayOptions.html#getCurrentAnimationIndex--">getCurrentAnimationIndex()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/GLTFOverlayOptions.html" title="com.amap.api.maps.model中的类">GLTFOverlayOptions</a></dt>
<dd>
<div class="block">获取当前执行动画的索引，取值必须小于模型支持动画的个数。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/particle/ParticleOverlay.html#getCurrentParticleNum--">getCurrentParticleNum()</a></span> - 类 中的方法com.amap.api.maps.model.particle.<a href="../com/amap/api/maps/model/particle/ParticleOverlay.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlay</a></dt>
<dd>
<div class="block">获取当前系统中粒子个数</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MultiPointItem.html#getCustomerId--">getCustomerId()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MultiPointItem.html" title="com.amap.api.maps.model中的类">MultiPointItem</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/BuildingOverlay.html#getCustomOptions--">getCustomOptions()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/BuildingOverlay.html" title="com.amap.api.maps.model中的类">BuildingOverlay</a></dt>
<dd>
<div class="block">获取用户设置的自定义BuildingOverlayOptions列表</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/PolylineOptions.html#getCustomTexture--">getCustomTexture()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/PolylineOptions.html" title="com.amap.api.maps.model中的类">PolylineOptions</a></dt>
<dd>
<div class="block">获取线段的纹理图。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/PolylineOptions.html#getCustomTextureIndex--">getCustomTextureIndex()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/PolylineOptions.html" title="com.amap.api.maps.model中的类">PolylineOptions</a></dt>
<dd>
<div class="block">获取线段纹理index列表</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/PolylineOptions.html#getCustomTextureList--">getCustomTextureList()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/PolylineOptions.html" title="com.amap.api.maps.model中的类">PolylineOptions</a></dt>
<dd>
<div class="block">获取线段纹理列表</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/HeatMapGridLayerOptions.html#getData--">getData()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/HeatMapGridLayerOptions.html" title="com.amap.api.maps.model中的类">HeatMapGridLayerOptions</a></dt>
<dd>
<div class="block">获取经纬度数据</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/HeatMapLayerOptions.html#getData--">getData()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/HeatMapLayerOptions.html" title="com.amap.api.maps.model中的类">HeatMapLayerOptions</a></dt>
<dd>
<div class="block">获取经纬度数据</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/BuildingOverlay.html#getDefaultOptions--">getDefaultOptions()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/BuildingOverlay.html" title="com.amap.api.maps.model中的类">BuildingOverlay</a></dt>
<dd>
<div class="block">获取默认配置信息</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/MapsInitializer.html#getDeviceId-android.content.Context-">getDeviceId(Context)</a></span> - 类 中的静态方法com.amap.api.maps.<a href="../com/amap/api/maps/MapsInitializer.html" title="com.amap.api.maps中的类">MapsInitializer</a></dt>
<dd>
<div class="block">获取设备号</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/TileOverlayOptions.html#getDiskCacheDir--">getDiskCacheDir()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/TileOverlayOptions.html" title="com.amap.api.maps.model中的类">TileOverlayOptions</a></dt>
<dd>
<div class="block">获取瓦片图层的磁盘缓存目录。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/TileOverlayOptions.html#getDiskCacheEnabled--">getDiskCacheEnabled()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/TileOverlayOptions.html" title="com.amap.api.maps.model中的类">TileOverlayOptions</a></dt>
<dd>
<div class="block">获取瓦片图层的磁盘缓存的开启状态。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/TileOverlayOptions.html#getDiskCacheSize--">getDiskCacheSize()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/TileOverlayOptions.html" title="com.amap.api.maps.model中的类">TileOverlayOptions</a></dt>
<dd>
<div class="block">获取瓦片图层的磁盘缓存大小。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/trace/TraceOverlay.html#getDistance--">getDistance()</a></span> - 类 中的方法com.amap.api.trace.<a href="../com/amap/api/trace/TraceOverlay.html" title="com.amap.api.trace中的类">TraceOverlay</a></dt>
<dd>
<div class="block">获取线路行驶距离</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/PolylineOptions.html#getDottedLineType--">getDottedLineType()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/PolylineOptions.html" title="com.amap.api.maps.model中的类">PolylineOptions</a></dt>
<dd>
<div class="block">获取虚线形状。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/OfflineMapManager.html#getDownloadingCityList--">getDownloadingCityList()</a></span> - 类 中的方法com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/OfflineMapManager.html" title="com.amap.api.maps.offlinemap中的类">OfflineMapManager</a></dt>
<dd>
<div class="block">所有正在下载或等待下载离线地图的城市列表。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/OfflineMapManager.html#getDownloadingProvinceList--">getDownloadingProvinceList()</a></span> - 类 中的方法com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/OfflineMapManager.html" title="com.amap.api.maps.offlinemap中的类">OfflineMapManager</a></dt>
<dd>
<div class="block">所有正在下载或等待下载离线地图的省份列表。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/OfflineMapManager.html#getDownloadOfflineMapCityList--">getDownloadOfflineMapCityList()</a></span> - 类 中的方法com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/OfflineMapManager.html" title="com.amap.api.maps.offlinemap中的类">OfflineMapManager</a></dt>
<dd>
<div class="block">返回已经下载完成离线地图的城市列表。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/OfflineMapManager.html#getDownloadOfflineMapProvinceList--">getDownloadOfflineMapProvinceList()</a></span> - 类 中的方法com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/OfflineMapManager.html" title="com.amap.api.maps.offlinemap中的类">OfflineMapManager</a></dt>
<dd>
<div class="block">返回已经下载完成离线地图的省份列表。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/RoutePara.html#getDrivingRouteStyle--">getDrivingRouteStyle()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/RoutePara.html" title="com.amap.api.maps.model中的类">RoutePara</a></dt>
<dd>
<div class="block">驾车规划模式</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/particle/ParticleOverlayOptions.html#getDuration--">getDuration()</a></span> - 类 中的方法com.amap.api.maps.model.particle.<a href="../com/amap/api/maps/model/particle/ParticleOverlayOptions.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlayOptions</a></dt>
<dd>
<div class="block">整个粒子效果的存活时间,单位毫秒</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/ArcOptions.html#getEnd--">getEnd()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/ArcOptions.html" title="com.amap.api.maps.model中的类">ArcOptions</a></dt>
<dd>
<div class="block">返回圆弧终点</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/RoutePara.html#getEndName--">getEndName()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/RoutePara.html" title="com.amap.api.maps.model中的类">RoutePara</a></dt>
<dd>
<div class="block">获取路线检索终点名称。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/RoutePara.html#getEndPoint--">getEndPoint()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/RoutePara.html" title="com.amap.api.maps.model中的类">RoutePara</a></dt>
<dd>
<div class="block">获取路线检索终点坐标。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMapException.html#getErrorMessage--">getErrorMessage()</a></span> - 异常错误 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMapException.html" title="com.amap.api.maps中的类">AMapException</a></dt>
<dd>
<div class="block">获取异常信息。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Circle.html#getFillColor--">getFillColor()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Circle.html" title="com.amap.api.maps.model中的类">Circle</a></dt>
<dd>
<div class="block">获取圆的填充颜色</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/CircleOptions.html#getFillColor--">getFillColor()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/CircleOptions.html" title="com.amap.api.maps.model中的类">CircleOptions</a></dt>
<dd>
<div class="block">获取圆的填充颜色</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Polygon.html#getFillColor--">getFillColor()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Polygon.html" title="com.amap.api.maps.model中的类">Polygon</a></dt>
<dd>
<div class="block">获取多边形的填充颜色</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/PolygonOptions.html#getFillColor--">getFillColor()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/PolygonOptions.html" title="com.amap.api.maps.model中的类">PolygonOptions</a></dt>
<dd>
<div class="block">获取多边形的填充颜色</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/animation/Animation.html#getFillMode--">getFillMode()</a></span> - 类 中的方法com.amap.api.maps.model.animation.<a href="../com/amap/api/maps/model/animation/Animation.html" title="com.amap.api.maps.model.animation中的类">Animation</a></dt>
<dd>
<div class="block">获取动画执行完成后的状态</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Text.html#getFontColor--">getFontColor()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Text.html" title="com.amap.api.maps.model中的类">Text</a></dt>
<dd>
<div class="block">获取文字覆盖物的字体颜色</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/TextOptions.html#getFontColor--">getFontColor()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/TextOptions.html" title="com.amap.api.maps.model中的类">TextOptions</a></dt>
<dd>
<div class="block">获取文字覆盖物的字体颜色</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Text.html#getFontSize--">getFontSize()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Text.html" title="com.amap.api.maps.model中的类">Text</a></dt>
<dd>
<div class="block">获取文字覆盖物的字体大小</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/TextOptions.html#getFontSize--">getFontSize()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/TextOptions.html" title="com.amap.api.maps.model中的类">TextOptions</a></dt>
<dd>
<div class="block">获取文字覆盖物的字体大小</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/AMapCameraInfo.html#getFov--">getFov()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/AMapCameraInfo.html" title="com.amap.api.maps.model中的类">AMapCameraInfo</a></dt>
<dd>
<div class="block">获取Fov（视角）</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/HeatMapLayerOptions.html#getGap--">getGap()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/HeatMapLayerOptions.html" title="com.amap.api.maps.model中的类">HeatMapLayerOptions</a></dt>
<dd>
<div class="block">获取间隔</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/HeatMapLayerOptions.html#getGradient--">getGradient()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/HeatMapLayerOptions.html" title="com.amap.api.maps.model中的类">HeatMapLayerOptions</a></dt>
<dd>
<div class="block">获取颜色处理</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/HeatMapLayer.html#getHeatMapItem-com.amap.api.maps.model.LatLng-">getHeatMapItem(LatLng)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/HeatMapLayer.html" title="com.amap.api.maps.model中的类">HeatMapLayer</a></dt>
<dd>
<div class="block">根据经纬度获取当前的热力信息</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/BitmapDescriptor.html#getHeight--">getHeight()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/BitmapDescriptor.html" title="com.amap.api.maps.model中的类">BitmapDescriptor</a></dt>
<dd>
<div class="block">返回 Bitmap 的高度。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/GroundOverlay.html#getHeight--">getHeight()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/GroundOverlay.html" title="com.amap.api.maps.model中的类">GroundOverlay</a></dt>
<dd>
<div class="block">获取ground 覆盖物的高。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/GroundOverlayOptions.html#getHeight--">getHeight()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/GroundOverlayOptions.html" title="com.amap.api.maps.model中的类">GroundOverlayOptions</a></dt>
<dd>
<div class="block">获取 ground 覆盖物的高。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Circle.html#getHoleOptions--">getHoleOptions()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Circle.html" title="com.amap.api.maps.model中的类">Circle</a></dt>
<dd>
<div class="block">获取洞的配置项集合</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Polygon.html#getHoleOptions--">getHoleOptions()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Polygon.html" title="com.amap.api.maps.model中的类">Polygon</a></dt>
<dd>
<div class="block">获取多边形空心洞的配置项集合</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/PolygonOptions.html#getHoleOptions--">getHoleOptions()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/PolygonOptions.html" title="com.amap.api.maps.model中的类">PolygonOptions</a></dt>
<dd>
<div class="block">获取空心洞的配置项</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MarkerOptions.html#getIcon--">getIcon()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MarkerOptions.html" title="com.amap.api.maps.model中的类">MarkerOptions</a></dt>
<dd>
<div class="block">获取Marker覆盖物的图标。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MultiPointOverlayOptions.html#getIcon--">getIcon()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MultiPointOverlayOptions.html" title="com.amap.api.maps.model中的类">MultiPointOverlayOptions</a></dt>
<dd>
<div class="block">获取海量点的显示图标</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/particle/ParticleOverlayOptions.html#getIcon--">getIcon()</a></span> - 类 中的方法com.amap.api.maps.model.particle.<a href="../com/amap/api/maps/model/particle/ParticleOverlayOptions.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlayOptions</a></dt>
<dd>
<div class="block">获取粒子效果的图标</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Marker.html#getIcons--">getIcons()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Marker.html" title="com.amap.api.maps.model中的类">Marker</a></dt>
<dd>
<div class="block">返回Marker动画帧的图标列表。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MarkerOptions.html#getIcons--">getIcons()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MarkerOptions.html" title="com.amap.api.maps.model中的类">MarkerOptions</a></dt>
<dd>
<div class="block">获取Marker覆盖物的动画帧图标列表，动画的描点和大小以第一帧为准，建议图片大小保持一致。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Arc.html#getId--">getId()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Arc.html" title="com.amap.api.maps.model中的类">Arc</a></dt>
<dd>
<div class="block">返回弧形的 Id。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/BasePointOverlay.html#getId--">getId()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/BasePointOverlay.html" title="com.amap.api.maps.model中的类">BasePointOverlay</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/BuildingOverlay.html#getId--">getId()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/BuildingOverlay.html" title="com.amap.api.maps.model中的类">BuildingOverlay</a></dt>
<dd>
<div class="block">获取当前overlay的id</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Circle.html#getId--">getId()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Circle.html" title="com.amap.api.maps.model中的类">Circle</a></dt>
<dd>
<div class="block">返回圆的id。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/GLTFOverlay.html#getId--">getId()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/GLTFOverlay.html" title="com.amap.api.maps.model中的类">GLTFOverlay</a></dt>
<dd>
<div class="block">得到GLTFOverlay的Id</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/GroundOverlay.html#getId--">getId()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/GroundOverlay.html" title="com.amap.api.maps.model中的类">GroundOverlay</a></dt>
<dd>
<div class="block">得到Ground 覆盖物的Id，一个地图上的每个图片层都有自己的ID。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/HeatMapGridLayer.html#getId--">getId()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/HeatMapGridLayer.html" title="com.amap.api.maps.model中的类">HeatMapGridLayer</a></dt>
<dd>
<div class="block">获取图层的Id</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/HeatMapLayer.html#getId--">getId()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/HeatMapLayer.html" title="com.amap.api.maps.model中的类">HeatMapLayer</a></dt>
<dd>
<div class="block">获取图层的Id</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Marker.html#getId--">getId()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Marker.html" title="com.amap.api.maps.model中的类">Marker</a></dt>
<dd>
<div class="block">返回Marker 的Id，每个marker 的唯一标识，用来区分不同的Marker。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MVTTileProvider.html#getId--">getId()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MVTTileProvider.html" title="com.amap.api.maps.model中的类">MVTTileProvider</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/NavigateArrow.html#getId--">getId()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/NavigateArrow.html" title="com.amap.api.maps.model中的类">NavigateArrow</a></dt>
<dd>
<div class="block">获取箭头(NavigateArrow)覆盖物的Id。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Polygon.html#getId--">getId()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Polygon.html" title="com.amap.api.maps.model中的类">Polygon</a></dt>
<dd>
<div class="block">获取多边形的Id。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Polyline.html#getId--">getId()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Polyline.html" title="com.amap.api.maps.model中的类">Polyline</a></dt>
<dd>
<div class="block">获取线段的Id。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Text.html#getId--">getId()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Text.html" title="com.amap.api.maps.model中的类">Text</a></dt>
<dd>
<div class="block">获取文字覆盖物的Id，用来区分不同的文字覆盖物。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/TileOverlay.html#getId--">getId()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/TileOverlay.html" title="com.amap.api.maps.model中的类">TileOverlay</a></dt>
<dd>
<div class="block">获取瓦片图层的Id</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/TileOverlaySource.html#getId--">getId()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/TileOverlaySource.html" title="com.amap.api.maps.model中的类">TileOverlaySource</a></dt>
<dd>
<div class="block">数据源唯一标识，请求时通过唯一标识来进行区分，内部生成</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/GroundOverlayOptions.html#getImage--">getImage()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/GroundOverlayOptions.html" title="com.amap.api.maps.model中的类">GroundOverlayOptions</a></dt>
<dd>
<div class="block">获取 ground 覆盖物的bitmap描述信息。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/HeatMapItem.html#getIndexes--">getIndexes()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/HeatMapItem.html" title="com.amap.api.maps.model中的类">HeatMapItem</a></dt>
<dd>
<div class="block">获取当前蜂窝或者网格包含数据的索引</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.InfoWindowAdapter.html#getInfoContents-com.amap.api.maps.model.Marker-">getInfoContents(Marker)</a></span> - 接口 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.InfoWindowAdapter.html" title="com.amap.api.maps中的接口">AMap.InfoWindowAdapter</a></dt>
<dd>
<div class="block">提定制展示marker信息的View。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.InfoWindowAdapter.html#getInfoWindow-com.amap.api.maps.model.Marker-">getInfoWindow(Marker)</a></span> - 接口 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.InfoWindowAdapter.html" title="com.amap.api.maps中的接口">AMap.InfoWindowAdapter</a></dt>
<dd>
<div class="block">定制展示marker信息的View。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MarkerOptions.html#getInfoWindowOffsetX--">getInfoWindowOffsetX()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MarkerOptions.html" title="com.amap.api.maps.model中的类">MarkerOptions</a></dt>
<dd>
<div class="block">获取Marker覆盖物的水平偏移距离</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MarkerOptions.html#getInfoWindowOffsetY--">getInfoWindowOffsetY()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MarkerOptions.html" title="com.amap.api.maps.model中的类">MarkerOptions</a></dt>
<dd>
<div class="block">获取Marker覆盖物的垂直偏移距离</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.ImageInfoWindowAdapter.html#getInfoWindowUpdateTime--">getInfoWindowUpdateTime()</a></span> - 接口 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.ImageInfoWindowAdapter.html" title="com.amap.api.maps中的接口">AMap.ImageInfoWindowAdapter</a></dt>
<dd>
<div class="block">注意，使用<a href="../com/amap/api/maps/AMap.ImageInfoWindowAdapter.html" title="com.amap.api.maps中的接口"><code>AMap.ImageInfoWindowAdapter</code></a>后InfoWindow作为View本身的功能被减弱，比如动态更新图片，播放Gif图片等等均无法使用。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/GLTFOverlay.html#getInfoWindowView--">getInfoWindowView()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/GLTFOverlay.html" title="com.amap.api.maps.model中的类">GLTFOverlay</a></dt>
<dd>
<div class="block">获取InfoWindowView</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/GLTFOverlayOptions.html#getInfoWindowView--">getInfoWindowView()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/GLTFOverlayOptions.html" title="com.amap.api.maps.model中的类">GLTFOverlayOptions</a></dt>
<dd>
<div class="block">获取点击的infoWindowView</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/trace/LBSTraceClient.html#getInstance-android.content.Context-">getInstance(Context)</a></span> - 类 中的静态方法com.amap.api.trace.<a href="../com/amap/api/trace/LBSTraceClient.html" title="com.amap.api.trace中的类">LBSTraceClient</a></dt>
<dd>
<div class="block">获取LBSTraceClient</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/HeatMapItem.html#getIntensity--">getIntensity()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/HeatMapItem.html" title="com.amap.api.maps.model中的类">HeatMapItem</a></dt>
<dd>
<div class="block">获取当前热力值，当前蜂窝或者网格的热力值</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MyLocationStyle.html#getInterval--">getInterval()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MyLocationStyle.html" title="com.amap.api.maps.model中的类">MyLocationStyle</a></dt>
<dd>
<div class="block">得到定位请求时间间隔。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/OfflineMapManager.html#getItemByCityCode-java.lang.String-">getItemByCityCode(String)</a></span> - 类 中的方法com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/OfflineMapManager.html" title="com.amap.api.maps.offlinemap中的类">OfflineMapManager</a></dt>
<dd>
<div class="block">根据城市编码获取OfflineMapCity对象<br>
 同步方法</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/OfflineMapManager.html#getItemByCityName-java.lang.String-">getItemByCityName(String)</a></span> - 类 中的方法com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/OfflineMapManager.html" title="com.amap.api.maps.offlinemap中的类">OfflineMapManager</a></dt>
<dd>
<div class="block">根据城市名称获取OfflneMapCity对象<br>
 同步方法</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/OfflineMapManager.html#getItemByProvinceName-java.lang.String-">getItemByProvinceName(String)</a></span> - 类 中的方法com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/OfflineMapManager.html" title="com.amap.api.maps.offlinemap中的类">OfflineMapManager</a></dt>
<dd>
<div class="block">根据省份名称获取OfflineMapProvince对象<br>
 同步方法</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MultiPointOverlay.html#getItems--">getItems()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MultiPointOverlay.html" title="com.amap.api.maps.model中的类">MultiPointOverlay</a></dt>
<dd>
<div class="block">获取海量点，初始化或者更新海量点时可以调用此方法<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/City.html#getJianpin--">getJianpin()</a></span> - 类 中的方法com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/City.html" title="com.amap.api.maps.offlinemap中的类">City</a></dt>
<dd>
<div class="block">返回城市名称简拼拼音。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/Province.html#getJianpin--">getJianpin()</a></span> - 类 中的方法com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/Province.html" title="com.amap.api.maps.offlinemap中的类">Province</a></dt>
<dd>
<div class="block">返回省名称简拼拼音。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MVTTileProvider.html#getKey--">getKey()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MVTTileProvider.html" title="com.amap.api.maps.model中的类">MVTTileProvider</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/PoiPara.html#getKeywords--">getKeywords()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/PoiPara.html" title="com.amap.api.maps.model中的类">PoiPara</a></dt>
<dd>
<div class="block">获取 poi 检索关键字”|”分割。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMapUtils.html#getLatestAMapApp-android.content.Context-">getLatestAMapApp(Context)</a></span> - 类 中的静态方法com.amap.api.maps.<a href="../com/amap/api/maps/AMapUtils.html" title="com.amap.api.maps中的类">AMapUtils</a></dt>
<dd>
<div class="block">跳转到高德地图APP最新版本下载页面</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/trace/TraceLocation.html#getLatitude--">getLatitude()</a></span> - 类 中的方法com.amap.api.trace.<a href="../com/amap/api/trace/TraceLocation.html" title="com.amap.api.trace中的类">TraceLocation</a></dt>
<dd>
<div class="block">获取纬度</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/GLTFOverlay.html#getLatlng--">getLatlng()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/GLTFOverlay.html" title="com.amap.api.maps.model中的类">GLTFOverlay</a></dt>
<dd>
<div class="block">获取位置</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/GLTFOverlayOptions.html#getLatLng--">getLatLng()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/GLTFOverlayOptions.html" title="com.amap.api.maps.model中的类">GLTFOverlayOptions</a></dt>
<dd>
<div class="block">获取 模型的中心点经纬度坐标。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MultiPointItem.html#getLatLng--">getLatLng()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MultiPointItem.html" title="com.amap.api.maps.model中的类">MultiPointItem</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/ColorLatLng.html#getLatLngs--">getLatLngs()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/ColorLatLng.html" title="com.amap.api.maps.model中的类">ColorLatLng</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/PolylineOptions.html#getLineCapType--">getLineCapType()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/PolylineOptions.html" title="com.amap.api.maps.model中的类">PolylineOptions</a></dt>
<dd>
<div class="block">返回Polyline尾部形状</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/PolygonOptions.html#getLineJoinType--">getLineJoinType()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/PolygonOptions.html" title="com.amap.api.maps.model中的类">PolygonOptions</a></dt>
<dd>
<div class="block">返回边框连接处形状</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/PolylineOptions.html#getLineJoinType--">getLineJoinType()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/PolylineOptions.html" title="com.amap.api.maps.model中的类">PolylineOptions</a></dt>
<dd>
<div class="block">返回Polyline连接处形状</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/GroundOverlayOptions.html#getLocation--">getLocation()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/GroundOverlayOptions.html" title="com.amap.api.maps.model中的类">GroundOverlayOptions</a></dt>
<dd>
<div class="block">获取 Ground覆盖物 的位置。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMapOptions.html#getLogoPosition--">getLogoPosition()</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMapOptions.html" title="com.amap.api.maps中的类">AMapOptions</a></dt>
<dd>
<div class="block">获取初始化选项“高德地图”Logo的位置。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/UiSettings.html#getLogoPosition--">getLogoPosition()</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/UiSettings.html" title="com.amap.api.maps中的类">UiSettings</a></dt>
<dd>
<div class="block">获取“高德地图”Logo的位置。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/trace/TraceLocation.html#getLongitude--">getLongitude()</a></span> - 类 中的方法com.amap.api.trace.<a href="../com/amap/api/trace/TraceLocation.html" title="com.amap.api.trace中的类">TraceLocation</a></dt>
<dd>
<div class="block">获取经度</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/MapFragment.html#getMap--">getMap()</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/MapFragment.html" title="com.amap.api.maps中的类">MapFragment</a></dt>
<dd>
<div class="block">获取地图控制器AMap 对象。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/MapView.html#getMap--">getMap()</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/MapView.html" title="com.amap.api.maps中的类">MapView</a></dt>
<dd>
<div class="block">获取地图控制器AMap 对象。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/SupportMapFragment.html#getMap--">getMap()</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/SupportMapFragment.html" title="com.amap.api.maps中的类">SupportMapFragment</a></dt>
<dd>
<div class="block">获取地图控制器AMap 对象。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/TextureMapFragment.html#getMap--">getMap()</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/TextureMapFragment.html" title="com.amap.api.maps中的类">TextureMapFragment</a></dt>
<dd>
<div class="block">获取地图控制器AMap 对象。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/TextureMapView.html#getMap--">getMap()</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/TextureMapView.html" title="com.amap.api.maps中的类">TextureMapView</a></dt>
<dd>
<div class="block">获取地图控制器AMap 对象。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/TextureSupportMapFragment.html#getMap--">getMap()</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/TextureSupportMapFragment.html" title="com.amap.api.maps中的类">TextureSupportMapFragment</a></dt>
<dd>
<div class="block">获取地图控制器AMap 对象。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/WearMapView.html#getMap--">getMap()</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/WearMapView.html" title="com.amap.api.maps中的类">WearMapView</a></dt>
<dd>
<div class="block">返回一个与这个视图（WearMapView）相关联的AMap 对象。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/Projection.html#getMapBounds-com.amap.api.maps.model.LatLng-float-">getMapBounds(LatLng, float)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/Projection.html" title="com.amap.api.maps中的类">Projection</a></dt>
<dd>
<div class="block">根据中心点和zoom级别获取地图控件对应的目标区域</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#getMapContentApprovalNumber--">getMapContentApprovalNumber()</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block">获取地图审图号（普通地图）<br>
 <font color="red">任何使用高德地图API调用地图服务的应用必须在其应用中对外透出审图号</font><br>
 如高德地图在"关于"中体现</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/MapView.html#getMapFragmentDelegate--">getMapFragmentDelegate()</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/MapView.html" title="com.amap.api.maps中的类">MapView</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/TextureMapView.html#getMapFragmentDelegate--">getMapFragmentDelegate()</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/TextureMapView.html" title="com.amap.api.maps中的类">TextureMapView</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#getMapPrintScreen-com.amap.api.maps.AMap.onMapPrintScreenListener-">getMapPrintScreen(AMap.onMapPrintScreenListener)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span>
<div class="block"><span class="deprecationComment">建议使用<a href="../com/amap/api/maps/AMap.html#getMapScreenShot-com.amap.api.maps.AMap.OnMapScreenShotListener-"><code>AMap.getMapScreenShot(OnMapScreenShotListener)</code></a></span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#getMapRegionSnapshot-com.amap.api.maps.model.LatLng-com.amap.api.maps.model.LatLng-android.util.Size-com.amap.api.maps.AMap.OnMapSnapshotListener-">getMapRegionSnapshot(LatLng, LatLng, Size, AMap.OnMapSnapshotListener)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#getMapScreenMarkers--">getMapScreenMarkers()</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block">获取当前地图可视区域范围所有marker对象。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#getMapScreenShot-com.amap.api.maps.AMap.OnMapScreenShotListener-">getMapScreenShot(AMap.OnMapScreenShotListener)</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block">发起地图截屏请求。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#getMapTextZIndex--">getMapTextZIndex()</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block">得到地图底图文字标注的层级指数。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#getMapType--">getMapType()</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block">获取地图当前的模式。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMapOptions.html#getMapType--">getMapType()</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMapOptions.html" title="com.amap.api.maps中的类">AMapOptions</a></dt>
<dd>
<div class="block">返回初始化选项中地图模式。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/utils/overlay/SmoothMoveMarker.html#getMarker--">getMarker()</a></span> - 类 中的方法com.amap.api.maps.utils.overlay.<a href="../com/amap/api/maps/utils/overlay/SmoothMoveMarker.html" title="com.amap.api.maps.utils.overlay中的类">SmoothMoveMarker</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">获取当前移动的Marker</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/HeatMapLayerOptions.html#getMaxIntensity--">getMaxIntensity()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/HeatMapLayerOptions.html" title="com.amap.api.maps.model中的类">HeatMapLayerOptions</a></dt>
<dd>
<div class="block">获取最大权重，0表示没有设置</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/particle/ParticleOverlayOptions.html#getMaxParticles--">getMaxParticles()</a></span> - 类 中的方法com.amap.api.maps.model.particle.<a href="../com/amap/api/maps/model/particle/ParticleOverlayOptions.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlayOptions</a></dt>
<dd>
<div class="block">整个粒子效果的粒子最大数量</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/GLTFOverlayOptions.html#getMaxZoom--">getMaxZoom()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/GLTFOverlayOptions.html" title="com.amap.api.maps.model中的类">GLTFOverlayOptions</a></dt>
<dd>
<div class="block">获取最大缩放级别</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/HeatMapGridLayerOptions.html#getMaxZoom--">getMaxZoom()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/HeatMapGridLayerOptions.html" title="com.amap.api.maps.model中的类">HeatMapGridLayerOptions</a></dt>
<dd>
<div class="block">获取最大显示级别</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/HeatMapLayerOptions.html#getMaxZoom--">getMaxZoom()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/HeatMapLayerOptions.html" title="com.amap.api.maps.model中的类">HeatMapLayerOptions</a></dt>
<dd>
<div class="block">获取最大显示级别</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/TileOverlaySource.html#getMaxZoom--">getMaxZoom()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/TileOverlaySource.html" title="com.amap.api.maps.model中的类">TileOverlaySource</a></dt>
<dd>
<div class="block">数据最大级别，高于了没有数据</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#getMaxZoomLevel--">getMaxZoomLevel()</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block">返回地图可显示最大缩放级别。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/TileOverlayOptions.html#getMemCacheSize--">getMemCacheSize()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/TileOverlayOptions.html" title="com.amap.api.maps.model中的类">TileOverlayOptions</a></dt>
<dd>
<div class="block">获取瓦片图层的内存缓存大小。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/TileOverlayOptions.html#getMemoryCacheEnabled--">getMemoryCacheEnabled()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/TileOverlayOptions.html" title="com.amap.api.maps.model中的类">TileOverlayOptions</a></dt>
<dd>
<div class="block">获取瓦片图层的内存缓存开启状态。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/GLTFOverlayOptions.html#getMinZoom--">getMinZoom()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/GLTFOverlayOptions.html" title="com.amap.api.maps.model中的类">GLTFOverlayOptions</a></dt>
<dd>
<div class="block">获取最小缩放级别</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/HeatMapGridLayerOptions.html#getMinZoom--">getMinZoom()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/HeatMapGridLayerOptions.html" title="com.amap.api.maps.model中的类">HeatMapGridLayerOptions</a></dt>
<dd>
<div class="block">获取最小显示级别</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/HeatMapLayerOptions.html#getMinZoom--">getMinZoom()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/HeatMapLayerOptions.html" title="com.amap.api.maps.model中的类">HeatMapLayerOptions</a></dt>
<dd>
<div class="block">获取最小显示级别</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/TileOverlaySource.html#getMinZoom--">getMinZoom()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/TileOverlaySource.html" title="com.amap.api.maps.model中的类">TileOverlaySource</a></dt>
<dd>
<div class="block">数据最小级别，低于了没有数据</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#getMinZoomLevel--">getMinZoomLevel()</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block">返回地图可显示最小缩放级别。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/GLTFOverlayOptions.html#getModelData--">getModelData()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/GLTFOverlayOptions.html" title="com.amap.api.maps.model中的类">GLTFOverlayOptions</a></dt>
<dd>
<div class="block">获取glTF的模型数据</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#getMyLocation--">getMyLocation()</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block">返回当前定位源（locationSource）提供的定位信息。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MyLocationStyle.html#getMyLocationIcon--">getMyLocationIcon()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MyLocationStyle.html" title="com.amap.api.maps.model中的类">MyLocationStyle</a></dt>
<dd>
<div class="block">得到当前位置的图标。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#getMyLocationStyle--">getMyLocationStyle()</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block">获取定位图层（myLocationOverlay）的样式。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MyLocationStyle.html#getMyLocationType--">getMyLocationType()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MyLocationStyle.html" title="com.amap.api.maps.model中的类">MyLocationStyle</a></dt>
<dd>
<div class="block">得到我的位置展示模式。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#getMyTrafficStyle--">getMyTrafficStyle()</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span>
<div class="block"><span class="deprecationComment">自7.8.0之后不再支持</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Poi.html#getName--">getName()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Poi.html" title="com.amap.api.maps.model中的类">Poi</a></dt>
<dd>
<div class="block">获取兴趣点的名称。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/NaviPara.html#getNaviStyle--">getNaviStyle()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/NaviPara.html" title="com.amap.api.maps.model中的类">NaviPara</a></dt>
<dd>
<div class="block">获取导航类型。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Polyline.html#getNearestLatLng-com.amap.api.maps.model.LatLng-">getNearestLatLng(LatLng)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Polyline.html" title="com.amap.api.maps.model中的类">Polyline</a></dt>
<dd>
<div class="block">获取某坐标点距线上最近的坐标点。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/MapsInitializer.html#getNetWorkEnable--">getNetWorkEnable()</a></span> - 类 中的静态方法com.amap.api.maps.<a href="../com/amap/api/maps/MapsInitializer.html" title="com.amap.api.maps中的类">MapsInitializer</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span>
<div class="block"><span class="deprecationComment">5.0.0开始废弃</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/BasePointOverlay.html#getObject--">getObject()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/BasePointOverlay.html" title="com.amap.api.maps.model中的类">BasePointOverlay</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Marker.html#getObject--">getObject()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Marker.html" title="com.amap.api.maps.model中的类">Marker</a></dt>
<dd>
<div class="block">获取Marker覆盖物的附加信息对象，即自定义的Marker的属性。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MultiPointItem.html#getObject--">getObject()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MultiPointItem.html" title="com.amap.api.maps.model中的类">MultiPointItem</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Text.html#getObject--">getObject()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Text.html" title="com.amap.api.maps.model中的类">Text</a></dt>
<dd>
<div class="block">获取文字覆盖物的额外信息</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/TextOptions.html#getObject--">getObject()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/TextOptions.html" title="com.amap.api.maps.model中的类">TextOptions</a></dt>
<dd>
<div class="block">获取文字覆盖物额外信息</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/utils/overlay/MovingPointOverlay.html#getObject--">getObject()</a></span> - 类 中的方法com.amap.api.maps.utils.overlay.<a href="../com/amap/api/maps/utils/overlay/MovingPointOverlay.html" title="com.amap.api.maps.utils.overlay中的类">MovingPointOverlay</a></dt>
<dd>
<div class="block">获取当前移动的Marker</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/OfflineMapManager.html#getOfflineMapCityList--">getOfflineMapCityList()</a></span> - 类 中的方法com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/OfflineMapManager.html" title="com.amap.api.maps.offlinemap中的类">OfflineMapManager</a></dt>
<dd>
<div class="block">获取所有存在有离线地图的城市列表。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/OfflineMapManager.html#getOfflineMapProvinceList--">getOfflineMapProvinceList()</a></span> - 类 中的方法com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/OfflineMapManager.html" title="com.amap.api.maps.offlinemap中的类">OfflineMapManager</a></dt>
<dd>
<div class="block">获取所有存在有离线地图的省的列表。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/HeatMapLayerOptions.html#getOpacity--">getOpacity()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/HeatMapLayerOptions.html" title="com.amap.api.maps.model中的类">HeatMapLayerOptions</a></dt>
<dd>
<div class="block">获取透明度</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/GLTFOverlay.html#getOptions--">getOptions()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/GLTFOverlay.html" title="com.amap.api.maps.model中的类">GLTFOverlay</a></dt>
<dd>
<div class="block">得到GLTFOverlay的Id</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/HeatMapGridLayer.html#getOptions--">getOptions()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/HeatMapGridLayer.html" title="com.amap.api.maps.model中的类">HeatMapGridLayer</a></dt>
<dd>
<div class="block">获取热力图属性，如果初始化传入的null 则返回也是null</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/HeatMapLayer.html#getOptions--">getOptions()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/HeatMapLayer.html" title="com.amap.api.maps.model中的类">HeatMapLayer</a></dt>
<dd>
<div class="block">获取热力图属性，如果初始化传入的null 则返回也是null</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Marker.html#getOptions--">getOptions()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Marker.html" title="com.amap.api.maps.model中的类">Marker</a></dt>
<dd>
<div class="block">获取Marker覆盖物的选项类</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Polyline.html#getOptions--">getOptions()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Polyline.html" title="com.amap.api.maps.model中的类">Polyline</a></dt>
<dd>
<div class="block">获取线段选项信息</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/particle/ParticleOverlayOptions.html#getParticleEmissionModule--">getParticleEmissionModule()</a></span> - 类 中的方法com.amap.api.maps.model.particle.<a href="../com/amap/api/maps/model/particle/ParticleOverlayOptions.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlayOptions</a></dt>
<dd>
<div class="block">发射率，每隔多少时间发射粒子数量，越多会越密集</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/particle/ParticleOverlayOptions.html#getParticleLifeTime--">getParticleLifeTime()</a></span> - 类 中的方法com.amap.api.maps.model.particle.<a href="../com/amap/api/maps/model/particle/ParticleOverlayOptions.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlayOptions</a></dt>
<dd>
<div class="block">每个粒子的存活时间,单位毫秒</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/particle/ParticleOverlayOptions.html#getParticleOverLifeModule--">getParticleOverLifeModule()</a></span> - 类 中的方法com.amap.api.maps.model.particle.<a href="../com/amap/api/maps/model/particle/ParticleOverlayOptions.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlayOptions</a></dt>
<dd>
<div class="block">每个粒子生命周期过程中状态变化，包含速度、旋转和颜色的变化</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/particle/ParticleOverlayOptions.html#getParticleShapeModule--">getParticleShapeModule()</a></span> - 类 中的方法com.amap.api.maps.model.particle.<a href="../com/amap/api/maps/model/particle/ParticleOverlayOptions.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlayOptions</a></dt>
<dd>
<div class="block">发射模型，比如所有粒子从一个点出来，或者从某个区域出来</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/particle/ParticleOverlayOptions.html#getParticleStartColor--">getParticleStartColor()</a></span> - 类 中的方法com.amap.api.maps.model.particle.<a href="../com/amap/api/maps/model/particle/ParticleOverlayOptions.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlayOptions</a></dt>
<dd>
<div class="block">每个粒子的初始颜色</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/particle/ParticleOverlayOptions.html#getParticleStartSpeed--">getParticleStartSpeed()</a></span> - 类 中的方法com.amap.api.maps.model.particle.<a href="../com/amap/api/maps/model/particle/ParticleOverlayOptions.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlayOptions</a></dt>
<dd>
<div class="block">获取初始速度</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/ArcOptions.html#getPassed--">getPassed()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/ArcOptions.html" title="com.amap.api.maps.model中的类">ArcOptions</a></dt>
<dd>
<div class="block">返回圆弧中点</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Marker.html#getPeriod--">getPeriod()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Marker.html" title="com.amap.api.maps.model中的类">Marker</a></dt>
<dd>
<div class="block">得到多少帧刷新一次图片资源。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MarkerOptions.html#getPeriod--">getPeriod()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MarkerOptions.html" title="com.amap.api.maps.model中的类">MarkerOptions</a></dt>
<dd>
<div class="block">得到多少帧刷新一次图片资源，值越小动画越快。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/City.html#getPinyin--">getPinyin()</a></span> - 类 中的方法com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/City.html" title="com.amap.api.maps.offlinemap中的类">City</a></dt>
<dd>
<div class="block">返回城市名称拼音</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/Province.html#getPinyin--">getPinyin()</a></span> - 类 中的方法com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/Province.html" title="com.amap.api.maps.offlinemap中的类">Province</a></dt>
<dd>
<div class="block">返回省名称拼音。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Poi.html#getPoiId--">getPoiId()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Poi.html" title="com.amap.api.maps.model中的类">Poi</a></dt>
<dd>
<div class="block">获取兴趣点的Id，它是兴趣点的唯一标识</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/particle/SinglePointParticleShape.html#getPoint--">getPoint()</a></span> - 类 中的方法com.amap.api.maps.model.particle.<a href="../com/amap/api/maps/model/particle/SinglePointParticleShape.html" title="com.amap.api.maps.model.particle中的类">SinglePointParticleShape</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/NavigateArrow.html#getPoints--">getPoints()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/NavigateArrow.html" title="com.amap.api.maps.model中的类">NavigateArrow</a></dt>
<dd>
<div class="block">获取箭头(NavigateArrow)覆盖物的顶点列表。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/NavigateArrowOptions.html#getPoints--">getPoints()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/NavigateArrowOptions.html" title="com.amap.api.maps.model中的类">NavigateArrowOptions</a></dt>
<dd>
<div class="block">获取箭头(NavigateArrow)覆盖物顶点坐标集合。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Polygon.html#getPoints--">getPoints()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Polygon.html" title="com.amap.api.maps.model中的类">Polygon</a></dt>
<dd>
<div class="block">获取多边形的顶点列表。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/PolygonHoleOptions.html#getPoints--">getPoints()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/PolygonHoleOptions.html" title="com.amap.api.maps.model中的类">PolygonHoleOptions</a></dt>
<dd>
<div class="block">返回多边形洞坐标点列表</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/PolygonOptions.html#getPoints--">getPoints()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/PolygonOptions.html" title="com.amap.api.maps.model中的类">PolygonOptions</a></dt>
<dd>
<div class="block">返回多边形坐标点列表</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Polyline.html#getPoints--">getPoints()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Polyline.html" title="com.amap.api.maps.model中的类">Polyline</a></dt>
<dd>
<div class="block">获取线段的顶点坐标点列表。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/PolylineOptions.html#getPoints--">getPoints()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/PolylineOptions.html" title="com.amap.api.maps.model中的类">PolylineOptions</a></dt>
<dd>
<div class="block">获取线段的点坐标列表。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/BasePointOverlay.html#getPosition--">getPosition()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/BasePointOverlay.html" title="com.amap.api.maps.model中的类">BasePointOverlay</a></dt>
<dd>
<div class="block">获取 Marker 覆盖物的位置坐标。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/GroundOverlay.html#getPosition--">getPosition()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/GroundOverlay.html" title="com.amap.api.maps.model中的类">GroundOverlay</a></dt>
<dd>
<div class="block">获取 ground 覆盖物位置。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Marker.html#getPosition--">getPosition()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Marker.html" title="com.amap.api.maps.model中的类">Marker</a></dt>
<dd>
<div class="block">获取 Marker 覆盖物的位置坐标。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MarkerOptions.html#getPosition--">getPosition()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MarkerOptions.html" title="com.amap.api.maps.model中的类">MarkerOptions</a></dt>
<dd>
<div class="block">获取Marker覆盖物的坐标位置。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Text.html#getPosition--">getPosition()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Text.html" title="com.amap.api.maps.model中的类">Text</a></dt>
<dd>
<div class="block">获取文字覆盖物的坐标位置</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/TextOptions.html#getPosition--">getPosition()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/TextOptions.html" title="com.amap.api.maps.model中的类">TextOptions</a></dt>
<dd>
<div class="block">获取文字覆盖物的地理坐标。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/utils/overlay/MovingPointOverlay.html#getPosition--">getPosition()</a></span> - 类 中的方法com.amap.api.maps.utils.overlay.<a href="../com/amap/api/maps/utils/overlay/MovingPointOverlay.html" title="com.amap.api.maps.utils.overlay中的类">MovingPointOverlay</a></dt>
<dd>
<div class="block">获取当前位置坐标</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/utils/overlay/SmoothMoveMarker.html#getPosition--">getPosition()</a></span> - 类 中的方法com.amap.api.maps.utils.overlay.<a href="../com/amap/api/maps/utils/overlay/SmoothMoveMarker.html" title="com.amap.api.maps.utils.overlay中的类">SmoothMoveMarker</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
<div class="block">获取当前位置坐标</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#getProjection--">getProjection()</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block">获取地图投影坐标转换器, 当地图初始化完成之前返回 null。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/MapsInitializer.html#getProtocol--">getProtocol()</a></span> - 类 中的静态方法com.amap.api.maps.<a href="../com/amap/api/maps/MapsInitializer.html" title="com.amap.api.maps中的类">MapsInitializer</a></dt>
<dd>
<div class="block">返回访问使用的协议类别</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/Province.html#getProvinceCode--">getProvinceCode()</a></span> - 类 中的方法com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/Province.html" title="com.amap.api.maps.offlinemap中的类">Province</a></dt>
<dd>
<div class="block">得到省的行政编码。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/Province.html#getProvinceName--">getProvinceName()</a></span> - 类 中的方法com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/Province.html" title="com.amap.api.maps.offlinemap中的类">Province</a></dt>
<dd>
<div class="block">返回省名称。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Circle.html#getRadius--">getRadius()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Circle.html" title="com.amap.api.maps.model中的类">Circle</a></dt>
<dd>
<div class="block">获取圆的半径，单位米。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/CircleHoleOptions.html#getRadius--">getRadius()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/CircleHoleOptions.html" title="com.amap.api.maps.model中的类">CircleHoleOptions</a></dt>
<dd>
<div class="block">获取圆洞的半径，单位米。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/CircleOptions.html#getRadius--">getRadius()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/CircleOptions.html" title="com.amap.api.maps.model中的类">CircleOptions</a></dt>
<dd>
<div class="block">获取圆的半径，单位米。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MyLocationStyle.html#getRadiusFillColor--">getRadiusFillColor()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MyLocationStyle.html" title="com.amap.api.maps.model中的类">MyLocationStyle</a></dt>
<dd>
<div class="block">得到圆形区域（以定位位置为圆心，定位半径的圆形区域）的填充颜色值。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/animation/Animation.html#getRepeatCount--">getRepeatCount()</a></span> - 类 中的方法com.amap.api.maps.model.animation.<a href="../com/amap/api/maps/model/animation/Animation.html" title="com.amap.api.maps.model.animation中的类">Animation</a></dt>
<dd>
<div class="block">获取动画重复执行的次数,默认为0。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/animation/Animation.html#getRepeatMode--">getRepeatMode()</a></span> - 类 中的方法com.amap.api.maps.model.animation.<a href="../com/amap/api/maps/model/animation/Animation.html" title="com.amap.api.maps.model.animation中的类">Animation</a></dt>
<dd>
<div class="block">重复执行的模式.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/AMapCameraInfo.html#getRotate--">getRotate()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/AMapCameraInfo.html" title="com.amap.api.maps.model中的类">AMapCameraInfo</a></dt>
<dd>
<div class="block">获取旋转角度</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Text.html#getRotate--">getRotate()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Text.html" title="com.amap.api.maps.model中的类">Text</a></dt>
<dd>
<div class="block">获取文字覆盖物的旋转角度，逆时针。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/TextOptions.html#getRotate--">getRotate()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/TextOptions.html" title="com.amap.api.maps.model中的类">TextOptions</a></dt>
<dd>
<div class="block">获取文字覆盖物的旋转角度</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/BasePointOverlay.html#getRotateAngle--">getRotateAngle()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/BasePointOverlay.html" title="com.amap.api.maps.model中的类">BasePointOverlay</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Marker.html#getRotateAngle--">getRotateAngle()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Marker.html" title="com.amap.api.maps.model中的类">Marker</a></dt>
<dd>
<div class="block">获取 Marker覆盖物的图片旋转角度，从正北开始，逆时针计算。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MarkerOptions.html#getRotateAngle--">getRotateAngle()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MarkerOptions.html" title="com.amap.api.maps.model中的类">MarkerOptions</a></dt>
<dd>
<div class="block">获取 Marker覆盖物的图片旋转角度，从正北开始，逆时针计算。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMapOptions.html#getRotateGesturesEnabled--">getRotateGesturesEnabled()</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMapOptions.html" title="com.amap.api.maps中的类">AMapOptions</a></dt>
<dd>
<div class="block">返回初始化选项中地图旋转手势是否可用。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#getSatelliteImageApprovalNumber--">getSatelliteImageApprovalNumber()</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block">获取地图审图号（卫星地图）<br>
 <font color="red">任何使用高德地图API调用地图服务的应用必须在其应用中对外透出审图号</font><br>
 如高德地图在"关于"中体现</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/GLTFOverlayOptions.html#getScale--">getScale()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/GLTFOverlayOptions.html" title="com.amap.api.maps.model中的类">GLTFOverlayOptions</a></dt>
<dd>
<div class="block">获取 模型的缩放比。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMapOptions.html#getScaleControlsEnabled--">getScaleControlsEnabled()</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMapOptions.html" title="com.amap.api.maps中的类">AMapOptions</a></dt>
<dd>
<div class="block">返回初始化选项中比例尺功能是否可用。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#getScalePerPixel--">getScalePerPixel()</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block">获取当前缩放级别下，地图上1像素点对应的长度，单位米。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMapOptions.html#getScrollGesturesEnabled--">getScrollGesturesEnabled()</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMapOptions.html" title="com.amap.api.maps中的类">AMapOptions</a></dt>
<dd>
<div class="block">返回初始化选项中拖动手势是否可用。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MyTrafficStyle.html#getSeriousCongestedColor--">getSeriousCongestedColor()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MyTrafficStyle.html" title="com.amap.api.maps.model中的类">MyTrafficStyle</a></dt>
<dd>
<div class="block">返回行驶严重拥堵路段的标记颜色。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/NavigateArrow.html#getSideColor--">getSideColor()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/NavigateArrow.html" title="com.amap.api.maps.model中的类">NavigateArrow</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/NavigateArrowOptions.html#getSideColor--">getSideColor()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/NavigateArrowOptions.html" title="com.amap.api.maps.model中的类">NavigateArrowOptions</a></dt>
<dd>
<div class="block">获取箭头(NavigateArrow)覆盖物的的侧边颜色，ARGB格式。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/HeatMapLayerOptions.html#getSize--">getSize()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/HeatMapLayerOptions.html" title="com.amap.api.maps.model中的类">HeatMapLayerOptions</a></dt>
<dd>
<div class="block">获取大小</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/OfflineMapCity.html#getSize--">getSize()</a></span> - 类 中的方法com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/OfflineMapCity.html" title="com.amap.api.maps.offlinemap中的类">OfflineMapCity</a></dt>
<dd>
<div class="block">返回下载城市数据的大小，单位字节。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/OfflineMapProvince.html#getSize--">getSize()</a></span> - 类 中的方法com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/OfflineMapProvince.html" title="com.amap.api.maps.offlinemap中的类">OfflineMapProvince</a></dt>
<dd>
<div class="block">返回下载省数据的大小，单位字节。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MyTrafficStyle.html#getSlowColor--">getSlowColor()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MyTrafficStyle.html" title="com.amap.api.maps.model中的类">MyTrafficStyle</a></dt>
<dd>
<div class="block">返回行驶缓慢路段的标记颜色。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MyTrafficStyle.html#getSmoothColor--">getSmoothColor()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MyTrafficStyle.html" title="com.amap.api.maps.model中的类">MyTrafficStyle</a></dt>
<dd>
<div class="block">返回行驶畅通路段的标记颜色。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/BasePointOverlay.html#getSnippet--">getSnippet()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/BasePointOverlay.html" title="com.amap.api.maps.model中的类">BasePointOverlay</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Marker.html#getSnippet--">getSnippet()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Marker.html" title="com.amap.api.maps.model中的类">Marker</a></dt>
<dd>
<div class="block">获取Marker 覆盖物的文字片段。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MarkerOptions.html#getSnippet--">getSnippet()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MarkerOptions.html" title="com.amap.api.maps.model中的类">MarkerOptions</a></dt>
<dd>
<div class="block">获取Marker覆盖物的文字片段。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MultiPointItem.html#getSnippet--">getSnippet()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MultiPointItem.html" title="com.amap.api.maps.model中的类">MultiPointItem</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/trace/TraceLocation.html#getSpeed--">getSpeed()</a></span> - 类 中的方法com.amap.api.trace.<a href="../com/amap/api/trace/TraceLocation.html" title="com.amap.api.trace中的类">TraceLocation</a></dt>
<dd>
<div class="block">获取速度</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/ArcOptions.html#getStart--">getStart()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/ArcOptions.html" title="com.amap.api.maps.model中的类">ArcOptions</a></dt>
<dd>
<div class="block">返回圆弧起点</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/RoutePara.html#getStartName--">getStartName()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/RoutePara.html" title="com.amap.api.maps.model中的类">RoutePara</a></dt>
<dd>
<div class="block">获取路线检索起点地址名称。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/particle/ParticleOverlayOptions.html#getstartParticleH--">getstartParticleH()</a></span> - 类 中的方法com.amap.api.maps.model.particle.<a href="../com/amap/api/maps/model/particle/ParticleOverlayOptions.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlayOptions</a></dt>
<dd>
<div class="block">粒子显示大小-高度</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/particle/ParticleOverlayOptions.html#getStartParticleW--">getStartParticleW()</a></span> - 类 中的方法com.amap.api.maps.model.particle.<a href="../com/amap/api/maps/model/particle/ParticleOverlayOptions.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlayOptions</a></dt>
<dd>
<div class="block">粒子显示大小-宽度</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/RoutePara.html#getStartPoint--">getStartPoint()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/RoutePara.html" title="com.amap.api.maps.model中的类">RoutePara</a></dt>
<dd>
<div class="block">获取起始点的经纬度</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/OfflineMapCity.html#getState--">getState()</a></span> - 类 中的方法com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/OfflineMapCity.html" title="com.amap.api.maps.offlinemap中的类">OfflineMapCity</a></dt>
<dd>
<div class="block">返回城市下载状态。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/OfflineMapProvince.html#getState--">getState()</a></span> - 类 中的方法com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/OfflineMapProvince.html" title="com.amap.api.maps.offlinemap中的类">OfflineMapProvince</a></dt>
<dd>
<div class="block">返回省下载状态。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Arc.html#getStrokeColor--">getStrokeColor()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Arc.html" title="com.amap.api.maps.model中的类">Arc</a></dt>
<dd>
<div class="block">返回弧形的边框颜色</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/ArcOptions.html#getStrokeColor--">getStrokeColor()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/ArcOptions.html" title="com.amap.api.maps.model中的类">ArcOptions</a></dt>
<dd>
<div class="block">返回弧形边框颜色，ARGB格式。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Circle.html#getStrokeColor--">getStrokeColor()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Circle.html" title="com.amap.api.maps.model中的类">Circle</a></dt>
<dd>
<div class="block">获取圆的边框颜色</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/CircleOptions.html#getStrokeColor--">getStrokeColor()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/CircleOptions.html" title="com.amap.api.maps.model中的类">CircleOptions</a></dt>
<dd>
<div class="block">获取圆的边框颜色。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MyLocationStyle.html#getStrokeColor--">getStrokeColor()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MyLocationStyle.html" title="com.amap.api.maps.model中的类">MyLocationStyle</a></dt>
<dd>
<div class="block">得到圆形区域（以定位位置为圆心，定位半径的圆形区域）边框的颜色值。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Polygon.html#getStrokeColor--">getStrokeColor()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Polygon.html" title="com.amap.api.maps.model中的类">Polygon</a></dt>
<dd>
<div class="block">获取多边形的边框颜色</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/PolygonOptions.html#getStrokeColor--">getStrokeColor()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/PolygonOptions.html" title="com.amap.api.maps.model中的类">PolygonOptions</a></dt>
<dd>
<div class="block">获取多边形的边框颜色</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Circle.html#getStrokeDottedLineType--">getStrokeDottedLineType()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Circle.html" title="com.amap.api.maps.model中的类">Circle</a></dt>
<dd>
<div class="block">获取圆的边框虚线形状。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/CircleOptions.html#getStrokeDottedLineType--">getStrokeDottedLineType()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/CircleOptions.html" title="com.amap.api.maps.model中的类">CircleOptions</a></dt>
<dd>
<div class="block">获取圆的边框虚线形状。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Arc.html#getStrokeWidth--">getStrokeWidth()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Arc.html" title="com.amap.api.maps.model中的类">Arc</a></dt>
<dd>
<div class="block">返回弧形的边框宽度</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/ArcOptions.html#getStrokeWidth--">getStrokeWidth()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/ArcOptions.html" title="com.amap.api.maps.model中的类">ArcOptions</a></dt>
<dd>
<div class="block">返回弧形边框宽度，单位：像素。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Circle.html#getStrokeWidth--">getStrokeWidth()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Circle.html" title="com.amap.api.maps.model中的类">Circle</a></dt>
<dd>
<div class="block">获取圆的边框宽度。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/CircleOptions.html#getStrokeWidth--">getStrokeWidth()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/CircleOptions.html" title="com.amap.api.maps.model中的类">CircleOptions</a></dt>
<dd>
<div class="block">获取圆的边框宽度。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MyLocationStyle.html#getStrokeWidth--">getStrokeWidth()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MyLocationStyle.html" title="com.amap.api.maps.model中的类">MyLocationStyle</a></dt>
<dd>
<div class="block">得到圆形区域（以定位位置为圆心，定位半径的圆形区域）边框的宽度。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Polygon.html#getStrokeWidth--">getStrokeWidth()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Polygon.html" title="com.amap.api.maps.model中的类">Polygon</a></dt>
<dd>
<div class="block">获取多边形的边框宽度</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/PolygonOptions.html#getStrokeWidth--">getStrokeWidth()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/PolygonOptions.html" title="com.amap.api.maps.model中的类">PolygonOptions</a></dt>
<dd>
<div class="block">获取多边形的边框宽度</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/CustomMapStyleOptions.html#getStyleData--">getStyleData()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/CustomMapStyleOptions.html" title="com.amap.api.maps.model中的类">CustomMapStyleOptions</a></dt>
<dd>
<div class="block">自定义样式二进制，使用二进制可以更快加载出自定义样式</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/CustomMapStyleOptions.html#getStyleDataPath--">getStyleDataPath()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/CustomMapStyleOptions.html" title="com.amap.api.maps.model中的类">CustomMapStyleOptions</a></dt>
<dd>
<div class="block">自定义样式路径</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/CustomMapStyleOptions.html#getStyleExtraData--">getStyleExtraData()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/CustomMapStyleOptions.html" title="com.amap.api.maps.model中的类">CustomMapStyleOptions</a></dt>
<dd>
<div class="block">样式额外的配置，比如路况，背景颜色等，使用二进制可以更快加载出自定义样式，如果设置了则不会读取styleExtraPath</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/CustomMapStyleOptions.html#getStyleExtraPath--">getStyleExtraPath()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/CustomMapStyleOptions.html" title="com.amap.api.maps.model中的类">CustomMapStyleOptions</a></dt>
<dd>
<div class="block">样式额外的配置，比如路况，背景颜色等 文件路径</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/CustomMapStyleOptions.html#getStyleId--">getStyleId()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/CustomMapStyleOptions.html" title="com.amap.api.maps.model中的类">CustomMapStyleOptions</a></dt>
<dd>
<div class="block">设置的底图自定义样式对应的styleID</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/CustomMapStyleOptions.html#getStyleTextureData--">getStyleTextureData()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/CustomMapStyleOptions.html" title="com.amap.api.maps.model中的类">CustomMapStyleOptions</a></dt>
<dd>
<div class="block">自定义样式纹理二进制，使用二进制可以更快加载出自定义样式</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/CustomMapStyleOptions.html#getStyleTexturePath--">getStyleTexturePath()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/CustomMapStyleOptions.html" title="com.amap.api.maps.model中的类">CustomMapStyleOptions</a></dt>
<dd>
<div class="block">自定义样式纹理路径</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/NaviPara.html#getTargetPoint--">getTargetPoint()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/NaviPara.html" title="com.amap.api.maps.model中的类">NaviPara</a></dt>
<dd>
<div class="block">获取导航目的地坐标。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#getTerrainApprovalNumber--">getTerrainApprovalNumber()</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block">获取地图审图号 （地形图）<br>
 <font color="red">任何使用高德地图API调用地图服务的应用必须在其应用中对外透出审图号</font><br>
 如高德地图在"关于"中体现</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Text.html#getText--">getText()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Text.html" title="com.amap.api.maps.model中的类">Text</a></dt>
<dd>
<div class="block">获取文字覆盖物的文字内容</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/TextOptions.html#getText--">getText()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/TextOptions.html" title="com.amap.api.maps.model中的类">TextOptions</a></dt>
<dd>
<div class="block">获取文字覆盖物的文字内容</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/HeatmapTileProvider.html#getTile-int-int-int-">getTile(int, int, int)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/HeatmapTileProvider.html" title="com.amap.api.maps.model中的类">HeatmapTileProvider</a></dt>
<dd>
<div class="block">根据瓦片列号行号和地图zoom级别获取瓦片对象</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MVTTileProvider.html#getTile-int-int-int-">getTile(int, int, int)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MVTTileProvider.html" title="com.amap.api.maps.model中的类">MVTTileProvider</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/TileProvider.html#getTile-int-int-int-">getTile(int, int, int)</a></span> - 接口 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/TileProvider.html" title="com.amap.api.maps.model中的接口">TileProvider</a></dt>
<dd>
<div class="block">获取指定坐标和缩放级别下的瓦片图层对象。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/UrlTileProvider.html#getTile-int-int-int-">getTile(int, int, int)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/UrlTileProvider.html" title="com.amap.api.maps.model中的类">UrlTileProvider</a></dt>
<dd>
<div class="block">获取指定瓦片坐标的tile对象。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/HeatmapTileProvider.html#getTileHeight--">getTileHeight()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/HeatmapTileProvider.html" title="com.amap.api.maps.model中的类">HeatmapTileProvider</a></dt>
<dd>
<div class="block">获取瓦片高度</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MVTTileProvider.html#getTileHeight--">getTileHeight()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MVTTileProvider.html" title="com.amap.api.maps.model中的类">MVTTileProvider</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/TileProvider.html#getTileHeight--">getTileHeight()</a></span> - 接口 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/TileProvider.html" title="com.amap.api.maps.model中的接口">TileProvider</a></dt>
<dd>
<div class="block">获取指定瓦片图层的图片像素高度，单位像素pixel。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/UrlTileProvider.html#getTileHeight--">getTileHeight()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/UrlTileProvider.html" title="com.amap.api.maps.model中的类">UrlTileProvider</a></dt>
<dd>
<div class="block">获取瓦片的图片高度。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/TileOverlayOptions.html#getTileProvider--">getTileProvider()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/TileOverlayOptions.html" title="com.amap.api.maps.model中的类">TileOverlayOptions</a></dt>
<dd>
<div class="block">获取瓦片图层的提供者。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MVTTileOverlayOptions.html#getTileProviderInner--">getTileProviderInner()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MVTTileOverlayOptions.html" title="com.amap.api.maps.model中的类">MVTTileOverlayOptions</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/UrlTileProvider.html#getTileUrl-int-int-int-">getTileUrl(int, int, int)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/UrlTileProvider.html" title="com.amap.api.maps.model中的类">UrlTileProvider</a></dt>
<dd>
<div class="block">获取指定瓦片坐标对应图片的URL。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/HeatmapTileProvider.html#getTileWidth--">getTileWidth()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/HeatmapTileProvider.html" title="com.amap.api.maps.model中的类">HeatmapTileProvider</a></dt>
<dd>
<div class="block">获取瓦片宽度</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MVTTileProvider.html#getTileWidth--">getTileWidth()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MVTTileProvider.html" title="com.amap.api.maps.model中的类">MVTTileProvider</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/TileProvider.html#getTileWidth--">getTileWidth()</a></span> - 接口 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/TileProvider.html" title="com.amap.api.maps.model中的接口">TileProvider</a></dt>
<dd>
<div class="block">获取瓦片图层的图片像素宽度，单位像素pixel。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/UrlTileProvider.html#getTileWidth--">getTileWidth()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/UrlTileProvider.html" title="com.amap.api.maps.model中的类">UrlTileProvider</a></dt>
<dd>
<div class="block">获取瓦片的图片宽度。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMapOptions.html#getTiltGesturesEnabled--">getTiltGesturesEnabled()</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMapOptions.html" title="com.amap.api.maps中的类">AMapOptions</a></dt>
<dd>
<div class="block">返回初始化选项中地图倾斜手势（显示3D效果）是否可用。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/trace/TraceLocation.html#getTime--">getTime()</a></span> - 类 中的方法com.amap.api.trace.<a href="../com/amap/api/trace/TraceLocation.html" title="com.amap.api.trace中的类">TraceLocation</a></dt>
<dd>
<div class="block">获取定位时间</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/BasePointOverlay.html#getTitle--">getTitle()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/BasePointOverlay.html" title="com.amap.api.maps.model中的类">BasePointOverlay</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Marker.html#getTitle--">getTitle()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Marker.html" title="com.amap.api.maps.model中的类">Marker</a></dt>
<dd>
<div class="block">获取Marker 覆盖物的标题。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MarkerOptions.html#getTitle--">getTitle()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MarkerOptions.html" title="com.amap.api.maps.model中的类">MarkerOptions</a></dt>
<dd>
<div class="block">获取Marker覆盖物的标题。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MultiPointItem.html#getTitle--">getTitle()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MultiPointItem.html" title="com.amap.api.maps.model中的类">MultiPointItem</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/NavigateArrow.html#getTopColor--">getTopColor()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/NavigateArrow.html" title="com.amap.api.maps.model中的类">NavigateArrow</a></dt>
<dd>
<div class="block">获取(NavigateArrow)覆盖物的顶颜色。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/NavigateArrowOptions.html#getTopColor--">getTopColor()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/NavigateArrowOptions.html" title="com.amap.api.maps.model中的类">NavigateArrowOptions</a></dt>
<dd>
<div class="block">获取箭头(NavigateArrow)覆盖物的顶颜色，ARGB格式。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/trace/TraceOverlay.html#getTraceStatus--">getTraceStatus()</a></span> - 类 中的方法com.amap.api.trace.<a href="../com/amap/api/trace/TraceOverlay.html" title="com.amap.api.trace中的类">TraceOverlay</a></dt>
<dd>
<div class="block">获取轨迹绘制状态</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/RoutePara.html#getTransitRouteStyle--">getTransitRouteStyle()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/RoutePara.html" title="com.amap.api.maps.model中的类">RoutePara</a></dt>
<dd>
<div class="block">公交驾车模式</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/GroundOverlay.html#getTransparency--">getTransparency()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/GroundOverlay.html" title="com.amap.api.maps.model中的类">GroundOverlay</a></dt>
<dd>
<div class="block">获取ground 覆盖物的透明度。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/GroundOverlayOptions.html#getTransparency--">getTransparency()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/GroundOverlayOptions.html" title="com.amap.api.maps.model中的类">GroundOverlayOptions</a></dt>
<dd>
<div class="block">获取 ground 覆盖物的透明度。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/PolylineOptions.html#getTransparency--">getTransparency()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/PolylineOptions.html" title="com.amap.api.maps.model中的类">PolylineOptions</a></dt>
<dd>
<div class="block">获取线的透明度</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/HeatMapGridLayerOptions.html#getType--">getType()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/HeatMapGridLayerOptions.html" title="com.amap.api.maps.model中的类">HeatMapGridLayerOptions</a></dt>
<dd>
<div class="block">获取热力图类型</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/HeatMapLayerOptions.html#getType--">getType()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/HeatMapLayerOptions.html" title="com.amap.api.maps.model中的类">HeatMapLayerOptions</a></dt>
<dd>
<div class="block">获取热力图类型</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/TileOverlaySource.html#getType--">getType()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/TileOverlaySource.html" title="com.amap.api.maps.model中的类">TileOverlaySource</a></dt>
<dd>
<div class="block">数据源类型</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Text.html#getTypeface--">getTypeface()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Text.html" title="com.amap.api.maps.model中的类">Text</a></dt>
<dd>
<div class="block">获取文字覆盖物的字体样式</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/TextOptions.html#getTypeface--">getTypeface()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/TextOptions.html" title="com.amap.api.maps.model中的类">TextOptions</a></dt>
<dd>
<div class="block">获取文字覆盖物的字体。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/AMapPara.LineCapType.html#getTypeValue--">getTypeValue()</a></span> - 枚举 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/AMapPara.LineCapType.html" title="com.amap.api.maps.model中的枚举">AMapPara.LineCapType</a></dt>
<dd>
<div class="block">获取形状值</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/AMapPara.LineJoinType.html#getTypeValue--">getTypeValue()</a></span> - 枚举 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/AMapPara.LineJoinType.html" title="com.amap.api.maps.model中的枚举">AMapPara.LineJoinType</a></dt>
<dd>
<div class="block">获取形状值</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/PolylineOptions.LineCapType.html#getTypeValue--">getTypeValue()</a></span> - 枚举 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/PolylineOptions.LineCapType.html" title="com.amap.api.maps.model中的枚举">PolylineOptions.LineCapType</a></dt>
<dd>
<div class="block">获取形状值</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/PolylineOptions.LineJoinType.html#getTypeValue--">getTypeValue()</a></span> - 枚举 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/PolylineOptions.LineJoinType.html" title="com.amap.api.maps.model中的枚举">PolylineOptions.LineJoinType</a></dt>
<dd>
<div class="block">获取形状值</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#getUiSettings--">getUiSettings()</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block">获取地图ui控制器，可以控制内置ui（缩放按钮、指北针等）是否显示及部分手势（滑动、双指缩放等）是否可用。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/GLTFOverlayOptions.html#getUriResources--">getUriResources()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/GLTFOverlayOptions.html" title="com.amap.api.maps.model中的类">GLTFOverlayOptions</a></dt>
<dd>
<div class="block">获取 glTF对应uri资源。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/amap3dmodeltile/AMap3DModelTileProvider.AMap3DModelRequest.html#getURL--">getURL()</a></span> - 类 中的方法com.amap.api.maps.model.amap3dmodeltile.<a href="../com/amap/api/maps/model/amap3dmodeltile/AMap3DModelTileProvider.AMap3DModelRequest.html" title="com.amap.api.maps.model.amap3dmodeltile中的类">AMap3DModelTileProvider.AMap3DModelRequest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MVTTileProvider.html#getUrl--">getUrl()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MVTTileProvider.html" title="com.amap.api.maps.model中的类">MVTTileProvider</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/TileOverlaySource.html#getUrl--">getUrl()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/TileOverlaySource.html" title="com.amap.api.maps.model中的类">TileOverlaySource</a></dt>
<dd>
<div class="block">数据源类型 下载地址</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/OfflineMapCity.html#getUrl--">getUrl()</a></span> - 类 中的方法com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/OfflineMapCity.html" title="com.amap.api.maps.offlinemap中的类">OfflineMapCity</a></dt>
<dd>
<div class="block">返回所下载城市的数据地址。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/OfflineMapProvince.html#getUrl--">getUrl()</a></span> - 类 中的方法com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/OfflineMapProvince.html" title="com.amap.api.maps.offlinemap中的类">OfflineMapProvince</a></dt>
<dd>
<div class="block">返回所下载省的数据地址。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMap.html#getVersion--">getVersion()</a></span> - 类 中的静态方法com.amap.api.maps.<a href="../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/MapsInitializer.html#getVersion--">getVersion()</a></span> - 类 中的静态方法com.amap.api.maps.<a href="../com/amap/api/maps/MapsInitializer.html" title="com.amap.api.maps中的类">MapsInitializer</a></dt>
<dd>
<div class="block">返回地图SDK的版本号。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/OfflineMapCity.html#getVersion--">getVersion()</a></span> - 类 中的方法com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/OfflineMapCity.html" title="com.amap.api.maps.offlinemap中的类">OfflineMapCity</a></dt>
<dd>
<div class="block">返回下载城市的数据版本。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/offlinemap/OfflineMapProvince.html#getVersion--">getVersion()</a></span> - 类 中的方法com.amap.api.maps.offlinemap.<a href="../com/amap/api/maps/offlinemap/OfflineMapProvince.html" title="com.amap.api.maps.offlinemap中的类">OfflineMapProvince</a></dt>
<dd>
<div class="block">返回下载省的数据版本。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/Projection.html#getVisibleRegion--">getVisibleRegion()</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/Projection.html" title="com.amap.api.maps中的类">Projection</a></dt>
<dd>
<div class="block">返回当前可视区域（包含MapView四个角点的经纬度坐标）坐标信息。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/trace/TraceOverlay.html#getWaitTime--">getWaitTime()</a></span> - 类 中的方法com.amap.api.trace.<a href="../com/amap/api/trace/TraceOverlay.html" title="com.amap.api.trace中的类">TraceOverlay</a></dt>
<dd>
<div class="block">获取线路停车时间</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/BitmapDescriptor.html#getWidth--">getWidth()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/BitmapDescriptor.html" title="com.amap.api.maps.model中的类">BitmapDescriptor</a></dt>
<dd>
<div class="block">返回 Bitmap的宽度。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/GroundOverlay.html#getWidth--">getWidth()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/GroundOverlay.html" title="com.amap.api.maps.model中的类">GroundOverlay</a></dt>
<dd>
<div class="block">获取 ground 覆盖物的宽度。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/GroundOverlayOptions.html#getWidth--">getWidth()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/GroundOverlayOptions.html" title="com.amap.api.maps.model中的类">GroundOverlayOptions</a></dt>
<dd>
<div class="block">获取 ground 覆盖物的宽 。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/NavigateArrow.html#getWidth--">getWidth()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/NavigateArrow.html" title="com.amap.api.maps.model中的类">NavigateArrow</a></dt>
<dd>
<div class="block">返回箭头(NavigateArrow)覆盖物的宽度值</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/NavigateArrowOptions.html#getWidth--">getWidth()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/NavigateArrowOptions.html" title="com.amap.api.maps.model中的类">NavigateArrowOptions</a></dt>
<dd>
<div class="block">获取箭头(NavigateArrow)覆盖物的宽度。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Polyline.html#getWidth--">getWidth()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Polyline.html" title="com.amap.api.maps.model中的类">Polyline</a></dt>
<dd>
<div class="block">获取线段的宽度</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/PolylineOptions.html#getWidth--">getWidth()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/PolylineOptions.html" title="com.amap.api.maps.model中的类">PolylineOptions</a></dt>
<dd>
<div class="block">获取线段的宽度。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/MapsInitializer.html#getWorldVectorOfflineMapStyleAssetsPath--">getWorldVectorOfflineMapStyleAssetsPath()</a></span> - 类 中的静态方法com.amap.api.maps.<a href="../com/amap/api/maps/MapsInitializer.html" title="com.amap.api.maps中的类">MapsInitializer</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/AMapCameraInfo.html#getX--">getX()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/AMapCameraInfo.html" title="com.amap.api.maps.model中的类">AMapCameraInfo</a></dt>
<dd>
<div class="block">获取相机位置坐标</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/GLTFOverlayOptions.html#getXDegree--">getXDegree()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/GLTFOverlayOptions.html" title="com.amap.api.maps.model中的类">GLTFOverlayOptions</a></dt>
<dd>
<div class="block">获取 模型的X旋转角度。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/AMapCameraInfo.html#getY--">getY()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/AMapCameraInfo.html" title="com.amap.api.maps.model中的类">AMapCameraInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/GLTFOverlayOptions.html#getYDegree--">getYDegree()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/GLTFOverlayOptions.html" title="com.amap.api.maps.model中的类">GLTFOverlayOptions</a></dt>
<dd>
<div class="block">获取 模型的Y旋转角度。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/AMapCameraInfo.html#getZ--">getZ()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/AMapCameraInfo.html" title="com.amap.api.maps.model中的类">AMapCameraInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/GLTFOverlayOptions.html#getZDegree--">getZDegree()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/GLTFOverlayOptions.html" title="com.amap.api.maps.model中的类">GLTFOverlayOptions</a></dt>
<dd>
<div class="block">获取 模型的Z旋转角度。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Arc.html#getZIndex--">getZIndex()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Arc.html" title="com.amap.api.maps.model中的类">Arc</a></dt>
<dd>
<div class="block">返回圆弧Z轴数值</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/ArcOptions.html#getZIndex--">getZIndex()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/ArcOptions.html" title="com.amap.api.maps.model中的类">ArcOptions</a></dt>
<dd>
<div class="block">返回弧形Z轴的数值。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/BuildingOverlay.html#getZIndex--">getZIndex()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/BuildingOverlay.html" title="com.amap.api.maps.model中的类">BuildingOverlay</a></dt>
<dd>
<div class="block">获取overlay的zindex值</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/BuildingOverlayOptions.html#getZIndex--">getZIndex()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/BuildingOverlayOptions.html" title="com.amap.api.maps.model中的类">BuildingOverlayOptions</a></dt>
<dd>
<div class="block">获取zindex值</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Circle.html#getZIndex--">getZIndex()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Circle.html" title="com.amap.api.maps.model中的类">Circle</a></dt>
<dd>
<div class="block">获取圆的Z轴数值</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/CircleOptions.html#getZIndex--">getZIndex()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/CircleOptions.html" title="com.amap.api.maps.model中的类">CircleOptions</a></dt>
<dd>
<div class="block">获取圆的Z轴的值</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/GroundOverlay.html#getZIndex--">getZIndex()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/GroundOverlay.html" title="com.amap.api.maps.model中的类">GroundOverlay</a></dt>
<dd>
<div class="block">获取ground 覆盖物的z轴指数。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/GroundOverlayOptions.html#getZIndex--">getZIndex()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/GroundOverlayOptions.html" title="com.amap.api.maps.model中的类">GroundOverlayOptions</a></dt>
<dd>
<div class="block">获取 ground 覆盖物的z轴指数。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/HeatMapGridLayer.html#getZIndex--">getZIndex()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/HeatMapGridLayer.html" title="com.amap.api.maps.model中的类">HeatMapGridLayer</a></dt>
<dd>
<div class="block">获取图层的Z轴值</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/HeatMapGridLayerOptions.html#getZIndex--">getZIndex()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/HeatMapGridLayerOptions.html" title="com.amap.api.maps.model中的类">HeatMapGridLayerOptions</a></dt>
<dd>
<div class="block">获取的Z轴值</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/HeatMapLayer.html#getZIndex--">getZIndex()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/HeatMapLayer.html" title="com.amap.api.maps.model中的类">HeatMapLayer</a></dt>
<dd>
<div class="block">获取图层的Z轴值</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/HeatMapLayerOptions.html#getZIndex--">getZIndex()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/HeatMapLayerOptions.html" title="com.amap.api.maps.model中的类">HeatMapLayerOptions</a></dt>
<dd>
<div class="block">获取的Z轴值</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Marker.html#getZIndex--">getZIndex()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Marker.html" title="com.amap.api.maps.model中的类">Marker</a></dt>
<dd>
<div class="block">获取Marker覆盖物的z轴值。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MarkerOptions.html#getZIndex--">getZIndex()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MarkerOptions.html" title="com.amap.api.maps.model中的类">MarkerOptions</a></dt>
<dd>
<div class="block">获取Marker覆盖物zIndex。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MVTTileOverlay.html#getZIndex--">getZIndex()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MVTTileOverlay.html" title="com.amap.api.maps.model中的类">MVTTileOverlay</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MVTTileOverlayOptions.html#getZIndex--">getZIndex()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MVTTileOverlayOptions.html" title="com.amap.api.maps.model中的类">MVTTileOverlayOptions</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/NavigateArrow.html#getZIndex--">getZIndex()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/NavigateArrow.html" title="com.amap.api.maps.model中的类">NavigateArrow</a></dt>
<dd>
<div class="block">获取箭头(NavigateArrow)覆盖物的z轴值。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/NavigateArrowOptions.html#getZIndex--">getZIndex()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/NavigateArrowOptions.html" title="com.amap.api.maps.model中的类">NavigateArrowOptions</a></dt>
<dd>
<div class="block">获取箭头(NavigateArrow)覆盖物的Z轴值</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/particle/ParticleOverlayOptions.html#getZIndex--">getZIndex()</a></span> - 类 中的方法com.amap.api.maps.model.particle.<a href="../com/amap/api/maps/model/particle/ParticleOverlayOptions.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlayOptions</a></dt>
<dd>
<div class="block">获取覆盖物的Z轴值</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Polygon.html#getZIndex--">getZIndex()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Polygon.html" title="com.amap.api.maps.model中的类">Polygon</a></dt>
<dd>
<div class="block">获取多边形的Z轴值</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/PolygonOptions.html#getZIndex--">getZIndex()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/PolygonOptions.html" title="com.amap.api.maps.model中的类">PolygonOptions</a></dt>
<dd>
<div class="block">获取多边形的Z轴数值</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Polyline.html#getZIndex--">getZIndex()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Polyline.html" title="com.amap.api.maps.model中的类">Polyline</a></dt>
<dd>
<div class="block">获取线段的z轴值。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/PolylineOptions.html#getZIndex--">getZIndex()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/PolylineOptions.html" title="com.amap.api.maps.model中的类">PolylineOptions</a></dt>
<dd>
<div class="block">获取线段的Z轴值</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Text.html#getZIndex--">getZIndex()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Text.html" title="com.amap.api.maps.model中的类">Text</a></dt>
<dd>
<div class="block">获取文字覆盖物的z轴数值。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/TextOptions.html#getZIndex--">getZIndex()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/TextOptions.html" title="com.amap.api.maps.model中的类">TextOptions</a></dt>
<dd>
<div class="block">获取文字覆盖物的z轴数值</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/TileOverlay.html#getZIndex--">getZIndex()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/TileOverlay.html" title="com.amap.api.maps.model中的类">TileOverlay</a></dt>
<dd>
<div class="block">获取瓦片图层的Z轴值</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/TileOverlayOptions.html#getZIndex--">getZIndex()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/TileOverlayOptions.html" title="com.amap.api.maps.model中的类">TileOverlayOptions</a></dt>
<dd>
<div class="block">获取瓦片图层的Z轴数值。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMapOptions.html#getZoomControlsEnabled--">getZoomControlsEnabled()</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMapOptions.html" title="com.amap.api.maps中的类">AMapOptions</a></dt>
<dd>
<div class="block">返回初始化选项中地图是否允许缩放。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMapOptions.html#getZoomGesturesEnabled--">getZoomGesturesEnabled()</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/AMapOptions.html" title="com.amap.api.maps中的类">AMapOptions</a></dt>
<dd>
<div class="block">返回初始化选项中缩放手势是否可用。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/UiSettings.html#getZoomPosition--">getZoomPosition()</a></span> - 类 中的方法com.amap.api.maps.<a href="../com/amap/api/maps/UiSettings.html" title="com.amap.api.maps中的类">UiSettings</a></dt>
<dd>
<div class="block">获取缩放按钮的位置。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/TileOverlay.html#glOverlayLayerRef">glOverlayLayerRef</a></span> - 类 中的变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/TileOverlay.html" title="com.amap.api.maps.model中的类">TileOverlay</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/amap/api/maps/model/GLTFOverlay.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">GLTFOverlay</span></a> - <a href="../com/amap/api/maps/model/package-summary.html">com.amap.api.maps.model</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><a href="../com/amap/api/maps/model/GLTFOverlayOptions.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">GLTFOverlayOptions</span></a> - <a href="../com/amap/api/maps/model/package-summary.html">com.amap.api.maps.model</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/GLTFOverlayOptions.html#GLTFOverlayOptions--">GLTFOverlayOptions()</a></span> - 类 的构造器com.amap.api.maps.model.<a href="../com/amap/api/maps/model/GLTFOverlayOptions.html" title="com.amap.api.maps.model中的类">GLTFOverlayOptions</a></dt>
<dd>
<div class="block">GLTF Overlay选项类的构造函数。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/GLTFOverlayOptions.html#GLTFOverlayOptions-android.os.Parcel-">GLTFOverlayOptions(Parcel)</a></span> - 类 的构造器com.amap.api.maps.model.<a href="../com/amap/api/maps/model/GLTFOverlayOptions.html" title="com.amap.api.maps.model中的类">GLTFOverlayOptions</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/amap/api/maps/model/GLTFOverlayOptionsCreator.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">GLTFOverlayOptionsCreator</span></a> - <a href="../com/amap/api/maps/model/package-summary.html">com.amap.api.maps.model</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/GLTFOverlayOptionsCreator.html#GLTFOverlayOptionsCreator--">GLTFOverlayOptionsCreator()</a></span> - 类 的构造器com.amap.api.maps.model.<a href="../com/amap/api/maps/model/GLTFOverlayOptionsCreator.html" title="com.amap.api.maps.model中的类">GLTFOverlayOptionsCreator</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/amap/api/maps/model/GLTFResourceIterm.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">GLTFResourceIterm</span></a> - <a href="../com/amap/api/maps/model/package-summary.html">com.amap.api.maps.model</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/GLTFResourceIterm.html#GLTFResourceIterm-java.lang.String-byte:A-">GLTFResourceIterm(String, byte[])</a></span> - 类 的构造器com.amap.api.maps.model.<a href="../com/amap/api/maps/model/GLTFResourceIterm.html" title="com.amap.api.maps.model中的类">GLTFResourceIterm</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/amap/api/maps/model/Gradient.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">Gradient</span></a> - <a href="../com/amap/api/maps/model/package-summary.html">com.amap.api.maps.model</a>中的类</dt>
<dd>
<div class="block">热力图渐变颜色定义类</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/Gradient.html#Gradient-int:A-float:A-">Gradient(int[], float[])</a></span> - 类 的构造器com.amap.api.maps.model.<a href="../com/amap/api/maps/model/Gradient.html" title="com.amap.api.maps.model中的类">Gradient</a></dt>
<dd>
<div class="block">构造函数，color和statPoints不能为null，长度不能为0，两数组长度须一致，startPoints数据必须递增。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/HeatMapLayerOptions.html#gradient-com.amap.api.maps.model.Gradient-">gradient(Gradient)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/HeatMapLayerOptions.html" title="com.amap.api.maps.model中的类">HeatMapLayerOptions</a></dt>
<dd>
<div class="block">设置热力图渐变，有默认值 DEFAULT_GRADIENT，可不设置该接口</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/HeatmapTileProvider.Builder.html#gradient-com.amap.api.maps.model.Gradient-">gradient(Gradient)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/HeatmapTileProvider.Builder.html" title="com.amap.api.maps.model中的类">HeatmapTileProvider.Builder</a></dt>
<dd>
<div class="block">设置热力图渐变，有默认值 DEFAULT_GRADIENT，可不设置该接口</div>
</dd>
<dt><a href="../com/amap/api/maps/model/GroundOverlay.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">GroundOverlay</span></a> - <a href="../com/amap/api/maps/model/package-summary.html">com.amap.api.maps.model</a>中的类</dt>
<dd>
<div class="block">定义在地图上绘制一个 Ground 覆盖物（一张图片以合适的大小贴在地图上的图片层）
 
 位置： 可以通过设置中心点或者图片区域来确定图片层的位置。</div>
</dd>
<dt><a href="../com/amap/api/maps/model/GroundOverlayOptions.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">GroundOverlayOptions</span></a> - <a href="../com/amap/api/maps/model/package-summary.html">com.amap.api.maps.model</a>中的类</dt>
<dd>
<div class="block">ground 覆盖物的选项类。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/GroundOverlayOptions.html#GroundOverlayOptions--">GroundOverlayOptions()</a></span> - 类 的构造器com.amap.api.maps.model.<a href="../com/amap/api/maps/model/GroundOverlayOptions.html" title="com.amap.api.maps.model中的类">GroundOverlayOptions</a></dt>
<dd>
<div class="block">ground 覆盖物的选项类的构造函数。</div>
</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">K</a>&nbsp;<a href="index-11.html">L</a>&nbsp;<a href="index-12.html">M</a>&nbsp;<a href="index-13.html">N</a>&nbsp;<a href="index-14.html">O</a>&nbsp;<a href="index-15.html">P</a>&nbsp;<a href="index-16.html">Q</a>&nbsp;<a href="index-17.html">R</a>&nbsp;<a href="index-18.html">S</a>&nbsp;<a href="index-19.html">T</a>&nbsp;<a href="index-20.html">U</a>&nbsp;<a href="index-21.html">V</a>&nbsp;<a href="index-22.html">W</a>&nbsp;<a href="index-23.html">Z</a>&nbsp;</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-6.html">上一个字母</a></li>
<li><a href="index-8.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-7.html" target="_top">框架</a></li>
<li><a href="index-7.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
