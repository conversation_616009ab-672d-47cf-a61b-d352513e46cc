<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>B - 索引</title>
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="B - \u7D22\u5F15";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-1.html">上一个字母</a></li>
<li><a href="index-3.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-2.html" target="_top">框架</a></li>
<li><a href="index-2.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="contentContainer"><a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">K</a>&nbsp;<a href="index-11.html">L</a>&nbsp;<a href="index-12.html">M</a>&nbsp;<a href="index-13.html">N</a>&nbsp;<a href="index-14.html">O</a>&nbsp;<a href="index-15.html">P</a>&nbsp;<a href="index-16.html">Q</a>&nbsp;<a href="index-17.html">R</a>&nbsp;<a href="index-18.html">S</a>&nbsp;<a href="index-19.html">T</a>&nbsp;<a href="index-20.html">U</a>&nbsp;<a href="index-21.html">V</a>&nbsp;<a href="index-22.html">W</a>&nbsp;<a href="index-23.html">Z</a>&nbsp;<a name="I:B">
<!--   -->
</a>
<h2 class="title">B</h2>
<dl>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/TextOptions.html#backgroundColor-int-">backgroundColor(int)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/TextOptions.html" title="com.amap.api.maps.model中的类">TextOptions</a></dt>
<dd>
<div class="block">设置文字覆盖物的背景颜色</div>
</dd>
<dt><a href="../com/amap/api/maps/model/BaseOptions.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">BaseOptions</span></a> - <a href="../com/amap/api/maps/model/package-summary.html">com.amap.api.maps.model</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/BaseOptions.html#BaseOptions--">BaseOptions()</a></span> - 类 的构造器com.amap.api.maps.model.<a href="../com/amap/api/maps/model/BaseOptions.html" title="com.amap.api.maps.model中的类">BaseOptions</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/amap/api/maps/model/BasePointOverlay.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">BasePointOverlay</span></a> - <a href="../com/amap/api/maps/model/package-summary.html">com.amap.api.maps.model</a>中的类</dt>
<dd>
<div class="block">点类型覆盖物基础对象，包含该设置位置、角度和信息窗体等基本信息</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/CameraPosition.html#bearing">bearing</a></span> - 类 中的变量com.amap.api.maps.model.<a href="../com/amap/api/maps/model/CameraPosition.html" title="com.amap.api.maps.model中的类">CameraPosition</a></dt>
<dd>
<div class="block">可视区域指向的方向，以角度为单位，从正北向逆时针方向计算，从0 度到360 度。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/CameraPosition.Builder.html#bearing-float-">bearing(float)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/CameraPosition.Builder.html" title="com.amap.api.maps.model中的类">CameraPosition.Builder</a></dt>
<dd>
<div class="block">设置可视区域的旋转方向，以角度为单位，正北方向到地图方向逆时针旋转的角度，范围从0度到360度</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/GroundOverlayOptions.html#bearing-float-">bearing(float)</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/GroundOverlayOptions.html" title="com.amap.api.maps.model中的类">GroundOverlayOptions</a></dt>
<dd>
<div class="block">设置ground 覆盖物从正北顺时针的角度，相对锚点旋转。</div>
</dd>
<dt><a href="../com/amap/api/maps/model/BitmapDescriptor.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">BitmapDescriptor</span></a> - <a href="../com/amap/api/maps/model/package-summary.html">com.amap.api.maps.model</a>中的类</dt>
<dd>
<div class="block">bitmap 描述信息
 在高德地图API 里，如果需要将一张图片绘制为Marker，需要用这个类把图片包装成对象，可以通过BitmapDescriptorFactory
 获得一个BitmapDescriptor 对象。</div>
</dd>
<dt><a href="../com/amap/api/maps/model/BitmapDescriptorFactory.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">BitmapDescriptorFactory</span></a> - <a href="../com/amap/api/maps/model/package-summary.html">com.amap.api.maps.model</a>中的类</dt>
<dd>
<div class="block">bitmap 描述信息工厂类
 创建BitmapDescriptor 对象。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/BitmapDescriptorFactory.html#BitmapDescriptorFactory--">BitmapDescriptorFactory()</a></span> - 类 的构造器com.amap.api.maps.model.<a href="../com/amap/api/maps/model/BitmapDescriptorFactory.html" title="com.amap.api.maps.model中的类">BitmapDescriptorFactory</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/CameraPosition.Builder.html#build--">build()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/CameraPosition.Builder.html" title="com.amap.api.maps.model中的类">CameraPosition.Builder</a></dt>
<dd>
<div class="block">构造一个CameraPosition 对象。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/HeatmapTileProvider.Builder.html#build--">build()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/HeatmapTileProvider.Builder.html" title="com.amap.api.maps.model中的类">HeatmapTileProvider.Builder</a></dt>
<dd>
<div class="block">构造热力图，调用该函数之前必须先通过 data 或者 weightedData 函数设置该热力图所要渲染的数据。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/LatLngBounds.Builder.html#build--">build()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/LatLngBounds.Builder.html" title="com.amap.api.maps.model中的类">LatLngBounds.Builder</a></dt>
<dd>
<div class="block">创建矩形区域。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MVTTileOverlayOptions.Builder.html#build--">build()</a></span> - 类 中的方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MVTTileOverlayOptions.Builder.html" title="com.amap.api.maps.model中的类">MVTTileOverlayOptions.Builder</a></dt>
<dd>
<div class="block">创建MVTTileOverlayOptions</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/CameraPosition.html#builder--">builder()</a></span> - 类 中的静态方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/CameraPosition.html" title="com.amap.api.maps.model中的类">CameraPosition</a></dt>
<dd>
<div class="block">创建一个CameraPosition.Builder 对象</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/CameraPosition.html#builder-com.amap.api.maps.model.CameraPosition-">builder(CameraPosition)</a></span> - 类 中的静态方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/CameraPosition.html" title="com.amap.api.maps.model中的类">CameraPosition</a></dt>
<dd>
<div class="block">根据传入的CameraPosition 创建一个CameraPosition.Builder 对象。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/CameraPosition.Builder.html#Builder--">Builder()</a></span> - 类 的构造器com.amap.api.maps.model.<a href="../com/amap/api/maps/model/CameraPosition.Builder.html" title="com.amap.api.maps.model中的类">CameraPosition.Builder</a></dt>
<dd>
<div class="block">构造一个新的CameraPosition对象。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/CameraPosition.Builder.html#Builder-com.amap.api.maps.model.CameraPosition-">Builder(CameraPosition)</a></span> - 类 的构造器com.amap.api.maps.model.<a href="../com/amap/api/maps/model/CameraPosition.Builder.html" title="com.amap.api.maps.model中的类">CameraPosition.Builder</a></dt>
<dd>
<div class="block">根据给定的参数构造一个CameraPosition的新对象。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/HeatmapTileProvider.Builder.html#Builder--">Builder()</a></span> - 类 的构造器com.amap.api.maps.model.<a href="../com/amap/api/maps/model/HeatmapTileProvider.Builder.html" title="com.amap.api.maps.model中的类">HeatmapTileProvider.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/LatLngBounds.html#builder--">builder()</a></span> - 类 中的静态方法com.amap.api.maps.model.<a href="../com/amap/api/maps/model/LatLngBounds.html" title="com.amap.api.maps.model中的类">LatLngBounds</a></dt>
<dd>
<div class="block">创建一个LatLngBounds构造器。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/LatLngBounds.Builder.html#Builder--">Builder()</a></span> - 类 的构造器com.amap.api.maps.model.<a href="../com/amap/api/maps/model/LatLngBounds.Builder.html" title="com.amap.api.maps.model中的类">LatLngBounds.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/MVTTileOverlayOptions.Builder.html#Builder--">Builder()</a></span> - 类 的构造器com.amap.api.maps.model.<a href="../com/amap/api/maps/model/MVTTileOverlayOptions.Builder.html" title="com.amap.api.maps.model中的类">MVTTileOverlayOptions.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/amap/api/maps/model/BuildingOverlay.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">BuildingOverlay</span></a> - <a href="../com/amap/api/maps/model/package-summary.html">com.amap.api.maps.model</a>中的类</dt>
<dd>
<div class="block">建筑物区域Overlay,可以设置指定区域内建筑物颜色、高度及高度比例等信息，默认区域为全世界。</div>
</dd>
<dt><a href="../com/amap/api/maps/model/BuildingOverlay.BuildingOverlayTotalOptions.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">BuildingOverlay.BuildingOverlayTotalOptions</span></a> - <a href="../com/amap/api/maps/model/package-summary.html">com.amap.api.maps.model</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><a href="../com/amap/api/maps/model/BuildingOverlayOptions.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">BuildingOverlayOptions</span></a> - <a href="../com/amap/api/maps/model/package-summary.html">com.amap.api.maps.model</a>中的类</dt>
<dd>
<div class="block">BuildingOverlay 选项类，包含高度颜色等设置</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/model/BuildingOverlay.BuildingOverlayTotalOptions.html#BuildingOverlayTotalOptions--">BuildingOverlayTotalOptions()</a></span> - 类 的构造器com.amap.api.maps.model.<a href="../com/amap/api/maps/model/BuildingOverlay.BuildingOverlayTotalOptions.html" title="com.amap.api.maps.model中的类">BuildingOverlay.BuildingOverlayTotalOptions</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMapUtils.html#BUS_COMFORT">BUS_COMFORT</a></span> - 类 中的静态变量com.amap.api.maps.<a href="../com/amap/api/maps/AMapUtils.html" title="com.amap.api.maps中的类">AMapUtils</a></dt>
<dd>
<div class="block">公交路径规划策略：（最舒适 : 4）</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMapUtils.html#BUS_MONEY_LITTLE">BUS_MONEY_LITTLE</a></span> - 类 中的静态变量com.amap.api.maps.<a href="../com/amap/api/maps/AMapUtils.html" title="com.amap.api.maps中的类">AMapUtils</a></dt>
<dd>
<div class="block">公交路径规划策略：（费用优先 : 1）</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMapUtils.html#BUS_NO_SUBWAY">BUS_NO_SUBWAY</a></span> - 类 中的静态变量com.amap.api.maps.<a href="../com/amap/api/maps/AMapUtils.html" title="com.amap.api.maps中的类">AMapUtils</a></dt>
<dd>
<div class="block">公交路径规划策略：（不乘地铁 : 5）</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMapUtils.html#BUS_TIME_FIRST">BUS_TIME_FIRST</a></span> - 类 中的静态变量com.amap.api.maps.<a href="../com/amap/api/maps/AMapUtils.html" title="com.amap.api.maps中的类">AMapUtils</a></dt>
<dd>
<div class="block">公交路径规划策略：（最快捷 : 0）</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMapUtils.html#BUS_TRANSFER_LITTLE">BUS_TRANSFER_LITTLE</a></span> - 类 中的静态变量com.amap.api.maps.<a href="../com/amap/api/maps/AMapUtils.html" title="com.amap.api.maps中的类">AMapUtils</a></dt>
<dd>
<div class="block">公交路径规划策略：（最少换乘 : 2）</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/amap/api/maps/AMapUtils.html#BUS_WALK_LITTLE">BUS_WALK_LITTLE</a></span> - 类 中的静态变量com.amap.api.maps.<a href="../com/amap/api/maps/AMapUtils.html" title="com.amap.api.maps中的类">AMapUtils</a></dt>
<dd>
<div class="block">公交路径规划策略：（最少步行 : 3）</div>
</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">K</a>&nbsp;<a href="index-11.html">L</a>&nbsp;<a href="index-12.html">M</a>&nbsp;<a href="index-13.html">N</a>&nbsp;<a href="index-14.html">O</a>&nbsp;<a href="index-15.html">P</a>&nbsp;<a href="index-16.html">Q</a>&nbsp;<a href="index-17.html">R</a>&nbsp;<a href="index-18.html">S</a>&nbsp;<a href="index-19.html">T</a>&nbsp;<a href="index-20.html">U</a>&nbsp;<a href="index-21.html">V</a>&nbsp;<a href="index-22.html">W</a>&nbsp;<a href="index-23.html">Z</a>&nbsp;</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-1.html">上一个字母</a></li>
<li><a href="index-3.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-2.html" target="_top">框架</a></li>
<li><a href="index-2.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
