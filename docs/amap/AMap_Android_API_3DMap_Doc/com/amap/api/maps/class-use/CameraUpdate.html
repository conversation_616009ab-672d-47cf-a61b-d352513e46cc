<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>类 com.amap.api.maps.CameraUpdate的使用</title>
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="\u7C7B com.amap.api.maps.CameraUpdate\u7684\u4F7F\u7528";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="../package-summary.html">程序包</a></li>
<li><a href="../../../../../com/amap/api/maps/CameraUpdate.html" title="com.amap.api.maps中的类">类</a></li>
<li class="navBarCell1Rev">使用</li>
<li><a href="../../../../../overview-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个</li>
<li>下一个</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/maps/class-use/CameraUpdate.html" target="_top">框架</a></li>
<li><a href="CameraUpdate.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h2 title="类的使用 com.amap.api.maps.CameraUpdate" class="title">类的使用<br>com.amap.api.maps.CameraUpdate</h2>
</div>
<div class="classUseContainer">
<ul class="blockList">
<li class="blockList">
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表程序包和解释">
<caption><span>使用<a href="../../../../../com/amap/api/maps/CameraUpdate.html" title="com.amap.api.maps中的类">CameraUpdate</a>的程序包</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">程序包</th>
<th class="colLast" scope="col">说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="#com.amap.api.maps">com.amap.api.maps</a></td>
<td class="colLast">
<div class="block">
地图显示包，帮助您在Android应用程序中显示高德地图，并且支持地图交互手势的功能。</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<ul class="blockList">
<li class="blockList"><a name="com.amap.api.maps">
<!--   -->
</a>
<h3><a href="../../../../../com/amap/api/maps/package-summary.html">com.amap.api.maps</a>中<a href="../../../../../com/amap/api/maps/CameraUpdate.html" title="com.amap.api.maps中的类">CameraUpdate</a>的使用</h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表方法和解释">
<caption><span>返回<a href="../../../../../com/amap/api/maps/CameraUpdate.html" title="com.amap.api.maps中的类">CameraUpdate</a>的<a href="../../../../../com/amap/api/maps/package-summary.html">com.amap.api.maps</a>中的方法</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../../com/amap/api/maps/CameraUpdate.html" title="com.amap.api.maps中的类">CameraUpdate</a></code></td>
<td class="colLast"><span class="typeNameLabel">CameraUpdateFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/CameraUpdateFactory.html#changeBearing-float-">changeBearing</a></span>(float&nbsp;bearing)</code>
<div class="block">设置地图的旋转角度。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../../com/amap/api/maps/CameraUpdate.html" title="com.amap.api.maps中的类">CameraUpdate</a></code></td>
<td class="colLast"><span class="typeNameLabel">CameraUpdateFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/CameraUpdateFactory.html#changeLatLng-com.amap.api.maps.model.LatLng-">changeLatLng</a></span>(<a href="../../../../../com/amap/api/maps/model/LatLng.html" title="com.amap.api.maps.model中的类">LatLng</a>&nbsp;latLng)</code>
<div class="block">设置地图的中心点。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../../com/amap/api/maps/CameraUpdate.html" title="com.amap.api.maps中的类">CameraUpdate</a></code></td>
<td class="colLast"><span class="typeNameLabel">CameraUpdateFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/CameraUpdateFactory.html#changeTilt-float-">changeTilt</a></span>(float&nbsp;tilt)</code>
<div class="block">设置地图倾斜度。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../../com/amap/api/maps/CameraUpdate.html" title="com.amap.api.maps中的类">CameraUpdate</a></code></td>
<td class="colLast"><span class="typeNameLabel">CameraUpdateFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/CameraUpdateFactory.html#newCameraPosition-com.amap.api.maps.model.CameraPosition-">newCameraPosition</a></span>(<a href="../../../../../com/amap/api/maps/model/CameraPosition.html" title="com.amap.api.maps.model中的类">CameraPosition</a>&nbsp;cameraPosition)</code>
<div class="block">给地图设置一个新的状态。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../../com/amap/api/maps/CameraUpdate.html" title="com.amap.api.maps中的类">CameraUpdate</a></code></td>
<td class="colLast"><span class="typeNameLabel">CameraUpdateFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/CameraUpdateFactory.html#newLatLng-com.amap.api.maps.model.LatLng-">newLatLng</a></span>(<a href="../../../../../com/amap/api/maps/model/LatLng.html" title="com.amap.api.maps.model中的类">LatLng</a>&nbsp;latLng)</code>
<div class="block">设置地图的中心点。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../../com/amap/api/maps/CameraUpdate.html" title="com.amap.api.maps中的类">CameraUpdate</a></code></td>
<td class="colLast"><span class="typeNameLabel">CameraUpdateFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/CameraUpdateFactory.html#newLatLngBounds-com.amap.api.maps.model.LatLngBounds-int-">newLatLngBounds</a></span>(<a href="../../../../../com/amap/api/maps/model/LatLngBounds.html" title="com.amap.api.maps.model中的类">LatLngBounds</a>&nbsp;bounds,
               int&nbsp;padding)</code>
<div class="block">设置显示在规定屏幕范围内的地图经纬度范围。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../../com/amap/api/maps/CameraUpdate.html" title="com.amap.api.maps中的类">CameraUpdate</a></code></td>
<td class="colLast"><span class="typeNameLabel">CameraUpdateFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/CameraUpdateFactory.html#newLatLngBounds-com.amap.api.maps.model.LatLngBounds-int-int-int-">newLatLngBounds</a></span>(<a href="../../../../../com/amap/api/maps/model/LatLngBounds.html" title="com.amap.api.maps.model中的类">LatLngBounds</a>&nbsp;bounds,
               int&nbsp;width,
               int&nbsp;height,
               int&nbsp;padding)</code>
<div class="block">设置显示在规定宽高中的地图经纬度范围。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../../com/amap/api/maps/CameraUpdate.html" title="com.amap.api.maps中的类">CameraUpdate</a></code></td>
<td class="colLast"><span class="typeNameLabel">CameraUpdateFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/CameraUpdateFactory.html#newLatLngBoundsRect-com.amap.api.maps.model.LatLngBounds-int-int-int-int-">newLatLngBoundsRect</a></span>(<a href="../../../../../com/amap/api/maps/model/LatLngBounds.html" title="com.amap.api.maps.model中的类">LatLngBounds</a>&nbsp;latlngbounds,
                   int&nbsp;paddingLeft,
                   int&nbsp;paddingRight,
                   int&nbsp;paddingTop,
                   int&nbsp;paddingBottom)</code>
<div class="block">设置显示在规定屏幕范围内的地图经纬度范围。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../../com/amap/api/maps/CameraUpdate.html" title="com.amap.api.maps中的类">CameraUpdate</a></code></td>
<td class="colLast"><span class="typeNameLabel">CameraUpdateFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/CameraUpdateFactory.html#newLatLngZoom-com.amap.api.maps.model.LatLng-float-">newLatLngZoom</a></span>(<a href="../../../../../com/amap/api/maps/model/LatLng.html" title="com.amap.api.maps.model中的类">LatLng</a>&nbsp;latLng,
             float&nbsp;zoom)</code>
<div class="block">设置地图中心点以及缩放级别。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../../com/amap/api/maps/CameraUpdate.html" title="com.amap.api.maps中的类">CameraUpdate</a></code></td>
<td class="colLast"><span class="typeNameLabel">CameraUpdateFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/CameraUpdateFactory.html#scrollBy-float-float-">scrollBy</a></span>(float&nbsp;xPixel,
        float&nbsp;yPixel)</code>
<div class="block">按像素移动地图中心点</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../../com/amap/api/maps/CameraUpdate.html" title="com.amap.api.maps中的类">CameraUpdate</a></code></td>
<td class="colLast"><span class="typeNameLabel">CameraUpdateFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/CameraUpdateFactory.html#zoomBy-float-">zoomBy</a></span>(float&nbsp;amount)</code>
<div class="block">根据给定增量缩放地图级别，在当前地图显示的级别基础上加上这个增量。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../../com/amap/api/maps/CameraUpdate.html" title="com.amap.api.maps中的类">CameraUpdate</a></code></td>
<td class="colLast"><span class="typeNameLabel">CameraUpdateFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/CameraUpdateFactory.html#zoomBy-float-android.graphics.Point-">zoomBy</a></span>(float&nbsp;amount,
      android.graphics.Point&nbsp;focus)</code>
<div class="block">根据给定增量并以给定的屏幕像素点为中心点缩放地图级别。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../../com/amap/api/maps/CameraUpdate.html" title="com.amap.api.maps中的类">CameraUpdate</a></code></td>
<td class="colLast"><span class="typeNameLabel">CameraUpdateFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/CameraUpdateFactory.html#zoomIn--">zoomIn</a></span>()</code>
<div class="block">放大地图缩放级别，在当前地图显示的级别基础上加1。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../../com/amap/api/maps/CameraUpdate.html" title="com.amap.api.maps中的类">CameraUpdate</a></code></td>
<td class="colLast"><span class="typeNameLabel">CameraUpdateFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/CameraUpdateFactory.html#zoomOut--">zoomOut</a></span>()</code>
<div class="block">缩小地图缩放级别，在当前地图显示的级别基础上减1。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../../com/amap/api/maps/CameraUpdate.html" title="com.amap.api.maps中的类">CameraUpdate</a></code></td>
<td class="colLast"><span class="typeNameLabel">CameraUpdateFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/CameraUpdateFactory.html#zoomTo-float-">zoomTo</a></span>(float&nbsp;zoom)</code>
<div class="block">设置地图缩放级别。</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表方法和解释">
<caption><span>参数类型为<a href="../../../../../com/amap/api/maps/CameraUpdate.html" title="com.amap.api.maps中的类">CameraUpdate</a>的<a href="../../../../../com/amap/api/maps/package-summary.html">com.amap.api.maps</a>中的方法</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">AMap.</span><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/AMap.html#animateCamera-com.amap.api.maps.CameraUpdate-">animateCamera</a></span>(<a href="../../../../../com/amap/api/maps/CameraUpdate.html" title="com.amap.api.maps中的类">CameraUpdate</a>&nbsp;update)</code>
<div class="block">以动画方式按照传入的CameraUpdate参数更新地图状态，默认动画耗时250毫秒。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">AMap.</span><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/AMap.html#animateCamera-com.amap.api.maps.CameraUpdate-com.amap.api.maps.AMap.CancelableCallback-">animateCamera</a></span>(<a href="../../../../../com/amap/api/maps/CameraUpdate.html" title="com.amap.api.maps中的类">CameraUpdate</a>&nbsp;update,
             <a href="../../../../../com/amap/api/maps/AMap.CancelableCallback.html" title="com.amap.api.maps中的接口">AMap.CancelableCallback</a>&nbsp;cancelableCallback)</code>
<div class="block">以动画方式按照传入的CameraUpdate参数更新地图状态，默认动画耗时250毫秒，同时设置一个cancelableCallback来监听动画执行的结果。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">AMap.</span><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/AMap.html#animateCamera-com.amap.api.maps.CameraUpdate-long-com.amap.api.maps.AMap.CancelableCallback-">animateCamera</a></span>(<a href="../../../../../com/amap/api/maps/CameraUpdate.html" title="com.amap.api.maps中的类">CameraUpdate</a>&nbsp;update,
             long&nbsp;durationMs,
             <a href="../../../../../com/amap/api/maps/AMap.CancelableCallback.html" title="com.amap.api.maps中的接口">AMap.CancelableCallback</a>&nbsp;cancelableCallback)</code>
<div class="block">按照指定的动画时长及传入的CameraUpdate参数更新地图状态，，同时设置一个cancelableCallback来监听动画执行的结果。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">AMap.</span><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/AMap.html#moveCamera-com.amap.api.maps.CameraUpdate-">moveCamera</a></span>(<a href="../../../../../com/amap/api/maps/CameraUpdate.html" title="com.amap.api.maps中的类">CameraUpdate</a>&nbsp;update)</code>
<div class="block">按照传入的CameraUpdate参数改变地图状态。</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="../package-summary.html">程序包</a></li>
<li><a href="../../../../../com/amap/api/maps/CameraUpdate.html" title="com.amap.api.maps中的类">类</a></li>
<li class="navBarCell1Rev">使用</li>
<li><a href="../../../../../overview-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个</li>
<li>下一个</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/maps/class-use/CameraUpdate.html" target="_top">框架</a></li>
<li><a href="CameraUpdate.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
