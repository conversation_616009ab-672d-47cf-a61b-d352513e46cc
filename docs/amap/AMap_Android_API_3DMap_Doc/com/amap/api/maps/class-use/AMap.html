<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>类 com.amap.api.maps.AMap的使用</title>
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="\u7C7B com.amap.api.maps.AMap\u7684\u4F7F\u7528";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="../package-summary.html">程序包</a></li>
<li><a href="../../../../../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">类</a></li>
<li class="navBarCell1Rev">使用</li>
<li><a href="../../../../../overview-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个</li>
<li>下一个</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/maps/class-use/AMap.html" target="_top">框架</a></li>
<li><a href="AMap.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h2 title="类的使用 com.amap.api.maps.AMap" class="title">类的使用<br>com.amap.api.maps.AMap</h2>
</div>
<div class="classUseContainer">
<ul class="blockList">
<li class="blockList">
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表程序包和解释">
<caption><span>使用<a href="../../../../../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a>的程序包</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">程序包</th>
<th class="colLast" scope="col">说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="#com.amap.api.maps">com.amap.api.maps</a></td>
<td class="colLast">
<div class="block">
地图显示包，帮助您在Android应用程序中显示高德地图，并且支持地图交互手势的功能。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.amap.api.maps.offlinemap">com.amap.api.maps.offlinemap</a></td>
<td class="colLast">
<div class="block">
离线地图包，用户可以通过手机WiFi下载高德3D离线地图。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#com.amap.api.maps.utils.overlay">com.amap.api.maps.utils.overlay</a></td>
<td class="colLast">
<div class="block">
工具类，基于地图现有接口实现的高级功能。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.amap.api.trace">com.amap.api.trace</a></td>
<td class="colLast">
<div class="block">
轨迹纠偏包，提供高精度定位轨迹抓路后绘制平滑轨迹。</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<ul class="blockList">
<li class="blockList"><a name="com.amap.api.maps">
<!--   -->
</a>
<h3><a href="../../../../../com/amap/api/maps/package-summary.html">com.amap.api.maps</a>中<a href="../../../../../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a>的使用</h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表方法和解释">
<caption><span>返回<a href="../../../../../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a>的<a href="../../../../../com/amap/api/maps/package-summary.html">com.amap.api.maps</a>中的方法</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></code></td>
<td class="colLast"><span class="typeNameLabel">MapFragment.</span><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/MapFragment.html#getMap--">getMap</a></span>()</code>
<div class="block">获取地图控制器AMap 对象。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></code></td>
<td class="colLast"><span class="typeNameLabel">MapView.</span><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/MapView.html#getMap--">getMap</a></span>()</code>
<div class="block">获取地图控制器AMap 对象。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></code></td>
<td class="colLast"><span class="typeNameLabel">SupportMapFragment.</span><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/SupportMapFragment.html#getMap--">getMap</a></span>()</code>
<div class="block">获取地图控制器AMap 对象。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></code></td>
<td class="colLast"><span class="typeNameLabel">TextureMapFragment.</span><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/TextureMapFragment.html#getMap--">getMap</a></span>()</code>
<div class="block">获取地图控制器AMap 对象。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></code></td>
<td class="colLast"><span class="typeNameLabel">TextureMapView.</span><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/TextureMapView.html#getMap--">getMap</a></span>()</code>
<div class="block">获取地图控制器AMap 对象。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></code></td>
<td class="colLast"><span class="typeNameLabel">TextureSupportMapFragment.</span><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/TextureSupportMapFragment.html#getMap--">getMap</a></span>()</code>
<div class="block">获取地图控制器AMap 对象。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></code></td>
<td class="colLast"><span class="typeNameLabel">WearMapView.</span><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/WearMapView.html#getMap--">getMap</a></span>()</code>
<div class="block">返回一个与这个视图（WearMapView）相关联的AMap 对象。</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.amap.api.maps.offlinemap">
<!--   -->
</a>
<h3><a href="../../../../../com/amap/api/maps/offlinemap/package-summary.html">com.amap.api.maps.offlinemap</a>中<a href="../../../../../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a>的使用</h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表构造器和解释">
<caption><span>参数类型为<a href="../../../../../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a>的<a href="../../../../../com/amap/api/maps/offlinemap/package-summary.html">com.amap.api.maps.offlinemap</a>中的构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/offlinemap/OfflineMapManager.html#OfflineMapManager-android.content.Context-com.amap.api.maps.offlinemap.OfflineMapManager.OfflineMapDownloadListener-com.amap.api.maps.AMap-">OfflineMapManager</a></span>(android.content.Context&nbsp;context,
                 <a href="../../../../../com/amap/api/maps/offlinemap/OfflineMapManager.OfflineMapDownloadListener.html" title="com.amap.api.maps.offlinemap中的接口">OfflineMapManager.OfflineMapDownloadListener</a>&nbsp;listener,
                 <a href="../../../../../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a>&nbsp;amap)</code>
<div class="block">根据给定的参数来构造OfflineMapManager对象。</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.amap.api.maps.utils.overlay">
<!--   -->
</a>
<h3><a href="../../../../../com/amap/api/maps/utils/overlay/package-summary.html">com.amap.api.maps.utils.overlay</a>中<a href="../../../../../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a>的使用</h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表构造器和解释">
<caption><span>参数类型为<a href="../../../../../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a>的<a href="../../../../../com/amap/api/maps/utils/overlay/package-summary.html">com.amap.api.maps.utils.overlay</a>中的构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/utils/overlay/MovingPointOverlay.html#MovingPointOverlay-com.amap.api.maps.AMap-com.amap.api.maps.model.BasePointOverlay-">MovingPointOverlay</a></span>(<a href="../../../../../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a>&nbsp;mAMap,
                  <a href="../../../../../com/amap/api/maps/model/BasePointOverlay.html" title="com.amap.api.maps.model中的类">BasePointOverlay</a>&nbsp;overlay)</code>
<div class="block">根据给定的参数来构造SmoothMoveMarker对象。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/utils/overlay/SmoothMoveMarker.html#SmoothMoveMarker-com.amap.api.maps.AMap-">SmoothMoveMarker</a></span>(<a href="../../../../../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a>&nbsp;mAMap)</code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;</div>
<div class="block">根据给定的参数来构造SmoothMoveMarker对象。</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.amap.api.trace">
<!--   -->
</a>
<h3><a href="../../../../../com/amap/api/trace/package-summary.html">com.amap.api.trace</a>中<a href="../../../../../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a>的使用</h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表构造器和解释">
<caption><span>参数类型为<a href="../../../../../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a>的<a href="../../../../../com/amap/api/trace/package-summary.html">com.amap.api.trace</a>中的构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/trace/TraceOverlay.html#TraceOverlay-com.amap.api.maps.AMap-">TraceOverlay</a></span>(<a href="../../../../../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a>&nbsp;amap)</code>
<div class="block">构造方法</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/trace/TraceOverlay.html#TraceOverlay-com.amap.api.maps.AMap-java.util.List-">TraceOverlay</a></span>(<a href="../../../../../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a>&nbsp;amap,
            java.util.List&lt;<a href="../../../../../com/amap/api/maps/model/LatLng.html" title="com.amap.api.maps.model中的类">LatLng</a>&gt;&nbsp;lines)</code>
<div class="block">构造方法</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="../package-summary.html">程序包</a></li>
<li><a href="../../../../../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">类</a></li>
<li class="navBarCell1Rev">使用</li>
<li><a href="../../../../../overview-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个</li>
<li>下一个</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/maps/class-use/AMap.html" target="_top">框架</a></li>
<li><a href="AMap.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
