<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>类 com.amap.api.maps.AMapOptions的使用</title>
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="\u7C7B com.amap.api.maps.AMapOptions\u7684\u4F7F\u7528";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="../package-summary.html">程序包</a></li>
<li><a href="../../../../../com/amap/api/maps/AMapOptions.html" title="com.amap.api.maps中的类">类</a></li>
<li class="navBarCell1Rev">使用</li>
<li><a href="../../../../../overview-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个</li>
<li>下一个</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/maps/class-use/AMapOptions.html" target="_top">框架</a></li>
<li><a href="AMapOptions.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h2 title="类的使用 com.amap.api.maps.AMapOptions" class="title">类的使用<br>com.amap.api.maps.AMapOptions</h2>
</div>
<div class="classUseContainer">
<ul class="blockList">
<li class="blockList">
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表程序包和解释">
<caption><span>使用<a href="../../../../../com/amap/api/maps/AMapOptions.html" title="com.amap.api.maps中的类">AMapOptions</a>的程序包</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">程序包</th>
<th class="colLast" scope="col">说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="#com.amap.api.maps">com.amap.api.maps</a></td>
<td class="colLast">
<div class="block">
地图显示包，帮助您在Android应用程序中显示高德地图，并且支持地图交互手势的功能。</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<ul class="blockList">
<li class="blockList"><a name="com.amap.api.maps">
<!--   -->
</a>
<h3><a href="../../../../../com/amap/api/maps/package-summary.html">com.amap.api.maps</a>中<a href="../../../../../com/amap/api/maps/AMapOptions.html" title="com.amap.api.maps中的类">AMapOptions</a>的使用</h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表方法和解释">
<caption><span>返回<a href="../../../../../com/amap/api/maps/AMapOptions.html" title="com.amap.api.maps中的类">AMapOptions</a>的<a href="../../../../../com/amap/api/maps/package-summary.html">com.amap.api.maps</a>中的方法</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/maps/AMapOptions.html" title="com.amap.api.maps中的类">AMapOptions</a></code></td>
<td class="colLast"><span class="typeNameLabel">AMapOptions.</span><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/AMapOptions.html#camera-com.amap.api.maps.model.CameraPosition-">camera</a></span>(<a href="../../../../../com/amap/api/maps/model/CameraPosition.html" title="com.amap.api.maps.model中的类">CameraPosition</a>&nbsp;camera)</code>
<div class="block">设置地图初始化时的地图状态， 默认地图中心点为北京天安门，缩放级别为 10.0f。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/maps/AMapOptions.html" title="com.amap.api.maps中的类">AMapOptions</a></code></td>
<td class="colLast"><span class="typeNameLabel">AMapOptions.</span><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/AMapOptions.html#compassEnabled-boolean-">compassEnabled</a></span>(boolean&nbsp;enabled)</code>
<div class="block">设置指南针是否可用。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/maps/AMapOptions.html" title="com.amap.api.maps中的类">AMapOptions</a></code></td>
<td class="colLast"><span class="typeNameLabel">AMapOptions.</span><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/AMapOptions.html#logoPosition-int-">logoPosition</a></span>(int&nbsp;position)</code>
<div class="block">设置“高德地图”Logo的位置。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/maps/AMapOptions.html" title="com.amap.api.maps中的类">AMapOptions</a></code></td>
<td class="colLast"><span class="typeNameLabel">AMapOptions.</span><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/AMapOptions.html#mapType-int-">mapType</a></span>(int&nbsp;mapType)</code>
<div class="block">设置地图模式，默认普通地图。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/maps/AMapOptions.html" title="com.amap.api.maps中的类">AMapOptions</a></code></td>
<td class="colLast"><span class="typeNameLabel">AMapOptions.</span><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/AMapOptions.html#rotateGesturesEnabled-boolean-">rotateGesturesEnabled</a></span>(boolean&nbsp;enabled)</code>
<div class="block">设置地图是否可以通过手势进行旋转。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/maps/AMapOptions.html" title="com.amap.api.maps中的类">AMapOptions</a></code></td>
<td class="colLast"><span class="typeNameLabel">AMapOptions.</span><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/AMapOptions.html#scaleControlsEnabled-boolean-">scaleControlsEnabled</a></span>(boolean&nbsp;enabled)</code>
<div class="block">设置地图是否显示比例尺，默认为false。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/maps/AMapOptions.html" title="com.amap.api.maps中的类">AMapOptions</a></code></td>
<td class="colLast"><span class="typeNameLabel">AMapOptions.</span><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/AMapOptions.html#scrollGesturesEnabled-boolean-">scrollGesturesEnabled</a></span>(boolean&nbsp;enabled)</code>
<div class="block">设置地图是否可以通过手势滑动。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/maps/AMapOptions.html" title="com.amap.api.maps中的类">AMapOptions</a></code></td>
<td class="colLast"><span class="typeNameLabel">AMapOptions.</span><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/AMapOptions.html#tiltGesturesEnabled-boolean-">tiltGesturesEnabled</a></span>(boolean&nbsp;enabled)</code>
<div class="block">设置地图是否可以通过手势倾斜（3D效果），默认为true。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/maps/AMapOptions.html" title="com.amap.api.maps中的类">AMapOptions</a></code></td>
<td class="colLast"><span class="typeNameLabel">AMapOptions.</span><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/AMapOptions.html#zoomControlsEnabled-boolean-">zoomControlsEnabled</a></span>(boolean&nbsp;enabled)</code>
<div class="block">设置地图是否允许缩放。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/maps/AMapOptions.html" title="com.amap.api.maps中的类">AMapOptions</a></code></td>
<td class="colLast"><span class="typeNameLabel">AMapOptions.</span><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/AMapOptions.html#zoomGesturesEnabled-boolean-">zoomGesturesEnabled</a></span>(boolean&nbsp;enabled)</code>
<div class="block">设置地图是否可以通过手势进行缩放。</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表方法和解释">
<caption><span>参数类型为<a href="../../../../../com/amap/api/maps/AMapOptions.html" title="com.amap.api.maps中的类">AMapOptions</a>的<a href="../../../../../com/amap/api/maps/package-summary.html">com.amap.api.maps</a>中的方法</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../../com/amap/api/maps/MapFragment.html" title="com.amap.api.maps中的类">MapFragment</a></code></td>
<td class="colLast"><span class="typeNameLabel">MapFragment.</span><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/MapFragment.html#newInstance-com.amap.api.maps.AMapOptions-">newInstance</a></span>(<a href="../../../../../com/amap/api/maps/AMapOptions.html" title="com.amap.api.maps中的类">AMapOptions</a>&nbsp;options)</code>
<div class="block">根据用户传入的AMapOptions创建MapFragment。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../../com/amap/api/maps/SupportMapFragment.html" title="com.amap.api.maps中的类">SupportMapFragment</a></code></td>
<td class="colLast"><span class="typeNameLabel">SupportMapFragment.</span><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/SupportMapFragment.html#newInstance-com.amap.api.maps.AMapOptions-">newInstance</a></span>(<a href="../../../../../com/amap/api/maps/AMapOptions.html" title="com.amap.api.maps中的类">AMapOptions</a>&nbsp;options)</code>
<div class="block">根据用户传入的AMapOptions 创建SupportMapFragment。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../../com/amap/api/maps/TextureMapFragment.html" title="com.amap.api.maps中的类">TextureMapFragment</a></code></td>
<td class="colLast"><span class="typeNameLabel">TextureMapFragment.</span><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/TextureMapFragment.html#newInstance-com.amap.api.maps.AMapOptions-">newInstance</a></span>(<a href="../../../../../com/amap/api/maps/AMapOptions.html" title="com.amap.api.maps中的类">AMapOptions</a>&nbsp;options)</code>
<div class="block">根据用户传入的AMapOptions 创建TextureMapFragment。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../../com/amap/api/maps/TextureSupportMapFragment.html" title="com.amap.api.maps中的类">TextureSupportMapFragment</a></code></td>
<td class="colLast"><span class="typeNameLabel">TextureSupportMapFragment.</span><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/TextureSupportMapFragment.html#newInstance-com.amap.api.maps.AMapOptions-">newInstance</a></span>(<a href="../../../../../com/amap/api/maps/AMapOptions.html" title="com.amap.api.maps中的类">AMapOptions</a>&nbsp;options)</code>
<div class="block">根据用户传入的AMapOptions 创建TextureSupportMapFragment。</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表构造器和解释">
<caption><span>参数类型为<a href="../../../../../com/amap/api/maps/AMapOptions.html" title="com.amap.api.maps中的类">AMapOptions</a>的<a href="../../../../../com/amap/api/maps/package-summary.html">com.amap.api.maps</a>中的构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/MapView.html#MapView-android.content.Context-com.amap.api.maps.AMapOptions-">MapView</a></span>(android.content.Context&nbsp;paramContext,
       <a href="../../../../../com/amap/api/maps/AMapOptions.html" title="com.amap.api.maps中的类">AMapOptions</a>&nbsp;mapOptions)</code>
<div class="block">根据给定的参数构造一个MapView 的新对象。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/TextureMapView.html#TextureMapView-android.content.Context-com.amap.api.maps.AMapOptions-">TextureMapView</a></span>(android.content.Context&nbsp;paramContext,
              <a href="../../../../../com/amap/api/maps/AMapOptions.html" title="com.amap.api.maps中的类">AMapOptions</a>&nbsp;mapOptions)</code>
<div class="block">根据给定的参数构造一个MapView 的新对象。</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/WearMapView.html#WearMapView-android.content.Context-com.amap.api.maps.AMapOptions-">WearMapView</a></span>(android.content.Context&nbsp;context,
           <a href="../../../../../com/amap/api/maps/AMapOptions.html" title="com.amap.api.maps中的类">AMapOptions</a>&nbsp;mapOptions)</code>
<div class="block">根据给定的参数构造一个WearMapView的新对象。</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="../package-summary.html">程序包</a></li>
<li><a href="../../../../../com/amap/api/maps/AMapOptions.html" title="com.amap.api.maps中的类">类</a></li>
<li class="navBarCell1Rev">使用</li>
<li><a href="../../../../../overview-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个</li>
<li>下一个</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/maps/class-use/AMapOptions.html" target="_top">框架</a></li>
<li><a href="AMapOptions.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
