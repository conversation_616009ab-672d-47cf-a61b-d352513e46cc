<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>MapsInitializer</title>
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="MapsInitializer";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":41,"i2":9,"i3":9,"i4":9,"i5":9,"i6":9,"i7":9,"i8":41,"i9":9,"i10":9,"i11":9,"i12":9};
var tabs = {65535:["t0","所有方法"],1:["t1","静态方法"],8:["t4","具体方法"],32:["t6","已过时的方法"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/MapsInitializer.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/amap/api/maps/MapFragment.html" title="com.amap.api.maps中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../com/amap/api/maps/MapView.html" title="com.amap.api.maps中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/amap/api/maps/MapsInitializer.html" target="_top">框架</a></li>
<li><a href="MapsInitializer.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li><a href="#field.summary">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li><a href="#field.detail">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.amap.api.maps</div>
<h2 title="类 MapsInitializer" class="title">类 MapsInitializer</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.amap.api.maps.MapsInitializer</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public final class <span class="typeNameLabel">MapsInitializer</span>
extends java.lang.Object</pre>
<div class="block">初始化 SDK context 全局变量，指定 sdcard 路径，设置鉴权所需的KEY。
 注：如果在创建地图之前使用BitmapDescriptorFactory的功能，则必须通过<a href="../../../../com/amap/api/maps/MapsInitializer.html#initialize-android.content.Context-"><code>MapsInitializer.initialize(Context)</code></a>来设置一个可用的context。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.2</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>字段概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="字段概要表, 列表字段和解释">
<caption><span>字段</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">字段和说明</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/MapsInitializer.html#HTTP">HTTP</a></span></code>
<div class="block">Http协议。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/MapsInitializer.html#HTTPS">HTTPS</a></span></code>
<div class="block">Https协议。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/MapsInitializer.html#sdcardDir">sdcardDir</a></span></code>
<div class="block">地图缓存目录。</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/MapsInitializer.html#MapsInitializer--">MapsInitializer</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>所有方法</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">静态方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">具体方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t6" class="tableTab"><span><a href="javascript:show(32);">已过时的方法</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/MapsInitializer.html#getDeviceId-android.content.Context-">getDeviceId</a></span>(android.content.Context&nbsp;context)</code>
<div class="block">获取设备号</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/MapsInitializer.html#getNetWorkEnable--">getNetWorkEnable</a></span>()</code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;
<div class="block"><span class="deprecationComment">5.0.0开始废弃</span></div>
</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/MapsInitializer.html#getProtocol--">getProtocol</a></span>()</code>
<div class="block">返回访问使用的协议类别</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/MapsInitializer.html#getVersion--">getVersion</a></span>()</code>
<div class="block">返回地图SDK的版本号。</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/MapsInitializer.html#getWorldVectorOfflineMapStyleAssetsPath--">getWorldVectorOfflineMapStyleAssetsPath</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/MapsInitializer.html#initialize-android.content.Context-">initialize</a></span>(android.content.Context&nbsp;paramContext)</code>
<div class="block">初始化全局 context。</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/MapsInitializer.html#isTerrainEnable--">isTerrainEnable</a></span>()</code>
<div class="block">是否打开地形图, 默认为关闭
 打开地形图之后，底图会变成3D模式，添加的点线面等覆盖物也会自动带有高程</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/MapsInitializer.html#setApiKey-java.lang.String-">setApiKey</a></span>(java.lang.String&nbsp;apiKey)</code>
<div class="block">动态设置apiKey。</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/MapsInitializer.html#setNetWorkEnable-boolean-">setNetWorkEnable</a></span>(boolean&nbsp;enable)</code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;
<div class="block"><span class="deprecationComment">5.0.0开始废弃</span></div>
</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/MapsInitializer.html#setProtocol-int-">setProtocol</a></span>(int&nbsp;protocol)</code>
<div class="block">设置访问使用的协议类别。</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/MapsInitializer.html#setTerrainEnable-boolean-">setTerrainEnable</a></span>(boolean&nbsp;isTerrainEnable)</code>
<div class="block">是否打开地形图, 默认为关闭
 打开地形图之后，底图会变成3D模式，添加的点线面等覆盖物也会自动带有高程
 
 注意：需要在MapView创建之前调用</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/MapsInitializer.html#updatePrivacyAgree-android.content.Context-boolean-">updatePrivacyAgree</a></span>(android.content.Context&nbsp;context,
                  boolean&nbsp;isAgree)</code>
<div class="block">更新同意隐私状态,需要在初始化地图之前完成</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/MapsInitializer.html#updatePrivacyShow-android.content.Context-boolean-boolean-">updatePrivacyShow</a></span>(android.content.Context&nbsp;context,
                 boolean&nbsp;isContains,
                 boolean&nbsp;isShow)</code>
<div class="block">更新隐私合规状态,需要在初始化地图之前完成</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>字段详细资料</h3>
<a name="sdcardDir">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>sdcardDir</h4>
<pre>public static&nbsp;java.lang.String sdcardDir</pre>
<div class="block">地图缓存目录。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.2</dd>
</dl>
</li>
</ul>
<a name="HTTP">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HTTP</h4>
<pre>public static final&nbsp;int HTTP</pre>
<div class="block">Http协议。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.5.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.maps.MapsInitializer.HTTP">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="HTTPS">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>HTTPS</h4>
<pre>public static final&nbsp;int HTTPS</pre>
<div class="block">Https协议。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.5.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.maps.MapsInitializer.HTTPS">常量字段值</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="MapsInitializer--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>MapsInitializer</h4>
<pre>public&nbsp;MapsInitializer()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="initialize-android.content.Context-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>initialize</h4>
<pre>public static&nbsp;void&nbsp;initialize(android.content.Context&nbsp;paramContext)
                       throws android.os.RemoteException</pre>
<div class="block">初始化全局 context。在使用SDK各组件之前调用。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>paramContext</code> - context对象，不能为空。</dd>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code>android.os.RemoteException</code></dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.2</dd>
</dl>
</li>
</ul>
<a name="setNetWorkEnable-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setNetWorkEnable</h4>
<pre>public static&nbsp;void&nbsp;setNetWorkEnable(boolean&nbsp;enable)</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;<span class="deprecationComment">5.0.0开始废弃</span></div>
<div class="block">设置是否可以联网获取地图数据</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>enable</code> - 是否可以联网获取地图数据。默认为true，可以联网</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.2.0</dd>
</dl>
</li>
</ul>
<a name="getNetWorkEnable--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNetWorkEnable</h4>
<pre>public static&nbsp;boolean&nbsp;getNetWorkEnable()</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;<span class="deprecationComment">5.0.0开始废弃</span></div>
<div class="block">是否可以联网获取地图数据。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>是否可以联网获取地图数据。默认为true，可以联网。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.2.0</dd>
</dl>
</li>
</ul>
<a name="setApiKey-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setApiKey</h4>
<pre>public static&nbsp;void&nbsp;setApiKey(java.lang.String&nbsp;apiKey)</pre>
<div class="block">动态设置apiKey。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>apiKey</code> - 在高德官网上申请的apiKey。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.4.1</dd>
</dl>
</li>
</ul>
<a name="getVersion--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVersion</h4>
<pre>public static&nbsp;java.lang.String&nbsp;getVersion()</pre>
<div class="block">返回地图SDK的版本号。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>地图的版本号。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.5.0</dd>
</dl>
</li>
</ul>
<a name="setProtocol-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setProtocol</h4>
<pre>public static&nbsp;void&nbsp;setProtocol(int&nbsp;protocol)</pre>
<div class="block">设置访问使用的协议类别。
 如果是Android P及以上 默认使用Https，就算设置了Http内部也会走Https</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>protocol</code> - 包含Http和Https两种协议。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.5.0</dd>
</dl>
</li>
</ul>
<a name="getProtocol--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProtocol</h4>
<pre>public static&nbsp;int&nbsp;getProtocol()</pre>
<div class="block">返回访问使用的协议类别</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>协议类别，包含Http和Https两种协议。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.5.0</dd>
</dl>
</li>
</ul>
<a name="getWorldVectorOfflineMapStyleAssetsPath--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWorldVectorOfflineMapStyleAssetsPath</h4>
<pre>public static&nbsp;java.lang.String&nbsp;getWorldVectorOfflineMapStyleAssetsPath()</pre>
</li>
</ul>
<a name="getDeviceId-android.content.Context-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDeviceId</h4>
<pre>public static&nbsp;java.lang.String&nbsp;getDeviceId(android.content.Context&nbsp;context)</pre>
<div class="block">获取设备号</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>context</code> - </dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>设备号, 如果获取不到返回null</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>7.9.0</dd>
</dl>
</li>
</ul>
<a name="isTerrainEnable--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isTerrainEnable</h4>
<pre>public static&nbsp;boolean&nbsp;isTerrainEnable()</pre>
<div class="block">是否打开地形图, 默认为关闭
 打开地形图之后，底图会变成3D模式，添加的点线面等覆盖物也会自动带有高程</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>true为打开，默认false</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>8.0.0</dd>
</dl>
</li>
</ul>
<a name="setTerrainEnable-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTerrainEnable</h4>
<pre>public static&nbsp;void&nbsp;setTerrainEnable(boolean&nbsp;isTerrainEnable)</pre>
<div class="block">是否打开地形图, 默认为关闭
 打开地形图之后，底图会变成3D模式，添加的点线面等覆盖物也会自动带有高程
 <p>
 注意：需要在MapView创建之前调用</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>isTerrainEnable</code> - true为打开，默认false</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>8.0.0</dd>
</dl>
</li>
</ul>
<a name="updatePrivacyShow-android.content.Context-boolean-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>updatePrivacyShow</h4>
<pre>public static&nbsp;void&nbsp;updatePrivacyShow(android.content.Context&nbsp;context,
                                     boolean&nbsp;isContains,
                                     boolean&nbsp;isShow)</pre>
<div class="block">更新隐私合规状态,需要在初始化地图之前完成</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>context:</code> - 上下文</dd>
<dd><code>isContains:</code> - 隐私权政策是否包含高德开平隐私权政策  true是包含</dd>
<dd><code>isShow: 隐私权政策是否弹窗展示告知用户 true是展示</code> - </dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>8.1.0</dd>
</dl>
</li>
</ul>
<a name="updatePrivacyAgree-android.content.Context-boolean-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>updatePrivacyAgree</h4>
<pre>public static&nbsp;void&nbsp;updatePrivacyAgree(android.content.Context&nbsp;context,
                                      boolean&nbsp;isAgree)</pre>
<div class="block">更新同意隐私状态,需要在初始化地图之前完成</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>context:</code> - 上下文</dd>
<dd><code>isAgree:</code> - 隐私权政策是否取得用户同意  true是用户同意</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>8.1.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/MapsInitializer.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/amap/api/maps/MapFragment.html" title="com.amap.api.maps中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../com/amap/api/maps/MapView.html" title="com.amap.api.maps中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/amap/api/maps/MapsInitializer.html" target="_top">框架</a></li>
<li><a href="MapsInitializer.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li><a href="#field.summary">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li><a href="#field.detail">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
