<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>AMap.ImageInfoWindowAdapter</title>
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="AMap.ImageInfoWindowAdapter";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":6};
var tabs = {65535:["t0","所有方法"],2:["t2","实例方法"],4:["t3","抽象方法"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/AMap.ImageInfoWindowAdapter.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/amap/api/maps/AMap.CancelableCallback.html" title="com.amap.api.maps中的接口"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../com/amap/api/maps/AMap.InfoWindowAdapter.html" title="com.amap.api.maps中的接口"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/amap/api/maps/AMap.ImageInfoWindowAdapter.html" target="_top">框架</a></li>
<li><a href="AMap.ImageInfoWindowAdapter.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.amap.api.maps</div>
<h2 title="接口 AMap.ImageInfoWindowAdapter" class="title">接口 AMap.ImageInfoWindowAdapter</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>所有超级接口:</dt>
<dd><a href="../../../../com/amap/api/maps/AMap.InfoWindowAdapter.html" title="com.amap.api.maps中的接口">AMap.InfoWindowAdapter</a></dd>
</dl>
<dl>
<dt>封闭类:</dt>
<dd><a href="../../../../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dd>
</dl>
<hr>
<br>
<pre>public static interface <span class="typeNameLabel">AMap.ImageInfoWindowAdapter</span>
extends <a href="../../../../com/amap/api/maps/AMap.InfoWindowAdapter.html" title="com.amap.api.maps中的接口">AMap.InfoWindowAdapter</a></pre>
<div class="block">用来实现marker与对应InfoWindow同步移动<br>
 默认情况下，InfoWindow是一个View， 拖动地图的时候由于View 布局较慢，会有延迟的效果。<br>
 为了解决此问题，新增<a href="../../../../com/amap/api/maps/AMap.ImageInfoWindowAdapter.html" title="com.amap.api.maps中的接口"><code>AMap.ImageInfoWindowAdapter</code></a>, InfoWindow会被转为图片，拖动地图时会跟随<a href="../../../../com/amap/api/maps/model/Marker.html" title="com.amap.api.maps.model中的类"><code>Marker</code></a><br>
 <br>
 默认情况下，当单击某个marker时，会触发getInfoWindow和getInfoContents回调。<br>
 而通过调用<a href="../../../../com/amap/api/maps/model/Marker.html#showInfoWindow--"><code>Marker.showInfoWindow()</code></a>同样可以触发上面两个回调。<br></div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>5.3.0</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>所有方法</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">实例方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">抽象方法</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.ImageInfoWindowAdapter.html#getInfoWindowUpdateTime--">getInfoWindowUpdateTime</a></span>()</code>
<div class="block">注意，使用<a href="../../../../com/amap/api/maps/AMap.ImageInfoWindowAdapter.html" title="com.amap.api.maps中的接口"><code>AMap.ImageInfoWindowAdapter</code></a>后InfoWindow作为View本身的功能被减弱，比如动态更新图片，播放Gif图片等等均无法使用。</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.amap.api.maps.AMap.InfoWindowAdapter">
<!--   -->
</a>
<h3>从接口继承的方法&nbsp;com.amap.api.maps.<a href="../../../../com/amap/api/maps/AMap.InfoWindowAdapter.html" title="com.amap.api.maps中的接口">AMap.InfoWindowAdapter</a></h3>
<code><a href="../../../../com/amap/api/maps/AMap.InfoWindowAdapter.html#getInfoContents-com.amap.api.maps.model.Marker-">getInfoContents</a>, <a href="../../../../com/amap/api/maps/AMap.InfoWindowAdapter.html#getInfoWindow-com.amap.api.maps.model.Marker-">getInfoWindow</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="getInfoWindowUpdateTime--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getInfoWindowUpdateTime</h4>
<pre>long&nbsp;getInfoWindowUpdateTime()</pre>
<div class="block">注意，使用<a href="../../../../com/amap/api/maps/AMap.ImageInfoWindowAdapter.html" title="com.amap.api.maps中的接口"><code>AMap.ImageInfoWindowAdapter</code></a>后InfoWindow作为View本身的功能被减弱，比如动态更新图片，播放Gif图片等等均无法使用。<br>
 如果想要动态的去更新infowindow内容，请务必仔细看看此接口的更新机制。<br><br>
 <p>
 设置此接口返回值之后，会定期(默认周期无穷大)调用一个 <a href="../../../../com/amap/api/maps/AMap.InfoWindowAdapter.html#getInfoWindow-com.amap.api.maps.model.Marker-"><code>AMap.InfoWindowAdapter.getInfoWindow(Marker)</code></a> 并将View转换为图片<br>
 由于将View转成图片会比较耗时，不能一直调用，而设置时间间隔可以减少一定的耗时<br><br>
 <br>
 <p>
 单位为 ms
 如果返回值 小于或等于 0，则认为是无穷大<br>
 如果返回值 (0,100] , 则认为是100（如果频繁将View转成图片，内存抖动会很严重，建议这个值不要太低）<br><br>
 <p>
 如果这个想实现更小的时间间隔或者不想受这个接口约束，可以保持返回默认值，并自行设置计时器，调用<a href="../../../../com/amap/api/maps/model/Marker.html#showInfoWindow--"><code>Marker.showInfoWindow()</code></a> 也可以触发调用 <a href="../../../../com/amap/api/maps/AMap.InfoWindowAdapter.html#getInfoWindow-com.amap.api.maps.model.Marker-"><code>AMap.InfoWindowAdapter.getInfoWindow(Marker)</code></a> 并将View转换为图片<br>
 <p>
 自定义整个信息窗口属性间隔时间<br></></div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>时间间隔</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>5.3.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/AMap.ImageInfoWindowAdapter.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/amap/api/maps/AMap.CancelableCallback.html" title="com.amap.api.maps中的接口"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../com/amap/api/maps/AMap.InfoWindowAdapter.html" title="com.amap.api.maps中的接口"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/amap/api/maps/AMap.ImageInfoWindowAdapter.html" target="_top">框架</a></li>
<li><a href="AMap.ImageInfoWindowAdapter.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
