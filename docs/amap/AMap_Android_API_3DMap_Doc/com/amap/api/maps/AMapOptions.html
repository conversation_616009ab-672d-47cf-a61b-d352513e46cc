<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>AMapOptions</title>
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="AMapOptions";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10};
var tabs = {65535:["t0","所有方法"],2:["t2","实例方法"],8:["t4","具体方法"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/AMapOptions.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/amap/api/maps/AMapException.html" title="com.amap.api.maps中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../com/amap/api/maps/AMapUtils.html" title="com.amap.api.maps中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/amap/api/maps/AMapOptions.html" target="_top">框架</a></li>
<li><a href="AMapOptions.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li><a href="#field.summary">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li><a href="#field.detail">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.amap.api.maps</div>
<h2 title="类 AMapOptions" class="title">类 AMapOptions</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.amap.api.maps.AMapOptions</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>所有已实现的接口:</dt>
<dd>android.os.Parcelable</dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">AMapOptions</span>
extends java.lang.Object
implements android.os.Parcelable</pre>
<div class="block">MapView 初始化选项。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>嵌套类概要</h3>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.android.os.Parcelable">
<!--   -->
</a>
<h3>从接口继承的嵌套类/接口&nbsp;android.os.Parcelable</h3>
<code>android.os.Parcelable.ClassLoaderCreator&lt;T&gt;, android.os.Parcelable.Creator&lt;T&gt;</code></li>
</ul>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>字段概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="字段概要表, 列表字段和解释">
<caption><span>字段</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">字段和说明</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/amap/api/maps/AMapOptionsCreator.html" title="com.amap.api.maps中的类">AMapOptionsCreator</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMapOptions.html#CREATOR">CREATOR</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMapOptions.html#LOGO_MARGIN_BOTTOM">LOGO_MARGIN_BOTTOM</a></span></code>
<div class="block">LOGO边缘MARGIN常量（底部）</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMapOptions.html#LOGO_MARGIN_LEFT">LOGO_MARGIN_LEFT</a></span></code>
<div class="block">LOGO边缘MARGIN常量（左边）</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMapOptions.html#LOGO_MARGIN_RIGHT">LOGO_MARGIN_RIGHT</a></span></code>
<div class="block">LOGO边缘MARGIN常量（右边）</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMapOptions.html#LOGO_POSITION_BOTTOM_CENTER">LOGO_POSITION_BOTTOM_CENTER</a></span></code>
<div class="block">Logo位置常量（地图底部居中）。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMapOptions.html#LOGO_POSITION_BOTTOM_LEFT">LOGO_POSITION_BOTTOM_LEFT</a></span></code>
<div class="block">Logo位置常量（地图左下角）。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMapOptions.html#LOGO_POSITION_BOTTOM_RIGHT">LOGO_POSITION_BOTTOM_RIGHT</a></span></code>
<div class="block">Logo位置常量（地图右下角）。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMapOptions.html#ZOOM_POSITION_RIGHT_BUTTOM">ZOOM_POSITION_RIGHT_BUTTOM</a></span></code>
<div class="block">缩放按钮位置常量（地图右下角）</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMapOptions.html#ZOOM_POSITION_RIGHT_CENTER">ZOOM_POSITION_RIGHT_CENTER</a></span></code>
<div class="block">缩放按钮位置常量（地图右边居中）。</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.android.os.Parcelable">
<!--   -->
</a>
<h3>从接口继承的字段&nbsp;android.os.Parcelable</h3>
<code>CONTENTS_FILE_DESCRIPTOR, PARCELABLE_WRITE_RETURN_VALUE</code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMapOptions.html#AMapOptions--">AMapOptions</a></span>()</code>
<div class="block">MapView 初始化选项。</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>所有方法</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">实例方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">具体方法</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../../../com/amap/api/maps/AMapOptions.html" title="com.amap.api.maps中的类">AMapOptions</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMapOptions.html#camera-com.amap.api.maps.model.CameraPosition-">camera</a></span>(<a href="../../../../com/amap/api/maps/model/CameraPosition.html" title="com.amap.api.maps.model中的类">CameraPosition</a>&nbsp;camera)</code>
<div class="block">设置地图初始化时的地图状态， 默认地图中心点为北京天安门，缩放级别为 10.0f。</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/amap/api/maps/AMapOptions.html" title="com.amap.api.maps中的类">AMapOptions</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMapOptions.html#compassEnabled-boolean-">compassEnabled</a></span>(boolean&nbsp;enabled)</code>
<div class="block">设置指南针是否可用。</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../../../com/amap/api/maps/model/CameraPosition.html" title="com.amap.api.maps.model中的类">CameraPosition</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMapOptions.html#getCamera--">getCamera</a></span>()</code>
<div class="block">获取初始化选项中地图状态。</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMapOptions.html#getCompassEnabled--">getCompassEnabled</a></span>()</code>
<div class="block">返回初始化选项中指南针功能是否可用。</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMapOptions.html#getLogoPosition--">getLogoPosition</a></span>()</code>
<div class="block">获取初始化选项“高德地图”Logo的位置。</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMapOptions.html#getMapType--">getMapType</a></span>()</code>
<div class="block">返回初始化选项中地图模式。</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMapOptions.html#getRotateGesturesEnabled--">getRotateGesturesEnabled</a></span>()</code>
<div class="block">返回初始化选项中地图旋转手势是否可用。</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMapOptions.html#getScaleControlsEnabled--">getScaleControlsEnabled</a></span>()</code>
<div class="block">返回初始化选项中比例尺功能是否可用。</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMapOptions.html#getScrollGesturesEnabled--">getScrollGesturesEnabled</a></span>()</code>
<div class="block">返回初始化选项中拖动手势是否可用。</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMapOptions.html#getTiltGesturesEnabled--">getTiltGesturesEnabled</a></span>()</code>
<div class="block">返回初始化选项中地图倾斜手势（显示3D效果）是否可用。</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMapOptions.html#getZoomControlsEnabled--">getZoomControlsEnabled</a></span>()</code>
<div class="block">返回初始化选项中地图是否允许缩放。</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMapOptions.html#getZoomGesturesEnabled--">getZoomGesturesEnabled</a></span>()</code>
<div class="block">返回初始化选项中缩放手势是否可用。</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code><a href="../../../../com/amap/api/maps/AMapOptions.html" title="com.amap.api.maps中的类">AMapOptions</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMapOptions.html#logoPosition-int-">logoPosition</a></span>(int&nbsp;position)</code>
<div class="block">设置“高德地图”Logo的位置。</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/amap/api/maps/AMapOptions.html" title="com.amap.api.maps中的类">AMapOptions</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMapOptions.html#mapType-int-">mapType</a></span>(int&nbsp;mapType)</code>
<div class="block">设置地图模式，默认普通地图。</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code><a href="../../../../com/amap/api/maps/AMapOptions.html" title="com.amap.api.maps中的类">AMapOptions</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMapOptions.html#rotateGesturesEnabled-boolean-">rotateGesturesEnabled</a></span>(boolean&nbsp;enabled)</code>
<div class="block">设置地图是否可以通过手势进行旋转。</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/amap/api/maps/AMapOptions.html" title="com.amap.api.maps中的类">AMapOptions</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMapOptions.html#scaleControlsEnabled-boolean-">scaleControlsEnabled</a></span>(boolean&nbsp;enabled)</code>
<div class="block">设置地图是否显示比例尺，默认为false。</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code><a href="../../../../com/amap/api/maps/AMapOptions.html" title="com.amap.api.maps中的类">AMapOptions</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMapOptions.html#scrollGesturesEnabled-boolean-">scrollGesturesEnabled</a></span>(boolean&nbsp;enabled)</code>
<div class="block">设置地图是否可以通过手势滑动。</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/amap/api/maps/AMapOptions.html" title="com.amap.api.maps中的类">AMapOptions</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMapOptions.html#tiltGesturesEnabled-boolean-">tiltGesturesEnabled</a></span>(boolean&nbsp;enabled)</code>
<div class="block">设置地图是否可以通过手势倾斜（3D效果），默认为true。</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code><a href="../../../../com/amap/api/maps/AMapOptions.html" title="com.amap.api.maps中的类">AMapOptions</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMapOptions.html#zoomControlsEnabled-boolean-">zoomControlsEnabled</a></span>(boolean&nbsp;enabled)</code>
<div class="block">设置地图是否允许缩放。</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/amap/api/maps/AMapOptions.html" title="com.amap.api.maps中的类">AMapOptions</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMapOptions.html#zoomGesturesEnabled-boolean-">zoomGesturesEnabled</a></span>(boolean&nbsp;enabled)</code>
<div class="block">设置地图是否可以通过手势进行缩放。</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.android.os.Parcelable">
<!--   -->
</a>
<h3>从接口继承的方法&nbsp;android.os.Parcelable</h3>
<code>describeContents, writeToParcel</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>字段详细资料</h3>
<a name="CREATOR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CREATOR</h4>
<pre>public static final&nbsp;<a href="../../../../com/amap/api/maps/AMapOptionsCreator.html" title="com.amap.api.maps中的类">AMapOptionsCreator</a> CREATOR</pre>
</li>
</ul>
<a name="LOGO_POSITION_BOTTOM_LEFT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LOGO_POSITION_BOTTOM_LEFT</h4>
<pre>public static final&nbsp;int LOGO_POSITION_BOTTOM_LEFT</pre>
<div class="block">Logo位置常量（地图左下角）。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>V2.0.2</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.maps.AMapOptions.LOGO_POSITION_BOTTOM_LEFT">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="LOGO_POSITION_BOTTOM_CENTER">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LOGO_POSITION_BOTTOM_CENTER</h4>
<pre>public static final&nbsp;int LOGO_POSITION_BOTTOM_CENTER</pre>
<div class="block">Logo位置常量（地图底部居中）。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>V2.0.2</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.maps.AMapOptions.LOGO_POSITION_BOTTOM_CENTER">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="LOGO_POSITION_BOTTOM_RIGHT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LOGO_POSITION_BOTTOM_RIGHT</h4>
<pre>public static final&nbsp;int LOGO_POSITION_BOTTOM_RIGHT</pre>
<div class="block">Logo位置常量（地图右下角）。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>V2.0.2</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.maps.AMapOptions.LOGO_POSITION_BOTTOM_RIGHT">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="ZOOM_POSITION_RIGHT_CENTER">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ZOOM_POSITION_RIGHT_CENTER</h4>
<pre>public static final&nbsp;int ZOOM_POSITION_RIGHT_CENTER</pre>
<div class="block">缩放按钮位置常量（地图右边居中）。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>V2.3.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.maps.AMapOptions.ZOOM_POSITION_RIGHT_CENTER">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="ZOOM_POSITION_RIGHT_BUTTOM">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ZOOM_POSITION_RIGHT_BUTTOM</h4>
<pre>public static final&nbsp;int ZOOM_POSITION_RIGHT_BUTTOM</pre>
<div class="block">缩放按钮位置常量（地图右下角）</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>V2.3.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.maps.AMapOptions.ZOOM_POSITION_RIGHT_BUTTOM">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="LOGO_MARGIN_LEFT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LOGO_MARGIN_LEFT</h4>
<pre>public static final&nbsp;int LOGO_MARGIN_LEFT</pre>
<div class="block">LOGO边缘MARGIN常量（左边）</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>V4.0.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.maps.AMapOptions.LOGO_MARGIN_LEFT">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="LOGO_MARGIN_RIGHT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LOGO_MARGIN_RIGHT</h4>
<pre>public static final&nbsp;int LOGO_MARGIN_RIGHT</pre>
<div class="block">LOGO边缘MARGIN常量（右边）</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>V4.0.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.maps.AMapOptions.LOGO_MARGIN_RIGHT">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="LOGO_MARGIN_BOTTOM">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>LOGO_MARGIN_BOTTOM</h4>
<pre>public static final&nbsp;int LOGO_MARGIN_BOTTOM</pre>
<div class="block">LOGO边缘MARGIN常量（底部）</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>V4.0.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.maps.AMapOptions.LOGO_MARGIN_BOTTOM">常量字段值</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="AMapOptions--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>AMapOptions</h4>
<pre>public&nbsp;AMapOptions()</pre>
<div class="block">MapView 初始化选项。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>V2.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="logoPosition-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>logoPosition</h4>
<pre>public&nbsp;<a href="../../../../com/amap/api/maps/AMapOptions.html" title="com.amap.api.maps中的类">AMapOptions</a>&nbsp;logoPosition(int&nbsp;position)</pre>
<div class="block">设置“高德地图”Logo的位置。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>position</code> - Logo的位置。
                 左下：LOGO_POSITION_BOTTOM_LEFT
                 底部居中：LOGO_POSITION_BOTTOM_CENTER
                 右下：LOGO_POSITION_BOTTOM_RIGHT</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>AMapOptions 该地图初始化选项对象。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.2</dd>
</dl>
</li>
</ul>
<a name="mapType-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>mapType</h4>
<pre>public&nbsp;<a href="../../../../com/amap/api/maps/AMapOptions.html" title="com.amap.api.maps中的类">AMapOptions</a>&nbsp;mapType(int&nbsp;mapType)</pre>
<div class="block">设置地图模式，默认普通地图。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>mapType</code> - 地图模式： MAP_TYPE_NORMAL：普通地图，值为1；
             MAP_TYPE_SATELLITE：卫星地图，值为2；
             MAP_TYPE_NIGHT 黑夜地图，夜间模式，值为3；
             MAP_TYPE_NAVI 导航模式，值为4；
             MAP_TYPE_BUS 公交模式，值为5。</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>AMapOptions 该地图初始化选项对象。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="camera-com.amap.api.maps.model.CameraPosition-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>camera</h4>
<pre>public&nbsp;<a href="../../../../com/amap/api/maps/AMapOptions.html" title="com.amap.api.maps中的类">AMapOptions</a>&nbsp;camera(<a href="../../../../com/amap/api/maps/model/CameraPosition.html" title="com.amap.api.maps.model中的类">CameraPosition</a>&nbsp;camera)</pre>
<div class="block">设置地图初始化时的地图状态， 默认地图中心点为北京天安门，缩放级别为 10.0f。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>camera</code> - 地图状态。</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>AMapOptions 该地图初始化选项对象。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="scaleControlsEnabled-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>scaleControlsEnabled</h4>
<pre>public&nbsp;<a href="../../../../com/amap/api/maps/AMapOptions.html" title="com.amap.api.maps中的类">AMapOptions</a>&nbsp;scaleControlsEnabled(boolean&nbsp;enabled)</pre>
<div class="block">设置地图是否显示比例尺，默认为false。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>enabled</code> - true 显示，false 不显示。</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>AMapOptions 该地图初始化选项对象。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.1</dd>
</dl>
</li>
</ul>
<a name="zoomControlsEnabled-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>zoomControlsEnabled</h4>
<pre>public&nbsp;<a href="../../../../com/amap/api/maps/AMapOptions.html" title="com.amap.api.maps中的类">AMapOptions</a>&nbsp;zoomControlsEnabled(boolean&nbsp;enabled)</pre>
<div class="block">设置地图是否允许缩放。默认为true。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>enabled</code> - true 支持，false 不支持。</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>AMapOptions 该地图初始化选项对象。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="compassEnabled-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>compassEnabled</h4>
<pre>public&nbsp;<a href="../../../../com/amap/api/maps/AMapOptions.html" title="com.amap.api.maps中的类">AMapOptions</a>&nbsp;compassEnabled(boolean&nbsp;enabled)</pre>
<div class="block">设置指南针是否可用。默认为启用。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>enabled</code> - true表示可用，false表示不可用。</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>AMapOptions 该地图初始化选项对象。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.1</dd>
</dl>
</li>
</ul>
<a name="scrollGesturesEnabled-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>scrollGesturesEnabled</h4>
<pre>public&nbsp;<a href="../../../../com/amap/api/maps/AMapOptions.html" title="com.amap.api.maps中的类">AMapOptions</a>&nbsp;scrollGesturesEnabled(boolean&nbsp;enabled)</pre>
<div class="block">设置地图是否可以通过手势滑动。默认为true。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>enabled</code> - true 支持，false 不支持。</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>AMapOptions 该地图初始化选项对象。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="zoomGesturesEnabled-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>zoomGesturesEnabled</h4>
<pre>public&nbsp;<a href="../../../../com/amap/api/maps/AMapOptions.html" title="com.amap.api.maps中的类">AMapOptions</a>&nbsp;zoomGesturesEnabled(boolean&nbsp;enabled)</pre>
<div class="block">设置地图是否可以通过手势进行缩放。默认为true。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>enabled</code> - true 支持，false 不支持。</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>AMapOptions 该地图初始化选项对象。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="tiltGesturesEnabled-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>tiltGesturesEnabled</h4>
<pre>public&nbsp;<a href="../../../../com/amap/api/maps/AMapOptions.html" title="com.amap.api.maps中的类">AMapOptions</a>&nbsp;tiltGesturesEnabled(boolean&nbsp;enabled)</pre>
<div class="block">设置地图是否可以通过手势倾斜（3D效果），默认为true。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>enabled</code> - true 支持，false 不支持。</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>AMapOptions 该地图初始化选项对象。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="rotateGesturesEnabled-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>rotateGesturesEnabled</h4>
<pre>public&nbsp;<a href="../../../../com/amap/api/maps/AMapOptions.html" title="com.amap.api.maps中的类">AMapOptions</a>&nbsp;rotateGesturesEnabled(boolean&nbsp;enabled)</pre>
<div class="block">设置地图是否可以通过手势进行旋转。默认为true.</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>enabled</code> - true 支持，false 不支持。</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>AMapOptions 该地图初始化选项对象。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="getLogoPosition--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLogoPosition</h4>
<pre>public&nbsp;int&nbsp;getLogoPosition()</pre>
<div class="block">获取初始化选项“高德地图”Logo的位置。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>返回“高德地图”Logo的位置常量。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.2</dd>
</dl>
</li>
</ul>
<a name="getMapType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMapType</h4>
<pre>public&nbsp;int&nbsp;getMapType()</pre>
<div class="block">返回初始化选项中地图模式。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>初始化选项中地图模式。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="getCamera--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCamera</h4>
<pre>public&nbsp;<a href="../../../../com/amap/api/maps/model/CameraPosition.html" title="com.amap.api.maps.model中的类">CameraPosition</a>&nbsp;getCamera()</pre>
<div class="block">获取初始化选项中地图状态。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>CameraPosition 初始化选项中地图状态。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="getScaleControlsEnabled--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getScaleControlsEnabled</h4>
<pre>public&nbsp;boolean&nbsp;getScaleControlsEnabled()</pre>
<div class="block">返回初始化选项中比例尺功能是否可用。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>初始化选项中比例尺功能是否可用。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.1</dd>
</dl>
</li>
</ul>
<a name="getZoomControlsEnabled--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getZoomControlsEnabled</h4>
<pre>public&nbsp;boolean&nbsp;getZoomControlsEnabled()</pre>
<div class="block">返回初始化选项中地图是否允许缩放。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>初始化选项中地图是否允许缩放。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="getCompassEnabled--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCompassEnabled</h4>
<pre>public&nbsp;boolean&nbsp;getCompassEnabled()</pre>
<div class="block">返回初始化选项中指南针功能是否可用。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>初始化选项中指南针是否可用。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.1</dd>
</dl>
</li>
</ul>
<a name="getScrollGesturesEnabled--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getScrollGesturesEnabled</h4>
<pre>public&nbsp;boolean&nbsp;getScrollGesturesEnabled()</pre>
<div class="block">返回初始化选项中拖动手势是否可用。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>初始化选项中拖动手势是否可用。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="getZoomGesturesEnabled--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getZoomGesturesEnabled</h4>
<pre>public&nbsp;boolean&nbsp;getZoomGesturesEnabled()</pre>
<div class="block">返回初始化选项中缩放手势是否可用。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>初始化选项中缩放手势是否可用。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="getTiltGesturesEnabled--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTiltGesturesEnabled</h4>
<pre>public&nbsp;boolean&nbsp;getTiltGesturesEnabled()</pre>
<div class="block">返回初始化选项中地图倾斜手势（显示3D效果）是否可用。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>初始化选项中地图倾斜手势（显示3D效果）是否可用。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="getRotateGesturesEnabled--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getRotateGesturesEnabled</h4>
<pre>public&nbsp;boolean&nbsp;getRotateGesturesEnabled()</pre>
<div class="block">返回初始化选项中地图旋转手势是否可用。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>初始化选项中地图旋转手势是否可用。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/AMapOptions.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/amap/api/maps/AMapException.html" title="com.amap.api.maps中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../com/amap/api/maps/AMapUtils.html" title="com.amap.api.maps中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/amap/api/maps/AMapOptions.html" target="_top">框架</a></li>
<li><a href="AMapOptions.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li><a href="#field.summary">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li><a href="#field.detail">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
