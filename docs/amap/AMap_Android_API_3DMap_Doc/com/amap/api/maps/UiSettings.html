<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>UiSettings</title>
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="UiSettings";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10,"i27":10};
var tabs = {65535:["t0","所有方法"],2:["t2","实例方法"],8:["t4","具体方法"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/UiSettings.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/amap/api/maps/TextureSupportMapFragment.html" title="com.amap.api.maps中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../com/amap/api/maps/WearMapView.html" title="com.amap.api.maps中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/amap/api/maps/UiSettings.html" target="_top">框架</a></li>
<li><a href="UiSettings.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.amap.api.maps</div>
<h2 title="类 UiSettings" class="title">类 UiSettings</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.amap.api.maps.UiSettings</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public final class <span class="typeNameLabel">UiSettings</span>
extends java.lang.Object</pre>
<div class="block">地图内置UI及手势控制器。</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>所有方法</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">实例方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">具体方法</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/UiSettings.html#getLogoPosition--">getLogoPosition</a></span>()</code>
<div class="block">获取“高德地图”Logo的位置。</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/UiSettings.html#getZoomPosition--">getZoomPosition</a></span>()</code>
<div class="block">获取缩放按钮的位置。</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/UiSettings.html#isCompassEnabled--">isCompassEnabled</a></span>()</code>
<div class="block">返回指南针控件是否可见。</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/UiSettings.html#isGestureScaleByMapCenter--">isGestureScaleByMapCenter</a></span>()</code>
<div class="block">返回是否以地图中心点缩放</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/UiSettings.html#isIndoorSwitchEnabled--">isIndoorSwitchEnabled</a></span>()</code>
<div class="block">返回室内地图楼层切换控件是否显示。</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>protected boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/UiSettings.html#isLogoEnable--">isLogoEnable</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/UiSettings.html#isMyLocationButtonEnabled--">isMyLocationButtonEnabled</a></span>()</code>
<div class="block">返回定位按钮是否可见。</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/UiSettings.html#isRotateGesturesEnabled--">isRotateGesturesEnabled</a></span>()</code>
<div class="block">返回旋转手势是否可用。</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/UiSettings.html#isScaleControlsEnabled--">isScaleControlsEnabled</a></span>()</code>
<div class="block">返回比例尺控件是否可见。</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/UiSettings.html#isScrollGesturesEnabled--">isScrollGesturesEnabled</a></span>()</code>
<div class="block">返回拖拽手势是否可用。</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/UiSettings.html#isTiltGesturesEnabled--">isTiltGesturesEnabled</a></span>()</code>
<div class="block">返回倾斜手势是否可用。</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/UiSettings.html#isZoomControlsEnabled--">isZoomControlsEnabled</a></span>()</code>
<div class="block">返回缩放按钮是否可见。</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/UiSettings.html#isZoomGesturesEnabled--">isZoomGesturesEnabled</a></span>()</code>
<div class="block">返回缩放手势是否可用。</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/UiSettings.html#setAllGesturesEnabled-boolean-">setAllGesturesEnabled</a></span>(boolean&nbsp;enabled)</code>
<div class="block">设置所有手势是否可用</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/UiSettings.html#setCompassEnabled-boolean-">setCompassEnabled</a></span>(boolean&nbsp;enabled)</code>
<div class="block">设置指南针是否可见。</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/UiSettings.html#setGestureScaleByMapCenter-boolean-">setGestureScaleByMapCenter</a></span>(boolean&nbsp;isGestureScaleByMapCenter)</code>
<div class="block">设置是否以地图中心点缩放 <br>
 注：优先级低于<a href="../../../../com/amap/api/maps/AMap.html#setPointToCenter-int-int-"><code>AMap.setPointToCenter(int, int)</code></a></div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/UiSettings.html#setIndoorSwitchEnabled-boolean-">setIndoorSwitchEnabled</a></span>(boolean&nbsp;isIndoorSwitchEnabled)</code>
<div class="block">设置室内地图楼层切换控件是否可见。</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/UiSettings.html#setLogoBottomMargin-int-">setLogoBottomMargin</a></span>(int&nbsp;pixels)</code>
<div class="block">设置Logo下边界距离屏幕底部的边距</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/UiSettings.html#setLogoLeftMargin-int-">setLogoLeftMargin</a></span>(int&nbsp;pixels)</code>
<div class="block">设置Logo左边界距离屏幕左侧的边距</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/UiSettings.html#setLogoPosition-int-">setLogoPosition</a></span>(int&nbsp;position)</code>
<div class="block">设置“高德地图”Logo的位置。</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/UiSettings.html#setMyLocationButtonEnabled-boolean-">setMyLocationButtonEnabled</a></span>(boolean&nbsp;enabled)</code>
<div class="block">设置定位按钮是否可见。</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/UiSettings.html#setRotateGesturesEnabled-boolean-">setRotateGesturesEnabled</a></span>(boolean&nbsp;enabled)</code>
<div class="block">设置旋转手势是否可用。</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/UiSettings.html#setScaleControlsEnabled-boolean-">setScaleControlsEnabled</a></span>(boolean&nbsp;enabled)</code>
<div class="block">设置比例尺控件是否可见</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/UiSettings.html#setScrollGesturesEnabled-boolean-">setScrollGesturesEnabled</a></span>(boolean&nbsp;enabled)</code>
<div class="block">设置拖拽手势是否可用。</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/UiSettings.html#setTiltGesturesEnabled-boolean-">setTiltGesturesEnabled</a></span>(boolean&nbsp;enabled)</code>
<div class="block">设置倾斜手势是否可用。</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/UiSettings.html#setZoomControlsEnabled-boolean-">setZoomControlsEnabled</a></span>(boolean&nbsp;enabled)</code>
<div class="block">设置缩放按钮是否可见。</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/UiSettings.html#setZoomGesturesEnabled-boolean-">setZoomGesturesEnabled</a></span>(boolean&nbsp;enabled)</code>
<div class="block">设置双指缩放手势是否可用。</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/UiSettings.html#setZoomPosition-int-">setZoomPosition</a></span>(int&nbsp;position)</code>
<div class="block">设置缩放按钮的位置。</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="setScaleControlsEnabled-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setScaleControlsEnabled</h4>
<pre>public&nbsp;void&nbsp;setScaleControlsEnabled(boolean&nbsp;enabled)</pre>
<div class="block">设置比例尺控件是否可见</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>enabled</code> - true表示可见，false表示不可见。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.1</dd>
</dl>
</li>
</ul>
<a name="setZoomControlsEnabled-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setZoomControlsEnabled</h4>
<pre>public&nbsp;void&nbsp;setZoomControlsEnabled(boolean&nbsp;enabled)</pre>
<div class="block">设置缩放按钮是否可见。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>enabled</code> - true表示可见，false表示不可见。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="setCompassEnabled-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCompassEnabled</h4>
<pre>public&nbsp;void&nbsp;setCompassEnabled(boolean&nbsp;enabled)</pre>
<div class="block">设置指南针是否可见。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>enabled</code> - true表示可见，false表示不可见。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.1</dd>
</dl>
</li>
</ul>
<a name="setMyLocationButtonEnabled-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMyLocationButtonEnabled</h4>
<pre>public&nbsp;void&nbsp;setMyLocationButtonEnabled(boolean&nbsp;enabled)</pre>
<div class="block">设置定位按钮是否可见。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>enabled</code> - true表示可见，false表示不可见。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="setScrollGesturesEnabled-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setScrollGesturesEnabled</h4>
<pre>public&nbsp;void&nbsp;setScrollGesturesEnabled(boolean&nbsp;enabled)</pre>
<div class="block">设置拖拽手势是否可用。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>enabled</code> - true为可用； false 为不可用。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="setZoomGesturesEnabled-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setZoomGesturesEnabled</h4>
<pre>public&nbsp;void&nbsp;setZoomGesturesEnabled(boolean&nbsp;enabled)</pre>
<div class="block">设置双指缩放手势是否可用。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>enabled</code> - true为可用； false 为不可用。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="setTiltGesturesEnabled-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTiltGesturesEnabled</h4>
<pre>public&nbsp;void&nbsp;setTiltGesturesEnabled(boolean&nbsp;enabled)</pre>
<div class="block">设置倾斜手势是否可用。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>enabled</code> - true为可用； false 为不可用。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="setRotateGesturesEnabled-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRotateGesturesEnabled</h4>
<pre>public&nbsp;void&nbsp;setRotateGesturesEnabled(boolean&nbsp;enabled)</pre>
<div class="block">设置旋转手势是否可用。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>enabled</code> - true为可用； false 为不可用。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="setAllGesturesEnabled-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAllGesturesEnabled</h4>
<pre>public&nbsp;void&nbsp;setAllGesturesEnabled(boolean&nbsp;enabled)</pre>
<div class="block">设置所有手势是否可用</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>enabled</code> - true为可用； false 为不可用。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="setLogoPosition-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLogoPosition</h4>
<pre>public&nbsp;void&nbsp;setLogoPosition(int&nbsp;position)</pre>
<div class="block">设置“高德地图”Logo的位置。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>position</code> - 位置参数。屏幕左下角：<a href="../../../../com/amap/api/maps/AMapOptions.html#LOGO_POSITION_BOTTOM_LEFT"><code>AMapOptions.LOGO_POSITION_BOTTOM_LEFT</code></a> 底部居中：<a href="../../../../com/amap/api/maps/AMapOptions.html#LOGO_POSITION_BOTTOM_CENTER"><code>AMapOptions.LOGO_POSITION_BOTTOM_CENTER</code></a> 右下：<a href="../../../../com/amap/api/maps/AMapOptions.html#LOGO_MARGIN_RIGHT"><code>AMapOptions.LOGO_MARGIN_RIGHT</code></a></dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.2</dd>
</dl>
</li>
</ul>
<a name="setZoomPosition-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setZoomPosition</h4>
<pre>public&nbsp;void&nbsp;setZoomPosition(int&nbsp;position)</pre>
<div class="block">设置缩放按钮的位置。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>position</code> - 右边界中部：<a href="../../../../com/amap/api/maps/AMapOptions.html#ZOOM_POSITION_RIGHT_CENTER"><code>AMapOptions.ZOOM_POSITION_RIGHT_CENTER</code></a> 右下：<a href="../../../../com/amap/api/maps/AMapOptions.html#ZOOM_POSITION_RIGHT_BUTTOM"><code>AMapOptions.ZOOM_POSITION_RIGHT_BUTTOM</code></a>。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.3.0</dd>
</dl>
</li>
</ul>
<a name="getZoomPosition--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getZoomPosition</h4>
<pre>public&nbsp;int&nbsp;getZoomPosition()</pre>
<div class="block">获取缩放按钮的位置。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>缩放按钮的位置常量。右边界中部：<a href="../../../../com/amap/api/maps/AMapOptions.html#ZOOM_POSITION_RIGHT_CENTER"><code>AMapOptions.ZOOM_POSITION_RIGHT_CENTER</code></a> 右下：<a href="../../../../com/amap/api/maps/AMapOptions.html#ZOOM_POSITION_RIGHT_BUTTOM"><code>AMapOptions.ZOOM_POSITION_RIGHT_BUTTOM</code></a>。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.2</dd>
</dl>
</li>
</ul>
<a name="isScaleControlsEnabled--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isScaleControlsEnabled</h4>
<pre>public&nbsp;boolean&nbsp;isScaleControlsEnabled()</pre>
<div class="block">返回比例尺控件是否可见。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>是否可见。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.1</dd>
</dl>
</li>
</ul>
<a name="isZoomControlsEnabled--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isZoomControlsEnabled</h4>
<pre>public&nbsp;boolean&nbsp;isZoomControlsEnabled()</pre>
<div class="block">返回缩放按钮是否可见。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>是否可见。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="isCompassEnabled--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isCompassEnabled</h4>
<pre>public&nbsp;boolean&nbsp;isCompassEnabled()</pre>
<div class="block">返回指南针控件是否可见。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>是否可见。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.1</dd>
</dl>
</li>
</ul>
<a name="isMyLocationButtonEnabled--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isMyLocationButtonEnabled</h4>
<pre>public&nbsp;boolean&nbsp;isMyLocationButtonEnabled()</pre>
<div class="block">返回定位按钮是否可见。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>是否可见。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="isScrollGesturesEnabled--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isScrollGesturesEnabled</h4>
<pre>public&nbsp;boolean&nbsp;isScrollGesturesEnabled()</pre>
<div class="block">返回拖拽手势是否可用。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>是否可用。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="isZoomGesturesEnabled--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isZoomGesturesEnabled</h4>
<pre>public&nbsp;boolean&nbsp;isZoomGesturesEnabled()</pre>
<div class="block">返回缩放手势是否可用。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>true 是否可用。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="isTiltGesturesEnabled--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isTiltGesturesEnabled</h4>
<pre>public&nbsp;boolean&nbsp;isTiltGesturesEnabled()</pre>
<div class="block">返回倾斜手势是否可用。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>是否可用。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="isRotateGesturesEnabled--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isRotateGesturesEnabled</h4>
<pre>public&nbsp;boolean&nbsp;isRotateGesturesEnabled()</pre>
<div class="block">返回旋转手势是否可用。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>是否可用。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="getLogoPosition--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLogoPosition</h4>
<pre>public&nbsp;int&nbsp;getLogoPosition()</pre>
<div class="block">获取“高德地图”Logo的位置。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>“高德地图”Logo的位置常量。屏幕左下角：<a href="../../../../com/amap/api/maps/AMapOptions.html#LOGO_POSITION_BOTTOM_LEFT"><code>AMapOptions.LOGO_POSITION_BOTTOM_LEFT</code></a> 底部居中：<a href="../../../../com/amap/api/maps/AMapOptions.html#LOGO_POSITION_BOTTOM_CENTER"><code>AMapOptions.LOGO_POSITION_BOTTOM_CENTER</code></a> 右下：<a href="../../../../com/amap/api/maps/AMapOptions.html#LOGO_MARGIN_RIGHT"><code>AMapOptions.LOGO_MARGIN_RIGHT</code></a></dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.2</dd>
</dl>
</li>
</ul>
<a name="isIndoorSwitchEnabled--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isIndoorSwitchEnabled</h4>
<pre>public&nbsp;boolean&nbsp;isIndoorSwitchEnabled()</pre>
<div class="block">返回室内地图楼层切换控件是否显示。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>是否显示。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.3.0</dd>
</dl>
</li>
</ul>
<a name="setIndoorSwitchEnabled-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setIndoorSwitchEnabled</h4>
<pre>public&nbsp;void&nbsp;setIndoorSwitchEnabled(boolean&nbsp;isIndoorSwitchEnabled)</pre>
<div class="block">设置室内地图楼层切换控件是否可见。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>isIndoorSwitchEnabled</code> - true 为可见；false 不可见。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.3.0</dd>
</dl>
</li>
</ul>
<a name="setLogoLeftMargin-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLogoLeftMargin</h4>
<pre>public final&nbsp;void&nbsp;setLogoLeftMargin(int&nbsp;pixels)</pre>
<div class="block">设置Logo左边界距离屏幕左侧的边距</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>pixels</code> - 像素</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>4.0.0</dd>
</dl>
</li>
</ul>
<a name="setLogoBottomMargin-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLogoBottomMargin</h4>
<pre>public final&nbsp;void&nbsp;setLogoBottomMargin(int&nbsp;pixels)</pre>
<div class="block">设置Logo下边界距离屏幕底部的边距</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>pixels</code> - 像素</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>4.0.0</dd>
</dl>
</li>
</ul>
<a name="setGestureScaleByMapCenter-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setGestureScaleByMapCenter</h4>
<pre>public&nbsp;void&nbsp;setGestureScaleByMapCenter(boolean&nbsp;isGestureScaleByMapCenter)</pre>
<div class="block">设置是否以地图中心点缩放 <br>
 注：优先级低于<a href="../../../../com/amap/api/maps/AMap.html#setPointToCenter-int-int-"><code>AMap.setPointToCenter(int, int)</code></a></div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>isGestureScaleByMapCenter</code> - true:以地图中心的进行缩放,false:不以地图中心的进行缩放</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>4.1.3</dd>
</dl>
</li>
</ul>
<a name="isGestureScaleByMapCenter--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isGestureScaleByMapCenter</h4>
<pre>public&nbsp;boolean&nbsp;isGestureScaleByMapCenter()</pre>
<div class="block">返回是否以地图中心点缩放</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>true  以地图中心点缩放；false 不以地图中心点缩放</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>4.1.3</dd>
</dl>
</li>
</ul>
<a name="isLogoEnable--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>isLogoEnable</h4>
<pre>protected&nbsp;boolean&nbsp;isLogoEnable()</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/UiSettings.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/amap/api/maps/TextureSupportMapFragment.html" title="com.amap.api.maps中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../com/amap/api/maps/WearMapView.html" title="com.amap.api.maps中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/amap/api/maps/UiSettings.html" target="_top">框架</a></li>
<li><a href="UiSettings.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
