<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>MovingPointOverlay</title>
<link rel="stylesheet" type="text/css" href="../../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="MovingPointOverlay";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10};
var tabs = {65535:["t0","所有方法"],2:["t2","实例方法"],8:["t4","具体方法"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/MovingPointOverlay.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个类</li>
<li><a href="../../../../../../com/amap/api/maps/utils/overlay/MovingPointOverlay.MoveListener.html" title="com.amap.api.maps.utils.overlay中的接口"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?com/amap/api/maps/utils/overlay/MovingPointOverlay.html" target="_top">框架</a></li>
<li><a href="MovingPointOverlay.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li><a href="#nested.class.summary">嵌套</a>&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.amap.api.maps.utils.overlay</div>
<h2 title="类 MovingPointOverlay" class="title">类 MovingPointOverlay</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.amap.api.maps.utils.overlay.MovingPointOverlay</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">MovingPointOverlay</span>
extends java.lang.Object</pre>
<div class="block">按照指定的经纬度数据和时间，平滑移动</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.3.1</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>嵌套类概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="嵌套类概要表, 列表嵌套类和解释">
<caption><span>嵌套类</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">类和说明</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static interface&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/maps/utils/overlay/MovingPointOverlay.MoveListener.html" title="com.amap.api.maps.utils.overlay中的接口">MovingPointOverlay.MoveListener</a></span></code>
<div class="block">平滑移动时返回剩余距离接口</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/maps/utils/overlay/MovingPointOverlay.html#MovingPointOverlay-com.amap.api.maps.AMap-com.amap.api.maps.model.BasePointOverlay-">MovingPointOverlay</a></span>(<a href="../../../../../../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a>&nbsp;mAMap,
                  <a href="../../../../../../com/amap/api/maps/model/BasePointOverlay.html" title="com.amap.api.maps.model中的类">BasePointOverlay</a>&nbsp;overlay)</code>
<div class="block">根据给定的参数来构造SmoothMoveMarker对象。</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>所有方法</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">实例方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">具体方法</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/maps/utils/overlay/MovingPointOverlay.html#destroy--">destroy</a></span>()</code>
<div class="block">停止滑动并销毁Marker</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/maps/model/BasePointOverlay.html" title="com.amap.api.maps.model中的类">BasePointOverlay</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/maps/utils/overlay/MovingPointOverlay.html#getObject--">getObject</a></span>()</code>
<div class="block">获取当前移动的Marker</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/maps/model/LatLng.html" title="com.amap.api.maps.model中的类">LatLng</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/maps/utils/overlay/MovingPointOverlay.html#getPosition--">getPosition</a></span>()</code>
<div class="block">获取当前位置坐标</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/maps/utils/overlay/MovingPointOverlay.html#resetIndex--">resetIndex</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/maps/utils/overlay/MovingPointOverlay.html#setMoveListener-com.amap.api.maps.utils.overlay.MovingPointOverlay.MoveListener-">setMoveListener</a></span>(<a href="../../../../../../com/amap/api/maps/utils/overlay/MovingPointOverlay.MoveListener.html" title="com.amap.api.maps.utils.overlay中的接口">MovingPointOverlay.MoveListener</a>&nbsp;moveListener)</code>
<div class="block">设置Marker移动的回调方法</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/maps/utils/overlay/MovingPointOverlay.html#setPoints-java.util.List-">setPoints</a></span>(java.util.List&lt;<a href="../../../../../../com/amap/api/maps/model/LatLng.html" title="com.amap.api.maps.model中的类">LatLng</a>&gt;&nbsp;points)</code>
<div class="block">设置平滑移动的经纬度数组</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/maps/utils/overlay/MovingPointOverlay.html#setTotalDuration-int-">setTotalDuration</a></span>(int&nbsp;duration)</code>
<div class="block">设置平滑移动的总时间</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/maps/utils/overlay/MovingPointOverlay.html#setVisible-boolean-">setVisible</a></span>(boolean&nbsp;b)</code>
<div class="block">设置 Marker 是否可见</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/maps/utils/overlay/MovingPointOverlay.html#startSmoothMove--">startSmoothMove</a></span>()</code>
<div class="block">开始平滑移动</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/maps/utils/overlay/MovingPointOverlay.html#stopMove--">stopMove</a></span>()</code>
<div class="block">停止平滑移动</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="MovingPointOverlay-com.amap.api.maps.AMap-com.amap.api.maps.model.BasePointOverlay-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>MovingPointOverlay</h4>
<pre>public&nbsp;MovingPointOverlay(<a href="../../../../../../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a>&nbsp;mAMap,
                          <a href="../../../../../../com/amap/api/maps/model/BasePointOverlay.html" title="com.amap.api.maps.model中的类">BasePointOverlay</a>&nbsp;overlay)</pre>
<div class="block">根据给定的参数来构造SmoothMoveMarker对象。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>mAMap</code> - 地图AMap对象</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>V6.3.1</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="setPoints-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPoints</h4>
<pre>public&nbsp;void&nbsp;setPoints(java.util.List&lt;<a href="../../../../../../com/amap/api/maps/model/LatLng.html" title="com.amap.api.maps.model中的类">LatLng</a>&gt;&nbsp;points)</pre>
<div class="block">设置平滑移动的经纬度数组</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>points</code> - 平滑移动轨迹的坐标数组</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.3.1</dd>
</dl>
</li>
</ul>
<a name="resetIndex--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>resetIndex</h4>
<pre>public&nbsp;void&nbsp;resetIndex()</pre>
</li>
</ul>
<a name="setTotalDuration-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTotalDuration</h4>
<pre>public&nbsp;void&nbsp;setTotalDuration(int&nbsp;duration)</pre>
<div class="block">设置平滑移动的总时间</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>duration</code> - 单位: 秒</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.3.1</dd>
</dl>
</li>
</ul>
<a name="startSmoothMove--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>startSmoothMove</h4>
<pre>public&nbsp;void&nbsp;startSmoothMove()</pre>
<div class="block">开始平滑移动</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.3.1</dd>
</dl>
</li>
</ul>
<a name="stopMove--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>stopMove</h4>
<pre>public&nbsp;void&nbsp;stopMove()</pre>
<div class="block">停止平滑移动</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.3.1</dd>
</dl>
</li>
</ul>
<a name="getObject--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getObject</h4>
<pre>public&nbsp;<a href="../../../../../../com/amap/api/maps/model/BasePointOverlay.html" title="com.amap.api.maps.model中的类">BasePointOverlay</a>&nbsp;getObject()</pre>
<div class="block">获取当前移动的Marker</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>放回当期那移动的Marker</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.3.1</dd>
</dl>
</li>
</ul>
<a name="getPosition--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPosition</h4>
<pre>public&nbsp;<a href="../../../../../../com/amap/api/maps/model/LatLng.html" title="com.amap.api.maps.model中的类">LatLng</a>&nbsp;getPosition()</pre>
<div class="block">获取当前位置坐标</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>返回当前Marker的坐标</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.3.1</dd>
</dl>
</li>
</ul>
<a name="destroy--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>destroy</h4>
<pre>public&nbsp;void&nbsp;destroy()</pre>
<div class="block">停止滑动并销毁Marker</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.3.1</dd>
</dl>
</li>
</ul>
<a name="setVisible-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setVisible</h4>
<pre>public&nbsp;void&nbsp;setVisible(boolean&nbsp;b)</pre>
<div class="block">设置 Marker 是否可见</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>b</code> - true 可见  false  不可见</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.3.1</dd>
</dl>
</li>
</ul>
<a name="setMoveListener-com.amap.api.maps.utils.overlay.MovingPointOverlay.MoveListener-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setMoveListener</h4>
<pre>public&nbsp;void&nbsp;setMoveListener(<a href="../../../../../../com/amap/api/maps/utils/overlay/MovingPointOverlay.MoveListener.html" title="com.amap.api.maps.utils.overlay中的接口">MovingPointOverlay.MoveListener</a>&nbsp;moveListener)</pre>
<div class="block">设置Marker移动的回调方法</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>moveListener</code> - 回调监听参数</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.3.1</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/MovingPointOverlay.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个类</li>
<li><a href="../../../../../../com/amap/api/maps/utils/overlay/MovingPointOverlay.MoveListener.html" title="com.amap.api.maps.utils.overlay中的接口"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?com/amap/api/maps/utils/overlay/MovingPointOverlay.html" target="_top">框架</a></li>
<li><a href="MovingPointOverlay.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li><a href="#nested.class.summary">嵌套</a>&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
