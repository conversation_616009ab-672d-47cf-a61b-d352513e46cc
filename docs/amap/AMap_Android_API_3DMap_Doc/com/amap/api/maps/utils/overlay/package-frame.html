<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>com.amap.api.maps.utils.overlay</title>
<link rel="stylesheet" type="text/css" href="../../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../../script.js"></script>
</head>
<body>
<h1 class="bar"><a href="../../../../../../com/amap/api/maps/utils/overlay/package-summary.html" target="classFrame">com.amap.api.maps.utils.overlay</a></h1>
<div class="indexContainer">
<h2 title="接口">接口</h2>
<ul title="接口">
<li><a href="MovingPointOverlay.MoveListener.html" title="com.amap.api.maps.utils.overlay中的接口" target="classFrame"><span class="interfaceName">MovingPointOverlay.MoveListener</span></a></li>
<li><a href="SmoothMoveMarker.MoveListener.html" title="com.amap.api.maps.utils.overlay中的接口" target="classFrame"><span class="interfaceName">SmoothMoveMarker.MoveListener</span></a></li>
</ul>
<h2 title="类">类</h2>
<ul title="类">
<li><a href="MovingPointOverlay.html" title="com.amap.api.maps.utils.overlay中的类" target="classFrame">MovingPointOverlay</a></li>
<li><a href="SmoothMoveMarker.html" title="com.amap.api.maps.utils.overlay中的类" target="classFrame">SmoothMoveMarker</a></li>
</ul>
</div>
</body>
</html>
