<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>SpatialRelationUtil</title>
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="SpatialRelationUtil";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":9,"i3":9};
var tabs = {65535:["t0","所有方法"],1:["t1","静态方法"],8:["t4","具体方法"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/SpatialRelationUtil.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个类</li>
<li>下一个类</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/maps/utils/SpatialRelationUtil.html" target="_top">框架</a></li>
<li><a href="SpatialRelationUtil.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li><a href="#field.summary">字段</a>&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li><a href="#field.detail">字段</a>&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.amap.api.maps.utils</div>
<h2 title="类 SpatialRelationUtil" class="title">类 SpatialRelationUtil</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.amap.api.maps.utils.SpatialRelationUtil</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">SpatialRelationUtil</span>
extends java.lang.Object</pre>
<div class="block">计算点到线最短距离工具类</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>4.1.3</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>字段概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="字段概要表, 列表字段和解释">
<caption><span>字段</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">字段和说明</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/utils/SpatialRelationUtil.html#A_CIRCLE_DEGREE">A_CIRCLE_DEGREE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/utils/SpatialRelationUtil.html#A_HALF_CIRCLE_DEGREE">A_HALF_CIRCLE_DEGREE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/utils/SpatialRelationUtil.html#MIN_OFFSET_DEGREE">MIN_OFFSET_DEGREE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/utils/SpatialRelationUtil.html#MIN_POLYLINE_POINT_SIZE">MIN_POLYLINE_POINT_SIZE</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>所有方法</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">静态方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">具体方法</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static android.util.Pair&lt;java.lang.Integer,DPoint&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/utils/SpatialRelationUtil.html#calShortestDistancePoint-java.util.List-DPoint-">calShortestDistancePoint</a></span>(java.util.List&lt;DPoint&gt;&nbsp;points,
                        DPoint&nbsp;point)</code>
<div class="block">计算一个点在线上的垂足，如果垂足在线上的某一顶点，则直接返回顶点的下标</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static android.util.Pair&lt;java.lang.Integer,DPoint&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/utils/SpatialRelationUtil.html#calShortestDistancePoint-java.util.List-DPoint-float-">calShortestDistancePoint</a></span>(java.util.List&lt;DPoint&gt;&nbsp;points,
                        DPoint&nbsp;point,
                        float&nbsp;bearing)</code>&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static android.util.Pair&lt;java.lang.Integer,<a href="../../../../../com/amap/api/maps/model/LatLng.html" title="com.amap.api.maps.model中的类">LatLng</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/utils/SpatialRelationUtil.html#calShortestDistancePoint-java.util.List-com.amap.api.maps.model.LatLng-">calShortestDistancePoint</a></span>(java.util.List&lt;<a href="../../../../../com/amap/api/maps/model/LatLng.html" title="com.amap.api.maps.model中的类">LatLng</a>&gt;&nbsp;points,
                        <a href="../../../../../com/amap/api/maps/model/LatLng.html" title="com.amap.api.maps.model中的类">LatLng</a>&nbsp;point)</code>
<div class="block">计算一个点在线上的垂足，如果垂足在线上的某一顶点，则直接返回顶点的下标</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>static android.util.Pair&lt;java.lang.Integer,<a href="../../../../../com/amap/api/maps/model/LatLng.html" title="com.amap.api.maps.model中的类">LatLng</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/utils/SpatialRelationUtil.html#calShortestDistancePoint-java.util.List-com.amap.api.maps.model.LatLng-float-double-">calShortestDistancePoint</a></span>(java.util.List&lt;<a href="../../../../../com/amap/api/maps/model/LatLng.html" title="com.amap.api.maps.model中的类">LatLng</a>&gt;&nbsp;points,
                        <a href="../../../../../com/amap/api/maps/model/LatLng.html" title="com.amap.api.maps.model中的类">LatLng</a>&nbsp;point,
                        float&nbsp;bearing,
                        double&nbsp;distance)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>字段详细资料</h3>
<a name="MIN_POLYLINE_POINT_SIZE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MIN_POLYLINE_POINT_SIZE</h4>
<pre>public static final&nbsp;int MIN_POLYLINE_POINT_SIZE</pre>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.maps.utils.SpatialRelationUtil.MIN_POLYLINE_POINT_SIZE">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="A_HALF_CIRCLE_DEGREE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>A_HALF_CIRCLE_DEGREE</h4>
<pre>public static final&nbsp;int A_HALF_CIRCLE_DEGREE</pre>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.maps.utils.SpatialRelationUtil.A_HALF_CIRCLE_DEGREE">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="A_CIRCLE_DEGREE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>A_CIRCLE_DEGREE</h4>
<pre>public static final&nbsp;int A_CIRCLE_DEGREE</pre>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.maps.utils.SpatialRelationUtil.A_CIRCLE_DEGREE">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="MIN_OFFSET_DEGREE">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>MIN_OFFSET_DEGREE</h4>
<pre>public static final&nbsp;int MIN_OFFSET_DEGREE</pre>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.maps.utils.SpatialRelationUtil.MIN_OFFSET_DEGREE">常量字段值</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="calShortestDistancePoint-java.util.List-com.amap.api.maps.model.LatLng-float-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>calShortestDistancePoint</h4>
<pre>public static&nbsp;android.util.Pair&lt;java.lang.Integer,<a href="../../../../../com/amap/api/maps/model/LatLng.html" title="com.amap.api.maps.model中的类">LatLng</a>&gt;&nbsp;calShortestDistancePoint(java.util.List&lt;<a href="../../../../../com/amap/api/maps/model/LatLng.html" title="com.amap.api.maps.model中的类">LatLng</a>&gt;&nbsp;points,
                                                                                   <a href="../../../../../com/amap/api/maps/model/LatLng.html" title="com.amap.api.maps.model中的类">LatLng</a>&nbsp;point,
                                                                                   float&nbsp;bearing,
                                                                                   double&nbsp;distance)</pre>
</li>
</ul>
<a name="calShortestDistancePoint-java.util.List-com.amap.api.maps.model.LatLng-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>calShortestDistancePoint</h4>
<pre>public static&nbsp;android.util.Pair&lt;java.lang.Integer,<a href="../../../../../com/amap/api/maps/model/LatLng.html" title="com.amap.api.maps.model中的类">LatLng</a>&gt;&nbsp;calShortestDistancePoint(java.util.List&lt;<a href="../../../../../com/amap/api/maps/model/LatLng.html" title="com.amap.api.maps.model中的类">LatLng</a>&gt;&nbsp;points,
                                                                                   <a href="../../../../../com/amap/api/maps/model/LatLng.html" title="com.amap.api.maps.model中的类">LatLng</a>&nbsp;point)</pre>
<div class="block">计算一个点在线上的垂足，如果垂足在线上的某一顶点，则直接返回顶点的下标</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>points</code> - 坐标数组</dd>
<dd><code>point</code> - 坐标点</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>4.1.3</dd>
</dl>
</li>
</ul>
<a name="calShortestDistancePoint-java.util.List-DPoint-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>calShortestDistancePoint</h4>
<pre>public static&nbsp;android.util.Pair&lt;java.lang.Integer,DPoint&gt;&nbsp;calShortestDistancePoint(java.util.List&lt;DPoint&gt;&nbsp;points,
                                                                                   DPoint&nbsp;point)</pre>
<div class="block">计算一个点在线上的垂足，如果垂足在线上的某一顶点，则直接返回顶点的下标</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>points</code> - DPoint 数组</dd>
<dd><code>point</code> - </dd>
<dt><span class="returnLabel">返回:</span></dt>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>4.1.3</dd>
</dl>
</li>
</ul>
<a name="calShortestDistancePoint-java.util.List-DPoint-float-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>calShortestDistancePoint</h4>
<pre>public static&nbsp;android.util.Pair&lt;java.lang.Integer,DPoint&gt;&nbsp;calShortestDistancePoint(java.util.List&lt;DPoint&gt;&nbsp;points,
                                                                                   DPoint&nbsp;point,
                                                                                   float&nbsp;bearing)</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/SpatialRelationUtil.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个类</li>
<li>下一个类</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/maps/utils/SpatialRelationUtil.html" target="_top">框架</a></li>
<li><a href="SpatialRelationUtil.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li><a href="#field.summary">字段</a>&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li><a href="#field.detail">字段</a>&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
