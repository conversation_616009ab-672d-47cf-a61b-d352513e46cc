<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>AMapUtils</title>
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="AMapUtils";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":9,"i3":9,"i4":9,"i5":9,"i6":9,"i7":9,"i8":9};
var tabs = {65535:["t0","所有方法"],1:["t1","静态方法"],8:["t4","具体方法"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/AMapUtils.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/amap/api/maps/AMapOptions.html" title="com.amap.api.maps中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../com/amap/api/maps/CameraUpdate.html" title="com.amap.api.maps中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/amap/api/maps/AMapUtils.html" target="_top">框架</a></li>
<li><a href="AMapUtils.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li><a href="#field.summary">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li><a href="#field.detail">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.amap.api.maps</div>
<h2 title="类 AMapUtils" class="title">类 AMapUtils</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.amap.api.maps.AMapUtils</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">AMapUtils</span>
extends java.lang.Object</pre>
<div class="block">AMap辅助工具类，包含计算距离、面积、调起高德地图APP进行导航等系列功能。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.2</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>字段概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="字段概要表, 列表字段和解释">
<caption><span>字段</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">字段和说明</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMapUtils.html#BUS_COMFORT">BUS_COMFORT</a></span></code>
<div class="block">公交路径规划策略：（最舒适 : 4）</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMapUtils.html#BUS_MONEY_LITTLE">BUS_MONEY_LITTLE</a></span></code>
<div class="block">公交路径规划策略：（费用优先 : 1）</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMapUtils.html#BUS_NO_SUBWAY">BUS_NO_SUBWAY</a></span></code>
<div class="block">公交路径规划策略：（不乘地铁 : 5）</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMapUtils.html#BUS_TIME_FIRST">BUS_TIME_FIRST</a></span></code>
<div class="block">公交路径规划策略：（最快捷 : 0）</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMapUtils.html#BUS_TRANSFER_LITTLE">BUS_TRANSFER_LITTLE</a></span></code>
<div class="block">公交路径规划策略：（最少换乘 : 2）</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMapUtils.html#BUS_WALK_LITTLE">BUS_WALK_LITTLE</a></span></code>
<div class="block">公交路径规划策略：（最少步行 : 3）</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMapUtils.html#DRIVING_AVOID_CONGESTION">DRIVING_AVOID_CONGESTION</a></span></code>
<div class="block">驾车路径规划策略：（避免拥堵 : 4）</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMapUtils.html#DRIVING_DEFAULT">DRIVING_DEFAULT</a></span></code>
<div class="block">驾车路径规划策略：（速度优先 : 0）</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMapUtils.html#DRIVING_NO_HIGHWAY">DRIVING_NO_HIGHWAY</a></span></code>
<div class="block">驾车路径规划策略：（不走高速 : 3）</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMapUtils.html#DRIVING_NO_HIGHWAY_AVOID_CONGESTION">DRIVING_NO_HIGHWAY_AVOID_CONGESTION</a></span></code>
<div class="block">驾车路径规划策略：（不走高速且躲避拥堵 : 6）</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMapUtils.html#DRIVING_NO_HIGHWAY_AVOID_SHORT_MONEY">DRIVING_NO_HIGHWAY_AVOID_SHORT_MONEY</a></span></code>
<div class="block">驾车路径规划策略：（不走高速且避免收费 : 5）</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMapUtils.html#DRIVING_NO_HIGHWAY_SAVE_MONEY_AVOID_CONGESTION">DRIVING_NO_HIGHWAY_SAVE_MONEY_AVOID_CONGESTION</a></span></code>
<div class="block">驾车路径规划策略：（不走高速躲避收费和拥堵 : 8）</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMapUtils.html#DRIVING_SAVE_MONEY">DRIVING_SAVE_MONEY</a></span></code>
<div class="block">驾车路径规划策略：（费用优先 : 1）</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMapUtils.html#DRIVING_SAVE_MONEY_AVOID_CONGESTION">DRIVING_SAVE_MONEY_AVOID_CONGESTION</a></span></code>
<div class="block">驾车路径规划策略：（躲避收费和拥堵 : 7）</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMapUtils.html#DRIVING_SHORT_DISTANCE">DRIVING_SHORT_DISTANCE</a></span></code>
<div class="block">驾车路径规划策略：（距离优先 : 2）</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMapUtils.html#AMapUtils--">AMapUtils</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>所有方法</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">静态方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">具体方法</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMapUtils.html#calculateArea-com.amap.api.maps.model.LatLng-com.amap.api.maps.model.LatLng-">calculateArea</a></span>(<a href="../../../../com/amap/api/maps/model/LatLng.html" title="com.amap.api.maps.model中的类">LatLng</a>&nbsp;leftTopLatlng,
             <a href="../../../../com/amap/api/maps/model/LatLng.html" title="com.amap.api.maps.model中的类">LatLng</a>&nbsp;rightBottomLatlng)</code>
<div class="block">计算地图上矩形区域的面积，单位平方米。</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMapUtils.html#calculateArea-java.util.List-">calculateArea</a></span>(java.util.List&lt;<a href="../../../../com/amap/api/maps/model/LatLng.html" title="com.amap.api.maps.model中的类">LatLng</a>&gt;&nbsp;points)</code>
<div class="block">计算多边形的面积，单位平方米。</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMapUtils.html#calculateLineDistance-com.amap.api.maps.model.LatLng-com.amap.api.maps.model.LatLng-">calculateLineDistance</a></span>(<a href="../../../../com/amap/api/maps/model/LatLng.html" title="com.amap.api.maps.model中的类">LatLng</a>&nbsp;startLatlng,
                     <a href="../../../../com/amap/api/maps/model/LatLng.html" title="com.amap.api.maps.model中的类">LatLng</a>&nbsp;endLatlng)</code>
<div class="block">根据用户的起点和终点经纬度计算两点间距离，单位米。</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMapUtils.html#getLatestAMapApp-android.content.Context-">getLatestAMapApp</a></span>(android.content.Context&nbsp;context)</code>
<div class="block">跳转到高德地图APP最新版本下载页面</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMapUtils.html#openAMapDrivingRoute-com.amap.api.maps.model.RoutePara-android.content.Context-">openAMapDrivingRoute</a></span>(<a href="../../../../com/amap/api/maps/model/RoutePara.html" title="com.amap.api.maps.model中的类">RoutePara</a>&nbsp;para,
                    android.content.Context&nbsp;context)</code>
<div class="block">调起高德地图APP进行驾车路线规划。</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMapUtils.html#openAMapNavi-com.amap.api.maps.model.NaviPara-android.content.Context-">openAMapNavi</a></span>(<a href="../../../../com/amap/api/maps/model/NaviPara.html" title="com.amap.api.maps.model中的类">NaviPara</a>&nbsp;para,
            android.content.Context&nbsp;context)</code>
<div class="block">调起高德地图APP进行导航。</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMapUtils.html#openAMapPoiNearbySearch-com.amap.api.maps.model.PoiPara-android.content.Context-">openAMapPoiNearbySearch</a></span>(<a href="../../../../com/amap/api/maps/model/PoiPara.html" title="com.amap.api.maps.model中的类">PoiPara</a>&nbsp;para,
                       android.content.Context&nbsp;context)</code>
<div class="block">调起高德地图APP进行poi周边检索。</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMapUtils.html#openAMapTransitRoute-com.amap.api.maps.model.RoutePara-android.content.Context-">openAMapTransitRoute</a></span>(<a href="../../../../com/amap/api/maps/model/RoutePara.html" title="com.amap.api.maps.model中的类">RoutePara</a>&nbsp;para,
                    android.content.Context&nbsp;context)</code>
<div class="block">调起高德地图APP进行公交路线规划。</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMapUtils.html#openAMapWalkingRoute-com.amap.api.maps.model.RoutePara-android.content.Context-">openAMapWalkingRoute</a></span>(<a href="../../../../com/amap/api/maps/model/RoutePara.html" title="com.amap.api.maps.model中的类">RoutePara</a>&nbsp;para,
                    android.content.Context&nbsp;context)</code>
<div class="block">调起高德地图APP进行步行路线规划。</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>字段详细资料</h3>
<a name="DRIVING_DEFAULT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DRIVING_DEFAULT</h4>
<pre>public static final&nbsp;int DRIVING_DEFAULT</pre>
<div class="block">驾车路径规划策略：（速度优先 : 0）</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.4.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.maps.AMapUtils.DRIVING_DEFAULT">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="DRIVING_SAVE_MONEY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DRIVING_SAVE_MONEY</h4>
<pre>public static final&nbsp;int DRIVING_SAVE_MONEY</pre>
<div class="block">驾车路径规划策略：（费用优先 : 1）</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.4.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.maps.AMapUtils.DRIVING_SAVE_MONEY">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="DRIVING_SHORT_DISTANCE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DRIVING_SHORT_DISTANCE</h4>
<pre>public static final&nbsp;int DRIVING_SHORT_DISTANCE</pre>
<div class="block">驾车路径规划策略：（距离优先 : 2）</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.4.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.maps.AMapUtils.DRIVING_SHORT_DISTANCE">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="DRIVING_NO_HIGHWAY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DRIVING_NO_HIGHWAY</h4>
<pre>public static final&nbsp;int DRIVING_NO_HIGHWAY</pre>
<div class="block">驾车路径规划策略：（不走高速 : 3）</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.4.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.maps.AMapUtils.DRIVING_NO_HIGHWAY">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="DRIVING_AVOID_CONGESTION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DRIVING_AVOID_CONGESTION</h4>
<pre>public static final&nbsp;int DRIVING_AVOID_CONGESTION</pre>
<div class="block">驾车路径规划策略：（避免拥堵 : 4）</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.4.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.maps.AMapUtils.DRIVING_AVOID_CONGESTION">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="DRIVING_NO_HIGHWAY_AVOID_SHORT_MONEY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DRIVING_NO_HIGHWAY_AVOID_SHORT_MONEY</h4>
<pre>public static final&nbsp;int DRIVING_NO_HIGHWAY_AVOID_SHORT_MONEY</pre>
<div class="block">驾车路径规划策略：（不走高速且避免收费 : 5）</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.4.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.maps.AMapUtils.DRIVING_NO_HIGHWAY_AVOID_SHORT_MONEY">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="DRIVING_NO_HIGHWAY_AVOID_CONGESTION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DRIVING_NO_HIGHWAY_AVOID_CONGESTION</h4>
<pre>public static final&nbsp;int DRIVING_NO_HIGHWAY_AVOID_CONGESTION</pre>
<div class="block">驾车路径规划策略：（不走高速且躲避拥堵 : 6）</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.4.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.maps.AMapUtils.DRIVING_NO_HIGHWAY_AVOID_CONGESTION">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="DRIVING_SAVE_MONEY_AVOID_CONGESTION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DRIVING_SAVE_MONEY_AVOID_CONGESTION</h4>
<pre>public static final&nbsp;int DRIVING_SAVE_MONEY_AVOID_CONGESTION</pre>
<div class="block">驾车路径规划策略：（躲避收费和拥堵 : 7）</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.4.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.maps.AMapUtils.DRIVING_SAVE_MONEY_AVOID_CONGESTION">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="DRIVING_NO_HIGHWAY_SAVE_MONEY_AVOID_CONGESTION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DRIVING_NO_HIGHWAY_SAVE_MONEY_AVOID_CONGESTION</h4>
<pre>public static final&nbsp;int DRIVING_NO_HIGHWAY_SAVE_MONEY_AVOID_CONGESTION</pre>
<div class="block">驾车路径规划策略：（不走高速躲避收费和拥堵 : 8）</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.4.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.maps.AMapUtils.DRIVING_NO_HIGHWAY_SAVE_MONEY_AVOID_CONGESTION">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="BUS_TIME_FIRST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BUS_TIME_FIRST</h4>
<pre>public static final&nbsp;int BUS_TIME_FIRST</pre>
<div class="block">公交路径规划策略：（最快捷 : 0）</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.4.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.maps.AMapUtils.BUS_TIME_FIRST">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="BUS_MONEY_LITTLE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BUS_MONEY_LITTLE</h4>
<pre>public static final&nbsp;int BUS_MONEY_LITTLE</pre>
<div class="block">公交路径规划策略：（费用优先 : 1）</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.4.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.maps.AMapUtils.BUS_MONEY_LITTLE">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="BUS_TRANSFER_LITTLE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BUS_TRANSFER_LITTLE</h4>
<pre>public static final&nbsp;int BUS_TRANSFER_LITTLE</pre>
<div class="block">公交路径规划策略：（最少换乘 : 2）</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.4.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.maps.AMapUtils.BUS_TRANSFER_LITTLE">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="BUS_WALK_LITTLE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BUS_WALK_LITTLE</h4>
<pre>public static final&nbsp;int BUS_WALK_LITTLE</pre>
<div class="block">公交路径规划策略：（最少步行 : 3）</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.4.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.maps.AMapUtils.BUS_WALK_LITTLE">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="BUS_COMFORT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BUS_COMFORT</h4>
<pre>public static final&nbsp;int BUS_COMFORT</pre>
<div class="block">公交路径规划策略：（最舒适 : 4）</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.4.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.maps.AMapUtils.BUS_COMFORT">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="BUS_NO_SUBWAY">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>BUS_NO_SUBWAY</h4>
<pre>public static final&nbsp;int BUS_NO_SUBWAY</pre>
<div class="block">公交路径规划策略：（不乘地铁 : 5）</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.4.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.maps.AMapUtils.BUS_NO_SUBWAY">常量字段值</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="AMapUtils--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>AMapUtils</h4>
<pre>public&nbsp;AMapUtils()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="calculateLineDistance-com.amap.api.maps.model.LatLng-com.amap.api.maps.model.LatLng-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>calculateLineDistance</h4>
<pre>public static&nbsp;float&nbsp;calculateLineDistance(<a href="../../../../com/amap/api/maps/model/LatLng.html" title="com.amap.api.maps.model中的类">LatLng</a>&nbsp;startLatlng,
                                          <a href="../../../../com/amap/api/maps/model/LatLng.html" title="com.amap.api.maps.model中的类">LatLng</a>&nbsp;endLatlng)</pre>
<div class="block">根据用户的起点和终点经纬度计算两点间距离，单位米。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>startLatlng</code> - 起点的坐标。</dd>
<dd><code>endLatlng</code> - 终点的坐标。</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>返回两点间的距离，单位米。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.2</dd>
</dl>
</li>
</ul>
<a name="calculateArea-com.amap.api.maps.model.LatLng-com.amap.api.maps.model.LatLng-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>calculateArea</h4>
<pre>public static&nbsp;float&nbsp;calculateArea(<a href="../../../../com/amap/api/maps/model/LatLng.html" title="com.amap.api.maps.model中的类">LatLng</a>&nbsp;leftTopLatlng,
                                  <a href="../../../../com/amap/api/maps/model/LatLng.html" title="com.amap.api.maps.model中的类">LatLng</a>&nbsp;rightBottomLatlng)</pre>
<div class="block">计算地图上矩形区域的面积，单位平方米。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>leftTopLatlng</code> - 矩形区域左上角坐标。</dd>
<dd><code>rightBottomLatlng</code> - 矩形区域右下角坐标。</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>返回地图上矩形区域的面积，单位平方米。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.2</dd>
</dl>
</li>
</ul>
<a name="calculateArea-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>calculateArea</h4>
<pre>public static&nbsp;float&nbsp;calculateArea(java.util.List&lt;<a href="../../../../com/amap/api/maps/model/LatLng.html" title="com.amap.api.maps.model中的类">LatLng</a>&gt;&nbsp;points)</pre>
<div class="block">计算多边形的面积，单位平方米。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>points</code> - 多边形顶点坐标。</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>返回多边形的面积，单位平方米。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.0.0</dd>
</dl>
</li>
</ul>
<a name="getLatestAMapApp-android.content.Context-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLatestAMapApp</h4>
<pre>public static&nbsp;void&nbsp;getLatestAMapApp(android.content.Context&nbsp;context)</pre>
<div class="block">跳转到高德地图APP最新版本下载页面</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>context</code> - 上下文</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.4.1</dd>
</dl>
</li>
</ul>
<a name="openAMapNavi-com.amap.api.maps.model.NaviPara-android.content.Context-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>openAMapNavi</h4>
<pre>public static&nbsp;void&nbsp;openAMapNavi(<a href="../../../../com/amap/api/maps/model/NaviPara.html" title="com.amap.api.maps.model中的类">NaviPara</a>&nbsp;para,
                                android.content.Context&nbsp;context)
                         throws <a href="../../../../com/amap/api/maps/AMapException.html" title="com.amap.api.maps中的类">AMapException</a></pre>
<div class="block">调起高德地图APP进行导航。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>para</code> - 导航需要的参数。</dd>
<dd><code>context</code> - 上下文</dd>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code><a href="../../../../com/amap/api/maps/AMapException.html" title="com.amap.api.maps中的类">AMapException</a></code></dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.4.1</dd>
</dl>
</li>
</ul>
<a name="openAMapPoiNearbySearch-com.amap.api.maps.model.PoiPara-android.content.Context-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>openAMapPoiNearbySearch</h4>
<pre>public static&nbsp;void&nbsp;openAMapPoiNearbySearch(<a href="../../../../com/amap/api/maps/model/PoiPara.html" title="com.amap.api.maps.model中的类">PoiPara</a>&nbsp;para,
                                           android.content.Context&nbsp;context)
                                    throws <a href="../../../../com/amap/api/maps/AMapException.html" title="com.amap.api.maps中的类">AMapException</a></pre>
<div class="block">调起高德地图APP进行poi周边检索。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>para</code> - poi周边检索需要的参数。</dd>
<dd><code>context</code> - 上下文</dd>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code><a href="../../../../com/amap/api/maps/AMapException.html" title="com.amap.api.maps中的类">AMapException</a></code></dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.5.0</dd>
</dl>
</li>
</ul>
<a name="openAMapDrivingRoute-com.amap.api.maps.model.RoutePara-android.content.Context-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>openAMapDrivingRoute</h4>
<pre>public static&nbsp;void&nbsp;openAMapDrivingRoute(<a href="../../../../com/amap/api/maps/model/RoutePara.html" title="com.amap.api.maps.model中的类">RoutePara</a>&nbsp;para,
                                        android.content.Context&nbsp;context)
                                 throws <a href="../../../../com/amap/api/maps/AMapException.html" title="com.amap.api.maps中的类">AMapException</a></pre>
<div class="block">调起高德地图APP进行驾车路线规划。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>para</code> - 驾车路线检索需要的参数。</dd>
<dd><code>context</code> - 上下文</dd>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code><a href="../../../../com/amap/api/maps/AMapException.html" title="com.amap.api.maps中的类">AMapException</a></code></dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.5.0</dd>
</dl>
</li>
</ul>
<a name="openAMapTransitRoute-com.amap.api.maps.model.RoutePara-android.content.Context-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>openAMapTransitRoute</h4>
<pre>public static&nbsp;void&nbsp;openAMapTransitRoute(<a href="../../../../com/amap/api/maps/model/RoutePara.html" title="com.amap.api.maps.model中的类">RoutePara</a>&nbsp;para,
                                        android.content.Context&nbsp;context)
                                 throws <a href="../../../../com/amap/api/maps/AMapException.html" title="com.amap.api.maps中的类">AMapException</a></pre>
<div class="block">调起高德地图APP进行公交路线规划。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>para</code> - 公交路线检索需要的参数。</dd>
<dd><code>context</code> - 上下文</dd>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code><a href="../../../../com/amap/api/maps/AMapException.html" title="com.amap.api.maps中的类">AMapException</a></code></dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.5.0</dd>
</dl>
</li>
</ul>
<a name="openAMapWalkingRoute-com.amap.api.maps.model.RoutePara-android.content.Context-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>openAMapWalkingRoute</h4>
<pre>public static&nbsp;void&nbsp;openAMapWalkingRoute(<a href="../../../../com/amap/api/maps/model/RoutePara.html" title="com.amap.api.maps.model中的类">RoutePara</a>&nbsp;para,
                                        android.content.Context&nbsp;context)
                                 throws <a href="../../../../com/amap/api/maps/AMapException.html" title="com.amap.api.maps中的类">AMapException</a></pre>
<div class="block">调起高德地图APP进行步行路线规划。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>para</code> - 规划参数</dd>
<dd><code>context</code> - 上下文</dd>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code><a href="../../../../com/amap/api/maps/AMapException.html" title="com.amap.api.maps中的类">AMapException</a></code></dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.5.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/AMapUtils.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/amap/api/maps/AMapOptions.html" title="com.amap.api.maps中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../com/amap/api/maps/CameraUpdate.html" title="com.amap.api.maps中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/amap/api/maps/AMapUtils.html" target="_top">框架</a></li>
<li><a href="AMapUtils.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li><a href="#field.summary">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li><a href="#field.detail">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
