<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>SupportMapFragment</title>
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="SupportMapFragment";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":9,"i2":9,"i3":10};
var tabs = {65535:["t0","所有方法"],1:["t1","静态方法"],2:["t2","实例方法"],8:["t4","具体方法"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/SupportMapFragment.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/amap/api/maps/Projection.html" title="com.amap.api.maps中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../com/amap/api/maps/SwipeDismissTouchListener.DismissCallbacks.html" title="com.amap.api.maps中的接口"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/amap/api/maps/SupportMapFragment.html" target="_top">框架</a></li>
<li><a href="SupportMapFragment.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.amap.api.maps</div>
<h2 title="类 SupportMapFragment" class="title">类 SupportMapFragment</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>android.support.v4.app.Fragment</li>
<li>
<ul class="inheritance">
<li>com.amap.api.maps.SupportMapFragment</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">SupportMapFragment</span>
extends android.support.v4.app.Fragment</pre>
<div class="block">SupportMapFragment 类, 管理地图生命周期。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>所有方法</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">静态方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">实例方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">具体方法</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../../../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/SupportMapFragment.html#getMap--">getMap</a></span>()</code>
<div class="block">获取地图控制器AMap 对象。</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/amap/api/maps/SupportMapFragment.html" title="com.amap.api.maps中的类">SupportMapFragment</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/SupportMapFragment.html#newInstance--">newInstance</a></span>()</code>
<div class="block">使用默认的选项创建SupportMapFragment。</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/amap/api/maps/SupportMapFragment.html" title="com.amap.api.maps中的类">SupportMapFragment</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/SupportMapFragment.html#newInstance-com.amap.api.maps.AMapOptions-">newInstance</a></span>(<a href="../../../../com/amap/api/maps/AMapOptions.html" title="com.amap.api.maps中的类">AMapOptions</a>&nbsp;options)</code>
<div class="block">根据用户传入的AMapOptions 创建SupportMapFragment。</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/SupportMapFragment.html#setUserVisibleHint-boolean-">setUserVisibleHint</a></span>(boolean&nbsp;isVisibleToUser)</code>
<div class="block">设置是否显示，在fragment切换的时候可以使用，或者想隐藏MapFragment的时候可以使用</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="newInstance--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>newInstance</h4>
<pre>public static&nbsp;<a href="../../../../com/amap/api/maps/SupportMapFragment.html" title="com.amap.api.maps中的类">SupportMapFragment</a>&nbsp;newInstance()</pre>
<div class="block">使用默认的选项创建SupportMapFragment。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>新创建的SupportMapFragment对象。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="newInstance-com.amap.api.maps.AMapOptions-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>newInstance</h4>
<pre>public static&nbsp;<a href="../../../../com/amap/api/maps/SupportMapFragment.html" title="com.amap.api.maps中的类">SupportMapFragment</a>&nbsp;newInstance(<a href="../../../../com/amap/api/maps/AMapOptions.html" title="com.amap.api.maps中的类">AMapOptions</a>&nbsp;options)</pre>
<div class="block">根据用户传入的AMapOptions 创建SupportMapFragment。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>options</code> - 地图初始化的options对象。</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>一个SupportMapFragment对象。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="getMap--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMap</h4>
<pre>public&nbsp;<a href="../../../../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a>&nbsp;getMap()</pre>
<div class="block">获取地图控制器AMap 对象。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>AMap 对象。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="setUserVisibleHint-boolean-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setUserVisibleHint</h4>
<pre>public&nbsp;void&nbsp;setUserVisibleHint(boolean&nbsp;isVisibleToUser)</pre>
<div class="block">设置是否显示，在fragment切换的时候可以使用，或者想隐藏MapFragment的时候可以使用</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>isVisibleToUser</code> - true：显示； false：不显示；</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.4.1</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/SupportMapFragment.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/amap/api/maps/Projection.html" title="com.amap.api.maps中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../com/amap/api/maps/SwipeDismissTouchListener.DismissCallbacks.html" title="com.amap.api.maps中的接口"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/amap/api/maps/SupportMapFragment.html" target="_top">框架</a></li>
<li><a href="SupportMapFragment.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
