<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>AMap</title>
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="AMap";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":42,"i26":10,"i27":10,"i28":10,"i29":10,"i30":10,"i31":10,"i32":10,"i33":10,"i34":10,"i35":42,"i36":10,"i37":10,"i38":10,"i39":10,"i40":10,"i41":41,"i42":10,"i43":10,"i44":10,"i45":10,"i46":10,"i47":10,"i48":10,"i49":10,"i50":10,"i51":10,"i52":10,"i53":10,"i54":10,"i55":10,"i56":42,"i57":42,"i58":10,"i59":10,"i60":10,"i61":10,"i62":10,"i63":42,"i64":10,"i65":10,"i66":10,"i67":10,"i68":10,"i69":10,"i70":42,"i71":10,"i72":42,"i73":42,"i74":10,"i75":10,"i76":10,"i77":10,"i78":10,"i79":10,"i80":10,"i81":10,"i82":10,"i83":10,"i84":10,"i85":10,"i86":10,"i87":10,"i88":10,"i89":10,"i90":10,"i91":10,"i92":10,"i93":10,"i94":10};
var tabs = {65535:["t0","所有方法"],1:["t1","静态方法"],2:["t2","实例方法"],8:["t4","具体方法"],32:["t6","已过时的方法"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/AMap.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个类</li>
<li><a href="../../../../com/amap/api/maps/AMap.AMapAppResourceRequestListener.html" title="com.amap.api.maps中的接口"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/amap/api/maps/AMap.html" target="_top">框架</a></li>
<li><a href="AMap.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li><a href="#nested.class.summary">嵌套</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">字段</a>&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li><a href="#field.detail">字段</a>&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.amap.api.maps</div>
<h2 title="类 AMap" class="title">类 AMap</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.amap.api.maps.AMap</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public final class <span class="typeNameLabel">AMap</span>
extends java.lang.Object</pre>
<div class="block">定义AMap 地图对象的操作方法与接口。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>嵌套类概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="嵌套类概要表, 列表嵌套类和解释">
<caption><span>嵌套类</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">类和说明</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static interface&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.AMapAppResourceRequestListener.html" title="com.amap.api.maps中的接口">AMap.AMapAppResourceRequestListener</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static interface&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.CancelableCallback.html" title="com.amap.api.maps中的接口">AMap.CancelableCallback</a></span></code>
<div class="block">在<a href="../../../../com/amap/api/maps/AMap.html#animateCamera-com.amap.api.maps.CameraUpdate-com.amap.api.maps.AMap.CancelableCallback-"><code>AMap.animateCamera(CameraUpdate, CancelableCallback)</code></a>设置一个CancelableCallback，用来监听该CameraUpdate是否执行完成或者被中断。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static interface&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.ImageInfoWindowAdapter.html" title="com.amap.api.maps中的接口">AMap.ImageInfoWindowAdapter</a></span></code>
<div class="block">用来实现marker与对应InfoWindow同步移动<br>
 默认情况下，InfoWindow是一个View， 拖动地图的时候由于View 布局较慢，会有延迟的效果。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static interface&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.InfoWindowAdapter.html" title="com.amap.api.maps中的接口">AMap.InfoWindowAdapter</a></span></code>
<div class="block">用来定制marker的信息窗口<br>
 默认情况下，当单击某个marker时，如果该marker的Title和Snippet不为空，则会触发getInfoWindow和getInfoContents回调。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static interface&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.OnCacheRemoveListener.html" title="com.amap.api.maps中的接口">AMap.OnCacheRemoveListener</a></span></code>
<div class="block">缓存数据清除监听接口</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static interface&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.OnCameraChangeListener.html" title="com.amap.api.maps中的接口">AMap.OnCameraChangeListener</a></span></code>
<div class="block">地图状态发生变化的监听接口。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static interface&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.OnIndoorBuildingActiveListener.html" title="com.amap.api.maps中的接口">AMap.OnIndoorBuildingActiveListener</a></span></code>
<div class="block">室内地图状态监听接口。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static interface&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.OnInfoWindowClickListener.html" title="com.amap.api.maps中的接口">AMap.OnInfoWindowClickListener</a></span></code>
<div class="block">marker的信息窗口点击事件监听接口。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static interface&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.OnMapClickListener.html" title="com.amap.api.maps中的接口">AMap.OnMapClickListener</a></span></code>
<div class="block">地图点击事件监听接口。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static interface&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.OnMapLoadedListener.html" title="com.amap.api.maps中的接口">AMap.OnMapLoadedListener</a></span></code>
<div class="block">地图加载完成监听接口</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static interface&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.OnMapLongClickListener.html" title="com.amap.api.maps中的接口">AMap.OnMapLongClickListener</a></span></code>
<div class="block">地图长按事件监听接口。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static interface&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.onMapPrintScreenListener.html" title="com.amap.api.maps中的接口">AMap.onMapPrintScreenListener</a></span></code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;
<div class="block"><span class="deprecationComment">建议使用 <a href="../../../../com/amap/api/maps/AMap.OnMapScreenShotListener.html" title="com.amap.api.maps中的接口"><code>AMap.OnMapScreenShotListener</code></a></span></div>
</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static interface&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.OnMapScreenShotListener.html" title="com.amap.api.maps中的接口">AMap.OnMapScreenShotListener</a></span></code>
<div class="block">地图截屏监听接口 。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static interface&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.OnMapSnapshotListener.html" title="com.amap.api.maps中的接口">AMap.OnMapSnapshotListener</a></span></code>
<div class="block">地图区域图异步返回接口 。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static interface&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.OnMapTouchListener.html" title="com.amap.api.maps中的接口">AMap.OnMapTouchListener</a></span></code>
<div class="block">地图触摸事件监听接口。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static interface&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.OnMarkerClickListener.html" title="com.amap.api.maps中的接口">AMap.OnMarkerClickListener</a></span></code>
<div class="block">marker点击事件监听接口。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static interface&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.OnMarkerDragListener.html" title="com.amap.api.maps中的接口">AMap.OnMarkerDragListener</a></span></code>
<div class="block">marker拖动事件监听接口。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static interface&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.OnMultiPointClickListener.html" title="com.amap.api.maps中的接口">AMap.OnMultiPointClickListener</a></span></code>
<div class="block">海量点中某一点被点击时的回调。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static interface&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.OnMyLocationChangeListener.html" title="com.amap.api.maps中的接口">AMap.OnMyLocationChangeListener</a></span></code>
<div class="block">用户定位信息监听接口。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static interface&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.OnPOIClickListener.html" title="com.amap.api.maps中的接口">AMap.OnPOIClickListener</a></span></code>
<div class="block">地图底图poi点击事件监听接口。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static interface&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.OnPolylineClickListener.html" title="com.amap.api.maps中的接口">AMap.OnPolylineClickListener</a></span></code>
<div class="block">polyline点击事件监听接口。</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>字段概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="字段概要表, 列表字段和解释">
<caption><span>字段</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">字段和说明</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#LOCATION_TYPE_LOCATE">LOCATION_TYPE_LOCATE</a></span></code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;
<div class="block"><span class="deprecationComment">5.0.0 之后请参考 <a href="../../../../com/amap/api/maps/model/MyLocationStyle.html#LOCATION_TYPE_LOCATE"><code>MyLocationStyle.LOCATION_TYPE_LOCATE</code></a></span></div>
</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#LOCATION_TYPE_MAP_FOLLOW">LOCATION_TYPE_MAP_FOLLOW</a></span></code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;
<div class="block"><span class="deprecationComment">5.0.0 之后请参考 <a href="../../../../com/amap/api/maps/model/MyLocationStyle.html#LOCATION_TYPE_FOLLOW"><code>MyLocationStyle.LOCATION_TYPE_FOLLOW</code></a></span></div>
</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#LOCATION_TYPE_MAP_ROTATE">LOCATION_TYPE_MAP_ROTATE</a></span></code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;
<div class="block"><span class="deprecationComment">5.0.0 之后请参考 <a href="../../../../com/amap/api/maps/model/MyLocationStyle.html#LOCATION_TYPE_MAP_ROTATE"><code>MyLocationStyle.LOCATION_TYPE_MAP_ROTATE</code></a></span></div>
</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#MAP_TYPE_BUS">MAP_TYPE_BUS</a></span></code>
<div class="block">公交模式常量。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#MAP_TYPE_NAVI">MAP_TYPE_NAVI</a></span></code>
<div class="block">导航模式常量。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#MAP_TYPE_NIGHT">MAP_TYPE_NIGHT</a></span></code>
<div class="block">夜景图模式常量。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#MAP_TYPE_NORMAL">MAP_TYPE_NORMAL</a></span></code>
<div class="block">普通地图模式常量。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#MAP_TYPE_SATELLITE">MAP_TYPE_SATELLITE</a></span></code>
<div class="block">卫星图模式常量。</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>所有方法</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">静态方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">实例方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">具体方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t6" class="tableTab"><span><a href="javascript:show(32);">已过时的方法</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#addAMapAppResourceListener-com.amap.api.maps.AMap.AMapAppResourceRequestListener-">addAMapAppResourceListener</a></span>(<a href="../../../../com/amap/api/maps/AMap.AMapAppResourceRequestListener.html" title="com.amap.api.maps中的接口">AMap.AMapAppResourceRequestListener</a>&nbsp;listen)</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/amap/api/maps/model/Arc.html" title="com.amap.api.maps.model中的类">Arc</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#addArc-com.amap.api.maps.model.ArcOptions-">addArc</a></span>(<a href="../../../../com/amap/api/maps/model/ArcOptions.html" title="com.amap.api.maps.model中的类">ArcOptions</a>&nbsp;options)</code>
<div class="block">在地图上添加一个圆弧（arc）对象。</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../../../com/amap/api/maps/model/BuildingOverlay.html" title="com.amap.api.maps.model中的类">BuildingOverlay</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#addBuildingOverlay--">addBuildingOverlay</a></span>()</code>
<div class="block">添加建筑物图层，默认图层的区域为全世界。</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/amap/api/maps/model/Circle.html" title="com.amap.api.maps.model中的类">Circle</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#addCircle-com.amap.api.maps.model.CircleOptions-">addCircle</a></span>(<a href="../../../../com/amap/api/maps/model/CircleOptions.html" title="com.amap.api.maps.model中的类">CircleOptions</a>&nbsp;options)</code>
<div class="block">在地图上添加一个圆（circle）对象。</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../../../../com/amap/api/maps/model/GLTFOverlay.html" title="com.amap.api.maps.model中的类">GLTFOverlay</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#addGLTFOverlay-com.amap.api.maps.model.GLTFOverlayOptions-">addGLTFOverlay</a></span>(<a href="../../../../com/amap/api/maps/model/GLTFOverlayOptions.html" title="com.amap.api.maps.model中的类">GLTFOverlayOptions</a>&nbsp;options)</code>
<div class="block">在地图上添加一个gltf对象。</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/amap/api/maps/model/GroundOverlay.html" title="com.amap.api.maps.model中的类">GroundOverlay</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#addGroundOverlay-com.amap.api.maps.model.GroundOverlayOptions-">addGroundOverlay</a></span>(<a href="../../../../com/amap/api/maps/model/GroundOverlayOptions.html" title="com.amap.api.maps.model中的类">GroundOverlayOptions</a>&nbsp;options)</code>
<div class="block">在地图上添加一个Ground覆盖物（groundOverlay）对象</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="../../../../com/amap/api/maps/model/HeatMapGridLayer.html" title="com.amap.api.maps.model中的类">HeatMapGridLayer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#addHeatMapGridLayer-com.amap.api.maps.model.HeatMapGridLayerOptions-">addHeatMapGridLayer</a></span>(<a href="../../../../com/amap/api/maps/model/HeatMapGridLayerOptions.html" title="com.amap.api.maps.model中的类">HeatMapGridLayerOptions</a>&nbsp;options)</code>
<div class="block">在地图上添加一个热力图网格对象。</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/amap/api/maps/model/HeatMapLayer.html" title="com.amap.api.maps.model中的类">HeatMapLayer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#addHeatMapLayer-com.amap.api.maps.model.HeatMapLayerOptions-">addHeatMapLayer</a></span>(<a href="../../../../com/amap/api/maps/model/HeatMapLayerOptions.html" title="com.amap.api.maps.model中的类">HeatMapLayerOptions</a>&nbsp;options)</code>
<div class="block">在地图上添加一个热力图对象。</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="../../../../com/amap/api/maps/model/Marker.html" title="com.amap.api.maps.model中的类">Marker</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#addMarker-com.amap.api.maps.model.MarkerOptions-">addMarker</a></span>(<a href="../../../../com/amap/api/maps/model/MarkerOptions.html" title="com.amap.api.maps.model中的类">MarkerOptions</a>&nbsp;options)</code>
<div class="block">在地图上添一个图片标记（marker）对象。</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>java.util.ArrayList&lt;<a href="../../../../com/amap/api/maps/model/Marker.html" title="com.amap.api.maps.model中的类">Marker</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#addMarkers-java.util.ArrayList-boolean-">addMarkers</a></span>(java.util.ArrayList&lt;<a href="../../../../com/amap/api/maps/model/MarkerOptions.html" title="com.amap.api.maps.model中的类">MarkerOptions</a>&gt;&nbsp;options,
          boolean&nbsp;moveToCenter)</code>
<div class="block">在地图上添一组图片标记（marker）对象，并设置是否改变地图状态以至于所有的marker对象都在当前地图可视区域范围内显示。</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code><a href="../../../../com/amap/api/maps/model/MultiPointOverlay.html" title="com.amap.api.maps.model中的类">MultiPointOverlay</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#addMultiPointOverlay-com.amap.api.maps.model.MultiPointOverlayOptions-">addMultiPointOverlay</a></span>(<a href="../../../../com/amap/api/maps/model/MultiPointOverlayOptions.html" title="com.amap.api.maps.model中的类">MultiPointOverlayOptions</a>&nbsp;options)</code>
<div class="block">在地图上添加一个海量点覆盖物（MultiPointOverlay）对象</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/amap/api/maps/model/MVTTileOverlay.html" title="com.amap.api.maps.model中的类">MVTTileOverlay</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#addMVTTileOverlay-com.amap.api.maps.model.MVTTileOverlayOptions-">addMVTTileOverlay</a></span>(<a href="../../../../com/amap/api/maps/model/MVTTileOverlayOptions.html" title="com.amap.api.maps.model中的类">MVTTileOverlayOptions</a>&nbsp;options)</code>
<div class="block">在地图上添加一个瓦片图层覆盖物（MVTtileOverlay）对象。</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code><a href="../../../../com/amap/api/maps/model/NavigateArrow.html" title="com.amap.api.maps.model中的类">NavigateArrow</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#addNavigateArrow-com.amap.api.maps.model.NavigateArrowOptions-">addNavigateArrow</a></span>(<a href="../../../../com/amap/api/maps/model/NavigateArrowOptions.html" title="com.amap.api.maps.model中的类">NavigateArrowOptions</a>&nbsp;options)</code>
<div class="block">在地图上添加一个导航指示箭头对象（navigateArrow）对象。</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/amap/api/maps/model/particle/ParticleOverlay.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlay</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#addParticleOverlay-com.amap.api.maps.model.particle.ParticleOverlayOptions-">addParticleOverlay</a></span>(<a href="../../../../com/amap/api/maps/model/particle/ParticleOverlayOptions.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlayOptions</a>&nbsp;options)</code>
<div class="block">在地图上添加一个粒子系统对象</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code><a href="../../../../com/amap/api/maps/model/Polygon.html" title="com.amap.api.maps.model中的类">Polygon</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#addPolygon-com.amap.api.maps.model.PolygonOptions-">addPolygon</a></span>(<a href="../../../../com/amap/api/maps/model/PolygonOptions.html" title="com.amap.api.maps.model中的类">PolygonOptions</a>&nbsp;options)</code>
<div class="block">在地图上添加一个多边形（polygon）对象。</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/amap/api/maps/model/Polyline.html" title="com.amap.api.maps.model中的类">Polyline</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#addPolyline-com.amap.api.maps.model.PolylineOptions-">addPolyline</a></span>(<a href="../../../../com/amap/api/maps/model/PolylineOptions.html" title="com.amap.api.maps.model中的类">PolylineOptions</a>&nbsp;options)</code>
<div class="block">在地图上添加一个折线对象（polyline）对象。</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code><a href="../../../../com/amap/api/maps/model/Text.html" title="com.amap.api.maps.model中的类">Text</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#addText-com.amap.api.maps.model.TextOptions-">addText</a></span>(<a href="../../../../com/amap/api/maps/model/TextOptions.html" title="com.amap.api.maps.model中的类">TextOptions</a>&nbsp;options)</code>
<div class="block">在地图上添一个文字标记（text）对象。</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/amap/api/maps/model/TileOverlay.html" title="com.amap.api.maps.model中的类">TileOverlay</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#addTileOverlay-com.amap.api.maps.model.TileOverlayOptions-">addTileOverlay</a></span>(<a href="../../../../com/amap/api/maps/model/TileOverlayOptions.html" title="com.amap.api.maps.model中的类">TileOverlayOptions</a>&nbsp;options)</code>
<div class="block">在地图上添加一个瓦片图层覆盖物（tileOverlay）对象。</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#animateCamera-com.amap.api.maps.CameraUpdate-">animateCamera</a></span>(<a href="../../../../com/amap/api/maps/CameraUpdate.html" title="com.amap.api.maps中的类">CameraUpdate</a>&nbsp;update)</code>
<div class="block">以动画方式按照传入的CameraUpdate参数更新地图状态，默认动画耗时250毫秒。</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#animateCamera-com.amap.api.maps.CameraUpdate-com.amap.api.maps.AMap.CancelableCallback-">animateCamera</a></span>(<a href="../../../../com/amap/api/maps/CameraUpdate.html" title="com.amap.api.maps中的类">CameraUpdate</a>&nbsp;update,
             <a href="../../../../com/amap/api/maps/AMap.CancelableCallback.html" title="com.amap.api.maps中的接口">AMap.CancelableCallback</a>&nbsp;cancelableCallback)</code>
<div class="block">以动画方式按照传入的CameraUpdate参数更新地图状态，默认动画耗时250毫秒，同时设置一个cancelableCallback来监听动画执行的结果。</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#animateCamera-com.amap.api.maps.CameraUpdate-long-com.amap.api.maps.AMap.CancelableCallback-">animateCamera</a></span>(<a href="../../../../com/amap/api/maps/CameraUpdate.html" title="com.amap.api.maps中的类">CameraUpdate</a>&nbsp;update,
             long&nbsp;durationMs,
             <a href="../../../../com/amap/api/maps/AMap.CancelableCallback.html" title="com.amap.api.maps中的接口">AMap.CancelableCallback</a>&nbsp;cancelableCallback)</code>
<div class="block">按照指定的动画时长及传入的CameraUpdate参数更新地图状态，，同时设置一个cancelableCallback来监听动画执行的结果。</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#clear--">clear</a></span>()</code>
<div class="block">从地图上删除所有的overlay（marker，circle，polyline 等对象）。</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#clear-boolean-">clear</a></span>(boolean&nbsp;isKeepMyLocationOverlay)</code>
<div class="block">从地图上删除所有的覆盖物（marker，circle，polyline 等对象），但myLocationOverlay（内置定位覆盖物）除外。</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/amap/api/maps/model/CameraPosition.html" title="com.amap.api.maps.model中的类">CameraPosition</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#getCameraPosition--">getCameraPosition</a></span>()</code>
<div class="block">获取地图的当前状态。</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#getMapContentApprovalNumber--">getMapContentApprovalNumber</a></span>()</code>
<div class="block">获取地图审图号（普通地图）<br>
 <font color="red">任何使用高德地图API调用地图服务的应用必须在其应用中对外透出审图号</font><br>
 如高德地图在"关于"中体现</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#getMapPrintScreen-com.amap.api.maps.AMap.onMapPrintScreenListener-">getMapPrintScreen</a></span>(<a href="../../../../com/amap/api/maps/AMap.onMapPrintScreenListener.html" title="com.amap.api.maps中的接口">AMap.onMapPrintScreenListener</a>&nbsp;listener)</code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;
<div class="block"><span class="deprecationComment">建议使用<a href="../../../../com/amap/api/maps/AMap.html#getMapScreenShot-com.amap.api.maps.AMap.OnMapScreenShotListener-"><code>AMap.getMapScreenShot(OnMapScreenShotListener)</code></a></span></div>
</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#getMapRegionSnapshot-com.amap.api.maps.model.LatLng-com.amap.api.maps.model.LatLng-android.util.Size-com.amap.api.maps.AMap.OnMapSnapshotListener-">getMapRegionSnapshot</a></span>(<a href="../../../../com/amap/api/maps/model/LatLng.html" title="com.amap.api.maps.model中的类">LatLng</a>&nbsp;topLeft,
                    <a href="../../../../com/amap/api/maps/model/LatLng.html" title="com.amap.api.maps.model中的类">LatLng</a>&nbsp;topRight,
                    android.util.Size&nbsp;regionPixels,
                    <a href="../../../../com/amap/api/maps/AMap.OnMapSnapshotListener.html" title="com.amap.api.maps中的接口">AMap.OnMapSnapshotListener</a>&nbsp;listener)</code>&nbsp;</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../com/amap/api/maps/model/Marker.html" title="com.amap.api.maps.model中的类">Marker</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#getMapScreenMarkers--">getMapScreenMarkers</a></span>()</code>
<div class="block">获取当前地图可视区域范围所有marker对象。</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#getMapScreenShot-com.amap.api.maps.AMap.OnMapScreenShotListener-">getMapScreenShot</a></span>(<a href="../../../../com/amap/api/maps/AMap.OnMapScreenShotListener.html" title="com.amap.api.maps中的接口">AMap.OnMapScreenShotListener</a>&nbsp;listener)</code>
<div class="block">发起地图截屏请求。</div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#getMapTextZIndex--">getMapTextZIndex</a></span>()</code>
<div class="block">得到地图底图文字标注的层级指数。</div>
</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#getMapType--">getMapType</a></span>()</code>
<div class="block">获取地图当前的模式。</div>
</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#getMaxZoomLevel--">getMaxZoomLevel</a></span>()</code>
<div class="block">返回地图可显示最大缩放级别。</div>
</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#getMinZoomLevel--">getMinZoomLevel</a></span>()</code>
<div class="block">返回地图可显示最小缩放级别。</div>
</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code>android.location.Location</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#getMyLocation--">getMyLocation</a></span>()</code>
<div class="block">返回当前定位源（locationSource）提供的定位信息。</div>
</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code><a href="../../../../com/amap/api/maps/model/MyLocationStyle.html" title="com.amap.api.maps.model中的类">MyLocationStyle</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#getMyLocationStyle--">getMyLocationStyle</a></span>()</code>
<div class="block">获取定位图层（myLocationOverlay）的样式。</div>
</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/amap/api/maps/model/MyTrafficStyle.html" title="com.amap.api.maps.model中的类">MyTrafficStyle</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#getMyTrafficStyle--">getMyTrafficStyle</a></span>()</code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;
<div class="block"><span class="deprecationComment">自7.8.0之后不再支持</span></div>
</div>
</td>
</tr>
<tr id="i36" class="altColor">
<td class="colFirst"><code><a href="../../../../com/amap/api/maps/Projection.html" title="com.amap.api.maps中的类">Projection</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#getProjection--">getProjection</a></span>()</code>
<div class="block">获取地图投影坐标转换器, 当地图初始化完成之前返回 null。</div>
</td>
</tr>
<tr id="i37" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#getSatelliteImageApprovalNumber--">getSatelliteImageApprovalNumber</a></span>()</code>
<div class="block">获取地图审图号（卫星地图）<br>
 <font color="red">任何使用高德地图API调用地图服务的应用必须在其应用中对外透出审图号</font><br>
 如高德地图在"关于"中体现</div>
</td>
</tr>
<tr id="i38" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#getScalePerPixel--">getScalePerPixel</a></span>()</code>
<div class="block">获取当前缩放级别下，地图上1像素点对应的长度，单位米。</div>
</td>
</tr>
<tr id="i39" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#getTerrainApprovalNumber--">getTerrainApprovalNumber</a></span>()</code>
<div class="block">获取地图审图号 （地形图）<br>
 <font color="red">任何使用高德地图API调用地图服务的应用必须在其应用中对外透出审图号</font><br>
 如高德地图在"关于"中体现</div>
</td>
</tr>
<tr id="i40" class="altColor">
<td class="colFirst"><code><a href="../../../../com/amap/api/maps/UiSettings.html" title="com.amap.api.maps中的类">UiSettings</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#getUiSettings--">getUiSettings</a></span>()</code>
<div class="block">获取地图ui控制器，可以控制内置ui（缩放按钮、指北针等）是否显示及部分手势（滑动、双指缩放等）是否可用。</div>
</td>
</tr>
<tr id="i41" class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#getVersion--">getVersion</a></span>()</code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;</div>
</td>
</tr>
<tr id="i42" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#hideBuildings-java.util.List-">hideBuildings</a></span>(java.util.List&lt;<a href="../../../../com/amap/api/maps/model/LatLng.html" title="com.amap.api.maps.model中的类">LatLng</a>&gt;&nbsp;polygon)</code>
<div class="block">隐藏建筑物</div>
</td>
</tr>
<tr id="i43" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#isMyLocationEnabled--">isMyLocationEnabled</a></span>()</code>
<div class="block">返回是否打开定位图层（myLocationOverlay）。</div>
</td>
</tr>
<tr id="i44" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#isTouchPoiEnable--">isTouchPoiEnable</a></span>()</code>
<div class="block">获取地图POI是否允许点击。</div>
</td>
</tr>
<tr id="i45" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#isTrafficEnabled--">isTrafficEnabled</a></span>()</code>
<div class="block">获取是否打开交通路况图层。</div>
</td>
</tr>
<tr id="i46" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#moveCamera-com.amap.api.maps.CameraUpdate-">moveCamera</a></span>(<a href="../../../../com/amap/api/maps/CameraUpdate.html" title="com.amap.api.maps中的类">CameraUpdate</a>&nbsp;update)</code>
<div class="block">按照传入的CameraUpdate参数改变地图状态。</div>
</td>
</tr>
<tr id="i47" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#reloadMap--">reloadMap</a></span>()</code>
<div class="block">重新加载地图引擎，即调用此接口时会重新加载底图数据，覆盖物不受影响。</div>
</td>
</tr>
<tr id="i48" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#removeAMapAppResourceListener-com.amap.api.maps.AMap.AMapAppResourceRequestListener-">removeAMapAppResourceListener</a></span>(<a href="../../../../com/amap/api/maps/AMap.AMapAppResourceRequestListener.html" title="com.amap.api.maps中的接口">AMap.AMapAppResourceRequestListener</a>&nbsp;listener)</code>&nbsp;</td>
</tr>
<tr id="i49" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#removecache--">removecache</a></span>()</code>
<div class="block">删除地图缓存。</div>
</td>
</tr>
<tr id="i50" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#removecache-com.amap.api.maps.AMap.OnCacheRemoveListener-">removecache</a></span>(<a href="../../../../com/amap/api/maps/AMap.OnCacheRemoveListener.html" title="com.amap.api.maps中的接口">AMap.OnCacheRemoveListener</a>&nbsp;onCacheRemoveListener)</code>
<div class="block">删除地图缓存。</div>
</td>
</tr>
<tr id="i51" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#resetMinMaxZoomPreference--">resetMinMaxZoomPreference</a></span>()</code>
<div class="block">重置最小及最大缩放级别 将恢复最小级别为3，最大级别为20 。</div>
</td>
</tr>
<tr id="i52" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#runOnDrawFrame--">runOnDrawFrame</a></span>()</code>
<div class="block">触发地图立即刷新。</div>
</td>
</tr>
<tr id="i53" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#setAMapGestureListener-com.amap.api.maps.model.AMapGestureListener-">setAMapGestureListener</a></span>(<a href="../../../../com/amap/api/maps/model/AMapGestureListener.html" title="com.amap.api.maps.model中的接口">AMapGestureListener</a>&nbsp;aMapGestureListener)</code>
<div class="block">设置手势监听接口</div>
</td>
</tr>
<tr id="i54" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#setConstructingRoadEnable-boolean-">setConstructingRoadEnable</a></span>(boolean&nbsp;enabled)</code>
<div class="block">设置在建道路图层是否显示,默认不显示。</div>
</td>
</tr>
<tr id="i55" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#setCustomMapStyle-com.amap.api.maps.model.CustomMapStyleOptions-">setCustomMapStyle</a></span>(<a href="../../../../com/amap/api/maps/model/CustomMapStyleOptions.html" title="com.amap.api.maps.model中的类">CustomMapStyleOptions</a>&nbsp;options)</code>
<div class="block">设置底图自定义样式对应配置文件路径。</div>
</td>
</tr>
<tr id="i56" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#setCustomMapStyleID-java.lang.String-">setCustomMapStyleID</a></span>(java.lang.String&nbsp;styleID)</code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;
<div class="block"><span class="deprecationComment">该方法已无效，建议到官网lbs.amap.com更新样式文件并使用 <a href="../../../../com/amap/api/maps/AMap.html#setCustomMapStyle-com.amap.api.maps.model.CustomMapStyleOptions-"><code>AMap.setCustomMapStyle(CustomMapStyleOptions)</code></a> )} 替换</span></div>
</div>
</td>
</tr>
<tr id="i57" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#setCustomMapStylePath-java.lang.String-">setCustomMapStylePath</a></span>(java.lang.String&nbsp;stylePath)</code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;
<div class="block"><span class="deprecationComment">该方法已无效，建议到官网lbs.amap.com更新样式文件并使用 <a href="../../../../com/amap/api/maps/AMap.html#setCustomMapStyle-com.amap.api.maps.model.CustomMapStyleOptions-"><code>AMap.setCustomMapStyle(CustomMapStyleOptions)</code></a> )} 替换</span></div>
</div>
</td>
</tr>
<tr id="i58" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#setCustomRenderer-com.amap.api.maps.CustomRenderer-">setCustomRenderer</a></span>(<a href="../../../../com/amap/api/maps/CustomRenderer.html" title="com.amap.api.maps中的接口">CustomRenderer</a>&nbsp;render)</code>
<div class="block">设置地图在初始化及每一帧绘制时的回调接口，该接口在OpenGL线程中调用。</div>
</td>
</tr>
<tr id="i59" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#setIndoorBuildingInfo-com.amap.api.maps.model.IndoorBuildingInfo-">setIndoorBuildingInfo</a></span>(<a href="../../../../com/amap/api/maps/model/IndoorBuildingInfo.html" title="com.amap.api.maps.model中的类">IndoorBuildingInfo</a>&nbsp;indoorBuildingInfo)</code>
<div class="block">室内地图楼层控制接口，通过此接口可以控制某个室内地图显示的楼层。</div>
</td>
</tr>
<tr id="i60" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#setInfoWindowAdapter-com.amap.api.maps.AMap.InfoWindowAdapter-">setInfoWindowAdapter</a></span>(<a href="../../../../com/amap/api/maps/AMap.InfoWindowAdapter.html" title="com.amap.api.maps中的接口">AMap.InfoWindowAdapter</a>&nbsp;adapter)</code>
<div class="block">设置marker的信息窗口定制接口。</div>
</td>
</tr>
<tr id="i61" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#setLoadOfflineData-boolean-">setLoadOfflineData</a></span>(boolean&nbsp;enabled)</code>
<div class="block">重新加载离线地图数据。</div>
</td>
</tr>
<tr id="i62" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#setLocationSource-com.amap.api.maps.LocationSource-">setLocationSource</a></span>(<a href="../../../../com/amap/api/maps/LocationSource.html" title="com.amap.api.maps中的接口">LocationSource</a>&nbsp;locationSource)</code>
<div class="block">设置定位源（locationSource）。</div>
</td>
</tr>
<tr id="i63" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#setMapCustomEnable-boolean-">setMapCustomEnable</a></span>(boolean&nbsp;enable)</code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;
<div class="block"><span class="deprecationComment">该方法已无效，建议到官网lbs.amap.com更新样式文件并使用 <a href="../../../../com/amap/api/maps/AMap.html#setCustomMapStyle-com.amap.api.maps.model.CustomMapStyleOptions-"><code>AMap.setCustomMapStyle(CustomMapStyleOptions)</code></a> )} 替换</span></div>
</div>
</td>
</tr>
<tr id="i64" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#setMapStatusLimits-com.amap.api.maps.model.LatLngBounds-">setMapStatusLimits</a></span>(<a href="../../../../com/amap/api/maps/model/LatLngBounds.html" title="com.amap.api.maps.model中的类">LatLngBounds</a>&nbsp;latLngBounds)</code>
<div class="block">设置地图显示范围，无论如何操作地图，显示区域都不能超过该矩形区域。</div>
</td>
</tr>
<tr id="i65" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#setMapTextZIndex-int-">setMapTextZIndex</a></span>(int&nbsp;x)</code>
<div class="block">设置地图底图文字标注的层级指数，默认为0，用来比较覆盖物（polyline、polygon、circle等）的zIndex。</div>
</td>
</tr>
<tr id="i66" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#setMapType-int-">setMapType</a></span>(int&nbsp;type)</code>
<div class="block">设置地图模式。</div>
</td>
</tr>
<tr id="i67" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#setMaxZoomLevel-float-">setMaxZoomLevel</a></span>(float&nbsp;zoomLevel)</code>
<div class="block">设置地图最大缩放级别 缩放级别范围为[3, 20],超出范围将按最大级别计算 。</div>
</td>
</tr>
<tr id="i68" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#setMinZoomLevel-float-">setMinZoomLevel</a></span>(float&nbsp;zoomLevel)</code>
<div class="block">设置最小缩放级别 缩放级别范围为[3, 20],超出范围将按最小级别计算</div>
</td>
</tr>
<tr id="i69" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#setMyLocationEnabled-boolean-">setMyLocationEnabled</a></span>(boolean&nbsp;enabled)</code>
<div class="block">设置是否打开定位图层（myLocationOverlay）。</div>
</td>
</tr>
<tr id="i70" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#setMyLocationRotateAngle-float-">setMyLocationRotateAngle</a></span>(float&nbsp;rotate)</code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;
<div class="block"><span class="deprecationComment">自5.0.0 废弃</span></div>
</div>
</td>
</tr>
<tr id="i71" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#setMyLocationStyle-com.amap.api.maps.model.MyLocationStyle-">setMyLocationStyle</a></span>(<a href="../../../../com/amap/api/maps/model/MyLocationStyle.html" title="com.amap.api.maps.model中的类">MyLocationStyle</a>&nbsp;style)</code>
<div class="block">设置定位图层（myLocationOverlay）的样式。</div>
</td>
</tr>
<tr id="i72" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#setMyLocationType-int-">setMyLocationType</a></span>(int&nbsp;type)</code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;
<div class="block"><span class="deprecationComment">5.0.0 之后请使用 <a href="../../../../com/amap/api/maps/AMap.html#setMyLocationStyle-com.amap.api.maps.model.MyLocationStyle-"><code>AMap.setMyLocationStyle(MyLocationStyle)</code></a> 代替</span></div>
</div>
</td>
</tr>
<tr id="i73" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#setMyTrafficStyle-com.amap.api.maps.model.MyTrafficStyle-">setMyTrafficStyle</a></span>(<a href="../../../../com/amap/api/maps/model/MyTrafficStyle.html" title="com.amap.api.maps.model中的类">MyTrafficStyle</a>&nbsp;style)</code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;
<div class="block"><span class="deprecationComment">自7.8.0之后不再支持</span></div>
</div>
</td>
</tr>
<tr id="i74" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#setOnCameraChangeListener-com.amap.api.maps.AMap.OnCameraChangeListener-">setOnCameraChangeListener</a></span>(<a href="../../../../com/amap/api/maps/AMap.OnCameraChangeListener.html" title="com.amap.api.maps中的接口">AMap.OnCameraChangeListener</a>&nbsp;listener)</code>
<div class="block">设置地图状态的监听接口。</div>
</td>
</tr>
<tr id="i75" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#setOnIndoorBuildingActiveListener-com.amap.api.maps.AMap.OnIndoorBuildingActiveListener-">setOnIndoorBuildingActiveListener</a></span>(<a href="../../../../com/amap/api/maps/AMap.OnIndoorBuildingActiveListener.html" title="com.amap.api.maps中的接口">AMap.OnIndoorBuildingActiveListener</a>&nbsp;listener)</code>
<div class="block">设置室内地图状态监听接口</div>
</td>
</tr>
<tr id="i76" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#setOnInfoWindowClickListener-com.amap.api.maps.AMap.OnInfoWindowClickListener-">setOnInfoWindowClickListener</a></span>(<a href="../../../../com/amap/api/maps/AMap.OnInfoWindowClickListener.html" title="com.amap.api.maps中的接口">AMap.OnInfoWindowClickListener</a>&nbsp;listener)</code>
<div class="block">设置marker的信息窗口点击事件监听接口。</div>
</td>
</tr>
<tr id="i77" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#setOnMapClickListener-com.amap.api.maps.AMap.OnMapClickListener-">setOnMapClickListener</a></span>(<a href="../../../../com/amap/api/maps/AMap.OnMapClickListener.html" title="com.amap.api.maps中的接口">AMap.OnMapClickListener</a>&nbsp;listener)</code>
<div class="block">设置地图点击事件监听接口。</div>
</td>
</tr>
<tr id="i78" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#setOnMapLoadedListener-com.amap.api.maps.AMap.OnMapLoadedListener-">setOnMapLoadedListener</a></span>(<a href="../../../../com/amap/api/maps/AMap.OnMapLoadedListener.html" title="com.amap.api.maps中的接口">AMap.OnMapLoadedListener</a>&nbsp;listener)</code>
<div class="block">设置地图加载完成监听接口</div>
</td>
</tr>
<tr id="i79" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#setOnMapLongClickListener-com.amap.api.maps.AMap.OnMapLongClickListener-">setOnMapLongClickListener</a></span>(<a href="../../../../com/amap/api/maps/AMap.OnMapLongClickListener.html" title="com.amap.api.maps中的接口">AMap.OnMapLongClickListener</a>&nbsp;listener)</code>
<div class="block">设置地图长按事件监听接口。</div>
</td>
</tr>
<tr id="i80" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#setOnMapTouchListener-com.amap.api.maps.AMap.OnMapTouchListener-">setOnMapTouchListener</a></span>(<a href="../../../../com/amap/api/maps/AMap.OnMapTouchListener.html" title="com.amap.api.maps中的接口">AMap.OnMapTouchListener</a>&nbsp;listener)</code>
<div class="block">设置地图触摸事件监听接口。</div>
</td>
</tr>
<tr id="i81" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#setOnMarkerClickListener-com.amap.api.maps.AMap.OnMarkerClickListener-">setOnMarkerClickListener</a></span>(<a href="../../../../com/amap/api/maps/AMap.OnMarkerClickListener.html" title="com.amap.api.maps中的接口">AMap.OnMarkerClickListener</a>&nbsp;listener)</code>
<div class="block">设置marker点击事件监听接口。</div>
</td>
</tr>
<tr id="i82" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#setOnMarkerDragListener-com.amap.api.maps.AMap.OnMarkerDragListener-">setOnMarkerDragListener</a></span>(<a href="../../../../com/amap/api/maps/AMap.OnMarkerDragListener.html" title="com.amap.api.maps中的接口">AMap.OnMarkerDragListener</a>&nbsp;listener)</code>
<div class="block">marker拖动事件监听接口</div>
</td>
</tr>
<tr id="i83" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#setOnMultiPointClickListener-com.amap.api.maps.AMap.OnMultiPointClickListener-">setOnMultiPointClickListener</a></span>(<a href="../../../../com/amap/api/maps/AMap.OnMultiPointClickListener.html" title="com.amap.api.maps中的接口">AMap.OnMultiPointClickListener</a>&nbsp;listener)</code>
<div class="block">设置海量点单击事件监听</div>
</td>
</tr>
<tr id="i84" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#setOnMyLocationChangeListener-com.amap.api.maps.AMap.OnMyLocationChangeListener-">setOnMyLocationChangeListener</a></span>(<a href="../../../../com/amap/api/maps/AMap.OnMyLocationChangeListener.html" title="com.amap.api.maps中的接口">AMap.OnMyLocationChangeListener</a>&nbsp;listener)</code>
<div class="block">设置用户定位信息监听接口。</div>
</td>
</tr>
<tr id="i85" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#setOnPOIClickListener-com.amap.api.maps.AMap.OnPOIClickListener-">setOnPOIClickListener</a></span>(<a href="../../../../com/amap/api/maps/AMap.OnPOIClickListener.html" title="com.amap.api.maps中的接口">AMap.OnPOIClickListener</a>&nbsp;listener)</code>
<div class="block">设置底图poi点击事件监听接口。</div>
</td>
</tr>
<tr id="i86" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#setOnPolylineClickListener-com.amap.api.maps.AMap.OnPolylineClickListener-">setOnPolylineClickListener</a></span>(<a href="../../../../com/amap/api/maps/AMap.OnPolylineClickListener.html" title="com.amap.api.maps中的接口">AMap.OnPolylineClickListener</a>&nbsp;listener)</code>
<div class="block">设置polyline点击事件监听接口。</div>
</td>
</tr>
<tr id="i87" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#setPointToCenter-int-int-">setPointToCenter</a></span>(int&nbsp;x,
                int&nbsp;y)</code>
<div class="block">设置屏幕上的某个像素点为地图中心点。</div>
</td>
</tr>
<tr id="i88" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#setTouchPoiEnable-boolean-">setTouchPoiEnable</a></span>(boolean&nbsp;touchPoiEnable)</code>
<div class="block">设置地图POI是否允许点击。</div>
</td>
</tr>
<tr id="i89" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#setTrafficEnabled-boolean-">setTrafficEnabled</a></span>(boolean&nbsp;enabled)</code>
<div class="block">设置是否打开交通路况图层。</div>
</td>
</tr>
<tr id="i90" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#showBuildings-boolean-">showBuildings</a></span>(boolean&nbsp;enabled)</code>
<div class="block">设置是否显示3D建筑物，默认显示。</div>
</td>
</tr>
<tr id="i91" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#showHideBuildings-int-">showHideBuildings</a></span>(int&nbsp;operateId)</code>
<div class="block">显示隐藏的建筑物</div>
</td>
</tr>
<tr id="i92" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#showIndoorMap-boolean-">showIndoorMap</a></span>(boolean&nbsp;enabled)</code>
<div class="block">设置是否显示室内地图，默认不显示。</div>
</td>
</tr>
<tr id="i93" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#showMapText-boolean-">showMapText</a></span>(boolean&nbsp;enabled)</code>
<div class="block">设置是否显示底图文字标注，默认显示。</div>
</td>
</tr>
<tr id="i94" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.html#stopAnimation--">stopAnimation</a></span>()</code>
<div class="block">停止当前执行的改变地图状态的动画。</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>字段详细资料</h3>
<a name="MAP_TYPE_NORMAL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MAP_TYPE_NORMAL</h4>
<pre>public static final&nbsp;int MAP_TYPE_NORMAL</pre>
<div class="block">普通地图模式常量。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.maps.AMap.MAP_TYPE_NORMAL">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="MAP_TYPE_SATELLITE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MAP_TYPE_SATELLITE</h4>
<pre>public static final&nbsp;int MAP_TYPE_SATELLITE</pre>
<div class="block">卫星图模式常量。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.maps.AMap.MAP_TYPE_SATELLITE">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="MAP_TYPE_NIGHT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MAP_TYPE_NIGHT</h4>
<pre>public static final&nbsp;int MAP_TYPE_NIGHT</pre>
<div class="block">夜景图模式常量。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.maps.AMap.MAP_TYPE_NIGHT">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="MAP_TYPE_NAVI">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MAP_TYPE_NAVI</h4>
<pre>public static final&nbsp;int MAP_TYPE_NAVI</pre>
<div class="block">导航模式常量。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.maps.AMap.MAP_TYPE_NAVI">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="MAP_TYPE_BUS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MAP_TYPE_BUS</h4>
<pre>public static final&nbsp;int MAP_TYPE_BUS</pre>
<div class="block">公交模式常量。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>4.2.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.maps.AMap.MAP_TYPE_BUS">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="LOCATION_TYPE_LOCATE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LOCATION_TYPE_LOCATE</h4>
<pre>public static final&nbsp;int LOCATION_TYPE_LOCATE</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;<span class="deprecationComment">5.0.0 之后请参考 <a href="../../../../com/amap/api/maps/model/MyLocationStyle.html#LOCATION_TYPE_LOCATE"><code>MyLocationStyle.LOCATION_TYPE_LOCATE</code></a></span></div>
<div class="block">普通定位模式常量，只在第一次定位移动到地图中心点。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.maps.AMap.LOCATION_TYPE_LOCATE">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="LOCATION_TYPE_MAP_FOLLOW">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LOCATION_TYPE_MAP_FOLLOW</h4>
<pre>public static final&nbsp;int LOCATION_TYPE_MAP_FOLLOW</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;<span class="deprecationComment">5.0.0 之后请参考 <a href="../../../../com/amap/api/maps/model/MyLocationStyle.html#LOCATION_TYPE_FOLLOW"><code>MyLocationStyle.LOCATION_TYPE_FOLLOW</code></a></span></div>
<div class="block">跟随定位模式常量，定位过程中自动移动到地图中心点。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.maps.AMap.LOCATION_TYPE_MAP_FOLLOW">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="LOCATION_TYPE_MAP_ROTATE">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>LOCATION_TYPE_MAP_ROTATE</h4>
<pre>public static final&nbsp;int LOCATION_TYPE_MAP_ROTATE</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;<span class="deprecationComment">5.0.0 之后请参考 <a href="../../../../com/amap/api/maps/model/MyLocationStyle.html#LOCATION_TYPE_MAP_ROTATE"><code>MyLocationStyle.LOCATION_TYPE_MAP_ROTATE</code></a></span></div>
<div class="block">旋转定位模式常量，定位过程中移动到地图中心点且根据陀螺仪方向旋转地图。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.2.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.maps.AMap.LOCATION_TYPE_MAP_ROTATE">常量字段值</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="getCameraPosition--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCameraPosition</h4>
<pre>public final&nbsp;<a href="../../../../com/amap/api/maps/model/CameraPosition.html" title="com.amap.api.maps.model中的类">CameraPosition</a>&nbsp;getCameraPosition()</pre>
<div class="block">获取地图的当前状态。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>一个cameraPosition对象，表示地图的当前状态。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="getMaxZoomLevel--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMaxZoomLevel</h4>
<pre>public final&nbsp;float&nbsp;getMaxZoomLevel()</pre>
<div class="block">返回地图可显示最大缩放级别。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>最大缩放级别。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="getMinZoomLevel--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMinZoomLevel</h4>
<pre>public final&nbsp;float&nbsp;getMinZoomLevel()</pre>
<div class="block">返回地图可显示最小缩放级别。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>最小缩放级别。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="moveCamera-com.amap.api.maps.CameraUpdate-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>moveCamera</h4>
<pre>public final&nbsp;void&nbsp;moveCamera(<a href="../../../../com/amap/api/maps/CameraUpdate.html" title="com.amap.api.maps中的类">CameraUpdate</a>&nbsp;update)</pre>
<div class="block">按照传入的CameraUpdate参数改变地图状态。（直接改变状态，没有动画效果）<br>
 可以通过<a href="../../../../com/amap/api/maps/CameraUpdateFactory.html#zoomIn--"><code>CameraUpdateFactory.zoomIn()</code></a>等方法来生成对应的CameraUpdate对象。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>update</code> - 地图状态将要发生的变化。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="animateCamera-com.amap.api.maps.CameraUpdate-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>animateCamera</h4>
<pre>public final&nbsp;void&nbsp;animateCamera(<a href="../../../../com/amap/api/maps/CameraUpdate.html" title="com.amap.api.maps中的类">CameraUpdate</a>&nbsp;update)</pre>
<div class="block">以动画方式按照传入的CameraUpdate参数更新地图状态，默认动画耗时250毫秒。<br>
 可以通过<a href="../../../../com/amap/api/maps/CameraUpdateFactory.html#zoomIn--"><code>CameraUpdateFactory.zoomIn()</code></a>等方法来生成对应的CameraUpdate对象。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>update</code> - 地图状态将要发生的变化。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.1</dd>
</dl>
</li>
</ul>
<a name="animateCamera-com.amap.api.maps.CameraUpdate-com.amap.api.maps.AMap.CancelableCallback-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>animateCamera</h4>
<pre>public final&nbsp;void&nbsp;animateCamera(<a href="../../../../com/amap/api/maps/CameraUpdate.html" title="com.amap.api.maps中的类">CameraUpdate</a>&nbsp;update,
                                <a href="../../../../com/amap/api/maps/AMap.CancelableCallback.html" title="com.amap.api.maps中的接口">AMap.CancelableCallback</a>&nbsp;cancelableCallback)</pre>
<div class="block">以动画方式按照传入的CameraUpdate参数更新地图状态，默认动画耗时250毫秒，同时设置一个cancelableCallback来监听动画执行的结果。<br>
 可以通过<a href="../../../../com/amap/api/maps/CameraUpdateFactory.html#zoomIn--"><code>CameraUpdateFactory.zoomIn()</code></a>等方法来生成对应的CameraUpdate对象。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>update</code> - 地图状态将要发生的变化。</dd>
<dd><code>cancelableCallback</code> - 如果动画正常完成，则会回调onFinish()方法，被中断时回调onCancel()。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.1</dd>
</dl>
</li>
</ul>
<a name="animateCamera-com.amap.api.maps.CameraUpdate-long-com.amap.api.maps.AMap.CancelableCallback-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>animateCamera</h4>
<pre>public final&nbsp;void&nbsp;animateCamera(<a href="../../../../com/amap/api/maps/CameraUpdate.html" title="com.amap.api.maps中的类">CameraUpdate</a>&nbsp;update,
                                long&nbsp;durationMs,
                                <a href="../../../../com/amap/api/maps/AMap.CancelableCallback.html" title="com.amap.api.maps中的接口">AMap.CancelableCallback</a>&nbsp;cancelableCallback)</pre>
<div class="block">按照指定的动画时长及传入的CameraUpdate参数更新地图状态，，同时设置一个cancelableCallback来监听动画执行的结果。<br>
 可以通过<a href="../../../../com/amap/api/maps/CameraUpdateFactory.html#zoomIn--"><code>CameraUpdateFactory.zoomIn()</code></a>等方法来生成对应的CameraUpdate对象。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>update</code> - 地图状态将要发生的变化。</dd>
<dd><code>durationMs</code> - 动画的持续时间，单位毫秒。</dd>
<dd><code>cancelableCallback</code> - 如果动画正常完成，则会回调onFinish()方法，被中断时回调onCancel()。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.1</dd>
</dl>
</li>
</ul>
<a name="stopAnimation--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>stopAnimation</h4>
<pre>public final&nbsp;void&nbsp;stopAnimation()</pre>
<div class="block">停止当前执行的改变地图状态的动画。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.1</dd>
</dl>
</li>
</ul>
<a name="addNavigateArrow-com.amap.api.maps.model.NavigateArrowOptions-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addNavigateArrow</h4>
<pre>public final&nbsp;<a href="../../../../com/amap/api/maps/model/NavigateArrow.html" title="com.amap.api.maps.model中的类">NavigateArrow</a>&nbsp;addNavigateArrow(<a href="../../../../com/amap/api/maps/model/NavigateArrowOptions.html" title="com.amap.api.maps.model中的类">NavigateArrowOptions</a>&nbsp;options)</pre>
<div class="block">在地图上添加一个导航指示箭头对象（navigateArrow）对象。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>options</code> - 一个navigateArrowOptions 对象，它定义navigateArrow的属性信息。</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>返回被添加的navigateArrow 对象。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="addPolyline-com.amap.api.maps.model.PolylineOptions-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addPolyline</h4>
<pre>public final&nbsp;<a href="../../../../com/amap/api/maps/model/Polyline.html" title="com.amap.api.maps.model中的类">Polyline</a>&nbsp;addPolyline(<a href="../../../../com/amap/api/maps/model/PolylineOptions.html" title="com.amap.api.maps.model中的类">PolylineOptions</a>&nbsp;options)</pre>
<div class="block">在地图上添加一个折线对象（polyline）对象。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>options</code> - 一个polylineOptions对象，它定义polyline的属性信息。</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>返回被添加的 polyline 对象。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="addBuildingOverlay--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addBuildingOverlay</h4>
<pre>public final&nbsp;<a href="../../../../com/amap/api/maps/model/BuildingOverlay.html" title="com.amap.api.maps.model中的类">BuildingOverlay</a>&nbsp;addBuildingOverlay()</pre>
<div class="block">添加建筑物图层，默认图层的区域为全世界。
 建议一张地图上只添加一个BuildingOverlay，不要添加多个BuildingOverlay</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>建筑物图层</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.3.0</dd>
</dl>
</li>
</ul>
<a name="addCircle-com.amap.api.maps.model.CircleOptions-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addCircle</h4>
<pre>public final&nbsp;<a href="../../../../com/amap/api/maps/model/Circle.html" title="com.amap.api.maps.model中的类">Circle</a>&nbsp;addCircle(<a href="../../../../com/amap/api/maps/model/CircleOptions.html" title="com.amap.api.maps.model中的类">CircleOptions</a>&nbsp;options)</pre>
<div class="block">在地图上添加一个圆（circle）对象。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>options</code> - 一个circleOptions对象，它定义circle的属性信息。</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>返回被添加的circle 对象。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="addArc-com.amap.api.maps.model.ArcOptions-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addArc</h4>
<pre>public final&nbsp;<a href="../../../../com/amap/api/maps/model/Arc.html" title="com.amap.api.maps.model中的类">Arc</a>&nbsp;addArc(<a href="../../../../com/amap/api/maps/model/ArcOptions.html" title="com.amap.api.maps.model中的类">ArcOptions</a>&nbsp;options)</pre>
<div class="block">在地图上添加一个圆弧（arc）对象。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>options</code> - 一个arcOptions对象，它定义arc的属性信息。</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>返回被添加的arc对象。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="addPolygon-com.amap.api.maps.model.PolygonOptions-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addPolygon</h4>
<pre>public final&nbsp;<a href="../../../../com/amap/api/maps/model/Polygon.html" title="com.amap.api.maps.model中的类">Polygon</a>&nbsp;addPolygon(<a href="../../../../com/amap/api/maps/model/PolygonOptions.html" title="com.amap.api.maps.model中的类">PolygonOptions</a>&nbsp;options)</pre>
<div class="block">在地图上添加一个多边形（polygon）对象。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>options</code> - 一个polygonOptions 对象，它定义了polygon的属性信息。</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>返回被添加的polygon对象。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="addGroundOverlay-com.amap.api.maps.model.GroundOverlayOptions-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addGroundOverlay</h4>
<pre>public final&nbsp;<a href="../../../../com/amap/api/maps/model/GroundOverlay.html" title="com.amap.api.maps.model中的类">GroundOverlay</a>&nbsp;addGroundOverlay(<a href="../../../../com/amap/api/maps/model/GroundOverlayOptions.html" title="com.amap.api.maps.model中的类">GroundOverlayOptions</a>&nbsp;options)</pre>
<div class="block">在地图上添加一个Ground覆盖物（groundOverlay）对象</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>options</code> - 一个groundOverlayOptions 对象，它定义了groundOverlay 的属性信息。</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>返回被添加的groundOverlay对象。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="addMarker-com.amap.api.maps.model.MarkerOptions-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addMarker</h4>
<pre>public final&nbsp;<a href="../../../../com/amap/api/maps/model/Marker.html" title="com.amap.api.maps.model中的类">Marker</a>&nbsp;addMarker(<a href="../../../../com/amap/api/maps/model/MarkerOptions.html" title="com.amap.api.maps.model中的类">MarkerOptions</a>&nbsp;options)</pre>
<div class="block">在地图上添一个图片标记（marker）对象。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>options</code> - 一个markerOptions 对象，它定义了marker 的属性信息。</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>返回被添加的marker对象。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="addText-com.amap.api.maps.model.TextOptions-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addText</h4>
<pre>public final&nbsp;<a href="../../../../com/amap/api/maps/model/Text.html" title="com.amap.api.maps.model中的类">Text</a>&nbsp;addText(<a href="../../../../com/amap/api/maps/model/TextOptions.html" title="com.amap.api.maps.model中的类">TextOptions</a>&nbsp;options)</pre>
<div class="block">在地图上添一个文字标记（text）对象。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>options</code> - 一个textOptions 对象，它定义了text 的属性信息。</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>返回被添加的text对象。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
<a name="addMarkers-java.util.ArrayList-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addMarkers</h4>
<pre>public final&nbsp;java.util.ArrayList&lt;<a href="../../../../com/amap/api/maps/model/Marker.html" title="com.amap.api.maps.model中的类">Marker</a>&gt;&nbsp;addMarkers(java.util.ArrayList&lt;<a href="../../../../com/amap/api/maps/model/MarkerOptions.html" title="com.amap.api.maps.model中的类">MarkerOptions</a>&gt;&nbsp;options,
                                                    boolean&nbsp;moveToCenter)</pre>
<div class="block">在地图上添一组图片标记（marker）对象，并设置是否改变地图状态以至于所有的marker对象都在当前地图可视区域范围内显示。<br></div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>options</code> - 多个markerOptions对象，它们分别定义了对应marker的属性信息。</dd>
<dd><code>moveToCenter</code> - 是否改变地图状态，默认为false。</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>返回一组被添加的marker对象。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.3.0</dd>
</dl>
</li>
</ul>
<a name="getMapScreenMarkers--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMapScreenMarkers</h4>
<pre>public final&nbsp;java.util.List&lt;<a href="../../../../com/amap/api/maps/model/Marker.html" title="com.amap.api.maps.model中的类">Marker</a>&gt;&nbsp;getMapScreenMarkers()</pre>
<div class="block">获取当前地图可视区域范围所有marker对象。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>当前地图可视区域范围的所有marker对象。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.7.0</dd>
</dl>
</li>
</ul>
<a name="addTileOverlay-com.amap.api.maps.model.TileOverlayOptions-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addTileOverlay</h4>
<pre>public final&nbsp;<a href="../../../../com/amap/api/maps/model/TileOverlay.html" title="com.amap.api.maps.model中的类">TileOverlay</a>&nbsp;addTileOverlay(<a href="../../../../com/amap/api/maps/model/TileOverlayOptions.html" title="com.amap.api.maps.model中的类">TileOverlayOptions</a>&nbsp;options)</pre>
<div class="block">在地图上添加一个瓦片图层覆盖物（tileOverlay）对象。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>options</code> - 一个tileOverlayOptions 对象，它定义了tileOverlay的属性信息。</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>返回被添加的tileOverlay对象。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.2.0</dd>
</dl>
</li>
</ul>
<a name="addMVTTileOverlay-com.amap.api.maps.model.MVTTileOverlayOptions-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addMVTTileOverlay</h4>
<pre>public final&nbsp;<a href="../../../../com/amap/api/maps/model/MVTTileOverlay.html" title="com.amap.api.maps.model中的类">MVTTileOverlay</a>&nbsp;addMVTTileOverlay(<a href="../../../../com/amap/api/maps/model/MVTTileOverlayOptions.html" title="com.amap.api.maps.model中的类">MVTTileOverlayOptions</a>&nbsp;options)</pre>
<div class="block">在地图上添加一个瓦片图层覆盖物（MVTtileOverlay）对象。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>options</code> - options 一个tileOverlayOptions 对象，它定义了tileOverlay的属性信息。</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>返回被添加的tileOverlay对象。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>v9.2.0</dd>
</dl>
</li>
</ul>
<a name="addHeatMapLayer-com.amap.api.maps.model.HeatMapLayerOptions-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addHeatMapLayer</h4>
<pre>public final&nbsp;<a href="../../../../com/amap/api/maps/model/HeatMapLayer.html" title="com.amap.api.maps.model中的类">HeatMapLayer</a>&nbsp;addHeatMapLayer(<a href="../../../../com/amap/api/maps/model/HeatMapLayerOptions.html" title="com.amap.api.maps.model中的类">HeatMapLayerOptions</a>&nbsp;options)</pre>
<div class="block">在地图上添加一个热力图对象。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>options</code> - 一个HeatMapLayerOptions对象，它定义了heatMapLayer的属性信息。</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>返回被添加的HeatMapLayer对象。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>7.0.0</dd>
</dl>
</li>
</ul>
<a name="addHeatMapGridLayer-com.amap.api.maps.model.HeatMapGridLayerOptions-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addHeatMapGridLayer</h4>
<pre>public final&nbsp;<a href="../../../../com/amap/api/maps/model/HeatMapGridLayer.html" title="com.amap.api.maps.model中的类">HeatMapGridLayer</a>&nbsp;addHeatMapGridLayer(<a href="../../../../com/amap/api/maps/model/HeatMapGridLayerOptions.html" title="com.amap.api.maps.model中的类">HeatMapGridLayerOptions</a>&nbsp;options)</pre>
<div class="block">在地图上添加一个热力图网格对象。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>options</code> - 一个HeatMapLayerGirdOptions对象，它定义了heatMapGirdLayer的属性信息。</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>返回被添加的HeatMapGirdLayer对象。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.2.0</dd>
</dl>
</li>
</ul>
<a name="addMultiPointOverlay-com.amap.api.maps.model.MultiPointOverlayOptions-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addMultiPointOverlay</h4>
<pre>public&nbsp;<a href="../../../../com/amap/api/maps/model/MultiPointOverlay.html" title="com.amap.api.maps.model中的类">MultiPointOverlay</a>&nbsp;addMultiPointOverlay(<a href="../../../../com/amap/api/maps/model/MultiPointOverlayOptions.html" title="com.amap.api.maps.model中的类">MultiPointOverlayOptions</a>&nbsp;options)</pre>
<div class="block">在地图上添加一个海量点覆盖物（MultiPointOverlay）对象</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>返回一个海量点控制对象</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>5.1.0</dd>
</dl>
</li>
</ul>
<a name="addParticleOverlay-com.amap.api.maps.model.particle.ParticleOverlayOptions-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addParticleOverlay</h4>
<pre>public&nbsp;<a href="../../../../com/amap/api/maps/model/particle/ParticleOverlay.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlay</a>&nbsp;addParticleOverlay(<a href="../../../../com/amap/api/maps/model/particle/ParticleOverlayOptions.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlayOptions</a>&nbsp;options)</pre>
<div class="block">在地图上添加一个粒子系统对象</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>返回一个海量点控制对象</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>5.1.0</dd>
</dl>
</li>
</ul>
<a name="addGLTFOverlay-com.amap.api.maps.model.GLTFOverlayOptions-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addGLTFOverlay</h4>
<pre>public final&nbsp;<a href="../../../../com/amap/api/maps/model/GLTFOverlay.html" title="com.amap.api.maps.model中的类">GLTFOverlay</a>&nbsp;addGLTFOverlay(<a href="../../../../com/amap/api/maps/model/GLTFOverlayOptions.html" title="com.amap.api.maps.model中的类">GLTFOverlayOptions</a>&nbsp;options)</pre>
<div class="block">在地图上添加一个gltf对象。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>options</code> - GLTFOverlayOptions，它定义了。</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>返回被添加的GLTFOverlay对象。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.2.0</dd>
</dl>
</li>
</ul>
<a name="clear--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>clear</h4>
<pre>public final&nbsp;void&nbsp;clear()</pre>
<div class="block">从地图上删除所有的overlay（marker，circle，polyline 等对象）。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="clear-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>clear</h4>
<pre>public final&nbsp;void&nbsp;clear(boolean&nbsp;isKeepMyLocationOverlay)</pre>
<div class="block">从地图上删除所有的覆盖物（marker，circle，polyline 等对象），但myLocationOverlay（内置定位覆盖物）除外。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>isKeepMyLocationOverlay</code> - 是否保留myLocationOverlay。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.0</dd>
</dl>
</li>
</ul>
<a name="getMapType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMapType</h4>
<pre>public final&nbsp;int&nbsp;getMapType()</pre>
<div class="block">获取地图当前的模式。可以参考<a href="../../../../com/amap/api/maps/AMap.html#MAP_TYPE_NORMAL"><code>AMap.MAP_TYPE_NORMAL</code></a>、<a href="../../../../com/amap/api/maps/AMap.html#MAP_TYPE_SATELLITE"><code>AMap.MAP_TYPE_SATELLITE</code></a> 、<a href="../../../../com/amap/api/maps/AMap.html#MAP_TYPE_NAVI"><code>AMap.MAP_TYPE_NAVI</code></a>等。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>地图当前的模式。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="setMapType-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMapType</h4>
<pre>public final&nbsp;void&nbsp;setMapType(int&nbsp;type)</pre>
<div class="block">设置地图模式。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>type</code> - 地图模式： MAP_TYPE_NORMAL：普通地图，值为1；<br>
             MAP_TYPE_SATELLITE：卫星地图，值为2；<br>
             MAP_TYPE_NIGHT 黑夜地图，夜间模式，值为3；<br>
             MAP_TYPE_NAVI 导航模式，值为4; <br>
             MAP_TYPE_BUS 公交模式，值为5。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="isTrafficEnabled--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isTrafficEnabled</h4>
<pre>public final&nbsp;boolean&nbsp;isTrafficEnabled()</pre>
<div class="block">获取是否打开交通路况图层。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>是否打开交通路况图层。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="setTrafficEnabled-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTrafficEnabled</h4>
<pre>public&nbsp;void&nbsp;setTrafficEnabled(boolean&nbsp;enabled)</pre>
<div class="block">设置是否打开交通路况图层。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>enabled</code> - 是否打开交通路况图层。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="showMapText-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showMapText</h4>
<pre>public&nbsp;void&nbsp;showMapText(boolean&nbsp;enabled)</pre>
<div class="block">设置是否显示底图文字标注，默认显示。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>enabled</code> - true： 表示显示，为默认值； false： 不显示</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.2.0</dd>
</dl>
</li>
</ul>
<a name="showIndoorMap-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showIndoorMap</h4>
<pre>public&nbsp;void&nbsp;showIndoorMap(boolean&nbsp;enabled)</pre>
<div class="block">设置是否显示室内地图，默认不显示。<br>
 <p>
 注：如果打开了室内地图，会显示3D建筑物，即如果之前有设置不显示3D建筑物，3D建筑物也会被显示出来。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>enabled</code> - true：显示室内地图；false：不显示；</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.1.0</dd>
</dl>
</li>
</ul>
<a name="showBuildings-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showBuildings</h4>
<pre>public&nbsp;void&nbsp;showBuildings(boolean&nbsp;enabled)</pre>
<div class="block">设置是否显示3D建筑物，默认显示。<br>
 注：<br>
 1. 开启显示3D建筑物后，地图需要被放大至Zoomlevel 17级以上且地图仰角>0（在地图上双指下滑可以调为仰视视角）时，才能可以看到3D建筑物的效果。<br>
 2. 夜间模式的地图无3D建筑物效果。<br></div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>enabled</code> - true：显示3D建筑物；false：不显示；</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.1</dd>
</dl>
</li>
</ul>
<a name="setMyTrafficStyle-com.amap.api.maps.model.MyTrafficStyle-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMyTrafficStyle</h4>
<pre>public&nbsp;void&nbsp;setMyTrafficStyle(<a href="../../../../com/amap/api/maps/model/MyTrafficStyle.html" title="com.amap.api.maps.model中的类">MyTrafficStyle</a>&nbsp;style)</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;<span class="deprecationComment">自7.8.0之后不再支持</span></div>
<div class="block">自定义交通路况图层的样式（各种路况对应的颜色）。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>style</code> - MyTrafficStyle 包含各种路况对应的颜色,详细情况请参考<a href="../../../../com/amap/api/maps/model/MyTrafficStyle.html" title="com.amap.api.maps.model中的类"><code>MyTrafficStyle</code></a>。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="getMyTrafficStyle--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMyTrafficStyle</h4>
<pre>public&nbsp;<a href="../../../../com/amap/api/maps/model/MyTrafficStyle.html" title="com.amap.api.maps.model中的类">MyTrafficStyle</a>&nbsp;getMyTrafficStyle()</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;<span class="deprecationComment">自7.8.0之后不再支持</span></div>
<div class="block">获取交通路况图层路况对应的颜色属性。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>MyTrafficStyle 包含各种路况对应的颜色。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="isMyLocationEnabled--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isMyLocationEnabled</h4>
<pre>public final&nbsp;boolean&nbsp;isMyLocationEnabled()</pre>
<div class="block">返回是否打开定位图层（myLocationOverlay）。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>是否打开定位图层。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="setMyLocationEnabled-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMyLocationEnabled</h4>
<pre>public final&nbsp;void&nbsp;setMyLocationEnabled(boolean&nbsp;enabled)</pre>
<div class="block">设置是否打开定位图层（myLocationOverlay）。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>enabled</code> - 是否打开定位图层。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="getMyLocation--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMyLocation</h4>
<pre>public final&nbsp;android.location.Location&nbsp;getMyLocation()</pre>
<div class="block">返回当前定位源（locationSource）提供的定位信息。如果未设置定位源（locationSource） 则返回null。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>当前定位源（locationSource）提供的定位信息。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="setLocationSource-com.amap.api.maps.LocationSource-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLocationSource</h4>
<pre>public final&nbsp;void&nbsp;setLocationSource(<a href="../../../../com/amap/api/maps/LocationSource.html" title="com.amap.api.maps中的接口">LocationSource</a>&nbsp;locationSource)</pre>
<div class="block">设置定位源（locationSource）。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>locationSource</code> - 定位源（locationSource）。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="setMyLocationStyle-com.amap.api.maps.model.MyLocationStyle-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMyLocationStyle</h4>
<pre>public final&nbsp;void&nbsp;setMyLocationStyle(<a href="../../../../com/amap/api/maps/model/MyLocationStyle.html" title="com.amap.api.maps.model中的类">MyLocationStyle</a>&nbsp;style)</pre>
<div class="block">设置定位图层（myLocationOverlay）的样式。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>style</code> - myLocationStyle 定位图层（myLocationOverlay）样式。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.4</dd>
</dl>
</li>
</ul>
<a name="getMyLocationStyle--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMyLocationStyle</h4>
<pre>public final&nbsp;<a href="../../../../com/amap/api/maps/model/MyLocationStyle.html" title="com.amap.api.maps.model中的类">MyLocationStyle</a>&nbsp;getMyLocationStyle()</pre>
<div class="block">获取定位图层（myLocationOverlay）的样式。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>myLocationStyle 定位图层（myLocationOverlay）样式。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>5.0.0</dd>
</dl>
</li>
</ul>
<a name="setMyLocationType-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMyLocationType</h4>
<pre>public final&nbsp;void&nbsp;setMyLocationType(int&nbsp;type)</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;<span class="deprecationComment">5.0.0 之后请使用 <a href="../../../../com/amap/api/maps/AMap.html#setMyLocationStyle-com.amap.api.maps.model.MyLocationStyle-"><code>AMap.setMyLocationStyle(MyLocationStyle)</code></a> 代替</span></div>
<div class="block">设置定位图层（myLocationOverlay）显示模式<br></div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>type</code> - 显示模式共有三种模式，参考：<a href="../../../../com/amap/api/maps/AMap.html#LOCATION_TYPE_LOCATE"><code>AMap.LOCATION_TYPE_LOCATE</code></a>普通、<a href="../../../../com/amap/api/maps/AMap.html#LOCATION_TYPE_MAP_FOLLOW"><code>AMap.LOCATION_TYPE_MAP_FOLLOW</code></a>跟随、<a href="../../../../com/amap/api/maps/AMap.html#LOCATION_TYPE_MAP_ROTATE"><code>AMap.LOCATION_TYPE_MAP_ROTATE</code></a>旋转。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.2.1</dd>
</dl>
</li>
</ul>
<a name="setMyLocationRotateAngle-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMyLocationRotateAngle</h4>
<pre>public final&nbsp;void&nbsp;setMyLocationRotateAngle(float&nbsp;rotate)</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;<span class="deprecationComment">自5.0.0 废弃</span></div>
<div class="block">设置定位图层（myLocationOverlay）旋转角度</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>rotate</code> - 旋转角度在0~360之间（从正北方向逆时针为正）。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.1</dd>
</dl>
</li>
</ul>
<a name="getUiSettings--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getUiSettings</h4>
<pre>public final&nbsp;<a href="../../../../com/amap/api/maps/UiSettings.html" title="com.amap.api.maps中的类">UiSettings</a>&nbsp;getUiSettings()</pre>
<div class="block">获取地图ui控制器，可以控制内置ui（缩放按钮、指北针等）是否显示及部分手势（滑动、双指缩放等）是否可用。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>地图ui控制器。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="getProjection--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProjection</h4>
<pre>public final&nbsp;<a href="../../../../com/amap/api/maps/Projection.html" title="com.amap.api.maps中的类">Projection</a>&nbsp;getProjection()</pre>
<div class="block">获取地图投影坐标转换器, 当地图初始化完成之前返回 null。<br></div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>地图投影坐标转换器。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="setOnCameraChangeListener-com.amap.api.maps.AMap.OnCameraChangeListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setOnCameraChangeListener</h4>
<pre>public final&nbsp;void&nbsp;setOnCameraChangeListener(<a href="../../../../com/amap/api/maps/AMap.OnCameraChangeListener.html" title="com.amap.api.maps中的接口">AMap.OnCameraChangeListener</a>&nbsp;listener)</pre>
<div class="block">设置地图状态的监听接口。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>listener</code> - 地图状态的监听接口。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="setOnMapClickListener-com.amap.api.maps.AMap.OnMapClickListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setOnMapClickListener</h4>
<pre>public final&nbsp;void&nbsp;setOnMapClickListener(<a href="../../../../com/amap/api/maps/AMap.OnMapClickListener.html" title="com.amap.api.maps中的接口">AMap.OnMapClickListener</a>&nbsp;listener)</pre>
<div class="block">设置地图点击事件监听接口。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>listener</code> - 地图单击事件监听接口。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="setOnMapTouchListener-com.amap.api.maps.AMap.OnMapTouchListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setOnMapTouchListener</h4>
<pre>public final&nbsp;void&nbsp;setOnMapTouchListener(<a href="../../../../com/amap/api/maps/AMap.OnMapTouchListener.html" title="com.amap.api.maps中的接口">AMap.OnMapTouchListener</a>&nbsp;listener)</pre>
<div class="block">设置地图触摸事件监听接口。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>listener</code> - 地图触摸事件监听接口。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.2.1</dd>
</dl>
</li>
</ul>
<a name="setOnPOIClickListener-com.amap.api.maps.AMap.OnPOIClickListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setOnPOIClickListener</h4>
<pre>public final&nbsp;void&nbsp;setOnPOIClickListener(<a href="../../../../com/amap/api/maps/AMap.OnPOIClickListener.html" title="com.amap.api.maps中的接口">AMap.OnPOIClickListener</a>&nbsp;listener)</pre>
<div class="block">设置底图poi点击事件监听接口。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>listener</code> - 底图poi点击事件监听接口。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.4.1</dd>
</dl>
</li>
</ul>
<a name="setOnMyLocationChangeListener-com.amap.api.maps.AMap.OnMyLocationChangeListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setOnMyLocationChangeListener</h4>
<pre>public final&nbsp;void&nbsp;setOnMyLocationChangeListener(<a href="../../../../com/amap/api/maps/AMap.OnMyLocationChangeListener.html" title="com.amap.api.maps中的接口">AMap.OnMyLocationChangeListener</a>&nbsp;listener)</pre>
<div class="block">设置用户定位信息监听接口。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>listener</code> - 用户定位信息监听接口。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="setOnMapLongClickListener-com.amap.api.maps.AMap.OnMapLongClickListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setOnMapLongClickListener</h4>
<pre>public final&nbsp;void&nbsp;setOnMapLongClickListener(<a href="../../../../com/amap/api/maps/AMap.OnMapLongClickListener.html" title="com.amap.api.maps中的接口">AMap.OnMapLongClickListener</a>&nbsp;listener)</pre>
<div class="block">设置地图长按事件监听接口。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>listener</code> - 地图长按事件监听接口。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="setOnMarkerClickListener-com.amap.api.maps.AMap.OnMarkerClickListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setOnMarkerClickListener</h4>
<pre>public final&nbsp;void&nbsp;setOnMarkerClickListener(<a href="../../../../com/amap/api/maps/AMap.OnMarkerClickListener.html" title="com.amap.api.maps中的接口">AMap.OnMarkerClickListener</a>&nbsp;listener)</pre>
<div class="block">设置marker点击事件监听接口。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>listener</code> - marker点击事件监听接口。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.1</dd>
</dl>
</li>
</ul>
<a name="setOnPolylineClickListener-com.amap.api.maps.AMap.OnPolylineClickListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setOnPolylineClickListener</h4>
<pre>public final&nbsp;void&nbsp;setOnPolylineClickListener(<a href="../../../../com/amap/api/maps/AMap.OnPolylineClickListener.html" title="com.amap.api.maps中的接口">AMap.OnPolylineClickListener</a>&nbsp;listener)</pre>
<div class="block">设置polyline点击事件监听接口。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>listener</code> - polyline点击事件监听接口。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.1.1</dd>
</dl>
</li>
</ul>
<a name="setOnMarkerDragListener-com.amap.api.maps.AMap.OnMarkerDragListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setOnMarkerDragListener</h4>
<pre>public final&nbsp;void&nbsp;setOnMarkerDragListener(<a href="../../../../com/amap/api/maps/AMap.OnMarkerDragListener.html" title="com.amap.api.maps中的接口">AMap.OnMarkerDragListener</a>&nbsp;listener)</pre>
<div class="block">marker拖动事件监听接口</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>listener</code> - marker拖动事件监听接口。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.1</dd>
</dl>
</li>
</ul>
<a name="setOnInfoWindowClickListener-com.amap.api.maps.AMap.OnInfoWindowClickListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setOnInfoWindowClickListener</h4>
<pre>public final&nbsp;void&nbsp;setOnInfoWindowClickListener(<a href="../../../../com/amap/api/maps/AMap.OnInfoWindowClickListener.html" title="com.amap.api.maps中的接口">AMap.OnInfoWindowClickListener</a>&nbsp;listener)</pre>
<div class="block">设置marker的信息窗口点击事件监听接口。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>listener</code> - marker的信息窗口点击事件监听接口。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="setInfoWindowAdapter-com.amap.api.maps.AMap.InfoWindowAdapter-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setInfoWindowAdapter</h4>
<pre>public final&nbsp;void&nbsp;setInfoWindowAdapter(<a href="../../../../com/amap/api/maps/AMap.InfoWindowAdapter.html" title="com.amap.api.maps中的接口">AMap.InfoWindowAdapter</a>&nbsp;adapter)</pre>
<div class="block">设置marker的信息窗口定制接口。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>adapter</code> - marker的信息窗口定制接口。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="setOnMapLoadedListener-com.amap.api.maps.AMap.OnMapLoadedListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setOnMapLoadedListener</h4>
<pre>public final&nbsp;void&nbsp;setOnMapLoadedListener(<a href="../../../../com/amap/api/maps/AMap.OnMapLoadedListener.html" title="com.amap.api.maps中的接口">AMap.OnMapLoadedListener</a>&nbsp;listener)</pre>
<div class="block">设置地图加载完成监听接口</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>listener</code> - 地图加载完成监听接口。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.1</dd>
</dl>
</li>
</ul>
<a name="setOnIndoorBuildingActiveListener-com.amap.api.maps.AMap.OnIndoorBuildingActiveListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setOnIndoorBuildingActiveListener</h4>
<pre>public final&nbsp;void&nbsp;setOnIndoorBuildingActiveListener(<a href="../../../../com/amap/api/maps/AMap.OnIndoorBuildingActiveListener.html" title="com.amap.api.maps中的接口">AMap.OnIndoorBuildingActiveListener</a>&nbsp;listener)</pre>
<div class="block">设置室内地图状态监听接口</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>listener</code> - 室内地图状态监听接口。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.3.0</dd>
</dl>
</li>
</ul>
<a name="setOnMultiPointClickListener-com.amap.api.maps.AMap.OnMultiPointClickListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setOnMultiPointClickListener</h4>
<pre>public&nbsp;void&nbsp;setOnMultiPointClickListener(<a href="../../../../com/amap/api/maps/AMap.OnMultiPointClickListener.html" title="com.amap.api.maps中的接口">AMap.OnMultiPointClickListener</a>&nbsp;listener)</pre>
<div class="block">设置海量点单击事件监听</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>listener</code> - 海量点单击事件监听</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>5.1.0</dd>
</dl>
</li>
</ul>
<a name="getMapPrintScreen-com.amap.api.maps.AMap.onMapPrintScreenListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMapPrintScreen</h4>
<pre>public&nbsp;void&nbsp;getMapPrintScreen(<a href="../../../../com/amap/api/maps/AMap.onMapPrintScreenListener.html" title="com.amap.api.maps中的接口">AMap.onMapPrintScreenListener</a>&nbsp;listener)</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;<span class="deprecationComment">建议使用<a href="../../../../com/amap/api/maps/AMap.html#getMapScreenShot-com.amap.api.maps.AMap.OnMapScreenShotListener-"><code>AMap.getMapScreenShot(OnMapScreenShotListener)</code></a></span></div>
<div class="block">发起地图截屏请求。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>listener</code> - 地图截屏监听接口。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.2</dd>
</dl>
</li>
</ul>
<a name="getMapScreenShot-com.amap.api.maps.AMap.OnMapScreenShotListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMapScreenShot</h4>
<pre>public&nbsp;void&nbsp;getMapScreenShot(<a href="../../../../com/amap/api/maps/AMap.OnMapScreenShotListener.html" title="com.amap.api.maps中的接口">AMap.OnMapScreenShotListener</a>&nbsp;listener)</pre>
<div class="block">发起地图截屏请求。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>listener</code> - 地图截屏监听接口。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="getMapRegionSnapshot-com.amap.api.maps.model.LatLng-com.amap.api.maps.model.LatLng-android.util.Size-com.amap.api.maps.AMap.OnMapSnapshotListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMapRegionSnapshot</h4>
<pre>public&nbsp;void&nbsp;getMapRegionSnapshot(<a href="../../../../com/amap/api/maps/model/LatLng.html" title="com.amap.api.maps.model中的类">LatLng</a>&nbsp;topLeft,
                                 <a href="../../../../com/amap/api/maps/model/LatLng.html" title="com.amap.api.maps.model中的类">LatLng</a>&nbsp;topRight,
                                 android.util.Size&nbsp;regionPixels,
                                 <a href="../../../../com/amap/api/maps/AMap.OnMapSnapshotListener.html" title="com.amap.api.maps中的接口">AMap.OnMapSnapshotListener</a>&nbsp;listener)</pre>
</li>
</ul>
<a name="getScalePerPixel--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getScalePerPixel</h4>
<pre>public&nbsp;float&nbsp;getScalePerPixel()</pre>
<div class="block">获取当前缩放级别下，地图上1像素点对应的长度，单位米。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>当前缩放级别下，地图上1像素点对应的长度，单位米。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.2</dd>
</dl>
</li>
</ul>
<a name="runOnDrawFrame--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>runOnDrawFrame</h4>
<pre>public&nbsp;void&nbsp;runOnDrawFrame()</pre>
<div class="block">触发地图立即刷新。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="removecache--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removecache</h4>
<pre>public&nbsp;void&nbsp;removecache()</pre>
<div class="block">删除地图缓存。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.3.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../com/amap/api/maps/AMap.html#removecache-com.amap.api.maps.AMap.OnCacheRemoveListener-"><code>AMap.removecache(OnCacheRemoveListener)</code></a></dd>
</dl>
</li>
</ul>
<a name="removecache-com.amap.api.maps.AMap.OnCacheRemoveListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removecache</h4>
<pre>public&nbsp;void&nbsp;removecache(<a href="../../../../com/amap/api/maps/AMap.OnCacheRemoveListener.html" title="com.amap.api.maps中的接口">AMap.OnCacheRemoveListener</a>&nbsp;onCacheRemoveListener)</pre>
<div class="block">删除地图缓存。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>onCacheRemoveListener</code> - 缓存数据清除监听接口。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.3.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../com/amap/api/maps/AMap.html#removecache--"><code>AMap.removecache()</code></a></dd>
</dl>
</li>
</ul>
<a name="setCustomRenderer-com.amap.api.maps.CustomRenderer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCustomRenderer</h4>
<pre>public&nbsp;void&nbsp;setCustomRenderer(<a href="../../../../com/amap/api/maps/CustomRenderer.html" title="com.amap.api.maps中的接口">CustomRenderer</a>&nbsp;render)</pre>
<div class="block">设置地图在初始化及每一帧绘制时的回调接口，该接口在OpenGL线程中调用。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>render</code> - 地图在初始化及每一帧绘制时的回调接口。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.3</dd>
</dl>
</li>
</ul>
<a name="setPointToCenter-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPointToCenter</h4>
<pre>public&nbsp;void&nbsp;setPointToCenter(int&nbsp;x,
                             int&nbsp;y)</pre>
<div class="block">设置屏幕上的某个像素点为地图中心点。
 <p>使用该方法设置后，地图将以所设置的屏幕坐标点为中心进行旋转、倾斜。同时， <a href="../../../../com/amap/api/maps/AMap.html#moveCamera-com.amap.api.maps.CameraUpdate-"><code>AMap.moveCamera(CameraUpdate)</code></a> 方法也将会以此坐标点为中心进行设置。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>x</code> - 屏幕像素点x轴坐标。</dd>
<dd><code>y</code> - 屏幕像素点y轴坐标。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>v2.2.0</dd>
</dl>
</li>
</ul>
<a name="setMapTextZIndex-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMapTextZIndex</h4>
<pre>public final&nbsp;void&nbsp;setMapTextZIndex(int&nbsp;x)</pre>
<div class="block">设置地图底图文字标注的层级指数，默认为0，用来比较覆盖物（polyline、polygon、circle等）的zIndex。
 如果覆盖物的zIndex大于底图文字标注的zIndex，则覆盖物会在文字标注上层绘制，否则，覆盖物在文字标注下面绘制。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>x</code> - 为地图底图文字标注的层级指数，默认为0。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>v2.2.0</dd>
</dl>
</li>
</ul>
<a name="setLoadOfflineData-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLoadOfflineData</h4>
<pre>public final&nbsp;void&nbsp;setLoadOfflineData(boolean&nbsp;enabled)</pre>
<div class="block">重新加载离线地图数据。
 <p>下载完离线数据之后如果要直接显示，可以调用此接口先关闭再打开，从而达到重新加载地图数据的效果。</p></div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>enabled</code> - true 打开地图数据库；false 关闭地图数据库。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.1.0</dd>
</dl>
</li>
</ul>
<a name="getMapTextZIndex--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMapTextZIndex</h4>
<pre>public final&nbsp;int&nbsp;getMapTextZIndex()</pre>
<div class="block">得到地图底图文字标注的层级指数。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>地图底图文字标注的层级指数。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>v2.2.0</dd>
</dl>
</li>
</ul>
<a name="getVersion--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVersion</h4>
<pre>@Deprecated
public static&nbsp;java.lang.String&nbsp;getVersion()</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;</div>
<div class="block">返回地图SDK的版本号。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>地图SDK的版本号。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.4</dd>
</dl>
</li>
</ul>
<a name="reloadMap--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>reloadMap</h4>
<pre>public&nbsp;void&nbsp;reloadMap()</pre>
<div class="block">重新加载地图引擎，即调用此接口时会重新加载底图数据，覆盖物不受影响。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.1.1</dd>
</dl>
</li>
</ul>
<a name="setIndoorBuildingInfo-com.amap.api.maps.model.IndoorBuildingInfo-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setIndoorBuildingInfo</h4>
<pre>public&nbsp;void&nbsp;setIndoorBuildingInfo(<a href="../../../../com/amap/api/maps/model/IndoorBuildingInfo.html" title="com.amap.api.maps.model中的类">IndoorBuildingInfo</a>&nbsp;indoorBuildingInfo)</pre>
<div class="block">室内地图楼层控制接口，通过此接口可以控制某个室内地图显示的楼层。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>indoorBuildingInfo</code> - indoorBuildingInfo 对象，它定义了室内地图属性,详情<a href="../../../../com/amap/api/maps/model/IndoorBuildingInfo.html" title="com.amap.api.maps.model中的类"><code>IndoorBuildingInfo</code></a>。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.3.0</dd>
</dl>
</li>
</ul>
<a name="setAMapGestureListener-com.amap.api.maps.model.AMapGestureListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAMapGestureListener</h4>
<pre>public&nbsp;void&nbsp;setAMapGestureListener(<a href="../../../../com/amap/api/maps/model/AMapGestureListener.html" title="com.amap.api.maps.model中的接口">AMapGestureListener</a>&nbsp;aMapGestureListener)</pre>
<div class="block">设置手势监听接口</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>aMapGestureListener</code> - 手势监听 详情见 <a href="../../../../com/amap/api/maps/model/AMapGestureListener.html" title="com.amap.api.maps.model中的接口"><code>AMapGestureListener</code></a></dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>4.0.0</dd>
</dl>
</li>
</ul>
<a name="setMaxZoomLevel-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMaxZoomLevel</h4>
<pre>public&nbsp;void&nbsp;setMaxZoomLevel(float&nbsp;zoomLevel)</pre>
<div class="block">设置地图最大缩放级别 缩放级别范围为[3, 20],超出范围将按最大级别计算 。
 注：只有在有室内地图显示的情况下最大级别为20，否则最大级别为19。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>zoomLevel</code> - 最大缩放等级</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>4.1.0</dd>
</dl>
</li>
</ul>
<a name="setMinZoomLevel-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMinZoomLevel</h4>
<pre>public&nbsp;void&nbsp;setMinZoomLevel(float&nbsp;zoomLevel)</pre>
<div class="block">设置最小缩放级别 缩放级别范围为[3, 20],超出范围将按最小级别计算</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>zoomLevel</code> - 最小缩放等级</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>4.1.0</dd>
</dl>
</li>
</ul>
<a name="resetMinMaxZoomPreference--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>resetMinMaxZoomPreference</h4>
<pre>public&nbsp;void&nbsp;resetMinMaxZoomPreference()</pre>
<div class="block">重置最小及最大缩放级别 将恢复最小级别为3，最大级别为20 。
 注：只有在有室内地图显示的情况下最大级别为20，否则最大级别为19。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>4.1.0</dd>
</dl>
</li>
</ul>
<a name="setMapStatusLimits-com.amap.api.maps.model.LatLngBounds-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMapStatusLimits</h4>
<pre>public&nbsp;void&nbsp;setMapStatusLimits(<a href="../../../../com/amap/api/maps/model/LatLngBounds.html" title="com.amap.api.maps.model中的类">LatLngBounds</a>&nbsp;latLngBounds)</pre>
<div class="block">设置地图显示范围，无论如何操作地图，显示区域都不能超过该矩形区域。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>latLngBounds</code> - 通过指定的两个经纬度坐标（左下、右上）构建的一个矩形区域，详情参见<a href="../../../../com/amap/api/maps/model/LatLngBounds.html" title="com.amap.api.maps.model中的类"><code>LatLngBounds</code></a></dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>4.1.0</dd>
</dl>
</li>
</ul>
<a name="setMapCustomEnable-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMapCustomEnable</h4>
<pre>public&nbsp;void&nbsp;setMapCustomEnable(boolean&nbsp;enable)</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;<span class="deprecationComment">该方法已无效，建议到官网lbs.amap.com更新样式文件并使用 <a href="../../../../com/amap/api/maps/AMap.html#setCustomMapStyle-com.amap.api.maps.model.CustomMapStyleOptions-"><code>AMap.setCustomMapStyle(CustomMapStyleOptions)</code></a> )} 替换</span></div>
<div class="block">设置是否开启底图自定义样式。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>enable</code> - 是否开启底图自定义样式。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>5.0.0</dd>
</dl>
</li>
</ul>
<a name="setCustomMapStylePath-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCustomMapStylePath</h4>
<pre>public&nbsp;void&nbsp;setCustomMapStylePath(java.lang.String&nbsp;stylePath)</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;<span class="deprecationComment">该方法已无效，建议到官网lbs.amap.com更新样式文件并使用 <a href="../../../../com/amap/api/maps/AMap.html#setCustomMapStyle-com.amap.api.maps.model.CustomMapStyleOptions-"><code>AMap.setCustomMapStyle(CustomMapStyleOptions)</code></a> )} 替换</span></div>
<div class="block">设置底图自定义样式对应配置文件路径。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>stylePath</code> - 底图自定义样式对应配置文件路径。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>5.0.0</dd>
</dl>
</li>
</ul>
<a name="setCustomMapStyle-com.amap.api.maps.model.CustomMapStyleOptions-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCustomMapStyle</h4>
<pre>public&nbsp;void&nbsp;setCustomMapStyle(<a href="../../../../com/amap/api/maps/model/CustomMapStyleOptions.html" title="com.amap.api.maps.model中的类">CustomMapStyleOptions</a>&nbsp;options)</pre>
<div class="block">设置底图自定义样式对应配置文件路径。
 可以支持分级样式配置，如控制不同级别显示不同的颜色

 自6.6.0开始使用新版样式，旧版样式无法在新版接口<a href="../../../../com/amap/api/maps/AMap.html#setCustomMapStyle-com.amap.api.maps.model.CustomMapStyleOptions-"><code>AMap.setCustomMapStyle(CustomMapStyleOptions)</code></a>中使用，请到官网(lbs.amap.com)更新新版样式文件</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>options</code> - 底图自定义样式对应配置,包含样式文件、纹理路径等。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.6.0</dd>
</dl>
</li>
</ul>
<a name="setCustomMapStyleID-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCustomMapStyleID</h4>
<pre>public&nbsp;void&nbsp;setCustomMapStyleID(java.lang.String&nbsp;styleID)</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;<span class="deprecationComment">该方法已无效，建议到官网lbs.amap.com更新样式文件并使用 <a href="../../../../com/amap/api/maps/AMap.html#setCustomMapStyle-com.amap.api.maps.model.CustomMapStyleOptions-"><code>AMap.setCustomMapStyle(CustomMapStyleOptions)</code></a> )} 替换</span></div>
<div class="block">设置底图自定义样式对应的styleID，id从官网获取。
 设置了之后官网修改会同步展示到客户端（客户端下次启动时生效）</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>styleID</code> - 样式id</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>5.7.0</dd>
</dl>
</li>
</ul>
<a name="getMapContentApprovalNumber--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMapContentApprovalNumber</h4>
<pre>public&nbsp;java.lang.String&nbsp;getMapContentApprovalNumber()</pre>
<div class="block">获取地图审图号（普通地图）<br>
 <font color="red">任何使用高德地图API调用地图服务的应用必须在其应用中对外透出审图号</font><br>
 如高德地图在"关于"中体现</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>普通矢量地图审图号</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>5.4.0</dd>
</dl>
</li>
</ul>
<a name="getSatelliteImageApprovalNumber--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSatelliteImageApprovalNumber</h4>
<pre>public&nbsp;java.lang.String&nbsp;getSatelliteImageApprovalNumber()</pre>
<div class="block">获取地图审图号（卫星地图）<br>
 <font color="red">任何使用高德地图API调用地图服务的应用必须在其应用中对外透出审图号</font><br>
 如高德地图在"关于"中体现</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>卫星地图审图号</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>5.4.0</dd>
</dl>
</li>
</ul>
<a name="getTerrainApprovalNumber--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTerrainApprovalNumber</h4>
<pre>public&nbsp;java.lang.String&nbsp;getTerrainApprovalNumber()</pre>
<div class="block">获取地图审图号 （地形图）<br>
 <font color="red">任何使用高德地图API调用地图服务的应用必须在其应用中对外透出审图号</font><br>
 如高德地图在"关于"中体现</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>地形图审图号</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.1.0</dd>
</dl>
</li>
</ul>
<a name="setTouchPoiEnable-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTouchPoiEnable</h4>
<pre>public&nbsp;void&nbsp;setTouchPoiEnable(boolean&nbsp;touchPoiEnable)</pre>
<div class="block">设置地图POI是否允许点击。<br>
 默认情况下单击地铁站，地铁路线会高亮，如果关闭了poi单击，则地铁站不会被单击，地铁路线也不会高亮</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>touchPoiEnable</code> - true： 表示允许点击，为默认值； false： 不允许点击</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>7.0.0</dd>
</dl>
</li>
</ul>
<a name="isTouchPoiEnable--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isTouchPoiEnable</h4>
<pre>public&nbsp;boolean&nbsp;isTouchPoiEnable()</pre>
<div class="block">获取地图POI是否允许点击。<br></div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>true： 表示允许点击，为默认值； false： 不允许点击</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>7.0.0</dd>
</dl>
</li>
</ul>
<a name="setConstructingRoadEnable-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setConstructingRoadEnable</h4>
<pre>public&nbsp;void&nbsp;setConstructingRoadEnable(boolean&nbsp;enabled)</pre>
<div class="block">设置在建道路图层是否显示,默认不显示。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>enabled</code> - true： 表示显示；false： 不显示，为默认值</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>7.7.0</dd>
</dl>
</li>
</ul>
<a name="addAMapAppResourceListener-com.amap.api.maps.AMap.AMapAppResourceRequestListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addAMapAppResourceListener</h4>
<pre>public&nbsp;void&nbsp;addAMapAppResourceListener(<a href="../../../../com/amap/api/maps/AMap.AMapAppResourceRequestListener.html" title="com.amap.api.maps中的接口">AMap.AMapAppResourceRequestListener</a>&nbsp;listen)</pre>
</li>
</ul>
<a name="removeAMapAppResourceListener-com.amap.api.maps.AMap.AMapAppResourceRequestListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removeAMapAppResourceListener</h4>
<pre>public&nbsp;void&nbsp;removeAMapAppResourceListener(<a href="../../../../com/amap/api/maps/AMap.AMapAppResourceRequestListener.html" title="com.amap.api.maps中的接口">AMap.AMapAppResourceRequestListener</a>&nbsp;listener)</pre>
</li>
</ul>
<a name="hideBuildings-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>hideBuildings</h4>
<pre>public&nbsp;int&nbsp;hideBuildings(java.util.List&lt;<a href="../../../../com/amap/api/maps/model/LatLng.html" title="com.amap.api.maps.model中的类">LatLng</a>&gt;&nbsp;polygon)</pre>
<div class="block">隐藏建筑物</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>polygon</code> - 围栏的经纬度信息 围栏的size（需 >= 3 否则无法构成围栏）</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>隐藏成功返回当前的operationId（>= 0） 失败返回-1</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.6.0</dd>
</dl>
</li>
</ul>
<a name="showHideBuildings-int-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>showHideBuildings</h4>
<pre>public&nbsp;void&nbsp;showHideBuildings(int&nbsp;operateId)</pre>
<div class="block">显示隐藏的建筑物</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>operateId</code> - 操作Id（隐藏建筑物接口的返回值</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>9.6.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/AMap.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个类</li>
<li><a href="../../../../com/amap/api/maps/AMap.AMapAppResourceRequestListener.html" title="com.amap.api.maps中的接口"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/amap/api/maps/AMap.html" target="_top">框架</a></li>
<li><a href="AMap.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li><a href="#nested.class.summary">嵌套</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">字段</a>&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li><a href="#field.detail">字段</a>&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
