<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>com.amap.api.maps</title>
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="com.amap.api.maps";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../overview-summary.html">概览</a></li>
<li class="navBarCell1Rev">程序包</li>
<li>类</li>
<li><a href="package-use.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个程序包</li>
<li><a href="../../../../com/amap/api/maps/model/package-summary.html">下一个程序包</a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/amap/api/maps/package-summary.html" target="_top">框架</a></li>
<li><a href="package-summary.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 title="程序包" class="title">程序包&nbsp;com.amap.api.maps</h1>
<div class="docSummary">
<div class="block">
地图显示包，帮助您在Android应用程序中显示高德地图，并且支持地图交互手势的功能。</div>
</div>
<p>请参阅:&nbsp;<a href="#package.description">说明</a></p>
</div>
<div class="contentContainer">
<ul class="blockList">
<li class="blockList">
<table class="typeSummary" border="0" cellpadding="3" cellspacing="0" summary="接口概要表, 列表接口和解释">
<caption><span>接口概要</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">接口</th>
<th class="colLast" scope="col">说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/amap/api/maps/AMap.AMapAppResourceRequestListener.html" title="com.amap.api.maps中的接口">AMap.AMapAppResourceRequestListener</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/amap/api/maps/AMap.CancelableCallback.html" title="com.amap.api.maps中的接口">AMap.CancelableCallback</a></td>
<td class="colLast">
<div class="block">在<a href="../../../../com/amap/api/maps/AMap.html#animateCamera-com.amap.api.maps.CameraUpdate-com.amap.api.maps.AMap.CancelableCallback-"><code>AMap.animateCamera(CameraUpdate, CancelableCallback)</code></a>设置一个CancelableCallback，用来监听该CameraUpdate是否执行完成或者被中断。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/amap/api/maps/AMap.ImageInfoWindowAdapter.html" title="com.amap.api.maps中的接口">AMap.ImageInfoWindowAdapter</a></td>
<td class="colLast">
<div class="block">用来实现marker与对应InfoWindow同步移动<br>
 默认情况下，InfoWindow是一个View， 拖动地图的时候由于View 布局较慢，会有延迟的效果。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/amap/api/maps/AMap.InfoWindowAdapter.html" title="com.amap.api.maps中的接口">AMap.InfoWindowAdapter</a></td>
<td class="colLast">
<div class="block">用来定制marker的信息窗口<br>
 默认情况下，当单击某个marker时，如果该marker的Title和Snippet不为空，则会触发getInfoWindow和getInfoContents回调。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/amap/api/maps/AMap.OnCacheRemoveListener.html" title="com.amap.api.maps中的接口">AMap.OnCacheRemoveListener</a></td>
<td class="colLast">
<div class="block">缓存数据清除监听接口</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/amap/api/maps/AMap.OnCameraChangeListener.html" title="com.amap.api.maps中的接口">AMap.OnCameraChangeListener</a></td>
<td class="colLast">
<div class="block">地图状态发生变化的监听接口。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/amap/api/maps/AMap.OnIndoorBuildingActiveListener.html" title="com.amap.api.maps中的接口">AMap.OnIndoorBuildingActiveListener</a></td>
<td class="colLast">
<div class="block">室内地图状态监听接口。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/amap/api/maps/AMap.OnInfoWindowClickListener.html" title="com.amap.api.maps中的接口">AMap.OnInfoWindowClickListener</a></td>
<td class="colLast">
<div class="block">marker的信息窗口点击事件监听接口。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/amap/api/maps/AMap.OnMapClickListener.html" title="com.amap.api.maps中的接口">AMap.OnMapClickListener</a></td>
<td class="colLast">
<div class="block">地图点击事件监听接口。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/amap/api/maps/AMap.OnMapLoadedListener.html" title="com.amap.api.maps中的接口">AMap.OnMapLoadedListener</a></td>
<td class="colLast">
<div class="block">地图加载完成监听接口</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/amap/api/maps/AMap.OnMapLongClickListener.html" title="com.amap.api.maps中的接口">AMap.OnMapLongClickListener</a></td>
<td class="colLast">
<div class="block">地图长按事件监听接口。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/amap/api/maps/AMap.onMapPrintScreenListener.html" title="com.amap.api.maps中的接口">AMap.onMapPrintScreenListener</a></td>
<td class="colLast">已过时
<div class="block"><span class="deprecationComment">建议使用 <a href="../../../../com/amap/api/maps/AMap.OnMapScreenShotListener.html" title="com.amap.api.maps中的接口"><code>AMap.OnMapScreenShotListener</code></a></span></div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/amap/api/maps/AMap.OnMapScreenShotListener.html" title="com.amap.api.maps中的接口">AMap.OnMapScreenShotListener</a></td>
<td class="colLast">
<div class="block">地图截屏监听接口 。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/amap/api/maps/AMap.OnMapSnapshotListener.html" title="com.amap.api.maps中的接口">AMap.OnMapSnapshotListener</a></td>
<td class="colLast">
<div class="block">地图区域图异步返回接口 。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/amap/api/maps/AMap.OnMapTouchListener.html" title="com.amap.api.maps中的接口">AMap.OnMapTouchListener</a></td>
<td class="colLast">
<div class="block">地图触摸事件监听接口。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/amap/api/maps/AMap.OnMarkerClickListener.html" title="com.amap.api.maps中的接口">AMap.OnMarkerClickListener</a></td>
<td class="colLast">
<div class="block">marker点击事件监听接口。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/amap/api/maps/AMap.OnMarkerDragListener.html" title="com.amap.api.maps中的接口">AMap.OnMarkerDragListener</a></td>
<td class="colLast">
<div class="block">marker拖动事件监听接口。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/amap/api/maps/AMap.OnMultiPointClickListener.html" title="com.amap.api.maps中的接口">AMap.OnMultiPointClickListener</a></td>
<td class="colLast">
<div class="block">海量点中某一点被点击时的回调。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/amap/api/maps/AMap.OnMyLocationChangeListener.html" title="com.amap.api.maps中的接口">AMap.OnMyLocationChangeListener</a></td>
<td class="colLast">
<div class="block">用户定位信息监听接口。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/amap/api/maps/AMap.OnPOIClickListener.html" title="com.amap.api.maps中的接口">AMap.OnPOIClickListener</a></td>
<td class="colLast">
<div class="block">地图底图poi点击事件监听接口。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/amap/api/maps/AMap.OnPolylineClickListener.html" title="com.amap.api.maps中的接口">AMap.OnPolylineClickListener</a></td>
<td class="colLast">
<div class="block">polyline点击事件监听接口。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/amap/api/maps/CustomRenderer.html" title="com.amap.api.maps中的接口">CustomRenderer</a></td>
<td class="colLast">
<div class="block">定义了一个地图在初始化及每一帧绘制时的回调接口。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/amap/api/maps/LocationSource.html" title="com.amap.api.maps中的接口">LocationSource</a></td>
<td class="colLast">
<div class="block">定义了一个定位源，为地图提供定位数据。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/amap/api/maps/LocationSource.OnLocationChangedListener.html" title="com.amap.api.maps中的接口">LocationSource.OnLocationChangedListener</a></td>
<td class="colLast">
<div class="block">位置改变的监听接口。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/amap/api/maps/SwipeDismissTouchListener.DismissCallbacks.html" title="com.amap.api.maps中的接口">SwipeDismissTouchListener.DismissCallbacks</a></td>
<td class="colLast">
<div class="block">The callback interface used by <a href="../../../../com/amap/api/maps/SwipeDismissTouchListener.html" title="com.amap.api.maps中的类"><code>SwipeDismissTouchListener</code></a> to
 inform its client about a successful dismissal of the view for which it
 was created.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/amap/api/maps/WearMapView.OnDismissCallback.html" title="com.amap.api.maps中的接口">WearMapView.OnDismissCallback</a></td>
<td class="colLast">
<div class="block">手势滑动监听</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="typeSummary" border="0" cellpadding="3" cellspacing="0" summary="类概要表, 列表类和解释">
<caption><span>类概要</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">类</th>
<th class="colLast" scope="col">说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></td>
<td class="colLast">
<div class="block">定义AMap 地图对象的操作方法与接口。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/amap/api/maps/AMapOptions.html" title="com.amap.api.maps中的类">AMapOptions</a></td>
<td class="colLast">
<div class="block">MapView 初始化选项。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/amap/api/maps/AMapUtils.html" title="com.amap.api.maps中的类">AMapUtils</a></td>
<td class="colLast">
<div class="block">AMap辅助工具类，包含计算距离、面积、调起高德地图APP进行导航等系列功能。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/amap/api/maps/CameraUpdate.html" title="com.amap.api.maps中的类">CameraUpdate</a></td>
<td class="colLast">
<div class="block">描述地图状态将要发生的变化。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/amap/api/maps/CameraUpdateFactory.html" title="com.amap.api.maps中的类">CameraUpdateFactory</a></td>
<td class="colLast">
<div class="block">创建CameraUpdate 对象,用来改变地图状态。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/amap/api/maps/CoordinateConverter.html" title="com.amap.api.maps中的类">CoordinateConverter</a></td>
<td class="colLast">
<div class="block">坐标转换工具类</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/amap/api/maps/MapFragment.html" title="com.amap.api.maps中的类">MapFragment</a></td>
<td class="colLast">
<div class="block">MapFragment 类, 管理地图生命周期。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/amap/api/maps/MapsInitializer.html" title="com.amap.api.maps中的类">MapsInitializer</a></td>
<td class="colLast">
<div class="block">初始化 SDK context 全局变量，指定 sdcard 路径，设置鉴权所需的KEY。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/amap/api/maps/MapView.html" title="com.amap.api.maps中的类">MapView</a></td>
<td class="colLast">
<div class="block">一个显示地图的视图（View），它负责从服务端获取地图数据，它将会捕捉屏幕触控手势事件。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/amap/api/maps/Projection.html" title="com.amap.api.maps中的类">Projection</a></td>
<td class="colLast">
<div class="block">Projection接口用于屏幕像素点坐标系统和地球表面经纬度点坐标系统之间的变换。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/amap/api/maps/SupportMapFragment.html" title="com.amap.api.maps中的类">SupportMapFragment</a></td>
<td class="colLast">
<div class="block">SupportMapFragment 类, 管理地图生命周期。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/amap/api/maps/TextureMapFragment.html" title="com.amap.api.maps中的类">TextureMapFragment</a></td>
<td class="colLast">
<div class="block">TextureMapFragment 类, 管理地图生命周期，android4.0以上版本使用。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/amap/api/maps/TextureMapView.html" title="com.amap.api.maps中的类">TextureMapView</a></td>
<td class="colLast">
<div class="block">一个显示地图的视图（View），它负责从服务端获取地图数据，它将会捕捉屏幕触控手势事件。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/amap/api/maps/TextureSupportMapFragment.html" title="com.amap.api.maps中的类">TextureSupportMapFragment</a></td>
<td class="colLast">
<div class="block">TextureSupportMapFragment 类, 管理地图生命周期。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/amap/api/maps/UiSettings.html" title="com.amap.api.maps中的类">UiSettings</a></td>
<td class="colLast">
<div class="block">地图内置UI及手势控制器。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/amap/api/maps/WearMapView.html" title="com.amap.api.maps中的类">WearMapView</a></td>
<td class="colLast">
<div class="block">一个显示手表地图的视图（View）。</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="typeSummary" border="0" cellpadding="3" cellspacing="0" summary="枚举概要表, 列表枚举和解释">
<caption><span>枚举概要</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">枚举</th>
<th class="colLast" scope="col">说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/amap/api/maps/CoordinateConverter.CoordType.html" title="com.amap.api.maps中的枚举">CoordinateConverter.CoordType</a></td>
<td class="colLast">&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="typeSummary" border="0" cellpadding="3" cellspacing="0" summary="异常错误概要表, 列表异常错误和解释">
<caption><span>异常错误概要</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">异常错误</th>
<th class="colLast" scope="col">说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/amap/api/maps/AMapException.html" title="com.amap.api.maps中的类">AMapException</a></td>
<td class="colLast">&nbsp;</td>
</tr>
</tbody>
</table>
</li>
</ul>
<a name="package.description">
<!--   -->
</a>
<h2 title="程序包com.amap.api.maps的说明">程序包com.amap.api.maps的说明</h2>
<div class="block"><p>
地图显示包，帮助您在Android应用程序中显示高德地图，并且支持地图交互手势的功能。
</p></div>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../overview-summary.html">概览</a></li>
<li class="navBarCell1Rev">程序包</li>
<li>类</li>
<li><a href="package-use.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个程序包</li>
<li><a href="../../../../com/amap/api/maps/model/package-summary.html">下一个程序包</a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/amap/api/maps/package-summary.html" target="_top">框架</a></li>
<li><a href="package-summary.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
