<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>CameraUpdateFactory</title>
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="CameraUpdateFactory";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":9,"i3":9,"i4":9,"i5":9,"i6":9,"i7":9,"i8":9,"i9":9,"i10":9,"i11":9,"i12":9,"i13":9,"i14":9};
var tabs = {65535:["t0","所有方法"],1:["t1","静态方法"],8:["t4","具体方法"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/CameraUpdateFactory.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/amap/api/maps/CameraUpdate.html" title="com.amap.api.maps中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../com/amap/api/maps/CoordinateConverter.html" title="com.amap.api.maps中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/amap/api/maps/CameraUpdateFactory.html" target="_top">框架</a></li>
<li><a href="CameraUpdateFactory.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.amap.api.maps</div>
<h2 title="类 CameraUpdateFactory" class="title">类 CameraUpdateFactory</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.amap.api.maps.CameraUpdateFactory</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public final class <span class="typeNameLabel">CameraUpdateFactory</span>
extends java.lang.Object</pre>
<div class="block">创建CameraUpdate 对象,用来改变地图状态。<br>
 调用 <a href="../../../../com/amap/api/maps/AMap.html#animateCamera-com.amap.api.maps.CameraUpdate-"><code>AMap.animateCamera(CameraUpdate)</code></a> or <a href="../../../../com/amap/api/maps/AMap.html#moveCamera-com.amap.api.maps.CameraUpdate-"><code>AMap.moveCamera(CameraUpdate)</code></a>。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/CameraUpdateFactory.html#CameraUpdateFactory--">CameraUpdateFactory</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>所有方法</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">静态方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">具体方法</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/amap/api/maps/CameraUpdate.html" title="com.amap.api.maps中的类">CameraUpdate</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/CameraUpdateFactory.html#changeBearing-float-">changeBearing</a></span>(float&nbsp;bearing)</code>
<div class="block">设置地图的旋转角度。</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/amap/api/maps/CameraUpdate.html" title="com.amap.api.maps中的类">CameraUpdate</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/CameraUpdateFactory.html#changeLatLng-com.amap.api.maps.model.LatLng-">changeLatLng</a></span>(<a href="../../../../com/amap/api/maps/model/LatLng.html" title="com.amap.api.maps.model中的类">LatLng</a>&nbsp;latLng)</code>
<div class="block">设置地图的中心点。</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/amap/api/maps/CameraUpdate.html" title="com.amap.api.maps中的类">CameraUpdate</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/CameraUpdateFactory.html#changeTilt-float-">changeTilt</a></span>(float&nbsp;tilt)</code>
<div class="block">设置地图倾斜度。</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/amap/api/maps/CameraUpdate.html" title="com.amap.api.maps中的类">CameraUpdate</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/CameraUpdateFactory.html#newCameraPosition-com.amap.api.maps.model.CameraPosition-">newCameraPosition</a></span>(<a href="../../../../com/amap/api/maps/model/CameraPosition.html" title="com.amap.api.maps.model中的类">CameraPosition</a>&nbsp;cameraPosition)</code>
<div class="block">给地图设置一个新的状态。</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/amap/api/maps/CameraUpdate.html" title="com.amap.api.maps中的类">CameraUpdate</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/CameraUpdateFactory.html#newLatLng-com.amap.api.maps.model.LatLng-">newLatLng</a></span>(<a href="../../../../com/amap/api/maps/model/LatLng.html" title="com.amap.api.maps.model中的类">LatLng</a>&nbsp;latLng)</code>
<div class="block">设置地图的中心点。</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/amap/api/maps/CameraUpdate.html" title="com.amap.api.maps中的类">CameraUpdate</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/CameraUpdateFactory.html#newLatLngBounds-com.amap.api.maps.model.LatLngBounds-int-">newLatLngBounds</a></span>(<a href="../../../../com/amap/api/maps/model/LatLngBounds.html" title="com.amap.api.maps.model中的类">LatLngBounds</a>&nbsp;bounds,
               int&nbsp;padding)</code>
<div class="block">设置显示在规定屏幕范围内的地图经纬度范围。</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/amap/api/maps/CameraUpdate.html" title="com.amap.api.maps中的类">CameraUpdate</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/CameraUpdateFactory.html#newLatLngBounds-com.amap.api.maps.model.LatLngBounds-int-int-int-">newLatLngBounds</a></span>(<a href="../../../../com/amap/api/maps/model/LatLngBounds.html" title="com.amap.api.maps.model中的类">LatLngBounds</a>&nbsp;bounds,
               int&nbsp;width,
               int&nbsp;height,
               int&nbsp;padding)</code>
<div class="block">设置显示在规定宽高中的地图经纬度范围。</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/amap/api/maps/CameraUpdate.html" title="com.amap.api.maps中的类">CameraUpdate</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/CameraUpdateFactory.html#newLatLngBoundsRect-com.amap.api.maps.model.LatLngBounds-int-int-int-int-">newLatLngBoundsRect</a></span>(<a href="../../../../com/amap/api/maps/model/LatLngBounds.html" title="com.amap.api.maps.model中的类">LatLngBounds</a>&nbsp;latlngbounds,
                   int&nbsp;paddingLeft,
                   int&nbsp;paddingRight,
                   int&nbsp;paddingTop,
                   int&nbsp;paddingBottom)</code>
<div class="block">设置显示在规定屏幕范围内的地图经纬度范围。</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/amap/api/maps/CameraUpdate.html" title="com.amap.api.maps中的类">CameraUpdate</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/CameraUpdateFactory.html#newLatLngZoom-com.amap.api.maps.model.LatLng-float-">newLatLngZoom</a></span>(<a href="../../../../com/amap/api/maps/model/LatLng.html" title="com.amap.api.maps.model中的类">LatLng</a>&nbsp;latLng,
             float&nbsp;zoom)</code>
<div class="block">设置地图中心点以及缩放级别。</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/amap/api/maps/CameraUpdate.html" title="com.amap.api.maps中的类">CameraUpdate</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/CameraUpdateFactory.html#scrollBy-float-float-">scrollBy</a></span>(float&nbsp;xPixel,
        float&nbsp;yPixel)</code>
<div class="block">按像素移动地图中心点</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/amap/api/maps/CameraUpdate.html" title="com.amap.api.maps中的类">CameraUpdate</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/CameraUpdateFactory.html#zoomBy-float-">zoomBy</a></span>(float&nbsp;amount)</code>
<div class="block">根据给定增量缩放地图级别，在当前地图显示的级别基础上加上这个增量。</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/amap/api/maps/CameraUpdate.html" title="com.amap.api.maps中的类">CameraUpdate</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/CameraUpdateFactory.html#zoomBy-float-android.graphics.Point-">zoomBy</a></span>(float&nbsp;amount,
      android.graphics.Point&nbsp;focus)</code>
<div class="block">根据给定增量并以给定的屏幕像素点为中心点缩放地图级别。</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/amap/api/maps/CameraUpdate.html" title="com.amap.api.maps中的类">CameraUpdate</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/CameraUpdateFactory.html#zoomIn--">zoomIn</a></span>()</code>
<div class="block">放大地图缩放级别，在当前地图显示的级别基础上加1。</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/amap/api/maps/CameraUpdate.html" title="com.amap.api.maps中的类">CameraUpdate</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/CameraUpdateFactory.html#zoomOut--">zoomOut</a></span>()</code>
<div class="block">缩小地图缩放级别，在当前地图显示的级别基础上减1。</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/amap/api/maps/CameraUpdate.html" title="com.amap.api.maps中的类">CameraUpdate</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/CameraUpdateFactory.html#zoomTo-float-">zoomTo</a></span>(float&nbsp;zoom)</code>
<div class="block">设置地图缩放级别。</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="CameraUpdateFactory--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>CameraUpdateFactory</h4>
<pre>public&nbsp;CameraUpdateFactory()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="zoomIn--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>zoomIn</h4>
<pre>public static&nbsp;<a href="../../../../com/amap/api/maps/CameraUpdate.html" title="com.amap.api.maps中的类">CameraUpdate</a>&nbsp;zoomIn()</pre>
<div class="block">放大地图缩放级别，在当前地图显示的级别基础上加1。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>包含缩放级别改变的cameraUpdate对象.</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="zoomOut--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>zoomOut</h4>
<pre>public static&nbsp;<a href="../../../../com/amap/api/maps/CameraUpdate.html" title="com.amap.api.maps中的类">CameraUpdate</a>&nbsp;zoomOut()</pre>
<div class="block">缩小地图缩放级别，在当前地图显示的级别基础上减1。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>包含缩放级别改变的cameraUpdate对象。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="scrollBy-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>scrollBy</h4>
<pre>public static&nbsp;<a href="../../../../com/amap/api/maps/CameraUpdate.html" title="com.amap.api.maps中的类">CameraUpdate</a>&nbsp;scrollBy(float&nbsp;xPixel,
                                    float&nbsp;yPixel)</pre>
<div class="block">按像素移动地图中心点</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>xPixel</code> - 这是水平移动的像素数。正值代表可视区域向右移动，负值代表可视区域向左移动。</dd>
<dd><code>yPixel</code> - 这是垂直移动的像素数。正值代表可视区域向下移动，负值代表可视区域向上移动。</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>包含x，y方向上移动像素数的cameraUpdate对象。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="zoomTo-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>zoomTo</h4>
<pre>public static&nbsp;<a href="../../../../com/amap/api/maps/CameraUpdate.html" title="com.amap.api.maps中的类">CameraUpdate</a>&nbsp;zoomTo(float&nbsp;zoom)</pre>
<div class="block">设置地图缩放级别。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>zoom</code> - 地图缩放级别。</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>包含指定缩放级别的cameraUpdate对象</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="zoomBy-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>zoomBy</h4>
<pre>public static&nbsp;<a href="../../../../com/amap/api/maps/CameraUpdate.html" title="com.amap.api.maps中的类">CameraUpdate</a>&nbsp;zoomBy(float&nbsp;amount)</pre>
<div class="block">根据给定增量缩放地图级别，在当前地图显示的级别基础上加上这个增量。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>amount</code> - 地图缩放级别增量。</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>包含指定缩放级别增量的cameraUpdate对象。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="zoomBy-float-android.graphics.Point-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>zoomBy</h4>
<pre>public static&nbsp;<a href="../../../../com/amap/api/maps/CameraUpdate.html" title="com.amap.api.maps中的类">CameraUpdate</a>&nbsp;zoomBy(float&nbsp;amount,
                                  android.graphics.Point&nbsp;focus)</pre>
<div class="block">根据给定增量并以给定的屏幕像素点为中心点缩放地图级别。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>amount</code> - 地图缩放级别增量。</dd>
<dd><code>focus</code> - 地图缩放中心点对应的屏幕坐标。</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>包含指定缩放级别增量及中心点像素坐标的cameraUpdate对象。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="newCameraPosition-com.amap.api.maps.model.CameraPosition-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>newCameraPosition</h4>
<pre>public static&nbsp;<a href="../../../../com/amap/api/maps/CameraUpdate.html" title="com.amap.api.maps中的类">CameraUpdate</a>&nbsp;newCameraPosition(<a href="../../../../com/amap/api/maps/model/CameraPosition.html" title="com.amap.api.maps.model中的类">CameraPosition</a>&nbsp;cameraPosition)</pre>
<div class="block">给地图设置一个新的状态。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>cameraPosition</code> - 新的地图状态。</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>包含新的地图状态的cameraUpdate对象</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="newLatLng-com.amap.api.maps.model.LatLng-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>newLatLng</h4>
<pre>public static&nbsp;<a href="../../../../com/amap/api/maps/CameraUpdate.html" title="com.amap.api.maps中的类">CameraUpdate</a>&nbsp;newLatLng(<a href="../../../../com/amap/api/maps/model/LatLng.html" title="com.amap.api.maps.model中的类">LatLng</a>&nbsp;latLng)</pre>
<div class="block">设置地图的中心点。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>latLng</code> - 地图中心点。</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>包含新的地图中心点的cameraUpdate对象。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="newLatLngZoom-com.amap.api.maps.model.LatLng-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>newLatLngZoom</h4>
<pre>public static&nbsp;<a href="../../../../com/amap/api/maps/CameraUpdate.html" title="com.amap.api.maps中的类">CameraUpdate</a>&nbsp;newLatLngZoom(<a href="../../../../com/amap/api/maps/model/LatLng.html" title="com.amap.api.maps.model中的类">LatLng</a>&nbsp;latLng,
                                         float&nbsp;zoom)</pre>
<div class="block">设置地图中心点以及缩放级别。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>latLng</code> - 地图中心点。</dd>
<dd><code>zoom</code> - 缩放级别，[3-20]。</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>包含新的地图中心点及缩放级别的cameraUpdate对象。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="newLatLngBounds-com.amap.api.maps.model.LatLngBounds-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>newLatLngBounds</h4>
<pre>public static&nbsp;<a href="../../../../com/amap/api/maps/CameraUpdate.html" title="com.amap.api.maps中的类">CameraUpdate</a>&nbsp;newLatLngBounds(<a href="../../../../com/amap/api/maps/model/LatLngBounds.html" title="com.amap.api.maps.model中的类">LatLngBounds</a>&nbsp;bounds,
                                           int&nbsp;padding)</pre>
<div class="block">设置显示在规定屏幕范围内的地图经纬度范围。
     。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>bounds</code> - 地图显示经纬度范围，不能为 null。</dd>
<dd><code>padding</code> - 设置经纬度范围和mapView边缘的空隙，单位像素。这个值适用于区域的四个边。</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>包含新的经纬度范围及边缘间隙的cameraUpdate对象。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.1</dd>
</dl>
</li>
</ul>
<a name="changeLatLng-com.amap.api.maps.model.LatLng-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>changeLatLng</h4>
<pre>public static&nbsp;<a href="../../../../com/amap/api/maps/CameraUpdate.html" title="com.amap.api.maps中的类">CameraUpdate</a>&nbsp;changeLatLng(<a href="../../../../com/amap/api/maps/model/LatLng.html" title="com.amap.api.maps.model中的类">LatLng</a>&nbsp;latLng)</pre>
<div class="block">设置地图的中心点。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>latLng</code> - 地图中心点。</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>包含新的地图中心点的cameraUpdate对象。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="changeBearing-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>changeBearing</h4>
<pre>public static&nbsp;<a href="../../../../com/amap/api/maps/CameraUpdate.html" title="com.amap.api.maps中的类">CameraUpdate</a>&nbsp;changeBearing(float&nbsp;bearing)</pre>
<div class="block">设置地图的旋转角度。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>bearing</code> - 地图旋转角度。以角度为单位，正北方向为0度，逆时针范围从0度到360度。</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>包含新的地图旋转角度的cameraUpdate对象</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="changeTilt-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>changeTilt</h4>
<pre>public static&nbsp;<a href="../../../../com/amap/api/maps/CameraUpdate.html" title="com.amap.api.maps中的类">CameraUpdate</a>&nbsp;changeTilt(float&nbsp;tilt)</pre>
<div class="block">设置地图倾斜度。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>tilt</code> - 地图倾斜度。以角度为单位，范围（0,60）。</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>包含新的地图倾斜角度的cameraUpdate对象。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="newLatLngBounds-com.amap.api.maps.model.LatLngBounds-int-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>newLatLngBounds</h4>
<pre>public static&nbsp;<a href="../../../../com/amap/api/maps/CameraUpdate.html" title="com.amap.api.maps中的类">CameraUpdate</a>&nbsp;newLatLngBounds(<a href="../../../../com/amap/api/maps/model/LatLngBounds.html" title="com.amap.api.maps.model中的类">LatLngBounds</a>&nbsp;bounds,
                                           int&nbsp;width,
                                           int&nbsp;height,
                                           int&nbsp;padding)</pre>
<div class="block">设置显示在规定宽高中的地图经纬度范围。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>bounds</code> - 地图显示经纬度范围。</dd>
<dd><code>width</code> - 限制区域的宽度，单位像素。</dd>
<dd><code>height</code> - 限制区域的高度，单位像素。</dd>
<dd><code>padding</code> - 经纬度范围与限制区域的边缘间隙，单位像素。</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>包含经纬度范围及限制区域宽高的cameraUpdate对象</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.1</dd>
</dl>
</li>
</ul>
<a name="newLatLngBoundsRect-com.amap.api.maps.model.LatLngBounds-int-int-int-int-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>newLatLngBoundsRect</h4>
<pre>public static&nbsp;<a href="../../../../com/amap/api/maps/CameraUpdate.html" title="com.amap.api.maps中的类">CameraUpdate</a>&nbsp;newLatLngBoundsRect(<a href="../../../../com/amap/api/maps/model/LatLngBounds.html" title="com.amap.api.maps.model中的类">LatLngBounds</a>&nbsp;latlngbounds,
                                               int&nbsp;paddingLeft,
                                               int&nbsp;paddingRight,
                                               int&nbsp;paddingTop,
                                               int&nbsp;paddingBottom)</pre>
<div class="block">设置显示在规定屏幕范围内的地图经纬度范围。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>latlngbounds</code> - 地图显示经纬度范围。</dd>
<dd><code>paddingLeft</code> - 设置经纬度范围和mapView左边缘的空隙。</dd>
<dd><code>paddingRight</code> - 设置经纬度范围和mapView右边缘的空隙。</dd>
<dd><code>paddingTop</code> - 设置经纬度范围和mapView上边缘的空隙。</dd>
<dd><code>paddingBottom</code> - 设置经纬度范围和mapView下边缘的空隙。</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>包含新的经纬度范围及边缘间隙的cameraUpdate对象。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>4.0.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/CameraUpdateFactory.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/amap/api/maps/CameraUpdate.html" title="com.amap.api.maps中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../com/amap/api/maps/CoordinateConverter.html" title="com.amap.api.maps中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/amap/api/maps/CameraUpdateFactory.html" target="_top">框架</a></li>
<li><a href="CameraUpdateFactory.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
