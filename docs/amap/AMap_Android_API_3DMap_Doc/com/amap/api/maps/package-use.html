<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>程序包 com.amap.api.maps的使用</title>
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="\u7A0B\u5E8F\u5305 com.amap.api.maps\u7684\u4F7F\u7528";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li>类</li>
<li class="navBarCell1Rev">使用</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个</li>
<li>下一个</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/amap/api/maps/package-use.html" target="_top">框架</a></li>
<li><a href="package-use.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 title="程序包的使用 com.amap.api.maps" class="title">程序包的使用<br>com.amap.api.maps</h1>
</div>
<div class="contentContainer">
<ul class="blockList">
<li class="blockList">
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表程序包和解释">
<caption><span>使用<a href="../../../../com/amap/api/maps/package-summary.html">com.amap.api.maps</a>的程序包</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">程序包</th>
<th class="colLast" scope="col">说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="#com.amap.api.maps">com.amap.api.maps</a></td>
<td class="colLast">
<div class="block">
地图显示包，帮助您在Android应用程序中显示高德地图，并且支持地图交互手势的功能。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.amap.api.maps.offlinemap">com.amap.api.maps.offlinemap</a></td>
<td class="colLast">
<div class="block">
离线地图包，用户可以通过手机WiFi下载高德3D离线地图。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#com.amap.api.maps.utils.overlay">com.amap.api.maps.utils.overlay</a></td>
<td class="colLast">
<div class="block">
工具类，基于地图现有接口实现的高级功能。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.amap.api.trace">com.amap.api.trace</a></td>
<td class="colLast">
<div class="block">
轨迹纠偏包，提供高精度定位轨迹抓路后绘制平滑轨迹。</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.amap.api.maps">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表类和解释">
<caption><span><a href="../../../../com/amap/api/maps/package-summary.html">com.amap.api.maps</a>使用的<a href="../../../../com/amap/api/maps/package-summary.html">com.amap.api.maps</a>中的类</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">类和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/amap/api/maps/class-use/AMap.html#com.amap.api.maps">AMap</a>
<div class="block">定义AMap 地图对象的操作方法与接口。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/amap/api/maps/class-use/AMap.AMapAppResourceRequestListener.html#com.amap.api.maps">AMap.AMapAppResourceRequestListener</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/amap/api/maps/class-use/AMap.CancelableCallback.html#com.amap.api.maps">AMap.CancelableCallback</a>
<div class="block">在<a href="../../../../com/amap/api/maps/AMap.html#animateCamera-com.amap.api.maps.CameraUpdate-com.amap.api.maps.AMap.CancelableCallback-"><code>AMap.animateCamera(CameraUpdate, CancelableCallback)</code></a>设置一个CancelableCallback，用来监听该CameraUpdate是否执行完成或者被中断。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/amap/api/maps/class-use/AMap.InfoWindowAdapter.html#com.amap.api.maps">AMap.InfoWindowAdapter</a>
<div class="block">用来定制marker的信息窗口<br>
 默认情况下，当单击某个marker时，如果该marker的Title和Snippet不为空，则会触发getInfoWindow和getInfoContents回调。</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/amap/api/maps/class-use/AMap.OnCacheRemoveListener.html#com.amap.api.maps">AMap.OnCacheRemoveListener</a>
<div class="block">缓存数据清除监听接口</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/amap/api/maps/class-use/AMap.OnCameraChangeListener.html#com.amap.api.maps">AMap.OnCameraChangeListener</a>
<div class="block">地图状态发生变化的监听接口。</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/amap/api/maps/class-use/AMap.OnIndoorBuildingActiveListener.html#com.amap.api.maps">AMap.OnIndoorBuildingActiveListener</a>
<div class="block">室内地图状态监听接口。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/amap/api/maps/class-use/AMap.OnInfoWindowClickListener.html#com.amap.api.maps">AMap.OnInfoWindowClickListener</a>
<div class="block">marker的信息窗口点击事件监听接口。</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/amap/api/maps/class-use/AMap.OnMapClickListener.html#com.amap.api.maps">AMap.OnMapClickListener</a>
<div class="block">地图点击事件监听接口。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/amap/api/maps/class-use/AMap.OnMapLoadedListener.html#com.amap.api.maps">AMap.OnMapLoadedListener</a>
<div class="block">地图加载完成监听接口</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/amap/api/maps/class-use/AMap.OnMapLongClickListener.html#com.amap.api.maps">AMap.OnMapLongClickListener</a>
<div class="block">地图长按事件监听接口。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/amap/api/maps/class-use/AMap.onMapPrintScreenListener.html#com.amap.api.maps">AMap.onMapPrintScreenListener</a>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;
<div class="block"><span class="deprecationComment">建议使用 <a href="../../../../com/amap/api/maps/AMap.OnMapScreenShotListener.html" title="com.amap.api.maps中的接口"><code>AMap.OnMapScreenShotListener</code></a></span></div>
</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/amap/api/maps/class-use/AMap.OnMapScreenShotListener.html#com.amap.api.maps">AMap.OnMapScreenShotListener</a>
<div class="block">地图截屏监听接口 。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/amap/api/maps/class-use/AMap.OnMapSnapshotListener.html#com.amap.api.maps">AMap.OnMapSnapshotListener</a>
<div class="block">地图区域图异步返回接口 。</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/amap/api/maps/class-use/AMap.OnMapTouchListener.html#com.amap.api.maps">AMap.OnMapTouchListener</a>
<div class="block">地图触摸事件监听接口。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/amap/api/maps/class-use/AMap.OnMarkerClickListener.html#com.amap.api.maps">AMap.OnMarkerClickListener</a>
<div class="block">marker点击事件监听接口。</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/amap/api/maps/class-use/AMap.OnMarkerDragListener.html#com.amap.api.maps">AMap.OnMarkerDragListener</a>
<div class="block">marker拖动事件监听接口。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/amap/api/maps/class-use/AMap.OnMultiPointClickListener.html#com.amap.api.maps">AMap.OnMultiPointClickListener</a>
<div class="block">海量点中某一点被点击时的回调。</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/amap/api/maps/class-use/AMap.OnMyLocationChangeListener.html#com.amap.api.maps">AMap.OnMyLocationChangeListener</a>
<div class="block">用户定位信息监听接口。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/amap/api/maps/class-use/AMap.OnPOIClickListener.html#com.amap.api.maps">AMap.OnPOIClickListener</a>
<div class="block">地图底图poi点击事件监听接口。</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/amap/api/maps/class-use/AMap.OnPolylineClickListener.html#com.amap.api.maps">AMap.OnPolylineClickListener</a>
<div class="block">polyline点击事件监听接口。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/amap/api/maps/class-use/AMapException.html#com.amap.api.maps">AMapException</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/amap/api/maps/class-use/AMapOptions.html#com.amap.api.maps">AMapOptions</a>
<div class="block">MapView 初始化选项。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/amap/api/maps/class-use/CameraUpdate.html#com.amap.api.maps">CameraUpdate</a>
<div class="block">描述地图状态将要发生的变化。</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/amap/api/maps/class-use/CoordinateConverter.html#com.amap.api.maps">CoordinateConverter</a>
<div class="block">坐标转换工具类</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/amap/api/maps/class-use/CoordinateConverter.CoordType.html#com.amap.api.maps">CoordinateConverter.CoordType</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/amap/api/maps/class-use/CustomRenderer.html#com.amap.api.maps">CustomRenderer</a>
<div class="block">定义了一个地图在初始化及每一帧绘制时的回调接口。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/amap/api/maps/class-use/LocationSource.html#com.amap.api.maps">LocationSource</a>
<div class="block">定义了一个定位源，为地图提供定位数据。</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/amap/api/maps/class-use/LocationSource.OnLocationChangedListener.html#com.amap.api.maps">LocationSource.OnLocationChangedListener</a>
<div class="block">位置改变的监听接口。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/amap/api/maps/class-use/MapFragment.html#com.amap.api.maps">MapFragment</a>
<div class="block">MapFragment 类, 管理地图生命周期。</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/amap/api/maps/class-use/Projection.html#com.amap.api.maps">Projection</a>
<div class="block">Projection接口用于屏幕像素点坐标系统和地球表面经纬度点坐标系统之间的变换。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/amap/api/maps/class-use/SupportMapFragment.html#com.amap.api.maps">SupportMapFragment</a>
<div class="block">SupportMapFragment 类, 管理地图生命周期。</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/amap/api/maps/class-use/TextureMapFragment.html#com.amap.api.maps">TextureMapFragment</a>
<div class="block">TextureMapFragment 类, 管理地图生命周期，android4.0以上版本使用。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/amap/api/maps/class-use/TextureSupportMapFragment.html#com.amap.api.maps">TextureSupportMapFragment</a>
<div class="block">TextureSupportMapFragment 类, 管理地图生命周期。</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/amap/api/maps/class-use/UiSettings.html#com.amap.api.maps">UiSettings</a>
<div class="block">地图内置UI及手势控制器。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/amap/api/maps/class-use/WearMapView.OnDismissCallback.html#com.amap.api.maps">WearMapView.OnDismissCallback</a>
<div class="block">手势滑动监听</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.amap.api.maps.offlinemap">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表类和解释">
<caption><span><a href="../../../../com/amap/api/maps/offlinemap/package-summary.html">com.amap.api.maps.offlinemap</a>使用的<a href="../../../../com/amap/api/maps/package-summary.html">com.amap.api.maps</a>中的类</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">类和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/amap/api/maps/class-use/AMap.html#com.amap.api.maps.offlinemap">AMap</a>
<div class="block">定义AMap 地图对象的操作方法与接口。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/amap/api/maps/class-use/AMapException.html#com.amap.api.maps.offlinemap">AMapException</a>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.amap.api.maps.utils.overlay">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表类和解释">
<caption><span><a href="../../../../com/amap/api/maps/utils/overlay/package-summary.html">com.amap.api.maps.utils.overlay</a>使用的<a href="../../../../com/amap/api/maps/package-summary.html">com.amap.api.maps</a>中的类</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">类和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/amap/api/maps/class-use/AMap.html#com.amap.api.maps.utils.overlay">AMap</a>
<div class="block">定义AMap 地图对象的操作方法与接口。</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.amap.api.trace">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表类和解释">
<caption><span><a href="../../../../com/amap/api/trace/package-summary.html">com.amap.api.trace</a>使用的<a href="../../../../com/amap/api/maps/package-summary.html">com.amap.api.maps</a>中的类</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">类和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/amap/api/maps/class-use/AMap.html#com.amap.api.trace">AMap</a>
<div class="block">定义AMap 地图对象的操作方法与接口。</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li>类</li>
<li class="navBarCell1Rev">使用</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个</li>
<li>下一个</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/amap/api/maps/package-use.html" target="_top">框架</a></li>
<li><a href="package-use.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
