<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>AMap.InfoWindowAdapter</title>
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="AMap.InfoWindowAdapter";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":6,"i1":6};
var tabs = {65535:["t0","所有方法"],2:["t2","实例方法"],4:["t3","抽象方法"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/AMap.InfoWindowAdapter.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/amap/api/maps/AMap.ImageInfoWindowAdapter.html" title="com.amap.api.maps中的接口"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../com/amap/api/maps/AMap.OnCacheRemoveListener.html" title="com.amap.api.maps中的接口"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/amap/api/maps/AMap.InfoWindowAdapter.html" target="_top">框架</a></li>
<li><a href="AMap.InfoWindowAdapter.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.amap.api.maps</div>
<h2 title="接口 AMap.InfoWindowAdapter" class="title">接口 AMap.InfoWindowAdapter</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>所有已知子接口:</dt>
<dd><a href="../../../../com/amap/api/maps/AMap.ImageInfoWindowAdapter.html" title="com.amap.api.maps中的接口">AMap.ImageInfoWindowAdapter</a></dd>
</dl>
<dl>
<dt>封闭类:</dt>
<dd><a href="../../../../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a></dd>
</dl>
<hr>
<br>
<pre>public static interface <span class="typeNameLabel">AMap.InfoWindowAdapter</span></pre>
<div class="block">用来定制marker的信息窗口<br>
 默认情况下，当单击某个marker时，如果该marker的Title和Snippet不为空，则会触发getInfoWindow和getInfoContents回调。<br>
 另外，通过调用<a href="../../../../com/amap/api/maps/model/Marker.html#showInfoWindow--"><code>Marker.showInfoWindow()</code></a>同样可以触发上面两个回调。<br>
 <font color="red">自5.2.1开始，如果<a href="../../../../com/amap/api/maps/AMap.InfoWindowAdapter.html#getInfoWindow-com.amap.api.maps.model.Marker-"><code>AMap.InfoWindowAdapter.getInfoWindow(Marker)</code></a> 和 <a href="../../../../com/amap/api/maps/AMap.InfoWindowAdapter.html#getInfoContents-com.amap.api.maps.model.Marker-"><code>AMap.InfoWindowAdapter.getInfoContents(Marker)</code></a> 均返回null，将不展示InfoWindow的信息</font></div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>所有方法</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">实例方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">抽象方法</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>android.view.View</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.InfoWindowAdapter.html#getInfoContents-com.amap.api.maps.model.Marker-">getInfoContents</a></span>(<a href="../../../../com/amap/api/maps/model/Marker.html" title="com.amap.api.maps.model中的类">Marker</a>&nbsp;marker)</code>
<div class="block">提定制展示marker信息的View。</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>android.view.View</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/AMap.InfoWindowAdapter.html#getInfoWindow-com.amap.api.maps.model.Marker-">getInfoWindow</a></span>(<a href="../../../../com/amap/api/maps/model/Marker.html" title="com.amap.api.maps.model中的类">Marker</a>&nbsp;marker)</code>
<div class="block">定制展示marker信息的View。</div>
</td>
</tr>
</table>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="getInfoWindow-com.amap.api.maps.model.Marker-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getInfoWindow</h4>
<pre>android.view.View&nbsp;getInfoWindow(<a href="../../../../com/amap/api/maps/model/Marker.html" title="com.amap.api.maps.model中的类">Marker</a>&nbsp;marker)</pre>
<div class="block">定制展示marker信息的View。<br>
 如果返回的View不为空且View的background不为null，则直接使用它来展示marker的信息。如果backgound为null，SDK内部会给这个View设置一个默认的background。<br>
 如果这个方法返回null，内容将会从getInfoContents(Marker)方法获取。<br>
 <font color="red">自5.2.1开始，如果<a href="../../../../com/amap/api/maps/AMap.InfoWindowAdapter.html#getInfoWindow-com.amap.api.maps.model.Marker-"><code>AMap.InfoWindowAdapter.getInfoWindow(Marker)</code></a> 和 <a href="../../../../com/amap/api/maps/AMap.InfoWindowAdapter.html#getInfoContents-com.amap.api.maps.model.Marker-"><code>AMap.InfoWindowAdapter.getInfoContents(Marker)</code></a> 均返回null，将不展示InfoWindow的信息</font></div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>marker</code> - 与改信息窗口相关联的marker对象。</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>一个定制化的信息窗口的View。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="getInfoContents-com.amap.api.maps.model.Marker-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getInfoContents</h4>
<pre>android.view.View&nbsp;getInfoContents(<a href="../../../../com/amap/api/maps/model/Marker.html" title="com.amap.api.maps.model中的类">Marker</a>&nbsp;marker)</pre>
<div class="block">提定制展示marker信息的View。<br>
 如果返回的View不为空且View的background不为null，则直接使用它来展示marker的信息。如果backgound为null，SDK内部会给这个View设置一个默认的background。<br>
 如果这个方法返回null，将使用内置的一个默认的View来展示marker的信息。<br>
 <font color="red">自5.2.1开始，如果<a href="../../../../com/amap/api/maps/AMap.InfoWindowAdapter.html#getInfoWindow-com.amap.api.maps.model.Marker-"><code>AMap.InfoWindowAdapter.getInfoWindow(Marker)</code></a> 和 <a href="../../../../com/amap/api/maps/AMap.InfoWindowAdapter.html#getInfoContents-com.amap.api.maps.model.Marker-"><code>AMap.InfoWindowAdapter.getInfoContents(Marker)</code></a> 均返回null，将不展示InfoWindow的信息</font></div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>marker</code> - 与改信息窗口相关联的marker对象。</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>一个定制化的信息窗口的View。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/AMap.InfoWindowAdapter.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/amap/api/maps/AMap.ImageInfoWindowAdapter.html" title="com.amap.api.maps中的接口"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../com/amap/api/maps/AMap.OnCacheRemoveListener.html" title="com.amap.api.maps中的接口"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/amap/api/maps/AMap.InfoWindowAdapter.html" target="_top">框架</a></li>
<li><a href="AMap.InfoWindowAdapter.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
