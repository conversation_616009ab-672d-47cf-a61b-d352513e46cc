<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Projection</title>
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Projection";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":42,"i5":10,"i6":10,"i7":10};
var tabs = {65535:["t0","所有方法"],2:["t2","实例方法"],8:["t4","具体方法"],32:["t6","已过时的方法"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/Projection.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/amap/api/maps/MapView.html" title="com.amap.api.maps中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../com/amap/api/maps/SupportMapFragment.html" title="com.amap.api.maps中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/amap/api/maps/Projection.html" target="_top">框架</a></li>
<li><a href="Projection.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.amap.api.maps</div>
<h2 title="类 Projection" class="title">类 Projection</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.amap.api.maps.Projection</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">Projection</span>
extends java.lang.Object</pre>
<div class="block">Projection接口用于屏幕像素点坐标系统和地球表面经纬度点坐标系统之间的变换。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>所有方法</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">实例方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">具体方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t6" class="tableTab"><span><a href="javascript:show(32);">已过时的方法</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../../../com/amap/api/maps/model/TileProjection.html" title="com.amap.api.maps.model中的类">TileProjection</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/Projection.html#fromBoundsToTile-com.amap.api.maps.model.LatLngBounds-int-int-">fromBoundsToTile</a></span>(<a href="../../../../com/amap/api/maps/model/LatLngBounds.html" title="com.amap.api.maps.model中的类">LatLngBounds</a>&nbsp;lb,
                int&nbsp;zoom,
                int&nbsp;width)</code>
<div class="block">根据指定的经纬度范围及缩放级别，返回SDK适用的瓦片编号范围（按照指定的瓦片宽度切图）。</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/amap/api/maps/model/LatLng.html" title="com.amap.api.maps.model中的类">LatLng</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/Projection.html#fromScreenLocation-android.graphics.Point-">fromScreenLocation</a></span>(android.graphics.Point&nbsp;paramPoint)</code>
<div class="block">将屏幕坐标转换成地理坐标。</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../../../com/amap/api/maps/model/LatLngBounds.html" title="com.amap.api.maps.model中的类">LatLngBounds</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/Projection.html#getMapBounds-com.amap.api.maps.model.LatLng-float-">getMapBounds</a></span>(<a href="../../../../com/amap/api/maps/model/LatLng.html" title="com.amap.api.maps.model中的类">LatLng</a>&nbsp;center,
            float&nbsp;zoom)</code>
<div class="block">根据中心点和zoom级别获取地图控件对应的目标区域</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/amap/api/maps/model/VisibleRegion.html" title="com.amap.api.maps.model中的类">VisibleRegion</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/Projection.html#getVisibleRegion--">getVisibleRegion</a></span>()</code>
<div class="block">返回当前可视区域（包含MapView四个角点的经纬度坐标）坐标信息。</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>android.graphics.PointF</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/Projection.html#toMapLocation-com.amap.api.maps.model.LatLng-">toMapLocation</a></span>(<a href="../../../../com/amap/api/maps/model/LatLng.html" title="com.amap.api.maps.model中的类">LatLng</a>&nbsp;paramLatLng)</code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>android.graphics.PointF</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/Projection.html#toOpenGLLocation-com.amap.api.maps.model.LatLng-">toOpenGLLocation</a></span>(<a href="../../../../com/amap/api/maps/model/LatLng.html" title="com.amap.api.maps.model中的类">LatLng</a>&nbsp;paramLatLng)</code>
<div class="block">将地理坐标转换成openGL坐标，在<code>GLSurfaceView.Renderer.onDrawFrame(GL10)</code>中使用openGL坐标绘制。</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/Projection.html#toOpenGLWidth-int-">toOpenGLWidth</a></span>(int&nbsp;screenWidth)</code>
<div class="block">返回一个屏幕宽度转换来的openGL 需要的宽度。</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>android.graphics.Point</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps/Projection.html#toScreenLocation-com.amap.api.maps.model.LatLng-">toScreenLocation</a></span>(<a href="../../../../com/amap/api/maps/model/LatLng.html" title="com.amap.api.maps.model中的类">LatLng</a>&nbsp;paramLatLng)</code>
<div class="block">将地理坐标转换成屏幕坐标。</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="fromScreenLocation-android.graphics.Point-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fromScreenLocation</h4>
<pre>public&nbsp;<a href="../../../../com/amap/api/maps/model/LatLng.html" title="com.amap.api.maps.model中的类">LatLng</a>&nbsp;fromScreenLocation(android.graphics.Point&nbsp;paramPoint)</pre>
<div class="block">将屏幕坐标转换成地理坐标。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>paramPoint</code> - 屏幕坐标 如果传入null 则返回null。</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>地理坐标。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="toScreenLocation-com.amap.api.maps.model.LatLng-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toScreenLocation</h4>
<pre>public&nbsp;android.graphics.Point&nbsp;toScreenLocation(<a href="../../../../com/amap/api/maps/model/LatLng.html" title="com.amap.api.maps.model中的类">LatLng</a>&nbsp;paramLatLng)</pre>
<div class="block">将地理坐标转换成屏幕坐标。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>paramLatLng</code> - 地理坐标 如果传入 null 则返回null。</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>屏幕坐标。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="toMapLocation-com.amap.api.maps.model.LatLng-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toMapLocation</h4>
<pre>@Deprecated
public&nbsp;android.graphics.PointF&nbsp;toMapLocation(<a href="../../../../com/amap/api/maps/model/LatLng.html" title="com.amap.api.maps.model中的类">LatLng</a>&nbsp;paramLatLng)</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;</div>
<div class="block">将地理坐标转换成openGL坐标，在<code>GLSurfaceView.Renderer.onDrawFrame(GL10)</code>中使用openGL坐标绘制。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>paramLatLng</code> - 地理坐标 如果传入 null 则返回null</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>opengl坐标</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="toOpenGLLocation-com.amap.api.maps.model.LatLng-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toOpenGLLocation</h4>
<pre>public&nbsp;android.graphics.PointF&nbsp;toOpenGLLocation(<a href="../../../../com/amap/api/maps/model/LatLng.html" title="com.amap.api.maps.model中的类">LatLng</a>&nbsp;paramLatLng)</pre>
<div class="block">将地理坐标转换成openGL坐标，在<code>GLSurfaceView.Renderer.onDrawFrame(GL10)</code>中使用openGL坐标绘制。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>paramLatLng</code> - 地理坐标 如果传入 null 则返回null</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>opengl坐标</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.4</dd>
</dl>
</li>
</ul>
<a name="toOpenGLWidth-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toOpenGLWidth</h4>
<pre>public&nbsp;float&nbsp;toOpenGLWidth(int&nbsp;screenWidth)</pre>
<div class="block">返回一个屏幕宽度转换来的openGL 需要的宽度。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>screenWidth</code> - 屏幕的宽度（像素）。</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>openGL坐标宽度。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.4</dd>
</dl>
</li>
</ul>
<a name="getVisibleRegion--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVisibleRegion</h4>
<pre>public&nbsp;<a href="../../../../com/amap/api/maps/model/VisibleRegion.html" title="com.amap.api.maps.model中的类">VisibleRegion</a>&nbsp;getVisibleRegion()</pre>
<div class="block">返回当前可视区域（包含MapView四个角点的经纬度坐标）坐标信息。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd> - </dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>isibleRegion 可视区域坐标信息。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="fromBoundsToTile-com.amap.api.maps.model.LatLngBounds-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fromBoundsToTile</h4>
<pre>public&nbsp;<a href="../../../../com/amap/api/maps/model/TileProjection.html" title="com.amap.api.maps.model中的类">TileProjection</a>&nbsp;fromBoundsToTile(<a href="../../../../com/amap/api/maps/model/LatLngBounds.html" title="com.amap.api.maps.model中的类">LatLngBounds</a>&nbsp;lb,
                                       int&nbsp;zoom,
                                       int&nbsp;width)</pre>
<div class="block">根据指定的经纬度范围及缩放级别，返回SDK适用的瓦片编号范围（按照指定的瓦片宽度切图）。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>lb</code> - 经纬度范围。</dd>
<dd><code>zoom</code> - 缩放级别。</dd>
<dd><code>width</code> - 瓦片宽度。要求为2 的 n次方。</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>瓦片编号范围。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.3.0</dd>
</dl>
</li>
</ul>
<a name="getMapBounds-com.amap.api.maps.model.LatLng-float-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getMapBounds</h4>
<pre>public&nbsp;<a href="../../../../com/amap/api/maps/model/LatLngBounds.html" title="com.amap.api.maps.model中的类">LatLngBounds</a>&nbsp;getMapBounds(<a href="../../../../com/amap/api/maps/model/LatLng.html" title="com.amap.api.maps.model中的类">LatLng</a>&nbsp;center,
                                 float&nbsp;zoom)</pre>
<div class="block">根据中心点和zoom级别获取地图控件对应的目标区域</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>center</code> - 坐标中心</dd>
<dd><code>zoom</code> - zoom级别</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>返回一个经纬度矩形局域</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.3.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/Projection.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/amap/api/maps/MapView.html" title="com.amap.api.maps中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../com/amap/api/maps/SupportMapFragment.html" title="com.amap.api.maps中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/amap/api/maps/Projection.html" target="_top">框架</a></li>
<li><a href="Projection.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
