<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>AMapGestureListener</title>
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="AMapGestureListener";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6};
var tabs = {65535:["t0","所有方法"],2:["t2","实例方法"],4:["t3","抽象方法"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/AMapGestureListener.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/maps/model/AMapCameraInfo.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/amap/api/maps/model/AMapGLOverlay.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/maps/model/AMapGestureListener.html" target="_top">框架</a></li>
<li><a href="AMapGestureListener.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.amap.api.maps.model</div>
<h2 title="接口 AMapGestureListener" class="title">接口 AMapGestureListener</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public interface <span class="typeNameLabel">AMapGestureListener</span></pre>
<div class="block">地图手势识别的回调，包含单双击、滑动等以及地图操作地图后稳定下来的回调</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>4.0.0</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>所有方法</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">实例方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">抽象方法</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/model/AMapGestureListener.html#onDoubleTap-float-float-">onDoubleTap</a></span>(float&nbsp;x,
           float&nbsp;y)</code>
<div class="block">双击</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/model/AMapGestureListener.html#onDown-float-float-">onDown</a></span>(float&nbsp;x,
      float&nbsp;y)</code>
<div class="block">按下</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/model/AMapGestureListener.html#onFling-float-float-">onFling</a></span>(float&nbsp;velocityX,
       float&nbsp;velocityY)</code>
<div class="block">惯性滑动</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/model/AMapGestureListener.html#onLongPress-float-float-">onLongPress</a></span>(float&nbsp;x,
           float&nbsp;y)</code>
<div class="block">长按</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/model/AMapGestureListener.html#onMapStable--">onMapStable</a></span>()</code>
<div class="block">地图稳定下来会回到此接口</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/model/AMapGestureListener.html#onScroll-float-float-">onScroll</a></span>(float&nbsp;distanceX,
        float&nbsp;distanceY)</code>
<div class="block">滑动</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/model/AMapGestureListener.html#onSingleTap-float-float-">onSingleTap</a></span>(float&nbsp;x,
           float&nbsp;y)</code>
<div class="block">单击</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/model/AMapGestureListener.html#onUp-float-float-">onUp</a></span>(float&nbsp;x,
    float&nbsp;y)</code>
<div class="block">抬起</div>
</td>
</tr>
</table>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="onDoubleTap-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onDoubleTap</h4>
<pre>void&nbsp;onDoubleTap(float&nbsp;x,
                 float&nbsp;y)</pre>
<div class="block">双击</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>x</code> - 像素 x坐标</dd>
<dd><code>y</code> - 像素 y坐标</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>4.0.0</dd>
</dl>
</li>
</ul>
<a name="onSingleTap-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onSingleTap</h4>
<pre>void&nbsp;onSingleTap(float&nbsp;x,
                 float&nbsp;y)</pre>
<div class="block">单击</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>x</code> - 像素 x坐标</dd>
<dd><code>y</code> - 像素 y坐标</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>4.0.0</dd>
</dl>
</li>
</ul>
<a name="onFling-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onFling</h4>
<pre>void&nbsp;onFling(float&nbsp;velocityX,
             float&nbsp;velocityY)</pre>
<div class="block">惯性滑动</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>velocityX</code> - 惯性滑动的水平速度</dd>
<dd><code>velocityY</code> - 惯性滑动的垂直速度</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>4.0.0</dd>
</dl>
</li>
</ul>
<a name="onScroll-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onScroll</h4>
<pre>void&nbsp;onScroll(float&nbsp;distanceX,
              float&nbsp;distanceY)</pre>
<div class="block">滑动</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>distanceX</code> - 滑动的水平距离</dd>
<dd><code>distanceY</code> - 滑动的垂直距离</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>4.0.0</dd>
</dl>
</li>
</ul>
<a name="onLongPress-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onLongPress</h4>
<pre>void&nbsp;onLongPress(float&nbsp;x,
                 float&nbsp;y)</pre>
<div class="block">长按</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>x</code> - 像素 x坐标</dd>
<dd><code>y</code> - 像素 y坐标</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>4.0.0</dd>
</dl>
</li>
</ul>
<a name="onDown-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onDown</h4>
<pre>void&nbsp;onDown(float&nbsp;x,
            float&nbsp;y)</pre>
<div class="block">按下</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>x</code> - 像素 x坐标</dd>
<dd><code>y</code> - 像素 y坐标</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>4.0.0</dd>
</dl>
</li>
</ul>
<a name="onUp-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onUp</h4>
<pre>void&nbsp;onUp(float&nbsp;x,
          float&nbsp;y)</pre>
<div class="block">抬起</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>x</code> - 像素 x坐标</dd>
<dd><code>y</code> - 像素 y坐标</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>4.0.0</dd>
</dl>
</li>
</ul>
<a name="onMapStable--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>onMapStable</h4>
<pre>void&nbsp;onMapStable()</pre>
<div class="block">地图稳定下来会回到此接口</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>4.0.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/AMapGestureListener.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/maps/model/AMapCameraInfo.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/amap/api/maps/model/AMapGLOverlay.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/maps/model/AMapGestureListener.html" target="_top">框架</a></li>
<li><a href="AMapGestureListener.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
