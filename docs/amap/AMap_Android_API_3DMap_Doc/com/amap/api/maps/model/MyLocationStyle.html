<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>MyLocationStyle</title>
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="MyLocationStyle";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10};
var tabs = {65535:["t0","所有方法"],2:["t2","实例方法"],8:["t4","具体方法"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/MyLocationStyle.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/maps/model/MVTTileProvider.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/amap/api/maps/model/MyTrafficStyle.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/maps/model/MyLocationStyle.html" target="_top">框架</a></li>
<li><a href="MyLocationStyle.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li><a href="#field.summary">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li><a href="#field.detail">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.amap.api.maps.model</div>
<h2 title="类 MyLocationStyle" class="title">类 MyLocationStyle</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.amap.api.maps.model.MyLocationStyle</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>所有已实现的接口:</dt>
<dd>android.os.Parcelable</dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">MyLocationStyle</span>
extends java.lang.Object
implements android.os.Parcelable</pre>
<div class="block">定位小蓝点（当前位置）的绘制样式类。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.4</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>嵌套类概要</h3>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.android.os.Parcelable">
<!--   -->
</a>
<h3>从接口继承的嵌套类/接口&nbsp;android.os.Parcelable</h3>
<code>android.os.Parcelable.ClassLoaderCreator&lt;T&gt;, android.os.Parcelable.Creator&lt;T&gt;</code></li>
</ul>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>字段概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="字段概要表, 列表字段和解释">
<caption><span>字段</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">字段和说明</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/model/MyLocationStyle.html#ERROR_CODE">ERROR_CODE</a></span></code>
<div class="block">在<a href="../../../../../com/amap/api/maps/AMap.OnMyLocationChangeListener.html#onMyLocationChange-android.location.Location-"><code>AMap.OnMyLocationChangeListener.onMyLocationChange(Location)</code></a> 中会返回定位信息<br>
     可通过 Location.getExtras().getInt(<a href="../../../../../com/amap/api/maps/model/MyLocationStyle.html#ERROR_CODE"><code>MyLocationStyle.ERROR_CODE</code></a>) 获取错误码</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/model/MyLocationStyle.html#ERROR_INFO">ERROR_INFO</a></span></code>
<div class="block">在<a href="../../../../../com/amap/api/maps/AMap.OnMyLocationChangeListener.html#onMyLocationChange-android.location.Location-"><code>AMap.OnMyLocationChangeListener.onMyLocationChange(Location)</code></a> 中会返回定位信息<br>
     可通过 Location.getExtras().getString(<a href="../../../../../com/amap/api/maps/model/MyLocationStyle.html#ERROR_INFO"><code>MyLocationStyle.ERROR_INFO</code></a>) 获取错误详细信息</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/model/MyLocationStyle.html#LOCATION_TYPE">LOCATION_TYPE</a></span></code>
<div class="block">在<a href="../../../../../com/amap/api/maps/AMap.OnMyLocationChangeListener.html#onMyLocationChange-android.location.Location-"><code>AMap.OnMyLocationChangeListener.onMyLocationChange(Location)</code></a> 中会返回定位信息<br>
     可通过 Location.getExtras().getInt(<a href="../../../../../com/amap/api/maps/model/MyLocationStyle.html#LOCATION_TYPE"><code>MyLocationStyle.LOCATION_TYPE</code></a>) 获取定位类型</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/model/MyLocationStyle.html#LOCATION_TYPE_FOLLOW">LOCATION_TYPE_FOLLOW</a></span></code>
<div class="block">定位、且将视角移动到地图中心点，定位点跟随设备移动。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/model/MyLocationStyle.html#LOCATION_TYPE_FOLLOW_NO_CENTER">LOCATION_TYPE_FOLLOW_NO_CENTER</a></span></code>
<div class="block">定位、但不会移动到地图中心点，并且会跟随设备移动。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/model/MyLocationStyle.html#LOCATION_TYPE_LOCATE">LOCATION_TYPE_LOCATE</a></span></code>
<div class="block">定位、且将视角移动到地图中心点。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/model/MyLocationStyle.html#LOCATION_TYPE_LOCATION_ROTATE">LOCATION_TYPE_LOCATION_ROTATE</a></span></code>
<div class="block">定位、且将视角移动到地图中心点，定位点依照设备方向旋转，并且会跟随设备移动。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/model/MyLocationStyle.html#LOCATION_TYPE_LOCATION_ROTATE_NO_CENTER">LOCATION_TYPE_LOCATION_ROTATE_NO_CENTER</a></span></code>
<div class="block">定位、但不会移动到地图中心点，定位点依照设备方向旋转，并且会跟随设备移动。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/model/MyLocationStyle.html#LOCATION_TYPE_MAP_ROTATE">LOCATION_TYPE_MAP_ROTATE</a></span></code>
<div class="block">定位、且将视角移动到地图中心点，地图依照设备方向旋转，定位点会跟随设备移动。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/model/MyLocationStyle.html#LOCATION_TYPE_MAP_ROTATE_NO_CENTER">LOCATION_TYPE_MAP_ROTATE_NO_CENTER</a></span></code>
<div class="block">定位、但不会移动到地图中心点，地图依照设备方向旋转，并且会跟随设备移动。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/model/MyLocationStyle.html#LOCATION_TYPE_SHOW">LOCATION_TYPE_SHOW</a></span></code>
<div class="block">只定位。</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.android.os.Parcelable">
<!--   -->
</a>
<h3>从接口继承的字段&nbsp;android.os.Parcelable</h3>
<code>CONTENTS_FILE_DESCRIPTOR, PARCELABLE_WRITE_RETURN_VALUE</code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/model/MyLocationStyle.html#MyLocationStyle--">MyLocationStyle</a></span>()</code>
<div class="block">定位（当前位置）的绘制样式类的构造函数</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>所有方法</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">实例方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">具体方法</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/maps/model/MyLocationStyle.html" title="com.amap.api.maps.model中的类">MyLocationStyle</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/model/MyLocationStyle.html#anchor-float-float-">anchor</a></span>(float&nbsp;u,
      float&nbsp;v)</code>
<div class="block">设置定位图标的锚点。</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/model/MyLocationStyle.html#getAnchorU--">getAnchorU</a></span>()</code>
<div class="block">得到锚点横坐标方向的偏移量。</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/model/MyLocationStyle.html#getAnchorV--">getAnchorV</a></span>()</code>
<div class="block">得到锚点纵坐标方向的偏移量。</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/model/MyLocationStyle.html#getInterval--">getInterval</a></span>()</code>
<div class="block">得到定位请求时间间隔。</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/maps/model/BitmapDescriptor.html" title="com.amap.api.maps.model中的类">BitmapDescriptor</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/model/MyLocationStyle.html#getMyLocationIcon--">getMyLocationIcon</a></span>()</code>
<div class="block">得到当前位置的图标。</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/model/MyLocationStyle.html#getMyLocationType--">getMyLocationType</a></span>()</code>
<div class="block">得到我的位置展示模式。</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/model/MyLocationStyle.html#getRadiusFillColor--">getRadiusFillColor</a></span>()</code>
<div class="block">得到圆形区域（以定位位置为圆心，定位半径的圆形区域）的填充颜色值。</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/model/MyLocationStyle.html#getStrokeColor--">getStrokeColor</a></span>()</code>
<div class="block">得到圆形区域（以定位位置为圆心，定位半径的圆形区域）边框的颜色值。</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/model/MyLocationStyle.html#getStrokeWidth--">getStrokeWidth</a></span>()</code>
<div class="block">得到圆形区域（以定位位置为圆心，定位半径的圆形区域）边框的宽度。</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/maps/model/MyLocationStyle.html" title="com.amap.api.maps.model中的类">MyLocationStyle</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/model/MyLocationStyle.html#interval-long-">interval</a></span>(long&nbsp;interval)</code>
<div class="block">设置发起定位请求的时间间隔，单位：毫秒，默认值：1000毫秒，如果传小于1000的任何值将执行单次定位。</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/model/MyLocationStyle.html#isMyLocationShowing--">isMyLocationShowing</a></span>()</code>
<div class="block">得到是否显示定位小蓝点。</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/maps/model/MyLocationStyle.html" title="com.amap.api.maps.model中的类">MyLocationStyle</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/model/MyLocationStyle.html#myLocationIcon-com.amap.api.maps.model.BitmapDescriptor-">myLocationIcon</a></span>(<a href="../../../../../com/amap/api/maps/model/BitmapDescriptor.html" title="com.amap.api.maps.model中的类">BitmapDescriptor</a>&nbsp;myLocationIcon)</code>
<div class="block">设置定位（当前位置）的icon图标。</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/maps/model/MyLocationStyle.html" title="com.amap.api.maps.model中的类">MyLocationStyle</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/model/MyLocationStyle.html#myLocationType-int-">myLocationType</a></span>(int&nbsp;type)</code>
<div class="block">设置我的位置展示模式，模式分别为 <br>
 <a href="../../../../../com/amap/api/maps/model/MyLocationStyle.html#LOCATION_TYPE_SHOW"><code>MyLocationStyle.LOCATION_TYPE_SHOW</code></a>
 <a href="../../../../../com/amap/api/maps/model/MyLocationStyle.html#LOCATION_TYPE_LOCATE"><code>MyLocationStyle.LOCATION_TYPE_LOCATE</code></a>
 <a href="../../../../../com/amap/api/maps/model/MyLocationStyle.html#LOCATION_TYPE_MAP_ROTATE"><code>MyLocationStyle.LOCATION_TYPE_MAP_ROTATE</code></a>
 <a href="../../../../../com/amap/api/maps/model/MyLocationStyle.html#LOCATION_TYPE_LOCATION_ROTATE"><code>MyLocationStyle.LOCATION_TYPE_LOCATION_ROTATE</code></a> (默认)</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/maps/model/MyLocationStyle.html" title="com.amap.api.maps.model中的类">MyLocationStyle</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/model/MyLocationStyle.html#radiusFillColor-int-">radiusFillColor</a></span>(int&nbsp;color)</code>
<div class="block">设置圆形区域（以定位位置为圆心，定位半径的圆形区域）的填充颜色。</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/maps/model/MyLocationStyle.html" title="com.amap.api.maps.model中的类">MyLocationStyle</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/model/MyLocationStyle.html#showMyLocation-boolean-">showMyLocation</a></span>(boolean&nbsp;myLocationVisible)</code>
<div class="block">设置是否显示定位小蓝点，true 显示，false不显示。</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/maps/model/MyLocationStyle.html" title="com.amap.api.maps.model中的类">MyLocationStyle</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/model/MyLocationStyle.html#strokeColor-int-">strokeColor</a></span>(int&nbsp;color)</code>
<div class="block">设置圆形区域（以定位位置为圆心，定位半径的圆形区域）的边框颜色。</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/maps/model/MyLocationStyle.html" title="com.amap.api.maps.model中的类">MyLocationStyle</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/model/MyLocationStyle.html#strokeWidth-float-">strokeWidth</a></span>(float&nbsp;width)</code>
<div class="block">设置圆形区域（以定位位置为圆心，定位半径的圆形区域）的边框宽度。</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.android.os.Parcelable">
<!--   -->
</a>
<h3>从接口继承的方法&nbsp;android.os.Parcelable</h3>
<code>describeContents, writeToParcel</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>字段详细资料</h3>
<a name="ERROR_CODE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ERROR_CODE</h4>
<pre>public static final&nbsp;java.lang.String ERROR_CODE</pre>
<div class="block">在<a href="../../../../../com/amap/api/maps/AMap.OnMyLocationChangeListener.html#onMyLocationChange-android.location.Location-"><code>AMap.OnMyLocationChangeListener.onMyLocationChange(Location)</code></a> 中会返回定位信息<br>
     可通过 Location.getExtras().getInt(<a href="../../../../../com/amap/api/maps/model/MyLocationStyle.html#ERROR_CODE"><code>MyLocationStyle.ERROR_CODE</code></a>) 获取错误码</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>5.0.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.maps.model.MyLocationStyle.ERROR_CODE">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="ERROR_INFO">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ERROR_INFO</h4>
<pre>public static final&nbsp;java.lang.String ERROR_INFO</pre>
<div class="block">在<a href="../../../../../com/amap/api/maps/AMap.OnMyLocationChangeListener.html#onMyLocationChange-android.location.Location-"><code>AMap.OnMyLocationChangeListener.onMyLocationChange(Location)</code></a> 中会返回定位信息<br>
     可通过 Location.getExtras().getString(<a href="../../../../../com/amap/api/maps/model/MyLocationStyle.html#ERROR_INFO"><code>MyLocationStyle.ERROR_INFO</code></a>) 获取错误详细信息</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>5.0.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.maps.model.MyLocationStyle.ERROR_INFO">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="LOCATION_TYPE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LOCATION_TYPE</h4>
<pre>public static final&nbsp;java.lang.String LOCATION_TYPE</pre>
<div class="block">在<a href="../../../../../com/amap/api/maps/AMap.OnMyLocationChangeListener.html#onMyLocationChange-android.location.Location-"><code>AMap.OnMyLocationChangeListener.onMyLocationChange(Location)</code></a> 中会返回定位信息<br>
     可通过 Location.getExtras().getInt(<a href="../../../../../com/amap/api/maps/model/MyLocationStyle.html#LOCATION_TYPE"><code>MyLocationStyle.LOCATION_TYPE</code></a>) 获取定位类型</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>5.0.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.maps.model.MyLocationStyle.LOCATION_TYPE">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="LOCATION_TYPE_SHOW">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LOCATION_TYPE_SHOW</h4>
<pre>public static final&nbsp;int LOCATION_TYPE_SHOW</pre>
<div class="block">只定位。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>5.0.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.maps.model.MyLocationStyle.LOCATION_TYPE_SHOW">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="LOCATION_TYPE_LOCATE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LOCATION_TYPE_LOCATE</h4>
<pre>public static final&nbsp;int LOCATION_TYPE_LOCATE</pre>
<div class="block">定位、且将视角移动到地图中心点。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>5.0.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.maps.model.MyLocationStyle.LOCATION_TYPE_LOCATE">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="LOCATION_TYPE_FOLLOW">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LOCATION_TYPE_FOLLOW</h4>
<pre>public static final&nbsp;int LOCATION_TYPE_FOLLOW</pre>
<div class="block">定位、且将视角移动到地图中心点，定位点跟随设备移动。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>5.0.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.maps.model.MyLocationStyle.LOCATION_TYPE_FOLLOW">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="LOCATION_TYPE_MAP_ROTATE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LOCATION_TYPE_MAP_ROTATE</h4>
<pre>public static final&nbsp;int LOCATION_TYPE_MAP_ROTATE</pre>
<div class="block">定位、且将视角移动到地图中心点，地图依照设备方向旋转，定位点会跟随设备移动。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>5.0.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.maps.model.MyLocationStyle.LOCATION_TYPE_MAP_ROTATE">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="LOCATION_TYPE_LOCATION_ROTATE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LOCATION_TYPE_LOCATION_ROTATE</h4>
<pre>public static final&nbsp;int LOCATION_TYPE_LOCATION_ROTATE</pre>
<div class="block">定位、且将视角移动到地图中心点，定位点依照设备方向旋转，并且会跟随设备移动。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>5.0.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.maps.model.MyLocationStyle.LOCATION_TYPE_LOCATION_ROTATE">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="LOCATION_TYPE_LOCATION_ROTATE_NO_CENTER">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LOCATION_TYPE_LOCATION_ROTATE_NO_CENTER</h4>
<pre>public static final&nbsp;int LOCATION_TYPE_LOCATION_ROTATE_NO_CENTER</pre>
<div class="block">定位、但不会移动到地图中心点，定位点依照设备方向旋转，并且会跟随设备移动。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>5.1.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.maps.model.MyLocationStyle.LOCATION_TYPE_LOCATION_ROTATE_NO_CENTER">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="LOCATION_TYPE_FOLLOW_NO_CENTER">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LOCATION_TYPE_FOLLOW_NO_CENTER</h4>
<pre>public static final&nbsp;int LOCATION_TYPE_FOLLOW_NO_CENTER</pre>
<div class="block">定位、但不会移动到地图中心点，并且会跟随设备移动。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>5.1.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.maps.model.MyLocationStyle.LOCATION_TYPE_FOLLOW_NO_CENTER">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="LOCATION_TYPE_MAP_ROTATE_NO_CENTER">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>LOCATION_TYPE_MAP_ROTATE_NO_CENTER</h4>
<pre>public static final&nbsp;int LOCATION_TYPE_MAP_ROTATE_NO_CENTER</pre>
<div class="block">定位、但不会移动到地图中心点，地图依照设备方向旋转，并且会跟随设备移动。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>5.1.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.maps.model.MyLocationStyle.LOCATION_TYPE_MAP_ROTATE_NO_CENTER">常量字段值</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="MyLocationStyle--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>MyLocationStyle</h4>
<pre>public&nbsp;MyLocationStyle()</pre>
<div class="block">定位（当前位置）的绘制样式类的构造函数</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.4</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="myLocationIcon-com.amap.api.maps.model.BitmapDescriptor-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>myLocationIcon</h4>
<pre>public&nbsp;<a href="../../../../../com/amap/api/maps/model/MyLocationStyle.html" title="com.amap.api.maps.model中的类">MyLocationStyle</a>&nbsp;myLocationIcon(<a href="../../../../../com/amap/api/maps/model/BitmapDescriptor.html" title="com.amap.api.maps.model中的类">BitmapDescriptor</a>&nbsp;myLocationIcon)</pre>
<div class="block">设置定位（当前位置）的icon图标。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>myLocationIcon</code> - 使用的位置图标。</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>一个设置了定位图标的MyLocationStyle对象。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.4</dd>
</dl>
</li>
</ul>
<a name="anchor-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>anchor</h4>
<pre>public&nbsp;<a href="../../../../../com/amap/api/maps/model/MyLocationStyle.html" title="com.amap.api.maps.model中的类">MyLocationStyle</a>&nbsp;anchor(float&nbsp;u,
                              float&nbsp;v)</pre>
<div class="block">设置定位图标的锚点。
 锚点是定位图标接触地图平面的点。图标的左顶点为（0,0）点，右底点为（1,1）点。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>u</code> - 锚点水平范围的比例，建议传入0 到1 之间的数值。</dd>
<dd><code>v</code> - 锚点垂直范围的比例，建议传入0 到1 之间的数值。</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>一个设置了定位图标锚点的MyLocationStyle对象。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.4</dd>
</dl>
</li>
</ul>
<a name="radiusFillColor-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>radiusFillColor</h4>
<pre>public&nbsp;<a href="../../../../../com/amap/api/maps/model/MyLocationStyle.html" title="com.amap.api.maps.model中的类">MyLocationStyle</a>&nbsp;radiusFillColor(int&nbsp;color)</pre>
<div class="block">设置圆形区域（以定位位置为圆心，定位半径的圆形区域）的填充颜色。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>color</code> - 圆形区域的填充颜色。</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>一个设置了定位圆形区域填充颜色的MyLocationStyle对象。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.4</dd>
</dl>
</li>
</ul>
<a name="strokeColor-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>strokeColor</h4>
<pre>public&nbsp;<a href="../../../../../com/amap/api/maps/model/MyLocationStyle.html" title="com.amap.api.maps.model中的类">MyLocationStyle</a>&nbsp;strokeColor(int&nbsp;color)</pre>
<div class="block">设置圆形区域（以定位位置为圆心，定位半径的圆形区域）的边框颜色。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>color</code> - 圆形区域的边框颜色。</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>一个设置了定位圆形区域边框颜色的MyLocationStyle对象。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.4</dd>
</dl>
</li>
</ul>
<a name="strokeWidth-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>strokeWidth</h4>
<pre>public&nbsp;<a href="../../../../../com/amap/api/maps/model/MyLocationStyle.html" title="com.amap.api.maps.model中的类">MyLocationStyle</a>&nbsp;strokeWidth(float&nbsp;width)</pre>
<div class="block">设置圆形区域（以定位位置为圆心，定位半径的圆形区域）的边框宽度。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>width</code> - 圆形区域的边框宽度。</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>一个设置了定位圆形区域边框宽度的MyLocationStyle对象。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.4</dd>
</dl>
</li>
</ul>
<a name="myLocationType-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>myLocationType</h4>
<pre>public&nbsp;<a href="../../../../../com/amap/api/maps/model/MyLocationStyle.html" title="com.amap.api.maps.model中的类">MyLocationStyle</a>&nbsp;myLocationType(int&nbsp;type)</pre>
<div class="block">设置我的位置展示模式，模式分别为 <br>
 <tr><a href="../../../../../com/amap/api/maps/model/MyLocationStyle.html#LOCATION_TYPE_SHOW"><code>MyLocationStyle.LOCATION_TYPE_SHOW</code></a></tr>
 <tr><a href="../../../../../com/amap/api/maps/model/MyLocationStyle.html#LOCATION_TYPE_LOCATE"><code>MyLocationStyle.LOCATION_TYPE_LOCATE</code></a></tr>
 <tr><a href="../../../../../com/amap/api/maps/model/MyLocationStyle.html#LOCATION_TYPE_MAP_ROTATE"><code>MyLocationStyle.LOCATION_TYPE_MAP_ROTATE</code></a></tr>
 <tr><a href="../../../../../com/amap/api/maps/model/MyLocationStyle.html#LOCATION_TYPE_LOCATION_ROTATE"><code>MyLocationStyle.LOCATION_TYPE_LOCATION_ROTATE</code></a></tr> (默认)</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>一个设置了展示模式的MyLocationStyle对象。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>5.0.0</dd>
</dl>
</li>
</ul>
<a name="interval-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>interval</h4>
<pre>public&nbsp;<a href="../../../../../com/amap/api/maps/model/MyLocationStyle.html" title="com.amap.api.maps.model中的类">MyLocationStyle</a>&nbsp;interval(long&nbsp;interval)</pre>
<div class="block">设置发起定位请求的时间间隔，单位：毫秒，默认值：1000毫秒，如果传小于1000的任何值将执行单次定位。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>一个设置了定位请求时间间隔的MyLocationStyle对象。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>5.0.0</dd>
</dl>
</li>
</ul>
<a name="showMyLocation-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showMyLocation</h4>
<pre>public&nbsp;<a href="../../../../../com/amap/api/maps/model/MyLocationStyle.html" title="com.amap.api.maps.model中的类">MyLocationStyle</a>&nbsp;showMyLocation(boolean&nbsp;myLocationVisible)</pre>
<div class="block">设置是否显示定位小蓝点，true 显示，false不显示。<br>
 默认为true，设置false不显示定位小蓝点但是会持续定位</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>一个设置了是否显示定位小蓝点的MyLocationStyle对象。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>5.1.0</dd>
</dl>
</li>
</ul>
<a name="getMyLocationIcon--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMyLocationIcon</h4>
<pre>public&nbsp;<a href="../../../../../com/amap/api/maps/model/BitmapDescriptor.html" title="com.amap.api.maps.model中的类">BitmapDescriptor</a>&nbsp;getMyLocationIcon()</pre>
<div class="block">得到当前位置的图标。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>当前位置的图标。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.4</dd>
</dl>
</li>
</ul>
<a name="getAnchorU--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAnchorU</h4>
<pre>public&nbsp;float&nbsp;getAnchorU()</pre>
<div class="block">得到锚点横坐标方向的偏移量。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>锚点横坐标方向的偏移量。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.4</dd>
</dl>
</li>
</ul>
<a name="getAnchorV--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAnchorV</h4>
<pre>public&nbsp;float&nbsp;getAnchorV()</pre>
<div class="block">得到锚点纵坐标方向的偏移量。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>锚点纵坐标方向的偏移量。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.4</dd>
</dl>
</li>
</ul>
<a name="getRadiusFillColor--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRadiusFillColor</h4>
<pre>public&nbsp;int&nbsp;getRadiusFillColor()</pre>
<div class="block">得到圆形区域（以定位位置为圆心，定位半径的圆形区域）的填充颜色值。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>圆形区域的填充颜色值。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.4</dd>
</dl>
</li>
</ul>
<a name="getStrokeColor--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStrokeColor</h4>
<pre>public&nbsp;int&nbsp;getStrokeColor()</pre>
<div class="block">得到圆形区域（以定位位置为圆心，定位半径的圆形区域）边框的颜色值。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>圆形区域边框的颜色值。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.4</dd>
</dl>
</li>
</ul>
<a name="getStrokeWidth--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStrokeWidth</h4>
<pre>public&nbsp;float&nbsp;getStrokeWidth()</pre>
<div class="block">得到圆形区域（以定位位置为圆心，定位半径的圆形区域）边框的宽度。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>圆形区域边框的宽度。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.4</dd>
</dl>
</li>
</ul>
<a name="getMyLocationType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMyLocationType</h4>
<pre>public&nbsp;int&nbsp;getMyLocationType()</pre>
<div class="block">得到我的位置展示模式。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>我的位置展示模式。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>5.0.0</dd>
</dl>
</li>
</ul>
<a name="getInterval--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getInterval</h4>
<pre>public&nbsp;long&nbsp;getInterval()</pre>
<div class="block">得到定位请求时间间隔。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>定位请求时间间隔。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>5.0.0</dd>
</dl>
</li>
</ul>
<a name="isMyLocationShowing--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>isMyLocationShowing</h4>
<pre>public&nbsp;boolean&nbsp;isMyLocationShowing()</pre>
<div class="block">得到是否显示定位小蓝点。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>是否显示定位小蓝点。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>5.1.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/MyLocationStyle.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/maps/model/MVTTileProvider.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/amap/api/maps/model/MyTrafficStyle.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/maps/model/MyLocationStyle.html" target="_top">框架</a></li>
<li><a href="MyLocationStyle.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li><a href="#field.summary">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li><a href="#field.detail">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
