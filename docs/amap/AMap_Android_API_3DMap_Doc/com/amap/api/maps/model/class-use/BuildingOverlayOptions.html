<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>类 com.amap.api.maps.model.BuildingOverlayOptions的使用</title>
<link rel="stylesheet" type="text/css" href="../../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="\u7C7B com.amap.api.maps.model.BuildingOverlayOptions\u7684\u4F7F\u7528";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../../overview-summary.html">概览</a></li>
<li><a href="../package-summary.html">程序包</a></li>
<li><a href="../../../../../../com/amap/api/maps/model/BuildingOverlayOptions.html" title="com.amap.api.maps.model中的类">类</a></li>
<li class="navBarCell1Rev">使用</li>
<li><a href="../../../../../../overview-tree.html">树</a></li>
<li><a href="../../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个</li>
<li>下一个</li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?com/amap/api/maps/model/class-use/BuildingOverlayOptions.html" target="_top">框架</a></li>
<li><a href="BuildingOverlayOptions.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h2 title="类的使用 com.amap.api.maps.model.BuildingOverlayOptions" class="title">类的使用<br>com.amap.api.maps.model.BuildingOverlayOptions</h2>
</div>
<div class="classUseContainer">
<ul class="blockList">
<li class="blockList">
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表程序包和解释">
<caption><span>使用<a href="../../../../../../com/amap/api/maps/model/BuildingOverlayOptions.html" title="com.amap.api.maps.model中的类">BuildingOverlayOptions</a>的程序包</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">程序包</th>
<th class="colLast" scope="col">说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="#com.amap.api.maps.model">com.amap.api.maps.model</a></td>
<td class="colLast">
<div class="block">
覆盖物包，覆盖物（叠加或覆盖到地图的内容）支持标记、折线、多边形和圆。</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<ul class="blockList">
<li class="blockList"><a name="com.amap.api.maps.model">
<!--   -->
</a>
<h3><a href="../../../../../../com/amap/api/maps/model/package-summary.html">com.amap.api.maps.model</a>中<a href="../../../../../../com/amap/api/maps/model/BuildingOverlayOptions.html" title="com.amap.api.maps.model中的类">BuildingOverlayOptions</a>的使用</h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表字段和解释">
<caption><span>类型参数类型为<a href="../../../../../../com/amap/api/maps/model/BuildingOverlayOptions.html" title="com.amap.api.maps.model中的类">BuildingOverlayOptions</a>的<a href="../../../../../../com/amap/api/maps/model/package-summary.html">com.amap.api.maps.model</a>中的字段</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">字段和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../../../com/amap/api/maps/model/BuildingOverlayOptions.html" title="com.amap.api.maps.model中的类">BuildingOverlayOptions</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">BuildingOverlay.BuildingOverlayTotalOptions.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/maps/model/BuildingOverlay.BuildingOverlayTotalOptions.html#allOptionList">allOptionList</a></span></code>&nbsp;</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表方法和解释">
<caption><span>返回<a href="../../../../../../com/amap/api/maps/model/BuildingOverlayOptions.html" title="com.amap.api.maps.model中的类">BuildingOverlayOptions</a>的<a href="../../../../../../com/amap/api/maps/model/package-summary.html">com.amap.api.maps.model</a>中的方法</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/maps/model/BuildingOverlayOptions.html" title="com.amap.api.maps.model中的类">BuildingOverlayOptions</a></code></td>
<td class="colLast"><span class="typeNameLabel">BuildingOverlay.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/maps/model/BuildingOverlay.html#getDefaultOptions--">getDefaultOptions</a></span>()</code>
<div class="block">获取默认配置信息</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/maps/model/BuildingOverlayOptions.html" title="com.amap.api.maps.model中的类">BuildingOverlayOptions</a></code></td>
<td class="colLast"><span class="typeNameLabel">BuildingOverlayOptions.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/maps/model/BuildingOverlayOptions.html#setBuildingHeight-int-">setBuildingHeight</a></span>(int&nbsp;buildingHeight)</code>
<div class="block">设置建筑物的高度</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/maps/model/BuildingOverlayOptions.html" title="com.amap.api.maps.model中的类">BuildingOverlayOptions</a></code></td>
<td class="colLast"><span class="typeNameLabel">BuildingOverlayOptions.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/maps/model/BuildingOverlayOptions.html#setBuildingHeightScale-int-">setBuildingHeightScale</a></span>(int&nbsp;buildingHeightScale)</code>
<div class="block">设置overlay的建筑物相对于默认高度的倍数，此方法与setBuildingHeight方法互斥，调用setBuildingHeight方法将使用此倍数值失效。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/maps/model/BuildingOverlayOptions.html" title="com.amap.api.maps.model中的类">BuildingOverlayOptions</a></code></td>
<td class="colLast"><span class="typeNameLabel">BuildingOverlayOptions.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/maps/model/BuildingOverlayOptions.html#setBuildingLatlngs-java.util.List-">setBuildingLatlngs</a></span>(java.util.List&lt;<a href="../../../../../../com/amap/api/maps/model/LatLng.html" title="com.amap.api.maps.model中的类">LatLng</a>&gt;&nbsp;buildingLatlngs)</code>
<div class="block">设置建筑物围栏坐标列表</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/maps/model/BuildingOverlayOptions.html" title="com.amap.api.maps.model中的类">BuildingOverlayOptions</a></code></td>
<td class="colLast"><span class="typeNameLabel">BuildingOverlayOptions.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/maps/model/BuildingOverlayOptions.html#setBuildingSideColor-int-">setBuildingSideColor</a></span>(int&nbsp;buildingSideColor)</code>
<div class="block">设置建筑物侧面颜色值</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/maps/model/BuildingOverlayOptions.html" title="com.amap.api.maps.model中的类">BuildingOverlayOptions</a></code></td>
<td class="colLast"><span class="typeNameLabel">BuildingOverlayOptions.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/maps/model/BuildingOverlayOptions.html#setBuildingTopColor-int-">setBuildingTopColor</a></span>(int&nbsp;buildingTopColor)</code>
<div class="block">设置建筑物顶部颜色值</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表方法和解释">
<caption><span>返回变量类型为<a href="../../../../../../com/amap/api/maps/model/BuildingOverlayOptions.html" title="com.amap.api.maps.model中的类">BuildingOverlayOptions</a>的类型的<a href="../../../../../../com/amap/api/maps/model/package-summary.html">com.amap.api.maps.model</a>中的方法</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../../../com/amap/api/maps/model/BuildingOverlayOptions.html" title="com.amap.api.maps.model中的类">BuildingOverlayOptions</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">BuildingOverlay.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/maps/model/BuildingOverlay.html#getCustomOptions--">getCustomOptions</a></span>()</code>
<div class="block">获取用户设置的自定义BuildingOverlayOptions列表</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表方法和解释">
<caption><span>参数类型为<a href="../../../../../../com/amap/api/maps/model/BuildingOverlayOptions.html" title="com.amap.api.maps.model中的类">BuildingOverlayOptions</a>的<a href="../../../../../../com/amap/api/maps/model/package-summary.html">com.amap.api.maps.model</a>中的方法</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">BuildingOverlay.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/maps/model/BuildingOverlay.html#setDefaultOptions-com.amap.api.maps.model.BuildingOverlayOptions-">setDefaultOptions</a></span>(<a href="../../../../../../com/amap/api/maps/model/BuildingOverlayOptions.html" title="com.amap.api.maps.model中的类">BuildingOverlayOptions</a>&nbsp;options)</code>
<div class="block">设置overlay的显示区域，默认全世界区域</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表方法和解释">
<caption><span>类型变量类型为<a href="../../../../../../com/amap/api/maps/model/BuildingOverlayOptions.html" title="com.amap.api.maps.model中的类">BuildingOverlayOptions</a>的<a href="../../../../../../com/amap/api/maps/model/package-summary.html">com.amap.api.maps.model</a>中的方法参数</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">BuildingOverlay.</span><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/maps/model/BuildingOverlay.html#setCustomOptions-java.util.List-">setCustomOptions</a></span>(java.util.List&lt;<a href="../../../../../../com/amap/api/maps/model/BuildingOverlayOptions.html" title="com.amap.api.maps.model中的类">BuildingOverlayOptions</a>&gt;&nbsp;options)</code>
<div class="block">设置自定义overlay子区域.</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../../overview-summary.html">概览</a></li>
<li><a href="../package-summary.html">程序包</a></li>
<li><a href="../../../../../../com/amap/api/maps/model/BuildingOverlayOptions.html" title="com.amap.api.maps.model中的类">类</a></li>
<li class="navBarCell1Rev">使用</li>
<li><a href="../../../../../../overview-tree.html">树</a></li>
<li><a href="../../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个</li>
<li>下一个</li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?com/amap/api/maps/model/class-use/BuildingOverlayOptions.html" target="_top">框架</a></li>
<li><a href="BuildingOverlayOptions.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
