<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>类 com.amap.api.maps.model.particle.ParticleOverlayOptions的使用</title>
<link rel="stylesheet" type="text/css" href="../../../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="\u7C7B com.amap.api.maps.model.particle.ParticleOverlayOptions\u7684\u4F7F\u7528";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../../../overview-summary.html">概览</a></li>
<li><a href="../package-summary.html">程序包</a></li>
<li><a href="../../../../../../../com/amap/api/maps/model/particle/ParticleOverlayOptions.html" title="com.amap.api.maps.model.particle中的类">类</a></li>
<li class="navBarCell1Rev">使用</li>
<li><a href="../../../../../../../overview-tree.html">树</a></li>
<li><a href="../../../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个</li>
<li>下一个</li>
</ul>
<ul class="navList">
<li><a href="../../../../../../../index.html?com/amap/api/maps/model/particle/class-use/ParticleOverlayOptions.html" target="_top">框架</a></li>
<li><a href="ParticleOverlayOptions.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h2 title="类的使用 com.amap.api.maps.model.particle.ParticleOverlayOptions" class="title">类的使用<br>com.amap.api.maps.model.particle.ParticleOverlayOptions</h2>
</div>
<div class="classUseContainer">
<ul class="blockList">
<li class="blockList">
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表程序包和解释">
<caption><span>使用<a href="../../../../../../../com/amap/api/maps/model/particle/ParticleOverlayOptions.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlayOptions</a>的程序包</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">程序包</th>
<th class="colLast" scope="col">说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="#com.amap.api.maps">com.amap.api.maps</a></td>
<td class="colLast">
<div class="block">
地图显示包，帮助您在Android应用程序中显示高德地图，并且支持地图交互手势的功能。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.amap.api.maps.model.particle">com.amap.api.maps.model.particle</a></td>
<td class="colLast">
<div class="block">
  粒子效果类，用于添加粒子效果覆盖物。</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<ul class="blockList">
<li class="blockList"><a name="com.amap.api.maps">
<!--   -->
</a>
<h3><a href="../../../../../../../com/amap/api/maps/package-summary.html">com.amap.api.maps</a>中<a href="../../../../../../../com/amap/api/maps/model/particle/ParticleOverlayOptions.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlayOptions</a>的使用</h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表方法和解释">
<caption><span>参数类型为<a href="../../../../../../../com/amap/api/maps/model/particle/ParticleOverlayOptions.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlayOptions</a>的<a href="../../../../../../../com/amap/api/maps/package-summary.html">com.amap.api.maps</a>中的方法</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../../../com/amap/api/maps/model/particle/ParticleOverlay.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlay</a></code></td>
<td class="colLast"><span class="typeNameLabel">AMap.</span><code><span class="memberNameLink"><a href="../../../../../../../com/amap/api/maps/AMap.html#addParticleOverlay-com.amap.api.maps.model.particle.ParticleOverlayOptions-">addParticleOverlay</a></span>(<a href="../../../../../../../com/amap/api/maps/model/particle/ParticleOverlayOptions.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlayOptions</a>&nbsp;options)</code>
<div class="block">在地图上添加一个粒子系统对象</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.amap.api.maps.model.particle">
<!--   -->
</a>
<h3><a href="../../../../../../../com/amap/api/maps/model/particle/package-summary.html">com.amap.api.maps.model.particle</a>中<a href="../../../../../../../com/amap/api/maps/model/particle/ParticleOverlayOptions.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlayOptions</a>的使用</h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表方法和解释">
<caption><span>返回<a href="../../../../../../../com/amap/api/maps/model/particle/ParticleOverlayOptions.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlayOptions</a>的<a href="../../../../../../../com/amap/api/maps/model/particle/package-summary.html">com.amap.api.maps.model.particle</a>中的方法</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../../../com/amap/api/maps/model/particle/ParticleOverlayOptions.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlayOptions</a></code></td>
<td class="colLast"><span class="typeNameLabel">ParticleOverlayOptions.</span><code><span class="memberNameLink"><a href="../../../../../../../com/amap/api/maps/model/particle/ParticleOverlayOptions.html#icon-com.amap.api.maps.model.BitmapDescriptor-">icon</a></span>(<a href="../../../../../../../com/amap/api/maps/model/BitmapDescriptor.html" title="com.amap.api.maps.model中的类">BitmapDescriptor</a>&nbsp;icon)</code>
<div class="block">设置覆盖物的图标。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../../../com/amap/api/maps/model/particle/ParticleOverlayOptions.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlayOptions</a></code></td>
<td class="colLast"><span class="typeNameLabel">ParticleOverlayOptions.</span><code><span class="memberNameLink"><a href="../../../../../../../com/amap/api/maps/model/particle/ParticleOverlayOptions.html#setDuration-long-">setDuration</a></span>(long&nbsp;duration)</code>
<div class="block">整个粒子效果的存活时间,单位毫秒</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../../../com/amap/api/maps/model/particle/ParticleOverlayOptions.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlayOptions</a></code></td>
<td class="colLast"><span class="typeNameLabel">ParticleOverlayOptions.</span><code><span class="memberNameLink"><a href="../../../../../../../com/amap/api/maps/model/particle/ParticleOverlayOptions.html#setLoop-boolean-">setLoop</a></span>(boolean&nbsp;loop)</code>
<div class="block">整个粒子效果是否循环</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../../../com/amap/api/maps/model/particle/ParticleOverlayOptions.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlayOptions</a></code></td>
<td class="colLast"><span class="typeNameLabel">ParticleOverlayOptions.</span><code><span class="memberNameLink"><a href="../../../../../../../com/amap/api/maps/model/particle/ParticleOverlayOptions.html#setMaxParticles-int-">setMaxParticles</a></span>(int&nbsp;maxParticles)</code>
<div class="block">整个粒子效果的粒子最大数量</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../../../com/amap/api/maps/model/particle/ParticleOverlayOptions.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlayOptions</a></code></td>
<td class="colLast"><span class="typeNameLabel">ParticleOverlayOptions.</span><code><span class="memberNameLink"><a href="../../../../../../../com/amap/api/maps/model/particle/ParticleOverlayOptions.html#setParticleEmissionModule-com.amap.api.maps.model.particle.ParticleEmissionModule-">setParticleEmissionModule</a></span>(<a href="../../../../../../../com/amap/api/maps/model/particle/ParticleEmissionModule.html" title="com.amap.api.maps.model.particle中的类">ParticleEmissionModule</a>&nbsp;particleEmissionModule)</code>
<div class="block">发射率，每个多少时间发射粒子数量，越多会越密集</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../../../com/amap/api/maps/model/particle/ParticleOverlayOptions.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlayOptions</a></code></td>
<td class="colLast"><span class="typeNameLabel">ParticleOverlayOptions.</span><code><span class="memberNameLink"><a href="../../../../../../../com/amap/api/maps/model/particle/ParticleOverlayOptions.html#setParticleLifeTime-long-">setParticleLifeTime</a></span>(long&nbsp;particleLifeTime)</code>
<div class="block">每个粒子的存活时间,单位毫秒</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../../../com/amap/api/maps/model/particle/ParticleOverlayOptions.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlayOptions</a></code></td>
<td class="colLast"><span class="typeNameLabel">ParticleOverlayOptions.</span><code><span class="memberNameLink"><a href="../../../../../../../com/amap/api/maps/model/particle/ParticleOverlayOptions.html#setParticleOverLifeModule-com.amap.api.maps.model.particle.ParticleOverLifeModule-">setParticleOverLifeModule</a></span>(<a href="../../../../../../../com/amap/api/maps/model/particle/ParticleOverLifeModule.html" title="com.amap.api.maps.model.particle中的类">ParticleOverLifeModule</a>&nbsp;particleOverLifeModule)</code>
<div class="block">每个粒子生命周期过程中状态变化，包含速度、旋转和颜色的变化</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../../../com/amap/api/maps/model/particle/ParticleOverlayOptions.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlayOptions</a></code></td>
<td class="colLast"><span class="typeNameLabel">ParticleOverlayOptions.</span><code><span class="memberNameLink"><a href="../../../../../../../com/amap/api/maps/model/particle/ParticleOverlayOptions.html#setParticleShapeModule-com.amap.api.maps.model.particle.ParticleShapeModule-">setParticleShapeModule</a></span>(<a href="../../../../../../../com/amap/api/maps/model/particle/ParticleShapeModule.html" title="com.amap.api.maps.model.particle中的类">ParticleShapeModule</a>&nbsp;particleShapeModule)</code>
<div class="block">发射模型，比如所有粒子从一个点出来，或者从某个区域出来</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../../../com/amap/api/maps/model/particle/ParticleOverlayOptions.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlayOptions</a></code></td>
<td class="colLast"><span class="typeNameLabel">ParticleOverlayOptions.</span><code><span class="memberNameLink"><a href="../../../../../../../com/amap/api/maps/model/particle/ParticleOverlayOptions.html#setParticleStartColor-com.amap.api.maps.model.particle.ColorGenerate-">setParticleStartColor</a></span>(<a href="../../../../../../../com/amap/api/maps/model/particle/ColorGenerate.html" title="com.amap.api.maps.model.particle中的类">ColorGenerate</a>&nbsp;startColor)</code>
<div class="block">每个粒子的初始颜色</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../../../com/amap/api/maps/model/particle/ParticleOverlayOptions.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlayOptions</a></code></td>
<td class="colLast"><span class="typeNameLabel">ParticleOverlayOptions.</span><code><span class="memberNameLink"><a href="../../../../../../../com/amap/api/maps/model/particle/ParticleOverlayOptions.html#setParticleStartSpeed-com.amap.api.maps.model.particle.VelocityGenerate-">setParticleStartSpeed</a></span>(<a href="../../../../../../../com/amap/api/maps/model/particle/VelocityGenerate.html" title="com.amap.api.maps.model.particle中的类">VelocityGenerate</a>&nbsp;startSpeed)</code>
<div class="block">粒子初始速度，每秒多少个像素</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../../../com/amap/api/maps/model/particle/ParticleOverlayOptions.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlayOptions</a></code></td>
<td class="colLast"><span class="typeNameLabel">ParticleOverlayOptions.</span><code><span class="memberNameLink"><a href="../../../../../../../com/amap/api/maps/model/particle/ParticleOverlayOptions.html#setStartParticleSize-int-int-">setStartParticleSize</a></span>(int&nbsp;startParticleW,
                    int&nbsp;startParticleH)</code>
<div class="block">粒子显示大小-宽高</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../../../com/amap/api/maps/model/particle/ParticleOverlayOptions.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlayOptions</a></code></td>
<td class="colLast"><span class="typeNameLabel">ParticleOverlayOptions.</span><code><span class="memberNameLink"><a href="../../../../../../../com/amap/api/maps/model/particle/ParticleOverlayOptions.html#setVisible-boolean-">setVisible</a></span>(boolean&nbsp;visible)</code>
<div class="block">设置 覆盖物的可见属性。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../../../com/amap/api/maps/model/particle/ParticleOverlayOptions.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlayOptions</a></code></td>
<td class="colLast"><span class="typeNameLabel">ParticleOverlayOptions.</span><code><span class="memberNameLink"><a href="../../../../../../../com/amap/api/maps/model/particle/ParticleOverlayOptions.html#zIndex-float-">zIndex</a></span>(float&nbsp;zIndex)</code>
<div class="block">获取覆盖物Z轴的值。</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表方法和解释">
<caption><span>返回变量类型为<a href="../../../../../../../com/amap/api/maps/model/particle/ParticleOverlayOptions.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlayOptions</a>的类型的<a href="../../../../../../../com/amap/api/maps/model/particle/package-summary.html">com.amap.api.maps.model.particle</a>中的方法</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>static java.util.List&lt;<a href="../../../../../../../com/amap/api/maps/model/particle/ParticleOverlayOptions.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlayOptions</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">ParticleOverlayOptionsFactory.</span><code><span class="memberNameLink"><a href="../../../../../../../com/amap/api/maps/model/particle/ParticleOverlayOptionsFactory.html#defaultOptions-int-">defaultOptions</a></span>(int&nbsp;type)</code>
<div class="block">默认粒子效果</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../../../overview-summary.html">概览</a></li>
<li><a href="../package-summary.html">程序包</a></li>
<li><a href="../../../../../../../com/amap/api/maps/model/particle/ParticleOverlayOptions.html" title="com.amap.api.maps.model.particle中的类">类</a></li>
<li class="navBarCell1Rev">使用</li>
<li><a href="../../../../../../../overview-tree.html">树</a></li>
<li><a href="../../../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个</li>
<li>下一个</li>
</ul>
<ul class="navList">
<li><a href="../../../../../../../index.html?com/amap/api/maps/model/particle/class-use/ParticleOverlayOptions.html" target="_top">框架</a></li>
<li><a href="ParticleOverlayOptions.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
