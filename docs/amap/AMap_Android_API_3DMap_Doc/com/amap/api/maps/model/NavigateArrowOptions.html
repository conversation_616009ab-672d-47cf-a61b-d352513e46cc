<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>NavigateArrowOptions</title>
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="NavigateArrowOptions";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10};
var tabs = {65535:["t0","所有方法"],2:["t2","实例方法"],8:["t4","具体方法"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/NavigateArrowOptions.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/maps/model/NavigateArrow.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/amap/api/maps/model/NaviPara.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/maps/model/NavigateArrowOptions.html" target="_top">框架</a></li>
<li><a href="NavigateArrowOptions.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li><a href="#field.summary">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li><a href="#field.detail">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.amap.api.maps.model</div>
<h2 title="类 NavigateArrowOptions" class="title">类 NavigateArrowOptions</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../../../com/amap/api/maps/model/BaseOptions.html" title="com.amap.api.maps.model中的类">com.amap.api.maps.model.BaseOptions</a></li>
<li>
<ul class="inheritance">
<li>com.amap.api.maps.model.NavigateArrowOptions</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>所有已实现的接口:</dt>
<dd>android.os.Parcelable, java.lang.Cloneable</dd>
</dl>
<hr>
<br>
<pre>public final class <span class="typeNameLabel">NavigateArrowOptions</span>
extends <a href="../../../../../com/amap/api/maps/model/BaseOptions.html" title="com.amap.api.maps.model中的类">BaseOptions</a>
implements android.os.Parcelable, java.lang.Cloneable</pre>
<div class="block">导航箭头(NavigateArrow)覆盖物的选项类。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>嵌套类概要</h3>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.android.os.Parcelable">
<!--   -->
</a>
<h3>从接口继承的嵌套类/接口&nbsp;android.os.Parcelable</h3>
<code>android.os.Parcelable.ClassLoaderCreator&lt;T&gt;, android.os.Parcelable.Creator&lt;T&gt;</code></li>
</ul>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>字段概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="字段概要表, 列表字段和解释">
<caption><span>字段</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">字段和说明</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../../com/amap/api/maps/model/NavigateArrowOptionsCreator.html" title="com.amap.api.maps.model中的类">NavigateArrowOptionsCreator</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/model/NavigateArrowOptions.html#CREATOR">CREATOR</a></span></code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.android.os.Parcelable">
<!--   -->
</a>
<h3>从接口继承的字段&nbsp;android.os.Parcelable</h3>
<code>CONTENTS_FILE_DESCRIPTOR, PARCELABLE_WRITE_RETURN_VALUE</code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/model/NavigateArrowOptions.html#NavigateArrowOptions--">NavigateArrowOptions</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>所有方法</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">实例方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">具体方法</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/maps/model/NavigateArrowOptions.html" title="com.amap.api.maps.model中的类">NavigateArrowOptions</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/model/NavigateArrowOptions.html#add-com.amap.api.maps.model.LatLng...-">add</a></span>(<a href="../../../../../com/amap/api/maps/model/LatLng.html" title="com.amap.api.maps.model中的类">LatLng</a>...&nbsp;points)</code>
<div class="block">追加一批顶点坐标到箭头终点。</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/maps/model/NavigateArrowOptions.html" title="com.amap.api.maps.model中的类">NavigateArrowOptions</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/model/NavigateArrowOptions.html#add-com.amap.api.maps.model.LatLng-">add</a></span>(<a href="../../../../../com/amap/api/maps/model/LatLng.html" title="com.amap.api.maps.model中的类">LatLng</a>&nbsp;point)</code>
<div class="block">追加一个顶点坐标到箭头的终点。</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/maps/model/NavigateArrowOptions.html" title="com.amap.api.maps.model中的类">NavigateArrowOptions</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/model/NavigateArrowOptions.html#addAll-java.lang.Iterable-">addAll</a></span>(java.lang.Iterable&lt;<a href="../../../../../com/amap/api/maps/model/LatLng.html" title="com.amap.api.maps.model中的类">LatLng</a>&gt;&nbsp;points)</code>
<div class="block">追加一批顶点坐标到箭头终点。</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../../com/amap/api/maps/model/LatLng.html" title="com.amap.api.maps.model中的类">LatLng</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/model/NavigateArrowOptions.html#getPoints--">getPoints</a></span>()</code>
<div class="block">获取箭头(NavigateArrow)覆盖物顶点坐标集合。</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/model/NavigateArrowOptions.html#getSideColor--">getSideColor</a></span>()</code>
<div class="block">获取箭头(NavigateArrow)覆盖物的的侧边颜色，ARGB格式。</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/model/NavigateArrowOptions.html#getTopColor--">getTopColor</a></span>()</code>
<div class="block">获取箭头(NavigateArrow)覆盖物的顶颜色，ARGB格式。</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/model/NavigateArrowOptions.html#getWidth--">getWidth</a></span>()</code>
<div class="block">获取箭头(NavigateArrow)覆盖物的宽度。</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/model/NavigateArrowOptions.html#getZIndex--">getZIndex</a></span>()</code>
<div class="block">获取箭头(NavigateArrow)覆盖物的Z轴值</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/model/NavigateArrowOptions.html#is3DModel--">is3DModel</a></span>()</code>
<div class="block">获取箭头(NavigateArrow)覆盖物是否是3D模式</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/model/NavigateArrowOptions.html#isVisible--">isVisible</a></span>()</code>
<div class="block">获取箭头(NavigateArrow)覆盖物是否可见</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/maps/model/NavigateArrowOptions.html" title="com.amap.api.maps.model中的类">NavigateArrowOptions</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/model/NavigateArrowOptions.html#set3DModel-boolean-">set3DModel</a></span>(boolean&nbsp;enable)</code>
<div class="block">设置箭头(NavigateArrow)覆盖物的是否为3D模式。</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/maps/model/NavigateArrowOptions.html" title="com.amap.api.maps.model中的类">NavigateArrowOptions</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/model/NavigateArrowOptions.html#sideColor-int-">sideColor</a></span>(int&nbsp;color)</code>
<div class="block">设置箭头箭头(NavigateArrow)覆盖物的侧边颜色，需要传入32位的ARGB格式。</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/maps/model/NavigateArrowOptions.html" title="com.amap.api.maps.model中的类">NavigateArrowOptions</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/model/NavigateArrowOptions.html#topColor-int-">topColor</a></span>(int&nbsp;color)</code>
<div class="block">设置箭头(NavigateArrow)覆盖物的顶颜色，需要传入32位的ARGB格式。</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/maps/model/NavigateArrowOptions.html" title="com.amap.api.maps.model中的类">NavigateArrowOptions</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/model/NavigateArrowOptions.html#visible-boolean-">visible</a></span>(boolean&nbsp;isVisible)</code>
<div class="block">设置箭头(NavigateArrow)覆盖物的可见性。</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/maps/model/NavigateArrowOptions.html" title="com.amap.api.maps.model中的类">NavigateArrowOptions</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/model/NavigateArrowOptions.html#width-float-">width</a></span>(float&nbsp;width)</code>
<div class="block">设置箭头(NavigateArrow)覆盖物的宽度，单位像素。</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/maps/model/NavigateArrowOptions.html" title="com.amap.api.maps.model中的类">NavigateArrowOptions</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/model/NavigateArrowOptions.html#zIndex-float-">zIndex</a></span>(float&nbsp;zIndex)</code>
<div class="block">设置箭头箭头(NavigateArrow)覆盖物的Z轴值。</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.android.os.Parcelable">
<!--   -->
</a>
<h3>从接口继承的方法&nbsp;android.os.Parcelable</h3>
<code>describeContents, writeToParcel</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>字段详细资料</h3>
<a name="CREATOR">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>CREATOR</h4>
<pre>public static final&nbsp;<a href="../../../../../com/amap/api/maps/model/NavigateArrowOptionsCreator.html" title="com.amap.api.maps.model中的类">NavigateArrowOptionsCreator</a> CREATOR</pre>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="NavigateArrowOptions--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>NavigateArrowOptions</h4>
<pre>public&nbsp;NavigateArrowOptions()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="add-com.amap.api.maps.model.LatLng-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>add</h4>
<pre>public&nbsp;<a href="../../../../../com/amap/api/maps/model/NavigateArrowOptions.html" title="com.amap.api.maps.model中的类">NavigateArrowOptions</a>&nbsp;add(<a href="../../../../../com/amap/api/maps/model/LatLng.html" title="com.amap.api.maps.model中的类">LatLng</a>&nbsp;point)</pre>
<div class="block">追加一个顶点坐标到箭头的终点。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>point</code> - 要添加的顶点。</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>追加一个顶点到箭头终点的 NavigateArrowOptions 对象。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.3.0</dd>
</dl>
</li>
</ul>
<a name="add-com.amap.api.maps.model.LatLng...-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>add</h4>
<pre>public&nbsp;<a href="../../../../../com/amap/api/maps/model/NavigateArrowOptions.html" title="com.amap.api.maps.model中的类">NavigateArrowOptions</a>&nbsp;add(<a href="../../../../../com/amap/api/maps/model/LatLng.html" title="com.amap.api.maps.model中的类">LatLng</a>...&nbsp;points)</pre>
<div class="block">追加一批顶点坐标到箭头终点。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>points</code> - 要添加的顶点集合。</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>追加一批顶点到箭头终点的 NavigateArrowOptions 对象。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.3.0</dd>
</dl>
</li>
</ul>
<a name="addAll-java.lang.Iterable-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addAll</h4>
<pre>public&nbsp;<a href="../../../../../com/amap/api/maps/model/NavigateArrowOptions.html" title="com.amap.api.maps.model中的类">NavigateArrowOptions</a>&nbsp;addAll(java.lang.Iterable&lt;<a href="../../../../../com/amap/api/maps/model/LatLng.html" title="com.amap.api.maps.model中的类">LatLng</a>&gt;&nbsp;points)</pre>
<div class="block">追加一批顶点坐标到箭头终点。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>points</code> - 要添加的顶点集合。</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>追加一批顶点到箭头终点的 NavigateArrowOptions 对象。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.3.0</dd>
</dl>
</li>
</ul>
<a name="width-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>width</h4>
<pre>public&nbsp;<a href="../../../../../com/amap/api/maps/model/NavigateArrowOptions.html" title="com.amap.api.maps.model中的类">NavigateArrowOptions</a>&nbsp;width(float&nbsp;width)</pre>
<div class="block">设置箭头(NavigateArrow)覆盖物的宽度，单位像素。默认为10。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>width</code> - 宽度。</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>设置箭头(NavigateArrow)覆盖物宽度的 NavigateArrowOptions 对象。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.3.0</dd>
</dl>
</li>
</ul>
<a name="topColor-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>topColor</h4>
<pre>public&nbsp;<a href="../../../../../com/amap/api/maps/model/NavigateArrowOptions.html" title="com.amap.api.maps.model中的类">NavigateArrowOptions</a>&nbsp;topColor(int&nbsp;color)</pre>
<div class="block">设置箭头(NavigateArrow)覆盖物的顶颜色，需要传入32位的ARGB格式。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>color</code> - 顶颜色。</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>设置箭头(NavigateArrow)覆盖物顶颜色的 NavigateArrowOptions 对象。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.3.0</dd>
</dl>
</li>
</ul>
<a name="sideColor-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>sideColor</h4>
<pre>public&nbsp;<a href="../../../../../com/amap/api/maps/model/NavigateArrowOptions.html" title="com.amap.api.maps.model中的类">NavigateArrowOptions</a>&nbsp;sideColor(int&nbsp;color)</pre>
<div class="block">设置箭头箭头(NavigateArrow)覆盖物的侧边颜色，需要传入32位的ARGB格式。
 注意：v3.0中不再提供该方法。
 注意：6.7.0中再次启用此方法。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>color</code> - 侧边颜色。</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>设置箭头侧边颜色的 NavigateArrowOptions 对象。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.3.0</dd>
</dl>
</li>
</ul>
<a name="zIndex-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>zIndex</h4>
<pre>public&nbsp;<a href="../../../../../com/amap/api/maps/model/NavigateArrowOptions.html" title="com.amap.api.maps.model中的类">NavigateArrowOptions</a>&nbsp;zIndex(float&nbsp;zIndex)</pre>
<div class="block">设置箭头箭头(NavigateArrow)覆盖物的Z轴值。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>zIndex</code> - 要设置的Z轴的值。</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>设置箭头新Z轴值的NavigateArrowOptions对象。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.3.0</dd>
</dl>
</li>
</ul>
<a name="visible-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>visible</h4>
<pre>public&nbsp;<a href="../../../../../com/amap/api/maps/model/NavigateArrowOptions.html" title="com.amap.api.maps.model中的类">NavigateArrowOptions</a>&nbsp;visible(boolean&nbsp;isVisible)</pre>
<div class="block">设置箭头(NavigateArrow)覆盖物的可见性。默认为可见。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>isVisible</code> - 一个表示箭头是否可见的布尔值，true表示可见，false表示不可见。</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>设置新可见属性的NavigateArrowOptions对象。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="set3DModel-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set3DModel</h4>
<pre>public&nbsp;<a href="../../../../../com/amap/api/maps/model/NavigateArrowOptions.html" title="com.amap.api.maps.model中的类">NavigateArrowOptions</a>&nbsp;set3DModel(boolean&nbsp;enable)</pre>
<div class="block">设置箭头(NavigateArrow)覆盖物的是否为3D模式。默认为false。
 设置为3D之后，箭头会悬空并增加厚度，此时<a href="../../../../../com/amap/api/maps/model/NavigateArrowOptions.html#sideColor-int-"><code>NavigateArrowOptions.sideColor(int)</code></a>的值也会生效</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>enable</code> - 一个表示箭头是为3D模式的布尔值，true表示3D，false表示不是3D即2D。</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>设置新3D模式属性的NavigateArrowOptions对象。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.7.0</dd>
</dl>
</li>
</ul>
<a name="getPoints--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPoints</h4>
<pre>public&nbsp;java.util.List&lt;<a href="../../../../../com/amap/api/maps/model/LatLng.html" title="com.amap.api.maps.model中的类">LatLng</a>&gt;&nbsp;getPoints()</pre>
<div class="block">获取箭头(NavigateArrow)覆盖物顶点坐标集合。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>线段的顶点坐标列表。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="getWidth--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWidth</h4>
<pre>public&nbsp;float&nbsp;getWidth()</pre>
<div class="block">获取箭头(NavigateArrow)覆盖物的宽度。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>宽度</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="getTopColor--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTopColor</h4>
<pre>public&nbsp;int&nbsp;getTopColor()</pre>
<div class="block">获取箭头(NavigateArrow)覆盖物的顶颜色，ARGB格式。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>顶颜色，ARGB格式。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="getSideColor--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSideColor</h4>
<pre>public&nbsp;int&nbsp;getSideColor()</pre>
<div class="block">获取箭头(NavigateArrow)覆盖物的的侧边颜色，ARGB格式。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>侧边颜色，ARGB格式。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="getZIndex--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getZIndex</h4>
<pre>public&nbsp;float&nbsp;getZIndex()</pre>
<div class="block">获取箭头(NavigateArrow)覆盖物的Z轴值</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>Z轴值</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="isVisible--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isVisible</h4>
<pre>public&nbsp;boolean&nbsp;isVisible()</pre>
<div class="block">获取箭头(NavigateArrow)覆盖物是否可见</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>true：可见; false：不可见。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="is3DModel--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>is3DModel</h4>
<pre>public&nbsp;boolean&nbsp;is3DModel()</pre>
<div class="block">获取箭头(NavigateArrow)覆盖物是否是3D模式</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>true：3D模式; false：非3D模式。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.7.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/NavigateArrowOptions.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/maps/model/NavigateArrow.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/amap/api/maps/model/NaviPara.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/maps/model/NavigateArrowOptions.html" target="_top">框架</a></li>
<li><a href="NavigateArrowOptions.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li><a href="#field.summary">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li><a href="#field.detail">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
