<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>ParticleOverlayOptions</title>
<link rel="stylesheet" type="text/css" href="../../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ParticleOverlayOptions";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10};
var tabs = {65535:["t0","所有方法"],2:["t2","实例方法"],8:["t4","具体方法"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/ParticleOverlayOptions.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../../com/amap/api/maps/model/particle/ParticleOverlay.html" title="com.amap.api.maps.model.particle中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../../com/amap/api/maps/model/particle/ParticleOverlayOptionsFactory.html" title="com.amap.api.maps.model.particle中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?com/amap/api/maps/model/particle/ParticleOverlayOptions.html" target="_top">框架</a></li>
<li><a href="ParticleOverlayOptions.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.amap.api.maps.model.particle</div>
<h2 title="类 ParticleOverlayOptions" class="title">类 ParticleOverlayOptions</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../../../../com/amap/api/maps/model/BaseOptions.html" title="com.amap.api.maps.model中的类">com.amap.api.maps.model.BaseOptions</a></li>
<li>
<ul class="inheritance">
<li>com.amap.api.maps.model.particle.ParticleOverlayOptions</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>所有已实现的接口:</dt>
<dd>android.os.Parcelable, java.lang.Cloneable</dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">ParticleOverlayOptions</span>
extends <a href="../../../../../../com/amap/api/maps/model/BaseOptions.html" title="com.amap.api.maps.model中的类">BaseOptions</a>
implements android.os.Parcelable, java.lang.Cloneable</pre>
<div class="block">粒子效果初始化属性控制<br>
 建议在<a href="../../../../../../com/amap/api/maps/AMap.OnMapLoadedListener.html" title="com.amap.api.maps中的接口"><code>AMap.OnMapLoadedListener</code></a> 之后调用</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.5.0</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>嵌套类概要</h3>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.android.os.Parcelable">
<!--   -->
</a>
<h3>从接口继承的嵌套类/接口&nbsp;android.os.Parcelable</h3>
<code>android.os.Parcelable.ClassLoaderCreator&lt;T&gt;, android.os.Parcelable.Creator&lt;T&gt;</code></li>
</ul>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>字段概要</h3>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.android.os.Parcelable">
<!--   -->
</a>
<h3>从接口继承的字段&nbsp;android.os.Parcelable</h3>
<code>CONTENTS_FILE_DESCRIPTOR, PARCELABLE_WRITE_RETURN_VALUE</code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/maps/model/particle/ParticleOverlayOptions.html#ParticleOverlayOptions--">ParticleOverlayOptions</a></span>()</code>
<div class="block">粒子效果初始化属性控制</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>所有方法</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">实例方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">具体方法</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/maps/model/particle/ParticleOverlayOptions.html#getDuration--">getDuration</a></span>()</code>
<div class="block">整个粒子效果的存活时间,单位毫秒</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/maps/model/BitmapDescriptor.html" title="com.amap.api.maps.model中的类">BitmapDescriptor</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/maps/model/particle/ParticleOverlayOptions.html#getIcon--">getIcon</a></span>()</code>
<div class="block">获取粒子效果的图标</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/maps/model/particle/ParticleOverlayOptions.html#getMaxParticles--">getMaxParticles</a></span>()</code>
<div class="block">整个粒子效果的粒子最大数量</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/maps/model/particle/ParticleEmissionModule.html" title="com.amap.api.maps.model.particle中的类">ParticleEmissionModule</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/maps/model/particle/ParticleOverlayOptions.html#getParticleEmissionModule--">getParticleEmissionModule</a></span>()</code>
<div class="block">发射率，每隔多少时间发射粒子数量，越多会越密集</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/maps/model/particle/ParticleOverlayOptions.html#getParticleLifeTime--">getParticleLifeTime</a></span>()</code>
<div class="block">每个粒子的存活时间,单位毫秒</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/maps/model/particle/ParticleOverLifeModule.html" title="com.amap.api.maps.model.particle中的类">ParticleOverLifeModule</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/maps/model/particle/ParticleOverlayOptions.html#getParticleOverLifeModule--">getParticleOverLifeModule</a></span>()</code>
<div class="block">每个粒子生命周期过程中状态变化，包含速度、旋转和颜色的变化</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/maps/model/particle/ParticleShapeModule.html" title="com.amap.api.maps.model.particle中的类">ParticleShapeModule</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/maps/model/particle/ParticleOverlayOptions.html#getParticleShapeModule--">getParticleShapeModule</a></span>()</code>
<div class="block">发射模型，比如所有粒子从一个点出来，或者从某个区域出来</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/maps/model/particle/ColorGenerate.html" title="com.amap.api.maps.model.particle中的类">ColorGenerate</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/maps/model/particle/ParticleOverlayOptions.html#getParticleStartColor--">getParticleStartColor</a></span>()</code>
<div class="block">每个粒子的初始颜色</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/maps/model/particle/VelocityGenerate.html" title="com.amap.api.maps.model.particle中的类">VelocityGenerate</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/maps/model/particle/ParticleOverlayOptions.html#getParticleStartSpeed--">getParticleStartSpeed</a></span>()</code>
<div class="block">获取初始速度</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/maps/model/particle/ParticleOverlayOptions.html#getstartParticleH--">getstartParticleH</a></span>()</code>
<div class="block">粒子显示大小-高度</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/maps/model/particle/ParticleOverlayOptions.html#getStartParticleW--">getStartParticleW</a></span>()</code>
<div class="block">粒子显示大小-宽度</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/maps/model/particle/ParticleOverlayOptions.html#getZIndex--">getZIndex</a></span>()</code>
<div class="block">获取覆盖物的Z轴值</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/maps/model/particle/ParticleOverlayOptions.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlayOptions</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/maps/model/particle/ParticleOverlayOptions.html#icon-com.amap.api.maps.model.BitmapDescriptor-">icon</a></span>(<a href="../../../../../../com/amap/api/maps/model/BitmapDescriptor.html" title="com.amap.api.maps.model中的类">BitmapDescriptor</a>&nbsp;icon)</code>
<div class="block">设置覆盖物的图标。</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/maps/model/particle/ParticleOverlayOptions.html#isLoop--">isLoop</a></span>()</code>
<div class="block">整个粒子效果是否循环</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/maps/model/particle/ParticleOverlayOptions.html#isVisibile--">isVisibile</a></span>()</code>
<div class="block">覆盖物的可见属性。</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/maps/model/particle/ParticleOverlayOptions.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlayOptions</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/maps/model/particle/ParticleOverlayOptions.html#setDuration-long-">setDuration</a></span>(long&nbsp;duration)</code>
<div class="block">整个粒子效果的存活时间,单位毫秒</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/maps/model/particle/ParticleOverlayOptions.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlayOptions</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/maps/model/particle/ParticleOverlayOptions.html#setLoop-boolean-">setLoop</a></span>(boolean&nbsp;loop)</code>
<div class="block">整个粒子效果是否循环</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/maps/model/particle/ParticleOverlayOptions.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlayOptions</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/maps/model/particle/ParticleOverlayOptions.html#setMaxParticles-int-">setMaxParticles</a></span>(int&nbsp;maxParticles)</code>
<div class="block">整个粒子效果的粒子最大数量</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/maps/model/particle/ParticleOverlayOptions.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlayOptions</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/maps/model/particle/ParticleOverlayOptions.html#setParticleEmissionModule-com.amap.api.maps.model.particle.ParticleEmissionModule-">setParticleEmissionModule</a></span>(<a href="../../../../../../com/amap/api/maps/model/particle/ParticleEmissionModule.html" title="com.amap.api.maps.model.particle中的类">ParticleEmissionModule</a>&nbsp;particleEmissionModule)</code>
<div class="block">发射率，每个多少时间发射粒子数量，越多会越密集</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/maps/model/particle/ParticleOverlayOptions.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlayOptions</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/maps/model/particle/ParticleOverlayOptions.html#setParticleLifeTime-long-">setParticleLifeTime</a></span>(long&nbsp;particleLifeTime)</code>
<div class="block">每个粒子的存活时间,单位毫秒</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/maps/model/particle/ParticleOverlayOptions.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlayOptions</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/maps/model/particle/ParticleOverlayOptions.html#setParticleOverLifeModule-com.amap.api.maps.model.particle.ParticleOverLifeModule-">setParticleOverLifeModule</a></span>(<a href="../../../../../../com/amap/api/maps/model/particle/ParticleOverLifeModule.html" title="com.amap.api.maps.model.particle中的类">ParticleOverLifeModule</a>&nbsp;particleOverLifeModule)</code>
<div class="block">每个粒子生命周期过程中状态变化，包含速度、旋转和颜色的变化</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/maps/model/particle/ParticleOverlayOptions.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlayOptions</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/maps/model/particle/ParticleOverlayOptions.html#setParticleShapeModule-com.amap.api.maps.model.particle.ParticleShapeModule-">setParticleShapeModule</a></span>(<a href="../../../../../../com/amap/api/maps/model/particle/ParticleShapeModule.html" title="com.amap.api.maps.model.particle中的类">ParticleShapeModule</a>&nbsp;particleShapeModule)</code>
<div class="block">发射模型，比如所有粒子从一个点出来，或者从某个区域出来</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/maps/model/particle/ParticleOverlayOptions.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlayOptions</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/maps/model/particle/ParticleOverlayOptions.html#setParticleStartColor-com.amap.api.maps.model.particle.ColorGenerate-">setParticleStartColor</a></span>(<a href="../../../../../../com/amap/api/maps/model/particle/ColorGenerate.html" title="com.amap.api.maps.model.particle中的类">ColorGenerate</a>&nbsp;startColor)</code>
<div class="block">每个粒子的初始颜色</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/maps/model/particle/ParticleOverlayOptions.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlayOptions</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/maps/model/particle/ParticleOverlayOptions.html#setParticleStartSpeed-com.amap.api.maps.model.particle.VelocityGenerate-">setParticleStartSpeed</a></span>(<a href="../../../../../../com/amap/api/maps/model/particle/VelocityGenerate.html" title="com.amap.api.maps.model.particle中的类">VelocityGenerate</a>&nbsp;startSpeed)</code>
<div class="block">粒子初始速度，每秒多少个像素</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/maps/model/particle/ParticleOverlayOptions.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlayOptions</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/maps/model/particle/ParticleOverlayOptions.html#setStartParticleSize-int-int-">setStartParticleSize</a></span>(int&nbsp;startParticleW,
                    int&nbsp;startParticleH)</code>
<div class="block">粒子显示大小-宽高</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/maps/model/particle/ParticleOverlayOptions.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlayOptions</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/maps/model/particle/ParticleOverlayOptions.html#setVisible-boolean-">setVisible</a></span>(boolean&nbsp;visible)</code>
<div class="block">设置 覆盖物的可见属性。</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code><a href="../../../../../../com/amap/api/maps/model/particle/ParticleOverlayOptions.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlayOptions</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/amap/api/maps/model/particle/ParticleOverlayOptions.html#zIndex-float-">zIndex</a></span>(float&nbsp;zIndex)</code>
<div class="block">获取覆盖物Z轴的值。</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.android.os.Parcelable">
<!--   -->
</a>
<h3>从接口继承的方法&nbsp;android.os.Parcelable</h3>
<code>describeContents, writeToParcel</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="ParticleOverlayOptions--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>ParticleOverlayOptions</h4>
<pre>public&nbsp;ParticleOverlayOptions()</pre>
<div class="block">粒子效果初始化属性控制</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.5.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="icon-com.amap.api.maps.model.BitmapDescriptor-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>icon</h4>
<pre>public&nbsp;<a href="../../../../../../com/amap/api/maps/model/particle/ParticleOverlayOptions.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlayOptions</a>&nbsp;icon(<a href="../../../../../../com/amap/api/maps/model/BitmapDescriptor.html" title="com.amap.api.maps.model中的类">BitmapDescriptor</a>&nbsp;icon)</pre>
<div class="block">设置覆盖物的图标。相同图案的 icon 的 覆盖物 最好使用同一个 BitmapDescriptor 对象以节省内存空间。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>icon</code> - 图标的BitmapDescriptor对象</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>ParticleSystemOptions对象。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.5.0</dd>
</dl>
</li>
</ul>
<a name="getIcon--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getIcon</h4>
<pre>public&nbsp;<a href="../../../../../../com/amap/api/maps/model/BitmapDescriptor.html" title="com.amap.api.maps.model中的类">BitmapDescriptor</a>&nbsp;getIcon()</pre>
<div class="block">获取粒子效果的图标</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
</dl>
</li>
</ul>
<a name="getMaxParticles--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMaxParticles</h4>
<pre>public&nbsp;int&nbsp;getMaxParticles()</pre>
<div class="block">整个粒子效果的粒子最大数量</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.5.0</dd>
</dl>
</li>
</ul>
<a name="setMaxParticles-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMaxParticles</h4>
<pre>public&nbsp;<a href="../../../../../../com/amap/api/maps/model/particle/ParticleOverlayOptions.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlayOptions</a>&nbsp;setMaxParticles(int&nbsp;maxParticles)</pre>
<div class="block">整个粒子效果的粒子最大数量</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.5.0</dd>
</dl>
</li>
</ul>
<a name="isLoop--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isLoop</h4>
<pre>public&nbsp;boolean&nbsp;isLoop()</pre>
<div class="block">整个粒子效果是否循环</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.5.0</dd>
</dl>
</li>
</ul>
<a name="setLoop-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLoop</h4>
<pre>public&nbsp;<a href="../../../../../../com/amap/api/maps/model/particle/ParticleOverlayOptions.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlayOptions</a>&nbsp;setLoop(boolean&nbsp;loop)</pre>
<div class="block">整个粒子效果是否循环</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.5.0</dd>
</dl>
</li>
</ul>
<a name="getDuration--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDuration</h4>
<pre>public&nbsp;long&nbsp;getDuration()</pre>
<div class="block">整个粒子效果的存活时间,单位毫秒</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.5.0</dd>
</dl>
</li>
</ul>
<a name="setDuration-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDuration</h4>
<pre>public&nbsp;<a href="../../../../../../com/amap/api/maps/model/particle/ParticleOverlayOptions.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlayOptions</a>&nbsp;setDuration(long&nbsp;duration)</pre>
<div class="block">整个粒子效果的存活时间,单位毫秒</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.5.0</dd>
</dl>
</li>
</ul>
<a name="getParticleLifeTime--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getParticleLifeTime</h4>
<pre>public&nbsp;long&nbsp;getParticleLifeTime()</pre>
<div class="block">每个粒子的存活时间,单位毫秒</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.5.0</dd>
</dl>
</li>
</ul>
<a name="setParticleLifeTime-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setParticleLifeTime</h4>
<pre>public&nbsp;<a href="../../../../../../com/amap/api/maps/model/particle/ParticleOverlayOptions.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlayOptions</a>&nbsp;setParticleLifeTime(long&nbsp;particleLifeTime)</pre>
<div class="block">每个粒子的存活时间,单位毫秒</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.5.0</dd>
</dl>
</li>
</ul>
<a name="getParticleEmissionModule--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getParticleEmissionModule</h4>
<pre>public&nbsp;<a href="../../../../../../com/amap/api/maps/model/particle/ParticleEmissionModule.html" title="com.amap.api.maps.model.particle中的类">ParticleEmissionModule</a>&nbsp;getParticleEmissionModule()</pre>
<div class="block">发射率，每隔多少时间发射粒子数量，越多会越密集</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.5.0</dd>
</dl>
</li>
</ul>
<a name="setParticleEmissionModule-com.amap.api.maps.model.particle.ParticleEmissionModule-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setParticleEmissionModule</h4>
<pre>public&nbsp;<a href="../../../../../../com/amap/api/maps/model/particle/ParticleOverlayOptions.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlayOptions</a>&nbsp;setParticleEmissionModule(<a href="../../../../../../com/amap/api/maps/model/particle/ParticleEmissionModule.html" title="com.amap.api.maps.model.particle中的类">ParticleEmissionModule</a>&nbsp;particleEmissionModule)</pre>
<div class="block">发射率，每个多少时间发射粒子数量，越多会越密集</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.5.0</dd>
</dl>
</li>
</ul>
<a name="getParticleShapeModule--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getParticleShapeModule</h4>
<pre>public&nbsp;<a href="../../../../../../com/amap/api/maps/model/particle/ParticleShapeModule.html" title="com.amap.api.maps.model.particle中的类">ParticleShapeModule</a>&nbsp;getParticleShapeModule()</pre>
<div class="block">发射模型，比如所有粒子从一个点出来，或者从某个区域出来</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.5.0</dd>
</dl>
</li>
</ul>
<a name="setParticleShapeModule-com.amap.api.maps.model.particle.ParticleShapeModule-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setParticleShapeModule</h4>
<pre>public&nbsp;<a href="../../../../../../com/amap/api/maps/model/particle/ParticleOverlayOptions.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlayOptions</a>&nbsp;setParticleShapeModule(<a href="../../../../../../com/amap/api/maps/model/particle/ParticleShapeModule.html" title="com.amap.api.maps.model.particle中的类">ParticleShapeModule</a>&nbsp;particleShapeModule)</pre>
<div class="block">发射模型，比如所有粒子从一个点出来，或者从某个区域出来</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.5.0</dd>
</dl>
</li>
</ul>
<a name="getParticleStartSpeed--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getParticleStartSpeed</h4>
<pre>public&nbsp;<a href="../../../../../../com/amap/api/maps/model/particle/VelocityGenerate.html" title="com.amap.api.maps.model.particle中的类">VelocityGenerate</a>&nbsp;getParticleStartSpeed()</pre>
<div class="block">获取初始速度</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.5.0</dd>
</dl>
</li>
</ul>
<a name="setParticleStartSpeed-com.amap.api.maps.model.particle.VelocityGenerate-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setParticleStartSpeed</h4>
<pre>public&nbsp;<a href="../../../../../../com/amap/api/maps/model/particle/ParticleOverlayOptions.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlayOptions</a>&nbsp;setParticleStartSpeed(<a href="../../../../../../com/amap/api/maps/model/particle/VelocityGenerate.html" title="com.amap.api.maps.model.particle中的类">VelocityGenerate</a>&nbsp;startSpeed)</pre>
<div class="block">粒子初始速度，每秒多少个像素</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>startSpeed</code> - 单位 像素/秒</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.5.0</dd>
</dl>
</li>
</ul>
<a name="setParticleStartColor-com.amap.api.maps.model.particle.ColorGenerate-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setParticleStartColor</h4>
<pre>public&nbsp;<a href="../../../../../../com/amap/api/maps/model/particle/ParticleOverlayOptions.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlayOptions</a>&nbsp;setParticleStartColor(<a href="../../../../../../com/amap/api/maps/model/particle/ColorGenerate.html" title="com.amap.api.maps.model.particle中的类">ColorGenerate</a>&nbsp;startColor)</pre>
<div class="block">每个粒子的初始颜色</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.5.0</dd>
</dl>
</li>
</ul>
<a name="getParticleStartColor--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getParticleStartColor</h4>
<pre>public&nbsp;<a href="../../../../../../com/amap/api/maps/model/particle/ColorGenerate.html" title="com.amap.api.maps.model.particle中的类">ColorGenerate</a>&nbsp;getParticleStartColor()</pre>
<div class="block">每个粒子的初始颜色</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.5.0</dd>
</dl>
</li>
</ul>
<a name="setParticleOverLifeModule-com.amap.api.maps.model.particle.ParticleOverLifeModule-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setParticleOverLifeModule</h4>
<pre>public&nbsp;<a href="../../../../../../com/amap/api/maps/model/particle/ParticleOverlayOptions.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlayOptions</a>&nbsp;setParticleOverLifeModule(<a href="../../../../../../com/amap/api/maps/model/particle/ParticleOverLifeModule.html" title="com.amap.api.maps.model.particle中的类">ParticleOverLifeModule</a>&nbsp;particleOverLifeModule)</pre>
<div class="block">每个粒子生命周期过程中状态变化，包含速度、旋转和颜色的变化</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.5.0</dd>
</dl>
</li>
</ul>
<a name="getParticleOverLifeModule--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getParticleOverLifeModule</h4>
<pre>public&nbsp;<a href="../../../../../../com/amap/api/maps/model/particle/ParticleOverLifeModule.html" title="com.amap.api.maps.model.particle中的类">ParticleOverLifeModule</a>&nbsp;getParticleOverLifeModule()</pre>
<div class="block">每个粒子生命周期过程中状态变化，包含速度、旋转和颜色的变化</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.5.0</dd>
</dl>
</li>
</ul>
<a name="setStartParticleSize-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setStartParticleSize</h4>
<pre>public&nbsp;<a href="../../../../../../com/amap/api/maps/model/particle/ParticleOverlayOptions.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlayOptions</a>&nbsp;setStartParticleSize(int&nbsp;startParticleW,
                                                   int&nbsp;startParticleH)</pre>
<div class="block">粒子显示大小-宽高</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.5.0</dd>
</dl>
</li>
</ul>
<a name="getStartParticleW--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStartParticleW</h4>
<pre>public&nbsp;int&nbsp;getStartParticleW()</pre>
<div class="block">粒子显示大小-宽度</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.5.0</dd>
</dl>
</li>
</ul>
<a name="getstartParticleH--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getstartParticleH</h4>
<pre>public&nbsp;int&nbsp;getstartParticleH()</pre>
<div class="block">粒子显示大小-高度</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.5.0</dd>
</dl>
</li>
</ul>
<a name="zIndex-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>zIndex</h4>
<pre>public&nbsp;<a href="../../../../../../com/amap/api/maps/model/particle/ParticleOverlayOptions.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlayOptions</a>&nbsp;zIndex(float&nbsp;zIndex)</pre>
<div class="block">获取覆盖物Z轴的值。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>zIndex</code> - 要设置的Z轴的值。</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>ParticleSystemOptions对象。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.5.0</dd>
</dl>
</li>
</ul>
<a name="getZIndex--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getZIndex</h4>
<pre>public&nbsp;float&nbsp;getZIndex()</pre>
<div class="block">获取覆盖物的Z轴值</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>Z轴值</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.5.0</dd>
</dl>
</li>
</ul>
<a name="setVisible-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setVisible</h4>
<pre>public&nbsp;<a href="../../../../../../com/amap/api/maps/model/particle/ParticleOverlayOptions.html" title="com.amap.api.maps.model.particle中的类">ParticleOverlayOptions</a>&nbsp;setVisible(boolean&nbsp;visible)</pre>
<div class="block">设置 覆盖物的可见属性。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>visible</code> - 一个布尔值，表示覆盖物是否可见，true表示可见，false表示不可见。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.5.0</dd>
</dl>
</li>
</ul>
<a name="isVisibile--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>isVisibile</h4>
<pre>public&nbsp;boolean&nbsp;isVisibile()</pre>
<div class="block">覆盖物的可见属性。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.5.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/ParticleOverlayOptions.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../../com/amap/api/maps/model/particle/ParticleOverlay.html" title="com.amap.api.maps.model.particle中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../../com/amap/api/maps/model/particle/ParticleOverlayOptionsFactory.html" title="com.amap.api.maps.model.particle中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?com/amap/api/maps/model/particle/ParticleOverlayOptions.html" target="_top">框架</a></li>
<li><a href="ParticleOverlayOptions.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
