<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>程序包 com.amap.api.maps.model的使用</title>
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="\u7A0B\u5E8F\u5305 com.amap.api.maps.model\u7684\u4F7F\u7528";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li>类</li>
<li class="navBarCell1Rev">使用</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个</li>
<li>下一个</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/maps/model/package-use.html" target="_top">框架</a></li>
<li><a href="package-use.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 title="程序包的使用 com.amap.api.maps.model" class="title">程序包的使用<br>com.amap.api.maps.model</h1>
</div>
<div class="contentContainer">
<ul class="blockList">
<li class="blockList">
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表程序包和解释">
<caption><span>使用<a href="../../../../../com/amap/api/maps/model/package-summary.html">com.amap.api.maps.model</a>的程序包</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">程序包</th>
<th class="colLast" scope="col">说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="#com.amap.api.maps">com.amap.api.maps</a></td>
<td class="colLast">
<div class="block">
地图显示包，帮助您在Android应用程序中显示高德地图，并且支持地图交互手势的功能。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.amap.api.maps.model">com.amap.api.maps.model</a></td>
<td class="colLast">
<div class="block">
覆盖物包，覆盖物（叠加或覆盖到地图的内容）支持标记、折线、多边形和圆。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#com.amap.api.maps.model.animation">com.amap.api.maps.model.animation</a></td>
<td class="colLast">
<div class="block">
  动画类，可用于支持动画的覆盖物。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.amap.api.maps.model.particle">com.amap.api.maps.model.particle</a></td>
<td class="colLast">
<div class="block">
  粒子效果类，用于添加粒子效果覆盖物。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#com.amap.api.maps.utils">com.amap.api.maps.utils</a></td>
<td class="colLast">
<div class="block">
工具类，基于地图现有接口的辅助工具。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.amap.api.maps.utils.overlay">com.amap.api.maps.utils.overlay</a></td>
<td class="colLast">
<div class="block">
工具类，基于地图现有接口实现的高级功能。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#com.amap.api.trace">com.amap.api.trace</a></td>
<td class="colLast">
<div class="block">
轨迹纠偏包，提供高精度定位轨迹抓路后绘制平滑轨迹。</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.amap.api.maps">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表类和解释">
<caption><span><a href="../../../../../com/amap/api/maps/package-summary.html">com.amap.api.maps</a>使用的<a href="../../../../../com/amap/api/maps/model/package-summary.html">com.amap.api.maps.model</a>中的类</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">类和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/maps/model/class-use/AMapGestureListener.html#com.amap.api.maps">AMapGestureListener</a>
<div class="block">地图手势识别的回调，包含单双击、滑动等以及地图操作地图后稳定下来的回调</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/maps/model/class-use/Arc.html#com.amap.api.maps">Arc</a>
<div class="block">定义了在地图上绘制弧形的类。</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/maps/model/class-use/ArcOptions.html#com.amap.api.maps">ArcOptions</a>
<div class="block">圆形选项类</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/maps/model/class-use/BuildingOverlay.html#com.amap.api.maps">BuildingOverlay</a>
<div class="block">建筑物区域Overlay,可以设置指定区域内建筑物颜色、高度及高度比例等信息，默认区域为全世界。</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/maps/model/class-use/CameraPosition.html#com.amap.api.maps">CameraPosition</a>
<div class="block">相机位置，这个类包含了所有的可视区域的位置参数。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/maps/model/class-use/Circle.html#com.amap.api.maps">Circle</a>
<div class="block">定义了在地图上绘制圆的类。</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/maps/model/class-use/CircleOptions.html#com.amap.api.maps">CircleOptions</a>
<div class="block">创建圆的选项类</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/maps/model/class-use/CustomMapStyleOptions.html#com.amap.api.maps">CustomMapStyleOptions</a>
<div class="block">自定义样式属性集</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/maps/model/class-use/GLTFOverlay.html#com.amap.api.maps">GLTFOverlay</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/maps/model/class-use/GLTFOverlayOptions.html#com.amap.api.maps">GLTFOverlayOptions</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/maps/model/class-use/GroundOverlay.html#com.amap.api.maps">GroundOverlay</a>
<div class="block">定义在地图上绘制一个 Ground 覆盖物（一张图片以合适的大小贴在地图上的图片层）
 
 位置： 可以通过设置中心点或者图片区域来确定图片层的位置。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/maps/model/class-use/GroundOverlayOptions.html#com.amap.api.maps">GroundOverlayOptions</a>
<div class="block">ground 覆盖物的选项类。</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/maps/model/class-use/HeatMapGridLayer.html#com.amap.api.maps">HeatMapGridLayer</a>
<div class="block">热力图图层</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/maps/model/class-use/HeatMapGridLayerOptions.html#com.amap.api.maps">HeatMapGridLayerOptions</a>
<div class="block">实现热力图</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/maps/model/class-use/HeatMapLayer.html#com.amap.api.maps">HeatMapLayer</a>
<div class="block">热力图图层</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/maps/model/class-use/HeatMapLayerOptions.html#com.amap.api.maps">HeatMapLayerOptions</a>
<div class="block">实现高德热力图</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/maps/model/class-use/IndoorBuildingInfo.html#com.amap.api.maps">IndoorBuildingInfo</a>
<div class="block">室内地图属性类，包含室内地图的POIID、楼层总数和当前显示楼层等。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/maps/model/class-use/LatLng.html#com.amap.api.maps">LatLng</a>
<div class="block">存储经纬度坐标值的类，单位角度。</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/maps/model/class-use/LatLngBounds.html#com.amap.api.maps">LatLngBounds</a>
<div class="block">代表了经纬度划分的一个矩形区域。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/maps/model/class-use/Marker.html#com.amap.api.maps">Marker</a>
<div class="block">定义地图 Marker 覆盖物

 Marker 是在地图上的一个点绘制图标。</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/maps/model/class-use/MarkerOptions.html#com.amap.api.maps">MarkerOptions</a>
<div class="block">Marker 的选项类。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/maps/model/class-use/MultiPointItem.html#com.amap.api.maps">MultiPointItem</a>
<div class="block">海量点中单个点的属性</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/maps/model/class-use/MultiPointOverlay.html#com.amap.api.maps">MultiPointOverlay</a>
<div class="block">海量点 管理对象<br>
 可以将大量点展示在地图上</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/maps/model/class-use/MultiPointOverlayOptions.html#com.amap.api.maps">MultiPointOverlayOptions</a>
<div class="block">海量点 的选项类。</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/maps/model/class-use/MVTTileOverlay.html#com.amap.api.maps">MVTTileOverlay</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/maps/model/class-use/MVTTileOverlayOptions.html#com.amap.api.maps">MVTTileOverlayOptions</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/maps/model/class-use/MyLocationStyle.html#com.amap.api.maps">MyLocationStyle</a>
<div class="block">定位小蓝点（当前位置）的绘制样式类。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/maps/model/class-use/MyTrafficStyle.html#com.amap.api.maps">MyTrafficStyle</a>
<div class="block">路况拥堵情况对应的颜色<br>
 默认颜色分布为：<br>
 畅通： 0xff00a209<br>
 缓慢： 0xffff7508<br>
 拥堵： 0xffea0312<br>
 严重拥堵： 0xff92000a<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/maps/model/class-use/NavigateArrow.html#com.amap.api.maps">NavigateArrow</a>
<div class="block">定义一个地图 箭头(NavigateArrow) 覆盖物
 一个导航箭头是多个连贯点的集合，拥有以下属性：
 
 顶点
 箭头是由两个顶点之间连贯的点构成的。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/maps/model/class-use/NavigateArrowOptions.html#com.amap.api.maps">NavigateArrowOptions</a>
<div class="block">导航箭头(NavigateArrow)覆盖物的选项类。</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/maps/model/class-use/NaviPara.html#com.amap.api.maps">NaviPara</a>
<div class="block">NaviPara 是唤起高德地图导航功能的参数类。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/maps/model/class-use/Poi.html#com.amap.api.maps">Poi</a>
<div class="block">点击地图Poi点时，该兴趣点的描述信息
 Poi 是指底图上的一个自带Poi点。</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/maps/model/class-use/PoiPara.html#com.amap.api.maps">PoiPara</a>
<div class="block">唤起高德地图周边搜索功能的参数类。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/maps/model/class-use/Polygon.html#com.amap.api.maps">Polygon</a>
<div class="block">定义在地图上绘制多边形覆盖物。</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/maps/model/class-use/PolygonOptions.html#com.amap.api.maps">PolygonOptions</a>
<div class="block">多边形覆盖物选项类</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/maps/model/class-use/Polyline.html#com.amap.api.maps">Polyline</a>
<div class="block">定义地图线段覆盖物。</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/maps/model/class-use/PolylineOptions.html#com.amap.api.maps">PolylineOptions</a>
<div class="block">线段的选项类</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/maps/model/class-use/RoutePara.html#com.amap.api.maps">RoutePara</a>
<div class="block">唤起高德地图路径规划功能的参数类。</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/maps/model/class-use/Text.html#com.amap.api.maps">Text</a>
<div class="block">定义在地图中绘制的文字覆盖物。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/maps/model/class-use/TextOptions.html#com.amap.api.maps">TextOptions</a>
<div class="block">创建文字覆盖物选项</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/maps/model/class-use/TileOverlay.html#com.amap.api.maps">TileOverlay</a>
<div class="block">定位地图瓦片图TileOverlay
 TileOverlay是瓦片图层的相关类。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/maps/model/class-use/TileOverlayOptions.html#com.amap.api.maps">TileOverlayOptions</a>
<div class="block">瓦片TileOverlay的构造选项。</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/maps/model/class-use/TileProjection.html#com.amap.api.maps">TileProjection</a>
<div class="block">瓦片编号范围。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/maps/model/class-use/VisibleRegion.html#com.amap.api.maps">VisibleRegion</a>
<div class="block">可视区域：地图View四个顶点对应的经纬度所围成的多边形被称作 可视区域；<br>

 此多边形是不规则四边形，如果地图没有倾斜时，可视区域为矩形，如果地图有倾斜时，可视区域为梯形。</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.amap.api.maps.model">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表类和解释">
<caption><span><a href="../../../../../com/amap/api/maps/model/package-summary.html">com.amap.api.maps.model</a>使用的<a href="../../../../../com/amap/api/maps/model/package-summary.html">com.amap.api.maps.model</a>中的类</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">类和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/maps/model/class-use/AMapPara.LineCapType.html#com.amap.api.maps.model">AMapPara.LineCapType</a>
<div class="block">边框尾部形状</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/maps/model/class-use/AMapPara.LineJoinType.html#com.amap.api.maps.model">AMapPara.LineJoinType</a>
<div class="block">边框连接处形状</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/maps/model/class-use/ArcOptions.html#com.amap.api.maps.model">ArcOptions</a>
<div class="block">圆形选项类</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/maps/model/class-use/BaseOptions.html#com.amap.api.maps.model">BaseOptions</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/maps/model/class-use/BasePointOverlay.html#com.amap.api.maps.model">BasePointOverlay</a>
<div class="block">点类型覆盖物基础对象，包含该设置位置、角度和信息窗体等基本信息</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/maps/model/class-use/BitmapDescriptor.html#com.amap.api.maps.model">BitmapDescriptor</a>
<div class="block">bitmap 描述信息
 在高德地图API 里，如果需要将一张图片绘制为Marker，需要用这个类把图片包装成对象，可以通过BitmapDescriptorFactory
 获得一个BitmapDescriptor 对象。</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/maps/model/class-use/BuildingOverlayOptions.html#com.amap.api.maps.model">BuildingOverlayOptions</a>
<div class="block">BuildingOverlay 选项类，包含高度颜色等设置</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/maps/model/class-use/CameraPosition.html#com.amap.api.maps.model">CameraPosition</a>
<div class="block">相机位置，这个类包含了所有的可视区域的位置参数。</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/maps/model/class-use/CameraPosition.Builder.html#com.amap.api.maps.model">CameraPosition.Builder</a>
<div class="block">创建一个摄像机的位置。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/maps/model/class-use/CircleHoleOptions.html#com.amap.api.maps.model">CircleHoleOptions</a>
<div class="block">创建圆洞的选项类</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/maps/model/class-use/CircleOptions.html#com.amap.api.maps.model">CircleOptions</a>
<div class="block">创建圆的选项类</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/maps/model/class-use/ColorLatLng.html#com.amap.api.maps.model">ColorLatLng</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/maps/model/class-use/CrossOverlay.UpdateItem.html#com.amap.api.maps.model">CrossOverlay.UpdateItem</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/maps/model/class-use/CustomMapStyleOptions.html#com.amap.api.maps.model">CustomMapStyleOptions</a>
<div class="block">自定义样式属性集</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/maps/model/class-use/GLTFOverlayOptions.html#com.amap.api.maps.model">GLTFOverlayOptions</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/maps/model/class-use/GLTFOverlayOptionsCreator.html#com.amap.api.maps.model">GLTFOverlayOptionsCreator</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/maps/model/class-use/GLTFResourceIterm.html#com.amap.api.maps.model">GLTFResourceIterm</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/maps/model/class-use/Gradient.html#com.amap.api.maps.model">Gradient</a>
<div class="block">热力图渐变颜色定义类</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/maps/model/class-use/GroundOverlayOptions.html#com.amap.api.maps.model">GroundOverlayOptions</a>
<div class="block">ground 覆盖物的选项类。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/maps/model/class-use/HeatMapGridLayerOptions.html#com.amap.api.maps.model">HeatMapGridLayerOptions</a>
<div class="block">实现热力图</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/maps/model/class-use/HeatMapItem.html#com.amap.api.maps.model">HeatMapItem</a>
<div class="block">热力图聚合点信息</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/maps/model/class-use/HeatMapLayerOptions.html#com.amap.api.maps.model">HeatMapLayerOptions</a>
<div class="block">实现高德热力图</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/maps/model/class-use/HeatmapTileProvider.html#com.amap.api.maps.model">HeatmapTileProvider</a>
<div class="block">实现TileProvider类，高德热力图Provider</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/maps/model/class-use/HeatmapTileProvider.Builder.html#com.amap.api.maps.model">HeatmapTileProvider.Builder</a>
<div class="block">热力图构造器.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/maps/model/class-use/ImageOptions.ShapeType.html#com.amap.api.maps.model">ImageOptions.ShapeType</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/maps/model/class-use/LatLng.html#com.amap.api.maps.model">LatLng</a>
<div class="block">存储经纬度坐标值的类，单位角度。</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/maps/model/class-use/LatLngBounds.html#com.amap.api.maps.model">LatLngBounds</a>
<div class="block">代表了经纬度划分的一个矩形区域。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/maps/model/class-use/LatLngBounds.Builder.html#com.amap.api.maps.model">LatLngBounds.Builder</a>
<div class="block">经纬度坐标矩形区域的生成器。</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/maps/model/class-use/MarkerOptions.html#com.amap.api.maps.model">MarkerOptions</a>
<div class="block">Marker 的选项类。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/maps/model/class-use/MultiPointItem.html#com.amap.api.maps.model">MultiPointItem</a>
<div class="block">海量点中单个点的属性</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/maps/model/class-use/MultiPointOverlayOptions.html#com.amap.api.maps.model">MultiPointOverlayOptions</a>
<div class="block">海量点 的选项类。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/maps/model/class-use/MVTTileOverlayOptions.html#com.amap.api.maps.model">MVTTileOverlayOptions</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/maps/model/class-use/MVTTileOverlayOptions.Builder.html#com.amap.api.maps.model">MVTTileOverlayOptions.Builder</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/maps/model/class-use/MyLocationStyle.html#com.amap.api.maps.model">MyLocationStyle</a>
<div class="block">定位小蓝点（当前位置）的绘制样式类。</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/maps/model/class-use/NavigateArrowOptions.html#com.amap.api.maps.model">NavigateArrowOptions</a>
<div class="block">导航箭头(NavigateArrow)覆盖物的选项类。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/maps/model/class-use/PolygonHoleOptions.html#com.amap.api.maps.model">PolygonHoleOptions</a>
<div class="block">多边形洞配置类</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/maps/model/class-use/PolygonOptions.html#com.amap.api.maps.model">PolygonOptions</a>
<div class="block">多边形覆盖物选项类</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/maps/model/class-use/PolylineOptions.html#com.amap.api.maps.model">PolylineOptions</a>
<div class="block">线段的选项类</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/maps/model/class-use/PolylineOptions.LineCapType.html#com.amap.api.maps.model">PolylineOptions.LineCapType</a>
<div class="block">Polyline尾部形状</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/maps/model/class-use/PolylineOptions.LineJoinType.html#com.amap.api.maps.model">PolylineOptions.LineJoinType</a>
<div class="block">Polyline连接处形状</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/maps/model/class-use/TextOptions.html#com.amap.api.maps.model">TextOptions</a>
<div class="block">创建文字覆盖物选项</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/maps/model/class-use/Tile.html#com.amap.api.maps.model">Tile</a>
<div class="block">图片瓦块信息类。</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/maps/model/class-use/TileOverlayOptions.html#com.amap.api.maps.model">TileOverlayOptions</a>
<div class="block">瓦片TileOverlay的构造选项。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/maps/model/class-use/TileProjection.html#com.amap.api.maps.model">TileProjection</a>
<div class="block">瓦片编号范围。</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/maps/model/class-use/TileProvider.html#com.amap.api.maps.model">TileProvider</a>
<div class="block">接口类，为类TileOverlay提供万片图像。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/maps/model/class-use/WeightedLatLng.html#com.amap.api.maps.model">WeightedLatLng</a>
<div class="block">带权值的经纬度位置点</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.amap.api.maps.model.animation">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表类和解释">
<caption><span><a href="../../../../../com/amap/api/maps/model/animation/package-summary.html">com.amap.api.maps.model.animation</a>使用的<a href="../../../../../com/amap/api/maps/model/package-summary.html">com.amap.api.maps.model</a>中的类</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">类和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/maps/model/class-use/LatLng.html#com.amap.api.maps.model.animation">LatLng</a>
<div class="block">存储经纬度坐标值的类，单位角度。</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.amap.api.maps.model.particle">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表类和解释">
<caption><span><a href="../../../../../com/amap/api/maps/model/particle/package-summary.html">com.amap.api.maps.model.particle</a>使用的<a href="../../../../../com/amap/api/maps/model/package-summary.html">com.amap.api.maps.model</a>中的类</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">类和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/maps/model/class-use/BaseOptions.html#com.amap.api.maps.model.particle">BaseOptions</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/maps/model/class-use/BitmapDescriptor.html#com.amap.api.maps.model.particle">BitmapDescriptor</a>
<div class="block">bitmap 描述信息
 在高德地图API 里，如果需要将一张图片绘制为Marker，需要用这个类把图片包装成对象，可以通过BitmapDescriptorFactory
 获得一个BitmapDescriptor 对象。</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.amap.api.maps.utils">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表类和解释">
<caption><span><a href="../../../../../com/amap/api/maps/utils/package-summary.html">com.amap.api.maps.utils</a>使用的<a href="../../../../../com/amap/api/maps/model/package-summary.html">com.amap.api.maps.model</a>中的类</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">类和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/maps/model/class-use/LatLng.html#com.amap.api.maps.utils">LatLng</a>
<div class="block">存储经纬度坐标值的类，单位角度。</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.amap.api.maps.utils.overlay">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表类和解释">
<caption><span><a href="../../../../../com/amap/api/maps/utils/overlay/package-summary.html">com.amap.api.maps.utils.overlay</a>使用的<a href="../../../../../com/amap/api/maps/model/package-summary.html">com.amap.api.maps.model</a>中的类</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">类和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/maps/model/class-use/BasePointOverlay.html#com.amap.api.maps.utils.overlay">BasePointOverlay</a>
<div class="block">点类型覆盖物基础对象，包含该设置位置、角度和信息窗体等基本信息</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/maps/model/class-use/BitmapDescriptor.html#com.amap.api.maps.utils.overlay">BitmapDescriptor</a>
<div class="block">bitmap 描述信息
 在高德地图API 里，如果需要将一张图片绘制为Marker，需要用这个类把图片包装成对象，可以通过BitmapDescriptorFactory
 获得一个BitmapDescriptor 对象。</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/maps/model/class-use/LatLng.html#com.amap.api.maps.utils.overlay">LatLng</a>
<div class="block">存储经纬度坐标值的类，单位角度。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/amap/api/maps/model/class-use/Marker.html#com.amap.api.maps.utils.overlay">Marker</a>
<div class="block">定义地图 Marker 覆盖物

 Marker 是在地图上的一个点绘制图标。</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.amap.api.trace">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="使用表, 列表类和解释">
<caption><span><a href="../../../../../com/amap/api/trace/package-summary.html">com.amap.api.trace</a>使用的<a href="../../../../../com/amap/api/maps/model/package-summary.html">com.amap.api.maps.model</a>中的类</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">类和说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/amap/api/maps/model/class-use/LatLng.html#com.amap.api.trace">LatLng</a>
<div class="block">存储经纬度坐标值的类，单位角度。</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li>类</li>
<li class="navBarCell1Rev">使用</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个</li>
<li>下一个</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/maps/model/package-use.html" target="_top">框架</a></li>
<li><a href="package-use.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
