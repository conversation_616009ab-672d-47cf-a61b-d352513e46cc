<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Circle</title>
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Circle";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10};
var tabs = {65535:["t0","所有方法"],2:["t2","实例方法"],8:["t4","具体方法"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/Circle.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/maps/model/CameraPosition.Builder.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/amap/api/maps/model/CircleHoleOptions.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/maps/model/Circle.html" target="_top">框架</a></li>
<li><a href="Circle.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.com.amap.api.maps.model.BaseOverlay">字段</a>&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.amap.api.maps.model</div>
<h2 title="类 Circle" class="title">类 Circle</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../../../com/amap/api/maps/model/BaseOverlay.html" title="com.amap.api.maps.model中的类">com.amap.api.maps.model.BaseOverlay</a></li>
<li>
<ul class="inheritance">
<li>com.amap.api.maps.model.Circle</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public final class <span class="typeNameLabel">Circle</span>
extends <a href="../../../../../com/amap/api/maps/model/BaseOverlay.html" title="com.amap.api.maps.model中的类">BaseOverlay</a></pre>
<div class="block">定义了在地图上绘制圆的类。
 <p>圆对象有以下属性：</p>
 <ul>
 <li>圆心 ：圆心的经纬度。</li>
 <li>半径： 圆的半径，单位：米。这个值应该大于等于0。</li>
 <li>边框宽度： 圆的边框宽度。这个值设置后不会受到可视区域缩放级别的影响。默认为10。</li>
 <li>边框颜色：圆的边框颜色，ARGB格式。默认为黑色。 </li>
 <li>填充颜色：圆的填充颜色，ARGB格式。默认为透明。 </li>
 <li>Z轴：Z轴是控制覆盖物重复区域的绘制顺序的值。Z轴较大的覆盖物会绘制在Z轴较小的覆盖物上面。如果两个覆盖物的Z轴数值相同，则覆盖情况将随机。默认值为0。</li>
 <li>可见属性：标识多边形是否可见。如果可见性为否，则不会被绘制。 圆形方法必须在主线程中调用，否则会抛出IllegalStateException。</li>
 </ul></div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>字段概要</h3>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.com.amap.api.maps.model.BaseOverlay">
<!--   -->
</a>
<h3>从类继承的字段&nbsp;com.amap.api.maps.model.<a href="../../../../../com/amap/api/maps/model/BaseOverlay.html" title="com.amap.api.maps.model中的类">BaseOverlay</a></h3>
<code><a href="../../../../../com/amap/api/maps/model/BaseOverlay.html#Field1">Field1</a>, <a href="../../../../../com/amap/api/maps/model/BaseOverlay.html#Field2">Field2</a>, <a href="../../../../../com/amap/api/maps/model/BaseOverlay.html#overlayName">overlayName</a></code></li>
</ul>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>所有方法</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">实例方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">具体方法</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/model/Circle.html#contains-com.amap.api.maps.model.LatLng-">contains</a></span>(<a href="../../../../../com/amap/api/maps/model/LatLng.html" title="com.amap.api.maps.model中的类">LatLng</a>&nbsp;latLng)</code>
<div class="block">判断圆是否包含传入的经纬度点。</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/model/Circle.html#equals-java.lang.Object-">equals</a></span>(java.lang.Object&nbsp;paramObject)</code>&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/maps/model/LatLng.html" title="com.amap.api.maps.model中的类">LatLng</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/model/Circle.html#getCenter--">getCenter</a></span>()</code>
<div class="block">获取圆心经纬度坐标。</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/model/Circle.html#getFillColor--">getFillColor</a></span>()</code>
<div class="block">获取圆的填充颜色</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>java.util.List</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/model/Circle.html#getHoleOptions--">getHoleOptions</a></span>()</code>
<div class="block">获取洞的配置项集合</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/model/Circle.html#getId--">getId</a></span>()</code>
<div class="block">返回圆的id。</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/model/Circle.html#getRadius--">getRadius</a></span>()</code>
<div class="block">获取圆的半径，单位米。</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/model/Circle.html#getStrokeColor--">getStrokeColor</a></span>()</code>
<div class="block">获取圆的边框颜色</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/model/Circle.html#getStrokeDottedLineType--">getStrokeDottedLineType</a></span>()</code>
<div class="block">获取圆的边框虚线形状。</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/model/Circle.html#getStrokeWidth--">getStrokeWidth</a></span>()</code>
<div class="block">获取圆的边框宽度。</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/model/Circle.html#getZIndex--">getZIndex</a></span>()</code>
<div class="block">获取圆的Z轴数值</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/model/Circle.html#hashCode--">hashCode</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/model/Circle.html#isVisible--">isVisible</a></span>()</code>
<div class="block">获取圆是否可见</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/model/Circle.html#remove--">remove</a></span>()</code>
<div class="block">删除从地图对象里圆</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/model/Circle.html#setCenter-com.amap.api.maps.model.LatLng-">setCenter</a></span>(<a href="../../../../../com/amap/api/maps/model/LatLng.html" title="com.amap.api.maps.model中的类">LatLng</a>&nbsp;center)</code>
<div class="block">设置圆心经纬度坐标，参数不能为null，无默认值。</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/model/Circle.html#setFillColor-int-">setFillColor</a></span>(int&nbsp;color)</code>
<div class="block">设置圆的填充颜色。</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/model/Circle.html#setHoleOptions-java.util.List-">setHoleOptions</a></span>(java.util.List&nbsp;options)</code>
<div class="block">设置洞的配置项集合</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/model/Circle.html#setRadius-double-">setRadius</a></span>(double&nbsp;radius)</code>
<div class="block">设置圆的半径，单位米。</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/model/Circle.html#setStrokeColor-int-">setStrokeColor</a></span>(int&nbsp;color)</code>
<div class="block">设置圆的边框颜色。</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/model/Circle.html#setStrokeDottedLineType-int-">setStrokeDottedLineType</a></span>(int&nbsp;type)</code>
<div class="block">设置圆的边框虚线形状。</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/model/Circle.html#setStrokeWidth-float-">setStrokeWidth</a></span>(float&nbsp;width)</code>
<div class="block">设置圆的边框宽度，单位像素。</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/model/Circle.html#setVisible-boolean-">setVisible</a></span>(boolean&nbsp;visible)</code>
<div class="block">设置圆的可见属性。</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/model/Circle.html#setZIndex-float-">setZIndex</a></span>(float&nbsp;zIndex)</code>
<div class="block">设置圆的Z轴数值。</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.amap.api.maps.model.BaseOverlay">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;com.amap.api.maps.model.<a href="../../../../../com/amap/api/maps/model/BaseOverlay.html" title="com.amap.api.maps.model中的类">BaseOverlay</a></h3>
<code><a href="../../../../../com/amap/api/maps/model/BaseOverlay.html#method1-java.lang.Object...-">method1</a>, <a href="../../../../../com/amap/api/maps/model/BaseOverlay.html#method2-java.lang.Object...-">method2</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, finalize, getClass, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="remove--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>remove</h4>
<pre>public&nbsp;void&nbsp;remove()</pre>
<div class="block">删除从地图对象里圆</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="getId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getId</h4>
<pre>public&nbsp;java.lang.String&nbsp;getId()</pre>
<div class="block">返回圆的id。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>返回圆的id。此id是覆盖物在所属的地图对象里的唯一标识。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="setCenter-com.amap.api.maps.model.LatLng-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCenter</h4>
<pre>public&nbsp;void&nbsp;setCenter(<a href="../../../../../com/amap/api/maps/model/LatLng.html" title="com.amap.api.maps.model中的类">LatLng</a>&nbsp;center)</pre>
<div class="block">设置圆心经纬度坐标，参数不能为null，无默认值。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>center</code> - 圆心经纬度坐标。</dd>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code>java.lang.NullPointerException</code> - 如果圆心经纬度坐标为null。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="getCenter--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCenter</h4>
<pre>public&nbsp;<a href="../../../../../com/amap/api/maps/model/LatLng.html" title="com.amap.api.maps.model中的类">LatLng</a>&nbsp;getCenter()</pre>
<div class="block">获取圆心经纬度坐标。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>圆心经纬度坐标。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="setRadius-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRadius</h4>
<pre>public&nbsp;void&nbsp;setRadius(double&nbsp;radius)</pre>
<div class="block">设置圆的半径，单位米。半径必须大于等于0。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>radius</code> - 圆形半径，单位米。</dd>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code>java.lang.IllegalArgumentException</code> - 如果传入的半径为负数。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="getRadius--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRadius</h4>
<pre>public&nbsp;double&nbsp;getRadius()</pre>
<div class="block">获取圆的半径，单位米。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>圆形半径，单位米。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="setStrokeWidth-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setStrokeWidth</h4>
<pre>public&nbsp;void&nbsp;setStrokeWidth(float&nbsp;width)</pre>
<div class="block">设置圆的边框宽度，单位像素。参数必须大于等于0。如果为0则不会绘制边框。默认为10。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>width</code> - 边框宽度，单位像素。</dd>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code>java.lang.IllegalArgumentException</code> - 如果宽度为负数。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="getStrokeWidth--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStrokeWidth</h4>
<pre>public&nbsp;float&nbsp;getStrokeWidth()</pre>
<div class="block">获取圆的边框宽度。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>圆的边框宽度，单位像素。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="setStrokeColor-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setStrokeColor</h4>
<pre>public&nbsp;void&nbsp;setStrokeColor(int&nbsp;color)</pre>
<div class="block">设置圆的边框颜色。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>color</code> - 边框颜色，ARGB格式。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="getStrokeColor--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStrokeColor</h4>
<pre>public&nbsp;int&nbsp;getStrokeColor()</pre>
<div class="block">获取圆的边框颜色</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>圆的边框颜色，ARGB格式。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="setFillColor-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFillColor</h4>
<pre>public&nbsp;void&nbsp;setFillColor(int&nbsp;color)</pre>
<div class="block">设置圆的填充颜色。填充颜色是绘制边框以内部分的颜色，ARGB格式。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>color</code> - 填充颜色ARGB格式。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="getFillColor--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFillColor</h4>
<pre>public&nbsp;int&nbsp;getFillColor()</pre>
<div class="block">获取圆的填充颜色</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>圆填充颜色的ARGB格式</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="setZIndex-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setZIndex</h4>
<pre>public&nbsp;void&nbsp;setZIndex(float&nbsp;zIndex)</pre>
<div class="block">设置圆的Z轴数值。数值更高的圆将绘制在数值较低的上面。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>zIndex</code> - Z轴数值。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="getZIndex--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getZIndex</h4>
<pre>public&nbsp;float&nbsp;getZIndex()</pre>
<div class="block">获取圆的Z轴数值</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>圆的Z轴数值</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="setVisible-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setVisible</h4>
<pre>public&nbsp;void&nbsp;setVisible(boolean&nbsp;visible)</pre>
<div class="block">设置圆的可见属性。如果不可见属性为false，则不会被绘制在地图上。默认为true。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>visible</code> - true为可见；false不可见。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="isVisible--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isVisible</h4>
<pre>public&nbsp;boolean&nbsp;isVisible()</pre>
<div class="block">获取圆是否可见</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>true 为可见，false为不可见。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="equals-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>equals</h4>
<pre>public&nbsp;boolean&nbsp;equals(java.lang.Object&nbsp;paramObject)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">覆盖:</span></dt>
<dd><code>equals</code>&nbsp;在类中&nbsp;<code>java.lang.Object</code></dd>
</dl>
</li>
</ul>
<a name="hashCode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>hashCode</h4>
<pre>public&nbsp;int&nbsp;hashCode()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">覆盖:</span></dt>
<dd><code>hashCode</code>&nbsp;在类中&nbsp;<code>java.lang.Object</code></dd>
</dl>
</li>
</ul>
<a name="contains-com.amap.api.maps.model.LatLng-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>contains</h4>
<pre>public&nbsp;boolean&nbsp;contains(<a href="../../../../../com/amap/api/maps/model/LatLng.html" title="com.amap.api.maps.model中的类">LatLng</a>&nbsp;latLng)</pre>
<div class="block">判断圆是否包含传入的经纬度点。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>latLng</code> - 经纬度点。</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>true 包含，false 为不包含。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.4</dd>
</dl>
</li>
</ul>
<a name="setHoleOptions-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setHoleOptions</h4>
<pre>public&nbsp;void&nbsp;setHoleOptions(java.util.List&nbsp;options)</pre>
<div class="block">设置洞的配置项集合</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>options</code> - 洞的配置项集合</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>5.5.0</dd>
</dl>
</li>
</ul>
<a name="getHoleOptions--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHoleOptions</h4>
<pre>public&nbsp;java.util.List&nbsp;getHoleOptions()</pre>
<div class="block">获取洞的配置项集合</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>洞的配置项集合</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>5.5.0</dd>
</dl>
</li>
</ul>
<a name="setStrokeDottedLineType-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setStrokeDottedLineType</h4>
<pre>public&nbsp;void&nbsp;setStrokeDottedLineType(int&nbsp;type)</pre>
<div class="block">设置圆的边框虚线形状。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>type</code> - 形状：<br>
                <a href="../../../../../com/amap/api/maps/model/AMapPara.html#DOTTEDLINE_TYPE_DEFAULT"><code>AMapPara.DOTTEDLINE_TYPE_DEFAULT</code></a>:不绘制虚线（默认）<br>
              <a href="../../../../../com/amap/api/maps/model/AMapPara.html#DOTTEDLINE_TYPE_SQUARE"><code>AMapPara.DOTTEDLINE_TYPE_SQUARE</code></a>:方形；<br>
             <a href="../../../../../com/amap/api/maps/model/AMapPara.html#DOTTEDLINE_TYPE_CIRCLE"><code>AMapPara.DOTTEDLINE_TYPE_CIRCLE</code></a>：圆形；<br>
             如果设置不属于给出的形状，则不绘制虚线</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.2.0</dd>
</dl>
</li>
</ul>
<a name="getStrokeDottedLineType--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getStrokeDottedLineType</h4>
<pre>public&nbsp;int&nbsp;getStrokeDottedLineType()</pre>
<div class="block">获取圆的边框虚线形状。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>形状： <a href="../../../../../com/amap/api/maps/model/AMapPara.html#DOTTEDLINE_TYPE_DEFAULT"><code>AMapPara.DOTTEDLINE_TYPE_DEFAULT</code></a>:不绘制虚线（默认）<br></dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>6.2.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/Circle.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/maps/model/CameraPosition.Builder.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/amap/api/maps/model/CircleHoleOptions.html" title="com.amap.api.maps.model中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/maps/model/Circle.html" target="_top">框架</a></li>
<li><a href="Circle.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.com.amap.api.maps.model.BaseOverlay">字段</a>&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
