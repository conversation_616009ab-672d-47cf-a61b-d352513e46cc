<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Province</title>
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Province";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10};
var tabs = {65535:["t0","所有方法"],2:["t2","实例方法"],8:["t4","具体方法"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/Province.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/maps/offlinemap/OfflineMapStatus.html" title="com.amap.api.maps.offlinemap中的类"><span class="typeNameLink">上一个类</span></a></li>
<li>下一个类</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/maps/offlinemap/Province.html" target="_top">框架</a></li>
<li><a href="Province.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li><a href="#field.summary">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li><a href="#field.detail">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.amap.api.maps.offlinemap</div>
<h2 title="类 Province" class="title">类 Province</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.amap.api.maps.offlinemap.Province</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>所有已实现的接口:</dt>
<dd>android.os.Parcelable</dd>
</dl>
<dl>
<dt>直接已知子类:</dt>
<dd><a href="../../../../../com/amap/api/maps/offlinemap/OfflineMapProvince.html" title="com.amap.api.maps.offlinemap中的类">OfflineMapProvince</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">Province</span>
extends java.lang.Object
implements android.os.Parcelable</pre>
<div class="block">省属性的相关类。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>v2.2.0</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>嵌套类概要</h3>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.android.os.Parcelable">
<!--   -->
</a>
<h3>从接口继承的嵌套类/接口&nbsp;android.os.Parcelable</h3>
<code>android.os.Parcelable.ClassLoaderCreator&lt;T&gt;, android.os.Parcelable.Creator&lt;T&gt;</code></li>
</ul>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>字段概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="字段概要表, 列表字段和解释">
<caption><span>字段</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">字段和说明</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static android.os.Parcelable.Creator&lt;<a href="../../../../../com/amap/api/maps/offlinemap/Province.html" title="com.amap.api.maps.offlinemap中的类">Province</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/offlinemap/Province.html#CREATOR">CREATOR</a></span></code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.android.os.Parcelable">
<!--   -->
</a>
<h3>从接口继承的字段&nbsp;android.os.Parcelable</h3>
<code>CONTENTS_FILE_DESCRIPTOR, PARCELABLE_WRITE_RETURN_VALUE</code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/offlinemap/Province.html#Province--">Province</a></span>()</code>
<div class="block">构造一个Province对象。</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>所有方法</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">实例方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">具体方法</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/offlinemap/Province.html#getJianpin--">getJianpin</a></span>()</code>
<div class="block">返回省名称简拼拼音。</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/offlinemap/Province.html#getPinyin--">getPinyin</a></span>()</code>
<div class="block">返回省名称拼音。</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/offlinemap/Province.html#getProvinceCode--">getProvinceCode</a></span>()</code>
<div class="block">得到省的行政编码。</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/offlinemap/Province.html#getProvinceName--">getProvinceName</a></span>()</code>
<div class="block">返回省名称。</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/offlinemap/Province.html#setJianpin-java.lang.String-">setJianpin</a></span>(java.lang.String&nbsp;jianpin)</code>
<div class="block">设置省名称简拼拼音。</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/offlinemap/Province.html#setPinyin-java.lang.String-">setPinyin</a></span>(java.lang.String&nbsp;pinyin)</code>
<div class="block">设置省名称拼音。</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/offlinemap/Province.html#setProvinceCode-java.lang.String-">setProvinceCode</a></span>(java.lang.String&nbsp;provinceCode)</code>
<div class="block">设置省行政编码。</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/offlinemap/Province.html#setProvinceName-java.lang.String-">setProvinceName</a></span>(java.lang.String&nbsp;provinceName)</code>
<div class="block">设置省名称。</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.android.os.Parcelable">
<!--   -->
</a>
<h3>从接口继承的方法&nbsp;android.os.Parcelable</h3>
<code>describeContents, writeToParcel</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>字段详细资料</h3>
<a name="CREATOR">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>CREATOR</h4>
<pre>public static final&nbsp;android.os.Parcelable.Creator&lt;<a href="../../../../../com/amap/api/maps/offlinemap/Province.html" title="com.amap.api.maps.offlinemap中的类">Province</a>&gt; CREATOR</pre>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>v2.2.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="Province--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>Province</h4>
<pre>public&nbsp;Province()</pre>
<div class="block">构造一个Province对象。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>v2.2.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="getProvinceName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProvinceName</h4>
<pre>public&nbsp;java.lang.String&nbsp;getProvinceName()</pre>
<div class="block">返回省名称。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>省名称。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>v2.2.0</dd>
</dl>
</li>
</ul>
<a name="getJianpin--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getJianpin</h4>
<pre>public&nbsp;java.lang.String&nbsp;getJianpin()</pre>
<div class="block">返回省名称简拼拼音。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>省名称简拼拼音。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>v2.2.0</dd>
</dl>
</li>
</ul>
<a name="getPinyin--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPinyin</h4>
<pre>public&nbsp;java.lang.String&nbsp;getPinyin()</pre>
<div class="block">返回省名称拼音。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>省名称拼音。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>v2.2.0</dd>
</dl>
</li>
</ul>
<a name="setProvinceName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setProvinceName</h4>
<pre>public&nbsp;void&nbsp;setProvinceName(java.lang.String&nbsp;provinceName)</pre>
<div class="block">设置省名称。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>provinceName</code> - 省名称。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>v2.2.0</dd>
</dl>
</li>
</ul>
<a name="setJianpin-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setJianpin</h4>
<pre>public&nbsp;void&nbsp;setJianpin(java.lang.String&nbsp;jianpin)</pre>
<div class="block">设置省名称简拼拼音。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>jianpin</code> - 省名称简拼拼音。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>v2.2.0</dd>
</dl>
</li>
</ul>
<a name="setPinyin-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPinyin</h4>
<pre>public&nbsp;void&nbsp;setPinyin(java.lang.String&nbsp;pinyin)</pre>
<div class="block">设置省名称拼音。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>pinyin</code> - 省名称拼音。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>v2.2.0</dd>
</dl>
</li>
</ul>
<a name="setProvinceCode-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setProvinceCode</h4>
<pre>public&nbsp;void&nbsp;setProvinceCode(java.lang.String&nbsp;provinceCode)</pre>
<div class="block">设置省行政编码。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>provinceCode</code> - 省的行政编码。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>v2.2.0</dd>
</dl>
</li>
</ul>
<a name="getProvinceCode--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getProvinceCode</h4>
<pre>public&nbsp;java.lang.String&nbsp;getProvinceCode()</pre>
<div class="block">得到省的行政编码。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>省的行政编码。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>v2.2.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/Province.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/maps/offlinemap/OfflineMapStatus.html" title="com.amap.api.maps.offlinemap中的类"><span class="typeNameLink">上一个类</span></a></li>
<li>下一个类</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/maps/offlinemap/Province.html" target="_top">框架</a></li>
<li><a href="Province.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li><a href="#field.summary">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li><a href="#field.detail">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
