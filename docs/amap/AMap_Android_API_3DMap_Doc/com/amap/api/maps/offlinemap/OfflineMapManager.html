<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>OfflineMapManager</title>
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="OfflineMapManager";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10};
var tabs = {65535:["t0","所有方法"],2:["t2","实例方法"],8:["t4","具体方法"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/OfflineMapManager.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/maps/offlinemap/OfflineMapCity.html" title="com.amap.api.maps.offlinemap中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/amap/api/maps/offlinemap/OfflineMapManager.OfflineLoadedListener.html" title="com.amap.api.maps.offlinemap中的接口"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/maps/offlinemap/OfflineMapManager.html" target="_top">框架</a></li>
<li><a href="OfflineMapManager.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li><a href="#nested.class.summary">嵌套</a>&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.amap.api.maps.offlinemap</div>
<h2 title="类 OfflineMapManager" class="title">类 OfflineMapManager</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.amap.api.maps.offlinemap.OfflineMapManager</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public final class <span class="typeNameLabel">OfflineMapManager</span>
extends java.lang.Object</pre>
<div class="block">离线地图下载管理类，支持3D矢量地图下载。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.1</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>嵌套类概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="嵌套类概要表, 列表嵌套类和解释">
<caption><span>嵌套类</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">类和说明</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static interface&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/offlinemap/OfflineMapManager.OfflineLoadedListener.html" title="com.amap.api.maps.offlinemap中的接口">OfflineMapManager.OfflineLoadedListener</a></span></code>
<div class="block">离线地图初始化完成回调</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static interface&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/offlinemap/OfflineMapManager.OfflineMapDownloadListener.html" title="com.amap.api.maps.offlinemap中的接口">OfflineMapManager.OfflineMapDownloadListener</a></span></code>
<div class="block">离线地图下载过程中状态回调</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/offlinemap/OfflineMapManager.html#OfflineMapManager-android.content.Context-com.amap.api.maps.offlinemap.OfflineMapManager.OfflineMapDownloadListener-">OfflineMapManager</a></span>(android.content.Context&nbsp;context,
                 <a href="../../../../../com/amap/api/maps/offlinemap/OfflineMapManager.OfflineMapDownloadListener.html" title="com.amap.api.maps.offlinemap中的接口">OfflineMapManager.OfflineMapDownloadListener</a>&nbsp;listener)</code>
<div class="block">根据给定的参数来构造OfflineMapManager对象。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/offlinemap/OfflineMapManager.html#OfflineMapManager-android.content.Context-com.amap.api.maps.offlinemap.OfflineMapManager.OfflineMapDownloadListener-com.amap.api.maps.AMap-">OfflineMapManager</a></span>(android.content.Context&nbsp;context,
                 <a href="../../../../../com/amap/api/maps/offlinemap/OfflineMapManager.OfflineMapDownloadListener.html" title="com.amap.api.maps.offlinemap中的接口">OfflineMapManager.OfflineMapDownloadListener</a>&nbsp;listener,
                 <a href="../../../../../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a>&nbsp;amap)</code>
<div class="block">根据给定的参数来构造OfflineMapManager对象。</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>所有方法</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">实例方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">具体方法</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/offlinemap/OfflineMapManager.html#destroy--">destroy</a></span>()</code>
<div class="block">销毁offlineManager中的资源</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/offlinemap/OfflineMapManager.html#downloadByCityCode-java.lang.String-">downloadByCityCode</a></span>(java.lang.String&nbsp;citycode)</code>
<div class="block">根据给定的城市编码下载该城市的离线地图包<br>
 异步方法，如果有注册OfflineMapDownloadListener监听，下载状态会回调onDownload方法</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/offlinemap/OfflineMapManager.html#downloadByCityName-java.lang.String-">downloadByCityName</a></span>(java.lang.String&nbsp;cityname)</code>
<div class="block">根据给定的城市名称下载该城市的离线地图包<br>
 异步方法，如果有注册OfflineMapDownloadListener监听，下载状态会回调onDownload方法</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/offlinemap/OfflineMapManager.html#downloadByProvinceName-java.lang.String-">downloadByProvinceName</a></span>(java.lang.String&nbsp;name)</code>
<div class="block">异步方法，如果有注册OfflineMapDownloadListener监听，下载状态会回调onDownload方法。</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>java.util.ArrayList&lt;<a href="../../../../../com/amap/api/maps/offlinemap/OfflineMapCity.html" title="com.amap.api.maps.offlinemap中的类">OfflineMapCity</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/offlinemap/OfflineMapManager.html#getDownloadingCityList--">getDownloadingCityList</a></span>()</code>
<div class="block">所有正在下载或等待下载离线地图的城市列表。</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>java.util.ArrayList&lt;<a href="../../../../../com/amap/api/maps/offlinemap/OfflineMapProvince.html" title="com.amap.api.maps.offlinemap中的类">OfflineMapProvince</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/offlinemap/OfflineMapManager.html#getDownloadingProvinceList--">getDownloadingProvinceList</a></span>()</code>
<div class="block">所有正在下载或等待下载离线地图的省份列表。</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>java.util.ArrayList&lt;<a href="../../../../../com/amap/api/maps/offlinemap/OfflineMapCity.html" title="com.amap.api.maps.offlinemap中的类">OfflineMapCity</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/offlinemap/OfflineMapManager.html#getDownloadOfflineMapCityList--">getDownloadOfflineMapCityList</a></span>()</code>
<div class="block">返回已经下载完成离线地图的城市列表。</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>java.util.ArrayList&lt;<a href="../../../../../com/amap/api/maps/offlinemap/OfflineMapProvince.html" title="com.amap.api.maps.offlinemap中的类">OfflineMapProvince</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/offlinemap/OfflineMapManager.html#getDownloadOfflineMapProvinceList--">getDownloadOfflineMapProvinceList</a></span>()</code>
<div class="block">返回已经下载完成离线地图的省份列表。</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/maps/offlinemap/OfflineMapCity.html" title="com.amap.api.maps.offlinemap中的类">OfflineMapCity</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/offlinemap/OfflineMapManager.html#getItemByCityCode-java.lang.String-">getItemByCityCode</a></span>(java.lang.String&nbsp;cityCode)</code>
<div class="block">根据城市编码获取OfflineMapCity对象<br>
 同步方法</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/maps/offlinemap/OfflineMapCity.html" title="com.amap.api.maps.offlinemap中的类">OfflineMapCity</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/offlinemap/OfflineMapManager.html#getItemByCityName-java.lang.String-">getItemByCityName</a></span>(java.lang.String&nbsp;cityName)</code>
<div class="block">根据城市名称获取OfflneMapCity对象<br>
 同步方法</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code><a href="../../../../../com/amap/api/maps/offlinemap/OfflineMapProvince.html" title="com.amap.api.maps.offlinemap中的类">OfflineMapProvince</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/offlinemap/OfflineMapManager.html#getItemByProvinceName-java.lang.String-">getItemByProvinceName</a></span>(java.lang.String&nbsp;provinceName)</code>
<div class="block">根据省份名称获取OfflineMapProvince对象<br>
 同步方法</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>java.util.ArrayList&lt;<a href="../../../../../com/amap/api/maps/offlinemap/OfflineMapCity.html" title="com.amap.api.maps.offlinemap中的类">OfflineMapCity</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/offlinemap/OfflineMapManager.html#getOfflineMapCityList--">getOfflineMapCityList</a></span>()</code>
<div class="block">获取所有存在有离线地图的城市列表。</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>java.util.ArrayList&lt;<a href="../../../../../com/amap/api/maps/offlinemap/OfflineMapProvince.html" title="com.amap.api.maps.offlinemap中的类">OfflineMapProvince</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/offlinemap/OfflineMapManager.html#getOfflineMapProvinceList--">getOfflineMapProvinceList</a></span>()</code>
<div class="block">获取所有存在有离线地图的省的列表。</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/offlinemap/OfflineMapManager.html#pause--">pause</a></span>()</code>
<div class="block">暂停离线地图下载。</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/offlinemap/OfflineMapManager.html#pauseByName-java.lang.String-">pauseByName</a></span>(java.lang.String&nbsp;name)</code>&nbsp;</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/offlinemap/OfflineMapManager.html#remove-java.lang.String-">remove</a></span>(java.lang.String&nbsp;name)</code>
<div class="block">根据给定的城市名称删除该城市的离线地图包。</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/offlinemap/OfflineMapManager.html#restart--">restart</a></span>()</code>
<div class="block">重新开始任务,开始下载队列中的第一个为等待中的任务</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/offlinemap/OfflineMapManager.html#setOnOfflineLoadedListener-com.amap.api.maps.offlinemap.OfflineMapManager.OfflineLoadedListener-">setOnOfflineLoadedListener</a></span>(<a href="../../../../../com/amap/api/maps/offlinemap/OfflineMapManager.OfflineLoadedListener.html" title="com.amap.api.maps.offlinemap中的接口">OfflineMapManager.OfflineLoadedListener</a>&nbsp;listener)</code>
<div class="block">设置离线地图城市列表初始化回调。</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/offlinemap/OfflineMapManager.html#stop--">stop</a></span>()</code>
<div class="block">停止离线地图下载。</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/offlinemap/OfflineMapManager.html#updateOfflineCityByCode-java.lang.String-">updateOfflineCityByCode</a></span>(java.lang.String&nbsp;citycode)</code>
<div class="block">判断传入的城市（城市编码）是否有更新的离线数据包。</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/offlinemap/OfflineMapManager.html#updateOfflineCityByName-java.lang.String-">updateOfflineCityByName</a></span>(java.lang.String&nbsp;cityname)</code>
<div class="block">判断传入的城市（城市名称）是否有更新的离线数据包。</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/offlinemap/OfflineMapManager.html#updateOfflineMapProvinceByName-java.lang.String-">updateOfflineMapProvinceByName</a></span>(java.lang.String&nbsp;name)</code>
<div class="block">判断传入的省份名称是否有更新的离线数据包。</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="OfflineMapManager-android.content.Context-com.amap.api.maps.offlinemap.OfflineMapManager.OfflineMapDownloadListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OfflineMapManager</h4>
<pre>public&nbsp;OfflineMapManager(android.content.Context&nbsp;context,
                         <a href="../../../../../com/amap/api/maps/offlinemap/OfflineMapManager.OfflineMapDownloadListener.html" title="com.amap.api.maps.offlinemap中的接口">OfflineMapManager.OfflineMapDownloadListener</a>&nbsp;listener)
                  throws java.lang.Exception</pre>
<div class="block">根据给定的参数来构造OfflineMapManager对象。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>context</code> - Context对象。</dd>
<dd><code>listener</code> - 下载事件监听。</dd>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code>java.lang.Exception</code></dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.1</dd>
</dl>
</li>
</ul>
<a name="OfflineMapManager-android.content.Context-com.amap.api.maps.offlinemap.OfflineMapManager.OfflineMapDownloadListener-com.amap.api.maps.AMap-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>OfflineMapManager</h4>
<pre>public&nbsp;OfflineMapManager(android.content.Context&nbsp;context,
                         <a href="../../../../../com/amap/api/maps/offlinemap/OfflineMapManager.OfflineMapDownloadListener.html" title="com.amap.api.maps.offlinemap中的接口">OfflineMapManager.OfflineMapDownloadListener</a>&nbsp;listener,
                         <a href="../../../../../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a>&nbsp;amap)</pre>
<div class="block">根据给定的参数来构造OfflineMapManager对象。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>context</code> - Context对象。</dd>
<dd><code>listener</code> - 下载事件监听。</dd>
<dd><code>amap</code> - 当离线下载和地图在同一个页面，或者离线地图是后台下载时需要传入这个对象
                 可以保证缓存和离线数据不冲突，而且可以使得离线地图下载可直接使用。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.3.1</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="downloadByCityCode-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>downloadByCityCode</h4>
<pre>public&nbsp;void&nbsp;downloadByCityCode(java.lang.String&nbsp;citycode)
                        throws <a href="../../../../../com/amap/api/maps/AMapException.html" title="com.amap.api.maps中的类">AMapException</a></pre>
<div class="block">根据给定的城市编码下载该城市的离线地图包<br>
 异步方法，如果有注册OfflineMapDownloadListener监听，下载状态会回调onDownload方法</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>citycode</code> - 城市编码</dd>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code><a href="../../../../../com/amap/api/maps/AMapException.html" title="com.amap.api.maps中的类">AMapException</a></code></dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.5.1</dd>
</dl>
</li>
</ul>
<a name="downloadByCityName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>downloadByCityName</h4>
<pre>public&nbsp;void&nbsp;downloadByCityName(java.lang.String&nbsp;cityname)
                        throws <a href="../../../../../com/amap/api/maps/AMapException.html" title="com.amap.api.maps中的类">AMapException</a></pre>
<div class="block">根据给定的城市名称下载该城市的离线地图包<br>
 异步方法，如果有注册OfflineMapDownloadListener监听，下载状态会回调onDownload方法</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>cityname</code> - 城市名称</dd>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code><a href="../../../../../com/amap/api/maps/AMapException.html" title="com.amap.api.maps中的类">AMapException</a></code></dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.5.1</dd>
</dl>
</li>
</ul>
<a name="downloadByProvinceName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>downloadByProvinceName</h4>
<pre>public&nbsp;void&nbsp;downloadByProvinceName(java.lang.String&nbsp;name)
                            throws <a href="../../../../../com/amap/api/maps/AMapException.html" title="com.amap.api.maps中的类">AMapException</a></pre>
<div class="block">异步方法，如果有注册OfflineMapDownloadListener监听，下载状态会回调onDownload方法。<br>
 根据省份名称下载一个省份的离线地图包。<br>
 省份离线地图包为改省下各个城市离线数据的合集</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>name</code> - 省份名称</dd>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code><a href="../../../../../com/amap/api/maps/AMapException.html" title="com.amap.api.maps中的类">AMapException</a></code></dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.5.1</dd>
</dl>
</li>
</ul>
<a name="remove-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>remove</h4>
<pre>public&nbsp;void&nbsp;remove(java.lang.String&nbsp;name)</pre>
<div class="block">根据给定的城市名称删除该城市的离线地图包。<br>
 可以删除通过OfflineManager下载的数据;<br>
 如果传入参数是省份，则会删除该省份以下的所有城市<br>
 异步方法，删除操作会比较耗时，删除状态会在
 <a href="../../../../../com/amap/api/maps/offlinemap/OfflineMapManager.OfflineMapDownloadListener.html#onRemove-boolean-java.lang.String-java.lang.String-"><code>OfflineMapManager.OfflineMapDownloadListener.onRemove(boolean, String, String)</code></a> (boolean, String)} 中进行回调返回</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>name</code> - 城市或省份的名称</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="getOfflineMapProvinceList--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getOfflineMapProvinceList</h4>
<pre>public&nbsp;java.util.ArrayList&lt;<a href="../../../../../com/amap/api/maps/offlinemap/OfflineMapProvince.html" title="com.amap.api.maps.offlinemap中的类">OfflineMapProvince</a>&gt;&nbsp;getOfflineMapProvinceList()</pre>
<div class="block">获取所有存在有离线地图的省的列表。<br>
 同步方法</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>ArrayList<OfflineMapProvince></dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="getItemByCityCode-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getItemByCityCode</h4>
<pre>public&nbsp;<a href="../../../../../com/amap/api/maps/offlinemap/OfflineMapCity.html" title="com.amap.api.maps.offlinemap中的类">OfflineMapCity</a>&nbsp;getItemByCityCode(java.lang.String&nbsp;cityCode)</pre>
<div class="block">根据城市编码获取OfflineMapCity对象<br>
 同步方法</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>cityCode</code> - 想查找城市编码</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>返回对应的OfflineMapCity对象</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="getItemByCityName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getItemByCityName</h4>
<pre>public&nbsp;<a href="../../../../../com/amap/api/maps/offlinemap/OfflineMapCity.html" title="com.amap.api.maps.offlinemap中的类">OfflineMapCity</a>&nbsp;getItemByCityName(java.lang.String&nbsp;cityName)</pre>
<div class="block">根据城市名称获取OfflneMapCity对象<br>
 同步方法</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>cityName</code> - 想查找城市名称</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>返回对应的OfflneMapCity对象</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="getItemByProvinceName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getItemByProvinceName</h4>
<pre>public&nbsp;<a href="../../../../../com/amap/api/maps/offlinemap/OfflineMapProvince.html" title="com.amap.api.maps.offlinemap中的类">OfflineMapProvince</a>&nbsp;getItemByProvinceName(java.lang.String&nbsp;provinceName)</pre>
<div class="block">根据省份名称获取OfflineMapProvince对象<br>
 同步方法</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>provinceName</code> - 省份名称</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>返回对应的OfflineMapProvince对象</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="getOfflineMapCityList--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getOfflineMapCityList</h4>
<pre>public&nbsp;java.util.ArrayList&lt;<a href="../../../../../com/amap/api/maps/offlinemap/OfflineMapCity.html" title="com.amap.api.maps.offlinemap中的类">OfflineMapCity</a>&gt;&nbsp;getOfflineMapCityList()</pre>
<div class="block">获取所有存在有离线地图的城市列表。<br></div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>ArrayList<OfflineMapCity></dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="getDownloadingCityList--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDownloadingCityList</h4>
<pre>public&nbsp;java.util.ArrayList&lt;<a href="../../../../../com/amap/api/maps/offlinemap/OfflineMapCity.html" title="com.amap.api.maps.offlinemap中的类">OfflineMapCity</a>&gt;&nbsp;getDownloadingCityList()</pre>
<div class="block">所有正在下载或等待下载离线地图的城市列表。<br></div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>ArrayList<OfflineMapCity></dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="getDownloadingProvinceList--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDownloadingProvinceList</h4>
<pre>public&nbsp;java.util.ArrayList&lt;<a href="../../../../../com/amap/api/maps/offlinemap/OfflineMapProvince.html" title="com.amap.api.maps.offlinemap中的类">OfflineMapProvince</a>&gt;&nbsp;getDownloadingProvinceList()</pre>
<div class="block">所有正在下载或等待下载离线地图的省份列表。<br>
 同步方法</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>ArrayList<OfflineMapProvince></dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="getDownloadOfflineMapCityList--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDownloadOfflineMapCityList</h4>
<pre>public&nbsp;java.util.ArrayList&lt;<a href="../../../../../com/amap/api/maps/offlinemap/OfflineMapCity.html" title="com.amap.api.maps.offlinemap中的类">OfflineMapCity</a>&gt;&nbsp;getDownloadOfflineMapCityList()</pre>
<div class="block">返回已经下载完成离线地图的城市列表。<br></div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>List<OfflineMapCity></dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="getDownloadOfflineMapProvinceList--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDownloadOfflineMapProvinceList</h4>
<pre>public&nbsp;java.util.ArrayList&lt;<a href="../../../../../com/amap/api/maps/offlinemap/OfflineMapProvince.html" title="com.amap.api.maps.offlinemap中的类">OfflineMapProvince</a>&gt;&nbsp;getDownloadOfflineMapProvinceList()</pre>
<div class="block">返回已经下载完成离线地图的省份列表。<br></div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>ArrayList<OfflineMapProvince></dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
</dl>
</li>
</ul>
<a name="updateOfflineCityByCode-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>updateOfflineCityByCode</h4>
<pre>public&nbsp;void&nbsp;updateOfflineCityByCode(java.lang.String&nbsp;citycode)
                             throws <a href="../../../../../com/amap/api/maps/AMapException.html" title="com.amap.api.maps中的类">AMapException</a></pre>
<div class="block">判断传入的城市（城市编码）是否有更新的离线数据包。<br>
 异步方法，会回调<a href="../../../../../com/amap/api/maps/offlinemap/OfflineMapManager.OfflineMapDownloadListener.html#onCheckUpdate-boolean-java.lang.String-"><code>OfflineMapManager.OfflineMapDownloadListener.onCheckUpdate(boolean, String)</code></a>
 方法</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>citycode</code> - 城市编码</dd>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code><a href="../../../../../com/amap/api/maps/AMapException.html" title="com.amap.api.maps中的类">AMapException</a></code> - 会根据cityCode去找城市，如果找不到就就会抛异常</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.5.1</dd>
</dl>
</li>
</ul>
<a name="updateOfflineCityByName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>updateOfflineCityByName</h4>
<pre>public&nbsp;void&nbsp;updateOfflineCityByName(java.lang.String&nbsp;cityname)
                             throws <a href="../../../../../com/amap/api/maps/AMapException.html" title="com.amap.api.maps中的类">AMapException</a></pre>
<div class="block">判断传入的城市（城市名称）是否有更新的离线数据包。 异步方法，会回调
 <a href="../../../../../com/amap/api/maps/offlinemap/OfflineMapManager.OfflineMapDownloadListener.html#onCheckUpdate-boolean-java.lang.String-"><code>OfflineMapManager.OfflineMapDownloadListener.onCheckUpdate(boolean, String)</code></a>方法</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>cityname</code> - 城市名称</dd>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code><a href="../../../../../com/amap/api/maps/AMapException.html" title="com.amap.api.maps中的类">AMapException</a></code> - 如果名称错误，不存在该城市，会返回无效的参数异常</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.5.1</dd>
</dl>
</li>
</ul>
<a name="updateOfflineMapProvinceByName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>updateOfflineMapProvinceByName</h4>
<pre>public&nbsp;void&nbsp;updateOfflineMapProvinceByName(java.lang.String&nbsp;name)
                                    throws <a href="../../../../../com/amap/api/maps/AMapException.html" title="com.amap.api.maps中的类">AMapException</a></pre>
<div class="block">判断传入的省份名称是否有更新的离线数据包。<br>
 异步方法，会回调<a href="../../../../../com/amap/api/maps/offlinemap/OfflineMapManager.OfflineMapDownloadListener.html#onCheckUpdate-boolean-java.lang.String-"><code>OfflineMapManager.OfflineMapDownloadListener.onCheckUpdate(boolean, String)</code></a>
 方法</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>name</code> - 省份名称</dd>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code><a href="../../../../../com/amap/api/maps/AMapException.html" title="com.amap.api.maps中的类">AMapException</a></code> - 如果名称错误，不存在该省份，会返回无效的参数异常</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.5.1</dd>
</dl>
</li>
</ul>
<a name="restart--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>restart</h4>
<pre>public&nbsp;void&nbsp;restart()</pre>
<div class="block">重新开始任务,开始下载队列中的第一个为等待中的任务</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.1</dd>
</dl>
</li>
</ul>
<a name="stop--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>stop</h4>
<pre>public&nbsp;void&nbsp;stop()</pre>
<div class="block">停止离线地图下载。<br>
 暂停所有下载城市或省份，包括队列中的。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.1</dd>
</dl>
</li>
</ul>
<a name="pause--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>pause</h4>
<pre>public&nbsp;void&nbsp;pause()</pre>
<div class="block">暂停离线地图下载。<br>
 暂停正在下载城市或省份，不包括队列中。<br>
 每次开启下载任务，都会存放在队列中，且只能同时下载一个城市或省份的离线数据</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.1</dd>
</dl>
</li>
</ul>
<a name="pauseByName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>pauseByName</h4>
<pre>public&nbsp;void&nbsp;pauseByName(java.lang.String&nbsp;name)</pre>
</li>
</ul>
<a name="destroy--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>destroy</h4>
<pre>public&nbsp;void&nbsp;destroy()</pre>
<div class="block">销毁offlineManager中的资源</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.5.1</dd>
</dl>
</li>
</ul>
<a name="setOnOfflineLoadedListener-com.amap.api.maps.offlinemap.OfflineMapManager.OfflineLoadedListener-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setOnOfflineLoadedListener</h4>
<pre>public&nbsp;void&nbsp;setOnOfflineLoadedListener(<a href="../../../../../com/amap/api/maps/offlinemap/OfflineMapManager.OfflineLoadedListener.html" title="com.amap.api.maps.offlinemap中的接口">OfflineMapManager.OfflineLoadedListener</a>&nbsp;listener)</pre>
<div class="block">设置离线地图城市列表初始化回调。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>listener</code> - 监听</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>4.1.3</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/OfflineMapManager.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/maps/offlinemap/OfflineMapCity.html" title="com.amap.api.maps.offlinemap中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/amap/api/maps/offlinemap/OfflineMapManager.OfflineLoadedListener.html" title="com.amap.api.maps.offlinemap中的接口"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/maps/offlinemap/OfflineMapManager.html" target="_top">框架</a></li>
<li><a href="OfflineMapManager.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li><a href="#nested.class.summary">嵌套</a>&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
