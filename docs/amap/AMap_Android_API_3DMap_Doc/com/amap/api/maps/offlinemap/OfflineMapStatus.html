<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>OfflineMapStatus</title>
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="OfflineMapStatus";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/OfflineMapStatus.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/maps/offlinemap/OfflineMapProvince.html" title="com.amap.api.maps.offlinemap中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/amap/api/maps/offlinemap/Province.html" title="com.amap.api.maps.offlinemap中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/maps/offlinemap/OfflineMapStatus.html" target="_top">框架</a></li>
<li><a href="OfflineMapStatus.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li><a href="#field.summary">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#methods.inherited.from.class.java.lang.Object">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li><a href="#field.detail">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li>方法</li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.amap.api.maps.offlinemap</div>
<h2 title="类 OfflineMapStatus" class="title">类 OfflineMapStatus</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.amap.api.maps.offlinemap.OfflineMapStatus</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">OfflineMapStatus</span>
extends java.lang.Object</pre>
<div class="block">地图下载的相关状态码。<br>
 <p/>
 状态说明<br></div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0.1</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>字段概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="字段概要表, 列表字段和解释">
<caption><span>字段</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">字段和说明</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/offlinemap/OfflineMapStatus.html#CHECKUPDATES">CHECKUPDATES</a></span></code>
<div class="block">默认状态</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/offlinemap/OfflineMapStatus.html#ERROR">ERROR</a></span></code>
<div class="block">解压失败错误，数据有可能有问题，所以重新下载</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/offlinemap/OfflineMapStatus.html#EXCEPTION_AMAP">EXCEPTION_AMAP</a></span></code>
<div class="block">AMap认证等异常，请检查key，下次还可以继续下载</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/offlinemap/OfflineMapStatus.html#EXCEPTION_NETWORK_LOADING">EXCEPTION_NETWORK_LOADING</a></span></code>
<div class="block">下载过程中网络问题，不属于错误，下次还可以继续下载</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/offlinemap/OfflineMapStatus.html#EXCEPTION_SDCARD">EXCEPTION_SDCARD</a></span></code>
<div class="block">SD卡读写异常,下载过程有写入文件，解压过程也有写入文件<br>
 即出现IOexception</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/offlinemap/OfflineMapStatus.html#LOADING">LOADING</a></span></code>
<div class="block">正在下载。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/offlinemap/OfflineMapStatus.html#NEW_VERSION">NEW_VERSION</a></span></code>
<div class="block">有更新，对于已下载的城市出现，线上出现新版本的时候会出现此状态。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/offlinemap/OfflineMapStatus.html#PAUSE">PAUSE</a></span></code>
<div class="block">暂停下载。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/offlinemap/OfflineMapStatus.html#START_DOWNLOAD_FAILD">START_DOWNLOAD_FAILD</a></span></code>
<div class="block">开始下载失败，已下载该城市地图</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/offlinemap/OfflineMapStatus.html#STOP">STOP</a></span></code>
<div class="block">停止下载。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/offlinemap/OfflineMapStatus.html#SUCCESS">SUCCESS</a></span></code>
<div class="block">下载成功。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/offlinemap/OfflineMapStatus.html#UNZIP">UNZIP</a></span></code>
<div class="block">正在解压。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/offlinemap/OfflineMapStatus.html#WAITING">WAITING</a></span></code>
<div class="block">等待下载。</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/amap/api/maps/offlinemap/OfflineMapStatus.html#OfflineMapStatus--">OfflineMapStatus</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>字段详细资料</h3>
<a name="CHECKUPDATES">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CHECKUPDATES</h4>
<pre>public static final&nbsp;int CHECKUPDATES</pre>
<div class="block">默认状态</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.maps.offlinemap.OfflineMapStatus.CHECKUPDATES">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="ERROR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ERROR</h4>
<pre>public static final&nbsp;int ERROR</pre>
<div class="block">解压失败错误，数据有可能有问题，所以重新下载</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.maps.offlinemap.OfflineMapStatus.ERROR">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="STOP">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>STOP</h4>
<pre>public static final&nbsp;int STOP</pre>
<div class="block">停止下载。<br>
 该状态不会出现</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.maps.offlinemap.OfflineMapStatus.STOP">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="LOADING">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LOADING</h4>
<pre>public static final&nbsp;int LOADING</pre>
<div class="block">正在下载。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.maps.offlinemap.OfflineMapStatus.LOADING">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="UNZIP">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UNZIP</h4>
<pre>public static final&nbsp;int UNZIP</pre>
<div class="block">正在解压。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.maps.offlinemap.OfflineMapStatus.UNZIP">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="WAITING">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>WAITING</h4>
<pre>public static final&nbsp;int WAITING</pre>
<div class="block">等待下载。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.maps.offlinemap.OfflineMapStatus.WAITING">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="PAUSE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PAUSE</h4>
<pre>public static final&nbsp;int PAUSE</pre>
<div class="block">暂停下载。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.maps.offlinemap.OfflineMapStatus.PAUSE">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="SUCCESS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SUCCESS</h4>
<pre>public static final&nbsp;int SUCCESS</pre>
<div class="block">下载成功。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.1.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.maps.offlinemap.OfflineMapStatus.SUCCESS">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="NEW_VERSION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NEW_VERSION</h4>
<pre>public static final&nbsp;int NEW_VERSION</pre>
<div class="block">有更新，对于已下载的城市出现，线上出现新版本的时候会出现此状态。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.2.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.maps.offlinemap.OfflineMapStatus.NEW_VERSION">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="EXCEPTION_NETWORK_LOADING">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>EXCEPTION_NETWORK_LOADING</h4>
<pre>public static final&nbsp;int EXCEPTION_NETWORK_LOADING</pre>
<div class="block">下载过程中网络问题，不属于错误，下次还可以继续下载</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.5.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.maps.offlinemap.OfflineMapStatus.EXCEPTION_NETWORK_LOADING">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="EXCEPTION_AMAP">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>EXCEPTION_AMAP</h4>
<pre>public static final&nbsp;int EXCEPTION_AMAP</pre>
<div class="block">AMap认证等异常，请检查key，下次还可以继续下载</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.5.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.maps.offlinemap.OfflineMapStatus.EXCEPTION_AMAP">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="EXCEPTION_SDCARD">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>EXCEPTION_SDCARD</h4>
<pre>public static final&nbsp;int EXCEPTION_SDCARD</pre>
<div class="block">SD卡读写异常,下载过程有写入文件，解压过程也有写入文件<br>
 即出现IOexception</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.5.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.maps.offlinemap.OfflineMapStatus.EXCEPTION_SDCARD">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="START_DOWNLOAD_FAILD">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>START_DOWNLOAD_FAILD</h4>
<pre>public static final&nbsp;int START_DOWNLOAD_FAILD</pre>
<div class="block">开始下载失败，已下载该城市地图</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.5.1</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.amap.api.maps.offlinemap.OfflineMapStatus.START_DOWNLOAD_FAILD">常量字段值</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="OfflineMapStatus--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>OfflineMapStatus</h4>
<pre>public&nbsp;OfflineMapStatus()</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/OfflineMapStatus.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/amap/api/maps/offlinemap/OfflineMapProvince.html" title="com.amap.api.maps.offlinemap中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/amap/api/maps/offlinemap/Province.html" title="com.amap.api.maps.offlinemap中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/amap/api/maps/offlinemap/OfflineMapStatus.html" target="_top">框架</a></li>
<li><a href="OfflineMapStatus.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li><a href="#field.summary">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#methods.inherited.from.class.java.lang.Object">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li><a href="#field.detail">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li>方法</li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
