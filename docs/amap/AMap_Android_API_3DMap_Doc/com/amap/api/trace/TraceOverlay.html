<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>TraceOverlay</title>
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="TraceOverlay";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10};
var tabs = {65535:["t0","所有方法"],2:["t2","实例方法"],8:["t4","具体方法"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/TraceOverlay.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/amap/api/trace/TraceLocation.html" title="com.amap.api.trace中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../com/amap/api/trace/TraceStatusListener.html" title="com.amap.api.trace中的接口"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/amap/api/trace/TraceOverlay.html" target="_top">框架</a></li>
<li><a href="TraceOverlay.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li><a href="#field.summary">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li><a href="#field.detail">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.amap.api.trace</div>
<h2 title="类 TraceOverlay" class="title">类 TraceOverlay</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.amap.api.trace.TraceOverlay</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">TraceOverlay</span>
extends java.lang.Object</pre>
<div class="block">用于绘制轨迹纠偏接口回调的一条平滑轨迹</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.4.0</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>字段概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="字段概要表, 列表字段和解释">
<caption><span>字段</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">字段和说明</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/trace/TraceOverlay.html#TRACE_STATUS_FAILURE">TRACE_STATUS_FAILURE</a></span></code>
<div class="block">描述轨迹绘制状态，失败</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/trace/TraceOverlay.html#TRACE_STATUS_FINISH">TRACE_STATUS_FINISH</a></span></code>
<div class="block">描述轨迹绘制状态，绘制完成</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/trace/TraceOverlay.html#TRACE_STATUS_PREPARE">TRACE_STATUS_PREPARE</a></span></code>
<div class="block">描述轨迹绘制状态，准备（默认状态）</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/trace/TraceOverlay.html#TRACE_STATUS_PROCESSING">TRACE_STATUS_PROCESSING</a></span></code>
<div class="block">描述轨迹绘制状态，绘制过程中</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/amap/api/trace/TraceOverlay.html#TraceOverlay-com.amap.api.maps.AMap-">TraceOverlay</a></span>(<a href="../../../../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a>&nbsp;amap)</code>
<div class="block">构造方法</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/amap/api/trace/TraceOverlay.html#TraceOverlay-com.amap.api.maps.AMap-java.util.List-">TraceOverlay</a></span>(<a href="../../../../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a>&nbsp;amap,
            java.util.List&lt;<a href="../../../../com/amap/api/maps/model/LatLng.html" title="com.amap.api.maps.model中的类">LatLng</a>&gt;&nbsp;lines)</code>
<div class="block">构造方法</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>所有方法</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">实例方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">具体方法</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/trace/TraceOverlay.html#add-java.util.List-">add</a></span>(java.util.List&lt;<a href="../../../../com/amap/api/maps/model/LatLng.html" title="com.amap.api.maps.model中的类">LatLng</a>&gt;&nbsp;segments)</code>
<div class="block">添加轨迹回调的一段，需要按照index顺序添加才能正常绘制</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/trace/TraceOverlay.html#getDistance--">getDistance</a></span>()</code>
<div class="block">获取线路行驶距离</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/trace/TraceOverlay.html#getTraceStatus--">getTraceStatus</a></span>()</code>
<div class="block">获取轨迹绘制状态</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/trace/TraceOverlay.html#getWaitTime--">getWaitTime</a></span>()</code>
<div class="block">获取线路停车时间</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/trace/TraceOverlay.html#remove--">remove</a></span>()</code>
<div class="block">移除一条轨迹线路</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/trace/TraceOverlay.html#setDistance-int-">setDistance</a></span>(int&nbsp;mDistance)</code>
<div class="block">设置线路行驶距离</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/trace/TraceOverlay.html#setProperCamera-java.util.List-">setProperCamera</a></span>(java.util.List&lt;<a href="../../../../com/amap/api/maps/model/LatLng.html" title="com.amap.api.maps.model中的类">LatLng</a>&gt;&nbsp;lists)</code>
<div class="block">根据传入的点集合设置合适的Camera</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/trace/TraceOverlay.html#setTraceStatus-int-">setTraceStatus</a></span>(int&nbsp;mTraceStatus)</code>
<div class="block">设置轨迹绘制状态</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/trace/TraceOverlay.html#setWaitTime-int-">setWaitTime</a></span>(int&nbsp;mWaitTime)</code>
<div class="block">设置线路停车时间</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/trace/TraceOverlay.html#zoopToSpan--">zoopToSpan</a></span>()</code>
<div class="block">设置当前合适显示Camera</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>字段详细资料</h3>
<a name="TRACE_STATUS_PROCESSING">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TRACE_STATUS_PROCESSING</h4>
<pre>public static final&nbsp;int TRACE_STATUS_PROCESSING</pre>
<div class="block">描述轨迹绘制状态，绘制过程中</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.4.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.trace.TraceOverlay.TRACE_STATUS_PROCESSING">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="TRACE_STATUS_FINISH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TRACE_STATUS_FINISH</h4>
<pre>public static final&nbsp;int TRACE_STATUS_FINISH</pre>
<div class="block">描述轨迹绘制状态，绘制完成</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.4.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.trace.TraceOverlay.TRACE_STATUS_FINISH">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="TRACE_STATUS_FAILURE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TRACE_STATUS_FAILURE</h4>
<pre>public static final&nbsp;int TRACE_STATUS_FAILURE</pre>
<div class="block">描述轨迹绘制状态，失败</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.4.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.trace.TraceOverlay.TRACE_STATUS_FAILURE">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="TRACE_STATUS_PREPARE">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>TRACE_STATUS_PREPARE</h4>
<pre>public static final&nbsp;int TRACE_STATUS_PREPARE</pre>
<div class="block">描述轨迹绘制状态，准备（默认状态）</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.4.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.trace.TraceOverlay.TRACE_STATUS_PREPARE">常量字段值</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="TraceOverlay-com.amap.api.maps.AMap-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TraceOverlay</h4>
<pre>public&nbsp;TraceOverlay(<a href="../../../../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a>&nbsp;amap,
                    java.util.List&lt;<a href="../../../../com/amap/api/maps/model/LatLng.html" title="com.amap.api.maps.model中的类">LatLng</a>&gt;&nbsp;lines)</pre>
<div class="block">构造方法</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>amap</code> - AMap对象</dd>
<dd><code>lines</code> - 一条轨迹或一条轨迹第一段</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.4.0</dd>
</dl>
</li>
</ul>
<a name="TraceOverlay-com.amap.api.maps.AMap-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>TraceOverlay</h4>
<pre>public&nbsp;TraceOverlay(<a href="../../../../com/amap/api/maps/AMap.html" title="com.amap.api.maps中的类">AMap</a>&nbsp;amap)</pre>
<div class="block">构造方法</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>amap</code> - AMap对象</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.4.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="add-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>add</h4>
<pre>public&nbsp;void&nbsp;add(java.util.List&lt;<a href="../../../../com/amap/api/maps/model/LatLng.html" title="com.amap.api.maps.model中的类">LatLng</a>&gt;&nbsp;segments)</pre>
<div class="block">添加轨迹回调的一段，需要按照index顺序添加才能正常绘制</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>segments</code> - 一段经纬度信息</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.4.0</dd>
</dl>
</li>
</ul>
<a name="remove--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>remove</h4>
<pre>public&nbsp;void&nbsp;remove()</pre>
<div class="block">移除一条轨迹线路</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.4.0</dd>
</dl>
</li>
</ul>
<a name="setProperCamera-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setProperCamera</h4>
<pre>public&nbsp;void&nbsp;setProperCamera(java.util.List&lt;<a href="../../../../com/amap/api/maps/model/LatLng.html" title="com.amap.api.maps.model中的类">LatLng</a>&gt;&nbsp;lists)</pre>
<div class="block">根据传入的点集合设置合适的Camera</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>lists</code> - 点集合</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.4.0</dd>
</dl>
</li>
</ul>
<a name="zoopToSpan--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>zoopToSpan</h4>
<pre>public&nbsp;void&nbsp;zoopToSpan()</pre>
<div class="block">设置当前合适显示Camera</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.4.0</dd>
</dl>
</li>
</ul>
<a name="getTraceStatus--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTraceStatus</h4>
<pre>public&nbsp;int&nbsp;getTraceStatus()</pre>
<div class="block">获取轨迹绘制状态</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>返回轨迹绘制状态</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.4.0</dd>
</dl>
</li>
</ul>
<a name="setTraceStatus-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTraceStatus</h4>
<pre>public&nbsp;void&nbsp;setTraceStatus(int&nbsp;mTraceStatus)</pre>
<div class="block">设置轨迹绘制状态</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>mTraceStatus</code> - 设置轨迹绘制状态</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.4.0</dd>
</dl>
</li>
</ul>
<a name="getDistance--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDistance</h4>
<pre>public&nbsp;int&nbsp;getDistance()</pre>
<div class="block">获取线路行驶距离</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>返回线路行驶距离</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.4.0</dd>
</dl>
</li>
</ul>
<a name="setDistance-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDistance</h4>
<pre>public&nbsp;void&nbsp;setDistance(int&nbsp;mDistance)</pre>
<div class="block">设置线路行驶距离</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>mDistance</code> - 线路行驶距离</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.4.0</dd>
</dl>
</li>
</ul>
<a name="getWaitTime--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWaitTime</h4>
<pre>public&nbsp;int&nbsp;getWaitTime()</pre>
<div class="block">获取线路停车时间</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>停车时间</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.4.0</dd>
</dl>
</li>
</ul>
<a name="setWaitTime-int-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setWaitTime</h4>
<pre>public&nbsp;void&nbsp;setWaitTime(int&nbsp;mWaitTime)</pre>
<div class="block">设置线路停车时间</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>mWaitTime</code> - 线路停车时间</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.4.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/TraceOverlay.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/amap/api/trace/TraceLocation.html" title="com.amap.api.trace中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../com/amap/api/trace/TraceStatusListener.html" title="com.amap.api.trace中的接口"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/amap/api/trace/TraceOverlay.html" target="_top">框架</a></li>
<li><a href="TraceOverlay.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li><a href="#field.summary">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li><a href="#field.detail">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
