<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>LBSTraceClient</title>
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="LBSTraceClient";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":9,"i2":10,"i3":10,"i4":10};
var tabs = {65535:["t0","所有方法"],1:["t1","静态方法"],2:["t2","实例方法"],8:["t4","具体方法"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/LBSTraceClient.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个类</li>
<li><a href="../../../../com/amap/api/trace/TraceListener.html" title="com.amap.api.trace中的接口"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/amap/api/trace/LBSTraceClient.html" target="_top">框架</a></li>
<li><a href="LBSTraceClient.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li><a href="#field.summary">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li><a href="#field.detail">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.amap.api.trace</div>
<h2 title="类 LBSTraceClient" class="title">类 LBSTraceClient</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.amap.api.trace.LBSTraceClient</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">LBSTraceClient</span>
extends java.lang.Object</pre>
<div class="block">轨迹纠偏类，对行车GPS高精度定位轨迹集合纠偏</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.4.0</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>字段概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="字段概要表, 列表字段和解释">
<caption><span>字段</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">字段和说明</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/trace/LBSTraceClient.html#LOCATE_TIMEOUT_ERROR">LOCATE_TIMEOUT_ERROR</a></span></code>
<div class="block">轨迹纠偏错误信息，定位超时</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/trace/LBSTraceClient.html#MIN_GRASP_POINT_ERROR">MIN_GRASP_POINT_ERROR</a></span></code>
<div class="block">轨迹纠偏错误信息，轨迹点太少或距离太近,轨迹纠偏失败</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/trace/LBSTraceClient.html#TRACE_SUCCESS">TRACE_SUCCESS</a></span></code>
<div class="block">轨迹纠偏错误信息，纠偏运行正常</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/trace/LBSTraceClient.html#TYPE_AMAP">TYPE_AMAP</a></span></code>
<div class="block">用于<a href="../../../../com/amap/api/trace/LBSTraceClient.html#queryProcessedTrace-int-java.util.List-int-com.amap.api.trace.TraceListener-"><code>LBSTraceClient.queryProcessedTrace(int, List, int, TraceListener)</code></a> type 坐标系类型为高德</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/trace/LBSTraceClient.html#TYPE_BAIDU">TYPE_BAIDU</a></span></code>
<div class="block">用于<a href="../../../../com/amap/api/trace/LBSTraceClient.html#queryProcessedTrace-int-java.util.List-int-com.amap.api.trace.TraceListener-"><code>LBSTraceClient.queryProcessedTrace(int, List, int, TraceListener)</code></a> type 坐标系类型为百度</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/trace/LBSTraceClient.html#TYPE_GPS">TYPE_GPS</a></span></code>
<div class="block">用于<a href="../../../../com/amap/api/trace/LBSTraceClient.html#queryProcessedTrace-int-java.util.List-int-com.amap.api.trace.TraceListener-"><code>LBSTraceClient.queryProcessedTrace(int, List, int, TraceListener)</code></a> type 坐标系类型为GPS原始坐标</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/amap/api/trace/LBSTraceClient.html#LBSTraceClient-android.content.Context-">LBSTraceClient</a></span>(android.content.Context&nbsp;context)</code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;
<div class="block"><span class="deprecationComment">使用 {<a href="../../../../com/amap/api/trace/LBSTraceClient.html#getInstance-android.content.Context-"><code>LBSTraceClient.getInstance(Context)</code></a>}</span></div>
</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>所有方法</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">静态方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">实例方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">具体方法</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/trace/LBSTraceClient.html#destroy--">destroy</a></span>()</code>
<div class="block">销毁LBSTraceClient</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/amap/api/trace/LBSTraceClient.html" title="com.amap.api.trace中的类">LBSTraceClient</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/trace/LBSTraceClient.html#getInstance-android.content.Context-">getInstance</a></span>(android.content.Context&nbsp;context)</code>
<div class="block">获取LBSTraceClient</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/trace/LBSTraceClient.html#queryProcessedTrace-int-java.util.List-int-com.amap.api.trace.TraceListener-">queryProcessedTrace</a></span>(int&nbsp;lineID,
                   java.util.List&lt;<a href="../../../../com/amap/api/trace/TraceLocation.html" title="com.amap.api.trace中的类">TraceLocation</a>&gt;&nbsp;locations,
                   int&nbsp;type,
                   <a href="../../../../com/amap/api/trace/TraceListener.html" title="com.amap.api.trace中的接口">TraceListener</a>&nbsp;listener)</code>&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/trace/LBSTraceClient.html#startTrace-com.amap.api.trace.TraceStatusListener-">startTrace</a></span>(<a href="../../../../com/amap/api/trace/TraceStatusListener.html" title="com.amap.api.trace中的接口">TraceStatusListener</a>&nbsp;traceStatusListener)</code>
<div class="block">开始记录轨迹，定位间隔2s，每隔5个点合并请求一次纠偏并回调。</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/trace/LBSTraceClient.html#stopTrace--">stopTrace</a></span>()</code>
<div class="block">停止记录轨迹</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>字段详细资料</h3>
<a name="TYPE_AMAP">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TYPE_AMAP</h4>
<pre>public static final&nbsp;int TYPE_AMAP</pre>
<div class="block">用于<a href="../../../../com/amap/api/trace/LBSTraceClient.html#queryProcessedTrace-int-java.util.List-int-com.amap.api.trace.TraceListener-"><code>LBSTraceClient.queryProcessedTrace(int, List, int, TraceListener)</code></a> type 坐标系类型为高德</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.4.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.trace.LBSTraceClient.TYPE_AMAP">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="TYPE_GPS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TYPE_GPS</h4>
<pre>public static final&nbsp;int TYPE_GPS</pre>
<div class="block">用于<a href="../../../../com/amap/api/trace/LBSTraceClient.html#queryProcessedTrace-int-java.util.List-int-com.amap.api.trace.TraceListener-"><code>LBSTraceClient.queryProcessedTrace(int, List, int, TraceListener)</code></a> type 坐标系类型为GPS原始坐标</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.4.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.trace.LBSTraceClient.TYPE_GPS">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="TYPE_BAIDU">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TYPE_BAIDU</h4>
<pre>public static final&nbsp;int TYPE_BAIDU</pre>
<div class="block">用于<a href="../../../../com/amap/api/trace/LBSTraceClient.html#queryProcessedTrace-int-java.util.List-int-com.amap.api.trace.TraceListener-"><code>LBSTraceClient.queryProcessedTrace(int, List, int, TraceListener)</code></a> type 坐标系类型为百度</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.4.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.trace.LBSTraceClient.TYPE_BAIDU">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="MIN_GRASP_POINT_ERROR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MIN_GRASP_POINT_ERROR</h4>
<pre>public static final&nbsp;java.lang.String MIN_GRASP_POINT_ERROR</pre>
<div class="block">轨迹纠偏错误信息，轨迹点太少或距离太近,轨迹纠偏失败</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>5.2.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.trace.LBSTraceClient.MIN_GRASP_POINT_ERROR">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="LOCATE_TIMEOUT_ERROR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LOCATE_TIMEOUT_ERROR</h4>
<pre>public static final&nbsp;java.lang.String LOCATE_TIMEOUT_ERROR</pre>
<div class="block">轨迹纠偏错误信息，定位超时</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>5.2.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.trace.LBSTraceClient.LOCATE_TIMEOUT_ERROR">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="TRACE_SUCCESS">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>TRACE_SUCCESS</h4>
<pre>public static final&nbsp;java.lang.String TRACE_SUCCESS</pre>
<div class="block">轨迹纠偏错误信息，纠偏运行正常</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>5.2.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.trace.LBSTraceClient.TRACE_SUCCESS">常量字段值</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="LBSTraceClient-android.content.Context-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>LBSTraceClient</h4>
<pre>public&nbsp;LBSTraceClient(android.content.Context&nbsp;context)
               throws java.lang.Exception</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;<span class="deprecationComment">使用 {<a href="../../../../com/amap/api/trace/LBSTraceClient.html#getInstance-android.content.Context-"><code>LBSTraceClient.getInstance(Context)</code></a>}</span></div>
<div class="block">轨迹纠偏构造函数</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>context</code> - context对象，不能为空。</dd>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code>java.lang.Exception</code></dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.4.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="getInstance-android.content.Context-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getInstance</h4>
<pre>public static&nbsp;<a href="../../../../com/amap/api/trace/LBSTraceClient.html" title="com.amap.api.trace中的类">LBSTraceClient</a>&nbsp;getInstance(android.content.Context&nbsp;context)
                                  throws java.lang.Exception</pre>
<div class="block">获取LBSTraceClient</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>context</code> - context对象，不能为空。</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>返回LBSTraceClient对象</dd>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code>java.lang.Exception</code></dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>4.1.3</dd>
</dl>
</li>
</ul>
<a name="queryProcessedTrace-int-java.util.List-int-com.amap.api.trace.TraceListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>queryProcessedTrace</h4>
<pre>public&nbsp;void&nbsp;queryProcessedTrace(int&nbsp;lineID,
                                java.util.List&lt;<a href="../../../../com/amap/api/trace/TraceLocation.html" title="com.amap.api.trace中的类">TraceLocation</a>&gt;&nbsp;locations,
                                int&nbsp;type,
                                <a href="../../../../com/amap/api/trace/TraceListener.html" title="com.amap.api.trace中的接口">TraceListener</a>&nbsp;listener)</pre>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>lineID</code> - 用于标示一条轨迹，支持多轨迹纠偏，如果多条轨迹调起纠偏接口，则lineID需不同</dd>
<dd><code>locations</code> - 一条轨迹的点集合，目前支持该点集合为一条行车GPS高精度定位轨迹</dd>
<dd><code>type</code> - 轨迹坐标系，目前支持高德 <a href="../../../../com/amap/api/trace/LBSTraceClient.html#TYPE_AMAP"><code>LBSTraceClient.TYPE_AMAP</code></a>;GPS
            <a href="../../../../com/amap/api/trace/LBSTraceClient.html#TYPE_GPS"><code>LBSTraceClient.TYPE_GPS</code></a>TYPE_GPS;百度 <a href="../../../../com/amap/api/trace/LBSTraceClient.html#TYPE_BAIDU"><code>LBSTraceClient.TYPE_BAIDU</code></a></dd>
<dd><code>listener</code> - 轨迹纠偏回调</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.4.0</dd>
</dl>
</li>
</ul>
<a name="startTrace-com.amap.api.trace.TraceStatusListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>startTrace</h4>
<pre>public&nbsp;void&nbsp;startTrace(<a href="../../../../com/amap/api/trace/TraceStatusListener.html" title="com.amap.api.trace中的接口">TraceStatusListener</a>&nbsp;traceStatusListener)</pre>
<div class="block">开始记录轨迹，定位间隔2s，每隔5个点合并请求一次纠偏并回调。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>traceStatusListener</code> - 回调监听</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>5.2.0</dd>
</dl>
</li>
</ul>
<a name="stopTrace--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>stopTrace</h4>
<pre>public&nbsp;void&nbsp;stopTrace()</pre>
<div class="block">停止记录轨迹</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>5.2.0</dd>
</dl>
</li>
</ul>
<a name="destroy--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>destroy</h4>
<pre>public&nbsp;void&nbsp;destroy()</pre>
<div class="block">销毁LBSTraceClient</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>5.0.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/LBSTraceClient.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个类</li>
<li><a href="../../../../com/amap/api/trace/TraceListener.html" title="com.amap.api.trace中的接口"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/amap/api/trace/LBSTraceClient.html" target="_top">框架</a></li>
<li><a href="LBSTraceClient.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li><a href="#field.summary">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li><a href="#field.detail">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
