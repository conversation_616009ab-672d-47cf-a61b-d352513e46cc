<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>TraceLocation</title>
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="TraceLocation";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10};
var tabs = {65535:["t0","所有方法"],2:["t2","实例方法"],8:["t4","具体方法"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/TraceLocation.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/amap/api/trace/TraceListener.html" title="com.amap.api.trace中的接口"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../com/amap/api/trace/TraceOverlay.html" title="com.amap.api.trace中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/amap/api/trace/TraceLocation.html" target="_top">框架</a></li>
<li><a href="TraceLocation.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.amap.api.trace</div>
<h2 title="类 TraceLocation" class="title">类 TraceLocation</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.amap.api.trace.TraceLocation</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">TraceLocation</span>
extends java.lang.Object</pre>
<div class="block">用于轨迹纠偏的一个点的信息，目前该点需要：经度、纬度、速度、方向角（和gps返回一致）、定位时间</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>V3.4.0</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/amap/api/trace/TraceLocation.html#TraceLocation--">TraceLocation</a></span>()</code>
<div class="block">默认构造方法</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/amap/api/trace/TraceLocation.html#TraceLocation-double-double-float-float-long-">TraceLocation</a></span>(double&nbsp;latitude,
             double&nbsp;longitude,
             float&nbsp;speed,
             float&nbsp;bearing,
             long&nbsp;time)</code>
<div class="block">构造方法</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>所有方法</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">实例方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">具体方法</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../../../com/amap/api/trace/TraceLocation.html" title="com.amap.api.trace中的类">TraceLocation</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/trace/TraceLocation.html#copy--">copy</a></span>()</code>
<div class="block">复制轨迹纠偏点</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/trace/TraceLocation.html#getBearing--">getBearing</a></span>()</code>
<div class="block">获取方向角</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/trace/TraceLocation.html#getLatitude--">getLatitude</a></span>()</code>
<div class="block">获取纬度</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/trace/TraceLocation.html#getLongitude--">getLongitude</a></span>()</code>
<div class="block">获取经度</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/trace/TraceLocation.html#getSpeed--">getSpeed</a></span>()</code>
<div class="block">获取速度</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/trace/TraceLocation.html#getTime--">getTime</a></span>()</code>
<div class="block">获取定位时间</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/trace/TraceLocation.html#setBearing-float-">setBearing</a></span>(float&nbsp;mBearing)</code>
<div class="block">设置方向角</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/trace/TraceLocation.html#setLatitude-double-">setLatitude</a></span>(double&nbsp;mLatitude)</code>
<div class="block">设置纬度</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/trace/TraceLocation.html#setLongitude-double-">setLongitude</a></span>(double&nbsp;mLongitude)</code>
<div class="block">设置经度</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/trace/TraceLocation.html#setSpeed-float-">setSpeed</a></span>(float&nbsp;mSpeed)</code>
<div class="block">设置速度</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/trace/TraceLocation.html#setTime-long-">setTime</a></span>(long&nbsp;mTime)</code>
<div class="block">设置定位时间</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="TraceLocation-double-double-float-float-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TraceLocation</h4>
<pre>public&nbsp;TraceLocation(double&nbsp;latitude,
                     double&nbsp;longitude,
                     float&nbsp;speed,
                     float&nbsp;bearing,
                     long&nbsp;time)</pre>
<div class="block">构造方法</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>latitude</code> - 纬度</dd>
<dd><code>longitude</code> - 经度</dd>
<dd><code>speed</code> - 速度 单位m/s</dd>
<dd><code>bearing</code> - 方向角 单位:度</dd>
<dd><code>time</code> - 定位时间</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.4.0</dd>
</dl>
</li>
</ul>
<a name="TraceLocation--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>TraceLocation</h4>
<pre>public&nbsp;TraceLocation()</pre>
<div class="block">默认构造方法</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.4.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="getLatitude--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLatitude</h4>
<pre>public&nbsp;double&nbsp;getLatitude()</pre>
<div class="block">获取纬度</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>纬度</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.4.0</dd>
</dl>
</li>
</ul>
<a name="setLatitude-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLatitude</h4>
<pre>public&nbsp;void&nbsp;setLatitude(double&nbsp;mLatitude)</pre>
<div class="block">设置纬度</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>mLatitude</code> - 纬度</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.4.0</dd>
</dl>
</li>
</ul>
<a name="getLongitude--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLongitude</h4>
<pre>public&nbsp;double&nbsp;getLongitude()</pre>
<div class="block">获取经度</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>经度</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.4.0</dd>
</dl>
</li>
</ul>
<a name="setLongitude-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLongitude</h4>
<pre>public&nbsp;void&nbsp;setLongitude(double&nbsp;mLongitude)</pre>
<div class="block">设置经度</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>mLongitude</code> - 经度</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.4.0</dd>
</dl>
</li>
</ul>
<a name="getSpeed--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSpeed</h4>
<pre>public&nbsp;float&nbsp;getSpeed()</pre>
<div class="block">获取速度</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>速度 单位m/s</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.4.0</dd>
</dl>
</li>
</ul>
<a name="setSpeed-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSpeed</h4>
<pre>public&nbsp;void&nbsp;setSpeed(float&nbsp;mSpeed)</pre>
<div class="block">设置速度</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>mSpeed</code> - 速度 单位m/s</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.4.0</dd>
</dl>
</li>
</ul>
<a name="getBearing--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBearing</h4>
<pre>public&nbsp;float&nbsp;getBearing()</pre>
<div class="block">获取方向角</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>方向角 定义同GPS bearing</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.4.0</dd>
</dl>
</li>
</ul>
<a name="setBearing-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBearing</h4>
<pre>public&nbsp;void&nbsp;setBearing(float&nbsp;mBearing)</pre>
<div class="block">设置方向角</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>mBearing</code> - 方向角 定义同GPS bearing</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.4.0</dd>
</dl>
</li>
</ul>
<a name="getTime--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTime</h4>
<pre>public&nbsp;long&nbsp;getTime()</pre>
<div class="block">获取定位时间</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>定位时间</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.4.0</dd>
</dl>
</li>
</ul>
<a name="setTime-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTime</h4>
<pre>public&nbsp;void&nbsp;setTime(long&nbsp;mTime)</pre>
<div class="block">设置定位时间</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>mTime</code> - 定位时间</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.4.0</dd>
</dl>
</li>
</ul>
<a name="copy--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>copy</h4>
<pre>public&nbsp;<a href="../../../../com/amap/api/trace/TraceLocation.html" title="com.amap.api.trace中的类">TraceLocation</a>&nbsp;copy()</pre>
<div class="block">复制轨迹纠偏点</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>轨迹纠偏点</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.4.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/TraceLocation.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/amap/api/trace/TraceListener.html" title="com.amap.api.trace中的接口"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../com/amap/api/trace/TraceOverlay.html" title="com.amap.api.trace中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/amap/api/trace/TraceLocation.html" target="_top">框架</a></li>
<li><a href="TraceLocation.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
