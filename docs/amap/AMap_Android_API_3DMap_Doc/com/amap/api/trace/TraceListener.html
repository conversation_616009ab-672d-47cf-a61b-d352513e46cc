<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>TraceListener</title>
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="TraceListener";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":6,"i1":6,"i2":6};
var tabs = {65535:["t0","所有方法"],2:["t2","实例方法"],4:["t3","抽象方法"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/TraceListener.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/amap/api/trace/LBSTraceClient.html" title="com.amap.api.trace中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../com/amap/api/trace/TraceLocation.html" title="com.amap.api.trace中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/amap/api/trace/TraceListener.html" target="_top">框架</a></li>
<li><a href="TraceListener.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.amap.api.trace</div>
<h2 title="接口 TraceListener" class="title">接口 TraceListener</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public interface <span class="typeNameLabel">TraceListener</span></pre>
<div class="block">轨迹纠偏回调</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.4.0</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>所有方法</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">实例方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">抽象方法</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/trace/TraceListener.html#onFinished-int-java.util.List-int-int-">onFinished</a></span>(int&nbsp;lineID,
          java.util.List&lt;<a href="../../../../com/amap/api/maps/model/LatLng.html" title="com.amap.api.maps.model中的类">LatLng</a>&gt;&nbsp;linepoints,
          int&nbsp;distance,
          int&nbsp;waitingtime)</code>
<div class="block">轨迹纠偏成功回调</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/trace/TraceListener.html#onRequestFailed-int-java.lang.String-">onRequestFailed</a></span>(int&nbsp;lineID,
               java.lang.String&nbsp;errorInfo)</code>
<div class="block">轨迹纠偏失败回调</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/trace/TraceListener.html#onTraceProcessing-int-int-java.util.List-">onTraceProcessing</a></span>(int&nbsp;lineID,
                 int&nbsp;index,
                 java.util.List&lt;<a href="../../../../com/amap/api/maps/model/LatLng.html" title="com.amap.api.maps.model中的类">LatLng</a>&gt;&nbsp;segments)</code>
<div class="block">轨迹纠偏过程回调，一条轨迹分割为多个段，按索引顺序回调其中一段</div>
</td>
</tr>
</table>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="onRequestFailed-int-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onRequestFailed</h4>
<pre>void&nbsp;onRequestFailed(int&nbsp;lineID,
                     java.lang.String&nbsp;errorInfo)</pre>
<div class="block">轨迹纠偏失败回调</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>lineID</code> - 用于标示一条轨迹，支持多轨迹纠偏，如果多条轨迹调起纠偏接口，则lineID需不同</dd>
<dd><code>errorInfo</code> - 轨迹纠偏失败原因</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.4.0</dd>
</dl>
</li>
</ul>
<a name="onTraceProcessing-int-int-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onTraceProcessing</h4>
<pre>void&nbsp;onTraceProcessing(int&nbsp;lineID,
                       int&nbsp;index,
                       java.util.List&lt;<a href="../../../../com/amap/api/maps/model/LatLng.html" title="com.amap.api.maps.model中的类">LatLng</a>&gt;&nbsp;segments)</pre>
<div class="block">轨迹纠偏过程回调，一条轨迹分割为多个段，按索引顺序回调其中一段</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>lineID</code> - 用于标示一条轨迹，支持多轨迹纠偏，如果多条轨迹调起纠偏接口，则lineID需不同</dd>
<dd><code>index</code> - 一条轨迹分割为多个段,标示当前轨迹段索引</dd>
<dd><code>segments</code> - 一条轨迹分割为多个段，segments标示当前轨迹段经过纠偏后经纬度点集合</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.4.0</dd>
</dl>
</li>
</ul>
<a name="onFinished-int-java.util.List-int-int-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>onFinished</h4>
<pre>void&nbsp;onFinished(int&nbsp;lineID,
                java.util.List&lt;<a href="../../../../com/amap/api/maps/model/LatLng.html" title="com.amap.api.maps.model中的类">LatLng</a>&gt;&nbsp;linepoints,
                int&nbsp;distance,
                int&nbsp;waitingtime)</pre>
<div class="block">轨迹纠偏成功回调</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>lineID</code> - 用于标示一条轨迹，支持多轨迹纠偏，如果多条轨迹调起纠偏接口，则lineID需不同</dd>
<dd><code>linepoints</code> - 整条轨迹经过纠偏后点的经纬度集合</dd>
<dd><code>distance</code> - 该轨迹经过纠偏后总距离，单位米</dd>
<dd><code>waitingtime</code> - 该轨迹中间停止时间，以GPS速度为参考，单位秒</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>3.4.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/TraceListener.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/amap/api/trace/LBSTraceClient.html" title="com.amap.api.trace中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../com/amap/api/trace/TraceLocation.html" title="com.amap.api.trace中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/amap/api/trace/TraceListener.html" target="_top">框架</a></li>
<li><a href="TraceListener.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
