<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>AMapException</title>
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="AMapException";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10};
var tabs = {65535:["t0","所有方法"],2:["t2","实例方法"],8:["t4","具体方法"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/AMapException.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/amap/api/maps2d/AMap.OnMyLocationChangeListener.html" title="com.amap.api.maps2d中的接口"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../com/amap/api/maps2d/AMapOptions.html" title="com.amap.api.maps2d中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/amap/api/maps2d/AMapException.html" target="_top">框架</a></li>
<li><a href="AMapException.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li><a href="#field.summary">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li><a href="#field.detail">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.amap.api.maps2d</div>
<h2 title="类 AMapException" class="title">类 AMapException</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>java.lang.Throwable</li>
<li>
<ul class="inheritance">
<li>java.lang.Exception</li>
<li>
<ul class="inheritance">
<li>com.amap.api.maps2d.AMapException</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>所有已实现的接口:</dt>
<dd>java.io.Serializable</dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">AMapException</span>
extends java.lang.Exception</pre>
<div class="block">异常信息类。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>V2.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../serialized-form.html#com.amap.api.maps2d.AMapException">序列化表格</a></dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>字段概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="字段概要表, 列表字段和解释">
<caption><span>字段</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">字段和说明</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps2d/AMapException.html#AMAP_NOT_SUPPORT">AMAP_NOT_SUPPORT</a></span></code>
<div class="block">移动设备上未安装高德地图或高德地图版本较旧</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps2d/AMapException.html#AMAP_NOT_SUPPORT_NAVI">AMAP_NOT_SUPPORT_NAVI</a></span></code>
<div class="block">移动设备上未安装高德地图或高德地图版本较旧</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps2d/AMapException.html#ERROR_CONNECTION">ERROR_CONNECTION</a></span></code>
<div class="block">http连接失败。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps2d/AMapException.html#ERROR_FAILURE_AUTH">ERROR_FAILURE_AUTH</a></span></code>
<div class="block">key鉴权失败。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps2d/AMapException.html#ERROR_ILLEGAL_VALUE">ERROR_ILLEGAL_VALUE</a></span></code>
<div class="block">非法坐标值</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps2d/AMapException.html#ERROR_INVALID_PARAMETER">ERROR_INVALID_PARAMETER</a></span></code>
<div class="block">无效的参数。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps2d/AMapException.html#ERROR_IO">ERROR_IO</a></span></code>
<div class="block">输入输出异常。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps2d/AMapException.html#ERROR_NULL_PARAMETER">ERROR_NULL_PARAMETER</a></span></code>
<div class="block">空指针异常。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps2d/AMapException.html#ERROR_PROTOCOL">ERROR_PROTOCOL</a></span></code>
<div class="block">协议解析错误。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps2d/AMapException.html#ERROR_SOCKE_TIME_OUT">ERROR_SOCKE_TIME_OUT</a></span></code>
<div class="block">socket 连接超时。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps2d/AMapException.html#ERROR_SOCKET">ERROR_SOCKET</a></span></code>
<div class="block">socket 连接异常。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps2d/AMapException.html#ERROR_UNKNOW_HOST">ERROR_UNKNOW_HOST</a></span></code>
<div class="block">未知主机。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps2d/AMapException.html#ERROR_UNKNOW_SERVICE">ERROR_UNKNOW_SERVICE</a></span></code>
<div class="block">服务器连接失败。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps2d/AMapException.html#ERROR_UNKNOWN">ERROR_UNKNOWN</a></span></code>
<div class="block">未知的错误。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps2d/AMapException.html#ERROR_URL">ERROR_URL</a></span></code>
<div class="block">url异常</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps2d/AMapException.html#ILLEGAL_AMAP_ARGUMENT">ILLEGAL_AMAP_ARGUMENT</a></span></code>
<div class="block">非法 参数</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps2d/AMapException.html#ILLEGAL_NAVI_ARGUMENT">ILLEGAL_NAVI_ARGUMENT</a></span></code>
<div class="block">非法导航参数</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps2d/AMapException.html#AMapException--">AMapException</a></span>()</code>
<div class="block">构造定位异常对象。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps2d/AMapException.html#AMapException-java.lang.String-">AMapException</a></span>(java.lang.String&nbsp;errorMessage)</code>
<div class="block">异常对象。</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>所有方法</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">实例方法</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">具体方法</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/amap/api/maps2d/AMapException.html#getErrorMessage--">getErrorMessage</a></span>()</code>
<div class="block">获取异常信息。</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Throwable">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Throwable</h3>
<code>addSuppressed, fillInStackTrace, getCause, getLocalizedMessage, getMessage, getStackTrace, getSuppressed, initCause, printStackTrace, printStackTrace, printStackTrace, setStackTrace, toString</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>字段详细资料</h3>
<a name="ERROR_IO">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ERROR_IO</h4>
<pre>public static final&nbsp;java.lang.String ERROR_IO</pre>
<div class="block">输入输出异常。</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.maps2d.AMapException.ERROR_IO">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="ERROR_SOCKET">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ERROR_SOCKET</h4>
<pre>public static final&nbsp;java.lang.String ERROR_SOCKET</pre>
<div class="block">socket 连接异常。</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.maps2d.AMapException.ERROR_SOCKET">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="ERROR_SOCKE_TIME_OUT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ERROR_SOCKE_TIME_OUT</h4>
<pre>public static final&nbsp;java.lang.String ERROR_SOCKE_TIME_OUT</pre>
<div class="block">socket 连接超时。</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.maps2d.AMapException.ERROR_SOCKE_TIME_OUT">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="ERROR_ILLEGAL_VALUE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ERROR_ILLEGAL_VALUE</h4>
<pre>public static final&nbsp;java.lang.String ERROR_ILLEGAL_VALUE</pre>
<div class="block">非法坐标值</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.maps2d.AMapException.ERROR_ILLEGAL_VALUE">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="ERROR_INVALID_PARAMETER">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ERROR_INVALID_PARAMETER</h4>
<pre>public static final&nbsp;java.lang.String ERROR_INVALID_PARAMETER</pre>
<div class="block">无效的参数。</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.maps2d.AMapException.ERROR_INVALID_PARAMETER">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="ERROR_NULL_PARAMETER">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ERROR_NULL_PARAMETER</h4>
<pre>public static final&nbsp;java.lang.String ERROR_NULL_PARAMETER</pre>
<div class="block">空指针异常。</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.maps2d.AMapException.ERROR_NULL_PARAMETER">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="ERROR_URL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ERROR_URL</h4>
<pre>public static final&nbsp;java.lang.String ERROR_URL</pre>
<div class="block">url异常</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.maps2d.AMapException.ERROR_URL">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="ERROR_UNKNOW_HOST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ERROR_UNKNOW_HOST</h4>
<pre>public static final&nbsp;java.lang.String ERROR_UNKNOW_HOST</pre>
<div class="block">未知主机。</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.maps2d.AMapException.ERROR_UNKNOW_HOST">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="ERROR_UNKNOW_SERVICE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ERROR_UNKNOW_SERVICE</h4>
<pre>public static final&nbsp;java.lang.String ERROR_UNKNOW_SERVICE</pre>
<div class="block">服务器连接失败。</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.maps2d.AMapException.ERROR_UNKNOW_SERVICE">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="ERROR_PROTOCOL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ERROR_PROTOCOL</h4>
<pre>public static final&nbsp;java.lang.String ERROR_PROTOCOL</pre>
<div class="block">协议解析错误。</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.maps2d.AMapException.ERROR_PROTOCOL">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="ERROR_CONNECTION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ERROR_CONNECTION</h4>
<pre>public static final&nbsp;java.lang.String ERROR_CONNECTION</pre>
<div class="block">http连接失败。</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.maps2d.AMapException.ERROR_CONNECTION">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="ERROR_UNKNOWN">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ERROR_UNKNOWN</h4>
<pre>public static final&nbsp;java.lang.String ERROR_UNKNOWN</pre>
<div class="block">未知的错误。</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.maps2d.AMapException.ERROR_UNKNOWN">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="ERROR_FAILURE_AUTH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ERROR_FAILURE_AUTH</h4>
<pre>public static final&nbsp;java.lang.String ERROR_FAILURE_AUTH</pre>
<div class="block">key鉴权失败。</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.maps2d.AMapException.ERROR_FAILURE_AUTH">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="AMAP_NOT_SUPPORT_NAVI">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>AMAP_NOT_SUPPORT_NAVI</h4>
<pre>public static final&nbsp;java.lang.String AMAP_NOT_SUPPORT_NAVI</pre>
<div class="block">移动设备上未安装高德地图或高德地图版本较旧</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.maps2d.AMapException.AMAP_NOT_SUPPORT_NAVI">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="AMAP_NOT_SUPPORT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>AMAP_NOT_SUPPORT</h4>
<pre>public static final&nbsp;java.lang.String AMAP_NOT_SUPPORT</pre>
<div class="block">移动设备上未安装高德地图或高德地图版本较旧</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.maps2d.AMapException.AMAP_NOT_SUPPORT">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="ILLEGAL_AMAP_ARGUMENT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ILLEGAL_AMAP_ARGUMENT</h4>
<pre>public static final&nbsp;java.lang.String ILLEGAL_AMAP_ARGUMENT</pre>
<div class="block">非法 参数</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.maps2d.AMapException.ILLEGAL_AMAP_ARGUMENT">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="ILLEGAL_NAVI_ARGUMENT">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>ILLEGAL_NAVI_ARGUMENT</h4>
<pre>public static final&nbsp;java.lang.String ILLEGAL_NAVI_ARGUMENT</pre>
<div class="block">非法导航参数</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../constant-values.html#com.amap.api.maps2d.AMapException.ILLEGAL_NAVI_ARGUMENT">常量字段值</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="AMapException-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>AMapException</h4>
<pre>public&nbsp;AMapException(java.lang.String&nbsp;errorMessage)</pre>
<div class="block">异常对象。</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>errorMessage</code> - 输入的异常信息。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>V2.0</dd>
</dl>
</li>
</ul>
<a name="AMapException--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>AMapException</h4>
<pre>public&nbsp;AMapException()</pre>
<div class="block">构造定位异常对象。</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>V2.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="getErrorMessage--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getErrorMessage</h4>
<pre>public&nbsp;java.lang.String&nbsp;getErrorMessage()</pre>
<div class="block">获取异常信息。</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>异常信息。</dd>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>V2.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="class-use/AMapException.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/amap/api/maps2d/AMap.OnMyLocationChangeListener.html" title="com.amap.api.maps2d中的接口"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../com/amap/api/maps2d/AMapOptions.html" title="com.amap.api.maps2d中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/amap/api/maps2d/AMapException.html" target="_top">框架</a></li>
<li><a href="AMapException.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li><a href="#field.summary">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li><a href="#field.detail">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
