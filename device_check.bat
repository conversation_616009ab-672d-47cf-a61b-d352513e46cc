@echo off
echo ========================================
echo    Android设备连接检查和应用安装
echo ========================================
echo.

echo 1. 检查ADB连接...
adb devices
if %errorlevel% neq 0 (
    echo ❌ ADB未找到或未正确配置
    echo 请确保Android SDK已安装并配置PATH环境变量
    pause
    exit /b 1
)

echo.
echo 2. 检查连接的设备...
for /f "tokens=1" %%i in ('adb devices ^| findstr /v "List"') do (
    if not "%%i"=="" (
        echo ✅ 发现设备: %%i
        set DEVICE_FOUND=1
    )
)

if not defined DEVICE_FOUND (
    echo ❌ 未发现连接的设备
    echo 请确保:
    echo   - 设备已通过USB连接到电脑
    echo   - 设备已开启USB调试模式
    echo   - 已允许此电脑的USB调试权限
    pause
    exit /b 1
)

echo.
echo 3. 检查APK文件...
if exist "app\build\outputs\apk\debug\app-debug.apk" (
    echo ✅ APK文件存在
    for %%A in ("app\build\outputs\apk\debug\app-debug.apk") do (
        echo    文件大小: %%~zA 字节
        echo    修改时间: %%~tA
    )
) else (
    echo ❌ APK文件不存在
    echo 请先运行编译命令: gradlew assembleDebug
    pause
    exit /b 1
)

echo.
echo 4. 检查设备信息...
echo 设备型号:
adb shell getprop ro.product.model
echo Android版本:
adb shell getprop ro.build.version.release
echo API级别:
adb shell getprop ro.build.version.sdk
echo 可用存储空间:
adb shell df /data | findstr /data

echo.
echo 5. 检查是否已安装旧版本...
adb shell pm list packages | findstr com.vere.likes
if %errorlevel% equ 0 (
    echo ⚠️  检测到已安装的版本
    set /p UNINSTALL="是否卸载旧版本? (y/n): "
    if /i "%UNINSTALL%"=="y" (
        echo 卸载旧版本...
        adb uninstall com.vere.likes
        if %errorlevel% equ 0 (
            echo ✅ 旧版本卸载成功
        ) else (
            echo ❌ 旧版本卸载失败
        )
    )
)

echo.
echo 6. 安装应用...
set /p INSTALL="是否安装应用? (y/n): "
if /i "%INSTALL%"=="y" (
    echo 正在安装APK...
    adb install app\build\outputs\apk\debug\app-debug.apk
    if %errorlevel% equ 0 (
        echo ✅ 应用安装成功!
        echo.
        set /p LAUNCH="是否启动应用? (y/n): "
        if /i "%LAUNCH%"=="y" (
            echo 启动应用...
            adb shell am start -n com.vere.likes/.MainActivity
            if %errorlevel% equ 0 (
                echo ✅ 应用启动成功!
                echo.
                echo 📱 应用已在设备上运行
                echo 请在设备上测试各项功能
            ) else (
                echo ❌ 应用启动失败
            )
        )
    ) else (
        echo ❌ 应用安装失败
        echo 可能的原因:
        echo   - 设备存储空间不足
        echo   - 签名冲突
        echo   - 权限问题
    )
)

echo.
echo 7. 实用命令提示...
echo 查看应用日志: adb logcat ^| findstr com.vere.likes
echo 清除应用数据: adb shell pm clear com.vere.likes
echo 卸载应用: adb uninstall com.vere.likes
echo 查看内存使用: adb shell dumpsys meminfo com.vere.likes

echo.
echo ========================================
echo           设备检查完成
echo ========================================
pause
