# 批量修复所有ArrowBack引用问题
Write-Host "开始批量修复ArrowBack引用问题..." -ForegroundColor Green

$filesToFix = @(
    "app\src\main\java\com\vere\likes\view\compose\screen\BankCardInfoEditScreen.kt",
    "app\src\main\java\com\vere\likes\view\compose\screen\CalendarScreen.kt",
    "app\src\main\java\com\vere\likes\view\compose\screen\GoalManagementScreen.kt",
    "app\src\main\java\com\vere\likes\view\compose\screen\HierarchicalPlanningScreen.kt",
    "app\src\main\java\com\vere\likes\view\compose\screen\HierarchicalTimelineScreen.kt",
    "app\src\main\java\com\vere\likes\view\compose\screen\MemoAddScreen.kt",
    "app\src\main\java\com\vere\likes\view\compose\screen\MemoDetailScreen.kt",
    "app\src\main\java\com\vere\likes\view\compose\screen\MemoEditScreen.kt",
    "app\src\main\java\com\vere\likes\view\compose\screen\PlanningSettingsScreen.kt",
    "app\src\main\java\com\vere\likes\view\compose\screen\ReminderSettingsScreen.kt",
    "app\src\main\java\com\vere\likes\view\compose\screen\TaskManagementScreen.kt"
)

$fixedCount = 0

foreach ($file in $filesToFix) {
    if (Test-Path $file) {
        Write-Host "修复文件: $file" -ForegroundColor Yellow
        
        $content = Get-Content $file -Raw -Encoding UTF8
        $originalContent = $content
        
        # 1. 添加AutoMirrored import（如果不存在）
        if ($content -notmatch "import androidx\.compose\.material\.icons\.Icons\.AutoMirrored") {
            $content = $content -replace "(import androidx\.compose\.material\.icons\.Icons\s*\n)", "`$1import androidx.compose.material.icons.Icons.AutoMirrored`n"
        }
        
        # 2. 修复引用方式
        $content = $content -replace "\bArrowBack\b", "Icons.AutoMirrored.Filled.ArrowBack"
        
        if ($content -ne $originalContent) {
            Set-Content $file $content -Encoding UTF8 -NoNewline
            Write-Host "✅ 已修复 $file" -ForegroundColor Green
            $fixedCount++
        } else {
            Write-Host "ℹ️ $file 无需修复" -ForegroundColor Cyan
        }
    } else {
        Write-Host "❌ 文件不存在: $file" -ForegroundColor Red
    }
}

Write-Host "`n=== 修复完成 ===" -ForegroundColor Green
Write-Host "修复的文件数: $fixedCount" -ForegroundColor Yellow
