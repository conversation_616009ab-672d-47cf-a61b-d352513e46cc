# PowerShell脚本：修复编译错误
Write-Host "开始修复编译错误..." -ForegroundColor Green

# 1. 修复PersonInfoPresenter.kt中的类型推断问题
Write-Host "修复PersonInfoPresenter.kt..." -ForegroundColor Yellow
$personInfoFile = "app\src\main\java\com\vere\likes\presenter\PersonInfoPresenter.kt"
if (Test-Path $personInfoFile) {
    $content = Get-Content $personInfoFile -Raw
    # 修复combine中的_personInfoList引用
    $content = $content -replace '_personInfoList,', 'personInfoList,'
    Set-Content $personInfoFile $content -Encoding UTF8
    Write-Host "✅ PersonInfoPresenter.kt 修复完成"
} else {
    Write-Host "❌ 找不到PersonInfoPresenter.kt文件"
}

# 2. 修复StateManagementUtils.kt中的kotlinx引用
Write-Host "修复StateManagementUtils.kt..." -ForegroundColor Yellow
$stateUtilsFile = "app\src\main\java\com\vere\likes\utils\StateManagementUtils.kt"
if (Test-Path $stateUtilsFile) {
    $content = Get-Content $stateUtilsFile -Raw
    # 添加kotlinx.coroutines导入
    if ($content -notmatch "import kotlinx.coroutines") {
        $content = $content -replace "import kotlinx.coroutines.flow", "import kotlinx.coroutines.*`nimport kotlinx.coroutines.flow"
    }
    Set-Content $stateUtilsFile $content -Encoding UTF8
    Write-Host "✅ StateManagementUtils.kt 修复完成"
} else {
    Write-Host "❌ 找不到StateManagementUtils.kt文件"
}

# 3. 修复Theme.kt中的Material 3 API问题
Write-Host "修复Theme.kt..." -ForegroundColor Yellow
$themeFile = "app\src\main\java\com\vere\likes\ui\theme\Theme.kt"
if (Test-Path $themeFile) {
    $content = Get-Content $themeFile -Raw
    # 移除不支持的surface参数
    $content = $content -replace "surfaceContainer = .*,", ""
    $content = $content -replace "surfaceContainerHigh = .*,", ""
    $content = $content -replace "surfaceContainerHighest = .*,", ""
    $content = $content -replace "surfaceContainerLow = .*,", ""
    $content = $content -replace "surfaceContainerLowest = .*,", ""
    Set-Content $themeFile $content -Encoding UTF8
    Write-Host "✅ Theme.kt 修复完成"
} else {
    Write-Host "❌ 找不到Theme.kt文件"
}

Write-Host "编译错误修复完成！" -ForegroundColor Green
Write-Host "请运行 ./gradlew assembleDebug 测试修复结果" -ForegroundColor Cyan
