# 🌈 Ink Memory (墨忆)

<div align="center">

![Ink Memory Logo](https://img.shields.io/badge/Ink%20Memory-墨忆-blue?style=for-the-badge&logo=android)
![Android](https://img.shields.io/badge/Android-3DDC84?style=for-the-badge&logo=android&logoColor=white)
![Kotlin](https://img.shields.io/badge/Kotlin-0095D5?style=for-the-badge&logo=kotlin&logoColor=white)
![Jetpack Compose](https://img.shields.io/badge/Jetpack%20Compose-4285F4?style=for-the-badge&logo=jetpackcompose&logoColor=white)

**A modern Android memo application with multi-theme support, image storage, and social-style experience**

[📱 Features](#-features) • [🎨 Themes](#-themes) • [🚀 Quick Start](#-quick-start) • [📖 User Guide](#-user-guide) • [🤝 Contributing](#-contributing)

</div>

---

## 📖 Project Introduction

Ink Memory is a modern Android memo application built with Jetpack Compose, offering rich theme options and elegant user experience. The name "Ink Memory" (墨忆) symbolizes recording beautiful memories with words, lasting like the fragrance of ink.

### ✨ Key Highlights

- 🌈 **Rainbow Theme with Social Style** - WeChat Moments-like social card design
- 🎨 **Diverse Theme System** - 5 theme modes, 3 card styles
- 🖼️ **Persistent Image Storage** - Deep copy to app private directory, no loss after restart
- 📱 **Modern Interface** - Material Design 3 + Jetpack Compose
- 🔄 **MVP Architecture** - Clean code structure, easy to maintain and extend

## 🌟 Features

### 📝 Memo Management
- ✅ Create, edit, delete memos
- ✅ Rich text editor support (lists, numbering)
- ✅ Image insertion (up to 9 images, grid layout)
- ✅ Priority settings (High, Medium, Low)
- ✅ Category tag management
- ✅ Favorite and completion status
- ✅ Reminder notification function

### 🎨 Theme System
- 🌈 **Rainbow Theme** - Social style with dynamic rainbow colors
- ⚪ **Minimal White** - Journal style, pure and clean
- 🌙 **Dark Theme** - Eye-friendly mode, night-friendly
- ☀️ **Light Theme** - Classic design, fresh and bright
- 🔄 **Follow System** - Auto-adapt to system theme

### 🎯 Card Styles
- 📋 **Standard Style** - Simple and practical traditional cards
- 💬 **Social Style** - Social media-like card design
- ✨ **Modern Style** - Beautiful cards with modern design language

### 🗂️ Information Management
- 👤 **ID Card Information** - Dedicated identity information storage
- 💳 **Bank Card Information** - Secure bank card information management
- 🔐 **Account Information** - Various account password storage
- 🔒 **Password Protection** - Password verification required for sensitive information access

### 🔍 Search & Filter
- 🔎 **Full-text Search** - Quickly find memo content
- 🏷️ **Tag Filtering** - Filter by category tags
- 📊 **Sorting Function** - Multiple sorting methods
- 📱 **Display Mode** - Memo mode/All-info mode switching

### 📤 Data Management
- 💾 **Data Export** - Support JSON format export
- 📥 **Data Import** - Restore data from files
- 🗑️ **Batch Operations** - Batch delete, mark complete
- 🔄 **Data Sync** - Local data persistent storage

## 🎨 Themes

### 🌈 Rainbow Theme - Social Style
- **Design Concept**: Mimics WeChat Moments posting style
- **Visual Features**: 8-second HSV hue cycle animation, flat design without rounded corners
- **Image Layout**: Single image large display, multi-image smart grid
- **User Experience**: Social avatar, username, Moments-style time format

### ⚪ Minimal White - Journal Style
- **Design Concept**: Pure and clean journal book style
- **Visual Features**: Hand-drawn style borders, black-white-gray color scheme
- **Layout Features**: 95% card width, one card per row
- **Use Case**: Focus on content, reduce visual distractions

### ✨ Modern Style - Material Design 3
- **Design Concept**: Modern design language
- **Visual Features**: 24dp large rounded corners, 12dp depth shadows
- **Interactive Experience**: Press scale animation, smooth state transitions
- **Complete Features**: Priority indicators, smart image grid

## 🏗️ Technical Architecture

### 📱 Development Tech Stack
- **Programming Language**: Kotlin 100%
- **UI Framework**: Jetpack Compose
- **Architecture Pattern**: MVP (Model-View-Presenter)
- **Dependency Injection**: Hilt
- **Data Storage**: SharedPreferences + Room Database
- **Image Loading**: Coil
- **Animation System**: Compose Animation

### 🗂️ Project Structure
```
app/src/main/java/com/vere/likes/
├── model/                  # Data model layer
│   ├── data/              # Data class definitions
│   └── repository/        # Data repositories
├── view/                  # View layer
│   ├── compose/           # Compose UI components
│   └── contract/          # MVP contract interfaces
├── presenter/             # Presenter layer
├── manager/               # Manager classes
├── notification/          # Notification system
└── ui/theme/             # Theme system
```

### 🔧 Core Components
- **PersistentImageManager** - Image persistence management
- **ThemeManager** - Theme state management
- **ReminderScheduler** - Reminder notification scheduling
- **ImportExportManager** - Data import/export

## 🚀 Quick Start

### 📋 Requirements
- Android Studio Hedgehog | 2023.1.1 or higher
- Android SDK API 24 (Android 7.0) or higher
- Kotlin 1.9.0 or higher
- Gradle 8.0 or higher

### 🛠️ Installation Steps

1. **Clone Repository**
   ```bash
   git clone https://gitee.com/beipiao_boy/ink-memory.git
   cd ink-memory
   ```

2. **Open Project**
   - Open the project with Android Studio
   - Wait for Gradle sync to complete

3. **Run Application**
   - Connect Android device or start emulator
   - Click run button or use shortcut `Shift + F10`

### 📦 Build APK
```bash
# Build Debug version
./gradlew assembleDebug

# Build Release version
./gradlew assembleRelease
```

## 📖 User Guide

### 🎯 Basic Operations

1. **Create Memo**
   - Click the "+" button in the bottom right corner
   - Select "Memo" type
   - Fill in title, content, select category and priority
   - Can add images and set reminder time

2. **Theme Switching**
   - Click the app title in the top left to open sidebar
   - Select "Settings" → "Appearance Settings" → "Theme Settings"
   - Choose your preferred theme mode

3. **Information Management**
   - Click "+" button to select information type
   - ID card info, bank card info, account info
   - First entry requires setting a 4-digit password

### 🔍 Advanced Features

1. **Search & Filter**
   - Click the search icon in the top right
   - Enter keywords for full-text search
   - Use tags to filter specific categories

2. **Data Management**
   - Settings → Data Management → Export Data
   - Select export location, generate JSON file
   - Import by selecting corresponding JSON file

3. **Batch Operations**
   - Long press memo card to enter selection mode
   - Select multiple items for batch delete or marking

## 🎨 Custom Configuration

### 🌈 Theme Configuration
The app supports 5 theme modes, each with unique visual style:

- **Rainbow Theme**: For users who like vibrant colors
- **Minimal White**: For users who focus on content
- **Dark Theme**: For nighttime use
- **Light Theme**: For daytime use
- **Follow System**: Auto-adapt to system settings

### 🎯 Card Styles
In non-dedicated themes, you can choose different card styles:

- **Standard Style**: Traditional Material Design cards
- **Social Style**: Social media-like card design
- **Modern Style**: Beautiful cards with modern design language

## 🤝 Contributing

We welcome all forms of contributions! Whether it's bug reports, feature suggestions, or code contributions.

### 🔧 Development Contribution

1. **Fork Repository**
   ```bash
   git clone https://gitee.com/beipiao_boy/ink-memory.git
   ```

2. **Create Feature Branch**
   ```bash
   git checkout -b feature/your-feature-name
   ```

3. **Commit Changes**
   ```bash
   git commit -m "feat: add new feature description"
   ```

4. **Push Branch**
   ```bash
   git push origin feature/your-feature-name
   ```

5. **Create Pull Request**
   - Create Pull Request on Gitee
   - Describe your changes in detail
   - Wait for code review

### 📝 Commit Convention
Please follow the commit message format:
- `feat:` New feature
- `fix:` Bug fix
- `docs:` Documentation update
- `style:` Code formatting
- `refactor:` Code refactoring
- `test:` Test related
- `chore:` Build process or auxiliary tool changes

### 🐛 Issue Reporting
If you find bugs or have feature suggestions, please:
1. Search existing issues first
2. Create new issue if none exists
3. Describe the problem or suggestion in detail
4. Provide reproduction steps (for bugs)

## 📄 License

This project is licensed under the [MIT License](LICENSE).

## 🙏 Acknowledgments

Thanks to the following open source projects and technologies:
- [Jetpack Compose](https://developer.android.com/jetpack/compose) - Modern UI toolkit
- [Material Design 3](https://m3.material.io/) - Design system
- [Hilt](https://dagger.dev/hilt/) - Dependency injection framework
- [Coil](https://coil-kt.github.io/coil/) - Image loading library

## 📞 Contact

- **Project URL**: https://gitee.com/beipiao_boy/ink-memory
- **Bug Reports**: [Issues](https://gitee.com/beipiao_boy/ink-memory/issues)
- **Feature Requests**: [Issues](https://gitee.com/beipiao_boy/ink-memory/issues)

---

<div align="center">

**If this project helps you, please give it a ⭐ Star!**

Made with ❤️ by [beipiao_boy](https://gitee.com/beipiao_boy)

</div>
