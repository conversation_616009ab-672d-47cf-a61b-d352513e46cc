# PowerShell脚本：将JPG转换为WebP格式
param(
    [string]$InputFile = "ic_launcher_custom.jpg",
    [string]$OutputFile = "ic_launcher_custom.webp",
    [int]$Quality = 90
)

Write-Host "正在转换 $InputFile 为 WebP 格式..."

try {
    # 加载System.Drawing程序集
    Add-Type -AssemblyName System.Drawing
    
    # 读取原始图像
    $image = [System.Drawing.Image]::FromFile((Resolve-Path $InputFile).Path)
    
    # 创建WebP编码器参数
    $encoderParams = New-Object System.Drawing.Imaging.EncoderParameters(1)
    $encoderParams.Param[0] = New-Object System.Drawing.Imaging.EncoderParameter([System.Drawing.Imaging.Encoder]::Quality, $Quality)
    
    # 获取WebP编码器
    $webpCodec = [System.Drawing.Imaging.ImageCodecInfo]::GetImageEncoders() | Where-Object { $_.MimeType -eq "image/webp" }
    
    if ($webpCodec) {
        # 保存为WebP
        $image.Save($OutputFile, $webpCodec, $encoderParams)
        Write-Host "转换成功: $OutputFile"
    } else {
        # 如果没有WebP编码器，保存为PNG
        $pngFile = $OutputFile -replace "\.webp$", ".png"
        $image.Save($pngFile, [System.Drawing.Imaging.ImageFormat]::Png)
        Write-Host "WebP编码器不可用，已保存为PNG: $pngFile"
    }
    
    $image.Dispose()
} catch {
    Write-Error "转换失败: $($_.Exception.Message)"
}
