--------- beginning of main
--------- beginning of kernel
--------- beginning of system
--------- beginning of crash
---------------------------- PROCESS STARTED (3364) for package com.vere.likes ----------------------------
2025-07-26 03:38:08.951  3364-3364  nativeloader            com.vere.likes                       D  Configuring clns-7 for other apk /data/app/~~epqd4ZyO0if5wOKssUqcEw==/com.vere.likes-j5hLD6DK_zEIQ96AZSiBBg==/base.apk. target_sdk_version=35, uses_libraries=, library_path=/data/app/~~epqd4ZyO0if5wOKssUqcEw==/com.vere.likes-j5hLD6DK_zEIQ96AZSiBBg==/lib/x86_64, permitted_path=/data:/mnt/expand:/data/user/0/com.vere.likes
2025-07-26 03:38:08.964  3364-3364  CompatChangeReporter    com.vere.likes                       D  Compat change id reported: 202956589; UID 10210; state: ENABLED
2025-07-26 03:38:08.988  3364-3364  GraphicsEnvironment     com.vere.likes                       V  Currently set values for:
2025-07-26 03:38:08.988  3364-3364  GraphicsEnvironment     com.vere.likes                       V    angle_gl_driver_selection_pkgs=[]
2025-07-26 03:38:08.989  3364-3364  GraphicsEnvironment     com.vere.likes                       V    angle_gl_driver_selection_values=[]
2025-07-26 03:38:08.989  3364-3364  GraphicsEnvironment     com.vere.likes                       V  Global.Settings values are invalid: number of packages: 0, number of values: 0
2025-07-26 03:38:08.990  3364-3364  GraphicsEnvironment     com.vere.likes                       V  Neither updatable production driver nor prerelease driver is supported.
2025-07-26 03:38:09.065  3364-3364  CompatChangeReporter    com.vere.likes                       D  Compat change id reported: 279646685; UID 10210; state: ENABLED
2025-07-26 03:38:09.092  3364-3364  LikesApplication        com.vere.likes                       D  应用启动，初始化事务性数据保护
2025-07-26 03:38:09.105  3364-3364  AppLifecycleObserver    com.vere.likes                       D  应用生命周期观察者已初始化
2025-07-26 03:38:09.133  3364-3380  LikesApplication        com.vere.likes                       D  开始启动时数据检查
2025-07-26 03:38:09.133  3364-3380  LikesApplication        com.vere.likes                       D  内存状态: MemoryStats(maxMemoryMB=192, totalMemoryMB=192, usedMemoryMB=3, freeMemoryMB=189, usagePercent=1)
2025-07-26 03:38:09.159  3364-3380  LikesApplication        com.vere.likes                       D  ANR风险等级: NONE
2025-07-26 03:38:09.163  3364-3380  DataRepairUtils         com.vere.likes                       D  开始检查SharedPreferences数据
2025-07-26 03:38:09.165  3364-3380  DataRepairUtils         com.vere.likes                       D  SharedPreferences数据检查完成，无问题
2025-07-26 03:38:09.169  3364-3380  LikesApplication        com.vere.likes                       D  数据统计: DataStatistics(personInfoDataSize=0, tagsDataSize=0, totalKeys=0, hasPersonInfoData=false, hasTagsData=false)
2025-07-26 03:38:09.204  3364-3380  LikesApplication        com.vere.likes                       D  事务性文件状态 - 个人信息: FileInfo(fileName=person_info_list.json, exists=false, size=0, lastModified=0, hasBackup=false, backupSize=0), 标签: FileInfo(fileName=person_tags_list.json, exists=true, size=1838, lastModified=1752341946372, hasBackup=false, backupSize=0)
2025-07-26 03:38:09.205  3364-3380  LikesApplication        com.vere.likes                       D  启动时数据检查完成
2025-07-26 03:38:09.325  3364-3364  MainActivity            com.vere.likes                       I  onCreate called
2025-07-26 03:38:09.368  3364-3364  CompatChangeReporter    com.vere.likes                       D  Compat change id reported: 309578419; UID 10210; state: ENABLED
2025-07-26 03:38:09.412  3364-3364  AppLifecycleObserver    com.vere.likes                       D  应用进入前台
2025-07-26 03:38:09.414  3364-3380  AppLifecycleObserver    com.vere.likes                       D  个人信息文件状态: FileInfo(fileName=person_info_list.json, exists=false, size=0, lastModified=0, hasBackup=false, backupSize=0)
2025-07-26 03:38:09.417  3364-3380  AppLifecycleObserver    com.vere.likes                       D  标签文件状态: FileInfo(fileName=person_tags_list.json, exists=true, size=1838, lastModified=1752341946372, hasBackup=false, backupSize=0)
2025-07-26 03:38:09.418  3364-3364  MainActivity            com.vere.likes                       I  onResume called
2025-07-26 03:38:09.444  3364-3364  HWUI                    com.vere.likes                       W  Unknown dataspace 0
2025-07-26 03:38:09.527  3364-3364  MainActivity            com.vere.likes                       I  setContent called
2025-07-26 03:38:09.591  3364-3384  EGL_emulation           com.vere.likes                       I  Opening libGLESv1_CM_emulation.so
2025-07-26 03:38:09.596  3364-3384  EGL_emulation           com.vere.likes                       I  Opening libGLESv2_emulation.so
2025-07-26 03:38:09.616  3364-3384  HWUI                    com.vere.likes                       W  Failed to choose config with EGL_SWAP_BEHAVIOR_PRESERVED, retrying without...
2025-07-26 03:38:09.616  3364-3384  HWUI                    com.vere.likes                       W  Failed to initialize 101010-2 format, error = EGL_SUCCESS
2025-07-26 03:38:09.723  3364-3384  Gralloc4                com.vere.likes                       I  mapper 4.x is not supported
2025-07-26 03:38:10.027  3364-3380  Transactio...onInfoRepo com.vere.likes                       D  开始加载所有数据
2025-07-26 03:38:10.036  3364-3380  Transactio...onInfoRepo com.vere.likes                       D  个人信息读取结果: Success
2025-07-26 03:38:10.036  3364-3380  Transactio...onInfoRepo com.vere.likes                       D  个人信息文件为空或不存在
2025-07-26 03:38:10.045  3364-3364  HomePresenter           com.vere.likes                       I  HomePresenter created with repositories: SharedPrefMemoRepositoryImpl, SharedPrefCategoryRepositoryImpl
2025-07-26 03:38:10.045  3364-3380  Transactio...onInfoRepo com.vere.likes                       D  标签读取结果: Success
2025-07-26 03:38:10.060  3364-3380  Transactio...onInfoRepo com.vere.likes                       D  加载标签成功，数量: 12
2025-07-26 03:38:10.060  3364-3380  Transactio...onInfoRepo com.vere.likes                       D  数据加载完成，个人信息: 0, 标签: 12
2025-07-26 03:38:10.069  3364-3364  StateManagementUtils    com.vere.likes                       D  PersonInfoRepository 更新: 列表大小=0
2025-07-26 03:38:10.074  3364-3364  StateManagementUtils    com.vere.likes                       D  列表 PersonInfoList 状态正常: 0 项
2025-07-26 03:38:10.074  3364-3364  StateManagementUtils    com.vere.likes                       D  状态变化 [PersonInfoPresenter]: []
2025-07-26 03:38:10.074  3364-3364  PersonInfoPresenter     com.vere.likes                       D  个人信息列表更新: 0 项
2025-07-26 03:38:10.181  3364-3364  HomeScreen              com.vere.likes                       D  Lifecycle event: ON_CREATE
2025-07-26 03:38:10.181  3364-3364  HomeScreen              com.vere.likes                       D  Lifecycle event: ON_START
2025-07-26 03:38:10.181  3364-3364  HomeScreen              com.vere.likes                       D  Lifecycle event: ON_RESUME
2025-07-26 03:38:10.182  3364-3364  HomeScreen              com.vere.likes                       D  ON_RESUME - refreshing data
2025-07-26 03:38:10.182  3364-3364  HomePresenter           com.vere.likes                       D  refreshData() called
2025-07-26 03:38:10.182  3364-3364  HomePresenter           com.vere.likes                       D  loadMemos() called
2025-07-26 03:38:10.636  3364-3364  DiagnosticUtils         com.vere.likes                       D  === 应用启动诊断开始 ===
2025-07-26 03:38:10.637  3364-3364  DiagnosticUtils         com.vere.likes                       D  --- 检查基础环境 ---
2025-07-26 03:38:10.637  3364-3364  DiagnosticUtils         com.vere.likes                       D  应用包名: com.vere.likes
2025-07-26 03:38:10.637  3364-3364  DiagnosticUtils         com.vere.likes                       D  应用版本: 1.0.0 (1)
2025-07-26 03:38:10.638  3364-3364  DiagnosticUtils         com.vere.likes                       D  应用目录: /data/user/0/com.vere.likes/files
2025-07-26 03:38:10.640  3364-3364  DiagnosticUtils         com.vere.likes                       D  应用目录存在: true
2025-07-26 03:38:10.640  3364-3364  DiagnosticUtils         com.vere.likes                       D  应用目录可写: true
2025-07-26 03:38:10.641  3364-3364  DiagnosticUtils         com.vere.likes                       D  --- 检查数据存储 ---
2025-07-26 03:38:10.641  3364-3364  DiagnosticUtils         com.vere.likes                       D  SharedPreferences可用: true
2025-07-26 03:38:10.642  3364-3364  DiagnosticUtils         com.vere.likes                       D  数据库目录存在: false
2025-07-26 03:38:10.643  3364-3364  DiagnosticUtils         com.vere.likes                       D  个人信息数据存在: false
2025-07-26 03:38:10.643  3364-3364  DiagnosticUtils         com.vere.likes                       D  --- 检查权限 ---
2025-07-26 03:38:10.644  3364-3364  DiagnosticUtils         com.vere.likes                       D  存储权限: false
2025-07-26 03:38:10.648  3364-3364  DiagnosticUtils         com.vere.likes                       D  网络权限: true
2025-07-26 03:38:10.649  3364-3364  DiagnosticUtils         com.vere.likes                       D  --- 检查主题配置 ---
2025-07-26 03:38:10.650  3364-3364  DiagnosticUtils         com.vere.likes                       D  当前主题: SYSTEM
2025-07-26 03:38:10.650  3364-3364  DiagnosticUtils         com.vere.likes                       D  动态颜色: false
2025-07-26 03:38:10.650  3364-3364  DiagnosticUtils         com.vere.likes                       D  卡片样式: STANDARD
2025-07-26 03:38:10.651  3364-3364  DiagnosticUtils         com.vere.likes                       D  === 应用启动诊断结束 ===
2025-07-26 03:38:10.652  3364-3364  DiagnosticUtils         com.vere.likes                       D  --- 检查内存使用 ---
2025-07-26 03:38:10.652  3364-3364  DiagnosticUtils         com.vere.likes                       D  最大内存: 192MB
2025-07-26 03:38:10.653  3364-3364  DiagnosticUtils         com.vere.likes                       D  总内存: 48MB
2025-07-26 03:38:10.654  3364-3364  DiagnosticUtils         com.vere.likes                       D  已用内存: 7MB
2025-07-26 03:38:10.655  3364-3364  DiagnosticUtils         com.vere.likes                       D  空闲内存: 41MB
2025-07-26 03:38:10.655  3364-3364  DiagnosticUtils         com.vere.likes                       D  内存使用率: 3%
2025-07-26 03:38:10.655  3364-3364  BlankScreenDetector     com.vere.likes                       D  正常显示: HomeScreen
2025-07-26 03:38:10.657  3364-3364  DiagnosticUtils         com.vere.likes                       D  组件渲染: HomeScreen - 可见: true
2025-07-26 03:38:10.657  3364-3364  HomePresenter           com.vere.likes                       D  loadMemos() called
2025-07-26 03:38:10.658  3364-3364  HomeScreen              com.vere.likes                       I  Navigation returned to home, refreshing data
2025-07-26 03:38:10.659  3364-3364  HomePresenter           com.vere.likes                       D  refreshData() called
2025-07-26 03:38:10.661  3364-3364  HomePresenter           com.vere.likes                       D  loadMemos() called
2025-07-26 03:38:10.669  3364-3364  Choreographer           com.vere.likes                       I  Skipped 30 frames!  The application may be doing too much work on its main thread.
2025-07-26 03:38:10.677  3364-3380  SharedPrefMemoRepo      com.vere.likes                       D  getAllMemos called, memosJson is null: false
2025-07-26 03:38:10.682  3364-3380  SharedPrefMemoRepo      com.vere.likes                       D  memosJson content: [{"id":"80cd2604-37bb-43c1-b9a5-6dd98c3ad5cb","title":"会议记录","content":"今天的项目会议讨论了新功能的开发计划...","categoryId":"default_work","priority":"HIGH","isFavorite":false,"isCompleted":false,"createdAt":"2025-07-12T17:39:06","updatedAt":"2025-07-12T17:39:06","reminderAt":"","tags":[],"imagePaths":[]},{"id":"6e8e88b6-d5d1-4278-930a-4e7f82a57db3","title":"购物清单","content":"牛奶、面包、鸡蛋、苹果...","categoryId":"default_life","priority":"MEDIUM","isFavorite":false,"isCompleted":false,"createdAt":"2025-07-12T17:39:06","updatedAt":"2025-07-12T17:39:06","reminderAt":"","tags":[],"imagePaths":[]},{"id":"67d5135c-7c1c-4b8e-96fc-a0e198251c2c","title":"学习计划","content":"本周需要完成Kotlin协程的学习...","categoryId":"default_study","priority":"HIGH","isFavorite":true,"isCompleted":false,"createdAt":"2025-07-12T17:39:06","updatedAt":"2025-07-12T17:39:06","reminderAt":"","tags":[],"imagePaths":[]}]
2025-07-26 03:38:10.695  3364-3380  SharedPrefMemoRepo      com.vere.likes                       D  Loaded 3 memos from SharedPreferences
2025-07-26 03:38:10.701  3364-3393  SharedPrefMemoRepo      com.vere.likes                       D  getAllMemos called, memosJson is null: false
2025-07-26 03:38:10.702  3364-3393  SharedPrefMemoRepo      com.vere.likes                       D  memosJson content: [{"id":"80cd2604-37bb-43c1-b9a5-6dd98c3ad5cb","title":"会议记录","content":"今天的项目会议讨论了新功能的开发计划...","categoryId":"default_work","priority":"HIGH","isFavorite":false,"isCompleted":false,"createdAt":"2025-07-12T17:39:06","updatedAt":"2025-07-12T17:39:06","reminderAt":"","tags":[],"imagePaths":[]},{"id":"6e8e88b6-d5d1-4278-930a-4e7f82a57db3","title":"购物清单","content":"牛奶、面包、鸡蛋、苹果...","categoryId":"default_life","priority":"MEDIUM","isFavorite":false,"isCompleted":false,"createdAt":"2025-07-12T17:39:06","updatedAt":"2025-07-12T17:39:06","reminderAt":"","tags":[],"imagePaths":[]},{"id":"67d5135c-7c1c-4b8e-96fc-a0e198251c2c","title":"学习计划","content":"本周需要完成Kotlin协程的学习...","categoryId":"default_study","priority":"HIGH","isFavorite":true,"isCompleted":false,"createdAt":"2025-07-12T17:39:06","updatedAt":"2025-07-12T17:39:06","reminderAt":"","tags":[],"imagePaths":[]}]
2025-07-26 03:38:10.705  3364-3380  SharedPrefMemoRepo      com.vere.likes                       D  getAllMemos called, memosJson is null: false
2025-07-26 03:38:10.706  3364-3393  SharedPrefMemoRepo      com.vere.likes                       D  Loaded 3 memos from SharedPreferences
2025-07-26 03:38:10.707  3364-3380  SharedPrefMemoRepo      com.vere.likes                       D  memosJson content: [{"id":"80cd2604-37bb-43c1-b9a5-6dd98c3ad5cb","title":"会议记录","content":"今天的项目会议讨论了新功能的开发计划...","categoryId":"default_work","priority":"HIGH","isFavorite":false,"isCompleted":false,"createdAt":"2025-07-12T17:39:06","updatedAt":"2025-07-12T17:39:06","reminderAt":"","tags":[],"imagePaths":[]},{"id":"6e8e88b6-d5d1-4278-930a-4e7f82a57db3","title":"购物清单","content":"牛奶、面包、鸡蛋、苹果...","categoryId":"default_life","priority":"MEDIUM","isFavorite":false,"isCompleted":false,"createdAt":"2025-07-12T17:39:06","updatedAt":"2025-07-12T17:39:06","reminderAt":"","tags":[],"imagePaths":[]},{"id":"67d5135c-7c1c-4b8e-96fc-a0e198251c2c","title":"学习计划","content":"本周需要完成Kotlin协程的学习...","categoryId":"default_study","priority":"HIGH","isFavorite":true,"isCompleted":false,"createdAt":"2025-07-12T17:39:06","updatedAt":"2025-07-12T17:39:06","reminderAt":"","tags":[],"imagePaths":[]}]
2025-07-26 03:38:10.709  3364-3380  SharedPrefMemoRepo      com.vere.likes                       D  Loaded 3 memos from SharedPreferences
2025-07-26 03:38:10.925  3364-3364  HomePresenter           com.vere.likes                       D  Loaded 3 memos from repository
2025-07-26 03:38:10.934  3364-3364  BlankScreenDetector     com.vere.likes                       I  数据加载中: HomeScreen
2025-07-26 03:38:10.937  3364-3364  HomePresenter           com.vere.likes                       D  Loaded 3 memos from repository
2025-07-26 03:38:10.940  3364-3374  HWUI                    com.vere.likes                       I  Davey! duration=1413ms; Flags=0, FrameTimelineVsyncId=11684, IntendedVsync=131157236008, Vsync=131557235992, InputEventId=0, HandleInputStart=131571484300, AnimationStart=131571542200, PerformTraversalsStart=131855517000, DrawStart=132275220100, FrameDeadline=131590569324, FrameInterval=131570417300, FrameStartTime=16666666, SyncQueued=132287076900, SyncStart=132292020600, IssueDrawCommandsStart=132292235600, SwapBuffers=132569137200, FrameCompleted=132575338000, DequeueBufferDuration=30400, QueueBufferDuration=3323800, GpuCompleted=132574508500, SwapBuffersCompleted=132575338000, DisplayPresentTime=0, CommandSubmissionCompleted=132569137200,
2025-07-26 03:38:11.012  3364-3364  HomePresenter           com.vere.likes                       D  Loaded 3 memos from repository
2025-07-26 03:38:11.125  3364-3364  BlankScreenDetector     com.vere.likes                       D  正常显示: HomeScreen
2025-07-26 03:38:14.266  3364-3402  ProfileInstaller        com.vere.likes                       D  Installing profile for com.vere.likes
---------------------------- PROCESS ENDED (3364) for package com.vere.likes ----------------------------
---------------------------- PROCESS STARTED (3600) for package com.vere.likes ----------------------------
2025-07-26 03:40:36.760  3600-3618  EGL_emulation           com.vere.likes                       D  app_time_stats: avg=276.25ms min=6.14ms max=1479.87ms count=7
2025-07-26 03:40:38.494  3600-3618  EGL_emulation           com.vere.likes                       D  app_time_stats: avg=31.09ms min=4.36ms max=873.08ms count=49
2025-07-26 03:40:38.521  3600-3600  HomeScreen              com.vere.likes                       D  Lifecycle event: ON_PAUSE
2025-07-26 03:40:38.522  3600-3600  HomeScreen              com.vere.likes                       D  Lifecycle event: ON_STOP
2025-07-26 03:40:38.541  3600-3600  System.out              com.vere.likes                       I  === HierarchicalPlanningDataManager: 构造函数开始 ===
2025-07-26 03:40:38.541  3600-3600  System.out              com.vere.likes                       I  === HierarchicalPlanningDataManager: 构造函数完成 ===
2025-07-26 03:40:38.541  3600-3600  System.out              com.vere.likes                       I  === HierarchicalPlanningViewModel: 初始化开始 ===
2025-07-26 03:40:38.542  3600-3628  System.out              com.vere.likes                       I  === HierarchicalPlanningDataManager: 协程初始化开始 ===
2025-07-26 03:40:38.542  3600-3628  System.out              com.vere.likes                       I  HierarchicalPlanningDataManager: 开始加载所有数据
2025-07-26 03:40:38.542  3600-3628  System.out              com.vere.likes                       I  HierarchicalPlanningDataManager: 测试数据库连接
2025-07-26 03:40:38.548  3600-3600  WindowOnBackDispatcher  com.vere.likes                       W  OnBackInvokedCallback is not enabled for the application.
                                                                                                    Set 'android:enableOnBackInvokedCallback="true"' in the application manifest.
2025-07-26 03:40:38.586  3600-3628  System.out              com.vere.likes                       I  HierarchicalPlanningDataManager: 数据库连接正常 - 计划:21, 任务:2, 目标:5
2025-07-26 03:40:38.587  3600-3628  System.out              com.vere.likes                       I  HierarchicalPlanningDataManager: 开始加载计划数据
2025-07-26 03:40:38.592  3600-3628  System.out              com.vere.likes                       I  HierarchicalPlanningDataManager: 从数据库加载了 21 个计划
2025-07-26 03:40:38.593  3600-3628  System.out              com.vere.likes                       I  HierarchicalPlanningDataManager: 发现重复计划 DAILY_{"startDate":"2025-07-24","endDate":"2025-07-24","level":"DAILY"}: [daily_plan_1753377077119, daily_plan_1753376511497, daily_plan_1753376474483, daily_plan_1753375949647, daily_plan_1753375900458, daily_plan_1753369370791, daily_plan_1753368501961]
2025-07-26 03:40:38.593  3600-3628  System.out              com.vere.likes                       I  HierarchicalPlanningDataManager: 发现重复计划 MONTHLY_{"startDate":"2025-07-01","endDate":"2025-07-31","level":"MONTHLY"}: [monthly_plan_1753377077152, monthly_plan_1753376511557, monthly_plan_1753376474535, monthly_plan_1753375949655, monthly_plan_1753375900506, monthly_plan_1753369370890, monthly_plan_1753368502047]
2025-07-26 03:40:38.593  3600-3628  System.out              com.vere.likes                       I  HierarchicalPlanningDataManager: 发现重复计划 WEEKLY_{"startDate":"2025-07-20","endDate":"2025-07-26","level":"WEEKLY"}: [weekly_plan_1753377077124, weekly_plan_1753376511549, weekly_plan_1753376474491, weekly_plan_1753375949652, weekly_plan_1753375900500, weekly_plan_1753369370884, weekly_plan_1753368502014]
2025-07-26 03:40:38.597  3600-3628  System.out              com.vere.likes                       I  HierarchicalPlanningDataManager: 计划数据加载完成，内存中有 21 个计划
2025-07-26 03:40:38.597  3600-3628  System.out              com.vere.likes                       I  HierarchicalPlanningDataManager: 开始加载目标数据
2025-07-26 03:40:38.598  3600-3628  System.out              com.vere.likes                       I  HierarchicalPlanningDataManager: 从数据库加载了 5 个目标
2025-07-26 03:40:38.599  3600-3628  System.out              com.vere.likes                       I  HierarchicalPlanningDataManager: 目标 - ID=daily_goal_1753377077174, 标题=123, parentId=daily_plan_1753377077119
2025-07-26 03:40:38.599  3600-3628  System.out              com.vere.likes                       I  HierarchicalPlanningDataManager: 目标 - ID=daily_goal_1753376474547, 标题=123, parentId=daily_plan_1753376474483
2025-07-26 03:40:38.599  3600-3628  System.out              com.vere.likes                       I  HierarchicalPlanningDataManager: 目标 - ID=daily_goal_1753375900612, 标题=123, parentId=null
2025-07-26 03:40:38.602  3600-3628  System.out              com.vere.likes                       I  HierarchicalPlanningDataManager: 目标 - ID=daily_goal_1753369370773, 标题=123, parentId=null
2025-07-26 03:40:38.604  3600-3628  System.out              com.vere.likes                       I  HierarchicalPlanningDataManager: 目标 - ID=daily_goal_1753368532145, 标题=123, parentId=null
2025-07-26 03:40:38.608  3600-3628  System.out              com.vere.likes                       I  HierarchicalPlanningDataManager: 目标数据加载完成，内存中有 5 个目标
2025-07-26 03:40:38.609  3600-3628  System.out              com.vere.likes                       I  HierarchicalPlanningDataManager: 开始加载任务数据
2025-07-26 03:40:38.612  3600-3628  System.out              com.vere.likes                       I  HierarchicalPlanningDataManager: 从数据库加载了 2 个任务
2025-07-26 03:40:38.615  3600-3628  System.out              com.vere.likes                       I  HierarchicalPlanningDataManager: 任务 - ID=daily_task_1753376511567, 标题=123, parentId=daily_plan_1753376511497
2025-07-26 03:40:38.615  3600-3628  System.out              com.vere.likes                       I  HierarchicalPlanningDataManager: 任务 - ID=daily_task_1753375949760, 标题=123, parentId=null
2025-07-26 03:40:38.617  3600-3628  System.out              com.vere.likes                       I  HierarchicalPlanningDataManager: 任务数据加载完成，内存中有 2 个任务
2025-07-26 03:40:38.619  3600-3628  System.out              com.vere.likes                       I  HierarchicalPlanningDataManager: 所有数据加载完成
2025-07-26 03:40:38.620  3600-3628  System.out              com.vere.likes                       I  === HierarchicalPlanningDataManager: 协程初始化完成 ===
2025-07-26 03:40:38.626  3600-3600  System.out              com.vere.likes                       I  HierarchicalPlanningViewModel: currentPlans更新 - 日期=2025-07-25
2025-07-26 03:40:38.626  3600-3600  System.out              com.vere.likes                       I  HierarchicalPlanningViewModel: 找到的计划 - 日:null, 周:weekly_plan_1753377077124, 月:monthly_plan_1753377077152
2025-07-26 03:40:38.626  3600-3600  System.out              com.vere.likes                       I  HierarchicalPlanningViewModel: currentLevelPlan更新 - 层级=DAILY, 计划ID=null
2025-07-26 03:40:38.627  3600-3600  System.out              com.vere.likes                       I  HierarchicalPlanningViewModel: currentLevelTasks更新 - 计划ID=null, 任务数量=0
2025-07-26 03:40:38.629  3600-3600  System.out              com.vere.likes                       I  HierarchicalPlanningViewModel: currentLevelGoals更新 - 计划ID=null, 目标数量=0
2025-07-26 03:40:38.630  3600-3600  System.out              com.vere.likes                       I  HierarchicalPlanningViewModel: 开始确保计划存在
2025-07-26 03:40:38.631  3600-3600  System.out              com.vere.likes                       I  HierarchicalPlanningViewModel: 当前日期 = 2025-07-25
2025-07-26 03:40:38.631  3600-3600  System.out              com.vere.likes                       I  HierarchicalPlanningViewModel: 当前层级 = DAILY
2025-07-26 03:40:38.631  3600-3600  System.out              com.vere.likes                       I  HierarchicalPlanningViewModel: 当前计划状态 = {DAILY=null, WEEKLY=HierarchicalPlan(id=weekly_plan_1753377077124, title=, description=, level=WEEKLY, timeRange=TimeRange(startDate=2025-07-20, endDate=2025-07-26, level=WEEKLY), status=TODO, priority=MEDIUM, category=PERSONAL, parentId=null, childIds=[], createdAt=2025-07-24T17:11:17.124512, updatedAt=2025-07-24T17:11:17.124512, goals=[], tasks=[], keyFocus=[], themes=[], metrics={}, notes=, timeAllocation={}, actualTimeSpent={}, completionRate=0.0, milestones=[]), MONTHLY=HierarchicalPlan(id=monthly_plan_1753377077152, title=, description=, level=MONTHLY, timeRange=TimeRange(startDate=2025-07-01, endDate=2025-07-31, level=MONTHLY), status=TODO, priority=MEDIUM, category=PERSONAL, parentId=null, childIds=[], createdAt=2025-07-24T17:11:17.152941, updatedAt=2025-07-24T17:11:17.152941, goals=[], tasks=[], keyFocus=[], themes=[], metrics={}, notes=, timeAllocation={}, actualTimeSpent={}, completionRate=0.0, milestones=[])}
2025-07-26 03:40:38.631  3600-3600  System.out              com.vere.likes                       I  HierarchicalPlanningViewModel: 创建日计划
2025-07-26 03:40:38.657  3600-3600  System.out              com.vere.likes                       I  HierarchicalPlanningViewModel: currentPlans更新 - 日期=2025-07-25
2025-07-26 03:40:38.657  3600-3600  System.out              com.vere.likes                       I  HierarchicalPlanningViewModel: 找到的计划 - 日:daily_plan_1753472438632, 周:weekly_plan_1753377077124, 月:monthly_plan_1753377077152
2025-07-26 03:40:38.657  3600-3600  System.out              com.vere.likes                       I  HierarchicalPlanningViewModel: currentLevelPlan更新 - 层级=DAILY, 计划ID=daily_plan_1753472438632
2025-07-26 03:40:38.657  3600-3600  System.out              com.vere.likes                       I  HierarchicalPlanningViewModel: currentLevelTasks更新 - 计划ID=daily_plan_1753472438632, 任务数量=0
2025-07-26 03:40:38.658  3600-3600  System.out              com.vere.likes                       I  HierarchicalPlanningViewModel: currentLevelGoals更新 - 计划ID=daily_plan_1753472438632, 目标数量=0
2025-07-26 03:40:38.658  3600-3600  System.out              com.vere.likes                       I  HierarchicalPlanningViewModel: 日计划ID = daily_plan_1753472438632
2025-07-26 03:40:38.659  3600-3600  System.out              com.vere.likes                       I  HierarchicalPlanningViewModel: 周计划已存在
2025-07-26 03:40:38.659  3600-3600  System.out              com.vere.likes                       I  HierarchicalPlanningViewModel: 月计划已存在
2025-07-26 03:40:38.659  3600-3600  System.out              com.vere.likes                       I  HierarchicalPlanningViewModel: 计划确保完成，目标计划ID = daily_plan_1753472438632
2025-07-26 03:40:40.190  3600-3646  ProfileInstaller        com.vere.likes                       D  Installing profile for com.vere.likes
2025-07-26 03:40:40.850  3600-3618  EGL_emulation           com.vere.likes                       D  app_time_stats: avg=37.71ms min=3.67ms max=1484.87ms count=51
2025-07-26 03:40:40.883  3600-3600  System.out              com.vere.likes                       I  === HierarchicalPlanningViewModel: 初始化开始 ===
2025-07-26 03:40:47.952  3600-3618  EGL_emulation           com.vere.likes                       D  app_time_stats: avg=151.05ms min=10.03ms max=6332.02ms count=47
2025-07-26 03:40:48.070  3600-3600  WindowOnBackDispatcher  com.vere.likes                       W  OnBackInvokedCallback is not enabled for the application.
                                                                                                    Set 'android:enableOnBackInvokedCallback="true"' in the application manifest.
2025-07-26 03:40:49.285  3600-3600  Compose Focus           com.vere.likes                       D  Owner FocusChanged(true)
2025-07-26 03:40:49.303  3600-3600  InsetsController        com.vere.likes                       D  show(ime(), fromIme=false)
2025-07-26 03:40:49.306  3600-3600  ImeTracker              com.vere.likes                       I  com.vere.likes:b4b0ce56: onRequestShow at ORIGIN_CLIENT reason SHOW_SOFT_INPUT_BY_INSETS_API fromUser false
2025-07-26 03:40:49.312  3600-3600  InputMethodManager      com.vere.likes                       D  showSoftInput() view=androidx.compose.ui.platform.AndroidComposeView{43b3e1b VFED..... .F....ID 0,0-1081,2219 aid=1073741825} flags=0 reason=SHOW_SOFT_INPUT_BY_INSETS_API
2025-07-26 03:40:49.379  3600-3618  EGL_emulation           com.vere.likes                       D  app_time_stats: avg=351.42ms min=24.48ms max=982.38ms count=3
2025-07-26 03:40:49.985  3600-3600  InsetsController        com.vere.likes                       D  show(ime(), fromIme=true)
2025-07-26 03:40:50.004  3600-3600  InteractionJankMonitor  com.vere.likes                       W  Initializing without READ_DEVICE_CONFIG permission. enabled=true, interval=1, missedFrameThreshold=3, frameTimeThreshold=64, package=com.vere.likes
2025-07-26 03:40:50.086  3600-3600  Compose Focus           com.vere.likes                       D  Owner FocusChanged(true)
2025-07-26 03:40:50.105  3600-3618  EGL_emulation           com.vere.likes                       D  app_time_stats: avg=49.26ms min=6.42ms max=1266.28ms count=43
2025-07-26 03:40:50.319  3600-3600  ImeTracker              com.vere.likes                       I  com.vere.likes:b4b0ce56: onShown
2025-07-26 03:40:50.538  3600-3618  EGL_emulation           com.vere.likes                       D  app_time_stats: avg=30.73ms min=5.24ms max=266.97ms count=32
2025-07-26 03:40:51.502  3600-3600  InsetsController        com.vere.likes                       D  show(ime(), fromIme=false)
2025-07-26 03:40:51.503  3600-3600  ImeTracker              com.vere.likes                       I  com.vere.likes:a25206f: onRequestShow at ORIGIN_CLIENT reason SHOW_SOFT_INPUT_BY_INSETS_API fromUser false
2025-07-26 03:40:51.503  3600-3600  ImeTracker              com.vere.likes                       I  com.vere.likes:a25206f: onCancelled at PHASE_CLIENT_APPLY_ANIMATION
2025-07-26 03:40:51.544  3600-3618  EGL_emulation           com.vere.likes                       D  app_time_stats: avg=167.55ms min=15.49ms max=388.00ms count=6
2025-07-26 03:40:51.585  3600-3600  InsetsController        com.vere.likes                       D  show(ime(), fromIme=true)
2025-07-26 03:40:51.585  3600-3600  ImeTracker              com.vere.likes                       I  com.vere.likes:7c6490c9: onCancelled at PHASE_CLIENT_APPLY_ANIMATION
2025-07-26 03:40:52.551  3600-3618  EGL_emulation           com.vere.likes                       D  app_time_stats: avg=59.17ms min=13.11ms max=348.10ms count=17
2025-07-26 03:40:53.589  3600-3618  EGL_emulation           com.vere.likes                       D  app_time_stats: avg=259.41ms min=18.56ms max=502.35ms count=4
2025-07-26 03:40:55.070  3600-3618  EGL_emulation           com.vere.likes                       D  app_time_stats: avg=493.44ms min=479.93ms max=516.80ms count=3
2025-07-26 03:40:56.022  3600-3600  InsetsController        com.vere.likes                       D  hide(ime(), fromIme=true)
2025-07-26 03:40:56.075  3600-3618  EGL_emulation           com.vere.likes                       D  app_time_stats: avg=248.26ms min=4.31ms max=498.86ms count=4
2025-07-26 03:40:56.349  3600-3600  ImeTracker              com.vere.likes                       I  com.vere.likes:6b376d28: onRequestHide at ORIGIN_CLIENT reason HIDE_SOFT_INPUT_ON_ANIMATION_STATE_CHANGED fromUser false
2025-07-26 03:40:56.352  3600-3600  ImeTracker              com.vere.likes                       I  com.google.android.inputmethod.latin:bd6a9878: onHidden
2025-07-26 03:40:57.085  3600-3618  EGL_emulation           com.vere.likes                       D  app_time_stats: avg=43.63ms min=4.49ms max=500.01ms count=19
2025-07-26 03:40:58.339  3600-3618  EGL_emulation           com.vere.likes                       D  app_time_stats: avg=418.01ms min=254.54ms max=517.79ms count=3
2025-07-26 03:40:58.500  3600-3600  System.out              com.vere.likes                       I  HierarchicalPlanningViewModel: 开始添加增强目标 - 123
2025-07-26 03:40:58.500  3600-3600  System.out              com.vere.likes                       I  HierarchicalPlanningViewModel: 开始确保计划存在
2025-07-26 03:40:58.500  3600-3600  System.out              com.vere.likes                       I  HierarchicalPlanningViewModel: 当前日期 = 2025-07-25
2025-07-26 03:40:58.500  3600-3600  System.out              com.vere.likes                       I  HierarchicalPlanningViewModel: 当前层级 = DAILY
2025-07-26 03:40:58.500  3600-3600  System.out              com.vere.likes                       I  HierarchicalPlanningViewModel: 当前计划状态 = {}
2025-07-26 03:40:58.500  3600-3600  System.out              com.vere.likes                       I  HierarchicalPlanningViewModel: 创建日计划
2025-07-26 03:40:58.502  3600-3600  WindowOnBackDispatcher  com.vere.likes                       W  sendCancelIfRunning: isInProgress=false callback=android.view.ViewRootImpl$$ExternalSyntheticLambda11@fdfb112
2025-07-26 03:40:58.513  3600-3618  HWUI                    com.vere.likes                       D  endAllActiveAnimators on 0x7e08034417e0 (UnprojectedRipple) with handle 0x7e089344dbb0
2025-07-26 03:40:58.525  3600-3600  System.out              com.vere.likes                       I  HierarchicalPlanningViewModel: 日计划ID = daily_plan_1753472458500
2025-07-26 03:40:58.525  3600-3600  System.out              com.vere.likes                       I  HierarchicalPlanningViewModel: 创建周计划
2025-07-26 03:40:58.529  3600-3600  ImeTracker              com.vere.likes                       I  com.vere.likes:98d3ea9f: onRequestHide at ORIGIN_CLIENT reason HIDE_SOFT_INPUT fromUser false
2025-07-26 03:40:58.530  3600-3600  ImeTracker              com.vere.likes                       I  com.vere.likes:98d3ea9f: onFailed at PHASE_CLIENT_VIEW_SERVED
2025-07-26 03:40:58.530  3600-3600  System.out              com.vere.likes                       I  HierarchicalPlanningViewModel: 创建月计划
2025-07-26 03:40:58.538  3600-3600  System.out              com.vere.likes                       I  HierarchicalPlanningViewModel: 计划确保完成，目标计划ID = daily_plan_1753472458500
2025-07-26 03:40:58.539  3600-3600  System.out              com.vere.likes                       I  HierarchicalPlanningViewModel: 获取到的当前计划ID = daily_plan_1753472458500
2025-07-26 03:40:58.539  3600-3600  System.out              com.vere.likes                       I  HierarchicalPlanningViewModel: 当前层级 = DAILY
2025-07-26 03:40:58.543  3600-3600  System.out              com.vere.likes                       I  HierarchicalPlanningViewModel: 创建的目标 - ID=daily_goal_1753472458540, parentId=daily_plan_1753472458500
2025-07-26 03:40:58.543  3600-3600  System.out              com.vere.likes                       I  HierarchicalPlanningViewModel: 调用dataManager.addGoal
2025-07-26 03:40:58.546  3600-3600  System.out              com.vere.likes                       I  HierarchicalPlanningDataManager: 开始添加目标 - 123
2025-07-26 03:40:58.556  3600-3628  System.out              com.vere.likes                       I  HierarchicalPlanningDataManager: 目标已保存到数据库 - 123
2025-07-26 03:40:58.557  3600-3600  System.out              com.vere.likes                       I  HierarchicalPlanningDataManager: 目标已添加到内存，当前目标数量: 6
2025-07-26 03:40:58.558  3600-3600  System.out              com.vere.likes                       I  HierarchicalPlanningViewModel: 目标添加完成
2025-07-26 03:41:00.565  3600-3600  FrameTracker            com.vere.likes                       E  force finish cuj, time out: J<IME_INSETS_SHOW_ANIMATION::0@<EMAIL>>
2025-07-26 03:41:06.599  3600-3600  FrameTracker            com.vere.likes                       E  force finish cuj, time out: J<IME_INSETS_HIDE_ANIMATION::1@<EMAIL>>
2025-07-26 03:41:06.600  3600-3600  FrameTracker            com.vere.likes                       W  Missed SF frame:UNKNOWN: 80, 67297, 0, CUJ=J<IME_INSETS_HIDE_ANIMATION::1@<EMAIL>>
2025-07-26 03:41:06.600  3600-3600  FrameTracker            com.vere.likes                       W  Missed SF frame:UNKNOWN: 80, 67333, 0, CUJ=J<IME_INSETS_HIDE_ANIMATION::1@<EMAIL>>
2025-07-26 03:41:06.600  3600-3600  FrameTracker            com.vere.likes                       W  Missed SF frame:UNKNOWN: 80, 67362, 0, CUJ=J<IME_INSETS_HIDE_ANIMATION::1@<EMAIL>>
2025-07-26 03:41:06.600  3600-3600  FrameTracker            com.vere.likes                       W  Missed SF frame:UNKNOWN: 80, 67398, 0, CUJ=J<IME_INSETS_HIDE_ANIMATION::1@<EMAIL>>
2025-07-26 03:41:06.601  3600-3600  FrameTracker            com.vere.likes                       W  Missed SF frame:UNKNOWN: 80, 67427, 0, CUJ=J<IME_INSETS_HIDE_ANIMATION::1@<EMAIL>>
2025-07-26 03:41:06.601  3600-3600  FrameTracker            com.vere.likes                       W  Missed SF frame:UNKNOWN: 80, 67449, 0, CUJ=J<IME_INSETS_HIDE_ANIMATION::1@<EMAIL>>
2025-07-26 03:41:06.601  3600-3600  FrameTracker            com.vere.likes                       W  Missed SF frame:UNKNOWN: 80, 67471, 0, CUJ=J<IME_INSETS_HIDE_ANIMATION::1@<EMAIL>>
2025-07-26 03:41:06.602  3600-3600  FrameTracker            com.vere.likes                       W  Missed SF frame:UNKNOWN: 80, 67493, 0, CUJ=J<IME_INSETS_HIDE_ANIMATION::1@<EMAIL>>
2025-07-26 03:41:06.603  3600-3600  FrameTracker            com.vere.likes                       W  Missed SF frame:UNKNOWN: 80, 67515, 0, CUJ=J<IME_INSETS_HIDE_ANIMATION::1@<EMAIL>>
2025-07-26 03:41:06.604  3600-3600  FrameTracker            com.vere.likes                       W  Missed SF frame:UNKNOWN: 80, 67537, 0, CUJ=J<IME_INSETS_HIDE_ANIMATION::1@<EMAIL>>
2025-07-26 03:41:06.604  3600-3600  FrameTracker            com.vere.likes                       W  Missed SF frame:UNKNOWN: 80, 67559, 0, CUJ=J<IME_INSETS_HIDE_ANIMATION::1@<EMAIL>>
2025-07-26 03:41:06.605  3600-3600  FrameTracker            com.vere.likes                       W  Missed SF frame:UNKNOWN: 80, 67581, 0, CUJ=J<IME_INSETS_HIDE_ANIMATION::1@<EMAIL>>
2025-07-26 03:41:06.605  3600-3600  FrameTracker            com.vere.likes                       W  Missed SF frame:UNKNOWN: 80, 67610, 0, CUJ=J<IME_INSETS_HIDE_ANIMATION::1@<EMAIL>>
2025-07-26 03:41:06.606  3600-3600  FrameTracker            com.vere.likes                       W  Missed SF frame:UNKNOWN: 80, 67632, 0, CUJ=J<IME_INSETS_HIDE_ANIMATION::1@<EMAIL>>
2025-07-26 03:41:06.606  3600-3600  FrameTracker            com.vere.likes                       W  Missed SF frame:UNKNOWN: 80, 67647, 0, CUJ=J<IME_INSETS_HIDE_ANIMATION::1@<EMAIL>>
2025-07-26 03:41:06.606  3600-3600  FrameTracker            com.vere.likes                       W  Missed SF frame:UNKNOWN: 80, 67676, 0, CUJ=J<IME_INSETS_HIDE_ANIMATION::1@<EMAIL>>
2025-07-26 03:41:06.606  3600-3600  FrameTracker            com.vere.likes                       W  Missed SF frame:UNKNOWN: 80, 67691, 0, CUJ=J<IME_INSETS_HIDE_ANIMATION::1@<EMAIL>>
2025-07-26 03:41:06.612  3600-3697  PerfettoTrigger         com.vere.likes                       V  Triggering /system/bin/trigger_perfetto com.android.telemetry.interaction-jank-monitor-81
2025-07-26 03:41:11.665  3600-3618  EGL_emulation           com.vere.likes                       D  app_time_stats: avg=21559.92ms min=21559.92ms max=21559.92ms count=1
2025-07-26 03:41:11.798  3600-3600  System.out              com.vere.likes                       I  HierarchicalPlanningViewModel: currentPlans更新 - 日期=2025-07-25
2025-07-26 03:41:11.798  3600-3600  System.out              com.vere.likes                       I  HierarchicalPlanningViewModel: 找到的计划 - 日:daily_plan_1753472438632, 周:weekly_plan_1753377077124, 月:monthly_plan_1753377077152
2025-07-26 03:41:11.798  3600-3600  System.out              com.vere.likes                       I  HierarchicalPlanningViewModel: currentLevelPlan更新 - 层级=DAILY, 计划ID=daily_plan_1753472438632
2025-07-26 03:41:11.799  3600-3600  System.out              com.vere.likes                       I  HierarchicalPlanningViewModel: currentLevelTasks更新 - 计划ID=daily_plan_1753472438632, 任务数量=0
2025-07-26 03:41:11.800  3600-3600  System.out              com.vere.likes                       I  HierarchicalPlanningViewModel: currentLevelGoals更新 - 计划ID=daily_plan_1753472438632, 目标数量=0
2025-07-26 03:41:11.800  3600-3600  System.out              com.vere.likes                       I  HierarchicalPlanningViewModel: 开始确保计划存在
2025-07-26 03:41:11.801  3600-3600  System.out              com.vere.likes                       I  HierarchicalPlanningViewModel: 当前日期 = 2025-07-25
2025-07-26 03:41:11.801  3600-3600  System.out              com.vere.likes                       I  HierarchicalPlanningViewModel: 当前层级 = DAILY
2025-07-26 03:41:11.802  3600-3600  System.out              com.vere.likes                       I  HierarchicalPlanningViewModel: 当前计划状态 = {DAILY=HierarchicalPlan(id=daily_plan_1753472438632, title=, description=, level=DAILY, timeRange=TimeRange(startDate=2025-07-25, endDate=2025-07-25, level=DAILY), status=TODO, priority=MEDIUM, category=PERSONAL, parentId=null, childIds=[], createdAt=2025-07-25T19:40:38.632211, updatedAt=2025-07-25T19:40:38.632211, goals=[], tasks=[], keyFocus=[], themes=[], metrics={}, notes=, timeAllocation={}, actualTimeSpent={}, completionRate=0.0, milestones=[]), WEEKLY=HierarchicalPlan(id=weekly_plan_1753377077124, title=, description=, level=WEEKLY, timeRange=TimeRange(startDate=2025-07-20, endDate=2025-07-26, level=WEEKLY), status=TODO, priority=MEDIUM, category=PERSONAL, parentId=null, childIds=[], createdAt=2025-07-24T17:11:17.124512, updatedAt=2025-07-24T17:11:17.124512, goals=[], tasks=[], keyFocus=[], themes=[], metrics={}, notes=, timeAllocation={}, actualTimeSpent={}, completionRate=0.0, milestones=[]), MONTHLY=HierarchicalPlan(id=monthly_plan_1753377077152, title=, description=, level=MONTHLY, timeRange=TimeRange(startDate=2025-07-01, endDate=2025-07-31, level=MONTHLY), status=TODO, priority=MEDIUM, category=PERSONAL, parentId=null, childIds=[], createdAt=2025-07-24T17:11:17.152941, updatedAt=2025-07-24T17:11:17.152941, goals=[], tasks=[], keyFocus=[], themes=[], metrics={}, notes=, timeAllocation={}, actualTimeSpent={}, completionRate=0.0, milestones=[])}
2025-07-26 03:41:11.802  3600-3600  System.out              com.vere.likes                       I  HierarchicalPlanningViewModel: 日计划已存在
2025-07-26 03:41:11.803  3600-3600  System.out              com.vere.likes                       I  HierarchicalPlanningViewModel: 周计划已存在
2025-07-26 03:41:11.803  3600-3600  System.out              com.vere.likes                       I  HierarchicalPlanningViewModel: 月计划已存在
2025-07-26 03:41:11.803  3600-3600  System.out              com.vere.likes                       I  HierarchicalPlanningViewModel: 计划确保完成，目标计划ID = daily_plan_1753472438632
2025-07-26 03:41:13.469  3600-3618  EGL_emulation           com.vere.likes                       D  app_time_stats: avg=35.73ms min=4.58ms max=948.47ms count=50
2025-07-26 03:41:13.550  3600-3600  System.out              com.vere.likes                       I  === HierarchicalPlanningViewModel: 初始化开始 ===
