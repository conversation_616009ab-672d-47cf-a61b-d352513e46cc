# 简化的API修复脚本
Write-Host "开始修复过时的API..." -ForegroundColor Green

# 获取所有Kotlin文件
$kotlinFiles = Get-ChildItem -Path "app\src" -Filter "*.kt" -Recurse

Write-Host "找到 $($kotlinFiles.Count) 个Kotlin文件" -ForegroundColor Yellow

$totalReplacements = 0
$modifiedFiles = 0

foreach ($file in $kotlinFiles) {
    $content = Get-Content $file.FullName -Raw -Encoding UTF8
    $originalContent = $content
    $fileReplacements = 0
    
    # 替换最常见的过时图标
    $replacements = @(
        @('Icons.Default.ArrowBack', 'Icons.AutoMirrored.Filled.ArrowBack'),
        @('Icons.Filled.ArrowBack', 'Icons.AutoMirrored.Filled.ArrowBack'),
        @('Icons.Default.ArrowForward', 'Icons.AutoMirrored.Filled.ArrowForward'),
        @('Icons.Default.KeyboardArrowLeft', 'Icons.AutoMirrored.Filled.KeyboardArrowLeft'),
        @('Icons.Default.KeyboardArrowRight', 'Icons.AutoMirrored.Filled.KeyboardArrowRight'),
        @('Icons.Default.TrendingUp', 'Icons.AutoMirrored.Filled.TrendingUp'),
        @('Icons.Default.Sort', 'Icons.AutoMirrored.Filled.Sort'),
        @('Icons.Default.Note', 'Icons.AutoMirrored.Filled.Note'),
        @('Icons.Filled.Note', 'Icons.AutoMirrored.Filled.Note'),
        @('Icons.Default.Notes', 'Icons.AutoMirrored.Filled.Notes'),
        @('Icons.Default.NoteAdd', 'Icons.AutoMirrored.Filled.NoteAdd'),
        @('Icons.Default.Help', 'Icons.AutoMirrored.Filled.Help'),
        @('Icons.Default.Label', 'Icons.AutoMirrored.Filled.Label'),
        @('Icons.Default.ViewList', 'Icons.AutoMirrored.Filled.ViewList'),
        @('Icons.Default.Send', 'Icons.AutoMirrored.Filled.Send'),
        @('Icons.Default.OpenInNew', 'Icons.AutoMirrored.Filled.OpenInNew'),
        @('Icons.Default.Assignment', 'Icons.AutoMirrored.Filled.Assignment'),
        @('Icons.Default.EventNote', 'Icons.AutoMirrored.Filled.EventNote'),
        @('Icons.Default.FormatListBulleted', 'Icons.AutoMirrored.Filled.FormatListBulleted'),
        @('Icons.Default.FormatAlignLeft', 'Icons.AutoMirrored.Filled.FormatAlignLeft'),
        @('Icons.Default.FormatAlignRight', 'Icons.AutoMirrored.Filled.FormatAlignRight'),
        @('Icons.Default.Undo', 'Icons.AutoMirrored.Filled.Undo'),
        @('Icons.Default.Redo', 'Icons.AutoMirrored.Filled.Redo'),
        @('Divider(', 'HorizontalDivider(')
    )
    
    foreach ($replacement in $replacements) {
        $oldValue = $replacement[0]
        $newValue = $replacement[1]
        
        if ($content.Contains($oldValue)) {
            $content = $content.Replace($oldValue, $newValue)
            $fileReplacements++
        }
    }
    
    # 如果有修改，保存文件
    if ($content -ne $originalContent) {
        Set-Content $file.FullName $content -Encoding UTF8 -NoNewline
        $modifiedFiles++
        $totalReplacements += $fileReplacements
        Write-Host "✅ 修改了 $($file.Name) ($fileReplacements 处替换)" -ForegroundColor Green
    }
}

Write-Host "`n=== 修复完成 ===" -ForegroundColor Green
Write-Host "修改的文件数: $modifiedFiles" -ForegroundColor Yellow
Write-Host "总替换次数: $totalReplacements" -ForegroundColor Yellow

Write-Host "`n🎉 过时API修复完成！" -ForegroundColor Green
Write-Host "建议运行编译测试以验证修复效果" -ForegroundColor Cyan
