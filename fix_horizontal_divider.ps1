# 修复所有HorizontalDivider引用问题
$files = @(
    "app\src\main\java\com\vere\likes\view\compose\component\SimplifiedRichTextEditor.kt",
    "app\src\main\java\com\vere\likes\view\compose\component\SmartRichTextEditor.kt",
    "app\src\main\java\com\vere\likes\view\compose\screen\AccountInfoDetailScreen.kt",
    "app\src\main\java\com\vere\likes\view\compose\screen\BankCardInfoDetailScreen.kt",
    "app\src\main\java\com\vere\likes\view\compose\screen\CalendarScreen.kt",
    "app\src\main\java\com\vere\likes\view\compose\screen\IdentityInfoDetailScreen.kt",
    "app\src\main\java\com\vere\likes\view\compose\screen\MemoEditScreen.kt",
    "app\src\main\java\com\vere\likes\view\compose\screen\ReminderSettingsScreen.kt"
)

foreach ($file in $files) {
    if (Test-Path $file) {
        Write-Host "修复文件: $file"
        $content = Get-Content $file -Raw
        
        # 添加HorizontalDivider导入
        if ($content -notmatch "import androidx.compose.material3.HorizontalDivider") {
            $content = $content -replace "import androidx.compose.material3\.\*", "import androidx.compose.material3.*`nimport androidx.compose.material3.HorizontalDivider"
        }
        
        Set-Content $file $content -Encoding UTF8
        Write-Host "✅ $file 修复完成"
    }
}

Write-Host "所有HorizontalDivider问题修复完成！"
