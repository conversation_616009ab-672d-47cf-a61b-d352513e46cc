# Google Maps API 配置指南

## 问题说明

您遇到的两个问题：
1. **设置界面的足迹地图点击就闪退** - 这是因为缺少Google Maps API密钥配置
2. **添加备忘录页面，打开记录位置信息，无法点击城市级别开关和精确位置** - 这是因为LocationPermissionManager没有正确初始化

## 解决方案

### 1. 获取Google Maps API密钥

1. 访问 [Google Cloud Console](https://console.cloud.google.com/)
2. 创建新项目或选择现有项目
3. 启用 "Maps SDK for Android" API
4. 创建API密钥：
   - 转到 "凭据" 页面
   - 点击 "创建凭据" > "API密钥"
   - 复制生成的API密钥

### 2. 配置API密钥

在 `app/src/main/res/values/strings.xml` 文件中，将以下行：

```xml
<string name="google_maps_key">YOUR_GOOGLE_MAPS_API_KEY_HERE</string>
```

替换为您的实际API密钥：

```xml
<string name="google_maps_key">您的实际API密钥</string>
```

### 3. 限制API密钥（推荐）

为了安全起见，建议限制API密钥的使用：

1. 在Google Cloud Console中，点击您的API密钥
2. 在 "应用限制" 部分，选择 "Android应用"
3. 添加您的应用包名：`com.vere.likes`
4. 添加SHA-1证书指纹（可选，用于发布版本）

### 4. 获取SHA-1指纹（可选）

对于调试版本：
```bash
keytool -list -v -keystore ~/.android/debug.keystore -alias androiddebugkey -storepass android -keypass android
```

对于发布版本：
```bash
keytool -list -v -keystore your-release-key.keystore -alias your-key-alias
```

## 临时解决方案

如果您暂时无法获取API密钥，可以：

1. **禁用足迹地图功能**：
   - 在设置界面中隐藏足迹地图入口
   - 或者显示 "功能开发中" 的提示

2. **使用模拟数据**：
   - 创建一些模拟的足迹数据用于测试
   - 显示静态地图图片而不是交互式地图

## 位置权限问题修复

位置权限问题已经在代码中修复：

1. 在 `MainActivity.kt` 中添加了 `LocationPermissionManager` 的初始化
2. 添加了调试日志来帮助排查问题

## 测试步骤

配置完API密钥后：

1. **测试足迹地图**：
   - 创建几个带位置信息的备忘录
   - 进入设置 > 足迹地图
   - 应该能看到地图和标记点

2. **测试位置权限**：
   - 创建新备忘录
   - 开启位置记录
   - 应该能够选择不同的精度级别
   - 检查logcat中的调试信息

## 常见问题

### Q: 地图显示空白
A: 检查API密钥是否正确配置，确保启用了Maps SDK for Android

### Q: 权限请求没有弹出
A: 检查logcat日志，确保LocationPermissionManager已正确初始化

### Q: 位置获取失败
A: 确保设备开启了位置服务，并且应用有位置权限

## 调试信息

查看logcat中的以下标签：
- `LocationPermissionManager`: 权限相关日志
- `LocationServiceImpl`: 位置服务日志
- `FootprintMapViewModel`: 地图数据日志

## 联系支持

如果问题仍然存在，请提供：
1. logcat日志
2. 设备型号和Android版本
3. 具体的错误步骤
